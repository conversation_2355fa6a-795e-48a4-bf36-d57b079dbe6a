package com.birdeye.social.listeners;

import com.birdeye.social.aspect.PageRegionSync;
import com.birdeye.social.aspect.SocialEsSync;
import com.birdeye.social.constant.KafkaTopicEnum;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.SocialPostRepository;
import com.birdeye.social.dao.SocialPostScheduleInfoRepo;
import com.birdeye.social.dao.SocialSetupAuditRepository;
import com.birdeye.social.dto.PageSyncEventRequest;
import com.birdeye.social.entities.*;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.model.FilterPageRequest;
import com.birdeye.social.model.SocialPostEsSyncRequest;
import com.birdeye.social.service.Channels.Facebook;
import com.birdeye.social.service.FacebookSocialService;
import com.birdeye.social.service.GoogleMyBusinessPageService;
import com.birdeye.social.service.SocialAccountService;
import com.birdeye.social.sro.SocialEsSyncDTO;
import org.hibernate.HibernateException;
import org.hibernate.event.spi.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;

@Component
// Check if we can use custom annotation to register on entities
public class EntityListener implements PersistEventListener, SaveOrUpdateEventListener, DeleteEventListener, FlushEntityEventListener,MergeEventListener {

    public static final EntityListener INSTANCE = new EntityListener();


    private static final Logger LOGGER = LoggerFactory.getLogger(EntityListener.class);

    @Autowired
    static FacebookSocialService fbSocialService;

    @Autowired
    static SocialSetupAuditRepository setupAuditRepo;

    @Autowired
    static GoogleMyBusinessPageService googleMyBusinessPageService;

    @Autowired
    static SocialPostRepository socialPostRepository;

    @Autowired
    static SocialPostScheduleInfoRepo socialPostScheduleInfoRepo;

    @Autowired
    static KafkaProducerService kafkaProducerService;

    @Autowired
    static SocialAccountService socialAccountService;

    @Autowired
    public void init(FacebookSocialService facebookSocialService,SocialSetupAuditRepository setupAuditRepo,
                     GoogleMyBusinessPageService googleMyBusinessPageService, SocialPostRepository socialPostRepository,
                     SocialPostScheduleInfoRepo socialPostScheduleInfoRepo,KafkaProducerService kafkaProducerService,
                     SocialAccountService socialAccountService)
    {
        EntityListener.fbSocialService = facebookSocialService;
        EntityListener.googleMyBusinessPageService = googleMyBusinessPageService;
        EntityListener.setupAuditRepo = setupAuditRepo;
        EntityListener.socialPostRepository = socialPostRepository;
        EntityListener.socialPostScheduleInfoRepo = socialPostScheduleInfoRepo;
        EntityListener.kafkaProducerService = kafkaProducerService;
        EntityListener.socialAccountService = socialAccountService;
    }

    private void evictCache(Object entity) {
        try {
            if (entity instanceof BusinessFBPage) {
                BusinessFBPage fbPage = (BusinessFBPage) entity;
                fbSocialService.clearFbPageCache(fbPage.getFacebookPageId());
                if ( fbPage.getEnterpriseId() != null ) {
                    fbSocialService.clearFbPagesEnterpriseCache(String.valueOf(fbPage.getEnterpriseId()));
                }
                LOGGER.info("Cache evicted for pageId {} and enterpriseId {}", fbPage.getFacebookPageId(), fbPage.getEnterpriseId());

                if(Objects.nonNull(fbPage.getBusinessId()) || Objects.nonNull(fbPage.getFacebookPageId())) {
                    // Logic of cacheEviction by filterPage object on the keys as either of businessId or pageId as null
                    FilterPageRequest filterPage = new FilterPageRequest();
                    filterPage.setSocialId(fbPage.getFacebookPageId());
                    filterPage.setBusinessId(fbPage.getBusinessId());
                    filterPage.setChannel(SocialChannel.FACEBOOK);
                    fbSocialService.clearFbPageByFilter(filterPage);
                    LOGGER.info("Cache evicted for fb Pages using filter{}", filterPage);
                }

            } else if (entity instanceof BusinessGoogleMyBusinessLocation){
                BusinessGoogleMyBusinessLocation googleMyBusinessLocation = (BusinessGoogleMyBusinessLocation) entity;

                if(Objects.nonNull(googleMyBusinessLocation.getBusinessId()) || Objects.nonNull(googleMyBusinessLocation.getLocationId())) {
                    FilterPageRequest filterPage = new FilterPageRequest();
                    filterPage.setSocialId(googleMyBusinessLocation.getLocationId());
                    filterPage.setPlaceId(Objects.isNull(googleMyBusinessLocation.getPlaceId())?"null":googleMyBusinessLocation.getPlaceId());
                    filterPage.setChannel(SocialChannel.GOOGLEPLUS);
                    filterPage.setBusinessId(googleMyBusinessLocation.getBusinessId());
                    googleMyBusinessPageService.clearGMBPageByFilter(filterPage);
                    LOGGER.info("Cache evicted for GMB Pages using filter{}", filterPage);
                }
            }
        } catch (Exception e) {
            LOGGER.error("evictCache: Error while evicting cache {}", e.getMessage());
        }
    }

    private String getAction(EventType eventType) {
        String action = "DEFAULT";
        if ( EventType.PERSIST == eventType || EventType.SAVE == eventType ) {
            action = "SAVE_MAPPING";
        } else if ( EventType.DELETE == eventType ) {
            action = "DELETE_MAPPING";
        } else if ( EventType.SAVE_UPDATE == eventType ) {
            action = "UPDATE_MAPPING";
        }
        return action;
    }

    private void audit(AbstractEvent event, EventType eventType, Object entity) {
        if (entity instanceof SocialPost || entity instanceof SocialPostScheduleInfo) {
            try {
                if(entity instanceof SocialPost && !eventType.equals(EventType.DELETE) && !eventType.equals(EventType.PERSIST)) {
                    SocialPost socialPost = (SocialPost) entity;
                    LOGGER.info("[Social Post] post info event :{}",socialPost.getId());
                    SocialPostEsSyncRequest request = new SocialPostEsSyncRequest(socialPost.getId(), null,socialPost,null,false,new Date());
                    kafkaProducerService.sendObjectWithKeyV1(String.valueOf(request.getPostId()),KafkaTopicEnum.SOCIAL_POST_ES_TOPIC.getName(), request);
                } else if(entity instanceof  SocialPostScheduleInfo) {
                    SocialPostScheduleInfo socialPostScheduleInfo = (SocialPostScheduleInfo) entity;
                    LOGGER.info("[Schedule Info] post info event :{}",socialPostScheduleInfo.getSocialPostId());
                    SocialPostEsSyncRequest request = new SocialPostEsSyncRequest(socialPostScheduleInfo.getSocialPostId(), socialPostScheduleInfo.getEnterpriseId(),null,Arrays.asList(socialPostScheduleInfo),eventType.equals(EventType.DELETE), new Date());
                    kafkaProducerService.sendObjectWithKeyV1(String.valueOf(request.getPostId()),KafkaTopicEnum.SOCIAL_POST_ES_TOPIC.getName(), request);
                }
            } catch ( Exception e ) {
                LOGGER.error("Exception occurred while persisting audit data {}", e.getMessage());
            }
        }
    }

    private void entityUpdateEvent(Object entity) {
        try {
            if (entity.getClass().isAnnotationPresent(SocialEsSync.class)) {
                SocialEsSyncDTO socialEsSyncDTO = new SocialEsSyncDTO();
                Field idField = entity.getClass().getDeclaredField("id");
                idField.setAccessible(true);
                socialEsSyncDTO.setId((Integer) idField.get(entity));
                socialEsSyncDTO.setSocialChannel(entity.getClass().getName());
                kafkaProducerService.sendObjectV1("social-entity-update", socialEsSyncDTO);
            }
        } catch (Exception e) {
            LOGGER.info("Exception while updating Social ES: ",e);
        }
    }


    private void regionUpdateEvent(Object entity) {
        try {
            if (entity.getClass().isAnnotationPresent(PageRegionSync.class)) {
                LOGGER.info("[remove------------] Region update event triggered for entity: {}", entity.getClass().getSimpleName());
                String pageId = null;
                Integer srcId = null;
                String className = entity.getClass().getSimpleName();
                switch (className) {
                    case "BusinessFBPage":
                        BusinessFBPage fbPage = (BusinessFBPage) entity;
                        pageId = fbPage.getFacebookPageId();
                        srcId = SocialChannel.FACEBOOK.getId();
                        break;
                    case "BusinessGoogleMyBusinessLocation":
                        BusinessGoogleMyBusinessLocation googleMyBusinessLocation = (BusinessGoogleMyBusinessLocation) entity;
                        pageId = googleMyBusinessLocation.getLocationId();
                        srcId = SocialChannel.GOOGLE.getId();
                        break;
                    case "BusinessTwitterAccounts":
                        BusinessTwitterAccounts twitterAccount = (BusinessTwitterAccounts) entity;
                        pageId = String.valueOf(twitterAccount.getProfileId());
                        srcId = SocialChannel.TWITTER.getId();
                        break;
                    case "BusinessLinkedinPage":
                        BusinessLinkedinPage linkedInPage = (BusinessLinkedinPage) entity;
                        pageId = linkedInPage.getProfileId();
                        srcId = SocialChannel.LINKEDIN.getId();
                        break;
                    case "BusinessInstagramAccount":
                        BusinessInstagramAccount instagramAccount = (BusinessInstagramAccount) entity;
                        pageId = instagramAccount.getInstagramAccountId();
                        srcId = SocialChannel.INSTAGRAM.getId();
                        break;
                    case "BusinessYoutubeChannel":
                        BusinessYoutubeChannel youtubeChannel = (BusinessYoutubeChannel) entity;
                        pageId = youtubeChannel.getChannelId();
                        srcId = SocialChannel.YOUTUBE.getId();
                        break;
                    case "BusinessTiktokAccounts":
                        BusinessTiktokAccounts tiktokAccount = (BusinessTiktokAccounts) entity;
                        pageId = tiktokAccount.getProfileId();
                        srcId = SocialChannel.TIKTOK.getId();
                        break;
                    case "BusinessAppleLocation":
                        BusinessAppleLocation appleLocation = (BusinessAppleLocation) entity;
                        pageId = appleLocation.getAppleLocationId();
                        srcId = SocialChannel.APPLE.getId();
                        break;
                    default:
                        // No action for other types
                        break;
                }
                if (Objects.nonNull(pageId)) {
                    socialAccountService.sendRegionUpdateEvent(pageId,srcId);
                }
            }
        } catch (Exception e) {
            LOGGER.info("Exception while updating region on notification: ", e);
        }
    }

    @Override
    public void onPersist(PersistEvent persistEvent) throws HibernateException {
        audit(persistEvent, EventType.PERSIST, persistEvent.getObject());
        entityUpdateEvent(persistEvent.getObject());
        evictCache(persistEvent.getObject());
        regionUpdateEvent(persistEvent.getObject());
    }

    @Override
    public void onPersist(PersistEvent persistEvent, Map map) throws HibernateException {
    }

    @Override
    public void onDelete(DeleteEvent deleteEvent) throws HibernateException {
        audit(deleteEvent, EventType.DELETE, deleteEvent.getObject());
        evictCache(deleteEvent.getObject());
    }

    @Override
    public void onDelete(DeleteEvent deleteEvent, Set set) throws HibernateException {
    }

    @Override
    public void onFlushEntity(FlushEntityEvent flushEntityEvent) throws HibernateException {
    }

    @Override
    public void onSaveOrUpdate(SaveOrUpdateEvent saveOrUpdateEvent) throws HibernateException {
        audit(saveOrUpdateEvent, EventType.SAVE_UPDATE, saveOrUpdateEvent.getObject());
        evictCache(saveOrUpdateEvent.getObject());
    }

    @Override
    public void onMerge(MergeEvent event) throws HibernateException {
        audit(event,EventType.SAVE_UPDATE,event.getEntity());
        entityUpdateEvent(event.getEntity());
        evictCache(event.getEntity());
    }

    @Override
    public void onMerge(MergeEvent event, Map copiedAlready) throws HibernateException {

    }
}
