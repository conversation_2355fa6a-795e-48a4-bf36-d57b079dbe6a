package com.birdeye.social.enterprise.setup;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.*;
import com.birdeye.social.dto.*;
import com.birdeye.social.dto.businessProfile.AccountListFilter;
import com.birdeye.social.dto.businessProfile.BusinessUserLocationData;
import com.birdeye.social.dto.businessProfile.BusinessUserLocationResponse;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.service.SocialAccountService;
import com.birdeye.social.service.SocialErrorMessagePageService;
import com.birdeye.social.service.doup.DoupConsumeRecords;
import com.birdeye.social.service.doup.DoupConsumeRecordsFactory;
import com.birdeye.social.sro.socialReseller.SocialResellerBulkStatusDTO;
import com.birdeye.social.sro.socialReseller.SocialResellerLocationDTO;
import com.birdeye.social.sro.socialenterprise.*;
import com.birdeye.social.utils.BusinessUtilsService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.social.model.SocialAddPagesResponse;
import com.birdeye.social.platform.dao.SessionTokenRepository;
import com.birdeye.social.platform.entities.SessionToken;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.nullsFirst;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

/**
 *
 * <AUTHOR>
 */
@Service
public class SocialEnterpriseSetupServiceImpl implements SocialEnterpriseSetupService {

	private static final Logger LOGGER = LoggerFactory.getLogger(SocialEnterpriseSetupServiceImpl.class);

	@Autowired
	private SessionTokenRepository sessionTokenRepository;

	@Autowired
	private SocialAccountService socialAccountService;

	@Autowired
	private BusinessUtilsService businessUtilService;

	@Autowired
	private DoupConsumeRecordsFactory doupConsumeRecordsFactory;

	@Autowired
	private IBusinessCoreService iBusinessCoreService;

	@Autowired
	private KafkaProducerService kafkaProducerService;

	@Autowired
	private SocialErrorMessagePageService socialErrorMessageService;

	private static final String INVALID_REQUEST = "Invalid Request";

	private static final String DEFAULT_ERROR_MESSAGE = "Something went wrong while mapping the location.";

	@Override
	public SocialAddPagesResponse getChannelWisePages(String sessionToken, String channel, String tempToken) {
		SessionToken sessionTokenObj = sessionTokenRepository.findBySessionToken(sessionToken);
		LOGGER.info("[Social Enterprise] Fetching all pages for business: {}, channel: {}",
				sessionTokenObj.getBusiness().getId(), channel);
		switch (channel) {
		case "google":

			break;
		default:
			break;
		}
		return null;
	}

	@Override
	public SocialEnterpriseReportUploadDTO processLocationMappingIntegrationReport(SocialEnterpriseDoupRequestDTO data,
																				   Integer size, Integer index,
																				   Integer userId) {
		DoupConsumeRecords execute = doupConsumeRecordsFactory.getDoupConsumeRecords(data.getChannel())
				.orElseThrow(() -> new IllegalArgumentException("Channel not found"));
		try {
			BusinessLiteDTO business = fetchBusinessLite(data.getBusinessNumber());
			List<Integer> businessIds = fetchBusinessIds(business, userId, index, size);

			if (CollectionUtils.isEmpty(businessIds)) {
				logAndThrowBusinessNotFoundException(data.getBusinessNumber());
			}

			List<Integer> filteredLocationData = execute.getMappedRecordsByLocationId(businessIds);
			return mappingLocationData(businessIds, filteredLocationData, size, index);
		} catch (Exception e) {
			logAndThrowProcessingException(data.getBusinessNumber(), e);
			return null; // This line is unreachable but required for compilation
		}
	}

	private BusinessLiteDTO fetchBusinessLite(Long businessNumber) {
		return iBusinessCoreService.getBusinessLiteByNumber(businessNumber);
	}

	private List<Integer> fetchBusinessIds(BusinessLiteDTO business, Integer userId, Integer index, Integer size) {
		AccountListFilter request = createBusinessAccountLocationRequest(userId, index, size, Arrays.asList("active", "demo"));
		BusinessUserLocationResponse response = socialAccountService.getAccountLeafLocations(business.getAccountId(), request);
		List<BusinessUserLocationData> locationDataList = (response.getAccounts() == null) ? new ArrayList<>() : response.getAccounts();
		return locationDataList.stream().map(BusinessUserLocationData::getBusinessId).collect(Collectors.toList());
	}

	private AccountListFilter createBusinessAccountLocationRequest(Integer userId, Integer page, Integer size,
																   List<String> status) {
		AccountListFilter request = new AccountListFilter();
		request.setStartIndex(page*size);
		request.setPageSize(size);
		request.setStatus(status);
		request.setUserId(userId);
		return request;
	}

	private void logAndThrowBusinessNotFoundException(Long businessNumber) {
		LOGGER.error("Exception while fetching enterprise sub hierarchy list from core for business number :{}", businessNumber);
		throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, "No accounts found for given business number");
	}

	private void logAndThrowProcessingException(Long businessNumber, Exception e) {
		LOGGER.error("Exception while processing unmapped location for Business Number :{} and exception", businessNumber, e);
		throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, "No location found for given businessId");
	}

	@Override
	public SocialEnterpriseReportUploadDTO processMappingIntegrationReport(SocialEnterpriseDoupRequestDTO data, Integer size, Integer page) {
		LOGGER.info("Received request to process enterprise mapping integration for channel {} and request {}", data.getChannel(), data);
		DoupConsumeRecords execute = doupConsumeRecordsFactory.getDoupConsumeRecords(data.getChannel())
				.orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

		return execute.processEnterpriseMappingIntegrationReport(data.getBusinessNumber(), size, page);
	}

	@Override
	public void processEnterprisePage(SocialEnterpriseBulkImportDTO data, String channel) {
		BusinessLiteDTO businessDetails;
		Long enterpriseId;
		SocialEnterpriseBulkStatusDTO operationStatus;
		LOGGER.info("Request received to map pages for enterprise for channel {} and data is {}", channel, data);
		try {
			// fetching short business id of business provided by user
			businessDetails = iBusinessCoreService.getBusinessLiteByNumber(Long.parseLong(data.getData().getBusinessId()));
			//	check if business is type is business location or SMB
			if(!checkBusinessType(businessDetails) ) {// add check for SMB  business.getEnterpriseId()==null
				String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.LOCATION_ERR.name(),SocialChannel.getSocialChannelByName(channel).getLabel());
				createDoupErrorResponse(data.getEventId(),errMessage,ResellerMappingStatusEnum.REJECTED.name());
				return;
			}

			// find parent business number of business provided by user
			List<Integer> accountIdSmallList = new ArrayList<>();
			enterpriseId = businessDetails.getEnterpriseNumber();// getEnterpriseIdParentEnterpriseBusinessNumber(businessDetails.getBusinessId(),accountIdSmallList);
			Integer accountIdSmall = null;
			if(isNotEmpty(accountIdSmallList)) {
				accountIdSmall = accountIdSmallList.get(0);
			}
			DoupConsumeRecords execute = doupConsumeRecordsFactory.getDoupConsumeRecords(channel)
					.orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
			operationStatus =  execute.mapPageWithEnterpriseLocation(data, businessDetails, enterpriseId, accountIdSmall);
			kafkaProducerService.sendWithKey(KafkaTopicEnum.SOCIAL_ENTERPRISE_CALLBACK_EVENT.getName(), Long.toString(data.getEventId()), operationStatus);
		} catch (Exception e) {
			LOGGER.error("Something went wrong while processing the data for enterprise {} with exception", e.getMessage(), e);
			String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.UNKNOWN_ERR.name(),SocialChannel.getSocialChannelByName(channel).getLabel());
			createDoupErrorResponse(data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
		}
	}

	public boolean checkBusinessType(BusinessLiteDTO business) {
		return "Product".equals(business.getType()) || "Business".equals(business.getType()) || "Enterprise-Location".equals(business.getType()) || "Enterprise-Product".equals(business.getType());
	}

	private void createDoupErrorResponse(long eventId, String errMessage, String status) {
		SocialResellerBulkStatusDTO operationStatus = new SocialResellerBulkStatusDTO();
		operationStatus.setEventId(eventId);
		operationStatus.setStatus(status);
		operationStatus.setOperation(Constants.REJECTED);
		operationStatus.setErrorMessage(errMessage);
		kafkaProducerService.sendWithKey(KafkaTopicEnum.SOCIAL_ENTERPRISE_CALLBACK_EVENT.getName(),  Long.toString(eventId), operationStatus );
	}

	private SocialEnterpriseReportUploadDTO mappingLocationData(List<Integer> businessIds, List<Integer> filteredLocationData, Integer size,
																Integer index
															) {
		if(Objects.nonNull(filteredLocationData) && CollectionUtils.isNotEmpty(filteredLocationData))
			businessIds.removeAll(filteredLocationData);
		if(CollectionUtils.isEmpty(businessIds))
			return new SocialEnterpriseReportUploadDTO(new ArrayList<>(),0L,0);
		businessIds = paginate(businessIds, index, size);
		Map<String, Object> objectMap =	iBusinessCoreService.getBusinessesInBulkByBusinessIds(businessIds, true);
		List<SocialEnterpriseLocationDTO> locationList = new ArrayList<>();
		if(Objects.nonNull(objectMap)){
			locationList = prepareChannelInfoData(objectMap);
		}
		return mapLocationData(locationList);
	}

	public List<Integer> paginate(List<Integer> fullList, int index, int pageSize) {
		int totalItems = fullList.size();
		int fromIndex = (index) * pageSize;
		int toIndex = Math.min(fromIndex + pageSize, totalItems);
		if (fromIndex >= totalItems || fromIndex < 0) {
			return Collections.emptyList();
		}
		return fullList.subList(fromIndex, toIndex);
	}

	private List<SocialEnterpriseLocationDTO> prepareChannelInfoData(Map<String, Object> businessLocationsMap) {
		List<SocialEnterpriseLocationDTO> locationList = new ArrayList<>();
		if(Objects.isNull(businessLocationsMap)){
			LOGGER.info("No Business found");
			return new ArrayList<>();
		}
		for (Map.Entry<String, Object> entry : businessLocationsMap.entrySet()) {
			Map<String ,Object> locationData = (Map<String, Object>) businessLocationsMap.get(entry.getKey());
			SocialEnterpriseLocationDTO locInfo = new SocialEnterpriseLocationDTO();
			Long businessNumber = (Long) locationData.get("businessNumber");
			locInfo.setLocationId(String.valueOf(businessNumber));
			locInfo.setLocationName(getLocationName(locationData));
			locInfo.setLocationAddress(prepareBusinessAddress((Map<String,Object>) locationData.get("location")));
			locationList.add(locInfo);
		}
		return locationList;
	}

	private String getLocationName(Map<String, Object> locationData) {
		String businessAlias = (String) locationData.get("businessAlias");
		if (Objects.nonNull(businessAlias) && StringUtils.isNotEmpty(businessAlias)) {
			return businessAlias;
		}
		return (String) locationData.get("businessName");
	}

	private String prepareBusinessAddress(Map<String,Object> locationData) {
		StringBuilder address = new StringBuilder();
		if (Objects.nonNull(locationData.get("address1")) && StringUtils.isNotEmpty((String)locationData.get("address1"))) {
			address.append((String)locationData.get("address1")).append(", ");
		}
		if (Objects.nonNull(locationData.get("address2")) && StringUtils.isNotEmpty((String)locationData.get("address2"))) {
			address.append((String)locationData.get("address2")).append(", ");
		}
		if (Objects.nonNull(locationData.get("city")) && StringUtils.isNotEmpty((String)locationData.get("city"))) {
			address.append((String)locationData.get("city")).append(", ");
		}
		if (Objects.nonNull(locationData.get("state")) && StringUtils.isNotEmpty((String)locationData.get("state"))) {
			address.append((String)locationData.get("state")).append(" ");
		}
		if (Objects.nonNull(locationData.get("zip")) && StringUtils.isNotEmpty((String)locationData.get("zip"))) {
			address.append((String)locationData.get("zip"));
		}
		return address.toString();
	}

	private SocialEnterpriseReportUploadDTO mapLocationData(List<SocialEnterpriseLocationDTO> responseList) {
		SocialEnterpriseReportUploadDTO socialResellerReportUploadDTO= new SocialEnterpriseReportUploadDTO();
		List<SocialResellerLocationDTO> socialResellerLocationDTOList =
                responseList.stream().map(data -> {
                    SocialResellerLocationDTO conf = new SocialResellerLocationDTO();
                    conf.setLocationName(data.getLocationName());
                    conf.setLocationId(data.getLocationId());
                    conf.setLocationAddress(data.getLocationAddress());
                    return conf;
                }).sorted(Comparator.comparing(SocialResellerLocationDTO::getLocationName, nullsFirst(Comparator.naturalOrder()))).collect(Collectors.toList());
        socialResellerReportUploadDTO.setData(socialResellerLocationDTOList);
		return socialResellerReportUploadDTO;

	}

}