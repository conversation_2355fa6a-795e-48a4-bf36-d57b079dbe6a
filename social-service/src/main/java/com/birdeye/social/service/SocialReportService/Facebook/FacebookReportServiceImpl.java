package com.birdeye.social.service.SocialReportService.Facebook;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.dao.reports.BusinessPostsRepository;
import com.birdeye.social.dao.reports.FacebookPageInsightRepo;
import com.birdeye.social.dao.reports.FacebookPostInsightRepo;
import com.birdeye.social.dao.reports.SocialReportPropertyRepository;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.entities.report.FacebookPageInsight;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.facebook.NewFbPostData;
import com.birdeye.social.insights.ES.SearchTemplate;
import com.birdeye.social.insights.Facebook.ExternalAPIResponse.Data;
import com.birdeye.social.insights.Facebook.FacebookInsightRequest;
import com.birdeye.social.insights.PageInsights;
import com.birdeye.social.insights.PageLevelMetaData;
import com.birdeye.social.insights.constants.InsightsConstants;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.service.SocialReportService.Converter.DbDataConverter;
import com.birdeye.social.service.SocialReportService.Converter.ReportDataConverter;
import com.birdeye.social.service.SocialReportService.ES.ReportsEsService;
import com.birdeye.social.service.SocialReportService.ExternalService.FacebookExternalService;
import com.birdeye.social.utils.InsightsReportUtil;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.SocialProxyHandler;
import com.birdeye.social.utils.TimeZoneUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class FacebookReportServiceImpl implements FacebookReportService {

    @Autowired
    FacebookExternalService facebookExternalService;

    @Autowired
    SocialFBPageRepository socialFBPageRepository;

    @Autowired
    ReportDataConverter reportDataConverter;

    @Autowired
    KafkaProducerService kafkaProducerService;

    @Autowired
    FacebookPageInsightRepo facebookPageInsightRepo;

    @Autowired
    FacebookPostInsightRepo facebookPostInsightRepo;

    @Autowired
    BusinessPostsRepository businessPostsRepository;

    @Autowired
    private ReportsEsService reportsEsService;

    private final String dateFormatterString = "yyyy-MM-dd HH:mm:ss";

    @Autowired
    DbDataConverter dbDataConverter;

    @Autowired
    private SocialProxyHandler socialProxyHandler;
    @Autowired
    private SocialReportPropertyRepository reportPropertyRepository;
    private static final Logger log = LoggerFactory.getLogger(FacebookReportServiceImpl.class);

    @Override
    public void getPageInsightsFromFacebook(SocialScanEventDTO socialScanEventDTO) {
        log.info("Get info for request : {}",socialScanEventDTO);
        Integer limit = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getSocialPageInsightsLimit(socialScanEventDTO.getSourceName().toLowerCase());
        Date startDate, endDate = Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
        List<FacebookPageInsight> businessPosts = facebookPageInsightRepo.findByPageIdOrderByIdDesc(socialScanEventDTO.getExternalId());
        if(Objects.nonNull(socialScanEventDTO.getStartDate() ) && Objects.nonNull(socialScanEventDTO.getEndDate())){
            startDate = socialScanEventDTO.getStartDate();
            endDate = socialScanEventDTO.getEndDate();
        } else if(CollectionUtils.isEmpty(businessPosts)){
            log.info("Page doesn't exist in db with page id : {}",socialScanEventDTO.getExternalId());
            startDate = DateUtils.addDays(endDate,-limit);
        } else {
            log.info("Page exist in db for request : {}",socialScanEventDTO.getExternalId());
            FacebookPageInsight facebookPageInsight = businessPosts.get(0);
            long diff = endDate.getTime() - facebookPageInsight.getLastSyncDate().getTime();
            long noOfDays = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
            if(noOfDays >= 1){
                startDate = DateUtils.addDays(facebookPageInsight.getLastSyncDate(),-10);
            }else {
                log.info("Page is already synced for today with Facebook page insights: {} and date {} with day diff {}",
                        businessPosts, endDate, diff);
                return;
            }
        }
        List<BusinessFBPage> businessFBPages = socialFBPageRepository.findByFacebookPageIdAndBusinessIdIsNotNull(socialScanEventDTO.getExternalId());
        if(CollectionUtils.isEmpty(businessFBPages) || startDate.after(endDate)){
            return;
        }

        // fetch previous insights from fb_page_insights to calculate delta
        Integer prevLinkClickCount = reportDataConverter.prevPageLinkClickCount(businessFBPages.get(0).getEnterpriseId(), InsightsConstants.FACEBOOK_PAGE_INSIGHTS, businessFBPages.get(0).getFacebookPageId());
        Integer prevOtherClickCount = reportDataConverter.prevPageOtherClickCount(businessFBPages.get(0).getEnterpriseId(), InsightsConstants.FACEBOOK_PAGE_INSIGHTS, businessFBPages.get(0).getFacebookPageId());
        Integer prevFbStoryCount = reportDataConverter.prevPageStoryInsights(businessFBPages.get(0).getEnterpriseId(), InsightsConstants.FACEBOOK_PAGE_INSIGHTS, businessFBPages.get(0).getFacebookPageId(), SearchTemplate.LAST_NON_ZERO_FB_STORY_INSIGHT_COUNT, "published_story_count");
        Integer prevFbStoryImpressions = reportDataConverter.prevPageStoryInsights(businessFBPages.get(0).getEnterpriseId(), InsightsConstants.FACEBOOK_PAGE_INSIGHTS, businessFBPages.get(0).getFacebookPageId(), SearchTemplate.LAST_NON_ZERO_FB_STORY_INSIGHT_COUNT,"story_impressions");
        Integer prevStoryEngagements = reportDataConverter.prevPageStoryInsights(businessFBPages.get(0).getEnterpriseId(), InsightsConstants.FACEBOOK_PAGE_INSIGHTS, businessFBPages.get(0).getFacebookPageId(), SearchTemplate.LAST_NON_ZERO_FB_STORY_INSIGHT_COUNT,"story_engagements");
        Integer prevStoryReach = reportDataConverter.prevPageStoryInsights(businessFBPages.get(0).getEnterpriseId(), InsightsConstants.FACEBOOK_PAGE_INSIGHTS, businessFBPages.get(0).getFacebookPageId(), SearchTemplate.LAST_NON_ZERO_FB_STORY_INSIGHT_COUNT,"story_reach");
        Integer prevStoryLikes = reportDataConverter.prevPageStoryInsights(businessFBPages.get(0).getEnterpriseId(), InsightsConstants.FACEBOOK_PAGE_INSIGHTS, businessFBPages.get(0).getFacebookPageId(), SearchTemplate.LAST_NON_ZERO_FB_STORY_INSIGHT_COUNT,"story_likes");
        Integer prevStoryComments = reportDataConverter.prevPageStoryInsights(businessFBPages.get(0).getEnterpriseId(), InsightsConstants.FACEBOOK_PAGE_INSIGHTS, businessFBPages.get(0).getFacebookPageId(), SearchTemplate.LAST_NON_ZERO_FB_STORY_INSIGHT_COUNT,"story_comments");
        Integer prevStoryShares = reportDataConverter.prevPageStoryInsights(businessFBPages.get(0).getEnterpriseId(), InsightsConstants.FACEBOOK_PAGE_INSIGHTS, businessFBPages.get(0).getFacebookPageId(), SearchTemplate.LAST_NON_ZERO_FB_STORY_INSIGHT_COUNT,"story_shares");

        // fetch summation of insights from post_insights to calculate delta
        Integer totalLinkClickCount = reportDataConverter.postLinkClickCountSummary(businessFBPages.get(0).getEnterpriseId(), InsightsConstants.POST_INSIGHT, businessFBPages.get(0).getFacebookPageId());
        Integer totalOtherClickCount = reportDataConverter.postOtherClickCountSummary(businessFBPages.get(0).getEnterpriseId(), InsightsConstants.POST_INSIGHT, businessFBPages.get(0).getFacebookPageId());
        Map<String, Integer> fbStoryInsightsSum = reportDataConverter.fetchSummationOfFbStoryInsights(businessFBPages.get(0).getEnterpriseId(), InsightsConstants.POST_INSIGHT, businessFBPages.get(0).getFacebookPageId(), SearchTemplate.POST_FB_STORY_AGGREGATION_SUMMARY_METRIC);

        Integer currentLinkClickCount = (totalLinkClickCount >= prevLinkClickCount) ? totalLinkClickCount - prevLinkClickCount : 0;
        Integer currentOtherClickCount = (totalOtherClickCount >= prevOtherClickCount) ? totalOtherClickCount - prevOtherClickCount : 0;
        Integer currentStoryCount = (fbStoryInsightsSum.get("storyCount") >= prevFbStoryCount) ? fbStoryInsightsSum.get("storyCount") - prevFbStoryCount : 0;
        Integer currentStoryImpressions = (fbStoryInsightsSum.get("storyImpressions") >= prevFbStoryImpressions) ? fbStoryInsightsSum.get("storyImpressions") - prevFbStoryImpressions : 0;
        Integer currentStoryEngagements = (fbStoryInsightsSum.get("storyEngagements") >= prevStoryEngagements) ? fbStoryInsightsSum.get("storyEngagements") - prevStoryEngagements : 0;
        Integer currentStoryReach = (fbStoryInsightsSum.get("storyReach") >= prevStoryReach) ? fbStoryInsightsSum.get("storyReach") - prevStoryReach : 0;
        Integer currentStoryLikes = (fbStoryInsightsSum.get("storyLikes") >= prevStoryLikes) ? fbStoryInsightsSum.get("storyLikes") - prevStoryLikes : 0;
        Integer currentStoryComments = (fbStoryInsightsSum.get("storyComments") >= prevStoryComments) ? fbStoryInsightsSum.get("storyComments") - prevStoryComments : 0;
        Integer currentStoryShares = (fbStoryInsightsSum.get("storyShares") >= prevStoryShares) ? fbStoryInsightsSum.get("storyShares") - prevStoryShares : 0;

        long diff = endDate.getTime() - startDate.getTime();
        int noOfDaysBetween = (int) TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        Date finalEndDate = endDate;
        businessFBPages.forEach(fbPage -> {
            int daysLeft = noOfDaysBetween;
            FacebookInsightRequest facebookInsightRequest;
            Date initialDate = startDate;
            do {
                if (daysLeft <= 90) {
                    facebookInsightRequest = reportDataConverter.createFacebookRequest(fbPage, finalEndDate, initialDate, socialScanEventDTO.getMatrix());
                    pushDataToKafkaForEsUpdate(fbPage, facebookInsightRequest, currentLinkClickCount, currentOtherClickCount, currentStoryCount, currentStoryImpressions,
                            currentStoryEngagements, currentStoryReach, currentStoryLikes, currentStoryComments, currentStoryShares);
                    break;
                }
                Date date = DateUtils.addDays(startDate, 90);
                facebookInsightRequest = reportDataConverter.createFacebookRequest(fbPage, date, initialDate, socialScanEventDTO.getMatrix());
                pushDataToKafkaForEsUpdate(fbPage, facebookInsightRequest, 0,0,0,0,0,0,0,0,0);
                initialDate = date;
                daysLeft -= 90;
            } while (true);
        });
    }

    private void pushDataToKafkaForEsUpdate(BusinessFBPage fbPage, FacebookInsightRequest facebookInsightRequest,
                                            Integer linkClickCount,  Integer otherClickCount, Integer storyCount, Integer storyImpressions,
                                            Integer storyEngagements, Integer storyReach, Integer storyLikes, Integer storyComments,
                                            Integer storyShares) {
        List<Data> facebookResponse = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(() ->
                facebookExternalService.getFacebookPageInsights(facebookInsightRequest));

        List<PageLevelMetaData> pageLevelMetaDataList = reportDataConverter.prepareDayWiseDataForFacebook(facebookResponse, fbPage, linkClickCount,
                otherClickCount, storyCount, storyImpressions, storyEngagements, storyReach, storyLikes, storyComments, storyShares);

        PageInsights pageInsights = new PageInsights(fbPage.getEnterpriseId(), fbPage.getFacebookPageId(), fbPage.getBusinessId(), SocialChannel.FACEBOOK.getName(), pageLevelMetaDataList);

        kafkaProducerService.sendObjectV1(Constants.FACEBOOK_PAGE_INSIGHTS, pageInsights);

        upsertPageInsights(pageLevelMetaDataList, fbPage.getFacebookPageId(), fbPage.getBusinessId(), fbPage.getEnterpriseId());
    }

    private Integer getLinkClickCountFromInsightData(String data) {
        Integer count = 0;
        try {
            JSONObject json = new JSONObject(data);
            JSONObject pageInsight = (JSONObject) json.getJSONArray("pageInsights").get(0);
            count = (Integer) pageInsight.get("clickCount");
        } catch (Exception e) {
            log.error("[getLinkClickCountFromInsightData] Error while parsing JSON");
        }
        return count;
    }

    private Integer getOtherClickCountFromInsightData(String data) {
        Integer count = 0;
        try {
            JSONObject json = new JSONObject(data);
            JSONObject pageInsight = (JSONObject) json.getJSONArray("pageInsights").get(0);
            count = (Integer) pageInsight.get("otherClickCount");
        } catch (Exception e) {
            log.error("[getOtherClickCountFromInsightData] Error while parsing JSON");
        }
        return count;
    }

    public Map<Date, List<FacebookPageInsight>> transformRecords(List<FacebookPageInsight> existingRecords) {
        return existingRecords.stream()
                .map(record -> new AbstractMap.SimpleEntry<>(extractMaxDate(record), record))
                .filter(entry -> entry.getKey() != null)
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                ));

    }

    private Date extractMaxDate(FacebookPageInsight record) {
        try {
            PageInsights pageInsights = JSONUtils.fromJSON(record.getData(), PageInsights.class);

            return pageInsights.getPageInsights().stream()
                    .map(PageLevelMetaData::getDate)
                    .max(Date::compareTo)
                    .orElse(null);
        } catch (Exception e) {
            log.info("Exception occurred while extracting facebook max date for id :{} and exception:{}",record.getId(),e.getMessage());
            return null;
        }
    }

    public void upsertPageInsights(List<PageLevelMetaData> pageLevelMetaDataList, String pageId, Integer businessId, Long enterpriseId) {

        if (pageLevelMetaDataList == null || pageLevelMetaDataList.isEmpty()) {
            return;
        }
        Date minDate = pageLevelMetaDataList.stream()
                .map(PageLevelMetaData::getDate)
                .min(Date::compareTo)
                .orElseThrow(() -> new IllegalArgumentException("No valid dates found"));

        Date startOfMinDate = TimeZoneUtil.normalizeToStartOfDay(minDate);

        Date endOfMaxDate = TimeZoneUtil.normalizeToEndOfDay(new Date());

        List<FacebookPageInsight> existingRecords = facebookPageInsightRepo.findByPageIdAndDateBetween(pageId, startOfMinDate, endOfMaxDate);

        Map<Date, List<FacebookPageInsight>> existingRecordMap = transformRecords(existingRecords);

        List<FacebookPageInsight> toUpdate = new ArrayList<>();
        List<FacebookPageInsight> toInsert = new ArrayList<>();

        Date now = new Date();
        Date nextSyncDate = DateUtils.addDays(now, 1);

        for (PageLevelMetaData metaData : pageLevelMetaDataList) {
            PageInsights pageInsights = new PageInsights(
                    enterpriseId,
                    pageId,
                    businessId,
                    SocialChannel.FACEBOOK.getName(),
                    Arrays.asList(metaData)
            );
            List<FacebookPageInsight> existingPageInsight = existingRecordMap.get(metaData.getDate());

            if (CollectionUtils.isNotEmpty(existingPageInsight)) {
                List<FacebookPageInsight> updatedInsights = existingPageInsight.stream()
                        .map(pageInsight -> {
                            pageInsight.setBusinessId(businessId);
                            pageInsight.setEnterpriseId(enterpriseId);
                            pageInsight.setLastSyncDate(now);
                            pageInsight.setNextSyncDate(nextSyncDate);
                            pageInsight.setData(JSONUtils.toJSON(pageInsights));
                            return pageInsight;
                        })
                        .collect(Collectors.toList());
                toUpdate.addAll(updatedInsights);
            } else {
                FacebookPageInsight newPageInsight = dbDataConverter.convertPageInsight(pageInsights);
                toInsert.add(newPageInsight);
            }
        }
        if (!toUpdate.isEmpty()) {
            facebookPageInsightRepo.save(toUpdate);
        }
        if (!toInsert.isEmpty()) {
            facebookPageInsightRepo.save(toInsert);
        }
    }

    @Override
    public void addInsightsToDB(PageInsights pageInsights) {
        FacebookPageInsight facebookPageInsights = dbDataConverter.convertPageInsight(pageInsights);
        facebookPageInsightRepo.save(facebookPageInsights);
    }

    @Override
    public BusinessPosts updatePostToDb(NewFbPostData newFbPostData) {
        BusinessPosts businessPosts = businessPostsRepository.findTopByExternalPageIdAndSourceIdOrderByIdDesc(newFbPostData.getPageId(), SocialChannel.FACEBOOK.getId());
        if(Objects.nonNull(businessPosts)){
            return businessPosts;
        }
        businessPosts = BusinessPosts.builder()
                .businessId(newFbPostData.getBusinessId())
                .enterpriseId(newFbPostData.getEnterpriseId())
                .bePostId(newFbPostData.getBePostId())
                .postId(newFbPostData.getPostId())
                .publishDate(newFbPostData.getPublishDate())
                .postText(newFbPostData.getText())
                .imageUrls(reportDataConverter.convertListToString(newFbPostData.getImageUrls()))
                .videoUrls(reportDataConverter.convertListToString(newFbPostData.getVideoUrls()))
                .externalPageId(newFbPostData.getPageId())
                .pageName(newFbPostData.getPageName())
                .sourceId(110)
                .postUrl(newFbPostData.getPostUrl())
                .build();

        businessPostsRepository.save(businessPosts);
        return businessPosts;
    }

    @Override
    public void backfillFbInsight(BackfillInsightReq socialScanEventDTO) {
        log.info("backfillFbInsight  request : {}",socialScanEventDTO);
        Integer minBackfillDays= reportPropertyRepository.findMinDaysForSourceIdAndReportType(
                socialScanEventDTO.getSourceId(),"profile_matric");
        log.info("minBackfillDays  {} for  channelId : {}",minBackfillDays,socialScanEventDTO.getSourceId());
        if(Objects.nonNull(minBackfillDays) && InsightsReportUtil.validateStartAndEndDate(socialScanEventDTO.getStartDate(),
                socialScanEventDTO.getEndDate(),minBackfillDays)) {
            List<BusinessFBPage> fbPages = socialFBPageRepository.findByFacebookPageId(socialScanEventDTO.getExternalId());
            long diff = socialScanEventDTO.getEndDate().getTime() - socialScanEventDTO.getStartDate().getTime();
            int daysLeft = (int) TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
            Date initialDate = socialScanEventDTO.getStartDate();
            List<FacebookPageInsight> businessPosts = facebookPageInsightRepo.findByPageIdOrderByIdDesc(socialScanEventDTO.getExternalId());
            Integer prevLinkClickCount = getLinkClickCountFromInsightData(businessPosts.get(0).getData());
            Integer prevOtherClickCount = getOtherClickCountFromInsightData(businessPosts.get(0).getData());
            Integer totalLinkClickCount = reportDataConverter.postLinkClickCountSummary(businessPosts.get(0).getEnterpriseId(), InsightsConstants.POST_INSIGHT, businessPosts.get(0).getPageId());
            Integer totalOtherClickCount = reportDataConverter.postOtherClickCountSummary(businessPosts.get(0).getEnterpriseId(), InsightsConstants.POST_INSIGHT, businessPosts.get(0).getPageId());
            Integer currentLinkClickCount = totalLinkClickCount - prevLinkClickCount;
            Integer currentOtherClickCount = totalOtherClickCount - prevOtherClickCount;
            if(CollectionUtils.isNotEmpty(fbPages)) {
                BusinessFBPage fbPage = fbPages.get(0);
                do {
                    if (daysLeft <= 90) {
                        FacebookInsightRequest facebookInsightRequest = reportDataConverter.createFacebookRequest(fbPage, socialScanEventDTO.getEndDate(), initialDate,socialScanEventDTO.getMatrix());
                        getInsightForFbAccount(fbPage, facebookInsightRequest, currentLinkClickCount, currentOtherClickCount);
                        return;
                    }
                    Date newEndDate = DateUtils.addDays(initialDate, 90);
                    FacebookInsightRequest facebookInsightRequest = reportDataConverter.createFacebookRequest(fbPage, newEndDate, initialDate,socialScanEventDTO.getMatrix());
                    getInsightForFbAccount(fbPage, facebookInsightRequest, 0, 0);
                    initialDate = newEndDate;
                    daysLeft -= 90;
                } while (true);
            }
        }
    }

    private void getInsightForFbAccount(BusinessFBPage fbPage, FacebookInsightRequest facebookInsightRequest, Integer linkClickCount, Integer otherClickCount) {
        List<Data> facebookResponse = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(() ->
                facebookExternalService.getFacebookPageInsights(facebookInsightRequest));

        log.info("Fb insight response for fb account id: {} : {}",fbPage.getFacebookPageId(),facebookResponse);

        List<PageLevelMetaData> pageLevelMetaDataList = reportDataConverter.prepareDayWiseDataForFacebook(facebookResponse, fbPage, linkClickCount, otherClickCount, 0, 0, 0, 0, 0, 0, 0);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Map<String,PageLevelMetaData> pageLevelMetaDataMap = pageLevelMetaDataList.stream()
                .collect(Collectors.toMap(metaData -> dateFormat.format(metaData.getDate()), metaData -> metaData));
        pageLevelMetaDataMap.forEach((k,v)-> processFbBackFill(fbPage,v));
    }

    private void processFbBackFill(BusinessFBPage fbPage, PageLevelMetaData pageLevelMetaData) {
        calculateImpressionAndReach(pageLevelMetaData);

        PageInsights pageInsights = new PageInsights(fbPage.getEnterpriseId(), fbPage.getFacebookPageId(), fbPage.getBusinessId(),
                SocialChannel.FACEBOOK.getName(), Collections.singletonList(pageLevelMetaData));

        kafkaProducerService.sendObjectV1(Constants.FACEBOOK_PAGE_INSIGHTS, pageInsights);

        addInsightsToDB(pageInsights);
    }
    private void calculateImpressionAndReach(PageLevelMetaData pageLevelMetaData) {
        pageLevelMetaData.setPostTotalCount(0);
        pageLevelMetaData.setPostCount(0);
        pageLevelMetaData.setTotalFollower(0);
        pageLevelMetaData.setPostEngagementTotal(0);
    }
}
