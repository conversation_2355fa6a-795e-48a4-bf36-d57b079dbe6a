package com.birdeye.social.service;

import com.birdeye.social.businessCore.BusinessActivationStatusEnum;
import com.birdeye.social.businessCore.BusinessStatusData;
import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.businessgetpage.IBusinessGetPageService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.*;
import com.birdeye.social.dto.*;
import com.birdeye.social.dto.businessProfile.AccountListFilter;
import com.birdeye.social.dto.businessProfile.BusinessUserLocationData;
import com.birdeye.social.dto.businessProfile.BusinessUserLocationResponse;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.elasticdto.SocialElasticDto;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.request.business.BusinessLiteRequest;
import com.birdeye.social.external.service.FbMessengerExternalService;
import com.birdeye.social.external.service.IGoogleService;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.factory.arbor.ArborServiceFactory;
import com.birdeye.social.google.GoogleBusinessCommService;
import com.birdeye.social.linkedin.LinkedinService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.instagram.TokenUpdateRequest;
import com.birdeye.social.model.tiktok.arbor.TiktokAuthUrlResponse;
import com.birdeye.social.model.usage.GoogleLocationResponseStatus;
import com.birdeye.social.model.usage.InboxStatusResponseComplete;
import com.birdeye.social.model.usage.InboxStatusSourceViseResponse;
import com.birdeye.social.nexus.EmailDTO;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.platform.dao.*;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.platform.entities.Location;
import com.birdeye.social.platform.entities.User;
import com.birdeye.social.platform.messages.OAuthRequestMessage;
import com.birdeye.social.service.SocialPostOperationService.PostOperation;
import com.birdeye.social.service.SocialPostOperationService.PostOperationFactory;
import com.birdeye.social.service.SocialUnSubscribeService.SocialUnSubscribe;
import com.birdeye.social.service.SocialUnSubscribeService.SocialUnSubscribeFactory;
import com.birdeye.social.service.applechat.AppleChatService;
import com.birdeye.social.service.approvalworkflow.ApprovalWorkflowConvertorService;
import com.birdeye.social.service.resellerservice.ResellerFactory;
import com.birdeye.social.service.resellerservice.SocialReseller;
import com.birdeye.social.service.tiktok.arbor.TiktokSocialAccountService;
import com.birdeye.social.service.whatsapp.arbor.WhatsappSocialAccountService;
import com.birdeye.social.sro.*;
import com.birdeye.social.sro.socialReseller.SocialBusinessStatusRequest;
import com.birdeye.social.sro.socialReseller.SocialBusinessStatusResponse;
import com.birdeye.social.utils.BusinessUtilsService;
import com.birdeye.social.utils.SocialElasticUtil;
import com.birdeye.social.utils.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.googleapis.json.GoogleJsonError;
import com.google.api.client.googleapis.json.GoogleJsonResponseException;
import com.google.api.services.businesscommunications.v1.model.Agent;
import com.google.api.services.businesscommunications.v1.model.Brand;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import twitter4j.Twitter;
import twitter4j.TwitterFactory;
import twitter4j.conf.Configuration;
import twitter4j.conf.ConfigurationBuilder;

import java.net.URISyntaxException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("socialAccountService")
public class SocialAccountServiceImpl extends SocialAccountSetupCommonService implements SocialAccountService {


	private static final String				PAGE_INFO_FOR_CHANNEL_BUSINESS_ID	= "page info for channel {}  businessId {} ";

    private static final String BUSINESS_PAGE_LOCATION_MISMATCH = "This page has a different address from your location’s address in BirdEye. Use parameter force=true to map anyway.";

	private static final String AUTOMAPPING = "automapping/";

	private static final String LOCATIONLAUNCH = "locationLaunch/";
	private static final String SOCIAL_UNSUB_TOPIC = "social-unsub-topic";

	private static final Integer autoMappingTimeout = 30;
	@Autowired
	private FacebookSocialAccountService	fbSocialAccountService;

	@Autowired
	private BusinessRepository				businessRepo;

	@Autowired
	private BusinessGMBLocationRepository businessGMBLocationRepository;

	@Autowired
	private BusinessUtilsService			businessUtilService;

	@Autowired
	private LocationRepository				locationRepo;

	@Autowired
 	private SocialTokenService socialTokenService;

	@Autowired
	private KafkaProducerService	producer;


	@Autowired
	private DisconnectedMailAuditService disconnectedMailAuditService;

	@Autowired
	private FacebookPageService				facebookPageService;

	@Autowired
	private IRedisExternalService redisExternalService;

	@Autowired
	private SocialSetupAuditRepository socialSetupAuditRepository;

	@Autowired
	private GoogleSocialAccountService		googleSocialAccountService;

	@Autowired
	private GMBLocationDetailService gmbLocationDetailService;

	@Autowired
	private CommonService					commonService;

	@Autowired
	private BusinessGMBLocationRawRepository socialGMBRepo;

	@Autowired
	private KafkaProducerService kafkaProducer;

	@Autowired
	private IInstragramSetupService socialInstagramService;

	@Autowired
	private IBusinessGetPageService businessGetPageService;

	@Autowired
	private SocialPostTwitterService 		twitterSocialPostService;

	@Autowired
	private TwitterSocialAccountService 	twitterSocialAccountService;

	@Autowired
	private BusinessUserRepository			businessUserRepository;

	@Autowired
	private IGMBNotificationService 		notificationService;

	@Autowired
	private SocialPagesAuditRepo 			socialPagesAuditRepo;

	@Autowired
	private BusinessFacebookPageRepository 	businessFacebookPageRepository;

	@Autowired
	private FbMessengerExternalService 		fbMsgService;

	@Autowired
	private AutoMappingService autoMappingService;

	@Autowired
	private AutoMappingRepo autoMappingRepo;

	@Autowired
	private LinkedinService 				linkedinService;

	@Autowired
	private SocialLinkedinService socialLinkedinService;

	@Autowired
	private IInstragramSetupService iInstragramSetupService;

	@Autowired
	private IGoogleService googleService;

	@Autowired
	private BusinessGnipRuleService businessGnipRuleService;

	@Autowired
	private SocialMentionService socialMentionService;

	@Autowired
	private BusinessInstagramAccountRepository instagramAccountRepo;

	@Autowired
	private BusinessGetPageReqRepo businessGetPageReqRepo;

	private static final Logger logger = LoggerFactory.getLogger(SocialAccountServiceImpl.class);

	@Autowired
	private IBusinessCachedService businessService;

	@Autowired
    private NexusService	nexusService;

    @Autowired
	private IBusinessCoreService businessCoreService;

    @Autowired
    private OpenUrlService openUrlService;

	@Autowired
	private BusinessGMBLocationRepository businessGMBPageRepo;

	@Autowired
	private GoogleMessagesAgentService googleMsgAgentService;

	@Autowired
	private GoogleBusinessCommService googleBizCommService;

	@Autowired
	private GoogleAccessTokenCache googleAccessTokenCache;

	@Autowired
	private GoogleMyBusinessPageService gmbRawPageService;

	@Autowired
	private AppleChatService appleChatService;

	@Autowired
	private YouTubeAccountService youTubeAccountService;

	@Autowired
	private GoogleLocationService googleLocationService;

	@Autowired
	private BusinessLinkedinPageRepository linkedinRepo;

	@Autowired
	private IPermissionMappingService iPermissionMappingService;

	@Autowired
	private InstagramSocialService instagramSocialService;

	@Autowired
	private IFbNotificationService fbNotificationService;

	@Autowired
	private BusinessGetPageOpenUrlReqRepo businessGetPageOpenUrlReqRepo;

	@Autowired
	private IInstragramSetupService instragramSetupService;

	@Autowired
	private SocialPostService socialPostService;

	@Autowired
	private ApprovalWorkflowConvertorService approvalWorkflowConvertor;

	@Autowired
	private AppleAccountService appleAccountService;

	@Autowired
	private SocialUnSubscribeFactory socialUnSubscribeFactory;

	@Autowired
	private TiktokSocialAccountService tiktokSocialAccountService;

	@Autowired
	private WhatsappSocialAccountService whatsappSocialAccountService;

	@Autowired
	private ResellerFactory resellerFactory;
	@Autowired
	private SocialBusinessPropertyRepo socialBusinessPropertyRepo;

	@Autowired
	@Qualifier(Constants.SOCIAL_TASK_EXECUTOR)
	private ThreadPoolTaskExecutor executor;

	private static final String INVALID_REQUEST = "Invalid Request";

	@Value("${social.server.region}")
	private String region;

	@Autowired
	private PostOperationFactory postOperationFactory;

	@Override
	public Map<String, Object> saveLocationPageMapping(String channel, Integer locationId, String pageId, Integer userId, Boolean force, Long enterpriseId) throws Exception {
		Map<String, Object> response = new HashMap<>();
		logger.info("For channel {}  page Id {} mapping with location Id {}", channel, pageId, locationId);
		if (StringUtils.isEmpty(pageId) || locationId == null) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_PAGE_OR_LOCATION_ID, "page/location id is null");
		}
        if ( !force && !isPageBusinessPlaceIdSame(channel, locationId, pageId, userId) ) {
			response.put("internalErrorCode", 1721);
			response.put("error", BUSINESS_PAGE_LOCATION_MISMATCH);
			return response;
        }
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			fbSocialAccountService.saveLocationPageMapping(locationId, pageId, userId,Constants.ENTERPRISE, null);
		} else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			googleSocialAccountService.saveGMBLocationMapping(locationId, pageId, userId,Constants.ENTERPRISE, null);
		} else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			twitterSocialAccountService.saveTwitterLocationMapping(locationId, Long.valueOf(pageId), userId,Constants.ENTERPRISE, null);
		} else if(SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)) {
			youTubeAccountService.saveLocationPageMapping(locationId, pageId, userId,Constants.ENTERPRISE, null);
		} else if(SocialChannel.APPLE_CONNECT.getName().equalsIgnoreCase(channel)) {
			appleAccountService.saveLocationPageMapping(locationId,pageId,userId);
		}  else if(SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel) || SocialChannel.WHATSAPP.getName().equalsIgnoreCase(channel)) {
			ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));
			arborService.saveLocationMapping(locationId, pageId, userId, Constants.ENTERPRISE, enterpriseId);
	}
        return response;
	}

	@Override
	public boolean isPageBusinessPlaceIdSame(String channel, Integer locationId, String pageId, Integer userId) throws Exception {
		logger.info("Checking page-location before mapping for channel {}  page Id {} mapping with location Id {}", channel, pageId, locationId);
		if (StringUtils.isEmpty(pageId) || locationId == null) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_PAGE_OR_LOCATION_ID, "page/location id is null");
		}
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			return fbSocialAccountService.isPageBusinessPlaceIdSame(locationId, pageId, userId);
		} else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			return googleSocialAccountService.isPageBusinessPlaceIdSame(locationId, pageId, userId);
		}
		// for twitter & linkedin, we return true (that is, always force map)
		return true;
	}

	@Override
	public void removePageMappings(String channel, List<LocationPageMappingRequest> locationPageMappingRequests, boolean unlink, Long enterpriseId) throws Exception {
		if ( locationPageMappingRequests == null || CollectionUtils.isEmpty(locationPageMappingRequests)) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_PAGE_OR_LOCATION_ID, "page/location id is null");
		}
		final Integer maxRequests = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getMaxDeleteMappingsCount();
		if ( locationPageMappingRequests.size() > maxRequests ) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Maximum remove mappings request can be " + maxRequests);
		}
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			fbSocialAccountService.removeFbLocationPageMapping(locationPageMappingRequests,Constants.ENTERPRISE, unlink);
		} else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			googleSocialAccountService.removeGMBLocationPageMappings(locationPageMappingRequests,Constants.ENTERPRISE,unlink);
		} else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			twitterSocialAccountService.removeTwitterLocationAccountMappings(locationPageMappingRequests,Constants.ENTERPRISE,unlink);
		} else if (SocialChannel.LINKEDIN.getName().equalsIgnoreCase(channel)) {
			socialLinkedinService.removeLinkedinLocationAccountMapping(locationPageMappingRequests,Constants.ENTERPRISE,unlink);
		} else if (SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(channel)) {
			socialInstagramService.removeInstagramLocationAccountMapping(locationPageMappingRequests,Constants.ENTERPRISE,unlink);
		} else if (SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)) {
			youTubeAccountService.removeLocationChannelMapping(locationPageMappingRequests,Constants.ENTERPRISE,unlink);
		} else if (SocialChannel.APPLE_CONNECT.getName().equalsIgnoreCase(channel)) {
			appleAccountService.removeLocationChannelMapping(locationPageMappingRequests);
		} else if (SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel)||SocialChannel.WHATSAPP.getName().equalsIgnoreCase(channel)) {
			ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));
			arborService.removePageMapping(locationPageMappingRequests, Constants.ENTERPRISE, unlink);
		}
	}

	@Override
	public void removeMappingForPageIds(RemovePageMappingRequest removePageMappingRequest, Long enterpriseId) throws Exception {
		removePageMappings(removePageMappingRequest.getChannel(),removePageMappingRequest.getLocationPageMappingRequests(),false, enterpriseId);
	}

	@Override
	public void cleanupPageMappings(String channel, List<FacebookRemovePageMappingCleanupRequest> fbRemovePageMappingCleanupReqs) throws Exception {
		if ( fbRemovePageMappingCleanupReqs == null || CollectionUtils.isEmpty(fbRemovePageMappingCleanupReqs)) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_PAGE_OR_LOCATION_ID, "page/location id is null");
		}
		final Integer maxRequests = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getMaxDeleteMappingsCount();
		if ( fbRemovePageMappingCleanupReqs.size() > maxRequests ) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Maximum cleanup mappings request can be " + maxRequests);
		}
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			fbSocialAccountService.cleanupPageMappings(fbRemovePageMappingCleanupReqs);
		}
	}

	@Override
	public Map<String, ChannelPageCount> getChannelPageCounts(Long businessId) throws Exception {
		List<String> channels = Arrays.asList(SocialChannel.FACEBOOK.getName(), SocialChannel.GOOGLE_PLUS_GMB.getName(), SocialChannel.LINKEDIN.getName(), SocialChannel.TWITTER.getName(), SocialChannel.INSTAGRAM.getName());
		Map<String, ChannelPageCount> pageCountMap = new HashMap<>();
		channels.parallelStream().forEach(channel -> pageCountMap.put(channel, getCountObject(businessId, channel)));
		return pageCountMap;
	}

	private ChannelPageCount getCountObject(Long businessId, String channel) {
		Business enterprise = businessRepo.findByBusinessId(businessId);
		ChannelPageCount channelCountObj = null;
		switch (channel) {
			case "facebook":
				channelCountObj = fbSocialAccountService.getCountObj(enterprise);
				break;
			case "googleplus":
				channelCountObj = new ChannelPageCount(300, 0, 200, 100, 200, 0);
				break;
			case "linkedin":
				channelCountObj = new ChannelPageCount(300, 0, 200, 100, 200, 0);
				break;
			case "instagram":
				channelCountObj = new ChannelPageCount(300, 0, 200, 100, 200, 0);
				break;
			case "twitter":
				channelCountObj = new ChannelPageCount(300, 0, 200, 100, 200, 0);
				break;
			default:
				logger.info("invalid channel");
		}
		return channelCountObj;
	}


	@Override
	public ConnectedPages getPages(String channel, Long enterpriseId, Integer userId, String type) {
		if ( !PageConnectionStatus.contains(type) ) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid value for type");
		}
		logger.info("Request received to get all selected pages for enterprise id {}, channel {}, user {}, pageConnectionStatus {}", enterpriseId, channel, userId, type);
		ConnectedPages connectedPages = new ConnectedPages();
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			connectedPages = fbSocialAccountService.getPages(enterpriseId, PageConnectionStatus.valueOf(type.toUpperCase()));
		}
		if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			connectedPages = googleSocialAccountService.getPages(enterpriseId, PageConnectionStatus.valueOf(type.toUpperCase()));
		}
		if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			connectedPages = twitterSocialPostService.getPages(enterpriseId, PageConnectionStatus.valueOf(type.toUpperCase()));
		}
		if (SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)) {
			connectedPages = youTubeAccountService.getPages(enterpriseId, PageConnectionStatus.valueOf(type.toUpperCase()));
		}
		return connectedPages;
	}

	@Override
	public ConnectedPages getPagesForPostReconnect(String channel, Long enterpriseId, Integer userId, String type, SocialPostPageConnectRequest request) {
		if ( !PageConnectionStatus.contains(type) ) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid value for type");
		}
		logger.info("Request received to get all selected pages for enterprise id {}, channel {}, user {}, pageConnectionStatus {}", enterpriseId, channel, userId, type);
		ConnectedPages connectedPages = new ConnectedPages();
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			connectedPages = fbSocialAccountService.getPagesForPostReconnect(enterpriseId, PageConnectionStatus.valueOf(type.toUpperCase()), request);
		}
		if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			connectedPages = googleSocialAccountService.getPagesForPostReconnect(enterpriseId, PageConnectionStatus.valueOf(type.toUpperCase()), request);
		}
		if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			connectedPages = twitterSocialPostService.getPagesForPostReconnect(PageConnectionStatus.valueOf(type.toUpperCase()), request);
		}
		if (SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)) {
			connectedPages = youTubeAccountService.getPagesForPostReconnect(PageConnectionStatus.valueOf(type.toUpperCase()), request);
		}
		if (SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(channel)) {
			connectedPages = socialInstagramService.getPagesForPostReconnect(enterpriseId, type, request);
		}
		if(SocialChannel.LINKEDIN.getName().equalsIgnoreCase(channel)) {
			connectedPages = socialLinkedinService.getPagesForPostReconnect(enterpriseId, type, request);
		}
		if(SocialChannel.APPLE.getName().equalsIgnoreCase(channel)) {
			// no implementation required
		}
		return connectedPages;
	}

	@Override
	public void updateAddress(SocialScanEventDTO socialScanEventDTO, String channel) {
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			fbSocialAccountService.updateAddress(socialScanEventDTO);
		} else {
			logger.info("channel other than facebook is not supported yet");
		}
	}

	@Override
	public SmbChannelData allSourcePageForSMB(Long businessId){
		SmbChannelData smbChannelData = new SmbChannelData();
		Map<String, LocationPageListInfo> result = getPageAllSource(businessId);
		smbChannelData.setGmb(result.get(SocialChannel.GMB.getName()));
		smbChannelData.setFacebook(result.get(SocialChannel.FACEBOOK.getName()));
		smbChannelData.setTwitter(result.get(SocialChannel.TWITTER.getName()));
		if(result.get(SocialChannel.LINKEDIN.getName())!=null) {
			smbChannelData.setLinkedin(result.get(SocialChannel.LINKEDIN.getName()));
		} if(result.get(SocialChannel.INSTAGRAM.getName())!=null) {
			smbChannelData.setInstagram(result.get(SocialChannel.INSTAGRAM.getName()));
		}
		if(result.get(SocialChannel.YOUTUBE.getName()) != null){
			smbChannelData.setYoutube(result.get(SocialChannel.YOUTUBE.getName()));
		}
		return smbChannelData;
	}

	@Override
	public Map<String, LocationPageListInfo> getPageAllSource(Long businessId) {

		Map<String, LocationPageListInfo> connectPage = new HashMap<>();

		List<SocialPageListInfo> fbPages = fbSocialAccountService.getUnmappedFbPagesByEnterpriseId(businessId);
		fbPages.stream().forEach(item -> {
			if(item.getMapped() == true) {
				LocationPageListInfo data = item;
				connectPage.put(SocialChannel.FACEBOOK.getName(),data);
			}
		});
		if(connectPage.get(SocialChannel.FACEBOOK.getName()) == null){
			connectPage.put(SocialChannel.FACEBOOK.getName(), null);
		}

		List<SocialPageListInfo> gmbPages = googleSocialAccountService.getUnmappedGMBPagesByEnterpriseId(businessId);
		gmbPages.stream().forEach(item -> {
			if(item.getMapped() == true) {
				LocationPageListInfo data = item;
				connectPage.put(SocialChannel.GMB.getName(),data);
			}
		});
		if(connectPage.get(SocialChannel.GMB.getName()) == null){
			connectPage.put(SocialChannel.GMB.getName(), null);
		}

		List<SocialPageListInfo> twitterPages = twitterSocialAccountService.getUnmappedTwitterPagesByEnterpriseId(businessId);
		twitterPages.stream().forEach(item -> {
			if(item.getMapped() == true) {
				LocationPageListInfo data = item;
				connectPage.put(SocialChannel.TWITTER.getName(),data);
			}
		});
		if(connectPage.get(SocialChannel.TWITTER.getName()) == null){
			connectPage.put(SocialChannel.TWITTER.getName(), null);
		}

		List<SocialPageListInfo> linkedinPages = socialLinkedinService.getUnmappedLinkedinPagesByEnterpriseId(businessId);
		linkedinPages.stream().forEach(item -> {
			if(item.getMapped() == true) {
				LocationPageListInfo data = item;
				connectPage.put(SocialChannel.LINKEDIN.getName(),data);
			}
		});
		if(connectPage.get(SocialChannel.LINKEDIN.getName()) == null){
			connectPage.put(SocialChannel.LINKEDIN.getName(), null);
		}

		List<SocialPageListInfo> instagramAccounts = socialInstagramService.getUnmappedInstagramAccountsByEnterpriseId(businessId);
		instagramAccounts.stream().forEach(item -> {
			if(item.getMapped() == true) {
				LocationPageListInfo data = item;
				connectPage.put(SocialChannel.INSTAGRAM.getName(),data);
			}
		});
		if(connectPage.get(SocialChannel.INSTAGRAM.getName()) == null){
			connectPage.put(SocialChannel.INSTAGRAM.getName(), null);
		}
		List<SocialPageListInfo> appleLocations = appleChatService.findPagesSocialList(businessId);
		appleLocations.stream().forEach(item -> {
			if(item.getMapped() == true) {
				LocationPageListInfo data = item;
				connectPage.put(SocialChannel.APPLE.getName(),data);
			}
		});
		if(connectPage.get(SocialChannel.APPLE.getName()) == null){
			connectPage.put(SocialChannel.APPLE.getName(), null);
		}

		List<SocialPageListInfo> ytLocations = youTubeAccountService.findPagesSocialList(businessId);
		ytLocations.forEach(item -> {
			if(item.getMapped()) {
				connectPage.put(SocialChannel.YOUTUBE.getName(), item);
			}
		});
		if(connectPage.get(SocialChannel.YOUTUBE.getName()) == null){
			connectPage.put(SocialChannel.YOUTUBE.getName(), null);
		}

		List<SocialPageListInfo> appleConnectLocations = appleAccountService.getUnmappedAppleLocationsByEnterpriseId(businessId);
		appleConnectLocations.forEach(item -> {
			if(item.getMapped()) {
				LocationPageListInfo data = item;
				connectPage.put(SocialChannel.APPLE_CONNECT.getName(),data);
			}
		});
		connectPage.putIfAbsent(SocialChannel.APPLE_CONNECT.getName(), null);

		ArborService arborService = ArborServiceFactory.getService(SocialChannel.TIKTOK);

		arborService.getPagesSocialList(connectPage, businessId);

		return connectPage;
	}

	private void validateBusiness(Business business, Long enterpriseId) {
		if (business == null) {
			throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, ("Business not found for enterpriseId :" + enterpriseId));
		}
	}

	@SuppressWarnings("deprecation")
	@Override
	public ReviewOptionsInfo getReviewShareOptions(String channel, Long enterpriseId, Integer startIndex, Integer count, Integer userId) {
		ReviewOptionsInfo info = new ReviewOptionsInfo();
		Business business = businessRepo.findByBusinessId(enterpriseId);
		User user = new User();
		user.setId(userId);
		List<Integer> totalBusinessIds = businessUtilService.getBusinessLocationsForEnterprise(business, null);
		// List<Business> businesses = businessRepo.findByIdIn(businessIds);
		List<Integer> businessIds = businessUserRepository.findBusiness(userId, totalBusinessIds);
		List<BusinessEntity> businesses = businessRepo.getAllBusinessByBid(businessIds);
		Map<Integer, BusinessEntity> idToBusinessMap = new HashMap<>();
		Map<Integer, String> locationToAddressMap = new HashMap<>();
		List<Integer> locationAddressIds = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(businesses)) {
			businesses.stream().forEach(childbusiness -> {
				idToBusinessMap.put(childbusiness.getId(), childbusiness);
				if (childbusiness.getLocationId() != null) {
					locationAddressIds.add(childbusiness.getLocationId());
				}
			});
			List<Location> locationList = null;
			if (CollectionUtils.isNotEmpty(locationAddressIds)) {
				locationList = locationRepo.findByIdIn(locationAddressIds);
				if (CollectionUtils.isNotEmpty(locationList)) {
					locationList.stream().forEach(location -> locationToAddressMap.put(location.getId(), prepareBusinessAddress(location)));
				}
			}
			if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
				info = fbSocialAccountService.getReviewShareOptions(enterpriseId, idToBusinessMap, locationToAddressMap, startIndex, count);
			} else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
				info = twitterSocialAccountService.getReviewShareOptions(enterpriseId, idToBusinessMap, locationToAddressMap, startIndex, count);
			}
		}
		return info;
	}

	private String prepareBusinessAddress(Location location) {
		StringBuilder address = new StringBuilder();
		if (StringUtils.isNotEmpty(location.getAddress1())) {
			address.append(location.getAddress1()).append(", ");
		}
		if (StringUtils.isNotEmpty(location.getAddress2())) {
			address.append(location.getAddress2()).append(", ");
		}
		if (StringUtils.isNotEmpty(location.getCity())) {
			address.append(location.getCity()).append(", ");
		}
		if (StringUtils.isNotEmpty(location.getState())) {
			address.append(location.getState()).append(" ");
		}
		// Zipcode will be always there
		if (StringUtils.isNotEmpty(location.getZip())) {
			address.append(location.getZip());
		}
		return address.toString();
	}

	@SuppressWarnings("deprecation")
	@Override
	public ReviewOptionDto updateReviewSharingOptions(Integer businessId, String pageId, Boolean autoPostingEnabled, Integer starVal, Integer postVal, String channel, Integer userId) throws Exception {
		ReviewOptionDto dto = null;
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			dto = fbSocialAccountService.updateReviewSharingOptions(businessId, pageId, autoPostingEnabled, starVal, postVal, userId);
		} else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			dto = twitterSocialAccountService.updateReviewSharingOptions(businessId, pageId, autoPostingEnabled, starVal, postVal, userId);
		}
		return dto;
	}

	@Deprecated
	@Override
	public LocationPageMapping getLocationMappingPages(String channel, Long businessId, Integer startIndex, Integer endIndex, String context, Integer sort, Integer userId, Integer accountId) throws Exception {
		logger.info("getLocationMappingPages for enterprise {}", businessId);
		Set<String> status = new HashSet<>();
		status.add(LocationStatusEnum.UNMAPPED.getName());
		status.add(LocationStatusEnum.MAPPED.getName());

		logger.info("This function is Deprecated, to be removed in future");
		LocationPageMapping response = null;
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			response = fbSocialAccountService.getLocationMappingPages(businessId, userId,null,status,null,null,null, null, null);
		} else if (SocialChannel.GOOGLE_PLUS_GMB.getName().equalsIgnoreCase(channel)) {
			response = googleSocialAccountService.getLocationMappingPages(businessId, userId,null,status,Constants.ENTERPRISE,null,null,null, null);
		}
		else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			response = twitterSocialAccountService.getLocationMappingPages(businessId, userId,null,status,null,null,null, null);
		}
		return response;
	}

	@Override
	public LocationPageMapping getLocationMappingPagesV2(String channel,LocationMappingRequest input, Integer accountId) throws Exception {
		logger.info("getLocationMappingPages for enterprise {} and userId: {}", input.getBusinessId(), input.getUserId());
		Set<String> finalStatus = new HashSet<>(input.getStatus());

		if(input.getBusinessIds() != null) {
			logger.info("total number business ids recieved as input: {} , {}",input.getBusinessIds().size(),input.getBusinessIds());
		}
		if(CollectionUtils.isNotEmpty(input.getBusinessIds())) {
			input.setBusinessIds(input.getBusinessIds().stream().filter(Objects::nonNull).collect(Collectors.toList()));
		}
		LocationPageMapping response = null;
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			response = fbSocialAccountService.getLocationMappingPages(input.getBusinessId(), input.getUserId(),input.getBusinessIds(),finalStatus,input.getPage(),input.getSize(),input.getSearch(), input.getPermission(), accountId);
		} else if (SocialChannel.GOOGLE_PLUS_GMB.getName().equalsIgnoreCase(channel) || SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			response = googleSocialAccountService.getLocationMappingPages(input.getBusinessId(),input.getUserId(),input.getBusinessIds(),finalStatus,Constants.ENTERPRISE,input.getPage(),input.getSize(),input.getSearch(), input.getPermission());
		}
		else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			response = twitterSocialAccountService.getLocationMappingPages(input.getBusinessId(),input.getUserId(),input.getBusinessIds(),finalStatus,input.getPage(),input.getSize(),input.getSearch(), input.getPermission());
		}
		else if (SocialChannel.LINKEDIN.getName().equalsIgnoreCase(channel)) {
			response = socialLinkedinService.getLocationMappingPages(input.getBusinessId(),input.getUserId(),input.getBusinessIds(),finalStatus,input.getPage(),input.getSize(),input.getSearch(), input.getPermission());
		}
		else if (SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(channel)) {
			response = socialInstagramService.getLocationMappingPages(input.getBusinessId(),input.getUserId(),input.getBusinessIds(),finalStatus,input.getPage(),input.getSize(),input.getSearch(), input.getPermission());
		}
		else if(SocialChannel.APPLE.getName().equalsIgnoreCase(channel)) {
			response = appleChatService.getLocationMappingPages(input);
		}
		else if(SocialChannel.APPLE_CONNECT.getName().equalsIgnoreCase(channel)) {
			response = appleAccountService.getLocationMappingPages(input);
		}
		else if (SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)) {
			response = youTubeAccountService.getLocationMappingPages(input.getBusinessId(),input.getUserId(),input.getBusinessIds(),finalStatus,input.getPage(),input.getSize(),input.getSearch(), input.getPermission());
		} else if (SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel) ||SocialChannel.WHATSAPP.getName().equalsIgnoreCase(channel) ) {
			ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));
			response = arborService.getLocationMappingPages(input.getBusinessId(),input.getUserId(),input.getBusinessIds(),finalStatus,input.getPage(),input.getSize(),input.getSearch(), input.getPermission());
		}
		return response;
	}

	@Override
	public UnmappedLocationMappingResponse getUnmappedLocationCount(UnmappedLocationMappingReq request){
		logger.info("request received to fetch unmapped location count for enterpriseId");
		if(Objects.isNull(request) || Objects.isNull(request.getEnterpriseId()) || CollectionUtils.isEmpty(request.getBusinessIds())){
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST,"businessId and businessIds are required");
		}
		UnmappedLocationMappingResponse response = new UnmappedLocationMappingResponse();
		Map<String,Integer> unmappedCount = new HashMap<>();
		unmappedCount.put(SocialChannel.FACEBOOK.getName(),fbSocialAccountService.getUnmappedLocationCount(request));
		unmappedCount.put(SocialChannel.GMB.getName(),googleSocialAccountService.getUnmappedLocationCount(request));
		response.setUnmappedCount(unmappedCount);
		return response;
	}

	@Override
	public ChannelLocationInfo getSingleLocationMappingPages(String channel, Integer businessId) throws Exception {
		ChannelLocationInfo response = null;
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			response = fbSocialAccountService.getSingleLocationMappingPages(businessId);
		} else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			response = googleSocialAccountService.getSingleLocationMappingPages(businessId);
		}
		return response;
	}

	@Override
	public void removePage(String channel, List<LocationPageMappingRequest> input, Long businessId, Integer userId, Long enterpriseId) throws Exception {
		logger.info("Request received to remove page from Social, Input {}", input);
//		BusinessLiteDTO business= businessCoreService.getBusinessLiteByNumber(businessId);
		//commonService.checkBusinessValidation(business);
		Map<String, LocationPageMappingRequest> pageToMappingMap = input.stream().collect(Collectors.toMap(LocationPageMappingRequest::getPageId, Function.identity()));
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			fbSocialAccountService.removeFbPage(pageToMappingMap);
		} else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			googleSocialAccountService.removeGMBPage(new ArrayList<>(pageToMappingMap.keySet()), businessId);
		} else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			twitterSocialAccountService.removeTwitterAccount(new ArrayList<>(pageToMappingMap.keySet()));
		} else if(SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)){
			youTubeAccountService.removeYoutubeChannel(new ArrayList<>(pageToMappingMap.keySet()),Constants.ENTERPRISE);
		} else if(SocialChannel.APPLE_CONNECT.getName().equalsIgnoreCase(channel)){
			appleAccountService.removeAppleLocation(new ArrayList<>(pageToMappingMap.keySet()));
		} else if (SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel) ||
				SocialChannel.WHATSAPP.getName().equalsIgnoreCase(channel)) {
			ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));

			arborService.removeAccounts(new ArrayList<>(pageToMappingMap.keySet()), enterpriseId);
		}
	}

	@Override
	public void removePagesByIds(DeleteEventRequest deleteEventRequest){
		SocialChannel socialChannel = SocialChannel.getSocialChannelByName(deleteEventRequest.getChannel());
		if(Objects.isNull(socialChannel) || CollectionUtils.isEmpty(deleteEventRequest.getPagesIds())){
			logger.info("Request received to remove pages ids : {}",deleteEventRequest);
			return;
		}
		switch (socialChannel){
			case FACEBOOK:
				fbSocialAccountService.removeFacebookPagesByPageIds(deleteEventRequest.getPagesIds());
				break;
			case GMB:
				googleSocialAccountService.removeGMBPageByPageIds(deleteEventRequest.getPagesIds());
				break;
			case TWITTER:
				twitterSocialAccountService.removeTwitterPagesByPageIds(deleteEventRequest.getPagesIds());
				break;
			case LINKEDIN:
				socialLinkedinService.removeLinkedinPagesByPagesIds(deleteEventRequest.getPagesIds());
				break;
			case YOUTUBE:
				youTubeAccountService.removeYoutubeChannelByPageIds(deleteEventRequest.getPagesIds());
                break;
			case INSTAGRAM:
				socialInstagramService.removeInstagramAccountByPagesIds(deleteEventRequest.getPagesIds());
				break;
			default:
				logger.info("Channel undefined : {}",socialChannel);
		}
	}

	/**
	 * Auto suggester page search API
	 *
	 * Returns list of unmapped pages by channel for a enterprise id
	 *
	 */
	@Override
	public AutoSuggesterPagesResponse findUnmappedPages(String channel, Long enterpriseId) throws Exception {
		logger.info("Request received for channel {} and enterpriseId {}", channel, enterpriseId);
		if (enterpriseId == null) {
			return new AutoSuggesterPagesResponse();
		}
		AutoSuggesterPagesResponse autoSuggesterPagesResponse = new AutoSuggesterPagesResponse();
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			List<SocialPageListInfo> pages = fbSocialAccountService.getUnmappedFbPagesByEnterpriseId(enterpriseId);
			autoSuggesterPagesResponse.setPages(pages);
		} else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			List<SocialPageListInfo> gmb = googleSocialAccountService.getUnmappedGMBPagesByEnterpriseId(enterpriseId);
			gmb.parallelStream().forEach(gmbPage -> {
				if (StringUtils.isBlank(gmbPage.getAddress())) {
					gmbPage.setAddress(gmbPage.getPhoneNumber());
				}
			});
			autoSuggesterPagesResponse.setPages(gmb);
		}
		else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			List<SocialPageListInfo> pages = twitterSocialAccountService.getUnmappedTwitterPagesByEnterpriseId(enterpriseId);
			autoSuggesterPagesResponse.setPages(pages);
		}
		else if (SocialChannel.LINKEDIN.getName().equalsIgnoreCase(channel)) {
			List<SocialPageListInfo> pages = socialLinkedinService.getUnmappedLinkedinPagesByEnterpriseId(enterpriseId);
			autoSuggesterPagesResponse.setPages(pages);
		}
		else if (SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(channel)) {
			List<SocialPageListInfo> pages = iInstragramSetupService.getUnmappedInstagramAccountsByEnterpriseId(enterpriseId);
			autoSuggesterPagesResponse.setPages(pages);
		}else if (SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)) {
			List<SocialPageListInfo> pages = youTubeAccountService.getUnmappedYoutubeChannelsByEnterpriseId(enterpriseId);
			autoSuggesterPagesResponse.setPages(pages);
		}else if (SocialChannel.APPLE_CONNECT.getName().equalsIgnoreCase(channel)) {
			List<SocialPageListInfo> pages = appleAccountService.getUnmappedAppleLocationsByEnterpriseId(enterpriseId);
			autoSuggesterPagesResponse.setPages(pages);
		}
		return autoSuggesterPagesResponse;
	}

	@SuppressWarnings("deprecation")
	@Override
	public void updateInvalidPage(String channel, String pageId) {
		logger.info("Marking invalid page {} for channel {}", channel, pageId);
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			facebookPageService.updateFacebookPageInvalidStatus(pageId, 0);
		} else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			googleSocialAccountService.updateGMBLocationIsValidStatus(pageId, 0);
		} else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			twitterSocialAccountService.updateTwitterAccountIsValidStatus(Long.valueOf(pageId), 0);
		} else if (SocialChannel.LINKEDIN.getName().equalsIgnoreCase(channel)) {
			socialLinkedinService.updateLinkedinAccountIsValidStatus(pageId, 0);
		} else if (SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(channel)) {
			socialInstagramService.updateInstagramAccountIsValidStatus(pageId, 0);
		} else if (SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)) {
			youTubeAccountService.updateYoutubeAccountIsValidStatus(pageId, 0);
		} else if (SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel)) {
			tiktokSocialAccountService.updateTiktokAccountIsValidStatus(pageId, 0);
		}

	}


	@Override
	public String getGoogleAuthUrl(Long businessId, Boolean redirectToSetup) throws Exception {
		return googleSocialAccountService.getGoogleAuthUrl(businessId, redirectToSetup);
	}

	@Override
	public String getYoutubeAuthUrl(Long businessId, Boolean redirectToSetup,String domainName, String origin) throws Exception {
		return googleSocialAccountService.getYoutubeAuthUrl(businessId, redirectToSetup,domainName, origin);
	}

	@Override
	public void submitGetPageRequest(String channel, ChannelAuthRequest authRequest) throws Exception {
		logger.info(PAGE_INFO_FOR_CHANNEL_BUSINESS_ID, channel, authRequest.getBusinessId());

		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			fbSocialAccountService.submitFetchPageRequest(authRequest, Constants.ENTERPRISE);
		} else if (SocialChannel.GOOGLE_PLUS_GMB.getName().equalsIgnoreCase(channel) || SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			googleSocialAccountService.submitFetchPageRequest(authRequest.getBusinessId(), authRequest.getBirdeyeUserId(), authRequest.getGooglePlusCode(),
				 authRequest.getRedirectUri());
		} else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			twitterSocialPostService.submitFetchPageRequest(authRequest.getBusinessId(), authRequest.getBirdeyeUserId(), authRequest.getRequestToken(), authRequest.getRequestSecret(), authRequest.getOauthVerifier(),Constants.ENTERPRISE);
		} else if (SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)) {
			youTubeAccountService.submitChannelRequest(authRequest, Constants.ENTERPRISE);
		} else if(SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel) || SocialChannel.WHATSAPP.getName().equalsIgnoreCase(channel)) {
			ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));
			arborService.submitFetchPageRequest(authRequest, Constants.ENTERPRISE);
		}
	}

	@Override
	public void initiateGmbAccountFetch(ChannelAuthRequest authRequest,String type) {
		logger.info("request received to fetch gmb accounts for business {}",authRequest.getBusinessId());
		googleSocialAccountService.submitFetchAccountRequest(authRequest,type);
	}

	@Override
	public SSOResponse googleSSOAuth(GoogleSSOAuthRequest googleSSOAuthRequest) {
		logger.info("request received to fetch gmb sso details");
		return googleSocialAccountService.getGoogleSSOAuthDetails(googleSSOAuthRequest);
	}

	@Override
	public SSOResponse fbSSOAuth(FacebookSSOAuthRequest fbSSOAuthRequest) {
		logger.info("request received to fetch fb sso details for payload: {}", fbSSOAuthRequest);
		return fbSocialAccountService.getFbSSOAuthDetails(fbSSOAuthRequest);
	}

	@Override
	public SSOResponse fbSSOAuthUserDetails(FacebookSSOAuthRequest fbSSOAuthRequest) {
		logger.info("request received to fetch fb user details for user access token: {}", fbSSOAuthRequest);
		return fbSocialAccountService.getFacebookUserDetailsByAccessToken(fbSSOAuthRequest.getToken());
	}

	@Override
	public GoogleWhiteLabelAccountsResponse getWhiteLabelAccounts(GoogleWhiteLabelAccountsRequest request) {
		logger.info("request received to fetch gmb white label details");
		return googleSocialAccountService.getGoogleWhiteLabelAccounts(request);
	}

	@Override
	public void addWhiteLabelAccounts(List<String> urls) {
		logger.info("request received to add gmb white label urls");
		googleSocialAccountService.addWhiteLabelAccounts(urls);
	}

	@Override
	public void initiatePageFetchFreemium(FreemiumSetupRequest authRequest){
		logger.info("Request received to fetch pages for freemium:{}",authRequest);
		googleSocialAccountService.fetchAccountsFreemium(authRequest);
	}

	@Override
	public FreemiumFetchPageResponse fetchFreemiumPages(Integer SessionId){
		logger.info("Fetching pages for businessId:{}",SessionId);
		return (googleSocialAccountService.fetchPages(SessionId));
	}

	@Override
	public 	ChannelPageInfo connectFreemiumpage(FreemiumConnectRequest freemiumConnectRequest){
		return(googleSocialAccountService.connectFreemium(freemiumConnectRequest));
	}

	@Override
	public 	FreemiumStatusResponse fetchStatus(Integer sessionId){
		return(googleSocialAccountService.fetchStatusForRequest(sessionId));
	}

	@Override
	public void refreshGmbUserAccount(String userEmail, Long parentId,String type){
		logger.info("request received to refresh gmb accounts for user , business {} {}",userEmail,parentId);
		googleSocialAccountService.refreshGmbUserAccount(userEmail,parentId,type);
	}

	@Override
	public void gmbBackStatus(Long businessId) {
		logger.info("request received to change gmb request status to account fetched business {}",businessId);
		googleSocialAccountService.gmbBackStatus(businessId, Constants.ENTERPRISE);
	}

	@Override
	public GmbAccountInfo getGmbAccount(Long businessId,String type) {
		logger.info("request received to get gmb accounts for business {}",businessId);
		return googleSocialAccountService.getGmbAccount(businessId,type);
	}

	@Override
	public void gmbPageFetchByAccount(GMBAccountDTO gmbAccountDTO,Long parentId,String type) {
		logger.info("request received to initiate fetch page request for account {} and business {}",gmbAccountDTO.getAccountId(),parentId);
		gmbLocationDetailService.gmbPageFetchByAccount(gmbAccountDTO,parentId,type);
	}

	@Override
	public void cancelRequest(String channel, Long businessId, Boolean forceCancel) {
		logger.info("cancel request for channel {}  businessId {} ", channel, businessId);
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			fbSocialAccountService.cancelRequest(channel, businessId, forceCancel);
		} else if (SocialChannel.GOOGLE_PLUS_GMB.getName().equalsIgnoreCase(channel) || SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			googleSocialAccountService.cancelRequest(channel, businessId, forceCancel);
		} else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			twitterSocialPostService.cancelRequest(channel, businessId, forceCancel);
		} else if(SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)){
			youTubeAccountService.cancelRequest(channel,businessId, forceCancel);
		} else if(SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel)||SocialChannel.WHATSAPP.getName().equalsIgnoreCase(channel)){
			ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));
			arborService.cancelRequest(channel,businessId, forceCancel);
		}
	}

	@Override
	public ChannelPageInfo connectPagesV1(String channel, Long enterpriseId, Map<String, List<String>> pageIds, Integer accountId) throws Exception {
		ChannelPageInfo accountInfo = null;
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			accountInfo = fbSocialAccountService.connectPagesV1(pageIds.get(SocialChannel.FACEBOOK.getName()), enterpriseId, accountId);
		}
		if (SocialChannel.GOOGLE_PLUS_GMB.getName().equalsIgnoreCase(channel) || SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			accountInfo = googleSocialAccountService.connectGooglePagesV1(pageIds, enterpriseId, accountId);
		}
		if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			TwitterConnectAccountRequest twitterConnectAccountRequest= createAccountRequest(pageIds.get(SocialChannel.TWITTER.getName()),enterpriseId,accountId);
			accountInfo = twitterSocialPostService.connectTwitterPagesV1(twitterConnectAccountRequest);
		}
		if(SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)) {
			accountInfo =  youTubeAccountService.connectPages(pageIds.get(SocialChannel.YOUTUBE.getName()), enterpriseId, null, null,Constants.ENTERPRISE,accountId);
		} else if(SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel) || SocialChannel.WHATSAPP.getName().equalsIgnoreCase(channel)) {
			TwitterConnectAccountRequest tiktokConnectAccountRequest = createAccountRequest(pageIds.get(channel.toLowerCase()), enterpriseId, accountId);
			ArborService execute = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));
			accountInfo = execute.connectPage(tiktokConnectAccountRequest);
		}

		commonService.setDisabledAsNullForAllChannel(accountInfo);
		return accountInfo;
	}

	private TwitterConnectAccountRequest createAccountRequest(List<String> pageIds, Long enterpriseId, Integer accountId) {

		TwitterConnectAccountRequest twitterConnectAccountRequest= new TwitterConnectAccountRequest();
		twitterConnectAccountRequest.setId(pageIds);
		twitterConnectAccountRequest.setBusinessId(enterpriseId);
		twitterConnectAccountRequest.setAccountId(accountId);
		twitterConnectAccountRequest.setType(Constants.ENTERPRISE);
		return twitterConnectAccountRequest;
	}



	/*
	 * (non-Javadoc)
	 * @see com.birdeye.social.service.SocialAccountService#generateAuthenticationURL(java.lang.String)
	 */
	@Override
	public OAuthRequestMessage generateTwitterAuthenticationURL(Long businessId) throws Exception {
		logger.info("[Twitter Setup] Request Received to generate twitter authentication url");

		Business business = businessRepo.findByBusinessId(businessId);
		if (business == null) {
			logger.error("[Twitter Setup] Business not found for Business id {}", businessId);
			throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND);
		}
		ConsumerTokenAndSecret consumerToken = commonService.getAppKeyAndToken(business, "twitter");
		if (consumerToken == null) {
			logger.error("[Twitter Setup] Consumer Token and Secret not found for Business id {}", businessId);
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Consumer Token and Secret not found.");
		}
		try {
			Configuration config = new ConfigurationBuilder().build();
			Twitter twitter = new TwitterFactory(config).getInstance();
			twitter.setOAuthConsumer(consumerToken.getToken(), consumerToken.getSecret());

			return new OAuthRequestMessage(twitter.getOAuthRequestToken());
		}   catch (Exception ex) {
			logger.error("[Twitter Setup] Error occurred while Setting up Twitter {}",ex);
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Invalid Twitter Request");
		}
	}

	@Override
	public OAuthRequestMessage generateTwitterAuthenticationURL(Long businessId, String origin) throws Exception {
		logger.info("[Twitter Setup] Request Received to generate twitter authentication url with Origin {}", origin);

		BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLiteByNumber(businessId);
		if (businessLiteDTO == null) {
			logger.error("[Twitter Setup] Business not found for Business id {}", businessId);
			throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND);
		}
		return twitterSocialAccountService.generateTwitterAuthenticationURL(businessLiteDTO.getBusinessId(),origin,false);
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.SocialAccountService#removeInactiveIntegrations(java.lang.String, java.lang.Long)
	 */
	@Override
	public void removeInactiveIntegration(Long enterpriseId) {
		logger.info("Request Received to remove inactive integration for enterprise {}", enterpriseId);
		notificationService.disableGMBNotification(enterpriseId);
		fbSocialAccountService.removeInactiveIntegration(SocialChannel.FACEBOOK.name(), enterpriseId);
		googleSocialAccountService.removeGMBInactiveIntegration(SocialChannel.GMB.name(), enterpriseId);
		twitterSocialAccountService.removeInactiveIntegration(SocialChannel.TWITTER.name(), enterpriseId);
		socialLinkedinService.removeInactiveIntegration(SocialChannel.LINKEDIN.name(), enterpriseId);
		socialInstagramService.removeInactiveIntegration(SocialChannel.INSTAGRAM.name(), enterpriseId);
		youTubeAccountService.removeYoutubeChannelByEnterprise(SocialChannel.YOUTUBE.getName(),enterpriseId);
	}

	@Override
	public void removeInactiveBusinessIntegration(BusinessStatusData request) {
		if(Objects.nonNull(request.getBusinessId()) && BusinessActivationStatusEnum.SUSPENDED.getStatus().equalsIgnoreCase(request.getActivationStatus())) {
			Integer businessId = request.getBusinessId();
			logger.info("Request Received to remove inactive integration for businessId {}", businessId);
			if(request.getIsEnterpriseLocation()){
				if(BusinessTypeEnum.BUSINESS.getName().equalsIgnoreCase(request.getBusinessType())){
					removePageMapping(businessId);
					socialPostService.removeScheduledPosts(request.getEnterpriseId(),request.getBusinessId(),true);
				}else if(BusinessTypeEnum.ENTERPRISE_LOCATION.getName().equalsIgnoreCase(request.getBusinessType())){
					socialPostService.deleteScheduledPosts(request.getEnterpriseId(),true);
					removeInactiveIntegration(request.getBusinessNumber());
				}
			}else if (request.getIsEnterprise()){
				socialPostService.deleteScheduledPosts(request.getEnterpriseId(), true);
				removeInactiveIntegration(request.getBusinessNumber());
				disableKeyWordsAndRule(request.getBusinessId());
			}else if (request.getIsSMB()){
				socialPostService.deleteScheduledPosts(businessId, true);
				disableAllIntegrationBusinessLevel(businessId);
				disableKeyWordsAndRule(businessId);
			}
			removeSocialBusinessProperty(request);
		}
	}


	private void removeSocialBusinessProperty(BusinessStatusData request) {
		logger.info("Removing entry from business property for enterpriseId: {}", request.getBusinessNumber());
		if(Objects.nonNull(request.getBusinessNumber())) {
			socialBusinessPropertyRepo.deleteByEnterpriseId(request.getBusinessNumber());
		}
	}

	private void removePageMapping(Integer businessId) {
		notificationService.disableBusinessGMBNotification(businessId);
		fbSocialAccountService.removePageMap(businessId);
		googleSocialAccountService.removePageMap(SocialChannel.GMB.name(), businessId);
		twitterSocialAccountService.removePageMap(SocialChannel.TWITTER.name(), businessId);
		socialInstagramService.removePageMap(businessId);
		youTubeAccountService.removePageMap(businessId);
		socialLinkedinService.removePageMap(businessId);
	}

	private void disableKeyWordsAndRule(Integer enterpriseId) {
		businessGnipRuleService.inactivateAndDeleteGnip(enterpriseId);
		socialMentionService.inactivateKeywords(enterpriseId);
	}

	private void disableAllIntegrationBusinessLevel(Integer businessId) {
		logger.info("RemoveIntegration for business id : {}",businessId);
		notificationService.disableBusinessGMBNotification(businessId);
		fbSocialAccountService.removeBusinessInactiveIntegration(SocialChannel.FACEBOOK.name(), businessId);
		googleSocialAccountService.removeBusinessGMBInactiveIntegration(SocialChannel.GMB.name(), businessId);
		twitterSocialAccountService.removeBusinessInactiveIntegration(SocialChannel.TWITTER.name(), businessId);
		socialLinkedinService.removeBusinessInactiveIntegration(SocialChannel.LINKEDIN.name(), businessId);
		socialInstagramService.removeBusinessInactiveIntegration(SocialChannel.INSTAGRAM.name(), businessId);
		youTubeAccountService.removeYoutubeChannelByBusinessId(SocialChannel.YOUTUBE.name(),businessId);
	}

	@Override
	public void restoreRules(BusinessStatusData request) {
		if(Objects.nonNull(request.getBusinessId()) && Objects.nonNull(request.getFromStatus())
				&& Objects.nonNull(request.getToStatus()) && (request.getIsEnterpriseLocation() || request.getIsSMB()) && BusinessActivationStatusEnum.PAID.getStatus().equalsIgnoreCase(request.getToStatus())
		&& BusinessActivationStatusEnum.SUSPENDED.getStatus().equalsIgnoreCase(request.getFromStatus())) {
			Integer businessId = request.getBusinessId();
			logger.info("Request Received to restore rules for businessId {}", businessId);
			fbSocialAccountService.restoreBusinessInactiveIntegration(SocialChannel.FACEBOOK.name(), businessId);
			googleSocialAccountService.restoreBusinessInactiveIntegration(SocialChannel.GMB.name(), businessId);
			twitterSocialAccountService.restoreBusinessInactiveIntegration(SocialChannel.TWITTER.name(), businessId);
			businessGnipRuleService.activateGnip(businessId);
			socialMentionService.activateKeywords(businessId);
		}
	}

	private void evictBusinessCache(Integer sourceBusinessAccountId,Integer targetBusinessAccountId, Long sourceBusinessAccountNumber, Long targetBusinessAccountNumber) {
		if(Objects.nonNull(sourceBusinessAccountId)) {
			businessCoreService.clearBusinessCache(sourceBusinessAccountId,true);
			businessCoreService.clearBusinessCache(sourceBusinessAccountId,false);
		}
		if(Objects.nonNull(targetBusinessAccountId)) {
			businessCoreService.clearBusinessCache(targetBusinessAccountId,true);
			businessCoreService.clearBusinessCache(targetBusinessAccountId,false);
		}
		if(Objects.nonNull(sourceBusinessAccountNumber)) {
			businessCoreService.clearBusinessCache(sourceBusinessAccountNumber);
		}
		if(Objects.nonNull(targetBusinessAccountNumber)) {
			businessCoreService.clearBusinessCache(targetBusinessAccountNumber);
		}
	}

	@Override
	public void moveBusinessLocation(LocationMovementDto locationMovementDto) {
		logger.info(" {} Request Received for location movement with Event Id {}",Constants.LOCATION_MOVEMENT_LOG_PREFIX,locationMovementDto.getEventId());
		LocationMovementKafkaDTO locationMovementKafkaDTO = new LocationMovementKafkaDTO(
				locationMovementDto.getEventId(), Constants.SOCIAL_MODULE);

		try {
			Long sourceBusinessAccountNumber = null;
			Integer sourceBusinessAccountId = null;
			Long targetBusinessAccountNumber = null;
			Integer targetBusinessAccountId = null;
			boolean isSingleLocationSource = true; // if source is single location then move all the enagage/streams data otherwise keep data with source
			String targetBusinessType = locationMovementDto.getTargetBusinessType();

			if (LocationMovementEnum.UPGRADE.getName().equalsIgnoreCase(locationMovementDto.getEventType())) {

				sourceBusinessAccountNumber = locationMovementDto.getSourceBusinessNumber();
				sourceBusinessAccountId = locationMovementDto.getSourceBusinessId();
				targetBusinessAccountNumber = locationMovementDto.getTargetEnterpriseNumber();
				targetBusinessAccountId = locationMovementDto.getTargetEnterpriseId();
				if(!Objects.equals(locationMovementDto.getSourceBusinessId(),locationMovementDto.getTargetEnterpriseId())){
					socialPostService.movePostScheduleData(locationMovementDto.getSourceBusinessId(),
							locationMovementDto.getSourceBusinessId(),locationMovementDto.getTargetEnterpriseId());
				}

			} else if (LocationMovementEnum.MOVE.getName().equalsIgnoreCase(locationMovementDto.getEventType())) {

				sourceBusinessAccountNumber = Objects.nonNull(locationMovementDto.getSourceEnterpriseNumber())
						? locationMovementDto.getSourceEnterpriseNumber()
						: locationMovementDto.getSourceBusinessNumber();
				sourceBusinessAccountId = Objects.nonNull(locationMovementDto.getSourceEnterpriseId())
						? locationMovementDto.getSourceEnterpriseId()
						: locationMovementDto.getSourceBusinessId();
				targetBusinessAccountNumber = locationMovementDto.getTargetEnterpriseNumber();
				targetBusinessAccountId = locationMovementDto.getTargetEnterpriseId();
				if (Objects.nonNull(locationMovementDto.getSourceEnterpriseNumber())) {
					isSingleLocationSource = false;
				}
				if(!Objects.equals(locationMovementDto.getSourceEnterpriseId(),locationMovementDto.getTargetEnterpriseId())){
					socialPostService.removeScheduledPosts(locationMovementDto.getSourceEnterpriseId(),locationMovementDto.getSourceBusinessId(), false);
				}
			} else if (LocationMovementEnum.DOWNGRADE.getName().equalsIgnoreCase(locationMovementDto.getEventType())) {

				sourceBusinessAccountNumber = locationMovementDto.getSourceEnterpriseNumber();
				sourceBusinessAccountId = locationMovementDto.getSourceEnterpriseId();
				targetBusinessAccountNumber = locationMovementDto.getTargetBusinessNumber();
				targetBusinessAccountId = locationMovementDto.getTargetBusinessId();
				if (!Objects.equals(sourceBusinessAccountNumber, targetBusinessAccountNumber)) {
					isSingleLocationSource = false;
					socialPostService.removeScheduledPosts(sourceBusinessAccountId,locationMovementDto.getSourceBusinessId(), false);
				}else {
					logger.warn("{} Deleting unmapped location for SMB  {}",Constants.LOCATION_MOVEMENT_LOG_PREFIX,sourceBusinessAccountNumber);
					Integer businessId = locationMovementDto.getSourceBusinessId();
					googleSocialAccountService.removeUnmappedByEnterprise(sourceBusinessAccountNumber, businessId);
					fbSocialAccountService.removeUnmappedByEnterprise(sourceBusinessAccountNumber, businessId);
					twitterSocialAccountService.removeUnmappedByEnterprise(sourceBusinessAccountNumber, businessId);
					socialPostService.movePostScheduleData(locationMovementDto.getSourceBusinessId(),locationMovementDto.getSourceEnterpriseId(), targetBusinessAccountId);
					socialLinkedinService.removeUnmappedPages(sourceBusinessAccountNumber, businessId);
					youTubeAccountService.removeUnmappedByEnterprise(sourceBusinessAccountNumber,businessId);
					socialInstagramService.removeUnmappedByEnterprise(sourceBusinessAccountNumber,businessId);
				}
			}else {
				logger.warn("{} Request Data is not correct for Event {}",Constants.LOCATION_MOVEMENT_LOG_PREFIX,locationMovementDto.getEventType());
				return;
			}
			logger.info("{} Request Data Event Type : {}, sourceAccountNumber - sourceAccountId(SMB/Enterprise) : {} - {} ,targerAccountNumber - targetAccountId(SMB/Enterprise) {} - {}",Constants.LOCATION_MOVEMENT_LOG_PREFIX ,locationMovementDto.getEventType(),sourceBusinessAccountNumber,sourceBusinessAccountId,targetBusinessAccountNumber,targetBusinessAccountId);
			evictBusinessCache(sourceBusinessAccountId,targetBusinessAccountId, sourceBusinessAccountNumber, targetBusinessAccountNumber);

			if (Objects.nonNull(targetBusinessAccountNumber) && Objects.nonNull(targetBusinessAccountId)) {
				if (!Objects.equals(sourceBusinessAccountNumber, targetBusinessAccountNumber)) {
					googleSocialAccountService.moveGmbAccountLocation(sourceBusinessAccountNumber, targetBusinessAccountNumber, locationMovementDto.getSourceBusinessId(), !isSingleLocationSource,targetBusinessAccountId);
					fbSocialAccountService.moveFBAccountLocation(sourceBusinessAccountNumber, targetBusinessAccountNumber, locationMovementDto.getSourceBusinessId(), !isSingleLocationSource,targetBusinessAccountId);
					twitterSocialAccountService.moveTwitterAccountLocation(sourceBusinessAccountNumber, targetBusinessAccountNumber, locationMovementDto.getSourceBusinessId(), !isSingleLocationSource,targetBusinessAccountId);
					youTubeAccountService.moveYoutubeAccountLocation(sourceBusinessAccountNumber, targetBusinessAccountNumber, locationMovementDto.getSourceBusinessId(), !isSingleLocationSource,targetBusinessAccountId);
					socialLinkedinService.moveLinkedinAccountLocation(sourceBusinessAccountNumber, targetBusinessAccountNumber, locationMovementDto.getSourceBusinessId(), !isSingleLocationSource,targetBusinessAccountId);
					socialInstagramService.moveInstagramAccountLocation(sourceBusinessAccountNumber, targetBusinessAccountNumber, locationMovementDto.getSourceBusinessId(), !isSingleLocationSource,targetBusinessAccountId);
				}
				if (!Objects.equals(sourceBusinessAccountId, targetBusinessAccountId) && isSingleLocationSource) {
					socialMentionService.updateEnterpriseBusinessId(sourceBusinessAccountId, targetBusinessAccountId);
					businessGnipRuleService.updateEnterpriseBusinessId(sourceBusinessAccountId, targetBusinessAccountId);
				}
			}
			locationMovementKafkaDTO.setStatus(Constants.SUCCESS);

		} catch (Exception e) {
			logger.warn("{} Error occurred in location movement ",Constants.LOCATION_MOVEMENT_LOG_PREFIX,e);
			locationMovementKafkaDTO.setStatus(Constants.FAILURE);

		}
		logger.info("{} Location movement success : {}",Constants.LOCATION_MOVEMENT_LOG_PREFIX,locationMovementDto.getEventId());

		kafkaProducer.sendWithKey(Constants.LOCATION_MOVEMENT_RESPONSE_AUDIT, locationMovementDto.getEventId().toString(),
				locationMovementKafkaDTO);

		// Google messaging is deprecated
		/*
		if (locationMovementKafkaDTO.getStatus().equals(Constants.SUCCESS)) {
			kafkaProducer.sendObject(Constants.LOCATION_MOVEMENT_GBM, locationMovementDto);
		}
		 */
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.SocialAccountService#updateInvalidPageCounter(java.lang.String)
	 */
	@Async
	@Override
	public void updateInvalidPageCounter(String pageId, Object details) {
		logger.info("Request received to updateInvalidPageCounter for FB pageId {} ", pageId);
			fbSocialAccountService.updateInvalidPageCounter(pageId, details);

	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.SocialAccountService#markFBIntegrationInvalid()
	 */
	@Override
	public void markFBIntegrationInvalid() {
		logger.info("Request received to markFBIntegrationInvalid");
		fbSocialAccountService.markFBIntegrationInvalid();

	}


	/* (non-Javadoc)
	 * @see com.birdeye.social.service.SocialAccountService#reconnectAllPage(java.lang.String, com.birdeye.social.sro.ChannelAllPageReconnectRequest, java.lang.Long, java.lang.Integer)
	 */
	@Override
	public void reconnectAllPage(String channel, ChannelAllPageReconnectRequest request, Long businessId, Integer userId,String type) throws Exception {
		// Reconnect flow requestId based
		logger.info("Request received to Reconnect Pages for channel {} : businessId {} and page Id {}", channel, businessId, request.getPageId());
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			fbSocialAccountService.reconnectFBPagesEnhancedFlow(businessId, request, userId);
		}
		if (SocialChannel.GOOGLE_PLUS_GMB.getName().equalsIgnoreCase(channel) || SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			googleSocialAccountService.reconnectGMBPagesEnhancedFlow(businessId, request.getPageId(), request.getAccessToken(), request.getRedirectUri(), userId,type);
		}
		if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			TwitterAuthRequest twitterAuthRequest= createAuthRequest(businessId, request.getPageId(), request.getRequestToken(), request.getRequestSecret(), request.getOauthVerifier(), userId);
			twitterSocialPostService.reconnectTwitterEnhancedFlow(twitterAuthRequest);
		}
		if(SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)) {
			youTubeAccountService.reconnectYoutubeChannel(businessId, request.getPageId(), request.getAccessToken(), request.getRedirectUri(), userId,type);
		} else if(SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel)) {
			ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));

			arborService.reconnectTiktokAccount(businessId, request, userId, Constants.ENTERPRISE);
		}
	}

	private TwitterAuthRequest createAuthRequest(Long businessId, List<String> pageId, String requestToken, String requestSecret,
												 String oauthVerifier, Integer userId) {
		TwitterAuthRequest twitterAuthRequest= new TwitterAuthRequest();
		twitterAuthRequest.setType(Constants.ENTERPRISE);
		twitterAuthRequest.setBusinessId(businessId);
		twitterAuthRequest.setBirdeyeUserId(userId);
		twitterAuthRequest.setTempAccessToken(requestToken);
		twitterAuthRequest.setOauthVerifier(oauthVerifier);
		twitterAuthRequest.setSecret(requestSecret);
		twitterAuthRequest.setPageId(pageId);
		return twitterAuthRequest;
	}

	@Override
	@Deprecated
	public ChannelPageInfo getIntegrationRequestInfo(String channel, Long businessId, Boolean reconnectFlag) throws Exception {
		logger.info("Request received to checkstatus for channel {} and business {}", channel, businessId);
		String requestType = null;

		if(reconnectFlag) {
			requestType = "reconnect";
		} else {
			requestType = "connect";
		}

		ChannelPageInfo response = null;
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			response = fbSocialAccountService.getFBIntegrationRequestStatus(businessId, requestType);
		} else if (SocialChannel.GOOGLE_PLUS_GMB.getName().equalsIgnoreCase(channel) || SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			response = googleSocialAccountService.getGoogleIntegrationRequestInfo(businessId, requestType);
		} else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			response = twitterSocialPostService.getTwitterIntegrationRequestInfo(businessId, requestType);
		}
		ObjectMapper objectMapper = new ObjectMapper();
		logger.info("check status response for businessId {} {} {}",businessId,channel,response!=null?objectMapper.writeValueAsString(response):"no response");
		return response;
	}

	@Override
	public CheckStatusResponse getIntegrationRequestStatus(String channel, Long businessId, Boolean reconnectFlag){
		logger.info("Request received to check status for channel {} and business {}", channel, businessId);
		return getIntegrationStatus(channel, businessId, reconnectFlag);
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.SocialAccountService#getReconnectSummary(java.lang.String, java.lang.Long, java.lang.Boolean, java.lang.Integer)
	 */

	@Override
	public LastReconnectDetails getIntegrationSummary(String channel, Long businessId, Boolean reconnectFlag) {
		logger.info("Request received to getIntegrationSummary for channel {} business {}", channel, businessId);
		LastReconnectDetails response = null;
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel) || SocialChannel.TWITTER.getName().equalsIgnoreCase(channel) || SocialChannel.LINKEDIN.getName().equalsIgnoreCase(channel) || SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(channel) || SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel) || SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel)) {
			response = commonService.getIntegrationSummary(businessId, reconnectFlag, channel);
		} else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel) || SocialChannel.GOOGLE_PLUS_GMB.getName().equalsIgnoreCase(channel)) {
			response = commonService.getIntegrationSummary(businessId, reconnectFlag, SocialChannel.GMB.getName());
		}
		return response;
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.SocialAccountService#updateFBTokenPermissionsForAllPages()
	 */
	@Override
	public void updateFBTokenPermissionsForAllPages() {
		fbSocialAccountService.updateFBTokenPermissionsForAllPages();

	}

	@Override
	public void backupPages(List<Long> enterpriseIds) {
		logger.info("Request received to start backup for enterpriseId {}", enterpriseIds);

		enterpriseIds.stream().forEach(eid ->{
			try {
				List<SocialPagesAudit> socialPagesAudit = socialPagesAuditRepo.findByEnterpriseIdOrderByRemovedOnDateDesc(eid);
				socialPagesAudit.stream().forEach(socialPage -> {
					try {
						SocialChannel channel = SocialChannel.getSocialChannelByName(socialPage.getChannel());
						switch (channel) {
							case GMB:
								googleSocialAccountService.backupGMBPages(socialPage);
								break;
							case TWITTER:
								twitterSocialAccountService.backupTwitterPages(socialPage);
								break;
							case FACEBOOK:
								fbSocialAccountService.backupFBPages(socialPage);
								break;
							case LINKEDIN:
								socialLinkedinService.backupLinkedinPages(socialPage);
								break;
							case INSTAGRAM:
								socialInstagramService.backupInstagramPages(socialPage);
								break;
							default:
								logger.error("Given channel {} not exits", channel);
								break;
						}
					} catch (Exception e) {
						logger.error("Error while storing for socialpage {} : ", socialPage);
					}
				});
			} catch(Exception e) {
				logger.error("Error while storing for EnterpiseId {} :s ",
						eid);
			}
		});
	}

	@Override
	public List<Integer> migrate(Integer limit) {
		return fbSocialAccountService.migrateForRatings(limit);
	}

	@Override
	public ChannelConnectedPageInfo checkForConnectedPages(Long accountId, String channel) {
		logger.info("Request received to check for connected pages for account {}, channel {}", accountId, channel);
		ChannelConnectedPageInfo channelWisePageInfo = new ChannelConnectedPageInfo();
		if(StringUtils.isEmpty(channel) || SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			boolean fbPageExists = fbSocialAccountService.checkIfPageExistsByAccountId(accountId);
			channelWisePageInfo.setFbPageExists(fbPageExists);
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			boolean gmbLocationExists = googleSocialAccountService.checkIfAccountExistsByAccountId(accountId);
			channelWisePageInfo.setGmbLocationExists(gmbLocationExists);
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			boolean twitterAccountExists = twitterSocialAccountService.checkIfAccountExistsByAccountId(accountId);
			channelWisePageInfo.setTwitterAccountExists(twitterAccountExists);
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.LINKEDIN.getName().equalsIgnoreCase(channel)) {
			boolean linkedinPageExists = socialLinkedinService.checkIfAccountExistsByAccountId(accountId);
			channelWisePageInfo.setLinkedinAccountExists(linkedinPageExists);
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(channel)) {
			boolean instagramAccountExists = socialInstagramService.checkIfAccountExistsByAccountId(accountId);
			channelWisePageInfo.setInstagramAccountExists(instagramAccountExists);
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)) {
			boolean youtubeChannelExists = youTubeAccountService.checkIfAccountExistsByAccountId(accountId);
			channelWisePageInfo.setYoutubeChannelsExists(youtubeChannelExists);
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.WHATSAPP.getName().equalsIgnoreCase(channel)) {
			boolean whatsappChannelExists = whatsappSocialAccountService.checkIfAccountExistsByAccountId(accountId);
			channelWisePageInfo.setWhatsappChannelsExists(whatsappChannelExists);
		}
		return channelWisePageInfo;
	}

	@Override
	public ChannelSetupStatus getChannelWiseSetupStatus(Long enterpriseId, String channel) {
		logger.info("Request received to check channel-wise page setup status for enterprise {}, channel {}", enterpriseId, channel);
		ChannelSetupStatus channelSetupStatus = new ChannelSetupStatus();
		if(StringUtils.isEmpty(channel) || SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			channelSetupStatus.setFacebook(fbSocialAccountService.getPageSetupStatus(enterpriseId));
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			channelSetupStatus.setGmb(googleSocialAccountService.getPageSetupStatus(enterpriseId));
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			channelSetupStatus.setTwitter(twitterSocialPostService.getPageSetupStatus(enterpriseId));
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.LINKEDIN.getName().equalsIgnoreCase(channel)) {
			channelSetupStatus.setLinkedin(socialLinkedinService.getPageSetupStatus(enterpriseId));
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(channel)) {
			channelSetupStatus.setInstagram(socialInstagramService.getPageSetupStatus(enterpriseId));
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.APPLE.getName().equalsIgnoreCase(channel)) {
			channelSetupStatus.setApple(appleChatService.getPageSetupStatus(enterpriseId));
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.APPLE_CONNECT.getName().equalsIgnoreCase(channel)) {
			channelSetupStatus.setAppleConnect(appleAccountService.getPageSetupStatus(enterpriseId));
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)){
			channelSetupStatus.setYoutube(youTubeAccountService.getPageSetupStatus(enterpriseId));
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel)){
			ArborService arborService = ArborServiceFactory.getService(SocialChannel.TIKTOK);
			channelSetupStatus.setTiktok(arborService.getPageSetupStatus(enterpriseId));
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.WHATSAPP.getName().equalsIgnoreCase(channel)){
			ArborService arborService = ArborServiceFactory.getService(SocialChannel.WHATSAPP);
			channelSetupStatus.setWhatsApp(arborService.getPageSetupStatus(enterpriseId));
		}
		return channelSetupStatus;
	}

	@Override
	public ChannelSetupStatus getChannelWisePageConnectionStatus(Long enterpriseId, String channel) {
		logger.info("Request received to check channel-wise page connection setup status for enterprise {}, channel {}", enterpriseId, channel);
		ChannelSetupStatus channelSetupStatus = new ChannelSetupStatus();
		if(StringUtils.isEmpty(channel) || SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			channelSetupStatus.setFacebook(fbSocialAccountService.getPageConnectionSetupStatus(enterpriseId));
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			channelSetupStatus.setGmb(googleSocialAccountService.getPageConnectionSetupStatus(enterpriseId));
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			channelSetupStatus.setTwitter(twitterSocialPostService.getPageConnectionSetupStatus(enterpriseId));
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.LINKEDIN.getName().equalsIgnoreCase(channel)) {
			channelSetupStatus.setLinkedin(socialLinkedinService.getPageConnectionSetupStatus(enterpriseId));
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(channel)) {
			channelSetupStatus.setInstagram(socialInstagramService.getPageConnectionSetupStatus(enterpriseId));
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.APPLE.getName().equalsIgnoreCase(channel)) {
			channelSetupStatus.setApple(appleChatService.getPageConnectionSetupStatus(enterpriseId));
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.APPLE_CONNECT.getName().equalsIgnoreCase(channel)) {
			channelSetupStatus.setAppleConnect(appleAccountService.getPageConnectionSetupStatus(enterpriseId));
		}
		if(StringUtils.isEmpty(channel) || SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)){
			channelSetupStatus.setYoutube(youTubeAccountService.getPageConnectionSetupStatus(enterpriseId));
		}
		return channelSetupStatus;
	}

	@Override
	public BusinessIntegrationStatus getBusinessIntegrationStatus(Integer businessId) {
		logger.info("Request received for getBusinessIntegrationStatus {}", businessId);
		final BusinessIntegrationStatus businessIntegrationStatus = new BusinessIntegrationStatus();
		businessIntegrationStatus.setBusinessId(businessId);
		Map<String, BusinessIntegrationStatus.ChannelIntegrationInfo> integrationInfoMap = businessIntegrationStatus.getIntegrationStatuses();
		try {
			integrationInfoMap.put(SocialChannel.GMB.getName(), googleSocialAccountService.getPageIntegrationStatus(businessId));
		}
		catch (Exception e) {
			businessIntegrationStatus.getErrors().put(SocialChannel.GMB.getName(),
					new BusinessIntegrationStatus.BusinessIntegrationError(e.getMessage(), 500));
		}

		try {
			integrationInfoMap.put(SocialChannel.FACEBOOK.getName(), fbSocialAccountService.getPageIntegrationStatus(businessId));
		}
		catch (Exception e) {
			businessIntegrationStatus.getErrors().put(SocialChannel.FACEBOOK.getName(),
					new BusinessIntegrationStatus.BusinessIntegrationError(e.getMessage(), 500));
		}
		try {
			integrationInfoMap.put(SocialChannel.TWITTER.getName(), twitterSocialPostService.getPageIntegrationStatus(businessId));
		}
		catch (Exception e) {
			businessIntegrationStatus.getErrors().put(SocialChannel.TWITTER.getName(),
					new BusinessIntegrationStatus.BusinessIntegrationError(e.getMessage(), 500));
		}
		businessIntegrationStatus.setIntegrationStatuses(integrationInfoMap);
		logger.info("Returning response getBusinessIntegrationStatus {}", businessIntegrationStatus);
		return businessIntegrationStatus;
	}

	@Override
	public void processAutoMappingInitRequest(AutoMappingRequest request) {
		logger.info("request received for Auto Mapping Init Request: enterprise: {}" +
						"channel: {}", request.getEnterpriseId(),request.getChannel());
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(request.getChannel())) {
			fbSocialAccountService.processAutoMappingInitFbRequest(request.getEnterpriseId());
		}
		else if (SocialChannel.GMB.getName().equalsIgnoreCase(request.getChannel())) {
			googleSocialAccountService.processAutoMappingInitRequest(request.getEnterpriseId());
		}

	}

	@Override
	public void processAutoMappingMatchedRequest(AutoMappingMatchedRequest request) {
		logger.info("request received for auto mapping of enterprise: {}, businessId: {}, " +
				"profileId: {}, channel: {}", request.getEnterpriseId(),request.getBusinessId(),
				request.getProfileId(),request.getChannel());
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(request.getChannel())) {
			fbSocialAccountService.processAutoMappingMatchedRequest(request);
		} else if (SocialChannel.GMB.getName().equalsIgnoreCase(request.getChannel())) {
			googleSocialAccountService.processAutoMappingMatchedRequest(request);
		}
	}

	@Override
	public void processDisconnectedLocations(String channel) {

		logger.info("Request received to check for diconnected pages for channel {}", channel);

		if (StringUtils.isEmpty(channel) || SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			googleSocialAccountService.fetchDisconnectedAndStore();
		}
		if (StringUtils.isEmpty(channel) || SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			fbSocialAccountService.fetchDisconnectedAndStore();
		}
		if (StringUtils.isEmpty(channel) || SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			twitterSocialAccountService.fetchDisconnectedAndStore();
		}
		if (StringUtils.isEmpty(channel) || SocialChannel.LINKEDIN.getName().equalsIgnoreCase(channel)) {
			linkedinService.fetchDisconnectedAndStore();

		}
		if (StringUtils.isEmpty(channel) || SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(channel)) {
			socialInstagramService.fetchDisconnectedAndStore();

		}
	}

	/**
	 * Common function to be called for action on disconnected enterprise
	 */
	@Override
	public void performActionOnDisconnected(ChannelDisconnectRequest channelDisconnectRequest) {
		long  enterpriseBusinessNumber = channelDisconnectRequest.getEnterpriseId();
		String channel = channelDisconnectRequest.getChannel();
		logger.info("{} {} Starting performActionOnGMBDisconnected for Business(SMB/Enterprise/Reseller) id {}",Constants.CHANNEL_DISCONNECT_PREFIX,channel,enterpriseBusinessNumber);
		BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest();
		businessLiteRequest.setKey("businessNumber");
		businessLiteRequest.setValue(enterpriseBusinessNumber);
		BusinessLiteDTO businessInfo = businessCoreService.getBusinessLite(businessLiteRequest);
		try {
			if (!businessInfo.getActivationStatus().equals("suspended")) {
				int enterpriseId = businessInfo.getBusinessId();
				List<String> users = businessService.getEnterpriseUsers(enterpriseId);
				if( users != null && users.size()>0) {
					for(String user : users) {
						logger.info("{} {} Sending to kafka for email for Business(SMB/Enterprise/Reseller) id {}",Constants.CHANNEL_DISCONNECT_PREFIX, channel,
								enterpriseBusinessNumber);

						nexusService.sendEmail(prepareMetadataForDisconEmail(enterpriseId,
								user,channelDisconnectRequest.getChannel()),
								createDataForDisconnectedMail(businessInfo,channelDisconnectRequest.getChannel()));
					}
					disconnectedMailAuditService.saveAudit(businessInfo,channel,users,Constants.SUCCESS,null);
				} else {
					logger.warn("{} {} No users found for sending mail {} {}",Constants.CHANNEL_DISCONNECT_PREFIX, channel,
							enterpriseBusinessNumber,businessInfo.getActivationStatus() );
					disconnectedMailAuditService.saveAudit(businessInfo,channel,null,Constants.FAILURE,"No users found for sending mail");
				}
			} else {
				logger.warn("{} {} Business activation status is not eligible for notification {} {}",Constants.CHANNEL_DISCONNECT_PREFIX, channel,
						enterpriseBusinessNumber,businessInfo.getActivationStatus() );
				disconnectedMailAuditService.saveAudit(businessInfo,channel,null,Constants.FAILURE,"Business activation status is not eligible for notification");
			}

		} catch (Exception e) {
			logger.error("{} {} Some error occurred in  performActionOnGMBDisconnected for Business(SMB/Enterprise/Reseller) id due to {} {}",Constants.CHANNEL_DISCONNECT_PREFIX,channel,enterpriseBusinessNumber,e.getMessage());
			disconnectedMailAuditService.saveAudit(businessInfo,channel,null,Constants.FAILURE,e.getMessage());
		}
	}



	  private EmailDTO prepareMetadataForDisconEmail(Integer businessId, String user,String channel) {
		  EmailDTO emailDto = new EmailDTO();
		  emailDto.setBusinessId(businessId);
		  emailDto.addTo(user);
		  emailDto.setExternalUuid(StringUtils.join(Constants.CHANNEL_DISCONNECTED_UUID_STRING, "-", businessId.toString()));
		  emailDto.setSubject(String.format("Let's reconnect your %s pages", SocialChannel.getSocialChannelByName(channel).getLabel())); // TODO verify, msged Shikha
		  emailDto.setRequestType(Constants.CHANNEL_DISCONNECTED_EMAIL_TYPE);
		  return emailDto;
	  }

	  private Map<String, Object> createDataForDisconnectedMail(BusinessLiteDTO businessInfo,String channel) {
		  Map<String,Object> dataObject = new HashMap<>();
			String businessName = null != businessInfo.getBusinessAlias() ? businessInfo.getBusinessAlias()
					: businessInfo.getBusinessName();
		  dataObject.put("businessName",businessName) ;
		  dataObject.put("channelName",SocialChannel.getSocialChannelByName(channel).getLabel());
		  dataObject.put("connectCTA",getConnectCTA(businessInfo,channel));
		  dataObject.put("sellerColumnShow",BusinessAccountTypeEnum.WHITELABELED.getName().equalsIgnoreCase(businessInfo.getAccountType()) ? true : false);
		  return dataObject;
	  }

	  @Override
	public String getConnectCTA(BusinessLiteDTO businessInfo, String channel) {

		logger.info("{} In getConnectCTA for getting connectionCTA for Business(SMB/Enterprise/Reseller) short id {}",Constants.CHANNEL_DISCONNECT_PREFIX,channel,businessInfo.getBusinessId());

		StringBuilder connectGmbCTA = new StringBuilder("");

		if (SocialChannel.GMB.getName().equalsIgnoreCase(channel) || SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel) || SocialChannel.LINKEDIN.getName().equalsIgnoreCase(channel)) {
			GenerateRequest generateRequest = new GenerateRequest();
			generateRequest.setEnterpriseId(businessInfo.getBusinessNumber());
			String openURL = socialTokenService.generateFreshUrl(channel, generateRequest, OpenUrlSourceEnum.API.getName());
			connectGmbCTA.append(openURL);
		}else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			WebsiteDomainInfo businessDomain = businessService.getBusinessDomain(businessInfo.getBusinessId());
			connectGmbCTA.append(getDomainBaseURL(businessDomain));
			connectGmbCTA.append(Constants.DASHBOARD_ENTERPRISE_SETUP_SOCIAL_CONNECT_URL);
			connectGmbCTA.append(SocialChannel.TWITTER.getName());
		}
		logger.info("{} {} In getConnectCTA : Final connectCTA for Business(SMB/Enterprise/Reseller) long id {} {}",Constants.CHANNEL_DISCONNECT_PREFIX,channel,businessInfo.getBusinessNumber(),connectGmbCTA);

		return connectGmbCTA.toString();
	}

	  private String getDomainBaseURL(WebsiteDomainInfo domainMessage) {
		  String protocal = domainMessage.getSecureEnabled() != null && domainMessage.getSecureEnabled() == 1 ? "https" : "http";
		  return protocal + "://" + domainMessage.getDomainName();
	  }


	private void pushFbAutoMappingInFirebase(Long enterpriseId, String status,String channel) {
		String topicSocial = AUTOMAPPING+channel+"/"+enterpriseId;
		nexusService.insertMapInFirebase(topicSocial,"socialAutoMappingStatus", status);
	}

	@Override
	public void updateFirebaseForAutoMapping() {
		long curTimeInMs = new Date().getTime();
		Date afterRemovingMins = new Date(curTimeInMs - (autoMappingTimeout * 60000));
		List <AutoMapping> autoMappingList = autoMappingRepo.findByStatusAndCreated(Status.INITIAL.getName(),afterRemovingMins);
		autoMappingList.stream().forEach(value->{
			pushFbAutoMappingInFirebase(value.getEnterpriseId(), Status.COMPLETE.getName(),value.getChannel());
			autoMappingService.updateAutoMappingEntryStatusWithValue(value);
		});
	}

	@Override
	public SmbUnmappedDataResponse getUnmappedPagesForSmb(String channel) {

		logger.info("request received for fetching unmapped SMB pages for channel: {}",channel);
		SmbUnmappedDataResponse smbUnmappedDataResponse = new SmbUnmappedDataResponse();
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			smbUnmappedDataResponse = fbSocialAccountService.getUnmappedfbPagesForSmb();
		}
		else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			smbUnmappedDataResponse = googleSocialAccountService.getUnmappedGMBPagesForSmb();
		}
		return smbUnmappedDataResponse;
	}

	@Override
	public BrandDataResponse getBrandData(Long enterpriseId) {
		final BrandDataResponse response = new BrandDataResponse();
		BusinessLiteDTO business = businessCoreService.getBusinessLiteByNumber(enterpriseId);
		final boolean isSmb = checkBusinessSMB(business);
		logger.info("Business info for enterprise id : {} and is smb : {}",enterpriseId,isSmb);
		if(isSmb) {
			final GoogleMessagesAgent agent  = googleMsgAgentService.findByEnterpriseId(enterpriseId);
			response.setShellCreated(agent != null);
			// brandName here should be sent as agentName
			response.setBrandName(agent != null && StringUtils.isNotEmpty(agent.getAgentDisplayName()) ? agent.getAgentDisplayName()
					: businessCoreService.getBusinessLiteByNumber(enterpriseId).getBusinessName());
			response.setWelcomeMessage(agent != null && StringUtils.isNotEmpty(agent.getWelcomeMsg()) ? agent.getWelcomeMsg()
					: CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultWelcomeMessage());
			response.setOfflineMessage(agent != null && StringUtils.isNotEmpty(agent.getOfflineMsg()) ? agent.getOfflineMsg()
					: CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultOfflineMessageMessage());
			String defaultLogoUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultLogoUrl();
			response.setLogoUrl(agent != null && StringUtils.isNotEmpty(agent.getLogoUrl()) ? agent.getLogoUrl()
					: defaultLogoUrl);
			response.setAgentId(agent != null ? agent.getId() : null);
		}else {
			response.setShellCreated(false);
			// brandName here should be sent as agentName
			response.setBrandName(businessCoreService.getBusinessLiteByNumber(enterpriseId).getBusinessName());
			response.setWelcomeMessage(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultWelcomeMessage());
			response.setOfflineMessage(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultOfflineMessageMessage());
			String defaultLogoUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultLogoUrl();
			response.setLogoUrl(defaultLogoUrl);
			response.setAgentId(null);
		}
		return response;
	}

	@Override
//	@Cacheable(value = "gMsgStatus", key = "#enterpriseId.toString()", unless = "#result == null")
	public InboxStatusResponse checkgMsgStatus(Long enterpriseId) {
		InboxStatusResponse response = new InboxStatusResponse();
		try {
			response.setMessagingEnabled(true);
			response.setMessagingInvalidType("NONE");
			logger.info("request received to check status for gbm : business {}", enterpriseId);
			BusinessLiteDTO business = businessCoreService.getBusinessLiteByNumber(enterpriseId);
			final boolean isBusinessNotMapped = googleSocialAccountService.isBusinessNotMappedToGMBPage(business);
			final boolean isSmbAndMapped = checkBusinessSMB(business) && !isBusinessNotMapped;
			Boolean isMessengerEnabled = googleSocialAccountService.isGoogleMessageEnabled(business.getBusinessId(), null);
			if (!isMessengerEnabled) {
				response.setMessagingEnabled(false);
				response.setMessagingInvalidType(Constants.MESSAGING_NOT_ALLOWED);
			} else {
				GoogleMessagesAgent agent = googleMsgAgentService.findByEnterpriseId(enterpriseId);
				logger.info("Agent: {}", agent);
				if (agent != null && agent.getStatus() != null && agent.getStatus().equalsIgnoreCase(GoogleAgentStatus.LAUNCHED.toString())) {
					response.setMessagingEnabled(true);
				} else if (agent != null && agent.getStatus() != null && agent.getStatus().equalsIgnoreCase(GoogleAgentStatus.VERIFICATION_ERROR.name())) {
					response.setMessagingEnabled(false);
					// In case there is no page present in this enterpriseId, return status as SETUP_MESSAGING
					if (!socialGMBRepo.existsByEnterpriseId(enterpriseId)) {
						response.setMessagingInvalidType(Constants.SETUP_MESSAGING);
					} else {
						if (googleBizCommService.isInvalidNameError(agent.getComments())) {
							response.setMessagingInvalidType(Constants.MESSENGER_NAME_VERIFICATION_ERROR);
						} else {
							response.setMessagingInvalidType(Constants.MESSENGER_VERIFICATION_ERROR);
						}
					}
				} else if (isSmbAndMapped) {
					List<BusinessGoogleMyBusinessLocation> gmbPages = gmbRawPageService.findByEnterpriseIdAndIsSelected(enterpriseId, 1);
					if (CollectionUtils.isNotEmpty(gmbPages)) {
						BusinessGoogleMyBusinessLocation gmbPage = gmbPages.get(0);
						if (gmbPage.getPermissions() == null || !gmbPage.getPermissions().contains(Constants.BUSINESS_MESSAGING)
								|| !gmbPage.getPermissions().contains(Constants.BUSINESS_COMMUNICATION)) {
							response.setMessagingInvalidType(Constants.MESSENGER_NOT_ENABLED);
							response.setMessagingEnabled(false);
						} else {
							response.setMessagingEnabled(false);
							response.setMessagingInvalidType(Constants.SETUP_MESSAGING);
						}
					} else {
						response.setMessagingEnabled(false);
						response.setMessagingInvalidType(Constants.SETUP_MESSAGING);
					}
				} else {
					response.setMessagingEnabled(false);
					response.setMessagingInvalidType(Constants.SETUP_MESSAGING);
				}
			}
		}catch (Exception e){
			logger.info("Exception occurred in checkgMsgStatus :{} for enterpriseId: {}", e,enterpriseId);
			throw new BirdeyeSocialException(ErrorCodes.MESSAGE_STATUS_ERROR,"Message Status error");

		}
		return response;

	}

	@Override
	public void updateAgent(UpdateAgentRequest req) {
		GoogleMessagesAgent gmAgent=null;
		AgentAuditStatus status=null;
		String comment=null;
		try {
			logger.info("updateLaunchedAgent: req {}", req);
			BusinessLiteDTO business = businessCoreService.getBusinessLiteByNumber(req.getEnterpriseId());
			if(checkBusinessSMB(business)){
				gmAgent = googleMsgAgentService.findByEnterpriseId(req.getEnterpriseId());
			}else {
				gmAgent = googleMsgAgentService.getAgentById(req.getAgentId());
			}
			if (gmAgent == null || GoogleAgentStatus.valueOf(gmAgent.getStatus()).compareTo(GoogleAgentStatus.AGENT_CREATED) < 0) {
				throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_NOT_CREATED, "Agent is not created for given enterpriseId");
			}
			if (Objects.nonNull(req.getWelcomeMessage()) && Objects.nonNull(req.getOfflineMessage())) {
				googleBizCommService.updateLaunchedAgent(gmAgent.getAgentName(), req.getWelcomeMessage(),
						req.getOfflineMessage());
			}
			googleMsgAgentService.updateAgentInfo(gmAgent, req, "Agent updated");
			googleMsgAgentService.evictAgentCache(req.getEnterpriseId());
			status=AgentAuditStatus.AGENT_UPDATED;
		} catch (GoogleJsonResponseException googleExp) {
			logger.error("Google exception in updateAgent", googleExp);
			status=AgentAuditStatus.AGENT_UPDATION_ERROR;
			comment=googleExp.toString();
			throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_UPDATE_ERROR,
					"Exception in Google Business Comm API for updateAgent");
		} catch (BirdeyeSocialException beExp) {
			status=AgentAuditStatus.AGENT_UPDATION_ERROR;
			comment=beExp.toString();
			throw beExp;
		} catch (Exception exp) {
			logger.error("Exception in updateAgent", exp);
			status=AgentAuditStatus.AGENT_UPDATION_ERROR;
			comment=exp.toString();
			throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_UPDATE_ERROR, exp.getMessage());
		}
		finally {
			assert status != null;
			if(Objects.isNull(gmAgent)){
				createAuditRequest(null, null, null, null,
						comment, null, null, req.getAgentId(), status.name());
			}else {
				createAuditRequest(null, null, null, null,
						comment, gmAgent.getBrandName(), gmAgent.getAgentName(), req.getAgentId(), status.name());
			}
		}
	}

	private void pushgMsgLocationLaunchInFirebase(Long enterpriseId, Integer agentId,String status) {
		BusinessLiteDTO business = businessCoreService.getBusinessLiteByNumber(enterpriseId);
		String topicSocial;
		if(checkBusinessSMB(business)) {
			topicSocial = LOCATIONLAUNCH + "/" + enterpriseId;
		}else {
			topicSocial = LOCATIONLAUNCH + "/" + enterpriseId + "/" + agentId;
		}
		nexusService.insertMapInFirebase(topicSocial,"locationLaunchStatus", status);
	}

	@Override
	public void setupAgent(SetupAgentRequest req) throws Exception {
		logger.info("setupAgent: req {}", req);
		GoogleMessagesAgent gmAgent = null;
		GoogleAgentStatus oldAgentStatus = null, updatedAgentStatus = null;
		Agent agent = null;
		String comments = null;
		List<GoogleMessagesAgent> googleMessagesAgentList = googleMsgAgentService.findByEnterpriseIdIn(req.getEnterpriseId());
		try {
			// Check if brand exists
			BusinessLiteDTO business = businessCoreService.getBusinessLiteByNumber(req.getEnterpriseId());
			boolean isSmb = checkBusinessSMB(business);
			if(isSmb) {
				if(CollectionUtils.isEmpty(googleMessagesAgentList)){
					comments = "Brand should be created to create agent";
					throw new BirdeyeSocialException(ErrorCodes.AGENT_IS_NULL,
							iPermissionMappingService.getDataByChannelAndModuleAndPermissionCode(SocialChannel.GMB.getName(),SocialMessageModule.GMB_MESSAGING,ErrorCodes.AGENT_IS_NULL.value()));
				}
				gmAgent = googleMessagesAgentList.get(0);
				socialGMBRepo.updateAgentIdByBusinessIds(gmAgent.getId(),Collections.singletonList(business.getBusinessId()));
			}else{
				for(GoogleMessagesAgent agent1 : googleMessagesAgentList){
					if(StringUtils.isNotEmpty(agent1.getStatus())
							&& GoogleAgentStatus.valueOf(agent1.getStatus()).compareTo(GoogleAgentStatus.BRAND_CREATED) > 0
							&& ((StringUtils.isNotEmpty(agent1.getAgentDisplayName())
							&& StringUtils.equalsIqnoreCase(agent1.getAgentDisplayName(),req.getDisplayName()))
							|| (StringUtils.isNotEmpty(agent1.getBrandDisplayName())
							&& StringUtils.equalsIqnoreCase(agent1.getBrandName(),req.getDisplayName())))
							&& !Objects.equals(agent1.getId(),req.getAgentId())){
						comments = "Brand already exists with same brand name";
						throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_BRAND_ALREADY_CREATED, comments);
					}
					if(Objects.nonNull(req.getAgentId()) && Objects.equals(req.getAgentId(),agent1.getId())){
						gmAgent = agent1;
					}
				}
			}
			if(Objects.isNull(gmAgent)){
				comments = "Brand should be created to create agent";
				throw new BirdeyeSocialException(ErrorCodes.AGENT_IS_NULL,
						iPermissionMappingService.getDataByChannelAndModuleAndPermissionCode(SocialChannel.GMB.getName(),SocialMessageModule.GMB_MESSAGING,ErrorCodes.AGENT_IS_NULL.value()));
			}
			// add agent id to gmb locations(pages)
			// drafting the agent
			if(Objects.nonNull(req.getStatus()) && req.getStatus().equals(GoogleAgentStatus.AGENT_DRAFT)) {
				createDraft(gmAgent, req);
				return;
			}
			// Get the existing status of agent
			updatedAgentStatus = oldAgentStatus = GoogleAgentStatus.valueOf(gmAgent.getStatus());
			logger.info("setupAgent: Agent found with status {}", oldAgentStatus);

			// Check for brand creation error
			if (oldAgentStatus == GoogleAgentStatus.BRAND_CREATION_ERROR)
				throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_BRAND_NOT_CREATED,
						iPermissionMappingService.getDataByChannelAndModuleAndPermissionCode(SocialChannel.GMB.getName(),SocialMessageModule.GMB_MESSAGING,ErrorCodes.GOOGLE_MESSAGES_BRAND_NOT_CREATED.value()));

			// Start agent setup process according to status
			switch (oldAgentStatus) {
				case BRAND_CREATED:
				case AGENT_CREATION_ERROR:
					logger.info("setupAgent: Creating agent...");
					agent = googleBizCommService.createAgent(gmAgent.getBrandName(), req.getDisplayName(),
							req.getWelcomeMessage(), req.getOfflineMessage(), req.getLogoUrl());
					updatedAgentStatus = GoogleAgentStatus.AGENT_CREATED;

				case AGENT_CREATED:
				case VERIFICATION_ERROR:
					logger.info("setupAgent: Verifying agent...");
					List<BusinessGoogleMyBusinessLocation> gmbRawPages = isSmb ? Collections.singletonList(gmbRawPageService.findByBusinessId(business.getBusinessId()))
							:gmbRawPageService.findLastUpdatedWithDistinctRefreshTokenIdWithAgentId(req.getAgentId(), 3);
//					List<BusinessGoogleMyBusinessLocation> gmbRawPages = gmbRawPageService.findLastUpdatedWithDistinctRefreshTokenId(
//							req.getEnterpriseId(), 3);
					if (CollectionUtils.isEmpty(gmbRawPages))
						throw new BirdeyeSocialException(ErrorCodes.GMB_PAGE_NOT_FOUND,
								iPermissionMappingService.getDataByChannelAndModuleAndPermissionCode(SocialChannel.GMB.getName(),SocialMessageModule.GMB_MESSAGING,ErrorCodes.GMB_PAGE_NOT_FOUND.value()));
					final BusinessCoreUser bizCoreUser = businessCoreService.getUserInfo(req.getUserId());
					if (StringUtils.isEmpty(req.getWebsiteUrl())) {
						logger.warn("setupAgent: websiteUrl is null. Using https://birdeye.com");
						req.setWebsiteUrl("https://birdeye.com");
					}

					if (oldAgentStatus == GoogleAgentStatus.VERIFICATION_ERROR) {
						logger.info("setupAgent: Updating fields of previously created agent...");
						if(Objects.isNull(req.getWelcomeMessage())) {
							comments = "Welcome message is not present";
							throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_NOT_VERIFIED,
									iPermissionMappingService.getDataByChannelAndModuleAndPermissionCode(SocialChannel.GMB.getName(),SocialMessageModule.GMB_MESSAGING,ErrorCodes.GOOGLE_MESSAGES_AGENT_UPDATE_ERROR.value()));

						}
						googleBizCommService.updateAgent(gmAgent.getAgentName(), req.getDisplayName(),
								req.getWelcomeMessage(), req.getOfflineMessage(), req.getLogoUrl());
					}

					// Try to verify the agent with the access token of GMB raw pages already present in DB
					final int MAX_ATTEMPTS = Math.min(3, gmbRawPages.size());
					for (int attempt = 0; attempt < MAX_ATTEMPTS; ++attempt) {
						try {
							logger.info("setupAgent: Agent verification attempt {} with locationId {}", attempt, gmbRawPages.get(attempt).getLocationId());
							googleBizCommService.requestAgentVerification(agent != null ? agent.getName() : gmAgent.getAgentName(),
									bizCoreUser, req.getWebsiteUrl(), googleAccessTokenCache.getGoogleAccessToken(gmbRawPages.get(attempt)));
							logger.info("setupAgent: Agent verification successful with locationId {}", gmbRawPages.get(attempt).getLocationId());
							break;
						} catch (GoogleJsonResponseException googleExp) {
							final GoogleJsonError googleJsonError = googleExp.getDetails();
							logger.warn("setupAgent: Agent verification attempt {} failed with exception {}", attempt, googleJsonError.getMessage());
							// Early exit if error is due to invalid agent name
							if (googleBizCommService.isInvalidNameError(googleJsonError.getMessage()) || attempt == MAX_ATTEMPTS - 1) {
								logger.info("exception from google : {}",googleJsonError);
								throw googleExp;
							}
						}
					}
					updatedAgentStatus = GoogleAgentStatus.VERIFIED;
					logger.info("old status : {} and new status : {} agent id : {}",oldAgentStatus,updatedAgentStatus,gmAgent.getId());

				case VERIFIED:
				case UN_LAUNCHED:
				case LAUNCH_ERROR:
					logger.info("setupAgent: Launching agent...");
					if(Objects.nonNull(gmAgent.getWelcomeMsg()) &&
							(!Objects.equals(req.getWelcomeMessage(),gmAgent.getWelcomeMsg()) || !Objects.equals(req.getOfflineMessage(),gmAgent.getOfflineMsg()))) {
						googleBizCommService.updateLaunchedAgent(agent != null ? agent.getName() : gmAgent.getAgentName(),req.getWelcomeMessage(),req.getOfflineMessage());
					}
					googleBizCommService.requestAgentLaunch(agent != null ? agent.getName() : gmAgent.getAgentName());
					updatedAgentStatus = GoogleAgentStatus.LAUNCHED;
					logger.info("setupAgent: Agent is successfully launched");
			}
		} catch (GoogleJsonResponseException googleExp) {
			logger.error("Google Exception in setupAgent", googleExp);
			comments = googleExp.getDetails().getMessage();
			if (StringUtils.isNotEmpty(comments) && googleBizCommService.isInvalidNameError(comments))
				throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_NAME_INVALID,
						iPermissionMappingService.getDataByChannelAndModuleAndPermissionCode(SocialChannel.GMB.getName(),SocialMessageModule.GMB_MESSAGING,ErrorCodes.GOOGLE_MESSAGES_AGENT_NAME_INVALID.value()));
			throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_SETUP_ERROR,
					iPermissionMappingService.getDataByChannelAndModuleAndPermissionCode(SocialChannel.GMB.getName(),SocialMessageModule.GMB_MESSAGING,ErrorCodes.GOOGLE_MESSAGES_AGENT_SETUP_ERROR.value()));
		} catch (BirdeyeSocialException beExp) {
			logger.info("exception from google : {}",beExp);
			comments = beExp.getMessage();
			throw beExp;
		} catch (Exception exp) {
			logger.error("Exception in setupAgent", exp);
			comments = StringUtils.isNotEmpty(exp.getMessage())
					? exp.getMessage().substring(0, Math.min(exp.getMessage().length(), 300))
					: "Exception in setupAgent. Last known status is " + updatedAgentStatus;
			throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_SETUP_ERROR,
					iPermissionMappingService.getDataByChannelAndModuleAndPermissionCode(SocialChannel.GMB.getName(),SocialMessageModule.GMB_MESSAGING,ErrorCodes.GOOGLE_MESSAGES_AGENT_SETUP_ERROR.value()));
		} finally {
			if (updatedAgentStatus != null) {
				switch (updatedAgentStatus) {
					case BRAND_CREATED: updatedAgentStatus = GoogleAgentStatus.AGENT_CREATION_ERROR; break;
					case AGENT_CREATED: updatedAgentStatus = GoogleAgentStatus.VERIFICATION_ERROR; break;
					case VERIFIED: updatedAgentStatus = GoogleAgentStatus.LAUNCH_ERROR; break;
				}
			}
			if (updatedAgentStatus != null && oldAgentStatus != null && updatedAgentStatus.compareTo(GoogleAgentStatus.LAUNCHED) <= 0
					&& oldAgentStatus.compareTo(GoogleAgentStatus.LAUNCHED) < 0) {
				// Update agent info if status was greater than BRAND_CREATED
				if (updatedAgentStatus.compareTo(GoogleAgentStatus.BRAND_CREATED) >= 0) {
					googleMsgAgentService.saveAgentInfo(gmAgent, updatedAgentStatus, req, agent, comments);
				}

				// Start location setup process if agent is now LAUNCHED (and was not already launched)
				if (updatedAgentStatus == GoogleAgentStatus.LAUNCHED) {
					logger.info("setupAgent: Setting up locations...");
					List<BusinessGoogleMyBusinessLocation> gmbPages =
							gmbRawPageService.findByEnterpriseIdAndIsValidAndIsSelectedAndAgentIdAndBusinessIdIsNotNull(req.getEnterpriseId(),gmAgent.getId());
					List<BusinessGoogleMyBusinessLocation> mappedGmbPages = new ArrayList<>();
					gmbPages.forEach(gmbPage->{
						if(gmbPage.getPermissions()!=null
								&& Objects.nonNull(gmbPage.getBusinessId())
								&& StringUtils.isNotEmpty(gmbPage.getPlaceId())
								&& gmbPage.getPermissions().contains(Constants.BUSINESS_MESSAGING)
								&& gmbPage.getPermissions().contains(Constants.BUSINESS_COMMUNICATION)
								&& (gmbPage.getgMsgLocationStatus() == null || (gmbPage.getgMsgLocationStatus() != null
								&& !gmbPage.getgMsgLocationStatus().equalsIgnoreCase(GoogleLocationStatus.LAUNCHED.name())))) {
							mappedGmbPages.add(gmbPage);
						}
					});
					if (CollectionUtils.isNotEmpty(mappedGmbPages)) {
						googleSocialAccountService.initSetupLocation(req.getEnterpriseId(),gmAgent,mappedGmbPages,false);
					} else {
						logger.warn("setupAgent: Mapped & Selected GMB Pages not found for enterpriseId {}", req.getEnterpriseId());
					}
				} else {
					logger.warn("setupAgent: Location setup not initiated due to status not being equal to {}", GoogleAgentStatus.LAUNCHED);
				}
			}
			if(Objects.nonNull(gmAgent)) {
				createAuditRequest(null, null, null, null,
						null, gmAgent.getBrandName(), gmAgent.getAgentName(), req.getAgentId(), gmAgent.getStatus());
			}
			else{
				createAuditRequest(null, null, null, null,
						"Error occurred while setup agent", req.getDisplayName(), null, req.getAgentId(), GoogleAgentStatus.AGENT_CREATION_ERROR.name());
			}
		}
	}

	private void createDraft(GoogleMessagesAgent gmAgent, SetupAgentRequest req) {
		logger.info("GMB agent draft for request {} for agent {}",req, gmAgent);
		GoogleAgentStatus status = GoogleAgentStatus.AGENT_DRAFT;
		if(StringUtils.isNotEmpty(gmAgent.getStatus()) && GoogleAgentStatus.valueOf(gmAgent.getStatus()).compareTo(status) > 0){
			status = GoogleAgentStatus.valueOf(gmAgent.getStatus());
		}
		if(status.compareTo(GoogleAgentStatus.VERIFIED) < 0) {
			googleMsgAgentService.saveAgentInfo(gmAgent, status, req, null, "Agent is drafted");
		}
	}

	private void editDraft(GoogleMessagesAgent gmAgent,EditWidgetRequest req){
		googleMsgAgentService.saveWidgetInfo(gmAgent,req);
	}

	// TODO: 19/01/23 ensure brand should be created directly in smb case but not in enterprise @Navroj -- done
	public void saveGoogleMessagingAudit(CreateGoogleMessagingAuditRequest req){
		googleMsgAgentService.saveAuditInfo(req);
	}


	// TODO: 19/01/23 ensure brand should be created directly in smb case but not in enterprise @Navroj
	@Override
	public void createBrand(CreateBrandRequest req) {
		GoogleMessagesAgent gmAgent = null;
		Brand brand = null;
		GoogleAgentStatus oldAgentStatus = null, newStatus = null;
		String comments = null;
		try {
			logger.info("createBrand: req {}", req);
			List<GoogleMessagesAgent>allAgents = googleMsgAgentService.findAllByEnterpriseId(req.getEnterpriseId());
			// TODO: 19/01/23 @navroj check non-null agent id -- done
			BusinessLiteDTO business = businessCoreService.getBusinessLiteByNumber(req.getEnterpriseId());
			if(checkBusinessSMB(business)){
				if(CollectionUtils.isNotEmpty(allAgents)) {
					gmAgent = allAgents.get(0);
					oldAgentStatus = newStatus = GoogleAgentStatus.valueOf(gmAgent.getStatus());
				}else{
					gmAgent = new GoogleMessagesAgent();
					// set status less than brand created for new SMB
					oldAgentStatus = newStatus = GoogleAgentStatus.WIDGET_CREATED;
					if(StringUtils.isEmpty(req.getDisplayName())){
						req.setDisplayName(StringUtils.isNotEmpty(business.getBusinessName()) ? business.getBusinessName() : business.getBusinessAlias());
					}
					gmAgent.setWidgetName(req.getDisplayName());
				}
				if(oldAgentStatus.compareTo(GoogleAgentStatus.BRAND_CREATED) < 0) {
					brand = googleBizCommService.createBrand(req.getDisplayName());
					newStatus = GoogleAgentStatus.BRAND_CREATED;
				}
			}else {
				if(Objects.isNull(req.getAgentId())){
					comments = "Agent id can not be null in the request";
					throw new BirdeyeSocialException(ErrorCodes.AGENT_IS_NULL,comments);
				}
				gmAgent = googleMsgAgentService.findByAgentId(req.getAgentId());
				if(Objects.isNull(gmAgent) || StringUtils.isEmpty(gmAgent.getStatus())){
					return;
				}
				oldAgentStatus = newStatus = GoogleAgentStatus.valueOf(gmAgent.getStatus());
				if(CollectionUtils.isNotEmpty(allAgents)) {
					logger.info("Widget with agents : {}",allAgents);
					Map<String, Integer> agentDetails = allAgents.stream()
							.filter(agent -> (StringUtils.isNotEmpty(agent.getBrandDisplayName())
									&& GoogleAgentStatus.valueOf(agent.getStatus()).compareTo(GoogleAgentStatus.AGENT_CREATED) > 0) )
							.collect(Collectors.toMap(GoogleMessagesAgent::getBrandDisplayName, GoogleMessagesAgent::getId));
					if (agentDetails.containsKey(req.getDisplayName()) && !Objects.equals(agentDetails.get(req.getDisplayName()), req.getAgentId())) {
						comments = "Brand already exists with same brand name";
						throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_BRAND_ALREADY_CREATED, comments);
					}
				}
				switch (oldAgentStatus) {
					case AGENT_DRAFT:
					case WIDGET_CREATED:
						brand = googleBizCommService.createBrand(req.getDisplayName());
						newStatus = GoogleAgentStatus.BRAND_CREATED;
				}
			}
		} catch (GoogleJsonResponseException googleExp) {
			logger.error("Google Exception in createBrand", googleExp);
			comments = googleExp.getDetails().getMessage();
			throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_BRAND_CREATE_ERROR,"Exception in createBrand while calling Google Business Comm API");
		} catch (Exception exp) {
			logger.error("Exception in createBrand", exp);
			comments = StringUtils.isNotEmpty(exp.getMessage())
					? exp.getMessage().substring(0, Math.min(300, exp.getMessage().length()))
					: "Exception in createBrand";
			throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_BRAND_CREATE_ERROR, comments);
		} finally {
			// TODO: 19/01/23 status for creation_error @Navroj --done
			if (newStatus == null) {
				newStatus = GoogleAgentStatus.BRAND_CREATION_ERROR;
			}
			if (Objects.isNull(oldAgentStatus) || newStatus.compareTo(oldAgentStatus) > 0) {
				googleMsgAgentService.addBrandInfo(gmAgent, newStatus, req, brand, comments);
			}
			if(Objects.nonNull(gmAgent)) {
				//if (newStatus.compareTo(GoogleAgentStatus.BRAND_CREATED) <= 0) {
				createAuditRequest(null, null, null, null,
						null, gmAgent.getBrandName(), null, req.getAgentId(), newStatus.name());
			}else {
				createAuditRequest(null, null, null, null,
						"Not able to save brand", req.getDisplayName(), null, req.getAgentId(), newStatus.name());
			}
		}
	}


	public void createAuditRequest(String placeId,String locationId,String gmsgLocationName,String gmsgLocationStatus,String gsmbLocationComment,
								   String brandName,String agentName, Integer agentId,String agentStatus){
		CreateGoogleMessagingAuditRequest audit=new CreateGoogleMessagingAuditRequest();
		audit.setGsmbLocationStatus(gmsgLocationStatus);
		audit.setAgentId(agentId);
		audit.setGsmbComment(gsmbLocationComment);
		audit.setGsmbLocationName(gmsgLocationName);
		audit.setAgentName(agentName);
		audit.setBrandName(brandName);
		audit.setAgentStatus(agentStatus);
		audit.setPlaceId(placeId);
		audit.setLocationId(locationId);
		googleMsgAgentService.addAuditInfo(audit);
	}

	private void handleGoogleError(Exception exp, GoogleMessagesAgent gmAgent) {
		if (exp instanceof GoogleJsonResponseException) {
			final GoogleJsonResponseException googleExp = (GoogleJsonResponseException) exp;
			final GoogleJsonError googleJsonError = googleExp.getDetails();
			logger.error("handleGoogleError: {}", googleJsonError);
			String errorMsg = googleJsonError.getMessage();
			int maxLength = Math.min(errorMsg.length(), 300);
			errorMsg = errorMsg.substring(0, maxLength);
			googleMsgAgentService.updateComments(gmAgent, errorMsg);
			if (googleJsonError.getCode() == 400
					&& googleBizCommService.isInvalidNameError(googleJsonError.getMessage())) {
				throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_NAME_INVALID, ErrorCodes.GOOGLE_MESSAGES_AGENT_NAME_INVALID.name());
			} else {
				throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_SETUP_ERROR, "Error in brand/agent setup");
			}
		}
	}

	@Override
	public GoogleMessagesAgentLiteDto getAgent(AgentRequest agentRequest) {
		return googleMsgAgentService.findDTOByAgentName(agentRequest.getAgentName());
	}

	@Override
	public void unLaunchAgent(AgentUnLaunchRequest request) throws Exception {
		GoogleMessagesAgent gmAgent = null;
		try {
			logger.info("un-launch agent: enterpriseId {} and agent id : {}", request.getEnterpriseId(),request.getAgentId());
			gmAgent = googleMsgAgentService.getAgentById(request.getAgentId());
			if (Objects.isNull(gmAgent) || GoogleAgentStatus.valueOf(gmAgent.getStatus()).compareTo(GoogleAgentStatus.AGENT_CREATED) < 0) {
				throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_NOT_CREATED, "Agent is not created for given enterpriseId");
			}
			googleBizCommService.unLaunchAgent(gmAgent);
			googleMsgAgentService.updateStatus(request.getAgentId(), GoogleAgentStatus.UN_LAUNCHED);
			//update state and agent_id to null
			googleSocialAccountService.updateLocationStateOfLocations(gmAgent.getId(),GoogleLocationStatus.UN_LAUNCHED.name());
			pushgMsgLocationLaunchInFirebase(request.getEnterpriseId(),request.getAgentId(),GoogleLocationResponseStatus.UNLAUNCHED.name());
		} catch(Exception exp) {
			pushgMsgLocationLaunchInFirebase(request.getEnterpriseId(),request.getAgentId(),GoogleLocationResponseStatus.ERROR.name());
			if (gmAgent != null) {
				handleGoogleError(exp, gmAgent);
			}
			throw exp;
		}finally{
			if(Objects.isNull(gmAgent)){
				createAuditRequest(null, null, null, null,
						"Agent is not created for given enterpriseId", null,null, request.getAgentId(),null);
			}else {
				createAuditRequest(null, null, null, null,
						"Agent Un-launched", gmAgent.getBrandName(), gmAgent.getAgentName(), request.getAgentId(), gmAgent.getStatus());
			}
		}
	}

	@Override
	public void editWidget(EditWidgetRequest req){
		logger.info("Request for edit widget : req {}", req.getEnterpriseId());
		List<GoogleMessagesAgent> googleMessagesAgents = googleMsgAgentService.findByEnterpriseIdAndWidgetName(req.getEnterpriseId(),req.getWidgetName());
		if(CollectionUtils.isNotEmpty(googleMessagesAgents) && !googleMessagesAgents.get(0).getId().equals(req.getAgentId())){
			throw new BirdeyeSocialException(ErrorCodes.WIDGET_ALREADY_EXISTS,"Widget with same widget name already exists");
		}
		GoogleMessagesAgent gmAgent = googleMsgAgentService.getAgentById(req.getAgentId());
		if (Objects.isNull(gmAgent)) {
			throw new BirdeyeSocialException(ErrorCodes.AGENT_IS_NULL,
					iPermissionMappingService.getDataByChannelAndModuleAndPermissionName(SocialChannel.GMB.getName(),SocialMessageModule.GMB_MESSAGING,ErrorCodes.AGENT_IS_NULL.name()));
		}
		editDraft(gmAgent,req);
		if (CollectionUtils.isEmpty(req.getBusinessIds())) {
			logger.info("Request does not contain business ids");
			return;
		}
		if (GoogleAgentStatus.valueOf(gmAgent.getStatus()).compareTo(GoogleAgentStatus.VERIFICATION_ERROR) < 0) {
			gmbRawPageService.updateAgentIds(req.getAgentId());
		}
		List<Integer> businessIds =  getBusinessIdsFromNumber(req.getBusinessIds());
		if(CollectionUtils.isEmpty(businessIds)){
			return;
		}
		gmbRawPageService.updateAgentIdsByBusinessIds(req.getAgentId(),businessIds);
		if(GoogleAgentStatus.LAUNCHED.name().equalsIgnoreCase(gmAgent.getStatus())) {
			List<BusinessGoogleMyBusinessLocation> gmbPages = gmbRawPageService.findByBusinessIds(businessIds,1,1);
			if (CollectionUtils.isEmpty(gmbPages)) {
				logger.info("No business id found for the given business numbers : {}",req.getBusinessIds());
				return;
			}
//			pushgMsgLocationLaunchInFirebase(req.getEnterpriseId(),gmAgent.getId(),GoogleLocationResponseStatus.INIT.name());
			//(Long enterpriseId, String placeId, String agentName, String brandName, Boolean postAutoMapping)
			googleSocialAccountService.initSetupLocation(req.getEnterpriseId(),gmAgent,gmbPages,false);
		}
	}

	private List<Integer> getBusinessIdsFromNumber(List<Long> businessIds) {
		Map<String, BusinessLocationLiteDTOForGMB> map =  businessCoreService.getBusinessesInBulkByBusinessNumber(businessIds, false);
		if(CollectionUtils.isEmpty(map.values())){
			logger.info("No business id found for the given business numbers : {}",businessIds);
			return new ArrayList<>();
		}
		return map.values().stream().map(BusinessLocationLiteDTOForGMB::getBusinessId).collect(Collectors.toList());
	}


	@Override
	public void updateLaunchStatus(UpdateLaunchStatusAgentRequest req) {
		GoogleMessagesAgent gmAgent;
		try{
			logger.info("Launch agent: enterpriseId {}",req.getEnterpriseId());
			// TODO: 11/01/23 @Navroj -- done
			gmAgent = googleMsgAgentService.findByAgentId(req.getAgentId());
			if (Objects.isNull(gmAgent)) {
				throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_NOT_CREATED, "Agent is not created");
			}
			if(req.getAgentDisplayName() != null){
				gmAgent.setAgentDisplayName(req.getAgentDisplayName());
			}
			if(req.getStatus().isEmpty()){
				gmAgent.setStatus(null);
			}else{
				gmAgent.setStatus(req.getStatus());
			}
			googleMsgAgentService.updateStatusAndName(gmAgent);
		}catch(Exception exp){
			logger.error("Error while updating google messaging agent {}",exp.getMessage());
		}
	}

	@Override
	public void updateStatus(List<String> requestIds) {
		if(CollectionUtils.isNotEmpty(requestIds)) {
			if(org.apache.commons.lang3.StringUtils.isNumeric(requestIds.get(0))) {
				logger.info("Inside update status for business_get_pages_request");
				List<Integer> intRequestIds = requestIds.stream().map(NumberUtils::toInt).collect(Collectors.toList());
				List<BusinessGetPageRequest> businessGetPageRequests = businessGetPageService.findAllById(intRequestIds);
				List<String> status = Arrays.asList(Status.ACCOUNT_FETCHED.getName(),Status.INITIAL.getName(),Status.ACCOUNT_INITIAL.getName(),Status.FETCHED.getName());
				List<Integer>requests = businessGetPageRequests.stream().filter(request ->
						status.contains(request.getStatus())).map(BusinessGetPageRequest::getId).collect(Collectors.toList());
				if(CollectionUtils.isNotEmpty(requests)) {
					businessGetPageService.updateAll(requests, Status.CANCEL.getName(), "Force Cancel by API");
				}
			} else {
				logger.info("Inside update status for business_get_pages_openurl_request");
				List<BusinessGetPageOpenUrlRequest> businessGetPageOpenUrlRequests = businessGetPageOpenUrlReqRepo.findAll(requestIds);
				List<String> status = Arrays.asList(Status.ACCOUNT_FETCHED.getName(),Status.INITIAL.getName(),Status.ACCOUNT_INITIAL.getName(),Status.FETCHED.getName());
				List<String>requests = businessGetPageOpenUrlRequests.stream().filter(request ->
						status.contains(request.getStatus())).map(BusinessGetPageOpenUrlRequest::getId).collect(Collectors.toList());
				if(CollectionUtils.isNotEmpty(requests)) {
					businessGetPageOpenUrlReqRepo.updateStatusByIdIn(requests, Status.CANCEL.getName());
				}
			}
		}
		logger.info("Moved requests to cancel state");
	}

	@Override
	public void setUpAgentsForReseller(LaunchAgentDTO launchAgentDTO){
		Set<Integer> agentIds = launchAgentDTO.getIsEnterprise()
				? gmbRawPageService.getDistinctAgentIdByEnterpriseId(launchAgentDTO.getParentId())
				: gmbRawPageService.getDistinctAgentIdByResellerId(launchAgentDTO.getParentId());
		if(CollectionUtils.isEmpty(agentIds)){
			logger.info("No agent found for payload : {}",launchAgentDTO);
		}
		agentIds.stream().filter(Objects::nonNull).forEach(id -> {
			GoogleMessagesAgent agent = googleMsgAgentService.findByAgentId(id);
			if( Objects.isNull(agent) || StringUtils.isEmpty(agent.getStatus()) || GoogleAgentStatus.UN_LAUNCHED.name().equalsIgnoreCase(agent.getStatus())){
				return;
			}
			if(GoogleAgentStatus.valueOf(agent.getStatus()).compareTo(GoogleAgentStatus.AGENT_CREATION_ERROR) > 0
					&& GoogleAgentStatus.valueOf(agent.getStatus()).compareTo(GoogleAgentStatus.LAUNCHED) < 0) {
				SetupAgentRequest request = new SetupAgentRequest();
				request.setDisplayName(agent.getAgentDisplayName());
				request.setEnterpriseId(agent.getEnterpriseId());
				request.setLogoUrl(agent.getLogoUrl());
				request.setAgentId(id);
				request.setOfflineMessage(agent.getOfflineMsg());
				request.setUserId(launchAgentDTO.getUserId());
				request.setWebsiteUrl(agent.getPrivacyUrl());
				try {
					setupAgent(request);
				} catch (Exception e) {
					logger.error("Unable to launch agent for agent id :{} and name : {} with error : {}", id, agent.getAgentDisplayName(),e.getMessage());
				}
			}
			if(GoogleAgentStatus.valueOf(agent.getStatus()).compareTo(GoogleAgentStatus.LAUNCHED) == 0){
				try {
					List<BusinessGoogleMyBusinessLocation> businessLocations = socialGMBRepo.findByAgentId(id);
					if (CollectionUtils.isEmpty(businessLocations)) {
						return;
					}
					List<BusinessGoogleMyBusinessLocation> finalBusinessList = new ArrayList<>();
					for (BusinessGoogleMyBusinessLocation gmbPage : businessLocations) {
						if (Objects.nonNull(gmbPage.getBusinessId()) && gmbPage.getgMsgLocationStatus() == null
								|| (StringUtils.isNotEmpty(gmbPage.getgMsgLocationStatus()) && !gmbPage.getgMsgLocationStatus().equalsIgnoreCase(GoogleLocationStatus.LAUNCHED.toString()))
								&& gmbPage.getPermissions() != null && gmbPage.getPermissions().contains(Constants.BUSINESS_MESSAGING)
								&& gmbPage.getPermissions().contains(Constants.BUSINESS_COMMUNICATION)
								&& Objects.nonNull(gmbPage.getPlaceId())) {
							finalBusinessList.add(gmbPage);
						}
					}
					googleSocialAccountService.initSetupLocation(businessLocations.get(0).getEnterpriseId(), agent, finalBusinessList, false);
				}catch (Exception e){
					logger.error("Unable to launch agent for agent id :{} and name : {} with error : {}", id, agent.getAgentDisplayName(),e.getMessage());
				}
			}
		});
	}

	@Override
	@Deprecated
	public void handleGbmLocationMovement(LocationMovementDto locMoveDto) throws Exception {
		logger.info("handleGbmLocationMovement: locationMovementDto {}", locMoveDto);
		if(! LocationMovementEnum.MOVE.name().equalsIgnoreCase(locMoveDto.getEventType())){
			return;
		}
		List<BusinessGoogleMyBusinessLocation> gmbRawPages = socialGMBRepo.findByEnterpriseId(locMoveDto.getTargetEnterpriseNumber());
		if (CollectionUtils.isEmpty(gmbRawPages)) {
			return;
		}
		logger.info("handleGbmLocationMovement: Found {} GMB raw pages", gmbRawPages.size());
		// Check if target has a launched agent or not
		List<GoogleMessagesAgent> gmAgents = googleMsgAgentService.findByEnterpriseIdIn(locMoveDto.getTargetEnterpriseNumber());

		if(CollectionUtils.isNotEmpty(gmAgents)) {
			Map<Integer,GoogleMessagesAgent> agentMap = gmAgents.stream().collect(Collectors.toMap(GoogleMessagesAgent::getId,Function.identity()));
			for (BusinessGoogleMyBusinessLocation page : gmbRawPages) {
				try {
					if(Objects.isNull(page.getAgentId())){
						logger.info("handleGbmLocationMovement: Null AgentId in GMB raw page {} ", page.getLocationId());
						return;
					}
					GoogleMessagesAgent gmAgent = agentMap.get(page.getAgentId());
					String locBrandName = "";
					logger.info("handleGbmLocationMovement: Processing pageId {}", page.getLocationId());
					if (StringUtils.isNotEmpty(page.getgMsgLocationStatus()) && page.getIsValid() == 1 && page.getIsVerified() == 1
							&& GoogleLocationStatus.LAUNCHED.name().equalsIgnoreCase(page.getgMsgLocationStatus())) {
						// Get the brandName associated with the location
						locBrandName = StringUtils.isNotEmpty(page.getgMsgLocationName()) ? page.getgMsgLocationName().split("/locations/")[0] : "";
						// If target does not have an agent OR has an un-launched agent OR has a launched agent which is
						// different from that already associated with the current location, we need to un-launch the location
						// that is associated with some other brand/agent
						if (Objects.nonNull(gmAgent) && !GoogleAgentStatus.LAUNCHED.name().equalsIgnoreCase(gmAgent.getStatus()) && !locBrandName.equals(gmAgent.getBrandName())) {
							logger.info("handleGbmLocationMovement: un-launching pageId {}", page.getLocationId());
							LocationUnlaunchRequest unlaunchRequest = new LocationUnlaunchRequest();
							unlaunchRequest.setEnterpriseId(locMoveDto.getTargetEnterpriseNumber());
							unlaunchRequest.setPageId(page.getPlaceId());
							unlaunchRequest.setAgentId(page.getAgentId());
							googleSocialAccountService.unLaunchLocation(unlaunchRequest);
						}
					}
					logger.info("Removing un-launched GBM location info(isMessagingEnabled={}, gMsgLocationName={}, gMsgLocationStatus={}) from pageId {}",
							page.getIsMessagingEnabled(), page.getgMsgLocationName(), page.getgMsgLocationStatus(), page.getLocationId());
					// Remove all agent/brand info from this location
					page.setIsMessagingEnabled(0);
					page.setgMsgLocationName(null);
					page.setgMsgLocationStatus(null);
					page.setgMsgLocationComment("GBM location info associated with old agent removed due to location movement");
					socialGMBRepo.save(page);
					logger.info(String.format("Sending validity check for location {} from %s", "handleGbmLocationMovement"), page.getLocationId());
					gmbRawPageService.pushToKafkaForValidity(Constants.GMB, page.getLocationId());
					// Launch the location, only if target has a LAUNCHED agent
					if (Objects.nonNull(gmAgent) && (GoogleAgentStatus.LAUNCHED.name().equals(gmAgent.getStatus()) && gmAgents.size() == 1 && page.getIsValid() == 1
							&& page.getIsVerified() == 1 && !locBrandName.equals(gmAgent.getBrandName()))) {
						logger.info("handleGbmLocationMovement: trying to launch location with target's agent : {}",gmAgents.get(0));
						googleSocialAccountService.setupLocation(new CreateLocationRequest(locMoveDto.getTargetEnterpriseNumber(),
								page.getPlaceId(),gmAgent.getAgentName(), gmAgent.getBrandName(), false));
					}
				} catch (Exception exp) {
					logger.info("handleGbmLocationMovement: Exception while processing pageId {}", page.getLocationId(), exp);
				}
			}
		}

		// In case of SMB, Check if source has any agent info
		if(Objects.isNull(locMoveDto.getSourceEnterpriseNumber()) && Objects.nonNull(locMoveDto.getSourceBusinessNumber())){
			return;
		}
		logger.info("handleGbmLocationMovement: Source is SMB");
		unLaunchAgent(new AgentUnLaunchRequest(locMoveDto.getSourceBusinessNumber(),gmAgents.get(0).getId()));
	}

	@Override
	public List<SocialElasticDto> fetchRawPagesSocialEsDto(List<String> integrationIds, String channel) {
		List<? extends Object> rawPages = new ArrayList<>();
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			rawPages = fbSocialAccountService.fetchRawPages(integrationIds);
		}
		else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			rawPages = googleSocialAccountService.fetchRawPages(integrationIds);
		}
		else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			rawPages = twitterSocialAccountService.fetchRawPages(integrationIds);
		}
		return SocialElasticUtil.getSocialEsDtoFromObjects(rawPages,channel);
	}

	@Override
	public List<Number> fetchRawPagesId(List<String> integrationIds,String channel) {
		List<Number> objects = new ArrayList<>();
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			return fbSocialAccountService.fetchRawPagesId(integrationIds);
		}
		else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			return googleSocialAccountService.fetchRawPagesId(integrationIds);
		}
		else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			return twitterSocialAccountService.fetchRawPagesId(integrationIds);
		}
		return objects;
	}

	@Override
	public List<SocialElasticDto> fetchRawPagesSocialEsDtoByChannel(String channel) {
		List<SocialElasticDto> rawPages = new ArrayList<>();
		if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			rawPages = googleSocialAccountService.fetchPagesEsDto();
		}else if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			rawPages = fbSocialAccountService.fetchPagesEsDto();
		}else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			rawPages = twitterSocialAccountService.fetchPagesEsDto();
		}else if (SocialChannel.LINKEDIN.getName().equalsIgnoreCase(channel)) {
			rawPages = socialLinkedinService.fetchPagesEsDto();
		}
		return rawPages;
	}

	@Override
	public List<SocialElasticDto> fetchRawPagesSocialEsDtoByChannelAndId(String channel, Integer rawId) {
		List<SocialElasticDto> rawPages = null;
		if (BusinessGoogleMyBusinessLocation.class.getName().equalsIgnoreCase(channel)) {
			rawPages = googleSocialAccountService.fetchPagesEsDto(rawId);
		}else if (BusinessFBPage.class.getName().equalsIgnoreCase(channel)) {
			rawPages = fbSocialAccountService.fetchPagesEsDto(rawId);
		}else if (BusinessTwitterAccounts.class.getName().equalsIgnoreCase(channel)) {
			rawPages = twitterSocialAccountService.fetchPagesEsDto(rawId);
		}else if (BusinessLinkedinPage.class.getName().equalsIgnoreCase(channel)) {
			rawPages = socialLinkedinService.fetchPagesEsDto(rawId);
		}
		return rawPages;
	}

	@Override
	public FetchPageResponse getIntegrationPage(String channel, Long businessId) {
		logger.info("Request received to fetch integrated pages for business and channel {} {}",businessId,channel);
		FetchPageResponse fetchPageResponse = new FetchPageResponse();
		Map<String, List<ChannelAccountInfo>> data = new HashMap<>();
		BusinessGetPageRequest businessGetPageRequest = businessGetPageService.findLastRequestByEnterpriseIdAndChannel(businessId, SocialChannel.getSocialChannelByName(channel).getName());
		if(Objects.isNull(businessGetPageRequest)){
			logger.info("No request exists for business and channel {} {}",businessId,channel);
			throw new BirdeyeSocialException(ErrorCodes.NO_BUSINESS_GET_PAGE_REQUEST_FOUND,"No request exists for business");
		}
		boolean allPagesMapped = false;
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			data = fbSocialAccountService.getPages(businessGetPageRequest, businessId);
		}
		else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			return googleSocialAccountService.getIntegrationPage(businessGetPageRequest, businessId);
		}
		else if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			data = twitterSocialPostService.getTwitterAccounts(businessGetPageRequest, businessId);
		}
		else if (SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)) {
			data = youTubeAccountService.getIntegratedChannels(businessGetPageRequest, businessId);

		} else if (SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel)) {
			ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));
			data = arborService.getIntegratedChannels(businessGetPageRequest, businessId);
		}
		if (Objects.nonNull(data) && Objects.nonNull(channel) && data.containsKey(channel)) {
			allPagesMapped = data.get(channel).stream()
					.allMatch(ChannelAccountInfo::getIsMapped);
		}
		fetchPageResponse.setAllPagesMapped(allPagesMapped);
		fetchPageResponse.setPageTypes(data);
		return fetchPageResponse;
	}

	@Override
	public InboxStatusResponseComplete getMessengerStatus(InboxStatus request) {
		InboxStatusResponseComplete responseComplete = new InboxStatusResponseComplete();
		List<InboxStatusSourceViseResponse> responses = new ArrayList<>();
		for(String source: request.getSources()) {
			InboxStatusResponse res = null;
			if(source.equalsIgnoreCase(SocialChannel.GMB.getName())) {
				res = checkgMsgStatus(request.getEnterpriseId());
			} else if(source.equalsIgnoreCase(SocialChannel.INSTAGRAM.getName())) {
				res = socialInstagramService.checkMessengerStatus(request.getEnterpriseId());
			}
			InboxStatusSourceViseResponse status = new InboxStatusSourceViseResponse(source,res);
			responses.add(status);
		}
		responseComplete.setResponse(responses);
		return responseComplete;
	}

	private void unSubscribeNotification(SocialSetupAudit socialSetupAudit) {
		logger.info("Unsubscribe notification for channel: {} and id: {}",socialSetupAudit.getChannel(),socialSetupAudit.getIntegrationId());
		try {
			String channel = socialSetupAudit.getChannel();
			SocialUnSubscribe execute = socialUnSubscribeFactory.getSocialUnsubscribeChannel(channel)
					.orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
			execute.unsubscribeNotification(socialSetupAudit);
		} catch (Exception e) {
			logger.info("Error while Unsubscribing notification for channel: {} and id: {}",socialSetupAudit.getChannel(),socialSetupAudit.getIntegrationId(),e);
		}
	}

	@Override
	public void auditSocialPage(SocialSetupAudit socialSetupAudit,String channel) {
		socialSetupAuditRepository.saveAndFlush(socialSetupAudit);
		if(Objects.nonNull(socialSetupAudit) && (SocialSetupAuditEnum.REMOVE_PAGE.name().equalsIgnoreCase(socialSetupAudit.getAction())||
				SocialSetupAuditEnum.REMOVE_MAPPING.name().equalsIgnoreCase(socialSetupAudit.getAction()))) {
			unSubscribeNotification(socialSetupAudit);
		}
	}

	@Override
	public void updateAccessToken(TokenUpdateRequest request) {
		if(request.getChannel().equalsIgnoreCase(SocialChannel.INSTAGRAM.getName())) {
			socialInstagramService.updateAccessToken(request);
		} else if(request.getChannel().equalsIgnoreCase(SocialChannel.FACEBOOK.getName())) {
			fbSocialAccountService.updateAccessToken(request);
		}
	}

	@Override
	public String getFbIdByBusinessId (Integer businessId) {
		List<BusinessFBPage> mappedPage = fbSocialAccountService.findByBusinessId(businessId);
		if(CollectionUtils.isNotEmpty(mappedPage)) {
			return mappedPage.get(0).getFacebookPageId();
		} else {
			return null;
		}
	}

	@Override
	public Map<String, Object> getByBusinessId(String facebookPageId, List<String> fields) {
		Map<String, Object> map = new HashMap<>();
		List<BusinessFBPage> fbPages = fbSocialAccountService.findByFacebookPageIdAndIsValid(facebookPageId, 1);
		if(CollectionUtils.isNotEmpty(fbPages)) {
			BusinessFBPage fbPage = fbPages.get(0);
			Map<String, Object> allfields = new ObjectMapper().convertValue(fbPage, Map.class);
			if(CollectionUtils.isEmpty(fields)) {
				return  allfields;
			}else {
				fields.forEach((f) -> map.put(f, allfields.get(f)));
			}
		}
		return map;

	}

	@Override
	public ChannelStatusResponse getChannelIntegrationStatus(String channel, SocialFilterRequest request) {

		logger.info("Received getChannelIntegrationStatus request for channel {} with request {}", channel,
				request.toString());

		ChannelStatusResponse response = new ChannelStatusResponse();
		List<Integer> locationIds = null;
		List<Integer> input = request.getBusinessIds();
		Long accountId = request.getBusinessId();
		// All locations are part of filter.
		if (input.isEmpty()) {
			BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLiteByNumber(accountId);
			boolean isSMB = checkBusinessSMB(businessLiteDTO);
			if (isSMB) {
				locationIds = Arrays.asList(businessLiteDTO.getBusinessId());
			} else {
				List<LocationDetailsDTO> locationDetailsDTOS = businessCoreService
						.getBusinessLocations(businessLiteDTO.getBusinessId());
				locationIds = locationDetailsDTOS.stream().map(l -> l.getBusinessId()).collect(Collectors.toList());
			}
			logger.info("For accountID {}, fetched {} businessIds", accountId, locationIds.size());
		} else {
			locationIds = input;
		}

		response.setTotalLocations(locationIds.size());
		Integer unmapped = null;
		if(CollectionUtils.isNotEmpty(locationIds)) {
			if (SocialChannel.GOOGLE_PLUS_GMB.getName().equalsIgnoreCase(channel)
					|| SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
				unmapped = googleSocialAccountService.getIntegrationStatus(accountId, locationIds);
			} else if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
				unmapped = fbSocialAccountService.getIntegrationStatus(accountId, locationIds);
			} else {
				throw new BirdeyeSocialException("Unsupported channel " + channel);
			}
			response.setUnmapped(unmapped);
		}
		else{
			logger.info("For accountID {}, No location found", accountId);
			response.setUnmapped(0);
		}

		return response;
	}

	//TODO: Listing support. should be fixed
	@Override
	public ConnectedPages getConnectedPages(String channel, Long enterpriseId, Integer userId) {
		Business business = businessRepo.findByBusinessId(enterpriseId);
		validateBusiness(business, enterpriseId);
		ConnectedPages connectedPages = null;
		Map<Integer, BusinessEntity> idToBusinessMap = new HashMap<>();
		List<Integer> businessIds = businessUtilService.getBusinessLocationsForEnterprise(business, null);
		if (CollectionUtils.isEmpty(businessIds)) {
			throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, ("Business not found for enterpriseId :" + enterpriseId));
		}
		List<BusinessEntity> businesses = businessRepo.getAllBusinessByBid(businessIds);
		logger.info("for enterprise {} total businesses are :: {}", enterpriseId, (businesses != null ? businesses.size() : 0));
		businesses.stream().forEach(childbusiness -> idToBusinessMap.put(childbusiness.getId(), childbusiness));
		if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			//TODO: KHYATI Handling required. 
			connectedPages = fbSocialAccountService.getConnectedPages(idToBusinessMap, business.getBusinessId());
		}
		if (SocialChannel.GOOGLE_PLUS_GMB.getName().equalsIgnoreCase(channel) || SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
			connectedPages = googleSocialAccountService.getConnectedPages(enterpriseId, idToBusinessMap);
		}
		if (SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
			connectedPages = twitterSocialPostService.getConnectedPages(idToBusinessMap, enterpriseId, 0, 0);
		}
		return connectedPages;
	}

	@Override
	public void updatedFbPagesWithPhone() {
		fbSocialAccountService.updatedFbPagesWithPhone();
	}

	@Override
	public AgentAccountResponse getAccount(String agentId) {
		logger.info("Get agent response for agent id : {}",agentId);
		AgentAccountResponse agentAccountResponse = new AgentAccountResponse();
		GoogleMessagesAgent googleMessagesAgent = googleMsgAgentService.findByAgentId(agentId);
		if(Objects.isNull(googleMessagesAgent)){
			return agentAccountResponse;
		}
		agentAccountResponse.setEnterpriseId(googleMessagesAgent.getEnterpriseId());
		BusinessGoogleMyBusinessLocation location = socialGMBRepo.findFirstByAgentIdAndGMsgLocationStatusAndBusinessIdNotNullOrderByBusinessIdAsc(googleMessagesAgent.getId(),GoogleLocationStatus.LAUNCHED.name());
		if(Objects.isNull(location)){
			logger.info("Return agent response : {} for agent id : {}",agentAccountResponse,agentId);
			return agentAccountResponse;
		}
		agentAccountResponse.setBusinessId(location.getBusinessId());
		logger.info("Return agent response : {} for agent id : {} with business id : {}",agentAccountResponse,agentId,location.getBusinessId());
		return agentAccountResponse;
	}



	public void createAgent(SetupAgentRequest req,String brandName) throws Exception {
		logger.info("setupAgent: req {}", req);
		GoogleMessagesAgent gmAgent = null;
		GoogleAgentStatus oldAgentStatus = null, updatedAgentStatus = null;
		Agent agent = null;
		String comments = null;
		try {
			// Check if brand exists
			gmAgent = googleMsgAgentService.findByEnterpriseId(req.getEnterpriseId());
			if (gmAgent == null)
				throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_BRAND_NOT_CREATED,
						String.format("Brand not found for enterpriseId %d", req.getEnterpriseId()));

			// Get the existing status of agent
			updatedAgentStatus = oldAgentStatus = GoogleAgentStatus.valueOf(gmAgent.getStatus());
			logger.info("setupAgent: Agent found with status {}", oldAgentStatus);

			// Check for brand creation error
			if (oldAgentStatus == GoogleAgentStatus.BRAND_CREATION_ERROR)
				throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_BRAND_NOT_CREATED,
						String.format("Brand creation error found enterpriseId %d", req.getEnterpriseId()));

			// Start agent setup process according to status
			switch (oldAgentStatus) {
				case BRAND_CREATED:
				case AGENT_CREATION_ERROR:
					logger.info("setupAgent: Creating agent...");
					agent = googleBizCommService.createAgent(gmAgent.getBrandName(), req.getDisplayName(),
							req.getWelcomeMessage(), req.getOfflineMessage(), req.getLogoUrl());
					updatedAgentStatus = GoogleAgentStatus.AGENT_CREATED;
			}
		} catch (GoogleJsonResponseException googleExp) {
			logger.error("Google Exception in setupAgent", googleExp);
			comments = googleExp.getDetails().getMessage();
			if (StringUtils.isNotEmpty(comments) && googleBizCommService.isInvalidNameError(comments))
				throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_NAME_INVALID,
						ErrorCodes.GOOGLE_MESSAGES_AGENT_NAME_INVALID.name());
			throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_SETUP_ERROR,
					"Exception in setupAgent while calling Google Business Comm API");
		} catch (BirdeyeSocialException beExp) {
			comments = beExp.getMessage();
			throw beExp;
		} catch (Exception exp) {
			logger.error("Exception in setupAgent", exp);
			comments = StringUtils.isNotEmpty(exp.getMessage())
					? exp.getMessage().substring(0, Math.min(exp.getMessage().length(), 300))
					: "Exception in setupAgent. Last known status is " + updatedAgentStatus;
			throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_SETUP_ERROR, comments);
		} finally {
			if (updatedAgentStatus != null) {
				switch (updatedAgentStatus) {
					case BRAND_CREATED:
						updatedAgentStatus = GoogleAgentStatus.AGENT_CREATION_ERROR;
						break;
					case VERIFIED:
						updatedAgentStatus = GoogleAgentStatus.LAUNCH_ERROR;
						break;
				}
			}
			if (updatedAgentStatus != null && oldAgentStatus != null
					&& updatedAgentStatus.compareTo(GoogleAgentStatus.LAUNCHED) <= 0
					&& oldAgentStatus.compareTo(GoogleAgentStatus.LAUNCHED) < 0) {
				// Update agent info if status was greater than BRAND_CREATED
				if (updatedAgentStatus.compareTo(GoogleAgentStatus.BRAND_CREATED) >= 0) {
					googleMsgAgentService.saveAgentInfo(gmAgent, updatedAgentStatus, req, agent, comments);
				}

				// Start location setup process if agent is now LAUNCHED (and was not already launched)
				if (updatedAgentStatus == GoogleAgentStatus.AGENT_CREATED) {
					List<BusinessGoogleMyBusinessLocation> mappedPages = socialGMBRepo.findByEnterpriseIdAndBusinessIdNotNull(req.getEnterpriseId());
					GoogleMessagesAgent finalGmAgent = gmAgent;
					mappedPages.forEach(page -> {
						CreateLocationRequest request = new CreateLocationRequest(req.getEnterpriseId(),page.getPlaceId(), finalGmAgent.getAgentName(), finalGmAgent.getBrandName(),false);
						try {
							BusinessGoogleMyBusinessLocation googleMyBusinessLocation = googleSocialAccountService.createLocation(request);
							socialGMBRepo.save(googleMyBusinessLocation);
						} catch (Exception e) {
							logger.info("Unable to create location place id :{}",page.getPlaceId());
						}
					});

				}
			}
		}
	}

	@Override
	public SocialChannelsByLocationResponse getAllChannelsByBusinessId(Integer businessId) {
		SocialChannelsByLocationResponse response = new SocialChannelsByLocationResponse();
		try {
			List<SocialChannelByLocationData> locationData = new ArrayList<>();

			List<BusinessFBPage> businessFBPages = fbSocialAccountService.findByBusinessId(businessId);
			if(CollectionUtils.isNotEmpty(businessFBPages)) {
				locationData.add(createSocialPageData(businessFBPages.get(0).getId(), businessId, businessFBPages.get(0).getFacebookPageName(), businessFBPages.get(0).getEnterpriseId(), SocialChannel.FACEBOOK.getName(),businessFBPages.get(0).getHandle(),businessFBPages.get(0).getFacebookPagePictureUrl()));
			}

//			BusinessGoogleMyBusinessLocation googleMyBusinessLocation = gmbRawPageService.findByBusinessId(businessId);
//			if(Objects.nonNull(googleMyBusinessLocation)) {
//				locationData.add(createSocialPageData(googleMyBusinessLocation.getId(), businessId, googleMyBusinessLocation.getAccountName(), googleMyBusinessLocation.getEnterpriseId(), SocialChannel.GMB.getName()));
//			}

			BusinessInstagramAccount instagramAccount = socialInstagramService.getInstagramPageByBusinessId(businessId);
			if(Objects.nonNull(instagramAccount)) {
				locationData.add(createSocialPageData(instagramAccount.getId(), businessId, instagramAccount.getInstagramAccountName(), instagramAccount.getEnterpriseId(), SocialChannel.INSTAGRAM.getName(), instagramAccount.getInstagramHandle(),instagramAccount.getInstagramAccountPictureUrl()));
			}

			BusinessTwitterAccounts twitterAccounts = twitterSocialAccountService.getTwitterAccountByBusinessId(businessId);
			if(Objects.nonNull(twitterAccounts)) {
				locationData.add(createSocialPageData(twitterAccounts.getId(), businessId, twitterAccounts.getName(), twitterAccounts.getEnterpriseId(), SocialChannel.TWITTER.getName(),twitterAccounts.getHandle().substring(1),twitterAccounts.getProfilePicUrl()));
			}

			// todo - @ashu - refactor to use service
			List<BusinessLinkedinPage> linkedinPages = linkedinRepo.findByBusinessId(businessId);
			linkedinPages = linkedinPages.stream().filter(page -> LinkedinPageTypeEnum.COMPANY.getName().equals(page.getPageType())).collect(Collectors.toList());
			if(CollectionUtils.isNotEmpty(linkedinPages)) {
				locationData.add(createSocialPageData(linkedinPages.get(0).getId(), businessId, linkedinPages.get(0).getCompanyName(), linkedinPages.get(0).getEnterpriseId(), SocialChannel.LINKEDIN.getName(), linkedinPages.get(0).getCompanyName(), null));
			}

			response.setChannels(locationData);

		} catch (Exception ex) {
			logger.info("Something went wrong while fetching social channel detaisl with businessId {} ", businessId);
		}
		return response;
	}

	@Override
	public SocialLocationsMappedResponse getAllMappedLocation(Long enterpriseId) {
		SocialLocationsMappedResponse response = new SocialLocationsMappedResponse();
		try {
			if ( enterpriseId == null ) {
				throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Invalid value for enterpriseId/userId/businessIds");
			}

			List<Integer> totalEnterpriseBusinessIds = new ArrayList<>();

			List<Integer> twitterBusinessIds = twitterSocialAccountService.getBusinessIdsByEnterpriseId(enterpriseId, 1);

			if(CollectionUtils.isNotEmpty(twitterBusinessIds)) {
				totalEnterpriseBusinessIds.addAll(twitterBusinessIds);
			}

			List<Integer> fbPages =  fbSocialAccountService.findBusinessIdsByEnterpriseId(enterpriseId, 1);
			if(CollectionUtils.isNotEmpty(fbPages)) {
				totalEnterpriseBusinessIds.addAll(fbPages);
			}

			List<Integer> igPages = instagramAccountRepo.findBusinessIdsByEnterpriseId(enterpriseId,1);
			if(CollectionUtils.isNotEmpty(igPages)) {
				totalEnterpriseBusinessIds.addAll(igPages);
			}

			// todo - @ashu - refactor to use service
			List<Integer> linkedinPages = linkedinRepo.findBusinessIdsByEnterpriseId(enterpriseId,1);
			if(CollectionUtils.isNotEmpty(linkedinPages)) {
				totalEnterpriseBusinessIds.addAll(linkedinPages);
			}

			totalEnterpriseBusinessIds = totalEnterpriseBusinessIds.stream()
					.distinct()
					.collect(Collectors.toList());

			List<BusinessBizLiteDto> businessDetails = businessCoreService.getBusinessLiteDtoByBusinessIds(totalEnterpriseBusinessIds);

			List<SocialLocationsMappedData> socialLocationsMappedDataList = new ArrayList<>();

			for(BusinessBizLiteDto business: businessDetails) {
				SocialLocationsMappedData socialLocationsMappedData = new SocialLocationsMappedData();
				socialLocationsMappedData.setLocationId(business.getId());
				socialLocationsMappedData.setLocationName(business.getName());

				socialLocationsMappedDataList.add(socialLocationsMappedData);
			}
			if(CollectionUtils.isNotEmpty(socialLocationsMappedDataList)) {
				// socialLocationsMappedDataList.sort(Comparator.comparing(SocialLocationsMappedData::getLocationName));
				Comparator<SocialLocationsMappedData> comparator = Comparator.comparing(
						SocialLocationsMappedData::getLocationName,
						Comparator.nullsFirst(String.CASE_INSENSITIVE_ORDER)
				);

				socialLocationsMappedDataList.sort(comparator);

			}
			response.setData(socialLocationsMappedDataList);

		} catch (Exception ex) {
			logger.info("Something went wrong while fetching location details for {}, exception: {}", enterpriseId, ex);
		}
		return response;
	}


	@Override
	@Deprecated
	public void relaunchLocation(CreateLocationRequest req) {
		GoogleMessagesAgent agent = null;
		try {
			logger.info("request received for location relaunch with data {}", req);
			agent = googleMsgAgentService.findByAgentId(req.getAgentId());
			if (Objects.isNull(agent) || StringUtils.isEmpty(agent.getStatus())) {
				logger.info("No agent found for agentId {}", req.getAgentId());
				return;
			}
			if (agent.getStatus().equalsIgnoreCase(GoogleAgentStatus.LAUNCHED.name())) {
				if (CollectionUtils.isEmpty(req.getBusinessIds())) {
					return;
				}
				// if agent id of given business is different form given agent id
				List<Integer> businessIds = getBusinessIdsFromNumber(req.getBusinessIds());
				List<BusinessGoogleMyBusinessLocation> gmbLocations = gmbRawPageService.findByBusinessIds(businessIds, 1, 1);
				if (CollectionUtils.isEmpty(gmbLocations)) {
					logger.info("No valid locations found to trigger launch activity for businessIds {}", req.getBusinessIds());
					return;
				}
				updateAndRelauchLocations(gmbLocations, agent);
			} else {
				SetupAgentRequest setupAgentRequest = createAgentSetUpRequest(req, agent);
				setupAgent(setupAgentRequest);
			}
		} catch (Exception ex) {
			throw new BirdeyeSocialException(ErrorCodes.ERROR_WHILE_RELAUNCHING_LOCATION,
					iPermissionMappingService.getDataByChannelAndModuleAndPermissionCode(SocialChannel.GMB.getName(), SocialMessageModule.GMB_MESSAGING, ErrorCodes.ERROR_WHILE_RELAUNCHING_LOCATION.value()));
		} finally {
			if (Objects.isNull(agent)) {
				createAuditRequest(req.getPlaceId(), null, null, null,
						"Error occured while location setup", req.getBrandName(), req.getAgentName(), req.getAgentId(), GoogleAgentStatus.LAUNCH_ERROR.name());

			} else {
				createAuditRequest(req.getPlaceId(), null, null, null,
						null, agent.getBrandName(), agent.getAgentName(),
						agent.getId(), agent.getStatus());
			}
		}
	}


	@Async
	@Deprecated
	private void updateAndRelauchLocations(List<BusinessGoogleMyBusinessLocation> gmbLocations, GoogleMessagesAgent agent) {
		List<BusinessGoogleMyBusinessLocation> businessGoogleMyBusinessLocations = new ArrayList<>();
		List<String> pageIds = gmbLocations.stream().map(BusinessGoogleMyBusinessLocation::getPlaceId).collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(pageIds)) {
			logger.info("Update location status for enterprise id : {}",agent.getEnterpriseId());
			UpdateLocationStateRequest updateLocationStateRequest = new UpdateLocationStateRequest(gmbLocations.get(0).getEnterpriseId(), pageIds,false);
			googleSocialAccountService.updateLocationState(updateLocationStateRequest);
		}
		gmbLocations.forEach(page -> {
			try {
				if (Objects.nonNull(page.getAgentId()) && !Objects.equals(agent.getId(), page.getAgentId())) {
					logger.info("Agent id {} not equal to requests agent id : {}", agent.getId(), page.getAgentId());
					return;
				}
				if(StringUtils.isNotEmpty(page.getPermissions())
						&& StringUtils.isNotEmpty(page.getPlaceId())
						&& page.getPermissions().contains(Constants.BUSINESS_MESSAGING)
						&& page.getPermissions().contains(Constants.BUSINESS_COMMUNICATION)
						&& (page.getgMsgLocationStatus() == null || (page.getgMsgLocationStatus() != null
						&& !page.getgMsgLocationStatus().equalsIgnoreCase(GoogleLocationStatus.LAUNCHED.name())))) {
					businessGoogleMyBusinessLocations.add(page);
				}
			} catch (Exception e) {
				logger.info("Something went wrong while launching the location for location {} and error {}", page.getLocationId(), e.getMessage());
			}
		});
		googleSocialAccountService.initSetupLocation(agent.getEnterpriseId(), agent,businessGoogleMyBusinessLocations, false);
	}

	private SetupAgentRequest createAgentSetUpRequest(CreateLocationRequest req, GoogleMessagesAgent agent) {
		SetupAgentRequest setupAgentRequest = new SetupAgentRequest();
		setupAgentRequest.setAgentId(req.getAgentId());
		setupAgentRequest.setDisplayName(agent.getAgentDisplayName());
		setupAgentRequest.setLogoUrl(agent.getLogoUrl());
		setupAgentRequest.setEnterpriseId(req.getEnterpriseId());
		setupAgentRequest.setOfflineMessage(agent.getOfflineMsg());
		setupAgentRequest.setWelcomeMessage(agent.getWelcomeMsg());
		setupAgentRequest.setUserId(agent.getCreatedBy());
		return setupAgentRequest;
	}

	@Override
	public AgentResponse createWidget(CreateWidgetRequest request) {
		AgentResponse agentResponse = new AgentResponse();
		logger.info("Create widget for request : {}",request);
		List<GoogleMessagesAgent> googleMessagesAgent =
				googleMsgAgentService.findByEnterpriseIdAndWidgetName(request.getEnterpriseId(),request.getWidgetName().trim());
		if(CollectionUtils.isNotEmpty(googleMessagesAgent)){
			throw new BirdeyeSocialException(ErrorCodes.WIDGET_ALREADY_EXISTS,"Widget with same name already exists");
		}
		GoogleMessagesAgent agent = new GoogleMessagesAgent();
		agent.setEnterpriseId(request.getEnterpriseId());
		agent.setWidgetName(request.getWidgetName());
		agent.setCreatedBy(request.getUserId());
		// if any refresh or redirection occurs then the agent will be drafted.
		agent.setStatus(GoogleAgentStatus.WIDGET_CREATED.name());
		GoogleMessagesAgent messagesAgent = googleMsgAgentService.saveWidgetInfo(agent);
		// add agent-id to gmb pages
		logger.info("Set agent id to gmb mapped pages :{}",request.getBusinessIds());
		Map<String, BusinessLocationLiteDTOForGMB> map =  businessCoreService.getBusinessesInBulkByBusinessNumber(request.getBusinessIds(), false);
		if(CollectionUtils.isEmpty(map.values())){
			return agentResponse;
		}
		List<Integer> businessIds = map.values().stream().map(BusinessLocationLiteDTOForGMB::getBusinessId).collect(Collectors.toList());
		logger.info("Set agent id to gmb mapped pages :{}",map);
		gmbRawPageService.addAgentIdToLocations(messagesAgent.getId(), businessIds);
		agentResponse.setAgentId(messagesAgent.getId());
		agentResponse.setWidgetName(request.getWidgetName());
		logger.info("Set default message for the created widget : {}",messagesAgent.getId());
		googleSocialAccountService.setDefaultMessageAndLogo(messagesAgent,agentResponse,request.getEnterpriseId());
		setLocationInfoForAgent(agentResponse,map.values());
		return agentResponse;
	}

	private void setLocationInfoForAgent(AgentResponse agentResponse,Collection<BusinessLocationLiteDTOForGMB> bizLiteDtos) {
		logger.info("Set location details for created widget with agent id: {}",agentResponse.getAgentId());
		if(CollectionUtils.isEmpty(bizLiteDtos)){
			return;
		}
		List<GMBLocationMessageAgent> gmbLocations = new ArrayList<>();
		bizLiteDtos.forEach(business -> {
			GMBLocationMessageAgent location = new GMBLocationMessageAgent();
			location.setBusinessAlias(business.getBusinessAlias());
			location.setBusinessId(business.getBusinessId());
			location.setBusinessNumber(business.getBusinessNumber());
			location.setBusinessName(business.getBusinessName());
			gmbLocations.add(location);
		});
		agentResponse.setBusinessLocations(gmbLocations);
	}

	@Async
	@Override
	public void validateToken(SocialTokenValidationDTO payload) {
		if(SocialChannel.YOUTUBE.getName().equalsIgnoreCase(payload.getChannel())) {
			// google_refresh_token contains channel=youtube when token is for youtube
			youTubeAccountService.validateSocialServiceTokens(payload);
		} else if(SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(payload.getChannel())) {
			instagramSocialService.validateToken(payload);
		}  else if(SocialChannel.TIKTOK.getName().equalsIgnoreCase(payload.getChannel())) {

			ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(payload.getChannel()));
			arborService.validateToken(payload);
		}
		 else {
			// google_refresh_token contains channel=null when token is for GMB
			googleSocialAccountService.validateSocialServiceTokens(payload);
		}
	}

	@Async
	@Override
	public void markPageAsInvalid(SocialTokenValidationDTO payload) {
		if(Objects.isNull(payload) || Objects.isNull(payload.getId()) || StringUtils.isEmpty(payload.getChannel())) {
			logger.error("invalid payload");
			return;
		}
		if(SocialChannel.TWITTER.getName().equalsIgnoreCase(payload.getChannel())) {
			twitterSocialAccountService.markTwitterPageAsInvalid(payload);
		}
		if(SocialChannel.LINKEDIN.getName().equalsIgnoreCase(payload.getChannel())) {
			socialLinkedinService.markLinkedinPageAsInvalid(payload);
		} if(SocialChannel.TIKTOK.getName().equalsIgnoreCase(payload.getChannel()) || SocialChannel.WHATSAPP.getName().equalsIgnoreCase(payload.getChannel())) {
			ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(payload.getChannel()));
			arborService.markPageInvalid(payload);
		}
	}

	@Override
	public void reconnectValidPages(AccessTokenUpdateRequest updateRequest) {
		facebookPageService.reconnectFBValidPages(updateRequest);
	}

	@Override
	public void processAdsEvent(FacebookEventRequest request, String channel) {
		if(SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			fbNotificationService.processFbPostEvent(request);
		} else {
			logger.info("This channel does not support ads event");
		}
	}

	@Override
	public void initiateDPSync(String channel) {
		SocialChannel socialChannel = SocialChannel.getSocialChannelByName(channel);
		if(Objects.isNull(socialChannel)) return;

		switch (socialChannel) {
			case FACEBOOK:
				break;
			case INSTAGRAM:
				instragramSetupService.initiateDPSync();
				break;
			case GMB:
				googleSocialAccountService.initiateDPSync();
				break;
			case TWITTER:
				twitterSocialAccountService.initiateDPSync();
				break;
			case LINKEDIN:
				socialLinkedinService.initiateDPSync();
				break;
			case YOUTUBE:
				youTubeAccountService.initiateDPSync();
				break;
			default:
				break;
		}
	}

	@Override
	public void syncDP(String channel, DpSyncRequest dpSyncRequest) {
		SocialChannel socialChannel = SocialChannel.getSocialChannelByName(channel);
		if(Objects.isNull(socialChannel)) return;

		MDC.put(RateLimitingConstants.PAGE_ID, dpSyncRequest.getPageId());
		switch (socialChannel) {
			case FACEBOOK:
				fbSocialAccountService.syncFacebookDP(dpSyncRequest);
				break;
			case INSTAGRAM:
				instragramSetupService.syncIGDP(dpSyncRequest);
				break;
			case GMB:
				googleSocialAccountService.syncGmbDP(dpSyncRequest);
				break;
			case TWITTER:
				twitterSocialAccountService.syncTwitterDP(dpSyncRequest);
				break;
			case LINKEDIN:
				socialLinkedinService.syncLinkedinDP(dpSyncRequest);
				break;
			case YOUTUBE:
				youTubeAccountService.syncYoutubeDP(dpSyncRequest);
				break;
			default:
				break;
		}
	}

	@Override
	public AccountValidationResponse getAllChannelWiseSetupStatus(AccountValidationRequest request) {
		logger.info("Inside getAllChannelWiseSetupStatus for :{}", request);
		AccountValidationResponse response = new AccountValidationResponse();
		Integer businessId = request.getBusinessId();
		if (businessId == null) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "businessId can not be null.");
		}
		if(request.getFbIntegrationStatus()){
			response.setFbIntegrationStatus(fbSocialAccountService.checkPageIntegrationStatusByBusinessId(businessId));
		}if(request.getInstagramIntegrationStatus()){
			response.setInstagramIntegrationStatus(socialInstagramService.checkPageIntegrationStatusByBusinessId(businessId));
		}if(request.getTwitterIntegrationStatus()){
			response.setTwitterIntegrationStatus(twitterSocialAccountService.checkPageIntegrationStatusByBusinessId(businessId));
		}if(request.getGoogleIntegrationStatus()){
			response.setGoogleIntegrationStatus(gmbLocationDetailService.getGMBIntegrationStatus(businessId));
		}if(request.getAppleIntegrationStatus()){
			//TODO apple integration check
		}
		return response;
	}

	@Override
	public SocialBusinessStatusResponse getStatusOfBusinessWithChannel(SocialBusinessStatusRequest request) {
		List<Integer> businessIds = request.getBusinessIds();
		logger.info("Business ids size :{}",businessIds.size());
		List<Integer> socialChannels =
				approvalWorkflowConvertor.getIdsFromListOfChannels(StringUtils.isEmpty(request.getChannel()) ? null : Collections.singletonList(request.getChannel()));
		SocialBusinessStatusResponse socialBusinessStatusResponse = approvalWorkflowConvertor.getAndConvertToBusinessIdResponse(socialChannels,businessIds);
		kafkaProducer.sendObjectV1(KafkaTopicEnum.RESELLER_REVAMP_BULK_SOCIAL_SYNC.getName(), socialBusinessStatusResponse);
		return socialBusinessStatusResponse;
	}

	private SocialChannelByLocationData createSocialPageData(Integer pageId, Integer businessId, String pageName, Long enterpriseId, String channel, String handle, String profilePic) {
		SocialChannelByLocationData socialChannelByLocationData = new SocialChannelByLocationData();
		socialChannelByLocationData.setPageId(pageId);
		socialChannelByLocationData.setBusinessId(businessId);
		socialChannelByLocationData.setPageName(pageName);
		socialChannelByLocationData.setEnterpriseId(enterpriseId);
		socialChannelByLocationData.setChannel(channel);
		socialChannelByLocationData.setHandleName(handle);
		socialChannelByLocationData.setProfilePic(profilePic);

		return socialChannelByLocationData;
	}

	@Override
	public String googleAuthSetupUrl(String origin) throws Exception{
		return googleSocialAccountService.googleAuthUrlV2(origin, false);
	}


	@Override
	public String googleAuthLoginUrl(String domain) throws Exception {
		String origin = "https://"+domain;
		return googleSocialAccountService.googleAuthUrlV2(origin, true);
	}

	@Override
	public TiktokAuthUrlResponse integrationAuthUrl(String channel, String origin) throws URISyntaxException {
		SocialChannel socialChannel = SocialChannel.getSocialChannelByName(channel);
		channel = socialChannel.getName();
		String url = "";
		TiktokAuthUrlResponse response = null;
		if(SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
			response = TiktokAuthUrlResponse.builder()
					.redirectUrl(fbSocialAccountService.getAuthorizationUrl(origin))
					.build();
		} else if (SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(channel)) {
			response = TiktokAuthUrlResponse.builder()
					.redirectUrl(instragramSetupService.getAuthorizationUrl(origin))
					.build();
		} else if(SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel) || SocialChannel.WHATSAPP.getName().equalsIgnoreCase(channel)) {
			ArborService arborService = ArborServiceFactory.getService(socialChannel);
			response = arborService.getAuthLoginUrl(origin, null);
		}
		return response;
	}

	@Override
	public void unsubscribeAccountInit(String channel, Integer months, SocialSetupAuditEnum action) {
		logger.info("Unsubscribe activity received for channel: {} and months: {} and action: {}",channel,months,action.name());
		if(action!=SocialSetupAuditEnum.REMOVE_PAGE && action!=SocialSetupAuditEnum.REMOVE_MAPPING) {
			logger.info("Invalid action, returning!!");
			return;
		}
		LocalDate localDate = LocalDate.now().minusMonths(months);
		Date fromDate =  Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
		try {
			SocialUnSubscribe execute = socialUnSubscribeFactory.getSocialUnsubscribeChannel(channel)
					.orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
			List<SocialSetupAudit> auditList = execute.getSocialSetupAuditIds(fromDate, action);
			auditList.forEach(audit -> {
				kafkaProducer.sendObjectV1(SOCIAL_UNSUB_TOPIC,audit);
			});
		} catch (Exception e) {
			logger.info("Error while running activity for channel: {}",channel);
		}
	}

	@Override
	public void unsubscribePage(SocialSetupAudit socialSetupAudit) {
		if(Objects.nonNull(socialSetupAudit)) {
			logger.info("Request received to unsubscribe page:{} for channel: {}",socialSetupAudit.getIntegrationId(),socialSetupAudit.getChannel());
			unSubscribeNotification(socialSetupAudit);
		}
	}

	@Override
	public LocationPageMappingAllChannels getLocationMappingPagesAllChannels(LocationMappingRequest input, Integer accountId) throws Exception {
		logger.info("getLocationMappingPages for enterprise {} and userId: {}", input.getBusinessId(), input.getUserId());
		Set<String> finalStatus = new HashSet<>(Arrays.asList("mapped"));

		input.setPermission(Arrays.asList("REPORT"));

		if(input.getBusinessIds() != null) {
			logger.info("total number business ids recieved as input: {} , {}",input.getBusinessIds().size(),input.getBusinessIds());
		}
		if(CollectionUtils.isNotEmpty(input.getBusinessIds())) {
			input.setBusinessIds(input.getBusinessIds().stream().filter(Objects::nonNull).collect(Collectors.toList()));
		}

		Map<String, List<ChannelLocationInfo>> channelList = new HashMap<>();
			CompletableFuture<LocationPageMapping> fbPages = CompletableFuture.supplyAsync(() ->
			{
				try {
					return fbSocialAccountService.getLocationMappingPages(input.getBusinessId(), input.getUserId(),input.getBusinessIds(),finalStatus,input.getPage(),input.getSize(),input.getSearch(), input.getPermission(), accountId);
				} catch (Exception e) {
					return null;
				}
			});

		CompletableFuture<LocationPageMapping> twitterPages = CompletableFuture.supplyAsync(() ->
		{
			try {
				return twitterSocialAccountService.getLocationMappingPages(input.getBusinessId(),input.getUserId(),input.getBusinessIds(),finalStatus,input.getPage(),input.getSize(),input.getSearch(), input.getPermission());
			} catch (Exception e) {
				return null;
			}
		});

		CompletableFuture<LocationPageMapping> igPages = CompletableFuture.supplyAsync(() ->
		{
			try {
				return socialInstagramService.getLocationMappingPages(input.getBusinessId(),input.getUserId(),input.getBusinessIds(),finalStatus,input.getPage(),input.getSize(),input.getSearch(), input.getPermission());
			} catch (Exception e) {
				return null;
			}
		});

		CompletableFuture<Void> result = CompletableFuture.allOf(fbPages, twitterPages, igPages);

		result.get();

		channelList.put(SocialChannel.FACEBOOK.getName(), Objects.nonNull(fbPages.get()) ? fbPages.get().getLocationList()
				: Arrays.asList());
		channelList.put(SocialChannel.TWITTER.getName(), Objects.nonNull(twitterPages.get()) ? twitterPages.get().getLocationList()
				: Arrays.asList());
		channelList.put(SocialChannel.INSTAGRAM.getName(), Objects.nonNull(igPages.get()) ? igPages.get().getLocationList()
				: Arrays.asList());


		return LocationPageMappingAllChannels.builder()
				.data(channelList)
				.build();
	}

	@Override
	public ResellerLeafLocationResponse getAllLocations(ResellerLeafLocationRequest requestMap, String channel, Integer accountId, Integer userId, Integer page, Integer size) {
		logger.info("[Arbor] Request received for to getAllLocations with data {}: {}", accountId, requestMap);
		ArborService execute = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));

		Integer businessId;
		try {
			businessId = requestMap.getBusinessId();

			AccountListFilter request
					= createBusinessAccountLocationRequest(userId, page, size, requestMap.getSearchStr(), Arrays.asList("active","demo"));

			BusinessUserLocationResponse response = getAccountLeafLocations(accountId, request);

			List<BusinessUserLocationData> locationDataList = (response.getAccounts() == null) ? new ArrayList<>() : response.getAccounts();
			List<Integer> locationsIds = locationDataList.stream().map(BusinessUserLocationData::getBusinessId).collect(Collectors.toList());
			List<Integer> mappedLocationIds = execute.getMappedAccountLeafLocationIds(locationsIds);

			List<ResellerLeafLocation> accountLeafLocations = new ArrayList<>();
			locationDataList.stream().forEach(location -> {
				boolean isCurrentLocationMapped = mappedLocationIds.contains(location.getBusinessId());
				accountLeafLocations.add(new ResellerLeafLocation(location.getBusinessId(), location.getName(), location.getAddress(), isCurrentLocationMapped));
			});
			String accountName = null;
			return new ResellerLeafLocationResponse(response.getTotalCount(), page, businessId, accountName, accountLeafLocations);
		} catch(BirdeyeSocialException e1) {
			logger.error("Unable to fetch all leaf location for account: {} with error: {}", accountId, e1.getMessage());
			throw(e1);
		} catch (Exception e2) {
			logger.error("Unable to fetch all leaf location for account: {} with error: {}", accountId, e2.getMessage());
			throw new BirdeyeSocialException(ErrorCodes.ERROR_FETCHING_LEAF_LOCATIONS, e2.getMessage());
		}
	}

	@Override
	public PaginatedConnectedPages getPaginatedPages(String channel, GetPagesRequest request) {

		if ( !PageConnectionStatus.contains(request.getType())) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid value for type");
		}

		SocialReseller execute = resellerFactory.getSocialResellerChannel(channel)
				.orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

		return execute.getPages(request.getEnterpriseId(), PageConnectionStatus.valueOf(request.getType().toUpperCase()),
				request.getPage(), request.getSize(),request.getSearchStr(), request.getSearchType(), request.getSortDirection(),
				request.getSortParam(), request.getLocationIds(), request.getMapped(), request.getUserIds(), request.getLocationFilterSelected(), Constants.ENTERPRISE);
	}

	@Override
	public SocialModulePermissionStatusResponse postPermissionStatus(Long enterpriseId, List<String> modules, String channel) {
		try {
			SocialModulePermissionStatusResponse response = new SocialModulePermissionStatusResponse();
			ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));
			boolean permission = arborService.getModulePermissions(enterpriseId, modules);

			Map<String, Boolean> result = new HashMap<>();
			result.put(channel, permission);
			response.setResult(result);

			return response;
		} catch (Exception ex) {
			logger.error("Something went wrong while fetching permission module for this {} with error", enterpriseId, ex );
			throw ex;
		}
	}

	@Override
	public void restorePosts(List<Long> enterpriseIds) {
		if (CollectionUtils.isEmpty(enterpriseIds)) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid request");
		}
		enterpriseIds.forEach(enterpriseId -> {
			try {
				socialPostService.restorePosts(enterpriseId);
			} catch (Exception e) {
				logger.error("Error while restoring posts for enterpriseId: {}", enterpriseId);
			}
		});
	}

	@Override
	public BusinessUserLocationResponse getAccountLeafLocations(Integer accountId, AccountListFilter request) {
		try {
			BusinessUserLocationResponse response = businessCoreService.getUserLocationsDetailsAccounts(accountId, request);
			if(Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getAccounts())) {
				List<Integer> businessIds = response.getAccounts().stream()
						.map(BusinessUserLocationData::getBusinessId).collect(Collectors.toList());

				Map<String, Object> objectMap =	businessCoreService.getBusinessesInBulkByBusinessIds(businessIds, true);


				for(String bizId : objectMap.keySet()){
					Map<String ,Object> data = (Map<String, Object>) objectMap.get(bizId);
					Map<String ,Object> locationData = (Map<String, Object>) data.get("location");
					if(Objects.isNull(locationData)) continue;

					String address = prepareBusinessAddress(locationData);
					response.getAccounts().stream().forEach(v -> {
						if(v.getBusinessId().equals(Integer.parseInt(bizId))) {
							v.setAddress(address);
						}});
				}
			}
			if (Objects.nonNull(response)) {
				return response;
			} else {
				logger.error("Unable to fetch reseller leaf locations for: {}", accountId);
				throw new BirdeyeSocialException(ErrorCodes.ERROR_FETCHING_LEAF_LOCATIONS, "Error while fetching reseller leaf locations");
			}
		} catch (Exception e) {
			logger.error("Unable to fetch reseller leaf locations with error: {}", e.getMessage());
			throw new BirdeyeSocialException(ErrorCodes.ERROR_FETCHING_LEAF_LOCATIONS, e.getMessage());
		}
	}




	private String prepareBusinessAddress(Map<String ,Object> locationData) {
		StringBuilder address = new StringBuilder();
		if (StringUtils.isNotEmpty((String) locationData.get("address1"))) {
			address.append(locationData.get("address1")).append(", ");
		}
		if (StringUtils.isNotEmpty((String) locationData.get("address2"))) {
			address.append(locationData.get("address2")).append(", ");
		}
		if (StringUtils.isNotEmpty((String) locationData.get("city"))) {
			address.append(locationData.get("city")).append(", ");
		}
		if (StringUtils.isNotEmpty((String) locationData.get("state"))) {
			address.append(locationData.get("state")).append(" ");
		}
		// Zipcode will be always there
		if (StringUtils.isNotEmpty((String) locationData.get("zip"))) {
			address.append(locationData.get("zip"));
		}
		return address.toString();
	}
	private AccountListFilter createBusinessAccountLocationRequest(Integer userId, Integer page, Integer size,
																   String searchString, List<String> status) {
		AccountListFilter request = new AccountListFilter();
		request.setSearch(searchString);
		request.setStartIndex(page*size);
		request.setPageSize(size);
		request.setStatus(status);
		request.setUserId(userId);
		return request;
	}



	@Override
	public void checkInvalidStatePageRequest(CheckInvalidGetPageState checkInvalidGetPageState) {
		List<String> distinctMappedRequestIds = new ArrayList<>();
		String channel = checkInvalidGetPageState.getChannel();
		Set<String> requestIds = checkInvalidGetPageState.getOldRequestId();

		logger.info("Request received to check invalid state for channel {} and requestId {}", channel, requestIds);

		switch (channel.toLowerCase()) {
			case Constants.FACEBOOK:
				distinctMappedRequestIds = fbSocialAccountService.getMappedRequestIds(requestIds);
				break;

			case Constants.GMB:
				distinctMappedRequestIds = googleSocialAccountService.getMappedRequestIds(requestIds);
				break;

			case Constants.INSTAGRAM:
				distinctMappedRequestIds = socialInstagramService.getMappedRequestIds(requestIds);
				break;

			case Constants.TWITTER:
				distinctMappedRequestIds = twitterSocialPostService.getMappedRequestIds(requestIds);
				break;

			case Constants.LINKEDIN:
				distinctMappedRequestIds = socialLinkedinService.getMappedRequestIds(requestIds);
				break;

			case Constants.YOUTUBE:
				distinctMappedRequestIds = youTubeAccountService.getMappedRequestIds(requestIds);
				break;

			case Constants.TIKTOK:
				distinctMappedRequestIds = tiktokSocialAccountService.getMappedRequestIds(requestIds);
				break;

			default:
				throw new BirdeyeSocialException("Invalid channel: " + channel);
		}

		handleUnmappedRequestIds(requestIds, distinctMappedRequestIds);
	}

	/*
	 *	Handle unmapped requests
	 */
	public void handleUnmappedRequestIds(Set<String> requestIds, List<String> distinctMappedRequestIds) {
		boolean invalidateAll = CollectionUtils.isEmpty(distinctMappedRequestIds);
		List<Integer> unmappedRequestIds = new ArrayList<>();
		List<String> unmappedRequestIdsForOpenUrl = new ArrayList<>();
		for(String requestId : requestIds) { // invalidate requestIds which are not found in raw tables
			if(invalidateAll || !distinctMappedRequestIds.contains(requestId)) {
				if(StringUtils.isNumeric(requestId)) {
					unmappedRequestIds.add(Integer.parseInt(requestId));
				} else { //openUrl has id prefixed with openurl hence it is string
					unmappedRequestIdsForOpenUrl.add(requestId);
				}
			}
		}

		try {
			Future<?> cancelInvalidRequestFuture = executor.submit(() -> cancelRequestInvalidRequest(unmappedRequestIds));
			Future<?> cancelInvalidRequestForOpenUrlFuture = executor.submit(() -> cancelRequestInvalidRequestForOpenUrl(unmappedRequestIdsForOpenUrl));
			cancelInvalidRequestFuture.get();
			cancelInvalidRequestForOpenUrlFuture.get();
		} catch (Exception e) {
			logger.error("Error occured while cancelling invalid get page requests {}", e.getMessage());
			throw new BirdeyeSocialException(e.getMessage());
		}
	}


	/*
	 *	Cancel unmapped page request which has status as INITIAL or FETCHED
	 */
	public void cancelRequestInvalidRequestForOpenUrl(List<String> unmappedRequestIds) {

		List<BusinessGetPageOpenUrlRequest> pageOpenUrlRequests = businessGetPageOpenUrlReqRepo.findAll(unmappedRequestIds);

		for(BusinessGetPageOpenUrlRequest orphanRequest : pageOpenUrlRequests) {
			logger.info("No page found for request id: {}", orphanRequest.getId());
			if (Status.INITIAL.getName().equals(orphanRequest.getStatus()) || Status.FETCHED.getName().equals(orphanRequest.getStatus())) {
				logger.info("Request: {} is in state: {}, hence cancelling the request", orphanRequest.getId(), orphanRequest.getStatus());
				// TODO: Commenting for monitoring first, will enable later
				//cancelRequest(orphanRequest, true);
			}
		}
	}

	/*
	 *	Cancel unmapped page request which has status as INITIAL or FETCHED
	 */
	public void cancelRequestInvalidRequest(List<Integer> unmappedRequestIds) {

		List<BusinessGetPageRequest> pageRequests = businessGetPageReqRepo.findAll(unmappedRequestIds);

		for(BusinessGetPageRequest orphanRequest : pageRequests) {
			logger.info("No page found for request id: {}", orphanRequest.getId());
			if (Status.INITIAL.getName().equals(orphanRequest.getStatus()) || Status.FETCHED.getName().equals(orphanRequest.getStatus())) {
				logger.info("Request: {} is in state: {}, hence cancelling the request", orphanRequest.getId(), orphanRequest.getStatus());
				// TODO: Commenting for monitoring first, will enable later
				//cancelRequest(orphanRequest, true);
			}
		}
	}

	@Override
	public PaginatedConnectedPages getPaginatedPagesForEnterprise(String channel, GetPagesRequest request) {
		if ( !PageConnectionStatus.contains(request.getType()) ) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid value for type");
		}
		logger.info("Request received to get all selected pages for Enterprise id {}, channel {}, user {}, pageConnectionStatus {}", request.getEnterpriseId(), channel, request.getUserId(), request.getType());
		ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));
		return  arborService.getPagesForEnterprise(request.getEnterpriseId(), PageConnectionStatus.valueOf(request.getType().toUpperCase()), request.getPage(),
				request.getSize(),request.getSearchStr(), request.getSearchType(), request.getSortDirection(), request.getSortParam(), request.getLocationIds(), request.getMapped(), request.getUserIds(), request.getLocationFilterSelected(), Constants.ENTERPRISE);
	}

	@Override
	public ConnectedPageResponse getConnectedPages(String channel, Long enterpriseId) {
		logger.info("Request received to fetch connected pages for enterpriseId and channel {} {}",enterpriseId,channel);
		ConnectedPageResponse connectedPageResponse = new ConnectedPageResponse();
		List<ChannelAccountInfo> data = new ArrayList<>();
		BusinessGetPageRequest businessGetPageRequest = businessGetPageService.findLastRequestByEnterpriseIdAndChannel(enterpriseId, SocialChannel.getSocialChannelByName(channel).getName());
		if(Objects.isNull(businessGetPageRequest)){
			logger.info("[getConnectedPages] No request exists for business and channel {} {}",enterpriseId,channel);
			throw new BirdeyeSocialException(ErrorCodes.NO_BUSINESS_GET_PAGE_REQUEST_FOUND,"No request exists for business");
		}
		ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));
		data = arborService.getAllPages(businessGetPageRequest, enterpriseId);
		connectedPageResponse.setSameAccountConnections(
				data.stream()
						.filter(ChannelAccountInfo::getSameAccountConnections) // Check if accountConnections is true
						.collect(Collectors.toList())
		);

		connectedPageResponse.setDifferentAccountConnections(
				data.stream()
						.filter(accountInfo -> !accountInfo.getSameAccountConnections()) // Check if accountConnections is false
						.collect(Collectors.toList())
		);
		return connectedPageResponse;
	}

	public void pageRegionSync(String channel, Long enterpriseId) {
		Integer sourceId = SocialChannel.getSocialChannelByName(channel).getId();
		PostOperation postOperation = postOperationFactory
				.getPostOperationChannel(SocialChannel.getSocialChannelNameById(sourceId))
				.orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

		if (Objects.nonNull(enterpriseId)) {
			processAllPagesWithoutPagination(postOperation, enterpriseId, sourceId);
		} else {
			processAllPagesWithPagination(postOperation, sourceId);
		}
	}

	private void processAllPagesWithoutPagination(PostOperation postOperation, Long enterpriseId, Integer sourceId) {
		List<String> pageIds = postOperation.getAllPageIds(0, 0, enterpriseId);
		if (CollectionUtils.isNotEmpty(pageIds)) {
			pageIds.forEach(pageId -> sendRegionUpdateEvent(pageId, sourceId));
		}
	}

	private void processAllPagesWithPagination(PostOperation postOperation, Integer sourceId) {
		int page = 0;
		int size = 100;
		List<String> pageIds;

		do {
			pageIds = postOperation.getAllPageIds(page, size, null);
			if (CollectionUtils.isNotEmpty(pageIds)) {
				pageIds.forEach(pageId -> sendRegionUpdateEvent(pageId, sourceId));
			}
			page++;
		} while (CollectionUtils.isNotEmpty(pageIds));
	}

	@Override
	public void sendRegionUpdateEvent(String pageId, Integer sourceId) {
		PageSyncEventRequest eventRequest = new PageSyncEventRequest(pageId, region, sourceId);
		kafkaProducer.sendObjectV1US("social-region-update", eventRequest);
		logger.info("Page sync event request sent for pageId: {} and sourceId: {}", pageId, sourceId);
	}



}