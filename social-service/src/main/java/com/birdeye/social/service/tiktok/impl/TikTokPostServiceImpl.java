package com.birdeye.social.service.tiktok.impl;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessGMBLocationRawRepository;
import com.birdeye.social.dao.BusinessTiktokAccountsRepository;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessTiktokAccounts;
import com.birdeye.social.entities.BusinessTwitterAccounts;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.model.Feed;
import com.birdeye.social.model.PageDetail;
import com.birdeye.social.model.SocialTimeline;
import com.birdeye.social.model.tiktok.TikTokHashtagResponse;
import com.birdeye.social.model.tiktok.TiktokAccessTokenDataResponse;
import com.birdeye.social.service.ChannelPostService;
import com.birdeye.social.service.tiktok.SocialTiktokService;
import com.birdeye.social.service.tiktok.external.TiktokExternalService;
import com.birdeye.social.tiktok.TikTokAccountAccessInfo;
import com.birdeye.social.tiktok.TikTokFeedData;
import com.birdeye.social.utils.ConversionUtils;
import com.birdeye.social.utils.SocialProxyHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("TikTokPostServiceImpl")
public class TikTokPostServiceImpl implements ChannelPostService {

    private static final Logger log = LoggerFactory.getLogger(TikTokPostServiceImpl.class);

    @Autowired
    private BusinessGMBLocationRawRepository gmbRepo;

    @Autowired
    private BusinessTiktokAccountsRepository businessTiktokAccountsRepository;

    @Autowired
    private SocialProxyHandler proxyHandler;

    @Autowired
    private SocialTiktokService tiktokService;

    @Autowired
    private TiktokExternalService tiktokExternalService;

    @Override
    public SocialChannel channelName() {
        return SocialChannel.TIKTOK;
    }

    @Override
    public SocialTimeline getFeedData(Date lastPostDate, SocialScanEventDTO data) {
        try {
            BusinessTiktokAccounts account = businessTiktokAccountsRepository.findById(data.getChannelPrimaryId());
            if (Objects.isNull(account)) {
                log.info("No social enabled tiktok account found for channel primary id: {}", data.getChannelPrimaryId());
                return null;
            }

            log.info("[getFeedData] for tiktok, request received to scan {}", account.getProfileId());

            List<TikTokFeedData.TikTokVideo> feedData = getTikTokFeedData(account,lastPostDate);

           return ConversionUtils.convertToTimelineObject(feedData, account, lastPostDate);
        } catch (Exception ex) {
            log.info("Social feed data exception caught for data {} with error", data, ex);
            return null;
        }
    }

    private List<TikTokFeedData.TikTokVideo> getTikTokFeedData(BusinessTiktokAccounts account, Date lastVideoDate) {
        List<TikTokFeedData.TikTokVideo> tikTokVideosList = new ArrayList<>();
        if (Objects.isNull(account)) {
            return tikTokVideosList;
        }

        try {
            // Prepare TikTok account access info
            TikTokAccountAccessInfo accessInfo = new TikTokAccountAccessInfo();
            TiktokAccessTokenDataResponse tiktokAccessTokenResponse = tiktokExternalService.generateRefreshToken(account.getRefreshToken());
            accessInfo.setAccessToken(tiktokAccessTokenResponse.getAccessToken());
            accessInfo.setProfileId(tiktokAccessTokenResponse.getOpenid());

            int pageLimit = CacheManager.getInstance()
                    .getCache(SystemPropertiesCache.class)
                    .getSocialScanLimit();


            int tiktokVideoLimitForBackFill = CacheManager.getInstance()
                    .getCache(SystemPropertiesCache.class)
                    .getTiktokVideoLimitForBackfill();

            // Fetch the initial feed data
            TikTokFeedData tikTokFeeds = proxyHandler.runWithRetryableBirdeyeException(
                    () -> tiktokService.getTikTokUserVideos(accessInfo, null)
            );

            if (tikTokFeeds != null && tikTokFeeds.getData() != null && !tikTokFeeds.getData().getVideos().isEmpty()) {
                // Remove videos with missing created_time
                tikTokFeeds.getData().getVideos().removeIf(data -> data.getCreateTime() == null);
                tikTokVideosList.addAll(tikTokFeeds.getData().getVideos());

                // Parse last video date from the latest video
                Date videoDate = new Date(tikTokFeeds.getData().getVideos()
                        .get(tikTokFeeds.getData().getVideos().size() - 1).getCreateTime() * 1000L);


                boolean moreData = tikTokFeeds.getData().getHasMore();
                // Paginate while there's more data and videos are newer than the last processed date
                Long nextCursor = tikTokFeeds.getData().getCursor();
                while (tikTokFeeds != null && tikTokFeeds.getData() != null && !tikTokFeeds.getData().getVideos().isEmpty()
                        && videoDate.after(lastVideoDate)
                        && tikTokFeeds.getData().getCursor() != null
                        && tikTokVideosList.size() <= tiktokVideoLimitForBackFill
                        && moreData) {

                    Long finalNextCursor = nextCursor;
                    tikTokFeeds = proxyHandler.runWithRetryableBirdeyeException(
                            () -> tiktokService.getTikTokUserVideos(accessInfo, finalNextCursor)
                    );
                    nextCursor = tikTokFeeds.getData().getCursor();

                    if (tikTokFeeds != null && tikTokFeeds.getData() != null && !tikTokFeeds.getData().getVideos().isEmpty()) {
                        // Remove videos with missing created_time
                        tikTokFeeds.getData().getVideos().removeIf(data -> data.getCreateTime() == null);
                        videoDate = new Date(tikTokFeeds.getData().getVideos()
                                .get(tikTokFeeds.getData().getVideos().size() - 1).getCreateTime() * 1000L);
                        tikTokVideosList.addAll(tikTokFeeds.getData().getVideos());
                    }
                }
            }

            return tikTokVideosList;
        } catch (BirdeyeSocialException ex) {
            log.info("[Social Report] Could not fetch videos for TikTok account {}", account, ex);
            return null;
        } catch (Exception e) {
            log.info("[Social Report] Something went wrong while fetching videos for TikTok account {}", account, e);
            return null;
        }
    }



    @Override
    public List<Object[]> findPageIdAndEnterpriseIdbyPageIds(List<String> pageIds) {
        return businessTiktokAccountsRepository.findPageIdAndEnterpriseIdByProfileIds(pageIds);
    }

    @Override
    public List<PageDetail> getPageDetails(List<String> pageIds) {
        List<PageDetail> pageDetails = new ArrayList<>();
        List<BusinessTiktokAccounts> businessTiktokAccounts = businessTiktokAccountsRepository.findByProfileIdIn(pageIds);
        if (CollectionUtils.isNotEmpty(businessTiktokAccounts)) {
            pageDetails.addAll(businessTiktokAccounts.stream().map(s -> new PageDetail(s.getProfileId(),
                    s.getProfileName(), s.getProfileImageUrl(), s.getBusinessId())).collect(Collectors.toList()));
        }
        return pageDetails;
    }

    @Override
    public Feed getPostFeedDetails(SocialPostPublishInfo request, SocialPost socialPost) {
        Feed feed = new Feed();
        feed.setFeedId(request.getPostId());
        return feed;
    }

    @Override
    public SocialScanEventDTO prepareSocialScanEventDto(String pageId) {
        BusinessTiktokAccounts tiktokAccount = businessTiktokAccountsRepository.findByProfileId(pageId);
        SocialScanEventDTO scanEventDTO = new SocialScanEventDTO();
        scanEventDTO.setChannelPrimaryId(tiktokAccount.getId());
        scanEventDTO.setBusinessId(tiktokAccount.getBusinessId());
        scanEventDTO.setEnterpriseId(tiktokAccount.getEnterpriseId());
        scanEventDTO.setExternalId(tiktokAccount.getProfileId());
        scanEventDTO.setPageName(tiktokAccount.getProfileUsername());
        scanEventDTO.setSourceName(SocialChannel.TIKTOK.getName());
        scanEventDTO.setSourceId(SocialChannel.TIKTOK.getId());
        return scanEventDTO;
    }

    @Override
    public List<TikTokHashtagResponse.HashtagResponse> fetchRecommendedHashtags(String keyword, Integer businessId) throws Exception {
        List<BusinessTiktokAccounts> tiktokAccount = businessTiktokAccountsRepository.findByAccountIdAndBusinessIdNotNull(businessId);
        if (CollectionUtils.isEmpty(tiktokAccount)) {
            throw new Exception("No account mapped to the business");
        }
        return tiktokExternalService.fetchRecommendedHashtags(keyword, tiktokAccount.get(0));
    }
}
