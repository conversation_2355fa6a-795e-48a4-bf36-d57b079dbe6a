package com.birdeye.social.service;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.model.IgContainerCheckDTO;
import com.birdeye.social.model.MentionData;
import com.birdeye.social.model.SocialLocationsTagData;
import com.birdeye.social.model.SocialMentionsData;
import com.birdeye.social.external.request.linkedin.TargetAudienceResponse;
import com.birdeye.social.model.Youtube.YoutubeCategory.YoutubeCategory;
import com.birdeye.social.model.Youtube.YoutubePlaylist.YoutubePlaylist;
import com.birdeye.social.platform.entities.*;

import java.util.List;

public interface SocialSitePostingService {

	public void postOnSocialSites(List<SocialPostPublishInfo> postPublishInfo);

	void scheduledPostOnSocialSites(SocialPostPublishInfo postPublishInfo, List<String> failedPageIds) throws Exception;

	public List<BusinessFBPage> getValidFbPages(Business business, User user) throws Exception;

	public List<BusinessTwitterAccounts> getTwitterConfig(Business business, User user) throws Exception;

	public List<BusinessLinkedinPage> getLinkedinPages(Business business, User user) throws Exception;

	List<BusinessGoogleMyBusinessLocation> getValidGMBPages(Business business, User user) throws Exception;

  void retryProcessingPosts(List<SocialPostPublishInfo> processingSocialPosts);

	SocialMentionsData searchMentionData(String channel, String search, Integer businessId, Long enterpriseId, String nextToken);

	SocialLocationsTagData searchLocationTagData(String channel, String search, Integer businessId) ;

	List<TargetAudienceResponse> getTargetAudienceList(String pageId, String category);

    List<YoutubeCategory> getYoutubeCategories(String pageId) throws Exception;

	List<YoutubePlaylist> getPlaylistForChannel(String pageId) throws Exception;

	void deletePublishedPostForChannel(List<SocialPostPublishInfo> socialPostPublishInfo, DeletePostRequest request, Integer businessId, Integer userId,
									   Integer postId);

	void pushCheckStatusInFirebase(String requestType,String status, Integer postId, Boolean error, Integer count);

	void checkIgContainerStatus(IgContainerCheckDTO igContainerCheckDTO, boolean markFailed) throws BirdeyeSocialException;

	void updatePostOnSocialSites(SocialPostPublishInfo postPublishInfo) throws Exception;

	void processPendingPosts(List<ProcessingPost> processingPostList);

	String getProfileData(String input, Integer businessId, List<MentionData> mentionData, Integer sourceId);
}
