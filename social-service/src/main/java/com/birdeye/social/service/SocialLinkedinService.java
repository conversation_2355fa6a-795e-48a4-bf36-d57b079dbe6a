package com.birdeye.social.service;

import com.birdeye.social.constant.MappingStatus;
import com.birdeye.social.constant.PageSortDirection;
import com.birdeye.social.constant.ResellerSearchType;
import com.birdeye.social.constant.ResellerSortType;
import com.birdeye.social.dto.SocialTokenValidationDTO;
import com.birdeye.social.dto.SocialEnabledStatusDto;
import com.birdeye.social.elasticdto.SocialElasticDto;
import com.birdeye.social.entities.*;
import com.birdeye.social.linkedin.AccessTokenInfo;
import com.birdeye.social.linkedin.LinkedinContactInfo;
import com.birdeye.social.linkedin.LinkedinOrganizationPagesInfo;
import com.birdeye.social.linkedin.LinkedinUserProfileInfo;
import com.birdeye.social.linkedin.organization.LinkedInOrganizationResponse;
import com.birdeye.social.model.*;
import com.birdeye.social.model.linkedin.LinkedinAuthRequest;
import com.birdeye.social.sro.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface SocialLinkedinService {

    String getAuthorizationUrl(Integer businessId, String redirectUri, String origin) throws Exception;

    Map<String, Object> getLinkedinTokenDetails(String token);

    LinkedinUserProfileInfo getLinkedInUserProfile(String token);

    LinkedinOrganizationPagesInfo linkedinOrganizationInfo(String token);

    void cancelRequest(Long businessId, Boolean forceCancel);

    @Deprecated
    ChannelPageInfo getLinkedinIntegrationRequestInfo(Long businessId, Boolean reconnectFlag) throws Exception;

    ChannelPageInfo connectLinkedinPagesV1(ConnectPageRequest input, Integer accountId);

    void removeLinkedinLocationAccountMapping(List<LocationPageMappingRequest> locationPageMappingRequests,String type, boolean unlink);

    void removeLinkedinPages(List<LocationPageMappingRequest> input, String type);

    OpenUrlPagesInfo getPagesFetchedByOpenUrl(Long enterpriseId) throws Exception;

    OpenUrlPagesInfo connectPagesFetchedByOpenUrl(Long enterpriseId, OpenUrlConnectRequest connectRequest, Integer userId) throws Exception;

    LocationPageMapping getLocationMappingPages(Long enterpriseId, Integer userId, List <Integer> businessIds, Set<String> status,Integer page,Integer size,String search, List<String> includeModules) throws Exception;

    void submitFetchPageRequest(LinkedinAuthRequest linkedinAuthRequest,String type);

    void fetchLinkedinPages(BusinessGetPageRequest linkedinRequest, AccessTokenInfo accessTokenInfo,String type);

    void pushToKafkaForValidity(String channel, Collection<String> profileIds);

    String getProfileIdFromLinkedinPagesOfSameAccount(List<BusinessLinkedinPage> businessLinkedinPages);

    void reconnectLinkedinPagesFlow(LinkedinAuthRequest linkedinAuthRequest,String type) throws Exception;

    void reconnectPages(BusinessGetPageRequest businessGetPageRequest, LinkedinAuthRequest linkedinAuthRequest,AccessTokenInfo accessTokenInfo,String type);

    void refreshAccessToken(BusinessLinkedinPage linkedinPage);

    AccessTokenInfo generateAccessTokenFromRefreshToken(LinkedinRefreshToken linkedinRefreshToken,Long enterpriseId);

    void saveLinkedinLocationMapping(Integer locationId, String pageId, String pageType, String type, Integer userId, Long resellerId) throws Exception;

    List<SocialPageListInfo> getUnmappedLinkedinPagesByEnterpriseId(Long enterpriseId);

    void updateLinkedinAccountIsValidStatus(String profileId, Integer isValid);

    void removeInactiveIntegration(String channel, Long enterpriseId);

    ConnectedPages getPages(Integer userId,Long enterpriseId,String type);

    ConnectedPages getPagesForPostReconnect(Long enterpriseId,String type, SocialPostPageConnectRequest request);

    boolean checkIfAccountExistsByAccountId(Long accountId);

    public ChannelSetupStatus.PageSetupStatus getPageSetupStatus(Long enterpriseId);

    public ChannelSetupStatus.PageSetupStatus getPageConnectionSetupStatus(Long enterpriseId);

    LinkedinContactInfo linkedinContactDetails(String token);

    void backupLinkedinPages(SocialPagesAudit socialPage);

    void updateExisitngPage(BusinessLinkedinPage existingBusinessLinkedinPage, BusinessLinkedinPage page, LinkedinRefreshToken linkedinRefreshToken);

    List<? extends Object> fetchRawPages(List<String> integrationIds,String type);

    List<BusinessLinkedinPage> findByProfileIdInAndPageType(List<String> pageIds, String pageType);

    List<Number> fetchRawPagesId(List<String> pages, String type);

    List<SocialElasticDto> fetchRawPagesSocialEsDto(List<LinkedinConnectRequest> linkedinConnectRequests);

    List<SocialElasticDto> fetchPagesEsDto();

    List<SocialElasticDto> fetchPagesEsDto(Integer id);

    CheckStatusResponse getIntegrationRequestStatus(Long businessId, Boolean reconnectFlag);

    FetchPageResponse getIntegrationPage(Long businessId);

    Map<String, Boolean> getLinkedinPostPermissionPageWise(List<BusinessLinkedinPage> businessLinkedinPages, List<String> modules);

    void removeBusinessInactiveIntegration(String name, Integer businessId);

    boolean getLinkedinPermission(Integer accountId, List<String> modules);
    boolean getLinkedinPostPermission(List<BusinessLinkedinPage> linkedinPages, List<String> modules);

    void markLinkedinPageAsInvalid(SocialTokenValidationDTO socialTokenValidationDTO);

    void updateSubscription(SocialEnabledStatusDto socialEnabledStatusDto);

    void updatePermission();

    void updatePermissionForValidPage(SocialTokenValidationDTO socialTokenValidationDTO);

    void initiateDPSync();

    void syncLinkedinDP(DpSyncRequest linkedinDpSyncRequest);

    void removeUnmappedPages(Long sourceBusinessAccountNumber, Integer businessId);

    LinkedInOrganizationResponse linkedInOrganizationResponse(String token);

    void removePageMap(Integer businessId);

    void moveLinkedinAccountLocation(Long sourceBusinessAccountNumber, Long targetBusinessAccountNumber, Integer sourceBusinessId, boolean b,Integer accountId);

    void removeLinkedinPagesByPagesIds(List<String> pagesIds);

	Map<String, Object> getPaginatedPages(BusinessGetPageRequest businessGetPageRequest, Integer page, Integer size,
			String search);
    void removeLinkedinPagesByPagesIdsWithLimit(List<String> pageIds, Integer limit);

    PaginatedConnectedPages getResellerPages(Long resellerId, PageConnectionStatus pageConnectionStatus, Integer page, Integer size, String search,
                                             ResellerSearchType searchType, PageSortDirection sortDirection,
                                             ResellerSortType sortParam, List<Integer> locationIds, MappingStatus mappingStatus, List<Integer> userIds,
                                             Boolean locationFilterSelected);

    BusinessLinkedinPage findByProfileId(String pageId);

    void validityCheckForLinkedin(Collection<String> linkedinProfileIds);

    void updateLinkedinValidityType(BusinessLinkedinPage page);

    void updateTable(Integer id, Integer isValid, String scope, String moduleImpacted);

    List<Integer> getMappedResellerLeafLocations(List<Integer> resellerLeafLocationIds);

    List<String> getMappedRequestIds(Set<String> requestIds);

    Validity fetchValidityAndErrorMessage(BusinessLinkedinPage page);

    List<ChannelAccountInfo> getLinkedinPages(BusinessGetPageRequest req, Long enterpriseId);

}
