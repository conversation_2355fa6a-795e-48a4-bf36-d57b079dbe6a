package com.birdeye.social.service.SocialReportService.ExternalService;

import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.exception.ExternalAPIErrorCode;
import com.birdeye.social.external.exception.ExternalAPIException;
import com.birdeye.social.facebook.response.FacebookErrorResponse;
import com.birdeye.social.facebook.response.FacebookPostResponse;
import com.birdeye.social.insights.Facebook.ExternalAPIResponse.*;
import com.birdeye.social.insights.Facebook.FacebookInsightRequest;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Objects;

@Service
public class FacebookExternalServiceImpl implements FacebookExternalService {

    @Autowired
    @Qualifier("socialRestTemplate")
    private RestTemplate socialRestTemplate;

    private static final Logger logger = LoggerFactory.getLogger(FacebookExternalServiceImpl.class);
    @Override
    public List<Data> getFacebookPageInsights(FacebookInsightRequest facebookPageData) {
        if (Objects.isNull(facebookPageData) || StringUtils.isEmpty(facebookPageData.getAccessToken())) {
            return null;
        }
        String url = StringUtils.format(GET_PAGE_INSIGHTS,facebookPageData.getPageId());
        ResponseEntity<FacebookResponse> response;
        MultiValueMap<String, String> queryParam = new LinkedMultiValueMap<>();
        queryParam.add("access_token",facebookPageData.getAccessToken());
        queryParam.add("metric",facebookPageData.getMetric());
        queryParam.add("period",facebookPageData.getPeriod());
        queryParam.add("since",String.valueOf(facebookPageData.getSince()));
        queryParam.add("until",String.valueOf(facebookPageData.getUntil()));

        url = UriComponentsBuilder.fromHttpUrl(url).queryParams(queryParam).build().encode().toUriString();

        try {
            logger.info("url to fetch getFacebookPageInsights {}",url);
            response = socialRestTemplate.exchange(url, HttpMethod.GET,null,FacebookResponse.class);
            if (response.getBody().getData() != null ) {
                return response.getBody().getData();
            }
        }
        catch (HttpStatusCodeException e) {
            //LOGGER.error("HttpStatusCodeException while calling facebook debug token API for URL {} :: {}", url, e.getResponseBodyAsString());
            FacebookPostResponse facebookPostResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
            FacebookErrorResponse errorResponse = facebookPostResponse != null ? facebookPostResponse.getError() : new FacebookErrorResponse();

            if (e.getStatusCode().is5xxServerError()) {
                //logger.error("InternalServerException while calling GMB message send for URL {} and exception {}", url, e.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, errorResponse.getMessage());
            }
            if(e.getStatusCode().is4xxClientError()){
                //logger.error("Exception while calling GMB message send for URL {} and exception {}", url, e.getResponseBodyAsString());
                throw new BirdeyeSocialException(e.getStatusCode().value(),errorResponse.getMessage());
            }
        }
        catch (Exception e) {
            throw new BirdeyeSocialException(ErrorCodes.UNKNOWN_ERROR_OCCURRED_PAGE_INSIGHTS, ErrorCodes.UNKNOWN_ERROR_OCCURRED_PAGE_INSIGHTS.name(), e);
        }
        return null;
    }

    @Override
    public List<Data> getTotalPostCount(String pageId,String accessToken,Integer count) {
        if (Objects.isNull(pageId) || StringUtils.isEmpty(accessToken)) {
            return null;
        }
        String url = StringUtils.format(GET_PAGE_POST_COUNT,pageId);
        ResponseEntity<FacebookResponse> response;
        MultiValueMap<String, String> queryParam = new LinkedMultiValueMap<>();
        queryParam.add("access_token",accessToken);
        queryParam.add("limit",String.valueOf(count));
        queryParam.add("field","id,created_time");
        url = UriComponentsBuilder.fromHttpUrl(url).queryParams(queryParam).build().encode().toUriString();
        try {
            logger.info("url to fetch getTotalPostCount {}",url);
            response = socialRestTemplate.exchange(url, HttpMethod.GET,null,FacebookResponse.class);
            if (response.getBody() != null && response.getBody() !=null && response.getBody().getData()!=null) {
                return response.getBody().getData();
            }
        }
        catch (HttpStatusCodeException e) {
            logger.error("HttpStatusCodeException while calling facebook API for URL {} :: {}", url, e.getResponseBodyAsString());
            FacebookPostResponse facebookPostResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
            FacebookErrorResponse errorResponse = facebookPostResponse != null ? facebookPostResponse.getError() : new FacebookErrorResponse();

            if (e.getStatusCode().is5xxServerError()) {
                logger.error("InternalServerException while calling facebook URL {} and exception {}", url, e.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, errorResponse.getMessage());
            }
            if(e.getStatusCode().is4xxClientError()){
                logger.error("Exception while calling facebook  URL {} and exception {}", url, e.getResponseBodyAsString());
                throw new BirdeyeSocialException(e.getStatusCode().value(),errorResponse.getError_user_msg());
            }
        }
        catch (Exception e) {
            throw new BirdeyeSocialException(ErrorCodes.UNKNOWN_ERROR_OCCURRED_PAGE_INSIGHTS, ErrorCodes.UNKNOWN_ERROR_OCCURRED_PAGE_INSIGHTS.name(), e);
        }
        return null;
    }

    @Override
    public String getPromotableId(String postId, String accessToken, String externalPageId) {
        if (StringUtils.isEmpty(externalPageId) || StringUtils.isEmpty(accessToken) || StringUtils.isEmpty(postId)) {
            return null;
        }
        String url = StringUtils.format(GET_PAGE_POST,externalPageId+"_"+postId);
        ResponseEntity<PromotableData> response;
        MultiValueMap<String, String> queryParam = new LinkedMultiValueMap<>();
        queryParam.add("access_token",accessToken);
        queryParam.add("fields","promotable_id");
        url = UriComponentsBuilder.fromHttpUrl(url).queryParams(queryParam).build().encode().toUriString();
        try {
            logger.info("url to fetch getPromotableId {}",url);
            response = socialRestTemplate.exchange(url, HttpMethod.GET,null,PromotableData.class);
            if (response.getBody() != null && response.getBody() !=null && response.getBody().getPromotableId()!=null) {
                return response.getBody().getPromotableId();
            }
        }
        catch (HttpStatusCodeException e) {
            logger.error("HttpStatusCodeException while calling facebook API for URL {} :: {}", url, e.getResponseBodyAsString());
            FacebookPostResponse facebookPostResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
            FacebookErrorResponse errorResponse = facebookPostResponse != null ? facebookPostResponse.getError() : new FacebookErrorResponse();

            if (e.getStatusCode().is5xxServerError()) {
                logger.error("InternalServerException while calling facebook URL {} and exception {}", url, e.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, errorResponse.getMessage());
            }
            if(e.getStatusCode().is4xxClientError()){
                logger.error("Exception while calling facebook  URL {} and exception {}", url, e.getResponseBodyAsString());
                throw new BirdeyeSocialException(e.getStatusCode().value(),errorResponse.getError_user_msg());
            }
        }
        catch (Exception e) {
            throw new BirdeyeSocialException(ErrorCodes.UNKNOWN_ERROR_OCCURRED_PAGE_INSIGHTS, ErrorCodes.UNKNOWN_ERROR_OCCURRED_PAGE_INSIGHTS.name(), e);
        }
        return null;
    }
}
