package com.birdeye.social.service;

import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.BusinessGetPageOpenUrlRequest;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.external.request.facebook.FbProfileInfoRequest;
import com.birdeye.social.facebook.FbPageInfo;
import com.birdeye.social.model.*;
import com.birdeye.social.platform.dao.BusinessFacebookPageRepository.BFP;
import com.birdeye.social.platform.entities.Business;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface FacebookPageService {

	void getPageChildLocation(Map<String, FbPageInfo> pages, Map<String, String> pageToPageURL, FbUserProfileInfo user, String baseUrl, String extendedToken);

	void getAccessTokenAndSaveToDB(List<FbPageInfo> pages, FbUserProfileInfo user, String baseUrl, String extendedToken, Boolean updateScope);

	public String getFBAccessToken(String pageId, String baseUrl, String extendedToken) throws Exception;

	void saveFBPageforMigratedFlow(FbPageInfo pages, String userId, UserProfileInfo user, String baseUrl, String extendedToken, Business business, String permToken);

	public Integer getBusinessSelectedPagesCount(Long enterpriseId);

	void updateFacebookPageInvalidStatus(String fbPageId, Integer inValidStatus);

	void updateFacebookPageIsSelectedStatus(String fbPageId, Integer isSelected);

	void fetchPages(BusinessGetPageRequest request, String extendedToken, FbUserProfileInfo user, Business business, String type, Long parentId, Boolean isSystemUser) throws BirdeyeSocialException, Exception;

	void fetchPagesForOpenUrl(BusinessGetPageOpenUrlRequest request, String extendedToken, FbUserProfileInfo user, Business business, String firebaseKey) throws BirdeyeSocialException, Exception;

	public void checkInvalidPages(Integer accountId);

	public Map<String, FbPageInfo> getPageListMap(List<FbPage> pagesList, Map<String, String> pageToPageURL);

	public boolean checkFacebookPageValidity(BFP page);

	void getFbPagesAndReconnect(Business business, List<String> fbPageIds, Integer userId, String extendedToken, String baseUrl, FbUserProfileInfo user, BusinessGetPageRequest request) throws Exception;

	void getFbPagesAndReconnectForReseller(Business business, List<String> fbPageIds, Integer userId, String extendedToken, String baseUrl, FbUserProfileInfo user, BusinessGetPageRequest request) throws Exception;

	public SocialAppCredsInfo getSocialAppCredsForBusiness();

	public SocialFBPageRepository.IntegrationSummary getInValidAndTotalCount(List<Integer> businessIdList);

	BusinessFBPage updateFacebookRole(BusinessFBPage oldpage);

	BusinessFBPage updateFacebookRoleNew(BusinessFBPage oldpage, Map<String, FbPageInfo> pages);

	void performPostMappingAction(BusinessFBPage fbPage) throws Exception;

	void performPostMappingAction(String facebookPageId);

	Map<String, FbPageInfo> getPageDetailsMapV1(String extendedToken, String userId, String channel);

	String extractSingleLineAddressFromLocation(FacebookPageInfo facebookPageInfo);

	FbUserProfileInfo getPagePhoneNumber(String extendedToken);

	void manualFetch(Integer bgpId);

	void manualReconnect(Integer bgpId);

	List<FbPage> getPageDetailsListV1(String accessToken, String userId, String channel);

	void updateAccessToken(Long enterpriseId, Integer lastReconnectReqId);

	void addPageDetails(FBConnectManualRequest request);

	void updateEligiblePages(Integer count, Date currentDate, Date maxDate);

	void reconnectFBValidPages(AccessTokenUpdateRequest accessTokenUpdateRequest);

	void updateFacebookGranularScope(GranularScopeUpdateRequest request);

	FbPublicProfileInfo facebookProfileInfo(FbProfileInfoRequest fbProfileInfoRequest);

	FbPublicProfileInfo facebookProfileDetails(FbProfileInfoRequest fbProfileInfoRequest);

	String getPPCASystemUserToken();

}
