package com.birdeye.social.service;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessLinkedinPageRepository;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessLinkedinPage;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.external.request.linkedin.MediaContent;
import com.birdeye.social.external.request.linkedin.SharePostRequestLinkedin;
import com.birdeye.social.external.request.linkedin.UploadMediaResponse;
import com.birdeye.social.linkedin.LinkedinService;
import com.birdeye.social.linkedin.response.LinkedInAPIResponse;
import com.birdeye.social.linkedin.response.LinkedInElement;
import com.birdeye.social.model.Feed;
import com.birdeye.social.model.PageDetail;
import com.birdeye.social.model.SocialTimeline;
import com.birdeye.social.model.tiktok.TikTokHashtagResponse;
import com.birdeye.social.utils.ConversionUtils;
import com.birdeye.social.utils.SocialProxyHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class LinkedinPostServiceImpl implements ChannelPostService{

    @Autowired
    private BusinessLinkedinPageRepository businessLinkedinPageRepo;

    @Autowired
    private SocialPostLinkedinService socialPostLinkedinService;

    @Autowired
    private LinkedinService linkedinService;

    @Autowired
    private SocialProxyHandler socialProxyHandler;

    private static final Logger LOG = LoggerFactory.getLogger(LinkedinPostServiceImpl.class);

    @Override
    public SocialChannel channelName() {
        return SocialChannel.LINKEDIN;
    }

    @Override
    public SocialTimeline getFeedData(Date lastPostDate, SocialScanEventDTO data) {
        try {
            BusinessLinkedinPage linkedinPage = businessLinkedinPageRepo.findById(data.getChannelPrimaryId());
            LOG.info("[Social Report] request received to scan {}", linkedinPage.getProfileId());
            LinkedInAPIResponse linkedinPosts = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(
                    () -> socialPostLinkedinService.getLinkedInPostsForReporting(linkedinPage.getBusinessId(), lastPostDate)
            );
            if(Objects.isNull(linkedinPosts)) {
                LOG.info("Could not fetch data for event {}", data);
                return null;
            }
            return convertToSocialTimeline(linkedinPage, lastPostDate, linkedinPosts);
        } catch (Exception ex) {
            LOG.info("Social feed data exception caught for data {} with error {}", data, ex);
            return null;
        }
    }

    @Override
    public List<Object[]> findPageIdAndEnterpriseIdbyPageIds(List<String> profileIds) {
        return businessLinkedinPageRepo.findPageIdAndEnterpriseIdbyProfileIds(profileIds);
    }

    public SocialTimeline convertToSocialTimeline(BusinessLinkedinPage linkedinPage, Date lastScanDate,  LinkedInAPIResponse feedData) {

        SocialTimeline socialTimeline = new SocialTimeline();
        List<Feed> feeds = new ArrayList<>();

        if(CollectionUtils.isEmpty(feedData.getElements())){
            LOG.info("No data found for to convert to social timeline");
            return socialTimeline;
        }

        // fetch social metadata for each post
        for (LinkedInElement element: feedData.getElements()) {
            Feed feed = ConversionUtils.convertLinkedinPostToFeed(linkedinPage, lastScanDate, element);

            // fetching video and image urls using UGCPost projection
            if (Objects.nonNull(element.getContent()))
            {
                fetchAndSetVideoUrls(linkedinPage, element, feed);
            }

            feeds.add(feed);
        }

        socialTimeline.setChannel(SocialChannel.LINKEDIN.getName());
        socialTimeline.setBusinessId(linkedinPage.getBusinessId());
        socialTimeline.setPageId(linkedinPage.getId());
        socialTimeline.setFeeds(feeds);

        return socialTimeline;
    }

    /**
     * Video urls are given by linkedin only if one ugcPost is fetched at a time
     * Therefore, fetching only those posts that can video to get video url
     *
     * @param linkedinPage
     * @param element
     * @param feed
     */
    private void fetchAndSetVideoUrls(BusinessLinkedinPage linkedinPage, LinkedInElement element, Feed feed) {
        try {
            SharePostRequestLinkedin videoResponse = linkedinService.fetchUGCPostByUrn(linkedinPage.getAccessToken(), element.getId());
            if (Objects.nonNull(videoResponse) && Objects.nonNull(videoResponse.getContent())
                    && Objects.nonNull(videoResponse.getContent().getMedia()) && videoResponse.getContent().getMedia().getId().contains("video")) {
                List<String> videoUrls = new ArrayList<>();
                UploadMediaResponse uploadMediaResponse = linkedinService.getLinkedinMediaUrl(videoResponse.getContent().getMedia().getId(), linkedinPage.getAccessToken(), true);
                if (Objects.nonNull(uploadMediaResponse)) {
                    videoUrls.add(uploadMediaResponse.getDownloadUrl());
                    feed.setVideos(videoUrls);
                }

            }
            else if(Objects.nonNull(videoResponse) && Objects.nonNull(videoResponse.getContent())
                    && Objects.nonNull(videoResponse.getContent().getMedia()) && videoResponse.getContent().getMedia().getId().contains("image"))
            {
                List<String> imageUrl = new ArrayList<>();
                UploadMediaResponse uploadMediaResponse = linkedinService.getLinkedinMediaUrl(videoResponse.getContent().getMedia().getId(), linkedinPage.getAccessToken(), false);
                if (Objects.nonNull(uploadMediaResponse)) {
                    imageUrl.add(uploadMediaResponse.getDownloadUrl());
                    feed.setImages(imageUrl);
                }
            }
            else if(Objects.nonNull(videoResponse) && Objects.nonNull(videoResponse.getContent())
                    && Objects.nonNull(videoResponse.getContent().getMultiImage()) && Objects.nonNull(videoResponse.getContent().getMultiImage().getImages())) {
                List<String> multiImageUrl = new ArrayList<>();
                for(MediaContent img:videoResponse.getContent().getMultiImage().getImages()){
                    UploadMediaResponse uploadMediaResponse = linkedinService.getLinkedinMediaUrl(img.getId(), linkedinPage.getAccessToken(), false);
                    if (Objects.nonNull(uploadMediaResponse))
                        multiImageUrl.add(uploadMediaResponse.getDownloadUrl());
                }
                if (CollectionUtils.isNotEmpty(multiImageUrl)) {
                    feed.setImages(multiImageUrl);
                }

            }
             if(Objects.nonNull(videoResponse) && Objects.nonNull(videoResponse.getContent())
                    && Objects.nonNull(videoResponse.getContent().getArticle()) && Objects.nonNull(videoResponse.getContent().getArticle().getThumbnail()) && videoResponse.getContent().getArticle().getThumbnail().contains("image")) {
                List<String> imageUrl = new ArrayList<>();
                UploadMediaResponse uploadMediaResponse = linkedinService.getLinkedinMediaUrl(videoResponse.getContent().getArticle().getThumbnail(), linkedinPage.getAccessToken(), false);
                if (Objects.nonNull(uploadMediaResponse)) {
                    imageUrl.add(uploadMediaResponse.getDownloadUrl());
                    feed.setImages(imageUrl);
                }

            }
        } catch(Exception e) {
            LOG.info("Exception occurred which fetching video urls for linkedin post {} : {}", element.getId(), e);
        }
    }

    @Override
    public List<PageDetail> getPageDetails(List<String> pageIds) {
        List<PageDetail> pageDetails = new ArrayList<>();
        List<BusinessLinkedinPage> linkedinPages = businessLinkedinPageRepo.findByProfileIdIn(pageIds);
        if (CollectionUtils.isNotEmpty(linkedinPages)) {
            for (BusinessLinkedinPage businessLinkedinPage : linkedinPages) {
                String pageName = null;
                if (businessLinkedinPage.getPageType().equalsIgnoreCase("profile")) {
                    String firstName = businessLinkedinPage.getFirstName();
                    if (StringUtils.isNotEmpty(firstName)) {
                        String lastName = businessLinkedinPage.getLastName();
                        if (StringUtils.isEmpty(lastName)) {
                            pageName = firstName;
                        } else {
                            pageName = firstName + " " + lastName;
                        }
                    }
                } else if (businessLinkedinPage.getPageType().equalsIgnoreCase("company")) {
                    pageName = businessLinkedinPage.getCompanyName();
                }
                pageDetails.add(new PageDetail(businessLinkedinPage.getProfileId(), pageName,
                        businessLinkedinPage.getLogoUrl(), businessLinkedinPage.getBusinessId()));
            }
        }
        return pageDetails;
    }

    @Override
    public Feed getPostFeedDetails(SocialPostPublishInfo request, SocialPost socialPost) {
        Feed feed = new Feed();
        feed.setFeedId(request.getPostId());
        feed.setDateModified(request.getPublishDate().toString());
        return feed;
    }

    @Override
    public SocialScanEventDTO prepareSocialScanEventDto(String pageId) {
        BusinessLinkedinPage linkedInPage = businessLinkedinPageRepo.findByProfileIdAndIsValid(pageId, 1);
        SocialScanEventDTO scanEventDTO = new SocialScanEventDTO();
        scanEventDTO.setChannelPrimaryId(linkedInPage.getId());
        scanEventDTO.setBusinessId(linkedInPage.getBusinessId());
        scanEventDTO.setEnterpriseId(linkedInPage.getEnterpriseId());
        scanEventDTO.setExternalId(linkedInPage.getProfileId());
        if("company".equalsIgnoreCase(linkedInPage.getPageType())){
            scanEventDTO.setPageName(linkedInPage.getCompanyName());
        } else {
            String fullName = null;
            if(StringUtils.isNotBlank(linkedInPage.getFirstName()) && StringUtils.isNotBlank(linkedInPage.getLastName())){
                fullName = linkedInPage.getFirstName() + " "+ linkedInPage.getLastName() ;
            } else {
                fullName =linkedInPage.getFirstName();
            }
            scanEventDTO.setPageName(fullName);
        }
        scanEventDTO.setSourceName(SocialChannel.LINKEDIN.getName());
        scanEventDTO.setSourceId(SocialChannel.LINKEDIN.getId());
        return scanEventDTO;
    }

    @Override
    public List<TikTokHashtagResponse.HashtagResponse> fetchRecommendedHashtags(String keyword, Integer businessId) throws Exception {
        return Collections.emptyList();
    }
}
