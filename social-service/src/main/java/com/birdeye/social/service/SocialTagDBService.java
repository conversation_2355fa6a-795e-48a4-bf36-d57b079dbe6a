package com.birdeye.social.service;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialTagEntityType;
import com.birdeye.social.dao.SocialTagRepository;
import com.birdeye.social.dto.SocialTagMappingInfo;
import com.birdeye.social.entities.SocialTag;
import com.birdeye.social.entities.SocialTagMapping;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 21/12/23
 */
@Service
public class SocialTagDBService {

    @Autowired
    private SocialTagRepoService socialTagRepoService;

    @Autowired
    private SocialTagMappingRepoService socialTagMappingRepoService;

    public static final int DB_FETCH_BATCH_SIZE = 2000;

    @Cacheable(value = Constants.ACCOUNT_ALL_SOCIAL_TAGS_CACHE, key = "#accountId.toString()", unless = "#result==null OR #result.isEmpty()")
    public List<SocialTag> findAllSocialTagsByAccountId(Integer accountId) {
        int page = 0;
        int batchSize = DB_FETCH_BATCH_SIZE;
        boolean hasMore;
        List<SocialTag> allSocialTags = new LinkedList<>();
        do {
            List<SocialTag> batchedSocialTags =
                    socialTagRepoService.findByAccountId(accountId, new PageRequest(page, batchSize));
            allSocialTags.addAll(batchedSocialTags);
            page++;
            hasMore = hasMore(batchedSocialTags, batchSize);
        } while (hasMore);
        return allSocialTags;
    }
    @CacheEvict(value = Constants.ACCOUNT_ALL_SOCIAL_TAGS_CACHE, key = "#accountId.toString()")
    public void clearSocialTagsCacheForAccount(Integer accountId) {
        
    }
    public Map<String, Long> getAllLowerCaseSocialTagNameToTagIdMap(Integer accountId) {
        List<SocialTag> allSocialTags = findAllSocialTagsByAccountId(accountId);
        return allSocialTags.stream().collect(Collectors.toMap(tag -> StringUtils.lowerCase(tag.getName()),
                tag -> tag.getId(), (first, second) -> second));
    }

    public Map<Long, SocialTag> getTagIdToSocialTagMap(Integer accountId) {
        List<SocialTag> allSocialTags = findAllSocialTagsByAccountId(accountId);
        return allSocialTags.stream().collect(Collectors.toMap(SocialTag::getId, Function.identity(), (first, second) -> second));
    }

    public List<SocialTag> saveAllAndFlushTags(List<SocialTag> socialTags) {
        if (CollectionUtils.isEmpty(socialTags)) {
            return Collections.emptyList();
        }
        return socialTagRepoService.saveAllAndFlush(socialTags);
    }

    public void deleteSocialTagsAndMappingsByAccountIdAndTagIds(Integer accountId, Collection<Long> tagIds) {
        if (CollectionUtils.isNotEmpty(tagIds)) {
            // Delete the social tag mapping as well for these tags thus deleted
            deleteSocialTagMappingByTagIds(tagIds);
            // Delete the social tags
            deleteBusinessTagsByAccountIdAndTagIds(accountId, tagIds);
        }
    }

    public void deleteBusinessTagsByAccountIdAndTagIds(Integer accountId, Collection<Long> tagIds) {
        socialTagRepoService.deleteByAccountIdAndIdIn(accountId, tagIds);
    }

    public List<SocialTagRepository.TagBasicDetails> findByTagIdInAndAccountId(Set<Long> tagIds, Integer accountId) {
        return socialTagRepoService.findByTagIdInAndAccountId(tagIds, accountId);
    }


    public void deleteSocialTagMappingByTagIds(Collection<Long> tagIds) {
        socialTagMappingRepoService.deleteByTagIds(tagIds);
    }

    public List<SocialTagMappingInfo> findDistinctEntityIdAndEntityTypeMappedToTagIds(Collection<Long> tagIds) {
        if (CollectionUtils.isNotEmpty(tagIds)) {
            return socialTagMappingRepoService.findDistinctEntityIdAndEntityTypeMappedToTagIds(tagIds);
        }
        return Collections.emptyList();
    }

    public List<SocialTagMappingInfo> findTagMappingInfoByEntityIdsAndEntityType(Collection<Long> entityIds, SocialTagEntityType entityType) {
        if (CollectionUtils.isEmpty(entityIds)) {
            return Collections.emptyList();
        }
        return socialTagMappingRepoService.findTagMappingInfoByEntityIdsAndEntityType(entityIds, entityType);
    }
    public List<SocialTagMappingInfo> findTagMappingInfoByEntityIdsAndEntityType(Collection<Long> entityIds, List<SocialTagEntityType> entityType) {
        if (CollectionUtils.isEmpty(entityIds)) {
            return Collections.emptyList();
        }
        return socialTagMappingRepoService.findTagMappingInfoByEntityIdsAndEntityType(entityIds, entityType);
    }

    public List<Long> findEntityIdByEntityTypeAndTagIdIn(Collection<Long> tagIds, SocialTagEntityType entityType) {
        if (CollectionUtils.isNotEmpty(tagIds)) {
            return socialTagMappingRepoService.findEntityIdByEntityTypeAndTagIdIn(tagIds, entityType);
        }
        return Collections.emptyList();
    }

    public List<Long> findEntityIdByEntityTypeAndAccountId(Integer accountId, SocialTagEntityType entityType) {
        return socialTagMappingRepoService.findEntityIdByEntityTypeAndAccountId(accountId, entityType);
    }

    public List<SocialTagMapping> saveAllAndFlushSocialTagMappings(List<SocialTagMapping> socialTagMappings) {
        return socialTagMappingRepoService.saveAllAndFlush(socialTagMappings);
    }

    public void deleteByEntityIdsEntityTypeAndTagIds(Collection<Long> entityIds, SocialTagEntityType entityType, Collection<Long> tagIds) {
        socialTagMappingRepoService.deleteByEntityIdsEntityTypeAndTagIds(entityIds, entityType, tagIds);
    }

    public void deleteByEntityIdsEntityTypeAndAccountId(Collection<Long> entityIds, SocialTagEntityType entityType, Integer accountId) {
        socialTagMappingRepoService.deleteByEntityIdsEntityTypeAndAccountId(entityIds, entityType, accountId);
    }

    private boolean hasMore(Collection<?> collection, int batchSize) {
        return CollectionUtils.size(collection) >= batchSize;
    }
}