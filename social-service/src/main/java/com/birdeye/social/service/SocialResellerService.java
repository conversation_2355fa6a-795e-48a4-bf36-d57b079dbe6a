package com.birdeye.social.service;

import com.birdeye.social.constant.MappingStatus;
import com.birdeye.social.constant.PageSortDirection;
import com.birdeye.social.constant.ResellerSearchType;
import com.birdeye.social.constant.ResellerSortType;
import com.birdeye.social.dto.GMBAccountDTO;
import com.birdeye.social.dto.ResellerLeafLocationRequest;
import com.birdeye.social.dto.ResellerLeafLocationResponse;
import com.birdeye.social.dto.ValidityRequestDTO;
import com.birdeye.social.model.ChannelAuthRequest;
import com.birdeye.social.model.ChannelConnectedPageInfo;
import com.birdeye.social.model.ConnectPagesResponse;
import com.birdeye.social.sro.*;
import com.birdeye.social.sro.socialReseller.*;

import java.util.List;
import java.util.Map;

public interface SocialResellerService {

    void cancelRequest(String channel, Long resellerId, Boolean forceCancel);

    void initiatePageRequest(String channel, ChannelAuthRequest authRequest,String type) throws Exception;

    void fetchAccountsForReseller(ChannelAuthRequest request,String type);

    void reconnetPages(String channel, Long resellerId, ChannelAllPageReconnectRequest input, Integer userId,String type) throws Exception;

    GmbAccountInfo getGmbAccount(Long resellerId,String type);

    void gmbPageFetchByAccount(GMBAccountDTO gmbAccountDTO, Long resellerId,String type);

    FetchPaginatedPageResponseForReseller getIntegrationPage(String channel,Integer page,Integer size,Long resellerId,String search);

    void refreshGmbResellerAccount(String userEmail, Long resellerId, String reseller);

    ChannelPageInfo connectPagesForReseller(String channel, Long resellerId, Map<String, List<String>> inputs,Boolean selectAll, String searchStr)throws Exception;

    PaginatedConnectedPages getPaginatedPages(String channel, Long enterpriseId, Integer userId, String type, Integer page,
                                              Integer size, String search, ResellerSearchType searchType,
                                              PageSortDirection sortDirection, ResellerSortType sortParam, List<Integer> locationIds,
                                              MappingStatus mappingStatus, List<Integer> userIds, Boolean locationFilterSelected);

    void removePage(String channel, List<LocationPageMappingRequest> input, Long resellerId, Integer userId);

    void fetchGMBLocationsForReconnect(GMBAccountSyncRequest gmbAccountSyncRequest);

    CheckStatusResponse getIntegrationRequestStatus(String channel, Long resellerId, Boolean reconnectFlag);

    ChannelConnectedPageInfo checkForConnectedPages(Long accountId, String channel);

    Object search(SearchDTO request, String channel);

    ConnectPagesResponse getConnectPagesForReseller(String channel, Long resellerId, Integer size, Integer page);

    public void gmbBackStatus(Long businessId,String type);

    void processResellerPage(SocialResellerBulkImportDTO data, String channel);

    SocialResellerReportUploadDTO processMappingIntegrationReport(SocialResellerDoupRequestDTO data, Integer size, Integer page);

    void validityCheck(ValidityRequestDTO request);

    void testnifi(Map<String, ?> request);

    void findEnterpriseWithNoReseller();

    void checkValidityForAllPages();

    SocialResellerReportUploadDTO processLocationMappingIntegrationReport(SocialResellerDoupRequestDTO data, Integer size, Integer page, Integer userId);

    Map<String, Object> saveLocationPageMapping(String channel, Integer locationId, String pageId, String pageType, Integer userId, Boolean force, Long resellerId) throws Exception;

	  void removePageMappings(String channel, List<LocationPageMappingRequest> input) throws Exception;

    ResellerLeafLocationResponse getAllLocations(ResellerLeafLocationRequest request, String channel, Integer accountId, Integer userId, Integer page, Integer size) throws Exception;

}
