package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.*;
import com.birdeye.social.dao.aipost.BusinessAiCustomizationRepo;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.SocialTagMappingInfo;
import com.birdeye.social.entities.*;
import com.birdeye.social.entities.aipost.BusinessAiCustomization;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.es.SocialPostCalendarMessage;
import com.birdeye.social.model.es.SocialPostsESSyncRequest;
import com.birdeye.social.service.calendar.SocialPostCalendarESService;
import com.birdeye.social.sro.SocialTagBasicDetail;
import com.birdeye.social.sro.applePost.ApplePublishStateEnum;
import com.birdeye.social.sro.applePost.AppleShowcaseStatusEnum;
import com.birdeye.social.utils.DateTimeUtils;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.TimeZoneUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.birdeye.social.constant.Constants.GOOGLE_OFFER;
import static com.birdeye.social.utils.ConversionUtils.convertToGMBOfferResponse;

@Service
public class SocialPostCalendarServiceImpl implements SocialPostCalendarService{
    private static final Logger LOGGER = LoggerFactory.getLogger(SocialPostCalendarService.class);

    private static final String SCHEDULE_DATE_FORMAT = "MM/dd/yyyy HH:mm:ss";

    private static final String SQL_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private SocialBusinessService socialBusinessService;
    @Autowired
    private SocialPostScheduleInfoRepo socialPostScheduleInfoRepository;
    @Autowired
    private SocialPostRepository socialPostRepo;

    @Autowired
    private SocialPostCalendarESService socialPostCalendarESService;

    @Autowired
    private SocialTagDBService socialTagDBService;

    @Autowired
    private PostActivityRepo postActivityRepo;

    @Autowired
    private SocialPostInfoRepository socialPostPublishInfoRepository;

    @Autowired
    private SocialPostService socialPostService;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private PostAssetService postAssetService;

    @Autowired
    private ApplePublishInfoMetadataRepository applePublishInfoMetadataRepository;

    @Autowired
    private SocialPostAiService postAiService;

    @Autowired
    private SocialTagService socialTagService;
    @Autowired
    private ISocialModulePermissionService socialModulePermissionService;
    @Autowired
    public BusinessAiCustomizationRepo aiCustomizationRepo;
    @Autowired
    public SocialAiPostHelperService socialAiPostHelperService;
    private static final String ES_NAME_GROUPS = "groups";
    private static final String ES_NAME_BUSINESS_LOCATIONS = "business_locations";
    private static final String ES_NAME_BULK_RESELLER_POSTING = "bulk_reseller_testing";

    @Autowired
    @Qualifier(Constants.SOCIAL_TASK_EXECUTOR)
    private ThreadPoolTaskExecutor executor;

    @Override
    public SocialSchedulePostResponse getAllPosts(GlobalFilterCriteriaSchedulePostMessage filter, Integer userId, boolean isListViewRequest, String source,String timezone) throws Exception {
        SocialSchedulePostResponse response;
        try {
            if(StringUtils.isBlank(source) || !StringUtils.equalsIgnoreCase(source, "dashboard")) {
                response = getAllScheduledPostsV2(filter, userId, isListViewRequest, source);
            }else
                response = getAllESScheduledPosts(filter, userId, isListViewRequest, source, timezone);
        } catch (Exception e) {
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, "internal server error");
        }

        return response;
    }

    private void shortenAiPostsResponse(Map<String, List<SocialSchedulePostMessage>> posts, Map<String, List<SocialSchedulePostMessage>> aiPosts) throws ParseException {
        if(MapUtils.isEmpty(posts) || MapUtils.isEmpty(aiPosts)) return;

        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");

        // Parse dates and find max and min
        List<Date> dates = aiPosts.keySet().stream()
                .map(dateStr -> {
                    try {
                        return sdf.parse(dateStr);
                    } catch (Exception e) {
                        throw new RuntimeException("Invalid date: " + dateStr);
                    }
                })
                .collect(Collectors.toList());

        Date minDate = Collections.min(dates);
        Date maxDate = Collections.max(dates);

        for(String dateKey: aiPosts.keySet()) {
            Date date = sdf.parse(dateKey);
            if(date.before(minDate) || date.after(maxDate)) aiPosts.remove(dateKey);
        }
    }

    /**
     * This is  new API implemented to fetch posts for calendar data from ES
     * @param filter
     * @return
     */
    @Override
    public SocialSchedulePostResponse getAllScheduledPostsForResellerV2(GlobalFilterCriteriaSchedulePostMessage filter) {
        try {
            if(!socialPostCalendarESService.isGetFlag(filter.getBusinessId())) {
                return socialPostService.getAllScheduledPostsForReseller(filter);
            }
            long startTime = System.currentTimeMillis();
            LOGGER.info("V2 getAllScheduledPosts : {}", filter);
            if(filter.isBusinessIdsSelected() && CollectionUtils.isEmpty(filter.getBusinessIds()))
                return  new SocialSchedulePostResponse();
            Map<Integer, SocialPost> socialPostMap = new HashMap<>();
            Map<Integer, List<SocialPostInfoRepository.PostPageStatus>> postPageStatusMap = new HashMap<>();
            Date startDate = new SimpleDateFormat(SCHEDULE_DATE_FORMAT).parse(filter.getStartDate());
            Date endDate = new SimpleDateFormat(SCHEDULE_DATE_FORMAT).parse(filter.getEndDate());
            List<Integer> sourceIds = new ArrayList<>();
            List<Integer> publishState = new ArrayList<>();
            List<String> approvalState = new ArrayList<>();
            Map<String, Boolean> pagePermissionMap = new HashMap<>();
            addFilterDetails(filter, publishState, sourceIds, approvalState);
            Map<Integer, List<String>> hasAccessMap = new HashMap<>();
//			List<Integer> allSourceIds = getAllSourceIdList();
            //remove below line when tiktok support is added at reseller level
//            sourceIds.remove(new Integer(SocialChannel.TIKTOK.getId()));
            if(CollectionUtils.isEmpty(filter.getAccessibleLocationIds())){
                LOGGER.info("Accessible Location Ids are empty for business ID : {}",filter.getBusinessId());
                throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Accessible Location Ids are empty");
            }
            LOGGER.info("*********V2** Finding results for Accessible Loc IDs {}", filter.getAccessibleLocationIds().toArray());
            List<Integer> allEnterpriseIdsToCheckForPosts = socialBusinessService.getAllEnterpriseAndSmbForLocationIds(filter.getAccessibleLocationIds());
            LOGGER.info("*********V2****Finding results for enterprises {}", allEnterpriseIdsToCheckForPosts.toArray());
            List<SocialPostCalendarMessage> socialPostScheduleInfoListAll;
            LOGGER.info("Reseller V2 Get data from ES for business ID : {}",filter.getBusinessId());
            List<SocialPostCalendarMessage> socialSchedulePostMessageESList = socialPostCalendarESService.searchFromEsIndex(allEnterpriseIdsToCheckForPosts, startDate, endDate, sourceIds, publishState, filter.getTagIds());
            Map<String, Integer> pageIdVsLocationIdMap = getPageIdVsLocationIdMapV1(socialSchedulePostMessageESList, pagePermissionMap);
            long checkpoint1Time = System.currentTimeMillis();
            LOGGER.info("checkpoint 1==============, time taken {} ms", checkpoint1Time - startTime);

            // fetch only location level details
            List<SocialPostCalendarMessage> finalSocialPostScheduleInfoListAll = socialSchedulePostMessageESList;
            List<SocialPostCalendarMessage> finalSocialPostScheduleInfoList = socialSchedulePostMessageESList;
            CompletableFuture<List<SocialPostCalendarMessage>> socialPostScheduleInfoListFAll = CompletableFuture.supplyAsync(() ->
                    getSocialPostSchInfoWithLocAllV3(filter.getAccessibleLocationIds(), finalSocialPostScheduleInfoListAll, hasAccessMap, pageIdVsLocationIdMap));
			CompletableFuture<List<SocialPostCalendarMessage>> socialPostScheduleInfoListF = CompletableFuture.supplyAsync(() ->
                    getSocialPostSchInfoWithLocV3(finalSocialPostScheduleInfoList, filter.getBusinessIds()));

            CompletableFuture<Void> executor1 = CompletableFuture.allOf( socialPostScheduleInfoListFAll, socialPostScheduleInfoListF);
            executor1.get(1000, TimeUnit.SECONDS);
            BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(filter.getBusinessId(), false);//getBusinessDetails.get();
            List<SocialPostCalendarMessage> socialSchedulePostMessageESListAll = socialPostScheduleInfoListFAll.get();
            LOGGER.info("socialSchedulePostMessageESListAll: {} , finalSocialPostScheduleInfoListAll: {}",socialSchedulePostMessageESListAll.size(), finalSocialPostScheduleInfoListAll.size());
            socialSchedulePostMessageESList = socialPostScheduleInfoListF.get();
            LOGGER.info("Before loop 2============== {}", new Date(System. currentTimeMillis()));

            long checkpoint2Time = System.currentTimeMillis();
            LOGGER.info("checkpoint 2==============, time taken {} ms", checkpoint2Time - startTime);
            socialSchedulePostMessageESList = socialSchedulePostMessageESList.stream().distinct().filter(socialSchedulePostMessageESListAll::contains).collect(Collectors.toList());
            Set<Long> distinctPostIdsLong = new HashSet<>();
            Set<Integer> distinctPostIds = new HashSet<>();
            for (SocialPostCalendarMessage socialPostCalendarMessage : socialSchedulePostMessageESList) {
                // this for loop finds distinct postIds
                Integer postId = socialPostCalendarMessage.getId();
                if (Objects.isNull(postId)) {
                    continue;
                }
                distinctPostIdsLong.add(Long.valueOf(postId));
                distinctPostIds.add(postId);
            }
            long checkpoint3Time = System.currentTimeMillis();
            LOGGER.info("checkpoint 3==============, time taken {} ms", checkpoint3Time - startTime);

            List<PostActivityRepo.ActivityCount> postVsDuplicateCount = new ArrayList<>();
            List<SocialPost> socialPosts = socialPostRepo.findByIdIn(distinctPostIds);

            CompletableFuture<Map<Integer, SocialPostsAssets>> getPostsAssetsDetail = CompletableFuture.supplyAsync(() -> postAssetService.getPostAssetsForList(socialPosts));
            CompletableFuture<Void> parallelExecuter = CompletableFuture.allOf( getPostsAssetsDetail);

            parallelExecuter.get(100, TimeUnit.SECONDS);
            Map<Integer, SocialPostsAssets> postAssetsMap = getPostsAssetsDetail.get();

            long checkpoint4Time = System.currentTimeMillis();
            LOGGER.info("checkpoint 4==============, time taken {} ms", checkpoint4Time - startTime);

            List<SocialPostInfoRepository.PostPageStatus> postPageStatusList = socialPostPublishInfoRepository.findBySocialPostIdInAndSourceIdIn(distinctPostIds, sourceIds);
            if(CollectionUtils.isNotEmpty(postPageStatusList)){
                postPageStatusMap = postPageStatusList.stream().collect(Collectors.groupingBy(
                        SocialPostInfoRepository.PostPageStatus::getSocialPostId,
                        Collectors.mapping(obj -> obj, Collectors.toList())));
            }

            long checkpoint5Time = System.currentTimeMillis();
            LOGGER.info("checkpoint 5==============, time taken {} ms", checkpoint5Time - startTime);

            Map<String, List<SocialSchedulePostMessage>> postData = new HashMap<>();
            List<SocialSchedulePostMessage> socialSchedulePostMessageList = new ArrayList<>();
            Set<Integer> addedPostSet = new HashSet<>();
            for(SocialPostCalendarMessage socialPostCalendarMessage : socialSchedulePostMessageESList.stream().
                    filter(post -> distinctPostIds.contains(post.getId())).collect(Collectors.toList())) {
                if(addedPostSet.contains(socialPostCalendarMessage.getId())) {
                    continue;
                }
                SocialSchedulePostMessage socialSchedulePostMessage = getResellerSocialSchedulePostMessage(filter, socialPostMap,postPageStatusMap, pagePermissionMap, hasAccessMap,
                        businessLiteDTO, postAssetsMap, new HashMap<>(), socialPostCalendarMessage, startTime);
                if (socialSchedulePostMessage == null) continue;
                addedPostSet.add(socialSchedulePostMessage.getId());

                socialSchedulePostMessageList.add(socialSchedulePostMessage);
            }
            long checkpoint6Time = System.currentTimeMillis();
            LOGGER.info("checkpoint 6==============, time taken {} ms", checkpoint6Time - startTime);

            SocialSchedulePostResponse socialSchedulePostResponse = new SocialSchedulePostResponse();
            socialSchedulePostResponse.setPosts(socialSchedulePostMessageList.stream().collect(Collectors.groupingBy(
                    map -> new SimpleDateFormat("MM/dd/yyyy").format(map.getDatePublish()),
                    Collectors.collectingAndThen(
                            Collectors.toList(),
                            list -> list.stream()
                                    .sorted(Comparator.comparing(SocialSchedulePostMessage::getDatePublish))
                                    .collect(Collectors.toList())
                    ))));
            return socialSchedulePostResponse;

        } catch (Exception e) {
            LOGGER.info("Social Post: something went wrong while fetching data for filter {} e {}", filter, e);
            throw new BirdeyeSocialException(e.getMessage());
        }
    }

    @Override
    public void saveSuspendedDeletedPost(SocialPostEsSyncRequest socialPostsESSyncRequest) throws Exception {
        socialPostCalendarESService.saveSuspendedDeletedPost(socialPostsESSyncRequest);
    }

    private SocialSchedulePostMessage getResellerSocialSchedulePostMessage(GlobalFilterCriteriaSchedulePostMessage filter, Map<Integer, SocialPost> socialPostMap, Map<Integer, List<SocialPostInfoRepository.PostPageStatus>> postPageStatusMap, Map<String, Boolean> pagePermissionMap, Map<Integer, List<String>> hasAccessMap, BusinessLiteDTO businessLiteDTO, Map<Integer, SocialPostsAssets> postAssetsMap, Map<Integer, Integer> postVsDuplicateCountMap, SocialPostCalendarMessage socialPostCalendarMessage, long startTime) throws Exception{
        SocialSchedulePostMessage socialSchedulePostMessage = new SocialSchedulePostMessage();
        boolean loggerFlag = false;
        if(Objects.nonNull(socialPostCalendarMessage) && Objects.nonNull(socialPostCalendarMessage.getId()) && socialPostCalendarMessage.getId()%100==0) {
            loggerFlag = true;
        }
        long checkpoint1 = System.currentTimeMillis();
        if(loggerFlag) {
            LOGGER.info("[temp log] checkpoint 1 for postId: {}, time: {}", socialPostCalendarMessage.getId(), checkpoint1 - startTime);
        }
//        String pstDate = new SimpleDateFormat(SCHEDULE_DATE_FORMAT).format(socialPostCalendarMessage.getPublishDate());
//        Date utcDate = socialPostService.convertScheduleDateToTimezone(pstDate);
        Date utcDate = socialPostCalendarMessage.getPublishDate();
        long checkpoint1_5 = System.currentTimeMillis();
        if(loggerFlag) {
            LOGGER.info("[temp log] checkpoint 1.5 for postId: {}, time: {}", socialPostCalendarMessage.getId(), checkpoint1_5 - startTime);
        }
        String utcFormat = new SimpleDateFormat(SCHEDULE_DATE_FORMAT).format(utcDate);
        socialSchedulePostMessage.setId(socialPostCalendarMessage.getId());
        socialSchedulePostMessage.setDatePublish(socialPostCalendarMessage.getPublishDate());
        socialSchedulePostMessage.setScheduleInfoId(socialPostCalendarMessage.getScheduleInfoId());
        socialSchedulePostMessage.setPublishDate(utcFormat);
        socialSchedulePostMessage.setPublishedBy(null);
        socialSchedulePostMessage.setIsPublished(socialPostCalendarMessage.getIsPublished());
        socialSchedulePostMessage.setTags(socialPostCalendarMessage.getTags());
        socialSchedulePostMessage.setPostingSites(socialPostCalendarMessage.getPostingSites());
        List<String> socialChannelEnabled = getEnabledSocialChannelList(socialPostCalendarMessage.getSourceId());
        if (socialSchedulePostMessage.getIsPublished() == 0) {
            socialSchedulePostMessage.setPermissionStatus(getPermissionFromMap(pagePermissionMap, socialPostCalendarMessage.getSourceId(), socialChannelEnabled, socialPostCalendarMessage.getPageIds()));
        }
        long checkpoint2 = System.currentTimeMillis();
        if(loggerFlag) {
            LOGGER.info("[temp log] checkpoint 2 for postId: {}, time: {}", socialPostCalendarMessage.getId(), checkpoint2-startTime);
        }
        if (socialSchedulePostMessage.getIsPublished() == 1) {
            Map<String, Object> output = getPostFailedFlagV2(socialSchedulePostMessage, postPageStatusMap );

            socialSchedulePostMessage.setHasPostFailed((Boolean) output.get("isFailed"));
            if (socialSchedulePostMessage.getHasPostFailed()) {
                socialSchedulePostMessage.setFailedPageCount((Integer) output.get("failedPageCount"));
                socialSchedulePostMessage.setFailedChannelCount((Integer) output.get("failedChannelCount"));
                socialSchedulePostMessage.setFailedSites((List<String>) output.get("failedChannelList"));
            }
            if (!(Boolean) output.get("isVisible")) {
                return null;
            }
        }
        long checkpoint3 = System.currentTimeMillis();
        if(loggerFlag) {
            LOGGER.info("[temp log] checkpoint 3 for postId: {}, time: {}", socialPostCalendarMessage.getId(), checkpoint3-startTime);
        }
        Integer enterpriseId;
        if (Objects.nonNull(socialPostCalendarMessage.getMediaSequence())) {
            socialSchedulePostMessage.setMediaSequence(socialPostCalendarMessage.getMediaSequence());
        } else if (StringUtils.isNotEmpty(socialPostCalendarMessage.getPostMethod())) {
            LOGGER.info("Fetching media seq using Post meta data");
            enterpriseId = getBusinessIdWherePostWasScheduledV3(socialPostCalendarMessage.getPostMethod(), businessLiteDTO);
            socialSchedulePostMessage.setMediaSequence(socialPostService.getMediaSequence(socialPostCalendarMessage, enterpriseId));
        }
        long checkpoint4 = System.currentTimeMillis();
        if(loggerFlag) {
            LOGGER.info("[temp log] checkpoint 4 for postId: {}, time: {}", socialPostCalendarMessage.getId(), checkpoint4-startTime);
        }
        socialSchedulePostMessage.setPostText(socialPostCalendarMessage.getPostText());
        socialSchedulePostMessage.setAiPost(socialPostCalendarMessage.getAiPost());
        if (CollectionUtils.isNotEmpty(socialPostCalendarMessage.getImageIds())) {
            List<SocialPostsAssets> imageAssets = postAssetService.getPostsAssetsById(postAssetsMap, socialPostCalendarMessage.getImageIds());
            socialSchedulePostMessage.setImages(postAssetService.getMediaDataV2(imageAssets, businessLiteDTO.getBusinessNumber(), Constants.IMAGE));
        }
        if (CollectionUtils.isNotEmpty(socialPostCalendarMessage.getCompressedImages())) {
            List<SocialPostsAssets> compressedImageAssets = postAssetService.getPostsAssetsById(postAssetsMap, socialPostCalendarMessage.getCompressedImages());
            socialSchedulePostMessage.setCompressedImages(postAssetService.getMediaRequestV2(compressedImageAssets, businessLiteDTO.getBusinessNumber(), Constants.IMAGE));
        }
        if (CollectionUtils.isNotEmpty(socialPostCalendarMessage.getVideoIds())) {
            List<SocialPostsAssets> videoAssets = postAssetService.getPostsAssetsById(postAssetsMap, socialPostCalendarMessage.getVideoIds());
            PostAssetsData postAssetsData = postAssetService.setSocialPostsAssetsDataV2(videoAssets, businessLiteDTO.getBusinessNumber(), Constants.VIDEO);
            socialSchedulePostMessage.setVideos(postAssetsData.getVideos());
            socialSchedulePostMessage.setVideoThumbnails(postAssetsData.getVideoThumbnailUrls());
        }
        if (CollectionUtils.isNotEmpty(socialPostCalendarMessage.getMentions())) {
            socialSchedulePostMessage.setMentions(socialPostCalendarMessage.getMentions());
        }
        if (StringUtils.isNotEmpty(socialPostCalendarMessage.getLinkPreviewUrl())) {
            socialSchedulePostMessage.setLinkPreviewUrl(socialPostCalendarMessage.getLinkPreviewUrl());
        }
//        if (MapUtils.isNotEmpty(postVsDuplicateCountMap) && postVsDuplicateCountMap.containsKey(socialPostCalendarMessage.getId())) {
//            socialSchedulePostMessage.setDuplicatedCount(postVsDuplicateCountMap.get(socialPostCalendarMessage.getId()));
//        } else {
//            socialSchedulePostMessage.setDuplicatedCount(0);
//        }
        if (
                (StringUtils.isNotEmpty(socialPostCalendarMessage.getQuotedPostIdNew())|| Objects.nonNull(socialPostCalendarMessage.getQuotedPostId()) )
                        && StringUtils.isNotEmpty(socialPostCalendarMessage.getQuotedPostUrl())) {
            socialSchedulePostMessage.setIsQuotedTweet(true);
        }
        long checkpoint5 = System.currentTimeMillis();
        if(loggerFlag) {
            LOGGER.info("[temp log] checkpoint 5 for postId: {}, time: {}", socialPostCalendarMessage.getId(), checkpoint5-startTime);
        }
                        // Reel/Story condition
        if (Objects.nonNull(socialPostCalendarMessage.getPostMetaData())
                && StringUtils.isNotEmpty(socialPostCalendarMessage.getPostMetaData().getIgPostMetadata())) {
//            SocialPostSchedulerMetadata metadata = JSONUtils.fromJSON(socialPostCalendarMessage.getPostMetaData(), SocialPostSchedulerMetadata.class);
//            if (Objects.nonNull(metadata) && StringUtils.isNotEmpty(metadata.getIgPostMetadata())) {
                IgPostMetadata igPostMetadata = JSONUtils.fromJSON(socialPostCalendarMessage.getPostMetaData().getIgPostMetadata(), IgPostMetadata.class);
                socialSchedulePostMessage.setType(Objects.nonNull(igPostMetadata)?igPostMetadata.getType():null);
                if(StringUtils.isNotEmpty(socialSchedulePostMessage.getType()) && socialSchedulePostMessage.getType().equalsIgnoreCase("story")
                        && socialSchedulePostMessage.getIsPublished()==1) {
                    //story expiry check
                    socialSchedulePostMessage.setIsExpired(isStoryExpired(socialSchedulePostMessage.getDatePublish()));
                }
        }

        if (Objects.nonNull(socialPostCalendarMessage.getPostMetaData())
                && StringUtils.isNotEmpty(socialPostCalendarMessage.getPostMetaData().getFbPostMetadata())) {
//            SocialPostSchedulerMetadata metadata = JSONUtils.fromJSON(socialPostCalendarMessage.getPostMetaData(), SocialPostSchedulerMetadata.class);
//            if (Objects.nonNull(metadata) && StringUtils.isNotEmpty(metadata.getIgPostMetadata())) {
            FbPostMetadata fbPostMetadata = JSONUtils.fromJSON(socialPostCalendarMessage.getPostMetaData().getFbPostMetadata(), FbPostMetadata.class);
            socialSchedulePostMessage.setType(Objects.nonNull(fbPostMetadata)?fbPostMetadata.getType():null);
            if(StringUtils.isNotEmpty(socialSchedulePostMessage.getType()) && socialSchedulePostMessage.getType().equalsIgnoreCase(FacebookPostType.STORY.getName())
                    && socialSchedulePostMessage.getIsPublished()==1) {
                //story expiry check
                socialSchedulePostMessage.setIsExpired(isStoryExpired(socialSchedulePostMessage.getDatePublish()));
            }
        }
        if (CollectionUtils.isNotEmpty(hasAccessMap.get(socialSchedulePostMessage.getId()))) {
            socialSchedulePostMessage.setHasAccess(false);
            socialSchedulePostMessage.setIncompleteChannel(hasAccessMap.get(socialSchedulePostMessage.getId()));
        }
        long checkpoint6 = System.currentTimeMillis();
        if(loggerFlag) {
            LOGGER.info("[temp log] checkpoint 6 for postId: {}, time: {}", socialPostCalendarMessage.getId(), checkpoint6-startTime);
        }

        // show only failed posts if filter= failed is applied
       if (filter.getPostStatus().equalsIgnoreCase(SocialPostStatusEnum.FAILED.getName()) && !socialSchedulePostMessage.getHasPostFailed()) {
           return null;
        }
        return socialSchedulePostMessage;
    }

    @Override
    public SocialSchedulePostResponse getAllScheduledPostsV2 (GlobalFilterCriteriaSchedulePostMessage filter, Integer userId, boolean isListViewRequest, String source) throws Exception {
        try {
            if(CollectionUtils.isNotEmpty(filter.getPostType()) && filter.getPostType().size()==1 && filter.getPostType().contains(FilterPostType.AI_SUGGESTED_POSTS)){
                return new SocialSchedulePostResponse();
            }
            long startTime = System.currentTimeMillis();
            LOGGER.info("getAllScheduledPosts : {}", filter);
            Map<Integer, SocialPost> socialPostMap;

            Date startDate = new SimpleDateFormat(SCHEDULE_DATE_FORMAT).parse(filter.getStartDate());
            Date endDate = new SimpleDateFormat(SCHEDULE_DATE_FORMAT).parse(filter.getEndDate());

            List<Integer> sourceIds = new ArrayList<>();
            List<Integer> publishState = new ArrayList<>();
            List<String> approvalState = new ArrayList<>();
            Map<Integer, List<SocialPostInfoRepository.PostPageStatus>> postPageStatusMap = new HashMap<>();
            Map<String, Boolean> pagePermissionMap = new HashMap<>();
            addFilterDetails(filter, publishState, sourceIds,approvalState);
            Map<Integer, List<String>> hasAccessMap = new HashMap<>();

            List<Integer> allSourceIds = socialPostService.getAllSourceIdList();
            List<SocialPostScheduleInfo> socialPostScheduleInfoListAll;

            if (socialTagService.isUntaggedRequest(filter.getTagIds())) {
                Set<Long> tagIdsWithoutUntagged = filter.getTagIds().stream().filter(s -> !s.equals(-1l)).collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(tagIdsWithoutUntagged)) {
                    socialPostScheduleInfoListAll = socialPostScheduleInfoRepository
                            .findBySocialPostIdsInAndEnterpriseAndDateAndUntagged(filter.getBusinessId(), startDate, endDate, publishState, allSourceIds);
                } else {
                    socialPostScheduleInfoListAll = socialPostScheduleInfoRepository
                            .findBySocialPostIdsInAndEnterpriseAndDateAndUntaggedAndTagIds(filter.getBusinessId(), startDate, endDate, publishState, allSourceIds, tagIdsWithoutUntagged);
                }
            } else {
                socialPostScheduleInfoListAll = CollectionUtils.isNotEmpty(filter.getTagIds())
                        ? socialPostScheduleInfoRepository.findBySocialPostIdsInAndEnterpriseAndDateAndTagIdsIn(filter.getBusinessId(), startDate, endDate, publishState, allSourceIds, filter.getTagIds())
                        : socialPostScheduleInfoRepository.findByEnterpriseAndDate(filter.getBusinessId(), startDate, endDate, publishState, allSourceIds);
            }
            List<SocialPostScheduleInfo> socialPostScheduleInfoList = areListsSame(sourceIds, allSourceIds)
                    ? socialPostScheduleInfoListAll
                    : socialPostScheduleInfoListAll.stream()
                    .filter(socialPostScheduleInfo -> sourceIds.contains(socialPostScheduleInfo.getSourceId()))
                    .collect(Collectors.toList());

            List<SocialPostScheduleInfo> socialPostScheduleInfoListForApple = new ArrayList<>();
            if(sourceIds.contains(SocialChannel.APPLE_CONNECT.getId())) {
                socialPostScheduleInfoListForApple =
                        socialPostScheduleInfoRepository.findByEnterpriseAndSourceIdForApple(filter.getBusinessId(),startDate,
                                DateTimeUtils.addTimeInMinutes(startDate,30*24*60), publishState,
                                Collections.singletonList(SocialChannel.APPLE_CONNECT.getId()));

            }
            if(CollectionUtils.isNotEmpty(socialPostScheduleInfoListForApple)) {
                socialPostScheduleInfoList.addAll(socialPostScheduleInfoListForApple);
                socialPostScheduleInfoListAll.addAll(socialPostScheduleInfoListForApple);
            }
            Map<String, Integer> pageIdVsLocationIdMap = getPageIdVsLocationIdMap(socialPostScheduleInfoListAll, pagePermissionMap);
            long checkpoint1Time = System.currentTimeMillis();
            LOGGER.info("checkpoint 1==============, time taken {} ms", checkpoint1Time - startTime);

            List<SocialPostScheduleInfo> finalSocialPostScheduleInfoListAll = socialPostScheduleInfoListAll;
            List<SocialPostScheduleInfo> finalSocialPostScheduleInfoList = socialPostScheduleInfoList;
            CompletableFuture<List<SocialPostScheduleInfo>> socialPostScheduleInfoListFAll = CompletableFuture.supplyAsync(() -> getSocialPostSchInfoWithLocAll(filter.getAccessibleLocationIds(), finalSocialPostScheduleInfoListAll, hasAccessMap, pageIdVsLocationIdMap));

            CompletableFuture<List<SocialPostScheduleInfo>> socialPostScheduleInfoListF = CompletableFuture.supplyAsync(() ->
                    getSocialPostSchInfoWithLoc(finalSocialPostScheduleInfoList, filter.getBusinessIds()));

            CompletableFuture<Void> executor1 = CompletableFuture.allOf( socialPostScheduleInfoListFAll,socialPostScheduleInfoListF);
            executor1.get(1000, TimeUnit.SECONDS);
            BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(filter.getBusinessId(), false);//getBusinessDetails.get();
            socialPostScheduleInfoListAll = socialPostScheduleInfoListFAll.get();
            socialPostScheduleInfoList = socialPostScheduleInfoListF.get();

            LOGGER.info("Before loop 2============== {}", new Date(System. currentTimeMillis()));

            long checkpoint2Time = System.currentTimeMillis();
            LOGGER.info("checkpoint 2==============, time taken {} ms", checkpoint2Time - checkpoint1Time);

            socialPostScheduleInfoList = socialPostScheduleInfoList.stream().distinct().filter(socialPostScheduleInfoListAll::contains).collect(Collectors.toList());

            Set<Integer> distinctPostIds = new HashSet<>();
            Set<Long> distinctPostIdsLong = new HashSet<>();

            for (SocialPostScheduleInfo scheduleInfo : socialPostScheduleInfoList) {
                // this for loop finds distinct postIds
                Integer postId = scheduleInfo.getSocialPostId();

                if (Objects.isNull(postId)) {
                    continue;
                }

                distinctPostIdsLong.add(Long.valueOf(postId));
                distinctPostIds.add(postId);
            }

            Future<Map<Long, List<SocialTagBasicDetail>>> tagMappingInfoMapFuture =
                    executor.submit(() -> socialTagService.getEntityIdToBasicTagDetailListMap(distinctPostIdsLong, SocialTagEntityType.POST));
            List<SocialPost> socialPostList = socialPostService.getSocialPost(distinctPostIds,approvalState,filter.getApprovals(),filter.getCreators(), filter.getPostType(), filter.getPostContent());
            Future<Map<Integer, BusinessCoreUser>> getUserDetailFuture = executor.submit(() -> {
                if(CollectionUtils.isNotEmpty(socialPostList)) {
                    return businessCoreService.getBusinessUserForUserId(socialPostList.stream().map(SocialPost::getCreatedBy).collect(Collectors.toList()));
                } else {
                    return new HashMap<>();
                }
            });

            if(CollectionUtils.isEmpty(socialPostList)){
                new SocialSchedulePostResponse();
            }

            CompletableFuture<Map<Integer, SocialPostsAssets>> getPostsAssetsDetail = CompletableFuture.supplyAsync(() -> postAssetService.getPostAssetsForList(socialPostList));
            CompletableFuture<Void> parallelExecuter = CompletableFuture.allOf( getPostsAssetsDetail);

            parallelExecuter.get(100, TimeUnit.SECONDS);
            Map<Integer, SocialPostsAssets> postAssetsMap = getPostsAssetsDetail.get();


            LOGGER.info("Before loop 3============== {}", new Date(System.currentTimeMillis()));
            LOGGER.info("before socialPostScheduleInfoList {}", socialPostScheduleInfoList);


            socialPostMap = socialPostService.generateMapForIdAndSocialPost(socialPostList);
            distinctPostIds.retainAll(socialPostMap.keySet());
            List<PostActivityRepo.ActivityCount> postVsDuplicateCount = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(distinctPostIds)) {
                postVsDuplicateCount = postActivityRepo.getPostWiseActivityCount(distinctPostIds, PostActivityType.DUPLICATE.getName());

            }

            List<SocialPostInfoRepository.PostPageStatus> postPageStatusList = socialPostPublishInfoRepository.findBySocialPostIdInAndSourceIdIn(distinctPostIds, sourceIds);
            if(CollectionUtils.isNotEmpty(postPageStatusList)){
                postPageStatusMap = postPageStatusList.stream().collect(Collectors.groupingBy(
                        SocialPostInfoRepository.PostPageStatus::getSocialPostId,
                        Collectors.mapping(obj -> obj, Collectors.toList())));
            }
            Map<Integer, Integer> postVsDuplicateCountMap = postVsDuplicateCount.stream()
                    .collect(Collectors.toMap(PostActivityRepo.ActivityCount::getPostId, PostActivityRepo.ActivityCount::getActivityCount));
            long checkpoint5Time = System.currentTimeMillis();
            LOGGER.info("checkpoint 5==============, time taken {} ms", checkpoint5Time );
            //Map<String, List<SocialSchedulePostMessage>> postData = new HashMap<>();
            List<SocialSchedulePostMessage> socialSchedulePostMessageList = new ArrayList<>();
            Map<Integer, BusinessCoreUser> userDetailMap = getUserDetailFuture.get();
            Map<Long, List<SocialTagBasicDetail>> tagMappingInfoMap = tagMappingInfoMapFuture.get();
            for(SocialPostScheduleInfo socialPostScheduled : socialPostScheduleInfoList.stream().
                    filter(post -> distinctPostIds.contains(post.getSocialPostId())).collect(Collectors.toList())) {
                SocialSchedulePostMessage socialSchedulePostMessage = getSocialSchedulePostMessage(filter, userId, socialPostMap, userDetailMap,postPageStatusMap, pagePermissionMap, hasAccessMap,
                        businessLiteDTO, postAssetsMap, postVsDuplicateCountMap, socialPostScheduled, tagMappingInfoMap, source);
                if (socialSchedulePostMessage == null) continue;

                socialSchedulePostMessageList.add(socialSchedulePostMessage);

            }

            long checkpoint6Time = System.currentTimeMillis();
            LOGGER.info("checkpoint 6==============, time taken {} ms", checkpoint6Time - checkpoint5Time);

            return createPaginationForListView(socialSchedulePostMessageList, filter.getPageNo(), filter.getPageSize(), filter.getOrder(), isListViewRequest);

        } catch (Exception e) {
            LOGGER.info("Social Post: something went wrong while fetching data for filter {} e {}", filter, e);
            throw new BirdeyeSocialException(e.getMessage());
        }
    }

    private boolean areListsSame(List<Integer> list1, List<Integer> list2) {
        list1 = Objects.isNull(list1) ? Collections.emptyList() : list1;
        list2 = Objects.isNull(list2) ? Collections.emptyList() : list2;

        if (list1.size() != list2.size()) {
            return false;
        }
        int listSize = list1.size();

        Set<Integer> cumulativeList = new HashSet<>();
        cumulativeList.addAll(list1);
        cumulativeList.addAll(list2);

        return cumulativeList.size() == listSize;
    }

    private Map<String, Integer> getPageIdVsLocationIdMap(List<SocialPostScheduleInfo> data, Map<String, Boolean> pagePermissionMap) {
        Map<String, Integer> responseMap = new HashMap<>();
        Map<Integer, List<String>> sourceIdvsPageIdsMap = new HashMap<>();
        for(SocialPostScheduleInfo info: data) {
            if(Objects.isNull(info) || CollectionUtils.isEmpty(info.getPageIds())) continue;
            if(sourceIdvsPageIdsMap.containsKey(info.getSourceId())) {
                sourceIdvsPageIdsMap.get(info.getSourceId()).addAll(info.getPageIds());
            } else {
                List<String> pageIds = new ArrayList<>();
                pageIds.addAll(info.getPageIds());
                sourceIdvsPageIdsMap.put(info.getSourceId(), pageIds);
            }
        }
        List<LocationPagePair> locationPagePairList = new ArrayList<>();
        for(Map.Entry<Integer, List<String>> entry: sourceIdvsPageIdsMap.entrySet()) {
            locationPagePairList.addAll(socialPostService.getBusinessIdPageIdPairFromPageId(entry.getKey(), entry.getValue(), pagePermissionMap));
        }

        if(CollectionUtils.isNotEmpty(locationPagePairList)) {
            responseMap = locationPagePairList.stream().filter(s->(Objects.nonNull(s.getLocationId()) && StringUtils.isNotEmpty(s.getPageId())))
                    .collect(Collectors.toMap(s->s.getPageId(), s->s.getLocationId()));
        }
        return responseMap;
    }

    private List<SocialPostScheduleInfo> getSocialPostSchInfoWithLocAll(List<Integer> locationIds, List<SocialPostScheduleInfo> socialPostScheduleInfoListAll, Map<Integer, List<String>> hasAccessMap, Map<String, Integer> pageIdVsLocationIdMap) {
        return getLocationAccessInfo(locationIds, socialPostScheduleInfoListAll, hasAccessMap, pageIdVsLocationIdMap);
    }

    private List<SocialPostScheduleInfo> getLocationAccessInfo(List < Integer > accessibleLocations,
                                                               List < SocialPostScheduleInfo > socialPostScheduleInfoList,
                                                               Map < Integer, List < String >> hasAccessMap,  Map<String, Integer> pageIdVsLocationIdMap){

        if (CollectionUtils.isNotEmpty(accessibleLocations)) {
            Set<Integer> accessibleLocationsSet = new HashSet<>(accessibleLocations);
            List<SocialPostScheduleInfo> locationsScheduleInfo = new ArrayList<>();
            for (SocialPostScheduleInfo scheduleInfo : socialPostScheduleInfoList) {
                List<Integer> locations = new ArrayList<>();
                if (CollectionUtils.isEmpty(scheduleInfo.getPageIds())) {
                    locations = socialPostService.fetchlocationsByScheduleCondition(scheduleInfo);
                } else {
                    if(Objects.nonNull(scheduleInfo.getSourceId())) {
                        for(String pageId : scheduleInfo.getPageIds()){
                            if(pageIdVsLocationIdMap.containsKey(pageId)) {
                                locations.add(pageIdVsLocationIdMap.get(pageId));
                            }
                        }
                    }
                    // businessIds -> accessible business IDs
                    //locations = getBusinessIdFromPageId(scheduleInfo.getSourceId(), scheduleInfo.getPageIds());
                    if (!accessibleLocationsSet.containsAll(locations)) {
                        String channel = SocialChannel.getSocialChannelNameById(scheduleInfo.getSourceId());
                        List<String> channelList = hasAccessMap.get(scheduleInfo.getSocialPostId());
                        if (CollectionUtils.isEmpty(channelList)) {
                            channelList = new ArrayList<>();
                            channelList.add(channel);
                        } else {
                            if (!channelList.contains(channel)) {
                                channelList.add(channel);
                            }
                        }
                        hasAccessMap.put(scheduleInfo.getSocialPostId(), channelList);
                    }
                }
                if (locations.stream().distinct().anyMatch(accessibleLocationsSet::contains)) {
                    locationsScheduleInfo.add(scheduleInfo);
                }

            }
            LOGGER.info("Location list: {}", locationsScheduleInfo.size());
            socialPostScheduleInfoList = locationsScheduleInfo;
        }
        LOGGER.info("socialPostScheduleInfoList: {}", socialPostScheduleInfoList.size());
        return socialPostScheduleInfoList;
    }

    private List<SocialPostScheduleInfo> getSocialPostSchInfoWithLoc(List<SocialPostScheduleInfo> socialPostScheduleInfoList, List<Integer> businessIds) {
        if (CollectionUtils.isNotEmpty(businessIds)) {
            List<SocialPostScheduleInfo> locationsScheduleInfo = new ArrayList<>();
            for (SocialPostScheduleInfo scheduleInfo : socialPostScheduleInfoList) {
                List<Integer> locations;
                if (CollectionUtils.isEmpty(scheduleInfo.getPageIds())) {
                    locations = socialPostService.fetchlocationsByScheduleCondition(scheduleInfo);
                } else {
                    locations = socialPostService.getBusinessIdFromPageId(scheduleInfo.getSourceId(), scheduleInfo.getPageIds());
                }
                if (businessIds.stream().distinct().anyMatch(locations::contains)) {
                    locationsScheduleInfo.add(scheduleInfo);
                }
            }
            socialPostScheduleInfoList = locationsScheduleInfo;
        }
        return socialPostScheduleInfoList;
    }

    private List<SocialPostCalendarMessage> getSocialPostSchInfoWithLocV3(List<SocialPostCalendarMessage> socialPostCalendarMessageList, List<Integer> businessIds) {
        if (CollectionUtils.isNotEmpty(businessIds)) {
            List<SocialPostCalendarMessage> locationsScheduleInfo = new ArrayList<>();
            for (SocialPostCalendarMessage scheduleInfo : socialPostCalendarMessageList) {
                List<Integer> locations;
                if (CollectionUtils.isEmpty(scheduleInfo.getPageIds())) {
                    locations = socialPostService.fetchlocationsByScheduleCondition(scheduleInfo);
                } else {
                    locations = socialPostService.getBusinessIdFromPageId(scheduleInfo.getSourceId(), scheduleInfo.getPageIds());
                }
                if (businessIds.stream().distinct().anyMatch(locations::contains)) {
                    locationsScheduleInfo.add(scheduleInfo);
                }
            }
            socialPostCalendarMessageList = locationsScheduleInfo;
        }
        return socialPostCalendarMessageList;
    }

    @Nullable
    private SocialSchedulePostMessage getSocialSchedulePostMessage(GlobalFilterCriteriaSchedulePostMessage filter, Integer userId, Map<Integer, SocialPost> socialPostMap, Map<Integer, BusinessCoreUser> userDetailMap, Map<Integer, List<SocialPostInfoRepository.PostPageStatus>> postPageStatusMap, Map<String, Boolean> pagePermissionMap,
                                                                   Map<Integer, List<String>> hasAccessMap, BusinessLiteDTO businessLiteDTO, Map<Integer, SocialPostsAssets> postAssetsMap, Map<Integer, Integer> postVsDuplicateCountMap, SocialPostScheduleInfo socialPostScheduled, Map<Long, List<SocialTagBasicDetail>> tagMappingInfoMap, String source) throws Exception {
        SocialSchedulePostMessage socialSchedulePostMessage = new SocialSchedulePostMessage();

        String pstDate = new SimpleDateFormat(SCHEDULE_DATE_FORMAT).format(socialPostScheduled.getPublishDate());
        Date utcDate = socialPostService.convertScheduleDateToTimezone(pstDate);
        String utcFormat = new SimpleDateFormat(SCHEDULE_DATE_FORMAT).format(utcDate);

        // 1. return post which has access of locations inside it wrt channel
        // 2. check for edit, repost and delete access. -> when all locations are accessible


        // location accessible -> locations Ids, source Id.


        socialSchedulePostMessage.setId(socialPostScheduled.getSocialPostId());
        socialSchedulePostMessage.setDatePublish(socialPostScheduled.getPublishDate());
        socialSchedulePostMessage.setScheduleInfoId(socialPostScheduled.getId());
        socialSchedulePostMessage.setPublishDate(utcFormat);
        socialSchedulePostMessage.setPublishedBy(null);
        socialSchedulePostMessage.setIsPublished(socialPostScheduled.getIsPublished());
        socialSchedulePostMessage.setPostCategory("post");

        socialSchedulePostMessage.setTags(tagMappingInfoMap.get(Long.valueOf(socialPostScheduled.getSocialPostId())));
        //LOGGER.info("2 ============== {}", new Date(System.currentTimeMillis()));

        //apple metadata setter
        if(socialPostScheduled.getSourceId() == SocialChannel.APPLE_CONNECT.getId()) {
            socialPostService.setAppleMetaData(socialSchedulePostMessage, socialPostScheduled.getSocialPostId());
            //apple publish status
            List<SocialPostInfoRepository.PostPageStatus> postPageStatus = postPageStatusMap.get(socialPostScheduled.getSocialPostId());
            if(CollectionUtils.isNotEmpty(postPageStatus)) {
                ApplePublishInfoMetadata applePublishInfoMetadata =
                        applePublishInfoMetadataRepository.findByPublishInfoId(postPageStatus.get(0).getId());
                socialSchedulePostMessage.setApplePublishStatus(applePublishStatus(applePublishInfoMetadata.getPublishStatus()));

            }
        }
        List<String> socialChannelEnabled = getEnabledSocialChannelList(socialPostScheduled);
        socialSchedulePostMessage.setPostingSites(socialChannelEnabled);

        if (socialSchedulePostMessage.getIsPublished() == 0) {
            socialSchedulePostMessage.setPermissionStatus(getPermissionFromMap(pagePermissionMap, socialPostScheduled, socialChannelEnabled, socialPostScheduled.getPageIds()));
            //getPermissionStatus(filter.getAccessibleLocationIds(), socialSchedulePostMessage, mapEValue));
        }
        if (socialSchedulePostMessage.getIsPublished() == 1) {
            Map<String, Object> output = getPostFailedFlag(CollectionUtils.isNotEmpty(filter.getAccessibleLocationIds()) ?
                    new HashSet<>(filter.getAccessibleLocationIds()) : new HashSet<>(), socialSchedulePostMessage,
                    postPageStatusMap, filter.getHasFullAccess());
            //LOGGER.info("Between 2 and 3 logs ");
            socialSchedulePostMessage.setHasPostFailed((Boolean) output.get("isFailed"));
            if (socialSchedulePostMessage.getHasPostFailed()) {
                socialSchedulePostMessage.setFailedPageCount((Integer) output.get("failedPageCount"));
                socialSchedulePostMessage.setFailedChannelCount((Integer) output.get("failedChannelCount"));
                socialSchedulePostMessage.setFailedSites((List<String>) output.get("failedChannelList"));
            }
            socialSchedulePostMessage.setIsOperationAllowed((Boolean) output.get("isOperationAllowed"));

            if (!(Boolean) output.get("isVisible")) {
                return null;
            }
        }
        SocialPost socialPost = socialPostMap.get(socialPostScheduled.getSocialPostId());
        if (Objects.nonNull(socialPost)) {
            if(MapUtils.isNotEmpty(userDetailMap) && Objects.nonNull(socialPost.getCreatedBy()) &&
                    userDetailMap.containsKey(socialPost.getCreatedBy()) ) {
                socialSchedulePostMessage.setCreatedByName(businessCoreService.getFullUsername(userDetailMap.get(socialPost.getCreatedBy())));
            }
            if (StringUtils.isNotEmpty(socialPost.getPostMetadata())) {
                socialSchedulePostMessage.setMediaSequence(socialPostService.getMediaSequence(socialPost, getBusinessIdWherePostWasScheduled(socialPostScheduled, businessLiteDTO)));
            }
            if(StringUtils.isNotEmpty(socialPost.getApprovalUserIds())){
                socialSchedulePostMessage.setIsApprover(socialPost.getApprovalUserIds().contains(String.valueOf(userId)));
            }
            socialSchedulePostMessage.setIsCreator(Objects.equals(socialPost.getCreatedBy(), userId));
            socialSchedulePostMessage.setPostText(socialPost.getPostText());
            socialSchedulePostMessage.setAiPost((Objects.isNull(socialPost.getAiPost()) || socialPost.getAiPost() == 0)?false:true);
            socialSchedulePostMessage.setApprovalStatus(socialPost.getApprovalStatus());
            socialSchedulePostMessage.setApproveWorkflowId(socialPost.getApprovalWorkflowId());
            if(StringUtils.isNotEmpty(socialPost.getApprovalMetadata())){
                ApprovalMetadata approvalMetadata = JSONUtils.fromJSON(socialPost.getApprovalMetadata(),ApprovalMetadata.class);
                if(Objects.nonNull(approvalMetadata)){
                    socialSchedulePostMessage.setApprovalUUId(approvalMetadata.getApprovalUUId());
                    socialSchedulePostMessage.setConversationId(socialPost.getConversationId());
                    socialSchedulePostMessage.setApprovalRequestId(approvalMetadata.getApprovalRequestId());
                    socialSchedulePostMessage.setReferenceStepId(approvalMetadata.getReferenceStepId());
                }
            }
            if(StringUtils.isNotEmpty(socialPost.getPostMetadata()) && socialPostScheduled.getSourceId() == SocialChannel.GMB.getId() ){
                SocialPostSchedulerMetadata postMetadata = JSONUtils.fromJSON(socialPost.getPostMetadata(),SocialPostSchedulerMetadata.class);
                GoogleOfferDetails googleOfferDetails = convertToGMBOfferResponse(postMetadata,null,null);
                if(Objects.nonNull(googleOfferDetails)){
                    socialSchedulePostMessage.setGmbOfferDetails(googleOfferDetails);
                    socialSchedulePostMessage.setType(GOOGLE_OFFER);
                    socialSchedulePostMessage.setPostCategory(GOOGLE_OFFER);
                }
            }
            if (StringUtils.isNotEmpty(socialPost.getImageIds())) {
                List<SocialPostsAssets> imageAssets = postAssetService.getPostsAssetsById(postAssetsMap, socialPost.getImageIds());
                socialSchedulePostMessage.setImages(postAssetService.getMediaDataV2(imageAssets, businessLiteDTO.getBusinessNumber(), Constants.IMAGE));
            }
            if (StringUtils.isNotEmpty(socialPost.getCompressedImageIds())) {
                List<SocialPostsAssets> compressedImageAssets = postAssetService.getPostsAssetsById(postAssetsMap, socialPost.getCompressedImageIds());
                socialSchedulePostMessage.setCompressedImages(postAssetService.getMediaRequestV2(compressedImageAssets, businessLiteDTO.getBusinessNumber(), Constants.IMAGE));
            }
            if (StringUtils.isNotEmpty(socialPost.getVideoIds())) {
                List<SocialPostsAssets> videoAssets = postAssetService.getPostsAssetsById(postAssetsMap, socialPost.getVideoIds());
                PostAssetsData postAssetsData = postAssetService.setSocialPostsAssetsDataV2(videoAssets, businessLiteDTO.getBusinessNumber(), Constants.VIDEO);
                socialSchedulePostMessage.setVideos(postAssetsData.getVideos());
                if(Constants.MOBILE.equalsIgnoreCase(source) && CollectionUtils.isEmpty(postAssetsData.getVideoThumbnailUrls())) {
                    socialSchedulePostMessage.setVideoThumbnails(Arrays.asList(Constants.DEFAULT_VIDEO_THUMBNAIL_URL));
                } else {
                    socialSchedulePostMessage.setVideoThumbnails(postAssetsData.getVideoThumbnailUrls());
                }
            }
            if (StringUtils.isNotEmpty(socialPost.getMentions())) {
                socialSchedulePostMessage.setMentions(JSONUtils.collectionFromJSON(socialPost.getMentions(), MentionData.class));
            }
            if (StringUtils.isNotEmpty(socialPost.getLinkPreviewUrl())) {
                socialSchedulePostMessage.setLinkPreviewUrl(socialPost.getLinkPreviewUrl());
            }

            if (MapUtils.isNotEmpty(postVsDuplicateCountMap) && postVsDuplicateCountMap.containsKey(socialPost.getId())) {
                socialSchedulePostMessage.setDuplicatedCount(postVsDuplicateCountMap.get(socialPost.getId()));
            } else {
                socialSchedulePostMessage.setDuplicatedCount(0);
            }
            if (StringUtils.isNotEmpty(socialPost.getQuotedTweetSource())) {
                socialSchedulePostMessage.setQuotedTweetSource(socialPost.getQuotedTweetSource());
            }
            else
            {
                socialSchedulePostMessage.setQuotedTweetSource(Constants.TWITTER_SOURCE_BE);
            }
            if (Objects.nonNull(socialPost.getQuotedPostId()) && StringUtils.isNotEmpty(socialPost.getQuotedPostUrl())) {
                socialSchedulePostMessage.setIsQuotedTweet(true);
            }
            // Reel/Story condition
            if (Objects.nonNull(socialPost.getPostMetadata())) {
                SocialPostSchedulerMetadata metadata = JSONUtils.fromJSON(socialPost.getPostMetadata(), SocialPostSchedulerMetadata.class);
                if (Objects.nonNull(metadata) && StringUtils.isNotEmpty(metadata.getIgPostMetadata())) {
                    IgPostMetadata igPostMetadata = JSONUtils.fromJSON(metadata.getIgPostMetadata(), IgPostMetadata.class);
                    socialSchedulePostMessage.setType(Objects.nonNull(igPostMetadata) ? igPostMetadata.getType() : null);
                    socialSchedulePostMessage.setPostCategory(Objects.nonNull(igPostMetadata) ? igPostMetadata.getType() : null);
                    if (StringUtils.isNotEmpty(socialSchedulePostMessage.getType()) && socialSchedulePostMessage.getType().equalsIgnoreCase("story")
                            && socialSchedulePostMessage.getIsPublished() == 1) {
                        //story expiry check
                        socialSchedulePostMessage.setIsExpired(isStoryExpired(socialSchedulePostMessage.getDatePublish()));
                    }
                }

                if (Objects.nonNull(metadata) && StringUtils.isNotEmpty(metadata.getFbPostMetadata())) {
                    FbPostMetadata fbPostMetadata = JSONUtils.fromJSON(metadata.getFbPostMetadata(), FbPostMetadata.class);
                    socialSchedulePostMessage.setType(Objects.nonNull(fbPostMetadata) ? fbPostMetadata.getType() : null);
                    socialSchedulePostMessage.setPostCategory(Objects.nonNull(fbPostMetadata) ? fbPostMetadata.getType() : null);
                    if (Constants.MOBILE.equalsIgnoreCase(source) &&
                            (FacebookPostType.REEL.getName().equalsIgnoreCase(socialSchedulePostMessage.getType()) ||
                                    FacebookPostType.STORY.getName().equalsIgnoreCase(socialSchedulePostMessage.getType()) ||
                                    ("post".equalsIgnoreCase(socialSchedulePostMessage.getType()) && fbPostMetadata != null && fbPostMetadata.getFirstComment() != null))) {
                        LOGGER.info("Skipping the facebook reel or story post from mobile view");
                        return null;
                    }
                    if (StringUtils.isNotEmpty(socialSchedulePostMessage.getType()) && socialSchedulePostMessage.getType().equalsIgnoreCase(FacebookPostType.STORY.getName())
                            && socialSchedulePostMessage.getIsPublished() == 1) {
                        //story expiry check
                        socialSchedulePostMessage.setIsExpired(isStoryExpired(socialSchedulePostMessage.getDatePublish()));
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(hasAccessMap.get(socialSchedulePostMessage.getId()))) {
            socialSchedulePostMessage.setHasAccess(false);
            socialSchedulePostMessage.setIncompleteChannel(hasAccessMap.get(socialSchedulePostMessage.getId()));
        }

        // show only failed posts if filter= failed is applied
        if (SocialPostStatusEnum.FAILED.getName().equalsIgnoreCase(filter.getPostStatus()) && !socialSchedulePostMessage.getHasPostFailed()) {
            return null;
        }
        return socialSchedulePostMessage;
    }

    private SocialPostPermissionStatusResponse getPermissionFromMap(Map<String, Boolean> pagePermissionMap, SocialPostScheduleInfo postMessage, List<String> socialChannelEnabled,
                                                                    List<String> pageIds) {
        return getPermissionFromMap(pagePermissionMap, postMessage.getSourceId(), socialChannelEnabled, pageIds);
    }

    private SocialPostPermissionStatusResponse getPermissionFromMap(Map<String, Boolean> pagePermissionMap,
                                                                    Integer sourceId, List<String> socialChannelEnabled,
                                                                    List<String> pageIds) {
        SocialPostPermissionStatusResponse  res = new SocialPostPermissionStatusResponse();
        for (String channel : socialChannelEnabled) {
            if(sourceId.equals(SocialChannel.getSocialChannelByName(channel).getId())){
                boolean areAllPagesAllowed = areAllPagesAllowed(pagePermissionMap,pageIds);
                if (SocialChannel.GOOGLE.getName().equalsIgnoreCase(channel)) {
                    res.setGoogleAccountPostPermission(areAllPagesAllowed);
                }else if(SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
                    res.setFacebookAccountPostPermission(areAllPagesAllowed);
                }else if(SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(channel)) {
                    res.setInstagramAccountPostPermission(areAllPagesAllowed);
                }else if(SocialChannel.LINKEDIN.getName().equalsIgnoreCase(channel)) {
                    res.setLinkedinAccountPostPermission(areAllPagesAllowed);
                }else if(SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)) {
                    res.setTwitterAccountPostPermission(areAllPagesAllowed);
                }else if(SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)) {
                    res.setYoutubeAccountPostPermission(areAllPagesAllowed);
                }else if(SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel)) {
                    res.setTiktokConnectAccountPostPermission(areAllPagesAllowed);
                }
            }
        }
        return res;
    }

    private boolean areAllPagesAllowed(Map<String, Boolean> pagePermissionMap, List<String> pageIds) {
        // Only consider pages that are present in the map
        if(MapUtils.isEmpty(pagePermissionMap)) {
            return true;
        }
        return pageIds.parallelStream().allMatch(id -> pagePermissionMap.getOrDefault(id, false));
    }

    private String applePublishStatus(String applePublishStatus) {
        if (StringUtils.equals(ApplePublishStateEnum.IN_PROCESS.getName(), applePublishStatus)  || StringUtils.equals(ApplePublishStateEnum.IMAGE_CREATED.getName(), applePublishStatus) ||
                StringUtils.equals(ApplePublishStateEnum.IMAGE_APPROVED.getName(), applePublishStatus)  || StringUtils.equals(ApplePublishStateEnum.SHOWCASE_CREATIVE_CREATED.getName(), applePublishStatus) ||
                StringUtils.equals(ApplePublishStateEnum.SHOWCASE_CREATIVE_APPROVED.getName(), applePublishStatus)  || StringUtils.equals(ApplePublishStateEnum.SHOWCASE_CREATED.getName(), applePublishStatus)) {
            return AppleShowcaseStatusEnum.SCHEDULED.name().toLowerCase();
        } else if(StringUtils.equals(ApplePublishStateEnum.SHOWCASE_APPROVED.getName(), applePublishStatus)) {
            return AppleShowcaseStatusEnum.PUBLISHED.name().toLowerCase();
        } else if(StringUtils.equals(ApplePublishStateEnum.LIVE.getName(), applePublishStatus)) {
            return AppleShowcaseStatusEnum.LIVE.name().toLowerCase();
        } else if(StringUtils.equals(ApplePublishStateEnum.IMAGE_REJECTED.getName(), applePublishStatus) || StringUtils.equals(ApplePublishStateEnum.SHOWCASE_CREATIVE_REJECTED.getName(), applePublishStatus) ||
                StringUtils.equals(ApplePublishStateEnum.SHOWCASE_REJECTED.getName(), applePublishStatus)) {
            return AppleShowcaseStatusEnum.REJECTED.name().toLowerCase();
        } else if(StringUtils.equals(ApplePublishStateEnum.INTERNAL_REJECTED.getName(), applePublishStatus)) {
            return AppleShowcaseStatusEnum.FAILED.name().toLowerCase();
        }
        return null;
    }

    private List<String> getEnabledSocialChannelList(SocialPostScheduleInfo socialPostScheduled) {
        return getEnabledSocialChannelList(socialPostScheduled.getSourceId());
    }

    private List<String> getEnabledSocialChannelList(Integer sourceId) {
        List<String> socialChannelEnabled = new ArrayList<>();
        if (sourceId == SocialChannel.GOOGLE.getId()) {
            socialChannelEnabled.add("google");
        } else if (sourceId == SocialChannel.TWITTER.getId()) {
            socialChannelEnabled.add("twitter");
        } else if (sourceId == SocialChannel.FACEBOOK.getId()) {
            socialChannelEnabled.add("facebook");
        } else if (sourceId == SocialChannel.INSTAGRAM.getId()) {
            socialChannelEnabled.add("instagram");
        } else if (sourceId == SocialChannel.LINKEDIN.getId()) {
            socialChannelEnabled.add("linkedin");
        } else if (sourceId == SocialChannel.YOUTUBE.getId()) {
            socialChannelEnabled.add("youtube");
        }else if (sourceId == SocialChannel.APPLE_CONNECT.getId()) {
            socialChannelEnabled.add("apple_connect");
        } else if(sourceId == SocialChannel.TIKTOK.getId()) {
            socialChannelEnabled.add("tiktok");
        }
        return socialChannelEnabled;
    }


    @Override
    public SocialSchedulePostResponse getAllScheduledPosts(GlobalFilterCriteriaSchedulePostMessage filter, Integer userId, boolean isListViewRequest) throws Exception {
        try {
            LOGGER.info("getAllScheduledPosts : {}", filter);
            SocialSchedulePostResponse socialSchedulePostResponse = new SocialSchedulePostResponse();
            Map<Integer, SocialPost> socialPostMap = new HashMap<>();
            Map<Integer, List<SocialPostInfoRepository.PostPageStatus>> postPageStatusMap = new HashMap<>();


            Date startDate = new SimpleDateFormat(SCHEDULE_DATE_FORMAT).parse(filter.getStartDate());
            Date endDate = new SimpleDateFormat(SCHEDULE_DATE_FORMAT).parse(filter.getEndDate());

            List<Integer> sourceIds = new ArrayList<>();
            List<Integer> publishState = new ArrayList<>();
            List<String> approvalState = new ArrayList<>();
            addFilterDetails(filter, publishState, sourceIds,approvalState);
            List<SocialPostScheduleInfo> socialPostScheduleInfoList =
                    socialPostScheduleInfoRepository.findByEnterpriseAndDate(filter.getBusinessId(), startDate, endDate, publishState, sourceIds);
            List<String> pageIdList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(filter.getAccessibleLocationIds())) {
                for (Integer sId : sourceIds) {
                    pageIdList.addAll(socialPostService.getPageIdFromBusinessId(sId, filter.getAccessibleLocationIds()));
                }
            }

            LOGGER.info("Before loop 1============== {}", new Date(System.currentTimeMillis()));

            // fetch only location level details
            List<SocialPostScheduleInfo> socialPostScheduleInfoListAll = socialPostScheduleInfoRepository.
                    findByEnterpriseAndDate(filter.getBusinessId(), startDate, endDate, publishState, socialPostService.getAllSourceIdList());
            BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(filter.getBusinessId(), false);// we can fetch all biz data before loop

            Map<Integer, List<String>> hasAccessMap = new HashMap<>();
            LOGGER.info("Before loop 2============== {}", new Date(System.currentTimeMillis()));
            setSocialPostScheduleInfos(filter, socialPostScheduleInfoList, socialPostScheduleInfoListAll, hasAccessMap);
//			LOGGER.info("Social Post: Schedule data found for filter: {} and data: {}", filter, socialPostScheduleInfoList);
            Set<Integer> distinctPostIds = socialPostScheduleInfoList.stream().map(SocialPostScheduleInfo::getSocialPostId).collect(Collectors.toSet());
            List<SocialPost> socialPostList = socialPostService.getSocialPost(distinctPostIds,approvalState,filter.getApprovals(),filter.getCreators(), null, null);
            if(CollectionUtils.isEmpty(socialPostList)){
                return socialSchedulePostResponse;
            }
            socialPostMap = socialPostService.generateMapForIdAndSocialPost(socialPostList);
            distinctPostIds.retainAll(socialPostMap.keySet());
            List<PostActivityRepo.ActivityCount> postVsDuplicateCount = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(distinctPostIds)) {
                postVsDuplicateCount = postActivityRepo.getPostWiseActivityCount(distinctPostIds, PostActivityType.DUPLICATE.getName());
            }
//			List<SocialPost> socialPosts = socialPostRepository.findByIdIn(distinctPostIds);
//			if(CollectionUtils.isNotEmpty(socialPosts)){
//				socialPostMap = socialPosts.stream().collect(Collectors.toMap(SocialPost::getId, s -> s));
//			}
            List<SocialPostInfoRepository.PostPageStatus> postPageStatusList = socialPostPublishInfoRepository.findBySocialPostIdInAndSourceIdIn(distinctPostIds, sourceIds);
            if(CollectionUtils.isNotEmpty(postPageStatusList)){
                postPageStatusMap = postPageStatusList.stream().collect(Collectors.groupingBy(
                        SocialPostInfoRepository.PostPageStatus::getSocialPostId,
                        Collectors.mapping(obj -> obj, Collectors.toList())));
            }
            Map<Integer, Integer> postVsDuplicateCountMap = postVsDuplicateCount.stream()
                    .collect(Collectors.toMap(PostActivityRepo.ActivityCount::getPostId, PostActivityRepo.ActivityCount::getActivityCount));
            LOGGER.info("Before loop 4============== {}", new Date(System.currentTimeMillis()));
            //Map<String, List<SocialSchedulePostMessage>> postData = new HashMap<>();
            List<SocialSchedulePostMessage> socialSchedulePostMessageList = new ArrayList<>();
            for (SocialPostScheduleInfo socialPostScheduled : socialPostScheduleInfoList.stream().
                    filter(post -> distinctPostIds.contains(post.getSocialPostId())).collect(Collectors.toList())) {
                SocialSchedulePostMessage socialSchedulePostMessage = getSocialSchedulePostMessage(filter, userId, socialPostMap, postPageStatusMap, businessLiteDTO, hasAccessMap, postVsDuplicateCountMap, socialPostScheduled, null);
                if (Objects.isNull(socialSchedulePostMessage)) continue;

                socialSchedulePostMessageList.add(socialSchedulePostMessage);
//				List<SocialPostScheduleInfo> mapEValue = mapE.getValue();
//				List<SocialSchedulePostMessage> socialSchedulePostMessageList = new ArrayList<>();
//				//LOGGER.info("1 ============== {}", new Date(System.currentTimeMillis()));
//				for (SocialPostScheduleInfo socialPostScheduled : mapEValue) {
//
//
//				}
//				postData.put(mapE.getKey(), socialSchedulePostMessageList);
            }
            LOGGER.info("5 ============== {}", new Date(System.currentTimeMillis()));
            return createPaginationForListView(socialSchedulePostMessageList, filter.getPageNo(), filter.getPageSize(), filter.getOrder(), isListViewRequest);

        } catch (Exception e) {
            LOGGER.info("Social Post: something went wrong while fetching data for filter {} e {}", filter, e);
            throw new BirdeyeSocialException(e.getMessage());
        }
    }

    @Override
    public Integer socialPostsESSyncRequest(SocialPostsESSyncRequest socialPostsESSyncRequest) {
        try {
            if(Objects.nonNull(socialPostsESSyncRequest)){
                Integer pageNo = socialPostsESSyncRequest.getPageNo();
                Integer size = socialPostsESSyncRequest.getSize();
                if(Objects.isNull(pageNo))
                    pageNo = 0;
                if(Objects.isNull(size))
                    size = 200;
                PageRequest request = new PageRequest(pageNo, size);
                Page<Integer> socialPostIdPage;
                if(Objects.nonNull(socialPostsESSyncRequest.getBusinessId())) {
                    Integer lastSyncedPostId = socialPostsESSyncRequest.getLastSyncedPostId();
                    if(Objects.isNull(lastSyncedPostId))
                        lastSyncedPostId = Integer.MAX_VALUE;
                    Integer latestSyncedPostId = socialPostsESSyncRequest.getLatestSyncedPostId();
                    if(Objects.isNull(latestSyncedPostId))
                        latestSyncedPostId = 0;
                    LOGGER.info("Finding social posts schedule list for business id {} and lastSyncedPostId {} and latestSyncedPostId {}",
                            socialPostsESSyncRequest.getBusinessId(), lastSyncedPostId , latestSyncedPostId);
                    socialPostIdPage = socialPostScheduleInfoRepository.findByEnterpriseId(
                            socialPostsESSyncRequest.getBusinessId(), lastSyncedPostId, latestSyncedPostId, request);
                    if(Objects.nonNull(socialPostIdPage)){
                        sendIdsOnKafka(socialPostIdPage);
                        while(socialPostIdPage.hasNext()) {
                            socialPostIdPage = socialPostScheduleInfoRepository.findByEnterpriseId(
                                    socialPostsESSyncRequest.getBusinessId(), lastSyncedPostId, latestSyncedPostId, socialPostIdPage.nextPageable());
                            sendIdsOnKafka(socialPostIdPage);
                        }
                    }
                }
                else if(StringUtils.isNotEmpty(socialPostsESSyncRequest.getStartDate())
                    && StringUtils.isNotEmpty(socialPostsESSyncRequest.getEndDate())) {
                    LOGGER.info("Finding social post schedule Infos for start date {} and end Date {}", socialPostsESSyncRequest.getStartDate(),
                            socialPostsESSyncRequest.getEndDate());
                        Date startDate  = new SimpleDateFormat("yyyy-MM-dd").parse(socialPostsESSyncRequest.getStartDate());
                        Date endDate  = new SimpleDateFormat("yyyy-MM-dd").parse(socialPostsESSyncRequest.getEndDate());
                    socialPostIdPage = socialPostRepo.findByCreatedDate(startDate,
                           endDate, request);
                    if(Objects.nonNull(socialPostIdPage)){
                        sendIdsOnKafka(socialPostIdPage);
                        while(socialPostIdPage.hasNext()) {
                            socialPostIdPage = socialPostRepo.findByCreatedDate(startDate,
                                    endDate, socialPostIdPage.nextPageable());
                            sendIdsOnKafka(socialPostIdPage);
                        }
                    }
                }
            } else {
                LOGGER.info("socialPostsESSyncRequest object is null");
        }
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return 0;
    }

    private void sendIdsOnKafka(Page<Integer> socialPostIdPage) {
        List<Integer> socialPostIds;
        if(Objects.nonNull(socialPostIdPage)) {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
               LOGGER.info("Interrupted exception found while sleeping thread ");
            }
            socialPostIds = socialPostIdPage.getContent();
            LOGGER.info("Sending {} social post Ids {} on kafka ", socialPostIds.size(), socialPostIds);
            if(CollectionUtils.isNotEmpty(socialPostIds))
                kafkaProducerService.sendObjectV1("es-migration-postids", new SocialPostsESSyncRequest(new ArrayList<>(socialPostIds)));

        }
    }

    @Override
    public void syncRecordsOnEsBySocialPostIds(Collection<Integer> socialPostIds) {
        if(CollectionUtils.isNotEmpty(socialPostIds)) {
            List<SocialPost> socialPosts = socialPostRepo.findByIdIn(socialPostIds);
            for(SocialPost socialPost : socialPosts) {
                if(Objects.nonNull(socialPost) && Objects.nonNull(socialPost.getId())) {
                    List<SocialPostScheduleInfo> socialPostScheduleInfoList  = socialPostScheduleInfoRepository
                            .findBySocialPostId(socialPost.getId());
                    List<String> failedPageIds = null;
                    if(CollectionUtils.isNotEmpty(socialPostScheduleInfoList)) {
                        try {
                            List<SocialTagBasicDetail> tags = null;
                            List<SocialTagMappingInfo> formerTagMappingInfos = socialTagDBService.findTagMappingInfoByEntityIdsAndEntityType(Collections.singletonList(Long.valueOf(socialPost.getId())), SocialTagEntityType.POST);
                            if (CollectionUtils.isNotEmpty(formerTagMappingInfos)) {
                                tags = new ArrayList<>();
                                for (SocialTagMappingInfo socialTagMappingInfo : formerTagMappingInfos) {
                                    tags.add(new SocialTagBasicDetail(socialTagMappingInfo.getTagId(),
                                            socialTagMappingInfo.getTagName()));
                                }
                                LOGGER.info("Added tags {} for social post id {}", tags.size(), socialPost.getId());
                            }
                            List<Integer> sourceIds = socialPostScheduleInfoList.stream().map(SocialPostScheduleInfo::getSourceId).collect(Collectors.toList());

                            List<SocialPostInfoRepository.PostPageStatus> postPageStatusList =
                                    socialPostPublishInfoRepository.findBySocialPostIdInAndSourceIdIn(Collections.singleton(socialPost.getId()),
                                    sourceIds);
                            if(CollectionUtils.isNotEmpty(postPageStatusList)){
                                failedPageIds = new ArrayList<>();
                                for (SocialPostInfoRepository.PostPageStatus ps : postPageStatusList) {
                                    //if (CollectionUtils.isEmpty(accessibleLocationIds) || accessibleLocationIds.contains(ps.getBusinessId())) {
                                        String publishStatus = socialPostService.convertPublishedStateToStatus(ps.getIsPublished(), ps.getBucket());
                                        if (publishStatus.contains(SocialPostStatusEnum.FAILED.getName()) && !Objects.equals(ps.getIsDisable(), 1)) {
                                            failedPageIds.add(ps.getExternalPageId());
                                        }
                                  //  }
                                }
                            }
                            else{
                                LOGGER.info("getting empty postPageStatusList for :{}", socialPost.getId());
                            }

                            List<SocialPostCalendarMessage> socialPostCalendarMessages = socialPostCalendarESService.getESCalendarSaveObj(socialPost, socialPostScheduleInfoList, null, tags, failedPageIds);
                            socialPostCalendarESService.save(socialPostCalendarMessages);
                        } catch (Exception e) {
                            LOGGER.info("Synced not performed for social post Id {}", socialPost.getId(), e);
                        }
                    }
                    else
                        LOGGER.info("No schedule info found for post id {}", socialPost.getId());
                }
                else
                    LOGGER.info("NUll object found for social post ");
            }
        }
        else
            LOGGER.info("No social post ids found");
    }

    @Nullable
    private SocialSchedulePostMessage getSocialSchedulePostMessage(GlobalFilterCriteriaSchedulePostMessage filter, Integer userId, Map<Integer, SocialPost> socialPostMap, Map<Integer, List<SocialPostInfoRepository.PostPageStatus>> postPageStatusMap, BusinessLiteDTO businessLiteDTO, Map<Integer, List<String>> hasAccessMap, Map<Integer, Integer> postVsDuplicateCountMap, SocialPostScheduleInfo socialPostScheduled, String source) throws Exception {
        SocialSchedulePostMessage socialSchedulePostMessage = new SocialSchedulePostMessage();


        String pstDate = new SimpleDateFormat(SCHEDULE_DATE_FORMAT).format(socialPostScheduled.getPublishDate());
        Date utcDate = socialPostService.convertScheduleDateToTimezone(pstDate);
        String utcFormat = new SimpleDateFormat(SCHEDULE_DATE_FORMAT).format(utcDate);

        // 1. return post which has access of locations inside it wrt channel
        // 2. check for edit, repost and delete access. -> when all locations are accessible
        // location accessible -> locations Ids, source Id.


        socialSchedulePostMessage.setId(socialPostScheduled.getSocialPostId());
        socialSchedulePostMessage.setDatePublish(socialPostScheduled.getPublishDate());
        socialSchedulePostMessage.setScheduleInfoId(socialPostScheduled.getId());
        socialSchedulePostMessage.setPublishDate(utcFormat);
        socialSchedulePostMessage.setPublishedBy(null);
        socialSchedulePostMessage.setIsPublished(socialPostScheduled.getIsPublished());
        //LOGGER.info("2 ============== {}", new Date(System.currentTimeMillis()));

        List<String> socialChannelEnabled = new ArrayList<>();
        // not required because now only one schedule info can be there for one social post
//				if (socialSchedulePostMessageList.stream().anyMatch(d -> d.getId().equals(socialSchedulePostMessage.getId()))) {
//					continue;
//				}
        if (socialPostScheduled.getSourceId() == SocialChannel.GOOGLE.getId()) {
            socialChannelEnabled.add("google");
        } else if (socialPostScheduled.getSourceId() == SocialChannel.TWITTER.getId()) {
            socialChannelEnabled.add("twitter");
        } else if (socialPostScheduled.getSourceId() == SocialChannel.FACEBOOK.getId()) {
            socialChannelEnabled.add("facebook");
        } else if (socialPostScheduled.getSourceId() == SocialChannel.INSTAGRAM.getId()) {
            socialChannelEnabled.add("instagram");
        } else if (socialPostScheduled.getSourceId() == SocialChannel.LINKEDIN.getId()) {
            socialChannelEnabled.add("linkedin");
        } else if (socialPostScheduled.getSourceId() == SocialChannel.YOUTUBE.getId()) {
            socialChannelEnabled.add("youtube");
        }
        socialSchedulePostMessage.setPostingSites(socialChannelEnabled);

        if (socialSchedulePostMessage.getIsPublished() == 0) {
            socialSchedulePostMessage.setPermissionStatus(socialPostService.getPermissionStatus(filter.getAccessibleLocationIds(), socialSchedulePostMessage, Collections.singletonList(socialPostScheduled)));
        }
        if (socialSchedulePostMessage.getIsPublished() == 1) {
            Map<String, Object> output = getPostFailedFlag(CollectionUtils.isNotEmpty(filter.getAccessibleLocationIds()) ?
                    new HashSet<>(filter.getAccessibleLocationIds()) : new HashSet<>(), socialSchedulePostMessage,
                    postPageStatusMap, filter.getHasFullAccess());
            //LOGGER.info("Between 2 and 3 logs ");
            socialSchedulePostMessage.setHasPostFailed((Boolean) output.get("isFailed"));
            if (socialSchedulePostMessage.getHasPostFailed()) {
                socialSchedulePostMessage.setFailedPageCount((Integer) output.get("failedPageCount"));
                socialSchedulePostMessage.setFailedChannelCount((Integer) output.get("failedChannelCount"));
                socialSchedulePostMessage.setFailedSites((List<String>) output.get("failedChannelList"));
            }
            socialSchedulePostMessage.setIsOperationAllowed((Boolean) output.get("isOperationAllowed"));
            if (!(Boolean) output.get("isVisible")) {
                return null;
            }
        }
        SocialPost socialPost = socialPostMap.get(socialPostScheduled.getSocialPostId());
        if (Objects.nonNull(socialPost)) {
            if (StringUtils.isNotEmpty(socialPost.getPostMetadata())) {
                socialSchedulePostMessage.setMediaSequence(socialPostService.getMediaSequence(socialPost, socialPostScheduled.getEnterpriseId()));
            }
            if(StringUtils.isNotEmpty(socialPost.getApprovalUserIds())){
                socialSchedulePostMessage.setIsApprover(socialPost.getApprovalUserIds().contains(String.valueOf(userId)));
            }
            socialSchedulePostMessage.setIsCreator(Objects.equals(socialPost.getCreatedBy(), userId));
            socialSchedulePostMessage.setPostText(socialPost.getPostText());
            socialSchedulePostMessage.setAiPost((Objects.isNull(socialPost.getAiPost()) || socialPost.getAiPost() == 0)?false:true);
            socialSchedulePostMessage.setApprovalStatus(socialPost.getApprovalStatus());
            socialSchedulePostMessage.setApproveWorkflowId(socialPost.getApprovalWorkflowId());
            if(StringUtils.isNotEmpty(socialPost.getApprovalMetadata())){
                ApprovalMetadata approvalMetadata = JSONUtils.fromJSON(socialPost.getApprovalMetadata(),ApprovalMetadata.class);
                if(Objects.nonNull(approvalMetadata)){
                    socialSchedulePostMessage.setApprovalUUId(approvalMetadata.getApprovalUUId());
                    socialSchedulePostMessage.setConversationId(socialPost.getConversationId());
                    socialSchedulePostMessage.setApprovalRequestId(approvalMetadata.getApprovalRequestId());
                    socialSchedulePostMessage.setReferenceStepId(approvalMetadata.getReferenceStepId());
                }
            }
            if (StringUtils.isNotEmpty(socialPost.getImageIds())) {
                socialSchedulePostMessage.setImages(postAssetService.getMediaDataForCal(socialPost.getImageIds(), businessLiteDTO.getBusinessNumber(), Constants.IMAGE));
            }
            if (StringUtils.isNotEmpty(socialPost.getCompressedImageIds())) {
                socialSchedulePostMessage.setCompressedImages(postAssetService.getMediaRequestForCal(socialPost.getCompressedImageIds(), businessLiteDTO.getBusinessNumber(), Constants.IMAGE));
            }
            if (StringUtils.isNotEmpty(socialPost.getVideoIds())) {
                PostAssetsData postAssetsData = postAssetService.setSocialPostsAssetsDataForCal(socialPost.getVideoIds(), businessLiteDTO.getBusinessNumber(), Constants.VIDEO);
                socialSchedulePostMessage.setVideos(postAssetsData.getVideos());
                if(Constants.MOBILE.equalsIgnoreCase(source) && CollectionUtils.isEmpty(postAssetsData.getVideoThumbnailUrls())) {
                    socialSchedulePostMessage.setVideoThumbnails(Arrays.asList(Constants.DEFAULT_VIDEO_THUMBNAIL_URL));
                } else {
                    socialSchedulePostMessage.setVideoThumbnails(postAssetsData.getVideoThumbnailUrls());
                }
            }
            if (StringUtils.isNotEmpty(socialPost.getMentions())) {
                socialSchedulePostMessage.setMentions(JSONUtils.collectionFromJSON(socialPost.getMentions(), MentionData.class));
            }
            if (StringUtils.isNotEmpty(socialPost.getLinkPreviewUrl())) {
                socialSchedulePostMessage.setLinkPreviewUrl(socialPost.getLinkPreviewUrl());
            }

            if (MapUtils.isNotEmpty(postVsDuplicateCountMap) && postVsDuplicateCountMap.containsKey(socialPost.getId())) {
                socialSchedulePostMessage.setDuplicatedCount(postVsDuplicateCountMap.get(socialPost.getId()));
            } else {
                socialSchedulePostMessage.setDuplicatedCount(0);
            }
            if (StringUtils.isNotEmpty(socialPost.getQuotedTweetSource())) {
                socialSchedulePostMessage.setQuotedTweetSource(socialPost.getQuotedTweetSource());
            }
            else
            {
                socialSchedulePostMessage.setQuotedTweetSource(Constants.TWITTER_SOURCE_BE);
            }
            if (Objects.nonNull(socialPost.getQuotedPostId()) && StringUtils.isNotEmpty(socialPost.getQuotedPostUrl())) {
                socialSchedulePostMessage.setIsQuotedTweet(true);
            }
            // Reel/Story condition
            if (Objects.nonNull(socialPost.getPostMetadata())) {
                SocialPostSchedulerMetadata metadata = JSONUtils.fromJSON(socialPost.getPostMetadata(), SocialPostSchedulerMetadata.class);
                if (Objects.nonNull(metadata) && StringUtils.isNotEmpty(metadata.getIgPostMetadata())) {
                    IgPostMetadata igPostMetadata = JSONUtils.fromJSON(metadata.getIgPostMetadata(), IgPostMetadata.class);
                    socialSchedulePostMessage.setType(Objects.nonNull(igPostMetadata)?igPostMetadata.getType():null);
                    if(StringUtils.isNotEmpty(socialSchedulePostMessage.getType()) && socialSchedulePostMessage.getType().equalsIgnoreCase("story")
                            && socialSchedulePostMessage.getIsPublished()==1) {
                        //story expiry check
                        socialSchedulePostMessage.setIsExpired(isStoryExpired(socialSchedulePostMessage.getDatePublish()));
                    }
                }

                if (Objects.nonNull(metadata) && StringUtils.isNotEmpty(metadata.getFbPostMetadata())) {
                    FbPostMetadata fbPostMetadata = JSONUtils.fromJSON(metadata.getFbPostMetadata(), FbPostMetadata.class);
                    socialSchedulePostMessage.setType(Objects.nonNull(fbPostMetadata)?fbPostMetadata.getType():null);
                    if(StringUtils.isNotEmpty(socialSchedulePostMessage.getType()) && socialSchedulePostMessage.getType().equalsIgnoreCase(FacebookPostType.STORY.getName())
                            && socialSchedulePostMessage.getIsPublished()==1) {
                        //story expiry check
                        socialSchedulePostMessage.setIsExpired(isStoryExpired(socialSchedulePostMessage.getDatePublish()));
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(hasAccessMap.get(socialSchedulePostMessage.getId()))) {
            socialSchedulePostMessage.setHasAccess(false);
            socialSchedulePostMessage.setIncompleteChannel(hasAccessMap.get(socialSchedulePostMessage.getId()));
        }

        // show only failed posts if filter= failed is applied
        if (SocialPostStatusEnum.FAILED.getName().equalsIgnoreCase(filter.getPostStatus()) && !socialSchedulePostMessage.getHasPostFailed()) {
            return null;
        }
        return socialSchedulePostMessage;
    }

    private Map<String, List<SocialSchedulePostMessage>> getAiPostsFromList(List<SocialPostCalendarMessage> aiPosts,
            GlobalFilterCriteriaSchedulePostMessage filter, Integer userId, boolean isListView) {

        if (CollectionUtils.isEmpty(aiPosts)) {
            return new HashMap<>();
        }
        BusinessAiCustomization businessAiCustomization = aiCustomizationRepo.findByAccountId(filter.getBusinessId());
        String createdByName = businessCoreService.getFullUsername(socialAiPostHelperService.getDefaultAiId());
        List<SocialSchedulePostMessage> socialSchedulePostMessageList = new ArrayList<>();

        // Convert AI posts to SocialSchedulePostMessage
        for(SocialPostCalendarMessage aiGeneratedPosts: aiPosts) {
            LOGGER.info("AI GeneratedPosts post: {}", aiGeneratedPosts);
            SocialSchedulePostMessage socialSchedulePostMessage = SocialSchedulePostMessage.builder()
                    .id(aiGeneratedPosts.getAiPostId())
                    .postText(aiGeneratedPosts.getPostText())
                    .images(aiGeneratedPosts.getImages())
                    .compressedImages(
                            CollectionUtils.isNotEmpty(aiGeneratedPosts.getCompressedMediaSequence())?
                                aiGeneratedPosts.getCompressedMediaSequence() : CollectionUtils.isNotEmpty(aiGeneratedPosts.getImages()) ?
                                    aiGeneratedPosts.getImages().stream().map(MediaData::getMediaUrl).collect(Collectors.toList()) : null)
                    .mediaSequence(aiGeneratedPosts.getMediaSequence())
                    .postHeader(aiGeneratedPosts.getPostHeader())
                    .publishDate(new SimpleDateFormat(SCHEDULE_DATE_FORMAT).format(aiGeneratedPosts.getPublishDate()))
                    .datePublish(aiGeneratedPosts.getPublishDate())
                    .tags(aiGeneratedPosts.getTags())
                    .postingSites(Objects.nonNull(businessAiCustomization)
                            ?socialAiPostHelperService.getChannelsFromSourceIds(businessAiCustomization.getSourceIds())
                            :socialAiPostHelperService.getDefaultChannels())
                    .createdByName(createdByName)
                    .aiSuggestion(true)
                    .build();
            socialSchedulePostMessageList.add(socialSchedulePostMessage);
        }

        // Group by date
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");
        return socialSchedulePostMessageList.stream()
            .collect(Collectors.groupingBy(
                post -> dateFormat.format(post.getDatePublish())
            ));
    }

    @NotNull
    private void setSocialPostScheduleInfos(GlobalFilterCriteriaSchedulePostMessage filter, List<SocialPostScheduleInfo> socialPostScheduleInfoList, List<SocialPostScheduleInfo> socialPostScheduleInfoListAll, Map<Integer, List<String>> hasAccessMap) {
        socialPostScheduleInfoListAll = getLocationAccessInfo(filter.getAccessibleLocationIds(), socialPostScheduleInfoListAll, hasAccessMap);
        if (!filter.getBusinessIds().isEmpty()) {
            List<SocialPostScheduleInfo> locationsScheduleInfo = new ArrayList<>();
            for (SocialPostScheduleInfo scheduleInfo : socialPostScheduleInfoList) {
                List<Integer> locations;
                if (CollectionUtils.isEmpty(scheduleInfo.getPageIds())) {
                    locations = socialPostService.fetchlocationsByScheduleCondition(scheduleInfo);
                } else {
                    locations = socialPostService.getBusinessIdFromPageId(scheduleInfo.getSourceId(), scheduleInfo.getPageIds());
                }
                if (filter.getBusinessIds().stream().distinct().anyMatch(locations::contains)) {
                    locationsScheduleInfo.add(scheduleInfo);
                }
            }
            socialPostScheduleInfoList = locationsScheduleInfo;
        }

        LOGGER.info("Before loop 3============== {}", new Date(System.currentTimeMillis()));

        socialPostScheduleInfoList = socialPostScheduleInfoList.stream().distinct().filter(socialPostScheduleInfoListAll::contains).collect(Collectors.toList());
    }

    private SocialSchedulePostResponse  createPaginationForListView(List<SocialSchedulePostMessage> socialPosts, Integer pageNo, Integer pageSize, PageSortDirection direction, boolean isListView) throws ParseException {
        SocialSchedulePostResponse socialSchedulePostResponse = new SocialSchedulePostResponse();
        if(CollectionUtils.isEmpty(socialPosts)) {
            return socialSchedulePostResponse;
        }
        if(!isListView) {
            socialSchedulePostResponse.setPosts(socialPosts.stream().collect(Collectors.groupingBy(
                    map -> new SimpleDateFormat("MM/dd/yyyy").format(map.getDatePublish()))));
            return socialSchedulePostResponse;
        }else
            shortenAiPostsResponse(socialSchedulePostResponse.getPosts(), socialSchedulePostResponse.getAiPosts());
        if(Objects.isNull(pageNo) || Objects.isNull(pageSize) || Objects.isNull(direction)) {
            LOGGER.info("Social Post: pageNo, or pageSize or order is missing for page view");
            throw new BirdeyeSocialException(ErrorCodes.CLIENT_ERROR_400, "pageNo, or pageSize or order is missing for page view:");
        }


        if(direction.equals(PageSortDirection.ASC)) {
            socialPosts.sort(
                    Comparator.comparing(SocialSchedulePostMessage::getDatePublish)
                            .thenComparing(SocialSchedulePostMessage::getId)
            );
        }
        int startIndex = pageNo * pageSize;
        int endIndex = Math.min(startIndex + pageSize, socialPosts.size());
        if(endIndex < socialPosts.size()) socialSchedulePostResponse.setHasMorePosts(true);
        if (startIndex < endIndex) {
            socialPosts = socialPosts.subList(startIndex, endIndex);
        } else {
            socialPosts = new ArrayList<>();
        }

        socialSchedulePostResponse.setPosts(socialPosts.stream().collect(Collectors.groupingBy(
                map -> new SimpleDateFormat("MM/dd/yyyy").format(map.getDatePublish()))));

        return socialSchedulePostResponse;
    }

    private void addFilterDetails(GlobalFilterCriteriaSchedulePostMessage filter, List<Integer> publishState, List<Integer> sourceIds, List<String> approvalState) {
        // Use new field if available, else fallback
        List<FilterPostStatuses> postStatuses = filter.getPostStatuses();
        if (postStatuses != null && !postStatuses.isEmpty()) {
            for (FilterPostStatuses status : postStatuses) {
                switch (status) {
                    case POSTED:
                        publishState.add(SocialPostStatusEnum.PUBLISHED.getId());
                        publishState.add(SocialPostStatusEnum.FAILED.getId());
                        break;
                    case SCHEDULED:
                        publishState.add(SocialPostStatusEnum.SCHEDULED.getId());
                        break;
                    case REJECTED:
                        publishState.add(SocialPostStatusEnum.APPROVAL_REJECTED.getId());
                        break;
                    case EXPIRED:
                        publishState.add(SocialPostStatusEnum.APPROVAL_TERMINATED.getId());
                        break;
                    default:
                        publishState.addAll(Arrays.asList(0, 1, 2, 5, 6));
                }
            }
        } else {
            // Backward compatibility
            if (SocialPostStatusEnum.PUBLISHED.name().equalsIgnoreCase(filter.getPostStatus()) ||
                    SocialPostStatusEnum.FAILED.getName().equalsIgnoreCase(filter.getPostStatus())) {
                publishState.add(1);
                publishState.add(2);
            } else if (SocialPostStatusEnum.SCHEDULED.getName().equalsIgnoreCase(filter.getPostStatus())) {
                publishState.add(0);
            } else if (SocialPostStatusEnum.APPROVAL_TERMINATED.getName().equalsIgnoreCase(filter.getPostStatus())) {
                publishState.add(6);
            } else if (SocialPostStatusEnum.APPROVAL_REJECTED.getName().equalsIgnoreCase(filter.getPostStatus())) {
                publishState.add(5);
            } else {
                publishState.addAll(Arrays.asList(0, 1, 2, 5, 6));
            }
        }

        filterForApprovals(filter, approvalState);

        // Social channel filtering
        List<String> channels = filter.getSocialChannels();
        if (channels == null || channels.isEmpty()) {
            sourceIds.addAll(Arrays.asList(
                    SocialChannel.FACEBOOK.getId(),
                    SocialChannel.GOOGLE.getId(),
                    SocialChannel.TWITTER.getId(),
                    SocialChannel.INSTAGRAM.getId(),
                    SocialChannel.LINKEDIN.getId(),
                    SocialChannel.YOUTUBE.getId(),
                    SocialChannel.APPLE_CONNECT.getId(),
                    SocialChannel.TIKTOK.getId()
            ));
        } else {
            if (channels.contains("facebook")) sourceIds.add(SocialChannel.FACEBOOK.getId());
            if (channels.contains("google")) sourceIds.add(SocialChannel.GOOGLE.getId());
            if (channels.contains("twitter")) sourceIds.add(SocialChannel.TWITTER.getId());
            if (channels.contains("instagram")) sourceIds.add(SocialChannel.INSTAGRAM.getId());
            if (channels.contains("linkedin")) sourceIds.add(SocialChannel.LINKEDIN.getId());
            if (channels.contains("youtube")) sourceIds.add(SocialChannel.YOUTUBE.getId());
            if (channels.contains("apple_connect")) sourceIds.add(SocialChannel.APPLE_CONNECT.getId());
            if (channels.contains("tiktok")) sourceIds.add(SocialChannel.TIKTOK.getId());
        }
    }

    private void filterForApprovals(GlobalFilterCriteriaSchedulePostMessage filter, List<String> approvalState) {
        List<FilterPostStatuses> postStatuses = filter.getPostStatuses();
        if (postStatuses != null && !postStatuses.isEmpty()) {
            for (FilterPostStatuses status : postStatuses) {
                switch (status) {
                    case AWAITING_APPROVAL:
                        approvalState.add(ApprovalStatus.PENDING.getName());
                        break;
                    case REJECTED:
                        approvalState.add(ApprovalStatus.REJECTED.getName());
                        break;
                    case EXPIRED:
                        approvalState.add(ApprovalStatus.TERMINATED.getName());
                        break;
                }
            }
        } else {
            String legacyStatus = filter.getPostStatus();
            if (ApprovalStatus.APPROVED.getName().equalsIgnoreCase(legacyStatus)) {
                approvalState.add(ApprovalStatus.APPROVED.getName());
            } else if (ApprovalStatus.REJECTED.getName().equalsIgnoreCase(legacyStatus)) {
                approvalState.add(ApprovalStatus.REJECTED.getName());
            } else if (ApprovalStatus.PENDING.getName().equalsIgnoreCase(legacyStatus)) {
                approvalState.add(ApprovalStatus.PENDING.getName());
            } else if (ApprovalStatus.TERMINATED.getName().equalsIgnoreCase(legacyStatus)) {
                approvalState.add(ApprovalStatus.TERMINATED.getName());
            } else {
                approvalState.addAll(Arrays.asList(
                        ApprovalStatus.APPROVED.getName(),
                        ApprovalStatus.REJECTED.getName(),
                        ApprovalStatus.PENDING.getName(),
                        ApprovalStatus.TERMINATED.getName()
                ));
            }
        }
    }

    private List<SocialPostScheduleInfo> getLocationAccessInfo(List < Integer > accessibleLocations,
                                                               List < SocialPostScheduleInfo > socialPostScheduleInfoList,
                                                               Map < Integer, List < String >> hasAccessMap){

        if (CollectionUtils.isNotEmpty(accessibleLocations)) {
            List<SocialPostScheduleInfo> locationsScheduleInfo = new ArrayList<>();
            for (SocialPostScheduleInfo scheduleInfo : socialPostScheduleInfoList) {
                List<Integer> locations;
                if (CollectionUtils.isEmpty(scheduleInfo.getPageIds())) {
                    locations = socialPostService.fetchlocationsByScheduleCondition(scheduleInfo);
                } else {
                    // businessIds -> accessible business IDs
                    locations = socialPostService.getBusinessIdFromPageId(scheduleInfo.getSourceId(), scheduleInfo.getPageIds());
                    if (!new HashSet<>(accessibleLocations).containsAll(locations)) {
                        String channel = SocialChannel.getSocialChannelNameById(scheduleInfo.getSourceId());
                        List<String> channelList = hasAccessMap.get(scheduleInfo.getSocialPostId());
                        if (CollectionUtils.isEmpty(channelList)) {
                            channelList = new ArrayList<>();
                            channelList.add(channel);
                        } else {
                            if (!channelList.contains(channel)) {
                                channelList.add(channel);
                            }
                        }
                        hasAccessMap.put(scheduleInfo.getSocialPostId(), channelList);
                    }
                }
                if (!accessibleLocations.stream().distinct().filter(locations::contains).collect(Collectors.toList()).isEmpty()) {
                    locationsScheduleInfo.add(scheduleInfo);
                }
            }
            LOGGER.info("Location list: {}", locationsScheduleInfo.size());
            socialPostScheduleInfoList = locationsScheduleInfo;
        }
        LOGGER.info("socialPostScheduleInfoList: {}", socialPostScheduleInfoList.size());
        return socialPostScheduleInfoList;
    }

    private Map<String, Object> getPostFailedFlag (Set<Integer> accessibleLocationIds, SocialSchedulePostMessage
            postMessage, Map<Integer, List<SocialPostInfoRepository.PostPageStatus>> pagePostStatusMapByBizId, boolean hasFullAccess){

        Map<String, Object> output = new HashMap<>();
        Set<String> failedChannels = new HashSet<>();
        List<String> pageIds = new ArrayList<>();
        List<SocialPostInfoRepository.PostPageStatus> postPageStatus = pagePostStatusMapByBizId.get(postMessage.getId());

        output.put("isFailed", false);
        output.put("isVisible", false);
        int failedPageCount = 0;
        int passedCount = 0;
        int pendingCount = 0;

        if(postPageStatus != null) {
            for (SocialPostInfoRepository.PostPageStatus ps : postPageStatus) {
                if (hasFullAccess || CollectionUtils.isEmpty(accessibleLocationIds) || accessibleLocationIds.contains(ps.getBusinessId())) {
                    String publishStatus = socialPostService.convertPublishedStateToStatus(ps.getIsPublished(), ps.getBucket());
                    if (publishStatus.contains(SocialPostStatusEnum.FAILED.getName()) && !Objects.equals(ps.getIsDisable(), 1)) {
                        output.put("isFailed", true);
                    }
                    if (publishStatus.contains(SocialPostStatusEnum.FAILED.getName())) {
                        failedPageCount++;
                        failedChannels.add(SocialChannel.getSocialChannelNameById(ps.getSourceId()));
                    }
                    if ((!publishStatus.contains(SocialPostStatusEnum.DELETED.getName()))
                            &&(!publishStatus.contains(SocialPostStatusEnum.FAILED.getName()) || !Objects.equals(ps.getIsDisable(), 1))) {
                        output.put("isVisible", true);
                    }
                    pageIds.add(ps.getExternalPageId());
                    if(Objects.equals(ps.getIsPublished(), SocialPostStatusEnum.PUBLISHED.getId()) ||
                            (Objects.equals(ps.getIsPublished(), SocialPostStatusEnum.EDITED.getId()) && Objects.equals(ps.getEditedIsPublished(), SocialPostStatusEnum.FAILED.getId()))) {
                        passedCount++;
                    }
                    if(Objects.equals(ps.getIsPublished(), SocialPostStatusEnum.SCHEDULED.getId()) ||
                            Objects.equals(ps.getIsPublished(), SocialPostStatusEnum.PROCESSING.getId())) {
                        pendingCount++;
                    }
                }
            }
        }else{
            LOGGER.info("getting null postpagestatus for :{}", postMessage.getId());
        }
        output.put("failedChannelList", new ArrayList<>(failedChannels) );
        output.put("pageIds", pageIds);
        output.put("failedPageCount", failedPageCount);
        output.put("failedChannelCount", failedChannels.size());
        boolean isOperable = passedCount>0&&pendingCount==0;
        output.put("isOperationAllowed", isOperable);

        return output;
    }

    private Map<String, Object> getPostFailedFlagV2 (SocialSchedulePostMessage postMessage, Map<Integer, List<SocialPostInfoRepository.PostPageStatus>> pagePostStatusMapByBizId){

        Map<String, Object> output = new HashMap<>();
        Set<String> failedChannels = new HashSet<>();
        List<String> pageIds = new ArrayList<>();
        List<SocialPostInfoRepository.PostPageStatus> postPageStatus = pagePostStatusMapByBizId.get(postMessage.getId());

        output.put("isFailed", false);
        output.put("isVisible", false);
        int failedPageCount = 0;

        if(postPageStatus != null) {
            for (SocialPostInfoRepository.PostPageStatus ps : postPageStatus) {
                String publishStatus = socialPostService.convertPublishedStateToStatus(ps.getIsPublished(), ps.getBucket());
                if (publishStatus.contains(SocialPostStatusEnum.FAILED.getName()) && !Objects.equals(ps.getIsDisable(), 1)) {
                    output.put("isFailed", true);
                }
                if (publishStatus.contains(SocialPostStatusEnum.FAILED.getName())) {
                    failedPageCount++;
                    failedChannels.add(SocialChannel.getSocialChannelNameById(ps.getSourceId()));
                }
                if ((!publishStatus.contains(SocialPostStatusEnum.DELETED.getName()))
                        &&(!publishStatus.contains(SocialPostStatusEnum.FAILED.getName()) || !Objects.equals(ps.getIsDisable(), 1))) {
                    output.put("isVisible", true);
                }
                pageIds.add(ps.getExternalPageId());
            }
        }else{
            LOGGER.info("getting null postpagestatus for :{}", postMessage.getId());
        }
        output.put("failedChannelList", new ArrayList<>(failedChannels) );
        output.put("pageIds", pageIds);
        output.put("failedPageCount", failedPageCount);
        output.put("failedChannelCount", failedChannels.size());
        output.put("isOperationAllowed", false);

        return output;
    }
    private boolean isStoryExpired(Date fromDate) {
        long hourDiff = 0;
        try {
            String pattern = SQL_DATE_FORMAT;
            Instant instant = Instant.now();
            ZonedDateTime zonedDateTime = instant.atZone(ZoneId.of("UTC"));
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            String formattedDate = zonedDateTime.format(formatter);
            SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
            Date currDate = dateFormat.parse(formattedDate);
            Duration duration = Duration.between(fromDate.toInstant(), currDate.toInstant());
            hourDiff = duration.toHours();
            LOGGER.info("Diff: {}", hourDiff);
        } catch (Exception e) {
            LOGGER.info("Error occurred during hour calculation: ",e);
        }
        return  hourDiff>23;
    }

    private Integer getBusinessIdWherePostWasScheduled(SocialPostScheduleInfo socialPostScheduled, BusinessLiteDTO businessLiteDTO) {
        return socialPostService.isPostedFromReseller(socialPostScheduled) ? businessLiteDTO.getResellerId() : businessLiteDTO.getBusinessId();
    }

    private Integer getBusinessIdWherePostWasScheduled(String postMethod, BusinessLiteDTO businessLiteDTO) {
        return socialPostService.isPostedFromReseller(postMethod) ? businessLiteDTO.getResellerId() : businessLiteDTO.getBusinessId();
    }

    private Integer getBusinessIdWherePostWasScheduledV3(String postMethod, BusinessLiteDTO businessLiteDTO) {
        boolean flag  = Stream.of(ES_NAME_GROUPS, ES_NAME_BUSINESS_LOCATIONS, ES_NAME_BULK_RESELLER_POSTING).anyMatch(x -> x.equalsIgnoreCase(postMethod));
        return flag ? businessLiteDTO.getResellerId() : businessLiteDTO.getBusinessId();
    }

    private void processFacebookPages(Map<Integer, List<PagePermissionInfoLite>> srcIdVsPageInfoMap,
                                      Map<String, Boolean> pagePermissionMap, String module) {
        List<String> facebookPermissions = new ArrayList<>();
        SocialModulePermission socialModulePermission = socialModulePermissionService
                .getPermissionsForChannelAndModule(SocialChannel.FACEBOOK.getId(), module);
        if(Objects.nonNull(socialModulePermission) && Objects.nonNull(socialModulePermission.getPermissionsNeeded())) {
            List<String> modulePermissions = Arrays.asList(socialModulePermission.getPermissionsNeeded().split(","));
            facebookPermissions.addAll(modulePermissions);
        }

        srcIdVsPageInfoMap.get(SocialChannel.FACEBOOK.getId()).forEach(page -> {
            boolean isValid = true;
            if (page.getIsValid() == 0 || com.birdeye.social.utils.StringUtils.isEmpty(page.getPermissions())) {
                isValid = false;
            }
            List<String> permissions = Arrays.stream(page.getPermissions().split(",")).collect(Collectors.toList());
            if(!new HashSet<>(permissions).containsAll(facebookPermissions)){
                LOGGER.info("[monitor log] permission issue for page, FB: {}", page.getPageId());
                isValid = false;
            }
            pagePermissionMap.put(page.getPageId(),isValid);
        });
    }
    private void processTwitterAccounts(Map<Integer, List<PagePermissionInfoLite>> srcIdVsPageInfoMap, Map<String, Boolean> pagePermissionMap, String module) {
        List<String> twitterPermissions = new ArrayList<>();
        SocialModulePermission socialModulePermission = socialModulePermissionService
                .getPermissionsForChannelAndModule(SocialChannel.TWITTER.getId(), module);
        if(Objects.nonNull(socialModulePermission) && Objects.nonNull(socialModulePermission.getPermissionsNeeded())) {
            List<String> modulePermissions = Arrays.asList(socialModulePermission.getPermissionsNeeded().split(","));
            twitterPermissions.addAll(modulePermissions);
        }

        srcIdVsPageInfoMap.get(SocialChannel.TWITTER.getId()).forEach(account -> {
            boolean isValid = true;
            if(account.getIsValid() == 0 || Objects.isNull(account.getPermissions())){
                isValid = false;
            }
            List<String> permissions = Arrays.stream(account.getPermissions().split(",")).collect(Collectors.toList());
            if(!new HashSet<>(permissions).containsAll(twitterPermissions)){
                LOGGER.info("[monitor log] permission issue for page, Twitter: {}", account.getPageId());
                isValid = false;
            }
            pagePermissionMap.put(account.getPageId(),isValid);
        });
    }

    private void processGoogleMyBusinessLocations(Map<Integer, List<PagePermissionInfoLite>> srcIdVsPageInfoMap, Map<String, Boolean> pagePermissionMap, String module) {
        List<String> gmbPermissions = new ArrayList<>();
        SocialModulePermission socialModulePermission = socialModulePermissionService
                .getPermissionsForChannelAndModule(SocialChannel.GOOGLE.getId(), module);
        if(Objects.nonNull(socialModulePermission) && Objects.nonNull(socialModulePermission.getPermissionsNeeded())) {
            List<String> modulePermissions = Arrays
                    .asList(socialModulePermission.getPermissionsNeeded().split(","));
            gmbPermissions.addAll(modulePermissions);
        }

        srcIdVsPageInfoMap.get(SocialChannel.GOOGLE.getId()).forEach(account -> {
            boolean isValid = true;
            if(account.getIsValid() == 0 || Objects.isNull(account.getPermissions())){
                isValid = false;
            }
            List<String> permissions = Arrays.stream(account.getPermissions().replaceAll("\\s","").split(",")).collect(Collectors.toList());
            if(!new HashSet<>(permissions).containsAll(gmbPermissions)){
                LOGGER.info("[monitor log] permission issue for page, GMB: {}", account.getPageId());
                isValid = false;
            }
            pagePermissionMap.put(account.getPageId(),isValid);
        });
    }
    private void processInstagramAccounts(Map<Integer, List<PagePermissionInfoLite>> srcIdVsPageInfoMap, Map<String, Boolean> pagePermissionMap, String module) {
        List<String> instagramPermissions = new ArrayList<>();
        SocialModulePermission socialModulePermission = socialModulePermissionService
                .getPermissionsForChannelAndModule(SocialChannel.INSTAGRAM.getId(), module);
        if(Objects.nonNull(socialModulePermission) && Objects.nonNull(socialModulePermission.getPermissionsNeeded())) {
            List<String> modulePermissions = Arrays
                    .asList(socialModulePermission.getPermissionsNeeded().split(","));
            instagramPermissions.addAll(modulePermissions);
        }

        srcIdVsPageInfoMap.get(SocialChannel.INSTAGRAM.getId()).forEach(account -> {
            boolean isValid = true;
            if(account.getIsValid() == 0 || Objects.isNull(account.getPermissions())){
                isValid = false;
            }
            List<String> permissions = Arrays.stream(account.getPermissions().split(",")).collect(Collectors.toList());
            if(!new HashSet<>(permissions).containsAll(instagramPermissions)){
                LOGGER.info("[monitor log] permission issue for page, IG: {}", account.getPageId());
                isValid = false;
            }
            pagePermissionMap.put(account.getPageId(),isValid);
        });
    }
    private void processLinkedinPages(Map<Integer, List<PagePermissionInfoLite>> srcIdVsPageInfoMap, Map<String, Boolean> pagePermissionMap) {

        srcIdVsPageInfoMap.get(SocialChannel.LINKEDIN.getId()).forEach(account -> {
            boolean isValid = true;
            if(account.getIsValid() == 0){
                isValid = false;
            }
            if(StringUtils.isNotEmpty(account.getPermissions()) && account.getPermissions().contains(EnabledTasks.POSTS.name())){
                LOGGER.info("[monitor log] permission issue for page, LN: {}", account.getPageId());
                isValid = false;
            }
            pagePermissionMap.put(account.getPageId(),isValid);
        });
    }
    private void processYoutubeChannels(Map<Integer, List<PagePermissionInfoLite>> srcIdVsPageInfoMap, Map<String, Boolean> pagePermissionMap) {
        srcIdVsPageInfoMap.get(SocialChannel.YOUTUBE.getId()).forEach(account -> {
            boolean isValid = account.getIsValid() != 0;
            pagePermissionMap.put(account.getPageId(),isValid);
        });
    }
    private void processAppleLocations(Map<Integer, List<PagePermissionInfoLite>> srcIdVsPageInfoMap, Map<String, Boolean> pagePermissionMap) {
        srcIdVsPageInfoMap.get(SocialChannel.APPLE_CONNECT.getId()).forEach(account -> {
            pagePermissionMap.put(account.getPageId(),true);
        });
    }

    private void processTiktokLocations(Map<Integer, List<PagePermissionInfoLite>> srcIdVsPageInfoMap, Map<String, Boolean> pagePermissionMap, String module) {
        List<String> tiktokPermissions = new ArrayList<>();
        SocialModulePermission socialModulePermission = socialModulePermissionService
                .getPermissionsForChannelAndModule(SocialChannel.TIKTOK.getId(), module);
        if(Objects.nonNull(socialModulePermission) && Objects.nonNull(socialModulePermission.getPermissionsNeeded())) {
            List<String> modulePermissions = Arrays
                    .asList(socialModulePermission.getPermissionsNeeded().split(","));
            tiktokPermissions.addAll(modulePermissions);
        }
        srcIdVsPageInfoMap.get(SocialChannel.TIKTOK.getId()).forEach(account -> {
            boolean isValid = true;
            if(account.getIsValid() == 0 || Objects.isNull(account.getPermissions())){
                isValid = false;
            }
            List<String> permissions = Arrays.stream(account.getPermissions().split(",")).collect(Collectors.toList());
            if(!new HashSet<>(permissions).containsAll(tiktokPermissions)){
                LOGGER.info("[monitor log] permission issue for page, TikTok: {}", account.getPageId());
                isValid = false;
            }
            pagePermissionMap.put(account.getPageId(),isValid);
        });
    }

    private Map<String, Boolean> populatePermissionMappingMap(Map<Integer,List<PagePermissionInfoLite>> srcIdVsPageInfoMap) {
        Map<String, Boolean> pagePermissionMap = new HashMap<>();
        String module = "PUBLISH";
        for(Map.Entry<Integer, List<PagePermissionInfoLite>> entry: srcIdVsPageInfoMap.entrySet()) {
            if (Objects.equals(entry.getKey(), SocialChannel.FACEBOOK.getId())) {
                processFacebookPages(srcIdVsPageInfoMap, pagePermissionMap, module);
            }  else if (Objects.equals(entry.getKey(), SocialChannel.TWITTER.getId())) {
                processTwitterAccounts(srcIdVsPageInfoMap, pagePermissionMap, module);
            } else if (Objects.equals(entry.getKey(), SocialChannel.GMB.getId())) {
                processGoogleMyBusinessLocations(srcIdVsPageInfoMap, pagePermissionMap, module);
            } else if (Objects.equals(entry.getKey(), SocialChannel.INSTAGRAM.getId())) {
                processInstagramAccounts(srcIdVsPageInfoMap, pagePermissionMap, module);
            } else if (Objects.equals(entry.getKey(), SocialChannel.LINKEDIN.getId())) {
                processLinkedinPages(srcIdVsPageInfoMap, pagePermissionMap);
            } else if (Objects.equals(entry.getKey(), SocialChannel.YOUTUBE.getId())) {
                processYoutubeChannels(srcIdVsPageInfoMap, pagePermissionMap);
            } else if (Objects.equals(entry.getKey(), SocialChannel.APPLE_CONNECT.getId())) {
                processAppleLocations(srcIdVsPageInfoMap, pagePermissionMap);
            } else if (Objects.equals(entry.getKey(), SocialChannel.TIKTOK.getId())) {
                processTiktokLocations(srcIdVsPageInfoMap, pagePermissionMap, module);
            }
        }
        return pagePermissionMap;
    }

    private List<SocialPostCalendarMessage> removeApplePosts(List<SocialPostCalendarMessage> allPostList) {
        List<SocialPostCalendarMessage> applePosts = allPostList.stream()
                .filter(post -> post.getSourceId() == 409)
                .collect(Collectors.toList());

        allPostList.removeIf(post -> post.getSourceId() == 409);

        return applePosts;
    }

    public SocialSchedulePostResponse getAllESScheduledPosts(GlobalFilterCriteriaSchedulePostMessage filter,
                                                             Integer userId, boolean isListViewRequest, String source,
                                                             String timezone) throws Exception {
        try {
            if(!socialPostCalendarESService.isGetFlag(filter.getBusinessId())) {
                return getAllScheduledPostsV2(filter,userId, isListViewRequest, source);
            }
            long startTime = System.currentTimeMillis();
            Date startDate = new SimpleDateFormat(SCHEDULE_DATE_FORMAT).parse(filter.getStartDate());
            Date endDate = new SimpleDateFormat(SCHEDULE_DATE_FORMAT).parse(filter.getEndDate());
            Map<Integer, SocialPost> socialPostMap;
            List<Integer> sourceIds = new ArrayList<>();
            List<Integer> publishState = new ArrayList<>();
            List<String> approvalState = new ArrayList<>();
            Map<Integer, List<SocialPostInfoRepository.PostPageStatus>> postPageStatusMap = new HashMap<>();
            Map<String, Boolean> pagePermissionMap = new HashMap<>();
            addFilterDetails(filter, publishState, sourceIds,approvalState);
            Map<Integer, List<String>> hasAccessMap = new HashMap<>();
            LOGGER.info("Get data from ES for business ID : {}",filter.getBusinessId());
            SocialSchedulePostResponse socialSchedulePostResponse = new SocialSchedulePostResponse();
            List<SocialPostCalendarMessage> socialSchedulePostMessageESList =
                    socialPostCalendarESService.searchFromEsIndex(filter.getBusinessId(),
                            startDate, endDate, sourceIds, publishState, filter.getCreators(), filter.getApprovals(),
                            null, filter.getTagIds(), filter.getPostType(), filter.getPostContent());
            Map<Boolean, List<SocialPostCalendarMessage>> partitionedPosts = socialSchedulePostMessageESList.stream()
                    .collect(Collectors.partitioningBy(post -> Boolean.TRUE.equals(post.isAiGenerated())));

            List<SocialPostCalendarMessage> aiGeneratedPosts = partitionedPosts.get(true);
            socialSchedulePostMessageESList = partitionedPosts.get(false);

            if(CollectionUtils.isEmpty(socialSchedulePostMessageESList) && CollectionUtils.isEmpty(aiGeneratedPosts)){
                LOGGER.info("No posts data found in ES for the request: {}, returning an empty response.",filter);
                return new SocialSchedulePostResponse();
            }
            if(CollectionUtils.isNotEmpty(socialSchedulePostMessageESList)) {
                LOGGER.info("Posts: {}", socialSchedulePostMessageESList.stream().map(SocialPostCalendarMessage::getId).collect(Collectors.toList()));
                LOGGER.info("checkpoint 001==============, time taken {} ms", System.currentTimeMillis() - startTime);
                Map<String, Integer> pageIdVsLocationIdMap =
                        getPageIdVsLocationIdMapV2(socialSchedulePostMessageESList, pagePermissionMap);
                long checkpoint1Time = System.currentTimeMillis();
                LOGGER.info("checkpoint 1==============, time taken {} ms", checkpoint1Time - startTime);

                List<SocialPostCalendarMessage> finalSocialPostScheduleInfoListAll = socialSchedulePostMessageESList;
                List<SocialPostCalendarMessage> finalSocialPostCalendarMessageList = socialSchedulePostMessageESList;
                CompletableFuture<List<SocialPostCalendarMessage>> socialPostScheduleInfoListFAll =
                        CompletableFuture.supplyAsync(() -> getSocialPostSchInfoWithLocAllV3(filter.getAccessibleLocationIds(),
                                finalSocialPostScheduleInfoListAll, hasAccessMap, pageIdVsLocationIdMap));

                CompletableFuture<List<SocialPostCalendarMessage>> socialPostScheduleInfoListF =
                        CompletableFuture.supplyAsync(() -> getSocialPostSchInfoWithLocV3(finalSocialPostCalendarMessageList,
                                filter.getBusinessIds()));

                //CompletableFuture<BusinessLiteDTO> getBusinessDetails = CompletableFuture.supplyAsync(() -> businessCoreService.getBusinessLite(filter.getBusinessId(), false));
                CompletableFuture<Void> executor1 =
                        CompletableFuture.allOf(socialPostScheduleInfoListFAll, socialPostScheduleInfoListF);
                executor1.get(1000, TimeUnit.SECONDS);
                BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(filter.getBusinessId(), false);//getBusinessDetails.get();
                List<SocialPostCalendarMessage> socialSchedulePostMessageESListAll = socialPostScheduleInfoListFAll.get();


                socialSchedulePostMessageESList = socialPostScheduleInfoListF.get();
//            socialPostScheduleInfoList = socialPostScheduleInfoListF.get();
                long checkpoint2Time = System.currentTimeMillis();
                LOGGER.info("checkpoint 2==============, time taken {} ms", checkpoint2Time - startTime);
                socialSchedulePostMessageESList =
                        socialSchedulePostMessageESList.stream().distinct().
                                filter(socialSchedulePostMessageESListAll::contains).collect(Collectors.toList());

                Set<Integer> distinctPostIds = new HashSet<>();
//                Set<Long> distinctPostIdsLong = new HashSet<>();
                for (SocialPostCalendarMessage scheduleInfo : socialSchedulePostMessageESList) {
                    Integer postId = scheduleInfo.getId();
                    if (Objects.isNull(postId)) {
                        continue;
                    }
//                    distinctPostIdsLong.add(Long.valueOf(postId));
                    distinctPostIds.add(postId);
                }
                List<SocialPost> socialPostList = socialPostService.getSocialPost(distinctPostIds, approvalState, filter.getApprovals(), filter.getCreators(), filter.getPostType(), filter.getPostContent());
                LOGGER.info("socialPostList: {}", socialPostList);
                Future<Map<Integer, BusinessCoreUser>> getUserDetailFuture = executor.submit(() -> {
                    if (CollectionUtils.isNotEmpty(socialPostList)) {
                        return businessCoreService.getBusinessUserForUserId(socialPostList.stream()
                                .map(SocialPost::getCreatedBy).collect(Collectors.toList()));
                    } else {
                        return new HashMap<>();
                    }
                });
                if (CollectionUtils.isEmpty(socialPostList)) {
                    new SocialSchedulePostResponse();
                }
                CompletableFuture<Map<Integer, SocialPostsAssets>> getPostsAssetsDetail =
                        CompletableFuture.supplyAsync(() -> postAssetService.getPostAssetsForList(socialPostList));
                CompletableFuture<Void> parallelExecuter = CompletableFuture.allOf(getPostsAssetsDetail);

                parallelExecuter.get(100, TimeUnit.SECONDS);
                Map<Integer, SocialPostsAssets> postAssetsMap = getPostsAssetsDetail.get();


                LOGGER.info("checkpoint 3============== {}", System.currentTimeMillis() - startTime);
//            LOGGER.info("before socialPostScheduleInfoList {}", socialPostScheduleInfoList);


                socialPostMap = socialPostService.generateMapForIdAndSocialPost(socialPostList);
                distinctPostIds.retainAll(socialPostMap.keySet());
                List<PostActivityRepo.ActivityCount> postVsDuplicateCount = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(distinctPostIds)) {
                    postVsDuplicateCount = postActivityRepo.getPostWiseActivityCount(distinctPostIds, PostActivityType.DUPLICATE.getName());
                }

                List<SocialPostInfoRepository.PostPageStatus> postPageStatusList =
                        socialPostPublishInfoRepository.findBySocialPostIdInAndSourceIdIn(distinctPostIds, sourceIds);
                if (CollectionUtils.isNotEmpty(postPageStatusList)) {
                    postPageStatusMap = postPageStatusList.stream().collect(Collectors.groupingBy(
                            SocialPostInfoRepository.PostPageStatus::getSocialPostId,
                            Collectors.mapping(obj -> obj, Collectors.toList())));
                }
                Map<Integer, Integer> postVsDuplicateCountMap = postVsDuplicateCount.stream()
                        .collect(Collectors.toMap(PostActivityRepo.ActivityCount::getPostId, PostActivityRepo.ActivityCount::getActivityCount));
                long checkpoint5Time = System.currentTimeMillis();
                LOGGER.info("checkpoint 4==============, time taken {} ms", checkpoint5Time - startTime);
                List<SocialSchedulePostMessage> socialSchedulePostMessageList = new ArrayList<>();
                Map<Integer, BusinessCoreUser> userDetailMap = getUserDetailFuture.get();
                LOGGER.info("Permission mapping size: {}", pagePermissionMap.size());

                timezone = StringUtils.isEmpty(timezone) ? "UTC" : timezone;
                if (Objects.isNull(filter.getPageSize()) || filter.getPageSize() < 1) {
                    filter.setPageSize(Integer.MAX_VALUE);
                }

                List<SocialPostCalendarMessage> applePostList = removeApplePosts(socialSchedulePostMessageESList);

                Map<String, List<SocialPostCalendarMessage>> groupedPosts = groupByFormattedDate(socialSchedulePostMessageESList, timezone);
                Map<String, List<SocialSchedulePostMessage>> result = new HashMap<>();
                long checkpoint6Time = System.currentTimeMillis();
                Set<Integer> accessibleLocationSet = CollectionUtils.isNotEmpty(filter.getAccessibleLocationIds())
                        ? new HashSet<>(filter.getAccessibleLocationIds()) : new HashSet<>();
                for (Map.Entry<String, List<SocialPostCalendarMessage>> entry : groupedPosts.entrySet()) {
                    String date = entry.getKey();
                    List<SocialPostCalendarMessage> postList = entry.getValue();

                    result.computeIfAbsent(date, k -> new ArrayList<>());

                    for (SocialPostCalendarMessage socialPostScheduled : postList) {
                        long checkpoint6_1Time = System.currentTimeMillis();
                        LOGGER.info("checkpoint 4.1 ==============, time taken {} ms", checkpoint6_1Time - checkpoint6Time);
                        if (!distinctPostIds.contains(socialPostScheduled.getId())) continue;
                        SocialSchedulePostMessage socialSchedulePostMessage = getSocialSchedulePostMessageV3(filter, userId,
                                socialPostMap, userDetailMap, postPageStatusMap, pagePermissionMap, hasAccessMap,
                                businessLiteDTO, postAssetsMap, postVsDuplicateCountMap, socialPostScheduled, accessibleLocationSet);

                        if (socialSchedulePostMessage == null) continue;
                        long checkpoint6_2Time = System.currentTimeMillis();
                        LOGGER.info("checkpoint 4.2 ==============, time taken {} ms", checkpoint6_2Time - checkpoint6_1Time);
                        result.get(date).add(socialSchedulePostMessage);
                        long checkpoint6_3Time = System.currentTimeMillis();
                        LOGGER.info("checkpoint 4.3 ==============, time taken {} ms", checkpoint6_3Time - checkpoint6_2Time);
                        if (result.get(date).size() >= filter.getPageSize()) break;
                    }
                }
                long checkpoint7Time = System.currentTimeMillis();
                LOGGER.info("checkpoint 4.4 ==============, time taken {} ms", checkpoint7Time - checkpoint6Time);

                if (CollectionUtils.isNotEmpty(applePostList)) {
                    addApplePosts(filter, userId, socialPostMap, postPageStatusMap, pagePermissionMap,
                            hasAccessMap, businessLiteDTO, distinctPostIds, postAssetsMap,
                            postVsDuplicateCountMap, socialSchedulePostMessageList, userDetailMap, applePostList, result, accessibleLocationSet);
                }

                long checkpoint8Time = System.currentTimeMillis();
                LOGGER.info("checkpoint 5==============, time taken {} ms", checkpoint8Time - startTime);
                socialSchedulePostResponse.setPosts(result);
            }
            if(CollectionUtils.isNotEmpty(aiGeneratedPosts) && Boolean.TRUE.equals(filter.getShowAiSuggestions())) {
                LOGGER.info("AI posts size: {}", aiGeneratedPosts.size());
                socialSchedulePostResponse.setAiPosts(getAiPostsFromList(aiGeneratedPosts, filter, userId, isListViewRequest));
            }
            if(isListViewRequest) {
                LOGGER.info("Creating paginated view for listViewRequest");
                return createPaginationForListViewWithAIPosts(socialSchedulePostResponse, filter.getPageNo(), filter.getPageSize(), filter.getOrder());
            } else {
                return socialSchedulePostResponse;
            }
        } catch (Exception e) {
            LOGGER.info("Social Post: something went wrong while fetching data for filter {} e ", filter, e);
            throw new BirdeyeSocialException(e.getMessage());
        }
    }

    private SocialSchedulePostResponse createPaginationForListViewWithAIPosts(SocialSchedulePostResponse socialSchedulePostResponse,
                                                Integer pageNo, Integer pageSize, PageSortDirection order) {

        SocialSchedulePostResponse response = new SocialSchedulePostResponse();
        // Combine all posts and aiPosts into a single list
        List<SocialSchedulePostMessage> allPosts = new ArrayList<>();
        if (MapUtils.isNotEmpty(socialSchedulePostResponse.getPosts())) {
            LOGGER.info("Total posts size: {}", socialSchedulePostResponse.getPosts().size());
            socialSchedulePostResponse.getPosts().values().forEach(allPosts::addAll);
        }
        if (MapUtils.isNotEmpty(socialSchedulePostResponse.getAiPosts())) {
            LOGGER.info("Total AI posts size: {}", socialSchedulePostResponse.getAiPosts().size());
            socialSchedulePostResponse.getAiPosts().values().forEach(allPosts::addAll);
        }

        if(CollectionUtils.isEmpty(allPosts)) {
            LOGGER.info("No posts found for the given filter criteria.");
            return response;
        }
        LOGGER.info("Total combined posts size: {}", allPosts.size());

        // Sort the combined list based on order
        if (order == PageSortDirection.ASC) {
            LOGGER.info("Sorting order ASC");
            allPosts.sort(Comparator.comparing(SocialSchedulePostMessage::getDatePublish));
        } else {
            LOGGER.info("Sorting order DESC");
            allPosts.sort(Comparator.comparing(SocialSchedulePostMessage::getDatePublish).reversed());
        }

        // Paginate the list
        int startIndex = pageNo * pageSize;
        int endIndex = Math.min(startIndex + pageSize, allPosts.size());
        List<SocialSchedulePostMessage> paginatedPosts = (startIndex < endIndex)
                ? allPosts.subList(startIndex, endIndex)
                : Collections.emptyList();


        // Separate paginated posts into two maps
        Map<String, List<SocialSchedulePostMessage>> postsMap = paginatedPosts.stream()
                .filter(post -> !post.getAiSuggestion()) // Filter non-AI posts
                .collect(Collectors.groupingBy(
                        post -> new SimpleDateFormat("MM/dd/yyyy").format(post.getDatePublish())
                ));

        LOGGER.info("Post MAP: {}", postsMap);

        Map<String, List<SocialSchedulePostMessage>> aiPostsMap = paginatedPosts.stream()
                .filter(SocialSchedulePostMessage::getAiSuggestion) // Filter AI posts
                .collect(Collectors.groupingBy(
                        post -> new SimpleDateFormat("MM/dd/yyyy").format(post.getDatePublish())
                ));

        LOGGER.info("AI Post MAP: {}", aiPostsMap);

        // Set the paginated posts and AI posts in the response
        response.setPosts(postsMap);
        response.setAiPosts(aiPostsMap);
        LOGGER.info("startIndex: {}, endIndex: {}, allPost: {}", startIndex, endIndex, allPosts);
        if(endIndex < allPosts.size())  {
            LOGGER.info("Setting has more posts to true");
            response.setHasMorePosts(true);
        }
        return response;
    }

    private void addApplePosts(GlobalFilterCriteriaSchedulePostMessage filter, Integer userId, Map<Integer, SocialPost> socialPostMap,
                               Map<Integer, List<SocialPostInfoRepository.PostPageStatus>> postPageStatusMap, Map<String, Boolean> pagePermissionMap,
                               Map<Integer, List<String>> hasAccessMap, BusinessLiteDTO businessLiteDTO, Set<Integer> distinctPostIds, Map<Integer, SocialPostsAssets> postAssetsMap,
                               Map<Integer, Integer> postVsDuplicateCountMap, List<SocialSchedulePostMessage> socialSchedulePostMessageList,
                               Map<Integer, BusinessCoreUser> userDetailMap, List<SocialPostCalendarMessage> applePostList, Map<String, List<SocialSchedulePostMessage>> result,
                               Set<Integer> accessibleLocationIds) throws Exception {

        for(SocialPostCalendarMessage socialPostScheduled : applePostList.stream().
                filter(post -> distinctPostIds.contains(post.getId())).collect(Collectors.toList())) {

            SocialSchedulePostMessage socialSchedulePostMessage = getSocialSchedulePostMessageV3(filter, userId,
                    socialPostMap, userDetailMap, postPageStatusMap, pagePermissionMap, hasAccessMap,
                    businessLiteDTO, postAssetsMap, postVsDuplicateCountMap, socialPostScheduled,accessibleLocationIds);
            if (socialSchedulePostMessage == null) continue;
            socialSchedulePostMessageList.add(socialSchedulePostMessage);
        }

        Map<String, List<SocialSchedulePostMessage>> applePostMap;
        applePostMap = socialSchedulePostMessageList.stream().collect(Collectors.groupingBy(
                map -> new SimpleDateFormat("MM/dd/yyyy").format(map.getDatePublish())));

        //merge apple posts and non apple posts
        applePostMap.forEach((key, value) ->
                result.merge(key, value, (list1, list2) -> {
                    List<SocialSchedulePostMessage> combinedList = new ArrayList<>(list1);
                    combinedList.addAll(list2);
                    return combinedList;
                })
        );
    }

    private Map<String, List<SocialPostCalendarMessage>> groupByFormattedDate(List<SocialPostCalendarMessage> socialSchedulePostMessageESList,
                                                                              String timeZone) {
        SimpleDateFormat outputFormat = new SimpleDateFormat("MM/dd/yyyy");
        SimpleDateFormat inputFormat = new SimpleDateFormat(SQL_DATE_FORMAT);

        return socialSchedulePostMessageESList.stream()
                .filter(message -> {
                    try {
                        inputFormat.parse(message.getDatePublish());
                        return true;
                    } catch (Exception e) {
                        LOGGER.info("Invalid date format, postId: {} date: {}", message.getId(),message.getDatePublish());
                        return false;
                    }
                })
                .collect(Collectors.groupingBy(
                        message -> {
                            try {
                                Date parsedDate = inputFormat.parse(message.getDatePublish());
                                Calendar convertedCalendar = TimeZoneUtil.convertToSpecificTimeZone(parsedDate, timeZone);
                                return outputFormat.format(convertedCalendar.getTime());
                            } catch (Exception e) {
                                throw new RuntimeException("Unexpected format exception", e);
                            }
                        },
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(SocialPostCalendarMessage::getDatePublish)
                                                .thenComparing(SocialPostCalendarMessage::getId))
                                        .collect(Collectors.toList())
                        )
                ));
    }


    @Nullable
    private SocialSchedulePostMessage getSocialSchedulePostMessageV3(GlobalFilterCriteriaSchedulePostMessage filter,
                                                                     Integer userId, Map<Integer, SocialPost> socialPostMap,
                                                                     Map<Integer, BusinessCoreUser> userDetailMap,
                                                                     Map<Integer, List<SocialPostInfoRepository.PostPageStatus>> postPageStatusMap,
                                                                     Map<String, Boolean> pagePermissionMap,
                                                                     Map<Integer, List<String>> hasAccessMap, BusinessLiteDTO businessLiteDTO,
                                                                     Map<Integer, SocialPostsAssets> postAssetsMap,
                                                                     Map<Integer, Integer> postVsDuplicateCountMap,
                                                                     SocialPostCalendarMessage socialPostCalendarMessage,
                                                                     Set<Integer> accessibleLocationSet) throws Exception {
        SocialSchedulePostMessage socialSchedulePostMessage = new SocialSchedulePostMessage();
        Date utcDate = socialPostCalendarMessage.getPublishDate();;
        String utcFormat = new SimpleDateFormat(SCHEDULE_DATE_FORMAT).format(utcDate);
        socialSchedulePostMessage.setId(socialPostCalendarMessage.getId());
        socialSchedulePostMessage.setDatePublish(socialPostCalendarMessage.getPublishDate());
        socialSchedulePostMessage.setScheduleInfoId(socialPostCalendarMessage.getScheduleInfoId());
        socialSchedulePostMessage.setPublishDate(utcFormat);
        socialSchedulePostMessage.setPublishedBy(null);
        socialSchedulePostMessage.setIsPublished(socialPostCalendarMessage.getIsPublished());

        socialSchedulePostMessage.setTags(socialPostCalendarMessage.getTags());
        long checkpoint1Time = System.currentTimeMillis();
        //apple metadata setter
        if (socialPostCalendarMessage.getSourceId() == SocialChannel.APPLE_CONNECT.getId()) {
            socialPostService.setAppleMetaData(socialSchedulePostMessage, socialPostCalendarMessage.getId());
            //apple publish status
            List<SocialPostInfoRepository.PostPageStatus> postPageStatus = postPageStatusMap.get(socialPostCalendarMessage.getId());
            if (CollectionUtils.isNotEmpty(postPageStatus)) {
                ApplePublishInfoMetadata applePublishInfoMetadata =
                        applePublishInfoMetadataRepository.findByPublishInfoId(postPageStatus.get(0).getId());
                socialSchedulePostMessage.setApplePublishStatus(applePublishStatus(applePublishInfoMetadata.getPublishStatus()));

            }
        }

        List<String> socialChannelEnabled = getEnabledSocialChannelList(socialPostCalendarMessage.getSourceId());
        socialSchedulePostMessage.setPostingSites(socialChannelEnabled);


        if (socialSchedulePostMessage.getIsPublished() == 0) {
            socialSchedulePostMessage.setPermissionStatus(getPermissionFromMap(pagePermissionMap,
                    socialPostCalendarMessage.getSourceId(), socialChannelEnabled, socialPostCalendarMessage.getPageIds()));
        }
        if (socialSchedulePostMessage.getIsPublished() == 1) {
            Map<String, Object> output = getPostFailedFlag(accessibleLocationSet,
                    socialSchedulePostMessage, postPageStatusMap, filter.getHasFullAccess());
            //LOGGER.info("Between 2 and 3 logs ");
            socialSchedulePostMessage.setHasPostFailed((Boolean) output.get("isFailed"));
            if (socialSchedulePostMessage.getHasPostFailed()) {
                socialSchedulePostMessage.setFailedPageCount((Integer) output.get("failedPageCount"));
                socialSchedulePostMessage.setFailedChannelCount((Integer) output.get("failedChannelCount"));
                socialSchedulePostMessage.setFailedSites((List<String>) output.get("failedChannelList"));
            }
            socialSchedulePostMessage.setIsOperationAllowed((Boolean) output.get("isOperationAllowed"));
            if (!(Boolean) output.get("isVisible")) {
                return null;
            }
        }
        long checkpoint2Time = System.currentTimeMillis();
        LOGGER.info("checkpoint 1.1==============, time taken {} ms", checkpoint2Time - checkpoint1Time);
        if (MapUtils.isNotEmpty(userDetailMap) && Objects.nonNull(socialPostCalendarMessage.getCreatedBy()) &&
                userDetailMap.containsKey(socialPostCalendarMessage.getCreatedBy())) {
            socialSchedulePostMessage.setCreatedByName(businessCoreService.getFullUsername(userDetailMap.get(socialPostCalendarMessage.getCreatedBy())));
        }
        Integer enterpriseId;
        if (Objects.nonNull(socialPostCalendarMessage.getMediaSequence())) {
            socialSchedulePostMessage.setMediaSequence(socialPostCalendarMessage.getMediaSequence());
        } else if (StringUtils.isNotEmpty(socialPostCalendarMessage.getPostMethod())) {
            LOGGER.info("Fetching media seq using Post meta data");
            enterpriseId = getBusinessIdWherePostWasScheduledV3(socialPostCalendarMessage.getPostMethod(), businessLiteDTO);
            socialSchedulePostMessage.setMediaSequence(socialPostService.getMediaSequence(socialPostCalendarMessage, enterpriseId));
        }

        if (CollectionUtils.isNotEmpty(socialPostCalendarMessage.getApprovalUserIds())) {
            socialSchedulePostMessage.setIsApprover(socialPostCalendarMessage.getApprovalUserIds().contains(String.valueOf(userId)));
        }
        socialSchedulePostMessage.setIsCreator(Objects.equals(socialPostCalendarMessage.getCreatedBy(), userId));
        socialSchedulePostMessage.setPostText(socialPostCalendarMessage.getPostText());
        socialSchedulePostMessage.setAiPost(socialPostCalendarMessage.getAiPost());
        socialSchedulePostMessage.setApprovalStatus(socialPostCalendarMessage.getApprovalStatus());
        socialSchedulePostMessage.setApproveWorkflowId(socialPostCalendarMessage.getApprovalWorkflowId());
        socialSchedulePostMessage.setApprovalUUId(socialPostCalendarMessage.getApprovalUUId());
        socialSchedulePostMessage.setConversationId(socialPostCalendarMessage.getConversationId());
        socialSchedulePostMessage.setApprovalRequestId(socialPostCalendarMessage.getApprovalRequestId());
        socialSchedulePostMessage.setReferenceStepId(socialPostCalendarMessage.getReferenceStepId());
        if (!Objects.isNull(socialPostCalendarMessage.getPostMetaData()) && socialPostCalendarMessage.getSourceId() == SocialChannel.GMB.getId()) {
            GoogleOfferDetails googleOfferDetails = convertToGMBOfferResponse(socialPostCalendarMessage.getPostMetaData(), null, null);
            if (Objects.nonNull(googleOfferDetails)) {
                socialSchedulePostMessage.setGmbOfferDetails(googleOfferDetails);
                socialSchedulePostMessage.setType(GOOGLE_OFFER);
            }
        }
        long checkpoint3Time = System.currentTimeMillis();
        LOGGER.info("checkpoint 1.1==============, time taken {} ms", checkpoint3Time - checkpoint2Time);
        if (CollectionUtils.isNotEmpty(socialPostCalendarMessage.getImageIds())) {
            List<SocialPostsAssets> imageAssets = postAssetService.getPostsAssetsById(postAssetsMap, socialPostCalendarMessage.getImageIds());
            socialSchedulePostMessage.setImages(postAssetService.getMediaDataV2(imageAssets, businessLiteDTO.getBusinessNumber(), Constants.IMAGE));
        }
        if (CollectionUtils.isNotEmpty(socialPostCalendarMessage.getCompressedImages())) {
            List<SocialPostsAssets> compressedImageAssets = postAssetService.getPostsAssetsById(postAssetsMap, socialPostCalendarMessage.getCompressedImages());
            socialSchedulePostMessage.setCompressedImages(postAssetService.getMediaRequestV2(compressedImageAssets, businessLiteDTO.getBusinessNumber(), Constants.IMAGE));
        }
        if (CollectionUtils.isNotEmpty(socialPostCalendarMessage.getVideoIds())) {
            List<SocialPostsAssets> videoAssets = postAssetService.getPostsAssetsById(postAssetsMap, socialPostCalendarMessage.getVideoIds());
            PostAssetsData postAssetsData = postAssetService.setSocialPostsAssetsDataV2(videoAssets, businessLiteDTO.getBusinessNumber(), Constants.VIDEO);
            socialSchedulePostMessage.setVideos(postAssetsData.getVideos());
            socialSchedulePostMessage.setVideoThumbnails(postAssetsData.getVideoThumbnailUrls());
        }
        long checkpoint4Time = System.currentTimeMillis();
        LOGGER.info("checkpoint 1.1==============, time taken {} ms", checkpoint4Time - checkpoint3Time);
        if (CollectionUtils.isNotEmpty(socialPostCalendarMessage.getMentions())) {
            socialSchedulePostMessage.setMentions(socialPostCalendarMessage.getMentions());
        }
        if (StringUtils.isNotEmpty(socialPostCalendarMessage.getLinkPreviewUrl())) {
            socialSchedulePostMessage.setLinkPreviewUrl(socialPostCalendarMessage.getLinkPreviewUrl());
        }

        if (MapUtils.isNotEmpty(postVsDuplicateCountMap) && postVsDuplicateCountMap.containsKey(socialPostCalendarMessage.getId())) {
            socialSchedulePostMessage.setDuplicatedCount(postVsDuplicateCountMap.get(socialPostCalendarMessage.getId()));
        } else {
            socialSchedulePostMessage.setDuplicatedCount(0);
        }
        if (StringUtils.isNotEmpty(socialPostCalendarMessage.getQuotedTweetSource())) {
            socialSchedulePostMessage.setQuotedTweetSource(socialPostCalendarMessage.getQuotedTweetSource());
        } else {
            socialSchedulePostMessage.setQuotedTweetSource(Constants.TWITTER_SOURCE_BE);
        }
        if (StringUtils.isNotEmpty(socialPostCalendarMessage.getQuotedPostIdNew()) && StringUtils.isNotEmpty(socialPostCalendarMessage.getQuotedPostUrl())) {
            socialSchedulePostMessage.setIsQuotedTweet(true);
        }
        // Reel/Story condition
        if (Objects.nonNull(socialPostCalendarMessage.getPostMetaData())
                && StringUtils.isNotEmpty(socialPostCalendarMessage.getPostMetaData().getIgPostMetadata())) {
            IgPostMetadata igPostMetadata = JSONUtils.fromJSON(socialPostCalendarMessage.getPostMetaData().getIgPostMetadata(),
                    IgPostMetadata.class);
            socialSchedulePostMessage.setType(Objects.nonNull(igPostMetadata) ? igPostMetadata.getType() : null);
            if (StringUtils.isNotEmpty(socialSchedulePostMessage.getType()) && socialSchedulePostMessage.getType().equalsIgnoreCase("story")
                    && socialSchedulePostMessage.getIsPublished() == 1) {
                //story expiry check
                socialSchedulePostMessage.setIsExpired(isStoryExpired(socialSchedulePostMessage.getDatePublish()));
            }
        }

        if (Objects.nonNull(socialPostCalendarMessage.getPostMetaData())
                && StringUtils.isNotEmpty(socialPostCalendarMessage.getPostMetaData().getFbPostMetadata())) {
            FbPostMetadata fbPostMetadata = JSONUtils.fromJSON(socialPostCalendarMessage.getPostMetaData().getFbPostMetadata(),
                    FbPostMetadata.class);
            socialSchedulePostMessage.setType(Objects.nonNull(fbPostMetadata) ? fbPostMetadata.getType() : null);
            if (StringUtils.isNotEmpty(socialSchedulePostMessage.getType()) && socialSchedulePostMessage.getType().equalsIgnoreCase(FacebookPostType.STORY.getName())
                    && socialSchedulePostMessage.getIsPublished() == 1) {
                //story expiry check
                socialSchedulePostMessage.setIsExpired(isStoryExpired(socialSchedulePostMessage.getDatePublish()));
            }
        }
        long checkpoint5Time = System.currentTimeMillis();
        LOGGER.info("checkpoint 1.1==============, time taken {} ms", checkpoint5Time - checkpoint4Time);
//        }
        if (CollectionUtils.isNotEmpty(hasAccessMap.get(socialSchedulePostMessage.getId()))) {
            socialSchedulePostMessage.setHasAccess(false);
            socialSchedulePostMessage.setIncompleteChannel(hasAccessMap.get(socialSchedulePostMessage.getId()));
        }

        // show only failed posts if filter= failed is applied
        if (SocialPostStatusEnum.FAILED.getName().equalsIgnoreCase(filter.getPostStatus()) && !socialSchedulePostMessage.getHasPostFailed()) {
            return null;
        }
        return socialSchedulePostMessage;
    }

    private Map<String, Integer> getPageIdVsLocationIdMapV1(List<SocialPostCalendarMessage> socialSchedulePostMessageESList,
                                                            Map<String, Boolean> pagePermissionMap) {
        Map<String, Integer> responseMap = new HashMap<>();
        Map<Integer, List<String>> sourceIdvsPageIdsMap = new HashMap<>();
        for(SocialPostCalendarMessage info: socialSchedulePostMessageESList) {
            if(Objects.isNull(info) || CollectionUtils.isEmpty(info.getPageIds())) continue;
            if(sourceIdvsPageIdsMap.containsKey(info.getSourceId())) {
                sourceIdvsPageIdsMap.get(info.getSourceId()).addAll(info.getPageIds());
            } else {
                List<String> pageIds = new ArrayList<>(info.getPageIds());
                sourceIdvsPageIdsMap.put(info.getSourceId(), pageIds);
            }
        }
        List<LocationPagePair> locationPagePairList = new ArrayList<>();
        for(Map.Entry<Integer, List<String>> entry: sourceIdvsPageIdsMap.entrySet()) {
            locationPagePairList.addAll(socialPostService.getBusinessIdPageIdPairFromPageId(entry.getKey(), entry.getValue(), pagePermissionMap));
        }
        if(CollectionUtils.isNotEmpty(locationPagePairList)) {
            responseMap = locationPagePairList.stream().filter(s->(Objects.nonNull(s.getLocationId()) && StringUtils.isNotEmpty(s.getPageId())))
                    .collect(Collectors.toMap(LocationPagePair::getPageId, LocationPagePair::getLocationId));
        }
        return responseMap;
    }

    private Map<String, Integer> getPageIdVsLocationIdMapV2(List<SocialPostCalendarMessage> socialSchedulePostMessageESList,
                                                            Map<String,Boolean> pagePermission) {
        Map<String, Integer> responseMap = new HashMap<>();
        Map<Integer, List<String>> sourceIdvsPageIdsMap = new HashMap<>();
        for(SocialPostCalendarMessage info: socialSchedulePostMessageESList) {
            if(Objects.isNull(info) || CollectionUtils.isEmpty(info.getPageIds())) continue;
            if(sourceIdvsPageIdsMap.containsKey(info.getSourceId())) {
                sourceIdvsPageIdsMap.get(info.getSourceId()).addAll(info.getPageIds());
            } else {
                List<String> pageIds = new ArrayList<>(info.getPageIds());
                sourceIdvsPageIdsMap.put(info.getSourceId(), pageIds);
            }
        }
        List<LocationPagePair> locationPagePairList = new ArrayList<>();
        for(Map.Entry<Integer, List<String>> entry: sourceIdvsPageIdsMap.entrySet()) {
            locationPagePairList.addAll(socialPostService.getBusinessIdPageIdPairFromPageId(entry.getKey(), entry.getValue(),pagePermission));
        }
        if(CollectionUtils.isNotEmpty(locationPagePairList)) {
            responseMap = locationPagePairList.stream().filter(s->(Objects.nonNull(s.getLocationId()) && StringUtils.isNotEmpty(s.getPageId())))
                    .collect(Collectors.toMap(LocationPagePair::getPageId, LocationPagePair::getLocationId));
        }
        return responseMap;
    }

    private List<SocialPostCalendarMessage> getSocialPostSchInfoWithLocAllV3(List<Integer> accessibleLocations,
                                                                             List<SocialPostCalendarMessage> socialPostCalendarMessages,
                                                                             Map<Integer, List<String>> hasAccessMap,
                                                                             Map<String, Integer> pageIdVsLocationIdMap) {

        if (CollectionUtils.isNotEmpty(accessibleLocations)) {
            Set<Integer> accessibleLocationsSet = new HashSet<>(accessibleLocations);
            List<SocialPostCalendarMessage> locationsScheduleInfo = new ArrayList<>();
            for (SocialPostCalendarMessage socialPostCalendarMessage : socialPostCalendarMessages) {
                List<Integer> locations = new ArrayList<>();
                if (CollectionUtils.isEmpty(socialPostCalendarMessage.getPageIds())) {
                    locations = socialPostService.fetchlocationsByScheduleCondition(socialPostCalendarMessage);
                } else {
                    if (Objects.nonNull(socialPostCalendarMessage.getSourceId())) {
                        for (String pageId : socialPostCalendarMessage.getPageIds()) {
                            if (pageIdVsLocationIdMap.containsKey(pageId.toString())) {
                                locations.add(pageIdVsLocationIdMap.get(pageId.toString()));
                            }
                        }
                    }
                    // businessIds -> accessible business IDs
                    //locations = getBusinessIdFromPageId(scheduleInfo.getSourceId(), scheduleInfo.getPageIds());
                    if (!accessibleLocationsSet.containsAll(locations)) {
                        String channel = SocialChannel.getSocialChannelNameById(socialPostCalendarMessage.getSourceId());
                        List<String> channelList = hasAccessMap.get(socialPostCalendarMessage.getId());
                        if (CollectionUtils.isEmpty(channelList)) {
                            channelList = new ArrayList<>();
                            channelList.add(channel);
                        } else {
                            if (!channelList.contains(channel)) {
                                channelList.add(channel);
                            }
                        }
                        hasAccessMap.put(socialPostCalendarMessage.getId(), channelList);
                    }
                }
                if (locations.stream().distinct().anyMatch(accessibleLocationsSet::contains)) {
                    locationsScheduleInfo.add(socialPostCalendarMessage);
                }

            }
            LOGGER.info("Location list: {}", locationsScheduleInfo.size());
            socialPostCalendarMessages = locationsScheduleInfo;
        }
        LOGGER.info("socialPostScheduleInfoList: {}", socialPostCalendarMessages.size());
        return socialPostCalendarMessages;
    }

}