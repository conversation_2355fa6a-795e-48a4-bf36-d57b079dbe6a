package com.birdeye.social.service;

import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.entities.mediaupload.SocialAssetChunkInfo;
import com.birdeye.social.entities.mediaupload.SocialMediaUploadInfo;
import com.birdeye.social.entities.mediaupload.SocialMediaUploadRequest;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.external.request.mediaupload.MediaUploadRequest;
import com.birdeye.social.external.service.SocialRawPageDetail;

import java.util.List;

public interface SocialMediaUploadService {
    String channelName();

    SocialRawPageDetail getPageDetails(String pageId);

    void registerMedia(Integer socialMediaUploadRequestId, SocialRawPageDetail socialRawPageDetail, MediaUploadRequest mediaInitiateRequest) throws Exception;

    void uploadChunk(SocialAssetChunkInfo socialAssetChunkInfo, SocialMediaUploadInfo socialMediaUploadInfo, SocialRawPageDetail socialRawPageDetail,
                     SocialMediaUploadRequest socialMediaUploadRequest, boolean isV2request) throws Exception;

    void uploadCaption(SocialRawPageDetail socialRawPageDetail, SocialMediaUploadRequest socialMediaUploadRequest);

    void finalizeVideoUpload(SocialRawPageDetail socialRawPageDetail, SocialMediaUploadRequest socialMediaUploadRequest, List<String> eTags) throws Exception;

    void postCaption(SocialMediaUploadRequest socialMediaUploadRequest, SocialRawPageDetail socialRawPageDetail);

    void checkStatus(SocialRawPageDetail socialRawPageDetail, MediaUploadRequest mediaUploadRequest) throws Exception;

    void birdeyeExceptionHandler(BirdeyeSocialException bse,Integer publishInfoId,String pageId);

    void generalExceptionHandler(String message, Integer publishInfoId);

    void postContentWithMedia(MediaUploadRequest request, String pageId, SocialPostPublishInfo publishInfoId) throws Exception;

    void evictPageCache(String externalPageId);
}
