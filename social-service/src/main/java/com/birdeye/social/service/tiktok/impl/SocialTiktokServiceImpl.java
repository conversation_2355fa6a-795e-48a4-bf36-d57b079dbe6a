package com.birdeye.social.service.tiktok.impl;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessGetPageOpenUrlReqRepo;
import com.birdeye.social.dao.BusinessTiktokAccountsRepository;
import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.dao.SocialSetupAuditRepository;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.SocialTokenValidationDTO;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.insights.tiktok.TiktokInsightRequest;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.FbNotification.EngageNotificationDetails;
import com.birdeye.social.model.engageV2.EngageCommentRequest;
import com.birdeye.social.model.tiktok.*;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.service.*;
import com.birdeye.social.service.tiktok.SocialTiktokService;
import com.birdeye.social.service.tiktok.arbor.TiktokCommonPageService;
import com.birdeye.social.service.tiktok.dto.TiktokNextCursorPayload;
import com.birdeye.social.service.tiktok.external.TiktokExternalService;
import com.birdeye.social.sro.OpenUrlPagesInfo;
import com.birdeye.social.sro.PageRequest;
import com.birdeye.social.tiktok.TikTokAccountAccessInfo;
import com.birdeye.social.tiktok.TikTokFeedData;
import com.birdeye.social.tiktok.TikTokPostStatusResponse;
import com.birdeye.social.tiktok.TikTokPageData;
import com.birdeye.social.utils.SocialProxyHandler;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import twitter4j.TwitterException;

import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.*;
import static com.birdeye.social.constant.KafkaTopicEnum.SOCIAL_SYNC_BUSINESS_POSTS;

@Service
public class SocialTiktokServiceImpl extends SocialAccountSetupCommonService implements SocialTiktokService {

    private static final Logger LOGGER	= LoggerFactory.getLogger(SocialTiktokServiceImpl.class);

    @Autowired
    private BusinessTiktokAccountsRepository tiktokAccountsRepository;

    @Autowired
    private TiktokExternalService tiktokExternalService;

    @Autowired
    private SocialTikTikHelperService socialTikTokHelperService;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private SocialPostInfoRepository publishInfoRepo;

    @Autowired
    private ISocialPostsAssetService socialPostsAssetService;

    @Autowired
    private KafkaExternalService kafkaExternalService;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private BusinessGetPageOpenUrlReqRepo businessGetPageOpenUrlReqRepo;

    @Autowired
    private IPermissionMappingService permissionMappingService;

    @Autowired
    private SocialSetupAuditRepository setupAuditRepo;

    @Autowired
    TiktokCommonPageService tiktokCommonPageService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private IBrokenIntegrationService brokenIntegrationService;

    @Autowired
    private SocialProxyHandler socialProxyHandler;

    @Autowired
    private IRedisLockService redisService;

    @Autowired
    private NexusService nexusService;

    @Autowired
    @Qualifier("socialRestTemplate")
    private RestTemplate socialRestTemplate;

    private static final String TIKTOK_API_URL = "https://business-api.tiktok.com/open_api/v1.3/business/video/list/";
    private static final String TIKTOK_POST_STATUS_URL = "https://business-api.tiktok.com/open_api/v1.3/business/publish/status/";
    private static final String TIKTOK_GET_COMMENT_API_URL = "https://business-api.tiktok.com/open_api/v1.3/business/comment/list/";
    private static final String TIKTOK_PROFILE_API_URL = "https://business-api.tiktok.com/open_api/v1.3/business/get/";

    public final String TIKTOK_VIDEO_LIST_FIELDS = "[\"caption\", \"video_duration\", \"item_id\", \"thumbnail_url\", \"thumbnail_url\", \"share_url\", \"embed_url\", \"create_time\", \"likes\", \"comments\", \"shares\", \"video_views\", \"reach\"]";


    @Override
    public void postOnTikTok(SocialPostPublishInfo publishInfo) throws Exception {
        postVideoOnTiktok(publishInfo);
    }

    @Override
    public OpenUrlPagesInfo getPagesFetchedByOpenUrl(Long enterpriseId) throws Exception {
        LOGGER.info("getPagesFetchedByOpenUrl tiktok: enterpriseId {}", enterpriseId);
        OpenUrlPagesInfo response = new OpenUrlPagesInfo();
        List<BusinessGetPageOpenUrlRequest> tiktokRequests = businessGetPageOpenUrlReqRepo.findFirstByEnterpriseIdAndChannelAndRequestTypeOrderByCreatedDesc(enterpriseId, SocialChannel.TIKTOK.getName(), CONNECT);
        LOGGER.info("getPagesFetchedByOpenUrl: List<BusinessGetPageOpenUrlRequest> got result {}", tiktokRequests);

        if (CollectionUtils.isNotEmpty(tiktokRequests)) {
            // only get the pages if status is fetched
            BusinessGetPageOpenUrlRequest tiktokRequest = tiktokRequests.get(0);

            if (tiktokRequest.getStatus().equalsIgnoreCase(Status.FETCHED.getName())) {
                LOGGER.info("getPagesFetchedByOpenUrl: tiktokRequest found with fetched status with id: {}", tiktokRequest.getId());
                List<BusinessTiktokAccounts> businessTiktokAccounts = tiktokAccountsRepository.findByRequestId(tiktokRequest.getId());
                List<ChannelAccountInfo> info = new ArrayList<>();
                for(BusinessTiktokAccounts businessTiktokAccount: businessTiktokAccounts) {
                    info.add(createChannelAccountInfo(businessTiktokAccount));
                }
                response.setPageTypes(info);
            } else {
                LOGGER.info("getPagesFetchedByOpenUrl: tiktokRequest found with {} status", tiktokRequest.getStatus());
            }
            response.setStatus(tiktokRequest.getStatus());
            response.setStatusType(tiktokRequest.getRequestType());
        } else {
            LOGGER.info("getPagesFetchedByOpenUrl: No BusinessGetPageOpenUrlRequest found with given input");
            response.setStatus(Status.COMPLETE.getName());
            response.setStatusType("connect");
        }

        LOGGER.info("getPagesFetchedByOpenUrl: Returning response {}", response);
        return response;
    }

    @Override
    public OpenUrlPagesInfo connectPagesFetchedByOpenUrl(Long enterpriseId, OpenUrlConnectRequest connectRequest, Integer userId) {
        LOGGER.info("connectPagesFetchedByOpenUrl tiktok : enterpriseId {} connectRequest {} userId {}", enterpriseId, connectRequest, userId);
        if ( enterpriseId == null || connectRequest == null || com.birdeye.social.utils.StringUtils.isEmpty(connectRequest.getFirebaseKey()) || CollectionUtils.isEmpty(connectRequest.getPageRequests()) ) {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid value for enterpriseId/connectRequest/userId");
        }
        final String firebaseKey = connectRequest.getFirebaseKey();
        final List<String> profileIds = connectRequest.getPageRequests().stream().map(PageRequest::getId).collect(Collectors.toList());
        List<BusinessGetPageOpenUrlRequest> tiktokRequests = getRequestForOpenUrlBusiness(enterpriseId, Status.FETCHED.getName(), SocialChannel.TIKTOK.getName(), CONNECT, firebaseKey);
        if (CollectionUtils.isEmpty(tiktokRequests)) { // case of no request found
            LOGGER.error("connectPagesFetchedByOpenUrl tiktok: No rows found with fetched status for profileIds {} enterpriseId {} firebaseKey {}", profileIds, enterpriseId, firebaseKey);
            throw new BirdeyeSocialException(ErrorCodes.STATUS_CHANGED, "Seems request status has already changed");
        } else if (tiktokRequests.size() > 1) {  // case of multiple requests present
            LOGGER.error("connectPagesFetchedByOpenUrl tiktok: Multiple rows found with fetched status for profileIds {} and enterpriseId {} firebaseKey {}", profileIds, enterpriseId, firebaseKey);
            throw new BirdeyeSocialException(ErrorCodes.MULTI_FETCHED_ROWS, "Multiple rows with fetched status found in BusinessGetPageOpenUrlRequest");
        } else {
            return fetchConnectedPagesOpenUrl(profileIds, tiktokRequests.get(0), enterpriseId, userId);
        }
    }

    private OpenUrlPagesInfo fetchConnectedPagesOpenUrl(List<String> profileIds, BusinessGetPageOpenUrlRequest tiktokRequest, Long enterpriseId, Integer userId) {
        OpenUrlPagesInfo response = new OpenUrlPagesInfo();
        List<BusinessTiktokAccounts> businessTiktokAccounts = tiktokAccountsRepository.findByProfileIdIn(profileIds);
        if ( CollectionUtils.isEmpty(businessTiktokAccounts) ) {
            LOGGER.error("fetchConnectedPagesOpenUrl tiktok: No tiktok account found with profileIds {}", profileIds);
            throw new BirdeyeSocialException(ErrorCodes.SOCIAL_RAW_PAGE_NOT_FOUND, "Tiktok Pages not found with given profileIds");
        }
        BusinessLiteDTO business = businessCoreService.getBusinessLiteByNumber(enterpriseId);
        if ( Objects.isNull(business) ) {
            throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, "Business not found using Business Lite API");
        }

        final boolean isBusinessMapped = isBusinessMappedToTiktokAccount(business.getBusinessId());
        final boolean isSmbAndMapped = checkBusinessSMB(business) && isBusinessMapped;

        LOGGER.info("fetchConnectedPagesOpenUrl Tiktok: isBusinessMapped {}, invalidSmbCase {}", isBusinessMapped, isSmbAndMapped);

        List<BusinessTiktokAccounts> existingProfiles = new ArrayList<>();
        List<BusinessTiktokAccounts> newProfiles = new ArrayList<>();

        getNewAndExistingProfiles(businessTiktokAccounts, business.getBusinessId(), existingProfiles, newProfiles, enterpriseId, userId, isSmbAndMapped);
        updateTiktokAccountIsValid(existingProfiles);

        response.setPageTypes(createTiktokAccountInfo(businessTiktokAccounts));
        response.setStatus(Status.COMPLETE.getName());
        response.setStatusType("connect");
        // update request in db
        tiktokRequest.setStatus(Status.COMPLETE.getName());
        tiktokRequest.setUpdated(new Date());
        businessGetPageOpenUrlReqRepo.saveAndFlush(tiktokRequest);
        LOGGER.info("fetchConnectedPagesOpenUrl Tiktok: Request status updated to complete");

        mapUnmappedPagesAndsendEvent(profileIds, newProfiles, business, enterpriseId, userId, isBusinessMapped);
        LOGGER.info("fetchConnectedPagesOpenUrl tiktok: Response {}", response);
        return response;
    }

    private void getNewAndExistingProfiles(List<BusinessTiktokAccounts> businessTiktokAccounts, Integer businessId,
                                            List<BusinessTiktokAccounts> existingProfiles, List<BusinessTiktokAccounts> newProfiles,
                                            Long enterpriseId, Integer userId, boolean isSmbAndMapped) {

        for (BusinessTiktokAccounts accounts : businessTiktokAccounts) {
            if ( accounts.getEnterpriseId() == null && !isSmbAndMapped ) { // case of newly found profile
                accounts.setIsSelected(1);
                accounts.setEnterpriseId(enterpriseId);
                accounts.setAccountId(businessId);
                tiktokAccountsRepository.saveAndFlush(accounts);
                newProfiles.add(accounts);
            } else if ( accounts.getEnterpriseId() != null && accounts.getEnterpriseId().equals(enterpriseId) ) { // case of existing profile
                existingProfiles.add(accounts);
            } else { // case of smb account already mapped OR case of trying to connect profile belonging to different enterpriseId
                String comment = isSmbAndMapped ? "SMB account is already mapped" : "This profile is already connected with some other enterpriseId";
                setupAuditRepo.saveAndFlush(new SocialSetupAudit("OPEN_URL_CONNECT", businessId,
                        accounts.getProfileId().toString(), String.valueOf(userId), accounts.toString(), SocialChannel.TIKTOK.getName(), comment));
            }
        }
    }

    private void mapUnmappedPagesAndsendEvent(List<String> profileIds, List<BusinessTiktokAccounts> newPages, BusinessLiteDTO business, Long enterpriseId,
                                 Integer userId, boolean isBusinessMapped) {
        if (checkBusinessSMB(business) && !isBusinessMapped) { //if business is smb and unmapped
            try {
                LOGGER.info("connectPagesFetchedByOpenUrl Tiktok: Trying to map business {} to profileId {}", business.getBusinessId(), profileIds.get(0));
                tiktokCommonPageService.saveTiktokLocationMapping(business.getBusinessId(), profileIds.get(0), userId, ENTERPRISE);
            } catch (Exception saveMappingException) {
                // we need to return 200 OK response even if mapping fails
                LOGGER.error("connectPagesFetchedByOpenUrl Tiktok: Failed to map business with profile with error {}", saveMappingException.getMessage());
                setupAuditRepo.saveAndFlush(new SocialSetupAudit("OPEN_URL_CONNECT", business.getBusinessId(),
                        profileIds.get(0).toString(), String.valueOf(userId), null, SocialChannel.TIKTOK.getName(),
                        "SMB mapping failed"));
            }
        } else {
            if(CollectionUtils.isNotEmpty(newPages)){
                LOGGER.info("publishing SOCIAL_PAGE_CONNECT event for tiktok open url for enterpriseId {}", enterpriseId);
                SocialConnectPageRequest socialConnectPageRequest = new SocialConnectPageRequest(
                        newPages.stream().map(BusinessTiktokAccounts :: getProfileId).map(aLong -> aLong.toString()).collect(Collectors.toList())
                        ,SocialChannel.TIKTOK.getName());
                kafkaProducerService.sendObject(SOCIAL_PAGE_CONNECT, socialConnectPageRequest);
            }
        }
    }

    @Override
    public OpenUrlFetchPageResponse submitFetchPageRequestForOpenURL(Long enterpriseId, ChannelAuthOpenUrlRequest authRequest) {
        OpenUrlFetchPageResponse openUrlFetchPageResponse = new OpenUrlFetchPageResponse();
        String code = authRequest.getAuthCode();
        String redirectUri = authRequest.getRedirectUri();
        String key = SocialChannel.TIKTOK.getName().concat(String.valueOf(authRequest.getFirebaseKey()));
        boolean lock = redisService.tryToAcquireLock(key);
        LOGGER.info("[Redis Lock tiktok Open url fetch page] Lock status on key {}: {}", lock, key);

        if(lock) {
            try {
                TiktokAccessTokenDataResponse refreshTokenDetails =  socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(() ->
                        tiktokExternalService.generateRefreshToken(code, redirectUri));

                if(Objects.isNull(refreshTokenDetails) && StringUtils.isNotEmpty(refreshTokenDetails.getRefreshToken()))  {
                    LOGGER.info("Extended token could not be retrieved, cancelling the fetch request");
                    throw new Exception("Invalid extended token");
                }
                LOGGER.info("[Tiktok OpenUrl] refresh token details", refreshTokenDetails);

                BusinessGetPageOpenUrlRequest request = saveBusinessGetPageOpenUrl(enterpriseId, refreshTokenDetails, authRequest.getFirebaseKey());
                socialProxyHandler.runInAsync(() -> {
                    tiktokCommonPageService.fetchPagesOpenUrl(request, refreshTokenDetails, enterpriseId, key);
                    return true;
                });
            } catch (Exception ex) {
                handleCleanupRedisForOpenurl(key, authRequest, enterpriseId);
            }
        } else {
            LOGGER.info("Could not acquire redis lock for tiktok open url fetch page for key {} ", key);
        }
        openUrlFetchPageResponse.setFirebaseKey(authRequest.getFirebaseKey());
        LOGGER.info("API response for tiktok open url request for enterprise {} : {}", enterpriseId, openUrlFetchPageResponse);
        return openUrlFetchPageResponse;
    }

    private BusinessGetPageOpenUrlRequest saveBusinessGetPageOpenUrl(Long enterpriseId, TiktokAccessTokenDataResponse refreshTokenDetails, String firebaseKey) throws TwitterException {
        BusinessGetPageOpenUrlRequest request = new BusinessGetPageOpenUrlRequest();
        request.setSocialUserId(Objects.nonNull(refreshTokenDetails) ? refreshTokenDetails.getOpenid() : "");
        request.setChannel(SocialChannel.TIKTOK.getName());
        request.setEnterpriseId(enterpriseId);
        request.setPageCount(0);
        request.setStatus(Status.INITIAL.getName());
        request.setRequestType(CONNECT);
        request.setUserAccessToken(refreshTokenDetails.getRefreshToken());
//        request.setEmail("@" + twitter.getScreenName());
        request.setUserFbPermissions(refreshTokenDetails.getScope());
        request.setFirebaseKey(firebaseKey);
        return businessGetPageOpenUrlReqRepo.save(request);
    }


    private boolean isBusinessMappedToTiktokAccount(Integer businessId) {
        return Objects.nonNull(tiktokAccountsRepository.findByBusinessId(businessId));
    }


    private void updateTiktokAccountIsValid(List<BusinessTiktokAccounts> existingPages) {
        LOGGER.info("markTiktokLocationMappingAsvalid : Marking BusinessTiktokProfiles as valid for profiles {}", existingPages);
        if ( existingPages != null && !existingPages.isEmpty() ) {
            Map<String, BusinessTiktokAccounts> pageIdTiktokPageMap = existingPages.stream()
                    .collect(Collectors.toMap(BusinessTiktokAccounts::getProfileId, twitterAccounts -> twitterAccounts, (x, y) -> x));

            List<BusinessTiktokAccounts> tiktokLocations = tiktokAccountsRepository.findByProfileIdIn(new ArrayList<>(pageIdTiktokPageMap.keySet()));
            if (!tiktokLocations.isEmpty()) {
                tiktokLocations.forEach(profile -> {
                    BusinessTiktokAccounts accounts = pageIdTiktokPageMap.get(profile.getProfileId());
                    profile.setIsValid(accounts.getIsValid());
                    profile.setValidType(accounts.getValidType());
                    tiktokAccountsRepository.saveAndFlush(profile);
                    if(profile.getIsValid().equals(0)) {
                        commonService.sendTiktokSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(profile),
                                null, profile.getBusinessId(), profile.getEnterpriseId());
                    }
                    brokenIntegrationService.pushValidIntegrationStatus(accounts.getEnterpriseId(),SocialChannel.TIKTOK.getName(),accounts.getId(),accounts.getIsValid(),accounts.getProfileId().toString());
                });
            }
            LOGGER.info("markTiktokLocationMappingAsInvalid : BusinessTwitterPage {} have been marked as valid", tiktokLocations);
        }
    }

    private List<ChannelAccountInfo> createTiktokAccountInfo(List<BusinessTiktokAccounts> accounts) {
        return accounts.stream().map(account -> createChannelAccountInfo(account)).collect(Collectors.toList());
    }

    private ChannelAccountInfo createChannelAccountInfo(BusinessTiktokAccounts profile) {
        ChannelAccountInfo accountInfo = new ChannelAccountInfo();
        accountInfo.setId(String.valueOf(profile.getProfileId()));
        accountInfo.setPageName(profile.getProfileName());
        accountInfo.setLink(profile.getProfileUrl());
        accountInfo.setImage(profile.getProfileImageUrl());
        accountInfo.setHandle(profile.getProfileUsername());
        accountInfo.setDisabled(profile.getIsSelected() != null && profile.getIsSelected() == 1);

        Validity validity = tiktokCommonPageService.fetchValidityAndErrorMessage(profile);
        accountInfo.setValidType(validity.getValidType());
        accountInfo.setErrorCode(validity.getErrorCode());
        accountInfo.setErrorMessage(validity.getErrorMessage());

        return accountInfo;
    }

    private void postVideoOnTiktok(SocialPostPublishInfo publishInfo) throws Exception {
        LOGGER.info("posting video for tiktok publish info: {}", publishInfo.getId());

        BusinessTiktokAccounts tiktokAccount = tiktokAccountsRepository.findByBusinessId(publishInfo.getBusinessId());
        if (Objects.isNull(tiktokAccount)) {
            throw new Exception("No account mapped to the business");
        }

        String accessToken = getAccessToken(tiktokAccount, false);
        SocialPost socialPost = publishInfo.getSocialPost();
        if (Objects.isNull(socialPost)) {
            throw new BirdeyeSocialException("empty social post for publish info id");
        }

        try {
            TiktokVideoPostingRequest videoPostingRequest = prepareVideoPostingRequest(publishInfo, socialPost, tiktokAccount);
            TiktokVideoPostingResponse tiktokVideoPostingResponse = tiktokExternalService.postVideo(videoPostingRequest, accessToken);
            handleTiktokResponse(publishInfo, tiktokAccount, videoPostingRequest, tiktokVideoPostingResponse);
        } catch (Exception e) {
            handlePostVideoException(publishInfo, e);
        }

        publishInfoRepo.save(publishInfo);
        kafkaExternalService.publishSocialPostEvent(publishInfo);
    }

    private TiktokVideoPostingRequest prepareVideoPostingRequest(SocialPostPublishInfo publishInfo, SocialPost socialPost, BusinessTiktokAccounts tiktokAccount) throws Exception {
        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(publishInfo.getEnterpriseId(), false);
        return socialTikTokHelperService.prepareTikTokVideoRequest(socialPost, businessLiteDTO.getBusinessNumber(), tiktokAccount.getProfileId());
    }

    private void handleTiktokResponse(SocialPostPublishInfo publishInfo, BusinessTiktokAccounts tiktokAccount, TiktokVideoPostingRequest videoPostingRequest, TiktokVideoPostingResponse tiktokVideoPostingResponse) throws Exception {
        if (Objects.nonNull(tiktokVideoPostingResponse) && StringUtils.isNotEmpty(tiktokVideoPostingResponse.getMessage())) {
            LOGGER.info("TIKTOK ERROR RESPONSE {} with Log Id {} and message {}", tiktokVideoPostingResponse.getCode(), tiktokVideoPostingResponse.getRequestId(), tiktokVideoPostingResponse.getMessage());

            if (Objects.equals(tiktokVideoPostingResponse.getCode(), TIKTOK_ACCESS_TOKEN_INVALID_CODE)) {
                LOGGER.info("[Tiktok] Retrying posting with new access token");
                String accessToken = getAccessToken(tiktokAccount, true);
                tiktokVideoPostingResponse = tiktokExternalService.postVideo(videoPostingRequest, accessToken);
            }

            if (!Objects.equals(tiktokVideoPostingResponse.getCode(),"0")
                    && !Objects.equals(tiktokVideoPostingResponse.getMessage(),"OK")) {
                handleTiktokError(publishInfo, tiktokAccount, tiktokVideoPostingResponse);
                return;
            }
        }

        if (Objects.nonNull(tiktokVideoPostingResponse) && Objects.nonNull(tiktokVideoPostingResponse.getData())) {
            TiktokVideoResponseData responseData = tiktokVideoPostingResponse.getData();
            publishInfo.setPostId(responseData.getShareId());
            publishInfo.setIsPublished(SocialPostStatusEnum.PROCESSING.getId());
            updateVideoAssetLastUsedDate(publishInfo);
        } else {
            publishInfo.setIsPublished(2);
            publishInfo.setFailureReason(null);
        }
    }

    private void handleTiktokError(SocialPostPublishInfo publishInfo, BusinessTiktokAccounts tiktokAccount, TiktokVideoPostingResponse tiktokVideoPostingResponse) {
        LOGGER.info("error occurred while posting video for tiktok publish info: {}, error: {}", publishInfo.getId(), tiktokVideoPostingResponse.getMessage());
        publishInfo.setIsPublished(2);
        publishInfo.setFailureReason(tiktokVideoPostingResponse.getMessage());
        kafkaExternalService.publishSocialPostEvent(publishInfo);
        publishInfoRepo.save(publishInfo);

        if (Objects.equals(tiktokVideoPostingResponse.getCode(), TIKTOK_ACCESS_TOKEN_INVALID_CODE)) {
            SocialTokenValidationDTO socialTokenValidationDTO = new SocialTokenValidationDTO();
            socialTokenValidationDTO.setChannel(SocialChannel.TIKTOK.getName());
            socialTokenValidationDTO.setId(tiktokAccount.getId());
            kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_MARK_PAGE_INVALID.getName(), socialTokenValidationDTO);
        }
    }

    private void handlePostVideoException(SocialPostPublishInfo publishInfo, Exception e) {
        publishInfo.setIsPublished(2);
        publishInfo.setFailureReason(e.getMessage());
    }

    private void updateVideoAssetLastUsedDate(SocialPostPublishInfo publishInfo) {
        SocialPostsAssets videoAsset = socialPostsAssetService.findById(Integer.parseInt(publishInfo.getSocialPost().getVideoIds()));
        videoAsset.setLastUsedDate(Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTime());
        socialPostsAssetService.save(videoAsset);
    }

    public String getAccessToken(BusinessTiktokAccounts tiktokAccount, boolean cacheEvict) {
        String refreshToken = tiktokAccount.getRefreshToken();

        if(cacheEvict) {
            tiktokExternalService.evictTiktokAccessToken(refreshToken);
        }

        TiktokAccessTokenDataResponse tiktokAccessTokenResponse =
                tiktokExternalService.generateRefreshToken(refreshToken);

        String accessToken = null;

        if(Objects.nonNull(tiktokAccessTokenResponse)) {
            accessToken = tiktokAccessTokenResponse.getAccessToken();
        }

        return accessToken;
    }


    /**
     * Fetch all comments and replies on a TikTok video using pagination.
     *
     * @param accessToken TikTok API access token
     * @param businessId  TikTok account ID
     * @param videoId     ID of the TikTok video
     * @return List of all comments and their replies
     */
    @Override
    public List<TikTokCommentResponse.TikTokComment> getCommentsOnTiktokVideo(String accessToken, String businessId, String videoId) {
        List<TikTokCommentResponse.TikTokComment> allComments = new ArrayList<>();
        int cursor = 0;
        boolean hasMore = true;

        while (hasMore) {
            try {
                // Build request object
                TikTokCommentRequest request = TikTokCommentRequest.builder()
                        .businessId(businessId)
                        .videoId(videoId)
                        .includeReplies(false)  // fetch only top level comments
                        .sortField("create_time")
                        .sortOrder("desc")
                        .cursor(cursor)
                        .maxCount(30)
                        .build();

                // Fetch comments
                ResponseEntity<TikTokCommentResponse> responseEntity = fetchComments(accessToken, request);

                // Handle response
                if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
                    TikTokCommentResponse response = responseEntity.getBody();
                    TikTokCommentResponse.TikTokCommentData commentData = response.getData();

                    if (commentData != null) {
                        List<TikTokCommentResponse.TikTokComment> comments = commentData.getComments();
                        if (comments != null) {
                            for (TikTokCommentResponse.TikTokComment comment : comments) {
                                allComments.add(comment);
                                // Fetch replies recursively
                                fetchRepliesRecursively(accessToken, businessId, videoId, comment);
                            }
                        }
                        cursor = commentData.getCursor();
                        hasMore = commentData.isHasMore();
                    } else {
                        hasMore = false;
                    }
                } else {
                    LOGGER.warn("Received unexpected response: {}", responseEntity.getStatusCode());
                    hasMore = false;
                }
            } catch (Exception e) {
                LOGGER.error("Error while fetching TikTok comments: ", e);
                hasMore = false;
            }
        }

        return allComments;
    }

    /**
     * Recursively fetches replies for a given comment and stores them in the `replyList`.
     */
    private void fetchRepliesRecursively(String accessToken, String businessId, String videoId, TikTokCommentResponse.TikTokComment comment) {
        if (comment.getReplies() == 0) {
            return;
        }

        int cursor = 0;
        boolean hasMore = true;
        List<TikTokCommentResponse.TikTokComment> replies = new ArrayList<>();

        while (hasMore) {
            try {
                // Build request to fetch replies
                String url = String.format("https://business-api.tiktok.com/open_api/v1.3/business/comment/reply/list/?business_id=%s&video_id=%s&comment_id=%s&cursor=%d",
                        businessId, videoId, comment.getCommentId(), cursor);

                HttpHeaders headers = new HttpHeaders();
                headers.set("Access-Token", accessToken);
                HttpEntity<String> entity = new HttpEntity<>(headers);

                ResponseEntity<TikTokCommentResponse> responseEntity = socialRestTemplate.exchange(url, HttpMethod.GET, entity, TikTokCommentResponse.class);

                if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
                    TikTokCommentResponse response = responseEntity.getBody();
                    TikTokCommentResponse.TikTokCommentData replyData = response.getData();

                    if (replyData != null) {
                        List<TikTokCommentResponse.TikTokComment> fetchedReplies = replyData.getComments();
                        for (TikTokCommentResponse.TikTokComment reply : fetchedReplies) {
                            fetchRepliesRecursively(accessToken, businessId, videoId, reply);
                            replies.add(reply);
                        }
                        cursor = replyData.getCursor();
                        hasMore = replyData.isHasMore();
                    } else {
                        hasMore = false;
                    }
                } else {
                    LOGGER.warn("Received unexpected response: {}", responseEntity.getStatusCode());
                    hasMore = false;
                }
            } catch (Exception e) {
                LOGGER.error("Error while fetching TikTok replies for comment {}: ", comment.getCommentId(), e);
                hasMore = false;
            }
        }

        comment.setReplyList(replies);
    }


    /**
     * Makes an API call to fetch TikTok comments.
     *
     * @param accessToken API access token
     * @param request     TikTokCommentRequest object
     * @return ResponseEntity containing TikTokCommentResponse
     */
    private ResponseEntity<TikTokCommentResponse> fetchComments(String accessToken, TikTokCommentRequest request) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Token", accessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> entity = new HttpEntity<>(headers);
        String url = buildUrlWithParams(TIKTOK_GET_COMMENT_API_URL, request);

        LOGGER.info("Starting to fetch TikTok comments from URL: {}", url);

        try {
            ResponseEntity<TikTokCommentResponse> responseEntity = socialRestTemplate.exchange(url, HttpMethod.GET, entity, TikTokCommentResponse.class);

            if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
                LOGGER.info("Successfully fetched TikTok comments for video ID: {}", request.getVideoId());
            } else {
                LOGGER.warn("Unexpected response while fetching TikTok comments: {}", responseEntity.getStatusCode());
            }

            return responseEntity;
        }
        catch (HttpStatusCodeException e) {
            LOGGER.error("HttpStatusCodeException while fetching TikTok comments from URL {} :: {}", url, e.getResponseBodyAsString());
            throw new BirdeyeSocialException(e.getRawStatusCode(), e.getMessage());
        }
        catch (Exception e) {
            LOGGER.error("Unable to fetch tiktok comments due to error, URL: {} :: {}", url, e.getMessage());
        }

        return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }


    /**
     * Builds the URL with query parameters for the TikTok API.
     *
     * @param baseUrl API endpoint
     * @param request TikTokCommentRequest object
     * @return URL string with query parameters
     */
    private String buildUrlWithParams(String baseUrl, TikTokCommentRequest request) {
        return baseUrl + "?business_id=" + request.getBusinessId() +
                "&video_id=" + request.getVideoId() +
                "&include_replies=" + request.getIncludeReplies() +
                "&sort_field=" + request.getSortField() +
                "&sort_order=" + request.getSortOrder() +
                "&cursor=" + request.getCursor() +
                "&max_count=" + request.getMaxCount();
    }



    private PermissionMapping errorHandlerForTiktokService(BirdeyeSocialException ex) {
        Integer errorCode = ex.getErrorCode();
        String message = ex.getMessage();
        // no need to check sub-code, text can be parsed on the basis of parentCode and error text.
        List<PermissionMapping> permissionMappings =permissionMappingService.getDataByChannelAndParentErrorCodeAndPermissionCodeAndModule(TIKTOK,
                errorCode, -1,SocialMessageModule.PUBLISH);

        if(CollectionUtils.isEmpty(permissionMappings)) {
            LOGGER.info("New error found for TIKTOK posting with errorCode: {}  and message: {}", errorCode, message);
            return permissionMappingService.getDataByChannelAndPermissionCodeAndModule(SocialChannel.GMB.getName(),
                    Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR,Constants.PUBLISH_MODULE);
        }
        if(permissionMappings.size() == 1) return permissionMappings.get(0);

        for(PermissionMapping pm: permissionMappings) {
            if(StringUtils.isNotEmpty(message) && message.contains(pm.geterrorActualMessage())){
                return pm;
            }
        }
        return null;
    }

    public TikTokFeedData getTikTokUserVideos(TikTokAccountAccessInfo accessInfo, Long nextCursor) {
        ResponseEntity<TikTokFeedData> response;

        try {
            // TikTok API endpoint with query parameters

            MultiValueMap<String , String>  parametersMap =  getVideoListParams(accessInfo.getProfileId(), nextCursor);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Access-Token", accessInfo.getAccessToken());

            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

            response = socialRestTemplate.exchange(buildUri(TIKTOK_API_URL, parametersMap), HttpMethod.GET, requestEntity, TikTokFeedData.class);

        } catch (HttpStatusCodeException e) {
            LOGGER.error("Error from TikTok API: Status - {}, Response - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());

        } catch (Exception ex) {
            LOGGER.error("[getTikTokUserVideos] Something went wrong ", ex);
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
        }

        return response.getBody();
    }

    public TikTokFeedData getTikTokUserVideosPaginated(TikTokAccountAccessInfo accessInfo, Long nextCursor, int maxCount) {
        ResponseEntity<TikTokFeedData> response;
        try{
            TiktokNextCursorPayload tiktokNextCursorPayload = TiktokNextCursorPayload.builder().cursor(nextCursor).build();

            String fields = "id,create_time,cover_image_url,share_url,video_description,duration,height,width,title,embed_html,embed_link,like_count,comment_count,share_count,view_count";
            String urlWithFields = String.format("%s?fields=%s", TIKTOK_API_URL, fields);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessInfo.getAccessToken());

            HttpEntity<TiktokNextCursorPayload> requestEntity = new HttpEntity<>(tiktokNextCursorPayload, headers);

            // Send POST request
           response = socialRestTemplate.exchange(
                    urlWithFields, HttpMethod.POST, requestEntity, TikTokFeedData.class);
        } catch (HttpStatusCodeException e) {
            LOGGER.error("[getTikTokUserVideosPaginated] Error from TikTok API: Status - {}, Response - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());

        } catch (Exception ex) {
            LOGGER.error("[getTikTokUserVideosPaginated] Something went wrong ", ex);
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
        }
        return response.getBody();
    }


    public TikTokFeedData getTiktokPostData(String pageId, String videoId, String accessInfo) {
        ResponseEntity<TikTokFeedData> response;
        try{

            MultiValueMap<String , String>  parametersMap =  getVideoParams(pageId, videoId);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Access-Token", accessInfo);

            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

            response = socialRestTemplate.exchange(buildUri(TIKTOK_API_URL, parametersMap), HttpMethod.GET, requestEntity, TikTokFeedData.class);
            LOGGER.info("response from tiktok {}", response.getBody().toString());
        } catch (HttpStatusCodeException e) {
            LOGGER.error("[getTiktokPostData] Error from TikTok API: Status - {}, Response - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
        } catch (Exception ex) {
            LOGGER.error("[getTiktokPostData] Something went wrong ", ex);
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
        }
        return response.getBody();
    }

    @Override
    public TikTokPostStatusResponse getPostStatus(Integer businessId, String postId,BusinessTiktokAccounts tiktokAccounts) {
        TiktokAccessTokenDataResponse tiktokAccessTokenResponse = tiktokExternalService.generateRefreshToken(tiktokAccounts.getRefreshToken());
        if (tiktokAccessTokenResponse == null) {
            LOGGER.warn("tiktokAccessTokenResponse is null, unable to retrieve access token.");
            return null;
        }
        String accessToken = tiktokAccessTokenResponse.getAccessToken();
        try {
            String url = String.format("%s?business_id=%s&publish_id=%s", TIKTOK_POST_STATUS_URL, tiktokAccounts.getProfileId(), postId);

            HttpHeaders headers = new HttpHeaders();
            headers.set("Access-Token", accessToken);

            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<TikTokPostStatusResponse> response = socialRestTemplate.exchange(url, HttpMethod.GET, entity, TikTokPostStatusResponse.class);

            return response.getBody();
        } catch (HttpStatusCodeException e) {
            LOGGER.error("[getPostStatus] Error from TikTok Post Status API: Status - {}, Response - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
        } catch (Exception ex) {
            LOGGER.error("[getPostStatus] Something went wrong ", ex);
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
        }
    }

    @Override
    public TikTokPageData getTiktokPageData(TiktokInsightRequest tiktokInsightRequest) {
        ResponseEntity<TikTokPageData> response;
        try{
            MultiValueMap<String , String>  parametersMap =  getPageParams(tiktokInsightRequest);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Access-Token", tiktokInsightRequest.getAccessToken());
            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);
            response = socialRestTemplate.exchange(buildUri(TIKTOK_PROFILE_API_URL, parametersMap), HttpMethod.GET, requestEntity, TikTokPageData.class);
            LOGGER.info("response from tiktok {}", response.getBody().toString());
            if(Objects.nonNull(response.getBody()) && (!Objects.equals(response.getBody().getCode(), 200) && !Objects.equals(response.getBody().getCode(), 0))) {
                LOGGER.error("[getTiktokPostData] Validation Error from TikTok API: Status - {}, Response Message - {}", response.getBody().getCode(), response.getBody().getMessage());
                throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
            }
        } catch (HttpStatusCodeException e) {
            LOGGER.error("[getTiktokPostData] Error from TikTok API: Status - {}, Response - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
        } catch (Exception ex) {
            LOGGER.error("[getTiktokPostData] Something went wrong ", ex);
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
        }
        return response.getBody();
    }


    private URI buildUri(String path, MultiValueMap<String, String> parameters) {
        if(Objects.isNull(parameters)) {
            return org.springframework.social.support.URIBuilder.fromUri(path).build();
        }
        return org.springframework.social.support.URIBuilder.fromUri(path).queryParams(parameters).build();
    }

    private MultiValueMap<String, String> getVideoListParams(String profileId, Long cursor) {

        MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
        parametersMap.add("fields", TIKTOK_VIDEO_LIST_FIELDS);
        parametersMap.add("business_id", profileId);


        if(Objects.nonNull(cursor)) {
            parametersMap.add("cursor", cursor.toString());
        }

        return parametersMap;
    }

    private MultiValueMap<String, String> getVideoParams(String profileId, String videoId) {

        MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
        parametersMap.add("fields", TIKTOK_VIDEO_LIST_FIELDS);
        parametersMap.add("business_id", profileId);

        // set video id
        List<String> videoIds = Collections.singletonList(videoId);
        String filtersParam = "{\"video_ids\":[\"" + String.join("\",\"", videoIds) + "\"]}";
        parametersMap.add("filters", filtersParam);

        return parametersMap;

    }

    private MultiValueMap<String, String> getPageParams(TiktokInsightRequest tiktokInsightRequest) {
        MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
        parametersMap.add("fields", tiktokInsightRequest.getFields());
        parametersMap.add("business_id", tiktokInsightRequest.getProfileId());
        if(Objects.nonNull(tiktokInsightRequest.getStartDate()))
            parametersMap.add("start_date", tiktokInsightRequest.getStartDate());
        if(Objects.nonNull(tiktokInsightRequest.getEndDate()))
            parametersMap.add("end_date", tiktokInsightRequest.getEndDate());
        return parametersMap;
    }

}
