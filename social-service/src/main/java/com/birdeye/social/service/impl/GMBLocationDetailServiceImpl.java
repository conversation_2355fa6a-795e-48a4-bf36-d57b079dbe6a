/**
 *
 *
 */
package com.birdeye.social.service.impl;

import com.birdeye.social.bam.BAMAggregationService;
import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.businessgetpage.IBusinessGetPageService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessGMBLocationRawRepository;
import com.birdeye.social.dao.BusinessGetPageOpenUrlReqRepo;
import com.birdeye.social.dao.GoogleMessagesAgentRepository;
import com.birdeye.social.dao.reports.GMBKeywordAnalyticsRepository;
import com.birdeye.social.dao.reports.GMBKeywordSyncRepository;
import com.birdeye.social.dto.*;
import com.birdeye.social.dto.BusinessGMBLocationAggregation;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.GMBAccountDTO;
import com.birdeye.social.dto.GMBPageAnalyticsRequestDTO;
import com.birdeye.social.dto.GMBReportAnalyticsResponse;
import com.birdeye.social.dto.GoogleMyBusinessPagesDTO;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialPageUpdateException;
import com.birdeye.social.exception.TooManyRequestException;
import com.birdeye.social.external.request.google.*;
import com.birdeye.social.external.response.google.*;
import com.birdeye.social.external.service.IGoogleService;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.google.response.GoogleErrorResponse;
import com.birdeye.social.googleplus.GooglePlusService;
import com.birdeye.social.googleplus.GoogleProfileResponse;
import com.birdeye.social.googleplus.IGMBService;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.gmb.GMBServiceArea;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.platform.dao.BusinessGMBLocationRepository;
import com.birdeye.social.platform.entities.BusinessGMBLocation;
import com.birdeye.social.platform.entities.Location;
import com.birdeye.social.service.*;
import com.birdeye.social.sro.GMBAccountSyncRequest;
import com.birdeye.social.sro.GoogleAuthToken;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.SocialProxyHandler;
import com.birdeye.social.utils.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.ZSetOperations.TypedTuple;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 *
 */
@Service
public class GMBLocationDetailServiceImpl extends SocialAccountSetupCommonService implements GMBLocationDetailService {
	
	private static final Logger	logger = LoggerFactory.getLogger(GMBLocationDetailServiceImpl.class);

	private static final String GMB_KEYWORD_KAFKA_TOPIC = "gmb-keyword-topic";

	@Autowired
	private BusinessGMBLocationRawRepository	socialGmbRepo;

	@Autowired
	private GMBOldObjects gmbOldObjects;

	@Autowired
	private IBrokenIntegrationService brokenIntegrationService;
	
	@Autowired
	private BusinessGMBLocationRepository	businessGMBLocationRepository;
	
	@Autowired
	private GoogleMyBusinessPageService		gmbPageService;
	
	@Autowired
	private IGMBService gmbService;
	
	@Autowired
	private GoogleAuthenticationService googleAuthenticationService;
	
	@Autowired
	private IRedisLockService  redisService;

	@Autowired
	private BAMAggregationService bamAggregationService;

	@Autowired
	private IBusinessCachedService businessService;

	@Autowired
	private KafkaProducerService	producer;
	
	@Autowired
	private ISocialAppService socialAppService;

	@Autowired
	private GooglePlusService googlePlusService;
	
    @Autowired
	private GoogleAccessTokenCache gAccessToken;
    
    @Autowired
    private IGoogleService googleService;

    @Autowired
     private IRedisExternalService redisExternalService;
    
    @Autowired
    private IGMBLocationAuditService gmbLocationAuditService;
  	
	@Autowired
	private IBusinessGetPageService businessGetPageService;
	
	@Autowired
	private BusinessGetPageOpenUrlReqRepo		businessGetPageOpenUrlReqRepo;

	@Autowired
	private GoogleSocialAccountService googleSocialAccountService;

	@Autowired
	private IBusinessCoreService businessCoreService;

	@Autowired
	private IGoogleAccountService googleAccountService;

	@Autowired
	private SocialProxyHandler socialProxyHandler;

	@Autowired
	private GoogleMessagesAgentService googleMsgAgentService;

	@Autowired
	private GoogleMessagesAgentRepository agentRepo;

	@Autowired
	private CommonService commonService;

	@Autowired
	private GMBKeywordAnalyticsRepository gmbKeywordAnalyticsRepository;

	@Autowired
	private GMBKeywordSyncRepository gmbKeywordSyncRepository;

	@Autowired
	private KafkaProducerService kafkaProducer;

	@Autowired
	private NexusService nexusService;


	/* (non-Javadoc)
	 * @see com.birdeye.social.service.GMBLocationDetailService#getGMBLocationDetails(java.lang.String, com.birdeye.social.platform.entities.BusinessGMBLocation)
	 * 
	 * Method to verify GMB Location Status from Google and Update is_valid flag in case of Disconnected, Duplicate or Unverified Location
	 */
	@Async
	@Override
	public void getGMBLocationDetails(String accessToken, BusinessGoogleMyBusinessLocation gmbRawLocation) {
		try {
			GMBPageLocation locationDetail = gmbService.getGMBLocationDetails(accessToken, gmbRawLocation.getLocationId());
			if (locationDetail != null) {
				logger.info("Updating LocationState in Social for location : {}", gmbRawLocation.getLocationId());
				gmbRawLocation.setLocationState(prepareLocationState(locationDetail.getMetadata()));
				socialGmbRepo.saveAndFlush(gmbRawLocation);
				locationDetail.setLocationState(prepareLocationStateObjectInfo(locationDetail.getMetadata()));

				if (Objects.nonNull(locationDetail.getLocationState()) && (locationDetail.getLocationState().getIsDuplicate()!=null && locationDetail.getLocationState().getIsDuplicate())
						|| !(locationDetail.getLocationState().getHasVoiceOfMerchant()!=null && locationDetail.getLocationState().getHasVoiceOfMerchant())) {
					logger.info("Zero reviews received from Google. Location {} is Disconnected or Duplicate or Unverified.", locationDetail.getName());
					// update is_valid flag in Social and Mapping table
					gmbRawLocation.setErrorLog("Location is Disconnected or Duplicate or Unverified");
					updateIsValidGMB(gmbRawLocation);
					
					//Saving Updated location in redis cache
					redisService.saveInvalidLocationForReviews(gmbRawLocation.getLocationId());
				} else {
					logger.info("Zero reviews received from Google for location {}", locationDetail.getName());
				}
			} else {
				logger.info("LocationState is null for {}. Exception from Google", gmbRawLocation.getLocationUrl());
			}
		} catch (BirdeyeSocialException ex) {
			if (ex.getCode() ==  ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value()) {
				gmbRawLocation.setErrorLog(ex.getMessage());
				updateIsValidGMB(gmbRawLocation);
			}
			if(ex.getCode() == ErrorCodes.INVALID_REQUEST.value()) {
				throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST);
			}
		}
	}

	private void updateIsValidGMB(BusinessGoogleMyBusinessLocation gmbLocation) {
		logger.info("Marking Location {} as invalid", gmbLocation.getLocationId());
		gmbLocation.setIsValid(0);
		gmbLocation.setUpdatedAt(new Date());
		socialGmbRepo.saveAndFlush(gmbLocation);
		gmbPageService.updateGMBLocationIsValidStatus(gmbLocation.getLocationId(), 0);
	}
	
	private static String prepareLocationState(GMBLocationPageUrl metaData) {
		GMBLocationState state = new GMBLocationState();
		try {
			ObjectMapper mapper = new ObjectMapper();
			createLocationState(metaData,state);
			return mapper.writeValueAsString(state);

		} catch (Exception exe) {
			logger.error("Error {} occurs while preparing location state {}", exe.getLocalizedMessage(), state);
		}
		return null;
	}
	
	private BusinessGoogleMyBusinessLocation findByLocationId(
			String locationId) {
		List<BusinessGoogleMyBusinessLocation> gmbRawLocations = socialGmbRepo
				.findByLocationId(locationId);
		if (gmbRawLocations == null || gmbRawLocations.isEmpty()) {
			logger.error(
					"No location found in GMB raw location for locationId: {}",
					locationId);
			throw new BirdeyeSocialException(
					ErrorCodes.GMB_PAGE_LOCATION_NOT_FOUND);
		}
		BusinessGoogleMyBusinessLocation gmbRawLocation = gmbRawLocations
				.stream().filter(l -> l.getEnterpriseId() != null).findFirst()
				.get();
		return gmbRawLocation;
	}
	
	/* (non-Javadoc)
	 * @see com.birdeye.social.service.SocialPostService#gmbLocationValidation(java.lang.String)
	 */
	@Override
	public GMBPageLocation gmbLocationValidationForBusiness(String locationId) {
		logger.info("Request received to verify Location State for gmb location id {}", locationId);
		BusinessGoogleMyBusinessLocation gmbRawLocation = findByLocationId(locationId);
		return gmbLocationValidation(gmbRawLocation);
	}

	@Override
	public GMBPageLocationForCore getGMBPageInfoForBusiness(Integer businessId, Boolean supportedAttributes) throws Exception {
		logger.info("getGMBPageInfoForBusiness: businessId {} supportedAttributes {}", businessId, supportedAttributes);
		if ( businessId == null )
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid args present");
		final BusinessGoogleMyBusinessLocation gmbLocation = socialGmbRepo.findByBusinessId(businessId);
		if (Objects.isNull(gmbLocation))
			throw new BirdeyeSocialException(ErrorCodes.GMB_MAPPING_NOT_FOUND, "Mapping is not present for given businessId");
		if (gmbLocation.getIsValid() != 1)
			throw new BirdeyeSocialException(ErrorCodes.GMB_MAPPING_INVALID, "GMB mapping is invalid");
		logger.info("gmbLocation: gmbLocation {}", gmbLocation.getLocationId());

		final String accessToken = gAccessToken.getGoogleAccessToken(gmbLocation);
		if(StringUtils.isEmpty(accessToken)) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GENERATE_ACCESS_TOKEN_ON_GMB, "Failed to generate access token");
		}
		final GMBPageLocation gmbPageLocation = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(()->
				gmbService.getGMBLocationDetails(accessToken,gmbLocation.getLocationId())
		);
		GMBAttribute attribute = socialProxyHandler.runWithRetryableBirdeyeException(()->
				gmbService.getGMBAttribute(accessToken,gmbLocation.getLocationId())
		);
		GMBPlaceActionsResponseList getPlaceActionsResponseList = googleService.getGMBPlaceActionAttributes(gmbLocation.getLocationId(), accessToken);
		mergeAllGmbAttributes(attribute, getPlaceActionsResponseList);
		if(Objects.isNull(gmbPageLocation)){
			logger.error("gmbPageLocation is null");
			throw new BirdeyeSocialException(ErrorCodes.GMB_PAGE_LOCATION_NOT_FOUND, "Get GMB Page Location Error");
		}
		gmbPageLocation.setAttributeResponse(attribute);

		final GMBPageLocationForCore gmbPageLocationForCore = prepareLocationObject(gmbPageLocation,gmbLocation.getLocationUrl());
		// Set outer regionCode from address
		if (Objects.nonNull(gmbPageLocation.getStorefrontAddress()) && Objects.nonNull(gmbPageLocation.getStorefrontAddress().getRegionCode()) && StringUtils.isNotEmpty(gmbPageLocation.getStorefrontAddress().getRegionCode())) {
			gmbPageLocationForCore.setRegionCode(gmbPageLocation.getStorefrontAddress().getRegionCode());
		} else {
			// Try to fetch regionCode from location table
			final BusinessLiteDTO businessLiteDTO = businessService.getBusinessLiteWithLocationRequired(businessId);
			if ((Objects.nonNull(businessLiteDTO.getLocation())) && StringUtils.isNotEmpty(businessLiteDTO.getLocation().getCountryCode())) {
				gmbPageLocationForCore.setRegionCode(businessLiteDTO.getLocation().getCountryCode());
			} else {
				logger.warn("getGMBPageInfoForBusiness: countryCode not present for businessId {}", businessId);
			}
		}

		// Fetch all the supported attributes by this category
		if (supportedAttributes) {
			final GMBAttributesRequest attributesRequest = new GMBAttributesRequest();
			attributesRequest.setCategoryId(Objects.nonNull(gmbPageLocation.getCategories()) && Objects.nonNull(gmbPageLocation.getCategories().getPrimaryCategory())  ?
					gmbPageLocation.getCategories().getPrimaryCategory().getName() : null);
			attributesRequest.setRegionCode(gmbPageLocationForCore.getRegionCode());
			attributesRequest.setLanguageCode(gmbPageLocation.getLanguageCode());
			final GMBAttributes gmbAttributes = gmbService.getGMBAttributes(accessToken, attributesRequest);
			if (gmbAttributes != null && CollectionUtils.isNotEmpty(gmbAttributes.getAttributeMetadata())) {
				gmbPageLocationForCore.setSupportedAttributes(gmbOldObjects.convertAttribute(gmbAttributes.getAttributeMetadata()));
			}
		}
		return gmbPageLocationForCore;
	}

	private void mergeAllGmbAttributes(GMBAttribute gmbAttribute, GMBPlaceActionsResponseList getPlaceActionsResponseList) throws Exception {
		if(Objects.isNull(gmbAttribute) || Objects.isNull(getPlaceActionsResponseList) ||
				CollectionUtils.isEmpty(getPlaceActionsResponseList.getPlaceActionLinks())) {
			return;
		}
		Map<String, PlaceActionLinkResponse> placeActionMap = getPlaceActionMap(getPlaceActionsResponseList);
		if (CollectionUtils.isNotEmpty(gmbAttribute.getAttributes())) {
			ListIterator<Attribute> iter = gmbAttribute.getAttributes().listIterator();
			while (iter.hasNext()) {
				// Do not send attributes alone for appointmemt and reservation if placeaction
				// for them is empty
				Attribute attr = iter.next();
				if (Objects.nonNull(attr) && StringUtils.isNotEmpty(attr.getName())) {
					if ("attributes/url_reservations".equals(attr.getName())
							|| "attributes/url_appointment".equals(attr.getName())
							|| "attributes/url_food_takeout".equals(attr.getName())
							|| "attributes/url_food_delivery".equals(attr.getName())
							|| "attributes/url_shop_online".equals(attr.getName())) {
						iter.remove();
					}
				}
			}
		}

		if(MapUtils.isEmpty(placeActionMap)) {
			return;
		}
		for(Map.Entry<String, PlaceActionLinkResponse> entry: placeActionMap.entrySet()) {
			if(GMBPlaceActionType.APPOINTMENT.toString().equals(entry.getKey())) {
				Attribute attribute = new Attribute();
				attribute.setName("attributes/url_appointment");
				attribute.setValueType(AttributeValueType.URL);
				UrlAttributeValue urlAttributeValue = new UrlAttributeValue();
				urlAttributeValue.setUri(placeActionMap.get(GMBPlaceActionType.APPOINTMENT.toString())!=null?placeActionMap.get(GMBPlaceActionType.APPOINTMENT.toString()).geturi():null);
				List<UrlAttributeValue> attributeValueList = new ArrayList<>();
				attributeValueList.add(urlAttributeValue);
				attribute.setUriValues(attributeValueList);
				if(Objects.isNull(gmbAttribute.getAttributes()))
					gmbAttribute.setAttributes(new ArrayList<>());
				gmbAttribute.getAttributes().add(attribute);
			} else if(GMBPlaceActionType.DINING_RESERVATION.toString().equals(entry.getKey())){
				Attribute attribute = new Attribute();
				attribute.setName("attributes/url_reservations");
				attribute.setValueType(AttributeValueType.URL);
				UrlAttributeValue urlAttributeValue = new UrlAttributeValue();
				urlAttributeValue.setUri(placeActionMap.get(GMBPlaceActionType.DINING_RESERVATION.toString())!=null?placeActionMap.get(GMBPlaceActionType.DINING_RESERVATION.toString()).geturi():null);
				List<UrlAttributeValue> attributeValueList = new ArrayList<>();
				attributeValueList.add(urlAttributeValue);
				attribute.setUriValues(attributeValueList);
				if(Objects.isNull(gmbAttribute.getAttributes()))
					gmbAttribute.setAttributes(new ArrayList<>());
				gmbAttribute.getAttributes().add(attribute);
			} else if(GMBPlaceActionType.FOOD_DELIVERY.toString().equals(entry.getKey())){
				Attribute attribute = new Attribute();
				attribute.setName("attributes/url_food_delivery");
				attribute.setValueType(AttributeValueType.URL);
				UrlAttributeValue urlAttributeValue = new UrlAttributeValue();
				urlAttributeValue.setUri(placeActionMap.get(GMBPlaceActionType.FOOD_DELIVERY.toString())!=null?placeActionMap.get(GMBPlaceActionType.FOOD_DELIVERY.toString()).geturi():null);
				List<UrlAttributeValue> attributeValueList = new ArrayList<>();
				attributeValueList.add(urlAttributeValue);
				attribute.setUriValues(attributeValueList);
				if(Objects.isNull(gmbAttribute.getAttributes()))
					gmbAttribute.setAttributes(new ArrayList<>());
				gmbAttribute.getAttributes().add(attribute);
			} else if(GMBPlaceActionType.FOOD_TAKEOUT.toString().equals(entry.getKey())){
				Attribute attribute = new Attribute();
				attribute.setName("attributes/url_food_takeout");
				attribute.setValueType(AttributeValueType.URL);
				UrlAttributeValue urlAttributeValue = new UrlAttributeValue();
				urlAttributeValue.setUri(placeActionMap.get(GMBPlaceActionType.FOOD_TAKEOUT.toString())!=null?placeActionMap.get(GMBPlaceActionType.FOOD_TAKEOUT.toString()).geturi():null);
				List<UrlAttributeValue> attributeValueList = new ArrayList<>();
				attributeValueList.add(urlAttributeValue);
				attribute.setUriValues(attributeValueList);
				if(Objects.isNull(gmbAttribute.getAttributes()))
					gmbAttribute.setAttributes(new ArrayList<>());
				gmbAttribute.getAttributes().add(attribute);
			} else if(GMBPlaceActionType.SHOP_ONLINE.toString().equals(entry.getKey())){
				Attribute attribute = new Attribute();
				attribute.setName("attributes/url_shop_online");
				attribute.setValueType(AttributeValueType.URL);
				UrlAttributeValue urlAttributeValue = new UrlAttributeValue();
				urlAttributeValue.setUri(placeActionMap.get(GMBPlaceActionType.SHOP_ONLINE.toString())!=null?placeActionMap.get(GMBPlaceActionType.SHOP_ONLINE.toString()).geturi():null);
				List<UrlAttributeValue> attributeValueList = new ArrayList<>();
				attributeValueList.add(urlAttributeValue);
				attribute.setUriValues(attributeValueList);
				if(Objects.isNull(gmbAttribute.getAttributes()))
					gmbAttribute.setAttributes(new ArrayList<>());
				gmbAttribute.getAttributes().add(attribute);
			}
		}
	}

	private Map<String, PlaceActionLinkResponse> getPlaceActionMap(GMBPlaceActionsResponseList getPlaceActionsResponseList) throws Exception {
		HashMap<String, PlaceActionLinkResponse> responseHashMap = new HashMap<>();
		if(Objects.isNull(getPlaceActionsResponseList) || CollectionUtils.isEmpty(getPlaceActionsResponseList.getPlaceActionLinks())) {
			return responseHashMap;
		}
		for(PlaceActionLinkResponse responseObject: getPlaceActionsResponseList.getPlaceActionLinks()) {
			if(Objects.isNull(responseObject) || Objects.isNull(responseObject.getPlaceActionType())) continue;
			if(responseHashMap.containsKey(responseObject.getPlaceActionType())) {
				PlaceActionLinkResponse existedObject = responseHashMap.get(responseObject.getPlaceActionType());
				if(Objects.nonNull(existedObject) && Objects.nonNull(existedObject.getIsPreferred()) && existedObject.getIsPreferred()) {
					continue;
				}
				if(Objects.nonNull(responseObject.getIsPreferred()) && responseObject.getIsPreferred()) {
					responseHashMap.put(responseObject.getPlaceActionType(), responseObject);
				} else {
					try {
						Date existedDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'").parse(existedObject.getUpdateTime());
						Date currentDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'").parse(responseObject.getUpdateTime());
						if(currentDate.after(existedDate)) {
							responseHashMap.put(responseObject.getPlaceActionType(), responseObject);
						}
					} catch (Exception e) {
						return new HashMap<>();
					}
				}
			} else {
				responseHashMap.put(responseObject.getPlaceActionType(), responseObject);
			}
		}
		return responseHashMap;
	}


	@Override
	public GMBPageLocationForCore prepareLocationObject(GMBPageLocation gmbPageLocation,String locationUrl) {
		GMBPageLocationForCore core = new GMBPageLocationForCore();
		core.setName(locationUrl);
		core.setAttributes(Objects.nonNull(gmbPageLocation.getAttributeResponse()) ?gmbOldObjects.convertAttributes( gmbPageLocation.getAttributeResponse().getAttributes()): new ArrayList<>());
		core.setStoreCode(gmbPageLocation.getStoreCode());
		core.setAdditionalCategories(Objects.nonNull(gmbPageLocation.getCategories()) ?gmbOldObjects.convertCategoryListResponse( gmbPageLocation.getCategories().getAdditionalCategories() ): new ArrayList<>());
		core.setAdditionalPhones(Objects.nonNull(gmbPageLocation.getPhoneNumbers()) ? gmbPageLocation.getPhoneNumbers().getAdditionalPhones(): new ArrayList<>());
		core.setAddress(gmbPageLocation.getStorefrontAddress());
		core.setSupportedAttributes(gmbOldObjects.convertAttribute(gmbPageLocation.getSupportedAttributes()));
		core.setLocationName(gmbPageLocation.getTitle());
		core.setLanguageCode(gmbPageLocation.getLanguageCode());
		core.setOpenInfo(gmbPageLocation.getOpenInfo());
		core.setServiceArea(gmbOldObjects.convertServiceArea(gmbPageLocation.getServiceArea()));
		core.setLatlng(gmbPageLocation.getLatlng());
		core.setWebsiteUrl(gmbPageLocation.getWebsiteUri());
		core.setLocationKey(new GMBLocationKey(null,Objects.nonNull(gmbPageLocation.getMetadata()) ? gmbPageLocation.getMetadata().getPlaceId(): ""));
		core.setPrimaryPhone(Objects.nonNull(gmbPageLocation.getPhoneNumbers()) ? gmbPageLocation.getPhoneNumbers().getPrimaryPhone() : null);
		core.setPrimaryCategory(Objects.nonNull(gmbPageLocation.getCategories()) ?gmbOldObjects.convertCategoryResponse( gmbPageLocation.getCategories().getPrimaryCategory()):null);
		core.setProfile(gmbPageLocation.getProfile());
		core.setMetadata(gmbOldObjects.convertMetaDataResponse(gmbPageLocation.getMetadata()));
		core.setLocationState(prepareLocationStateObjectInfo(gmbPageLocation.getMetadata()));
		core.setRegularHours(gmbOldObjects.prepareRegularHours(gmbPageLocation.getRegularHours()));
		core.setSpecialHours(gmbOldObjects.prepareSpecialHours(gmbPageLocation.getSpecialHours()));
		core.setServiceList(gmbOldObjects.convertServiceListResponse(locationUrl,gmbPageLocation.getServiceItems()));
		core.setMoreHours(gmbPageLocation.getMoreHours());
		return core;
	}

	private GMBLocationState prepareLocationStateObjectInfo(GMBLocationPageUrl metaData) {
		GMBLocationState state = new GMBLocationState();
		createLocationState(metaData, state);
		return state;
	}

	public static void createLocationState(GMBLocationPageUrl metaData, GMBLocationState state) {
		if (Objects.nonNull(metaData)) {
			if(Boolean.TRUE.equals(metaData.getHasVoiceOfMerchant())){
				state.setHasVoiceOfMerchant(true);
			} else {
				state.setHasVoiceOfMerchant(false);
			}
			if (Objects.nonNull(metaData.getDuplicateLocation())){
				state.setIsDuplicate(true);
			}
		}
	}


	@Override
	public GMBAttributesResponse getGMBAttributesList(Integer businessId, String categoryId, String country, String languageCode, Integer pageSize, String pageToken,String regionCode) {
		logger.info("getGMBAttributesList: locationId {} categoryId {} country {} languageCode {} pageSize {} pageToken {}",
				businessId, categoryId, country, languageCode, pageSize, pageToken);
		StringBuilder category = new StringBuilder("categories/").append(categoryId);
		if ( businessId != null && StringUtils.isNotEmpty(category) && StringUtils.isNotEmpty(languageCode) ) {
			try {
				final BusinessGoogleMyBusinessLocation gmbLocation = socialGmbRepo.findByBusinessId(businessId);
				if (Objects.isNull(gmbLocation))
					throw new BirdeyeSocialException(ErrorCodes.GMB_MAPPING_NOT_FOUND, "Mapping is not present for given businessId");
				if (gmbLocation.getIsValid() != 1)
					throw new BirdeyeSocialException(ErrorCodes.GMB_MAPPING_INVALID, "GMB mapping is invalid");
				logger.info("gmbLocation: gmbLocation {}", gmbLocation.getLocationId());

				final String accessToken = googleAuthenticationService.getGoogleAccessToken(gmbLocation.getRefreshTokenId());
				final GMBAttributesRequest attributesRequest = new GMBAttributesRequest();
				attributesRequest.setCategoryId(category.toString());
				attributesRequest.setRegionCode(country);
				attributesRequest.setLanguageCode(languageCode);
				attributesRequest.setPageSize(pageSize);
				attributesRequest.setPageToken(pageToken);
				GMBAttributesResponse gmbAttributes = gmbOldObjects.convertToAttributeResponse(gmbService.getGMBAttributes(accessToken, attributesRequest));
				return gmbAttributes;
			} catch (Exception exp) {
				Integer beAdminBusinessId = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBirdeyeAdminBusinessId();
				if (businessId.equals(beAdminBusinessId))
					throw exp;
				return getGMBAttributesList(beAdminBusinessId, categoryId, country, languageCode, pageSize, pageToken,regionCode);
			}
		} else {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid args specified");
		}
	}



	@Override
	public GMBMedia getGMBMedia(Integer businessId) throws Exception {
		logger.info("getGMBMedia: locationId {}", businessId);
		if ( businessId == null )
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid args present");

		final BusinessGoogleMyBusinessLocation gmbLocation = socialGmbRepo.findByBusinessId(businessId);
		logger.info("getGMBMedia: gmbLocation {}", gmbLocation);
		if (Objects.isNull(gmbLocation))
			throw new BirdeyeSocialException(ErrorCodes.GMB_MAPPING_NOT_FOUND, "Mapping is not present for given businessId");
		if (gmbLocation.getIsValid() != 1)
			throw new BirdeyeSocialException(ErrorCodes.GMB_MAPPING_INVALID, "GMB mapping is invalid");

		final BusinessGoogleMyBusinessLocation gmbRawLoc = findByLocationId(gmbLocation.getLocationId());
		if (gmbRawLoc.getIsValid() != 1)
			throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN, "GMB Raw location is invalid");

		final String accessToken = googleAuthenticationService.getGoogleAccessToken(gmbRawLoc.getRefreshTokenId());
		return googleService.getGoogleMedia(gmbRawLoc.getLocationUrl(), accessToken);
	}

	@Override
	public GMBCategoriesResponse getCategoriesList(String regionCode, String languageCode,
												   Integer businessId, String pageToken,
												   String searchTerm, String view, Integer pageSize) throws Exception {
		logger.info("getCategoriesList regionCode {} languageCode {} locationId {} pageToken {} searchTerm {} view {} pageSize {}",
				regionCode, languageCode, businessId, pageToken, searchTerm, view, pageSize);
		if (StringUtils.isNotEmpty(regionCode) && StringUtils.isNotEmpty(languageCode) && businessId != null) {
			final BusinessGoogleMyBusinessLocation gmbLocation = socialGmbRepo.findByBusinessId(businessId);
			if (Objects.isNull(gmbLocation))
				throw new BirdeyeSocialException(ErrorCodes.GMB_MAPPING_NOT_FOUND, "Mapping is not present for given businessId");
			if (gmbLocation.getIsValid() != 1)
				throw new BirdeyeSocialException(ErrorCodes.GMB_MAPPING_INVALID, "GMB mapping is invalid");
			logger.info("gmbLocation: gmbLocation {}", gmbLocation.getLocationId());

			final String accessToken = googleAuthenticationService.getGoogleAccessToken(gmbLocation.getRefreshTokenId());
			final GMBCategoriesRequest categoriesRequest = new GMBCategoriesRequest();
			categoriesRequest.setRegionCode(regionCode);
			categoriesRequest.setLanguageCode(languageCode);
			categoriesRequest.setPageToken(pageToken);
			categoriesRequest.setSearchTerm(searchTerm);
			if (StringUtils.isNotEmpty(view)) {
				categoriesRequest.setView(GMBCategoryView.valueOf(view));
			}else {
				categoriesRequest.setView(GMBCategoryView.BASIC);
			}
			categoriesRequest.setPageSize(pageSize);
			GMBCategoriesResponse gmbCategories = gmbOldObjects.convertToGMBCategoryResponse(gmbService.getCategoriesList(accessToken, categoriesRequest));
			return gmbCategories;
		} else {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid args specified for getCategoriesList");
		}
	}



	/**
	 * For successful API integration, location should be verified and not
	 * marked as disconnected/duplicate
	 * 
	 * @param locationState - GMB location state
	 */
	private static boolean isNotValid(GMBLocationPageUrl locationState, OpenInfo info){
		return (locationState.getHasVoiceOfMerchant()!=null && locationState.getHasVoiceOfMerchant())
		|| (locationState.getDuplicateLocation()!=null);
	}
	
	// GMB Location validation
	private GMBPageLocation gmbLocationValidation(BusinessGoogleMyBusinessLocation gmbRawLocation) {
		
		GMBPageLocation pageData = new GMBPageLocation();
		boolean initialStatus = (gmbRawLocation.getIsValid()==1?true:false);
		boolean valid = initialStatus;
		try {
			String accessToken = googleAuthenticationService.getGoogleAccessToken(gmbRawLocation.getRefreshTokenId());
			pageData = gmbService.getGMBLocationDetails(accessToken, gmbRawLocation.getLocationId());
			gmbRawLocation.setLocationState(prepareLocationState(pageData.getMetadata()));
			if (isNotValid(pageData.getMetadata(),pageData.getOpenInfo())) {
				logger.info("Integration Broken on Google. Location {} is Disconnected or Duplicate or Unverified.", pageData.getName());
				// update is_valid flag in Social and Mapping table
				valid=false;
			} else {
				logger.info("Integration is Valid on Google. Location {}", pageData.getName());
				valid = true;
			}
			
		} catch (BirdeyeSocialException e) {
			if (ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value() == e.getCode()) {
				logger.error("Unable to fetch data for Location id {} with specified token", gmbRawLocation.getLocationId());
				valid = false;
			}
		}
		boolean updateRequired = (valid != initialStatus);
		// If raw location is not valid, update flags on both tables.
		if (updateRequired) {
			int value = valid ? 1 : 0;
			logger.info("Updating GMB data for location : {} in social DB as valid :{}", gmbRawLocation.getLocationId(),valid);
			logger.info("Integration status Before# {} and After# {}",initialStatus, valid);
			gmbRawLocation.setIsValid(value);
			gmbRawLocation.setUpdatedAt(new Date());
			//TODO: In case of no changes, access token is invalid, page data is not present, no need for update.
			socialGmbRepo.saveAndFlush(gmbRawLocation);
			// Update mapped location
			brokenIntegrationService.pushValidIntegrationStatus(gmbRawLocation.getEnterpriseId(),SocialChannel.GMB.getName(), gmbRawLocation.getId(),value,gmbRawLocation.getLocationId());

			if(gmbRawLocation.getIsValid().equals(0)) {
				commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(gmbRawLocation),
						gmbRawLocation.getUserId(), gmbRawLocation.getBusinessId(), gmbRawLocation.getEnterpriseId());
			}
			// Update BAM in case of integration is fixed.
			if(valid && Objects.nonNull(gmbRawLocation.getBusinessId())){
				logger.info("Calling BAM for GMB location for placeId : {}", gmbRawLocation.getPlaceId());

				BAMUpdateRequest payload = new BAMUpdateRequest("gmb", gmbRawLocation.getBusinessId(), gmbRawLocation.getLocationMapUrl(),gmbRawLocation.getLocationId(), gmbRawLocation.getPlaceId());

				producer.sendWithKey(Constants.INTEGRATION_TOPIC, String.valueOf(gmbRawLocation.getBusinessId()), payload);
			}
		}
		return pageData;
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.GMBLocationDetailService#validateGmbLocationForEnterprise(java.lang.Long)
	 */
	@Override
	public void gmbLocationValidationForEnterprise(Long enterpriseId) {
		logger.info("Request received to verify Location State for enterprise id {}", enterpriseId);
		List<BusinessGoogleMyBusinessLocation> gmbRawLocation = socialGmbRepo.findByEnterpriseIdAndIsValid(enterpriseId, 1);
		if (CollectionUtils.isEmpty(gmbRawLocation)) {
			logger.error("No location found in GMB raw location for enterpriseId: {}", enterpriseId);
		} else {
			gmbRawLocation.parallelStream().forEach(location -> {
				try {
					gmbLocationValidation(location);
				} catch (Exception e) {
					logger.warn("Exception occurred while processing GMB location: {}", location.getLocationId());
				}
			});
		}
	}
	
	private static String getString(
			Map.Entry<String, Set<BusinessGoogleMyBusinessLocation>> entry) {
		final StringBuilder builder = new StringBuilder();
		builder.append("{").append("PlaceID:").append(entry.getKey())
				.append(" # ");
		entry.getValue().stream().forEach(gmbRawLoaction -> {
			builder.append("{").append("ID:").append(gmbRawLoaction.getId())
					.append(",").append("AccountID:")
					.append(gmbRawLoaction.getAccountId()).append(",")
					.append("LocationID:")
					.append(gmbRawLoaction.getLocationId()).append(",")
					.append("UserID: ").append(gmbRawLoaction.getUserId())
					.append("");
		});
		builder.append("}");
		return builder.toString();
	}
	
	//Place IDs uniquely identify a place in the Google Places database and on Google Maps.
	
	public void scanAccount(Long enterpriseId) {
		List<BusinessGoogleMyBusinessLocation> gmbRawLocations = socialGmbRepo
				.findByEnterpriseId(enterpriseId);
		if (gmbRawLocations == null || gmbRawLocations.isEmpty()) {
			throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND);
		}
		logger.info("Overall location count for enterprise:{} is - {}",
				enterpriseId, gmbRawLocations.size());
		Map<String, Set<BusinessGoogleMyBusinessLocation>> byPlaceId = gmbRawLocations
				.stream().filter(l -> l.getPlaceId() != null)
				.collect(Collectors.groupingBy(
						BusinessGoogleMyBusinessLocation::getPlaceId,
						Collectors.toSet()));
		logger.info("Unique placeId count for enterprise:{} is - {}",
				enterpriseId, byPlaceId.keySet().size());
	
		Set<String> uniqueLocationIds = byPlaceId.values().stream()
				.flatMap(x -> x.stream())
				.map(BusinessGoogleMyBusinessLocation::getLocationId)
				.collect(Collectors.toSet());
		logger.info("Unique locationId count for enterprise:{} is - {}",
				enterpriseId, uniqueLocationIds.size());
		
		Set<String> duplicatePlaceIds = byPlaceId
				.entrySet().stream().filter(e -> e.getValue().size() > 1)
				.map(l->getString(l))
				.collect(Collectors.toSet());

		logger.info("Duplicate placeId data {} ",duplicatePlaceIds);

		// Mapped location information
		List<BusinessGMBLocation> mappedBirdeyeLocation = businessGMBLocationRepository
				.findByLocationIdIn(uniqueLocationIds);
		logger.info("Mapped locationId count for enterprise:{} is - {}",
				enterpriseId, mappedBirdeyeLocation.size());
		
		Set<String> validMappedLocationIds = mappedBirdeyeLocation.stream()
				.filter(bl -> bl.getIsValid() == 1).map(l -> l.getLocationId())
				.collect(Collectors.toSet());
		
		logger.info("Valid Mapped locationId count for enterprise:{} is - {}",
				enterpriseId, validMappedLocationIds.size());
		
		Set<String> rawLocationIds = gmbRawLocations.stream()
				.filter(bl -> bl.getIsValid() == 1).map(l -> l.getLocationId())
				.collect(Collectors.toSet());
		// Possible problamatic cases.
		 validMappedLocationIds.removeAll(rawLocationIds);
		 logger.info("Unsynced data for enterprise:{} is - {}",
					enterpriseId, validMappedLocationIds);
	}
	
	/**
	 * Check status of GMB Location and delete only if it is disconnected or duplicate.
	 */
	public void deleteGmbLocationForBusiness(String locationId) {
		logger.info("Request received to delete GMB location with id {}",
				locationId);
		BusinessGoogleMyBusinessLocation gmbRawLocation = findByLocationId(
				locationId);
		boolean removeRequired = false;
		try {
			String accessToken = googleAuthenticationService.getGoogleAccessToken(
					gmbRawLocation.getRefreshTokenId());
			GMBPageLocation pageData = gmbService.getGMBLocationDetails(accessToken,
					gmbRawLocation.getLocationId());
			gmbRawLocation.setLocationState(prepareLocationState(pageData.getMetadata()));
			if (isNotValid(pageData.getMetadata(),pageData.getOpenInfo())) {
				logger.info(
						"Integration Broken on Google. Location {} is Disconnected: {} or Duplicate: {} or Unverified: {} ",
						pageData.getName(),
						pageData.getMetadata().getDuplicateLocation(),
						pageData.getMetadata().getHasVoiceOfMerchant(),
						pageData.getOpenInfo().getStatus());
				// update is_valid flag in Social and Mapping table
				removeRequired = true;

			} else {
				logger.info("Integration is Valid on Google. Location {}",
						pageData.getName());

			}

		} catch (BirdeyeSocialException e) {
			if (ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value() == e
					.getCode()) {
				logger.error(
						"Unable to fetch data for Location id {} with specified token",
						gmbRawLocation.getLocationId());
				removeRequired = true;
			}
		}
		// If raw location is not valid, update flags on both tables.
		if (removeRequired) {
			logger.info("Deleting GMB data for location : {} in social DB with BE ID {}  ",gmbRawLocation.getLocationId(),gmbRawLocation.getId());
			socialGmbRepo.delete(gmbRawLocation);
			producer.sendObject(Constants.SOCIAL_PAGE_REMOVED,Collections.singletonList(new ChannelPageRemoved(
					SocialChannel.GMB.getName(),
					gmbRawLocation.getLocationId(),
					gmbRawLocation.getLocationName(),
					gmbRawLocation.getBusinessId(),
					gmbRawLocation.getEnterpriseId(),
					gmbRawLocation.getPlaceId(),
					null,
					gmbRawLocation.getId()
			)));
		}
	}
	
	//https://developers.google.com/identity/protocols/OpenIDConnect
	public Map<String, List<GoogleMyBusinessPagesDTO>> getGMBAccountDetails(Integer refreshTokenId) {
		GoogleAuthToken token = new GoogleAuthToken();
		token.setRefreshTokenId(refreshTokenId);
		String accessToken = googleAuthenticationService.getGoogleAccessToken(refreshTokenId);
		token.setAccess_token(accessToken);
		long start = System.currentTimeMillis();
		Map<String, GoogleMyBusinessPagesDTO> data = gmbPageService.getGoogleMyBusinessPagesDTO(token, 1);
		logger.info("[getGMBAccountDetails] Total time taken in fetching {} GMB pages: {}", data.size(), (System.currentTimeMillis() - start) / 1000);
		Map<String, List<GoogleMyBusinessPagesDTO>> map = data.values().stream().collect(Collectors.groupingBy(GoogleMyBusinessPagesDTO::getAccountId));
		logger.info("[getGMBAccountDetails] map: {}", map);
		return map;
	}

	//https://developers.google.com/identity/protocols/OpenIDConnect
	public Map<?,?> getUserDetailsInfo(Integer refreshTokenId){
		String accessToken = googleAuthenticationService.getGoogleAccessToken(refreshTokenId);
		GoogleProfileResponse user = googlePlusService.getUserDetailsForGoogleUser(accessToken);
		Map<String,String> data = new  HashMap<>();
		if(user!=null){
			data.put("ID", user.getId());
			data.put("Name", user.getName());
		}
		return data;
	}

  @Override
  public GMBReportInsightsResponse getBusinessReportInsights(Integer businessId, GMBReportInsightsRequest insightsRequest) {
    logger.info("getBusinessReportInsights for businessId: {} and insightsRequest is : {}",businessId, insightsRequest);
    validateGMBReportInsightsRequest(insightsRequest);
    BusinessGoogleMyBusinessLocation gmbLocation = socialGmbRepo.findByBusinessIdAndIsValid(businessId,1);
    if (gmbLocation == null) {
        logger.error("Error while fetching location report insights as gmb location not found/invalid for business id {}", businessId);
        throw new BirdeyeSocialException(ErrorCodes.GMB_PAGE_NOT_FOUND, "GMB location isn't integrated or inactive");
    } 
    String locationName = gmbLocation.getLocationUrl();
    String locationId = gmbLocation.getLocationId();
    if(StringUtils.isEmpty(locationName)) {
      logger.error("getBusinessReportInsights Location name is null or empty for businessId {} ",businessId);
      throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GET_LOCATION_NAME, "LocationName is null or empty for this businessId");
    }
    GMBLocationMetricsResponse gmbReportInsightsResponse = null;
    //Cache access token
     String  accessToken = gAccessToken.getGoogleAccessToken(gmbLocation);
    if (accessToken == null) {
      throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GENERATE_ACCESS_TOKEN_ON_GMB, "Exception occured while generating accessToken on gmb");
    } 
    gmbReportInsightsResponse =  googleService.getGMBLocationReportInsights(accessToken, insightsRequest.getGMBLocationReportRequest(locationName));
    return convertToResponse(locationId, insightsRequest.getStartDate(), insightsRequest.getEndDate(), gmbReportInsightsResponse);
  }

	@Override
	public GMBReportInsightsResponse getPostReportInsights(String postId, Integer businessId, GMBReportInsightsRequest insightsRequest) {
		logger.info("getBusinessReportInsights for businessId: {} and insightsRequest is : {}",businessId, insightsRequest);
		validateGMBReportInsightsRequest(insightsRequest);
		BusinessGoogleMyBusinessLocation gmbLocation = socialGmbRepo.findByBusinessIdAndIsValid(businessId,1);
		if (gmbLocation == null) {
			logger.error("Error while fetching location report insights as gmb location not found/invalid for business id {}", businessId);
			throw new BirdeyeSocialException(ErrorCodes.GMB_PAGE_NOT_FOUND, "GMB location isn't integrated or inactive");
		}
		String locationName = gmbLocation.getLocationUrl();
		String locationId = gmbLocation.getLocationId();
		if(StringUtils.isEmpty(locationName)) {
			logger.error("getBusinessReportInsights Location name is null or empty for businessId {} ",businessId);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GET_LOCATION_NAME, "LocationName is null or empty for this businessId");
		}
		GMBLocalPostMetricsResponse gmbLocalPostMetricsResponse = null;
		//Cache access token
		String  accessToken = gAccessToken.getGoogleAccessToken(gmbLocation);
		if (accessToken == null) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GENERATE_ACCESS_TOKEN_ON_GMB, "Exception occured while generating accessToken on gmb");
		}
		gmbLocalPostMetricsResponse =  googleService.getGMBPostReportInsights(locationName, accessToken, insightsRequest.getGMBLocalPostReportRequest(postId));

		return convertLocalPostMetricsToResponse(locationId, insightsRequest.getStartDate(), insightsRequest.getEndDate(), gmbLocalPostMetricsResponse);
	}

  private void validateGMBReportInsightsRequest(GMBReportInsightsRequest insightsRequest) {    
    if(CollectionUtils.isEmpty(insightsRequest.getMetric()) || CollectionUtils.isEmpty(insightsRequest.getMetricOption())) {
      throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Metrics or MetricOptions cannot be null or blank.");
    }
    for(String metric : insightsRequest.getMetric()) {
      if(!MetricEnum.contains(metric)) {
        throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Metrics should be of type MetricEnum.");
      }
    }
    for(String metricOption : insightsRequest.getMetricOption()) {
      if(!MetricOptionEnum.contains(metricOption)) {
        throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "MetricOption should be of type MetricOptionEnum.");
      }
    }
    if(insightsRequest.getStartDate() == null || insightsRequest.getStartDate().isEmpty() || insightsRequest.getEndDate() == null
        || insightsRequest.getEndDate().isEmpty())
      throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "StartDate or EndDate cannot be null or empty.");

    if(!insightsRequest.isValidDate(insightsRequest.getStartDate()) || !insightsRequest.isValidDate(insightsRequest.getEndDate()))
      throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "StartDate or EndDate should be in ISO_DATE format(yyyy-mm-dd)");
    
    LocalDate startTime = LocalDate.parse(insightsRequest.getStartDate());
    LocalDate endTime = LocalDate.parse(insightsRequest.getEndDate());
    if(endTime.isEqual(startTime)) 
      throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT,"EndDate cannot be equal or before startDate");
    LocalDate currentDate = LocalDate.now(ZoneOffset.UTC);
    currentDate = currentDate.minusMonths(18L);
    currentDate = currentDate.plusDays(1L);
    if (startTime.isBefore(currentDate) || startTime.isEqual(currentDate))
      throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Maximum startDate can be 18 months ago.");
  }

	private GMBReportInsightsResponse convertLocalPostMetricsToResponse(String locationId, String startDate , String endDate, GMBLocalPostMetricsResponse gmbLocalPostMetricsResponse) {
		if(gmbLocalPostMetricsResponse == null || gmbLocalPostMetricsResponse.getLocalPostMetrics() == null)
			return new GMBReportInsightsResponse();

		List<LocationMetricsResponse> locationMetricsResponses = new ArrayList<LocationMetricsResponse>();
		List<GMBLocalPostMetrics> localPostMetricsList = gmbLocalPostMetricsResponse.getLocalPostMetrics();

		List<MetricsResponse> metrices = new ArrayList<>();
		for(GMBLocalPostMetrics localPostMetrics : localPostMetricsList) {
			LocationMetricsResponse locationMetrics = new LocationMetricsResponse();

			MetricsResponse metricsResponse = new MetricsResponse();
			for(MetricTotalValues metricTotalValues : localPostMetrics.getMetricValues()) {
			 if(MetricEnum.LOCAL_POST_VIEWS_SEARCH.name().equalsIgnoreCase(metricTotalValues.getMetric().name())) {
				 metricsResponse.setLocalPostViews(Integer.parseInt(metricTotalValues.getTotalValue().getValue()));
			 } else if(MetricEnum.LOCAL_POST_ACTIONS_CALL_TO_ACTION.name().equalsIgnoreCase(metricTotalValues.getMetric().name())) {
				 metricsResponse.setLocalPostCta(Integer.parseInt(metricTotalValues.getTotalValue().getValue()));
			 }
			}
			metrices.add(metricsResponse);
			locationMetrics.setMetrices(metrices);
			locationMetrics.setLocationName(localPostMetrics.getLocalPostName());
			locationMetrics.setTimeZone(gmbLocalPostMetricsResponse.getTimeZone());
			locationMetrics.setAllMetricsDate(startDate);
			locationMetricsResponses.add(locationMetrics);

		}
		return new GMBReportInsightsResponse(locationMetricsResponses);
	}


	private GMBReportInsightsResponse convertToResponse(String locationId, String startDate , String endDate, GMBLocationMetricsResponse gmbReportInsightsResponse) {
   if(gmbReportInsightsResponse == null || gmbReportInsightsResponse.getLocationMetrics() == null) 
     return new GMBReportInsightsResponse();
   String timeZone = null;
   String allMetricsDate = null;
   List<MetricValues> metricValuesList = null;
   LocalDate startRange = LocalDate.parse(startDate);
   LocalDate endRange = LocalDate.parse(endDate);
   List<LocationMetricsResponse> locationMetricsResponses = new ArrayList<LocationMetricsResponse>();
   List<GMBLocationMetrics> gmbLocationMetricsList = gmbReportInsightsResponse.getLocationMetrics();
   for(GMBLocationMetrics gmbLocationMetrics : gmbLocationMetricsList) {
     LocationMetricsResponse locationMetricsResponse = new LocationMetricsResponse();
     timeZone = gmbLocationMetrics.getTimeZone();
     metricValuesList = gmbLocationMetrics.getMetricValues();
     locationMetricsResponse.setLocationName(locationId);
     locationMetricsResponse.setTimeZone(timeZone);
     Map<LocalDate, MetricsResponse> map = new LinkedHashMap<LocalDate, MetricsResponse>();
     if(metricValuesList != null) {
     for(MetricValues metricValues : metricValuesList) {
       MetricEnum metric = metricValues.getMetric();
       if(metricValues.getDimensionalValues() != null) {
       for(MetricDimensionalValue metricDimensionalValue : metricValues.getDimensionalValues()) {
         MetricOptionEnum metricOptionEnum = metricDimensionalValue.getMetricOption();
         Integer value = null;
         LocalDate date = null;
         MetricsResponse metricResponse = null;
         if(metricOptionEnum.equals(MetricOptionEnum.AGGREGATED_DAILY)) {
           Instant startTime = Instant.parse(metricDimensionalValue.getTimeDimension().getTimeRange().getStartTime());
           date = startTime.atZone(ZoneId.of(timeZone)).toLocalDate();
            if (!date.isBefore(startRange) && !date.isAfter(endRange)) {
              if (map.get(date) != null) {
                metricResponse = map.get(date);
              } else {
                metricResponse = new MetricsResponse();
                metricResponse.setDate(date.toString());
              }
              if (metricDimensionalValue.getValue() != null)
                value = Integer.valueOf(metricDimensionalValue.getValue());
            }
          }
          if (metricResponse != null) {
          	switch (metric) {
				case ACTIONS_PHONE: metricResponse.setPhone(value); break;
				case VIEWS_MAPS: metricResponse.setMaps(value); break;
				case VIEWS_SEARCH: metricResponse.setSearch(value); break;
				case ACTIONS_WEBSITE: metricResponse.setWebsite(value); break;
				case ACTIONS_DRIVING_DIRECTIONS: metricResponse.setDirection(value); break;
				case QUERIES_DIRECT: metricResponse.setQueriesDirect(value); break;
				case QUERIES_INDIRECT: metricResponse.setQueriesIndirect(value); break;
				case QUERIES_CHAIN: metricResponse.setQueriesChain(value); break;
				case LOCAL_POST_VIEWS_SEARCH: metricResponse.setLocalPostViews(value); break;
				case LOCAL_POST_ACTIONS_CALL_TO_ACTION: metricResponse.setLocalPostCta(value); break;
			}
            map.put(date, metricResponse);
          }
       }
      } 
     }
     List<MetricsResponse> metricResponses = new ArrayList<MetricsResponse>(map.values());
     ListIterator<MetricsResponse> li = metricResponses.listIterator(metricResponses.size());
     while(li.hasPrevious()) {
       MetricsResponse metricResponse = li.previous();
       int flag = 0;
       if(metricResponse.getSearch() == null || metricResponse.getDirection() == null || metricResponse.getMaps() == null
            || metricResponse.getPhone() == null || metricResponse.getWebsite() == null) flag = 1;
          if (flag == 0) {
         allMetricsDate = metricResponse.getDate();
         break;
       }
     }
     locationMetricsResponse.setMetrices(metricResponses);
     locationMetricsResponse.setAllMetricsDate(allMetricsDate);
     locationMetricsResponses.add(locationMetricsResponse);
   }
  }
    return new GMBReportInsightsResponse(locationMetricsResponses); 
}

  @Override
  public List<Integer> getGmbActiveLocations() {
    return socialGmbRepo.findBusinessIdsByIsValid(1);
  }
  
  @Override
	public void fillPriorityQueue(List<Integer> snapshot) {
		
	  logger.info("Received call to fill  priority queue with refresh token Ids");
	  	
		redisExternalService.populatePriorityQueue(
				Constants.PRIORITY_QUEUE_FOR_REFRESH_TOKENS,
				createTypedTupleSet(snapshot)
				);
			
		Long queueSize = redisExternalService
					.getPriorityQueueSize(Constants.PRIORITY_QUEUE_FOR_REFRESH_TOKENS);					
		
		logger.info("Completed recording daily refresh token Id list snapshot. "
				+ "Current priority queue size is :{}",queueSize);
	}

	@Override
	public void fillPriorityQueueEnterpriseId(List<Long> snapshot) {

		logger.info("Received call to fill  priority queue with enterprise Ids");

		redisExternalService.populatePriorityQueue(
				Constants.PRIORITY_QUEUE_FOR_GMB_ENTERPRISE_IDS,
				createTypedTupleLongSet(snapshot)
		);

		Long queueSize = redisExternalService
				.getPriorityQueueSize(Constants.PRIORITY_QUEUE_FOR_GMB_ENTERPRISE_IDS);

		logger.info("Completed recording daily enterprise Id list snapshot. "
				+ "Current priority queue size is :{}",queueSize);
	}

	/**
	 * fetch refresh token Ids and process each
	 */
	@Override
	public void submitLocationSync(int queueLength) {
		long inProgressAccountCount = socialGmbRepo.countDistinctAccountIdByLocationSyncStatus(GMBLocationJobStatus.IN_PROGRESS);

		
		int inProgressAccountLimit = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("gmb.account.sync.limit");
		if(inProgressAccountCount > inProgressAccountLimit) {
			logger.info(Constants.GMB_LOCATION_CHECK_JOB_PREFIX, "In progress request count is more then threshold. exiting ..");
			return;
		}
		
		try {
			Long queueSize = redisExternalService.getPriorityQueueSize(Constants.PRIORITY_QUEUE_FOR_REFRESH_TOKENS);
			if (queueSize == 0) {
				logger.warn("Priority queue for location check refreshToken snapshotting is empty filling queue first");				
				List<Integer> refreshTokenIdList = gmbPageService.fetchRefreshTokens(queueLength);
				fillPriorityQueue(refreshTokenIdList);
			}
			
			logger.info(Constants.GMB_LOCATION_CHECK_JOB_PREFIX, "Fetching refresh token Ids from queue");
			List<Integer> refreshTokenIdList = redisExternalService.popFromRedis(Constants.PRIORITY_QUEUE_FOR_REFRESH_TOKENS, 100);
			
			if(CollectionUtils.isEmpty(refreshTokenIdList)) {
				logger.warn(Constants.GMB_LOCATION_CHECK_JOB_PREFIX, "No gmb refresh token received to sync");
			}
			
			logger.info("GMB Location Check Job  : Starting sync for {} refresh ids", refreshTokenIdList.size() );

			for (Integer refreshTokenId : refreshTokenIdList) {
				
				logger.info("GMB Location Update JOB for refresh_token {}", refreshTokenId);
				List<BusinessGoogleMyBusinessLocation> locationListForRefreshToken = socialGmbRepo.findLocationsByRefreshTokenId(refreshTokenId);
				
				if(CollectionUtils.isEmpty(locationListForRefreshToken)) {
					logger.info("No location found to sync for refresh token {}" , refreshTokenId);
					continue;
				}
				logger.info(String.format(Constants.GMB_LOCATION_SYNC_JOB_REFRESH_TOKEN_PREFIX_VALUES, "to process for all accounts : {} "), refreshTokenId,
						"Locations Count Found",locationListForRefreshToken.size());
				
				try {
					fetchAccessTokenAndSubmitSyncJob(refreshTokenId, locationListForRefreshToken);
				}catch(BirdeyeSocialException be) {
					if (be.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value() ) {
						auditGmbDisconEvent(locationListForRefreshToken, be);
						logger.error(Constants.GMB_LOCATION_SYNC_JOB_REFRESH_TOKEN_PREFIX, refreshTokenId, be.getMessage());
						gmbPageService.updateGMBLocationBatches(refreshTokenId, getLocationIdList(locationListForRefreshToken), GMBLocationJobStatus.COMPLETE);
						continue;
					}
				}catch (Exception e) {
					gmbPageService.updateGMBLocationBatches(refreshTokenId, getLocationIdList(locationListForRefreshToken), GMBLocationJobStatus.FAILED);
					logger.error(Constants.GMB_LOCATION_SYNC_JOB_REFRESH_TOKEN_PREFIX, refreshTokenId, e);
				}
			}
		} catch (Exception e) {
			
			logger.error(Constants.GMB_LOCATION_CHECK_JOB_PREFIX, e);
		}
	}

	@Override
	public void submitLocationStateSync(int queueLength) {
		try {
			Long queueSize = redisExternalService.getPriorityQueueSize(Constants.PRIORITY_QUEUE_FOR_GMB_ENTERPRISE_IDS);
			if (queueSize == 0) {
				logger.warn("Priority queue for location status of enterprise id snapshotting is empty, filling queue first");
				// add date to pick only nextSyncDate less than date when it ran
				List<Long> enterpriseIds = googleMsgAgentService.findAllByIds(queueLength,
						new DateTime(new Date()).withTimeAtStartOfDay().plusDays(1).withTimeAtStartOfDay().toDate());
				fillPriorityQueueEnterpriseId(enterpriseIds);
			}

			logger.info(Constants.GMB_LOCATION_STATUS_JOB_ENTERPRISE_ID_PREFIX_VALUES, "Fetching enterprise ids from queue");
			// set the count 50% of total e_ids ran in real time
			List<Long> enterpriseIdList = redisExternalService.popLongFromRedis(Constants.PRIORITY_QUEUE_FOR_GMB_ENTERPRISE_IDS,
					CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("redis.pop.gmb.enterprise.id.count"));

			if(CollectionUtils.isEmpty(enterpriseIdList)) {
				logger.warn(Constants.GMB_LOCATION_STATUS_JOB_ENTERPRISE_ID_PREFIX_VALUES, "No enterprise id received to sync");
			}

			logger.info("GMB Location Status Job  : Starting sync for {} enterprise ids", enterpriseIdList.size());
			for (Long id : enterpriseIdList) {
				try {
					logger.info("GMB Location Status JOB for enterprise id {}", id);
					List<String> placeIds = googleSocialAccountService.getPlaceIds(id);
					logger.info(String.format(Constants.GMB_LOCATION_STATUS_JOB_ENTERPRISE_ID_PREFIX_VALUES, "to process for enterpriseId : {} "), id,
						"Locations Count Found",placeIds.size());
					if (CollectionUtils.isNotEmpty(placeIds)) {
						submitSyncJob(id, placeIds);
					}else{
						agentRepo.updateGMBSyncStatus(id, GMBLocationJobStatus.FAILED, Timestamp.valueOf(LocalDateTime.now().plusHours(24)));
					}
				} catch (Exception e) {
					agentRepo.updateGMBSyncStatus(id, GMBLocationJobStatus.FAILED, Timestamp.valueOf(LocalDateTime.now().plusHours(24)));
					logger.error("error while performing job for enterpriseId {} and message {}", id, e.getMessage());
				}
			}
		} catch (Exception e) {
			logger.info("Error while submitting job for location sync {}", e.getMessage());
		}
	}

	public List<String> getLocationIdList(List<BusinessGoogleMyBusinessLocation> gmblocationList){
		return gmblocationList == null ? null :gmblocationList.parallelStream().map(BusinessGoogleMyBusinessLocation::getLocationId).collect(Collectors.toList());
	}

	
	private void auditGmbDisconEvent(List<BusinessGoogleMyBusinessLocation> locationListForRefreshToken, BirdeyeSocialException be) {
		locationListForRefreshToken.forEach(rawLocation -> {
			logger.error("Marking gmb disconnected event to mark Location {} as invalid in audit table : {}", rawLocation.getLocationId(),be.getMessage());
			//gmbLocationAuditService.create(rawLocation.getId(), null, rawLocation.getLocationId(), Constants.GMB_LOCATION_DISCONNECTED);

		});
	}

	private void fetchAccessTokenAndSubmitSyncJob(Integer refreshTokenId,
			List<BusinessGoogleMyBusinessLocation> locationListForRefreshToken) {
		// fetch access token
		GoogleAuthToken token = new GoogleAuthToken(googleAuthenticationService.getGoogleAccessToken(refreshTokenId), refreshTokenId);
		
		 // update status to in progress for location ids
		gmbPageService.updateGMBLocationBatches(refreshTokenId, getLocationIdList(locationListForRefreshToken),GMBLocationJobStatus.IN_PROGRESS);
		
		submitLocationsCheckForAccount(token, locationListForRefreshToken);
	}

	private void submitSyncJob(Long enterpriseId, List<String> placeIds) {
		UpdateLocationStateRequest request = new UpdateLocationStateRequest(enterpriseId, placeIds,false);
		agentRepo.updateGMBSyncStatus(enterpriseId, GMBLocationJobStatus.IN_PROGRESS, Timestamp.valueOf(LocalDateTime.now().plusHours(24)));
		submitLocationStatusJobForEnterprise(request);
	}


	/**
	 * Submitting GMB account and access token to kafka for parallel processing for
	 * accounts
	 *
	 */
	private void submitLocationsCheckForAccount(GoogleAuthToken gmbAuth, List<BusinessGoogleMyBusinessLocation> locationListForRefreshToken) {
		int refreshTokenId = gmbAuth.getRefreshTokenId();
		Set<GMBAccountDTO> gmbAccounts = new HashSet<>();
		 locationListForRefreshToken.forEach(rawGmbLocation -> {
			 // add to set of account ids
			 gmbAccounts.add(new GMBAccountDTO(rawGmbLocation));

		 });
			logger.info(String.format(Constants.GMB_LOCATION_SYNC_JOB_REFRESH_TOKEN_PREFIX_VALUES, "submitting GMB account List  : {} and access token"), refreshTokenId, "", gmbAccounts.size());

			if (CollectionUtils.isNotEmpty((Collection<?>) gmbAccounts)) {
				for (GMBAccountDTO gmbAccount : gmbAccounts) {
					try {
						GMBAccountSyncRequest gmbAccountCheckRequest = new GMBAccountSyncRequest();
						gmbAccountCheckRequest.setGmbAccount(gmbAccount);
						gmbAccountCheckRequest.setGoogleAuthToken(gmbAuth);
						logger.info(Constants.GMB_LOCATION_SYNC_JOB_REFRESH_TOKEN_PREFIX, refreshTokenId,
								"Submitting account to kafka");
						producer.sendWithKey(Constants.GMB_ACCOUNT_SYNC_TOPIC,
								gmbAccount.getAccountId(), gmbAccountCheckRequest);
					} catch (Exception e) {
						List<String> locationIdssForAccount = locationListForRefreshToken.parallelStream().filter(locationForRefreshToken -> locationForRefreshToken.getAccountId().equals(gmbAccount.getAccountId())).map(locationData -> locationData.getLocationId()).collect(Collectors.toList());
						gmbPageService.updateGMBLocationBatches(refreshTokenId, locationIdssForAccount, GMBLocationJobStatus.FAILED);
 
						logger.error(
								String.format(Constants.GMB_LOCATION_SYNC_JOB_REFRESH_TOKEN_PREFIX_VALUES,
										" Error while Submitting to kafka : {}"),
								gmbAuth.getRefreshTokenId(), gmbAccount.getAccountId(), e);
					}
				}
			}
	}

	private void submitLocationStatusJobForEnterprise(UpdateLocationStateRequest request) {
		logger.info("Submitting to kafka for enterpriseId {}", request.getEnterpriseId());

		if (!ObjectUtils.isEmpty(request)) {
			try {
				producer.sendObjectV1(Constants.GMB_LOCATION_STATE_SYNC, request);
			} catch(Exception e) {
				agentRepo.updateGMBSyncStatus(request.getEnterpriseId(), GMBLocationJobStatus.FAILED, Timestamp.valueOf(LocalDateTime.now().plusHours(24)));
				logger.error("Error while submitting UpdateLocationStateRequest {} to kafka", request);
			}
		}
	}

	/**
	 * Comparator for comparing field values of latest GMB Location and saved GMB
	 * Location and submitting for post action
	 * 
	 * @param gmbPageFromGoogle
	 * @param gmbPageFromSocial
	 */
	private String compareAndTriggerAction(GoogleMyBusinessPagesDTO gmbPageFromGoogle,
			BusinessGoogleMyBusinessLocation gmbPageFromSocial) {
		StringBuilder eventTriggered = new StringBuilder();
		String locationId = gmbPageFromGoogle.getLocationId();
		
			if (isServiceAreaUpdated(gmbPageFromGoogle, gmbPageFromSocial)) {
		     // ACTION SUPRESSED
			/*
			 * logger.info(
			 * "GMB Location Update JOB: Submitting post action for service area update to kafka topic {} with key {}"
			 * , Constants.GMB_LOCATION_SERVICE_AREA_UPDATE, locationId);
			 * producer.sendWithKey(Constants.GMB_LOCATION_SERVICE_AREA_UPDATE,
			 * gmbPageFromGoogle.getLocationId(), gmbPageFromSocial);
			 */
				eventTriggered.append(Constants.GMB_LOCATION_SERVICE_AREA_UPDATE).append(" | ");
			} else {
				logger.info(Constants.GMB_LOCATION_CHECK_JOB_LOCATION_PREFIX, locationId,
						"Service Area not changed for location since last check");
			}
			if (isLocationSuspended(gmbPageFromGoogle, gmbPageFromSocial)) {
			/*
			 * GMBLocationRequestDTO gmbLocationRequestDTO = new GMBLocationRequestDTO();
			 * gmbLocationRequestDTO.setLocationId(locationId);
			 * gmbLocationRequestDTO.setLocationState(gmbPageFromSocial.getLocationState());
			 * 
			 * logger.info(
			 * "GMB Location Update JOB: Submitting post action for location update to kafka topic {} with key {}"
			 * , Constants.GMB_LOCATION_SUSPENDED, locationId);
			 * producer.sendWithKey(Constants.GMB_LOCATION_SUSPENDED, locationId,
			 * gmbLocationRequestDTO);
			 */
				eventTriggered.append(Constants.GMB_LOCATION_SUSPENDED).append(" | ");
			} else {
				logger.info(Constants.GMB_LOCATION_CHECK_JOB_LOCATION_PREFIX, locationId,
						"Location is not suspended after last check");

			}
		
		return eventTriggered.toString(); /// TODO return no event in case no event triggered
	}

	/**
	 * Service Area comparator Utility
	 * 
	 * @param gmbPageFromGoogle
	 * @param gmbPageFromSocial
	 * @return
	 */
	boolean isServiceAreaUpdated(GoogleMyBusinessPagesDTO gmbPageFromGoogle,
			BusinessGoogleMyBusinessLocation gmbPageFromSocial) {
		GMBServiceArea gmbServiceAreaFromGoogle, gmbServiceAreaFromSocial;
		gmbServiceAreaFromGoogle = JSONUtils.fromJSON(gmbPageFromGoogle.getServiceArea(), GMBServiceArea.class);
		gmbServiceAreaFromSocial = JSONUtils.fromJSON(gmbPageFromSocial.getServiceArea(), GMBServiceArea.class);
		return !Objects.equals(gmbServiceAreaFromGoogle, gmbServiceAreaFromSocial);

	}
 
	/**
	 * Location suspended comparator Utility
	 * 
	 * @param gmbPageFromGoogle
	 * @param gmbPageFromSocial
	 * @return
	 */
	boolean isLocationSuspended(GoogleMyBusinessPagesDTO gmbPageFromGoogle,
			BusinessGoogleMyBusinessLocation gmbPageFromSocial) {
		GMBLocationState gmblocationStateFromGoogle, gmblocationStateFromSocial;

		gmblocationStateFromGoogle = JSONUtils.fromJSON(gmbPageFromGoogle.getLocationState(), GMBLocationState.class);
		gmblocationStateFromSocial = JSONUtils.fromJSON(gmbPageFromSocial.getLocationState(), GMBLocationState.class);

		if (gmblocationStateFromGoogle.getIsSuspended() != null
				&& gmblocationStateFromGoogle.getIsSuspended().equals(true)
				&& (gmblocationStateFromSocial.getIsSuspended() == null
						|| gmblocationStateFromSocial.getIsSuspended().equals(false))) {
			return true;
		}
		 return false;
	}

	/**
	 *Fetch GMB location data for GMB account from GMB and DB Table(raw) and submit to comparator
	 */
	@Override
	public void processAllGMBLocationsForAccount(GMBAccountSyncRequest gmbAccountSyncRequest) {

		Integer refreshTokenId = gmbAccountSyncRequest.getGoogleAuthToken().getRefreshTokenId();
		List<BusinessGoogleMyBusinessLocation> gMBPageListFromSocial =null;
	    String accountId = gmbAccountSyncRequest.getGmbAccount().getAccountId();
		try {
			logger.info(Constants.GMB_LOCATION_SYNC_JOB_REFRESH_TOKEN_PREFIX, refreshTokenId,
					"Location details fetch from GMB : start");
			List<BusinessGoogleMyBusinessLocation> gmbLocationListFromSocial = socialGmbRepo.findByRefreshTokenIdAndAccountId(refreshTokenId,accountId);
			Map<String,BusinessGoogleMyBusinessLocation> gmbLocationMapFromSocial = gmbLocationListFromSocial.stream().collect(Collectors.toMap(gmbLocationFromSocial-> gmbLocationFromSocial.getLocationId(), gmbLocationFromSocial -> gmbLocationFromSocial, (loc1,loc2)-> loc1));
	
			Map<String, GoogleMyBusinessPagesDTO> locationIDToGMBPageFromGoogleMap = gmbPageService.processAllGMBLocationsForAccount(gmbAccountSyncRequest.getGmbAccount(), gmbAccountSyncRequest.getGoogleAuthToken());

			if(locationIDToGMBPageFromGoogleMap == null || locationIDToGMBPageFromGoogleMap.isEmpty()) {
				logger.warn(Constants.GMB_LOCATION_SYNC_JOB_REFRESH_TOKEN_PREFIX, refreshTokenId,
						" No Location from GMB hence no further action");
				gmbLocationListFromSocial.forEach(gmbLocationFromSocial -> {
					gmbLocationAuditService.create(gmbLocationFromSocial.getId(), null, gmbLocationFromSocial.getLocationId(), Constants.GMB_GOOGLE_LOCATION_NOT_PRESENT);

				});
				gmbPageService.updateGMBAccountLocationBatches(refreshTokenId, accountId, GMBLocationJobStatus.FAILED);
				return;
			}
			logger.info(Constants.GMB_LOCATION_SYNC_JOB_REFRESH_TOKEN_PREFIX, refreshTokenId,
					"Location details fetch from GMB : End");

			logger.info(Constants.GMB_LOCATION_SYNC_JOB_REFRESH_TOKEN_PREFIX, refreshTokenId,
					"Location details fetch from DB (raw) : start");
			 
			 if(CollectionUtils.isEmpty(gmbLocationListFromSocial)) {
				 logger.info(Constants.GMB_LOCATION_SYNC_JOB_REFRESH_TOKEN_PREFIX, refreshTokenId,
							" No Locations present in  DB (raw)");
			 }
			 
			logger.info(Constants.GMB_LOCATION_SYNC_JOB_REFRESH_TOKEN_PREFIX, refreshTokenId,
					"Location details fetch from DB (raw) : end");
			
			locationIDToGMBPageFromGoogleMap.values().parallelStream().filter(gmbLocationId -> {
				return gmbLocationMapFromSocial.containsKey(gmbLocationId.getLocationId());
			}).forEach(gmbPageFromGoogle -> {
				String locationId = gmbPageFromGoogle.getLocationId();

				try {
					
					BusinessGoogleMyBusinessLocation gmbPageFromSocial = gmbLocationMapFromSocial.get(locationId);
					logger.info(Constants.GMB_LOCATION_CHECK_JOB_LOCATION_PREFIX, locationId, "Initiating comparison");

					String eventTriggered = compareAndTriggerAction(gmbPageFromGoogle, gmbPageFromSocial);
					logger.info(Constants.GMB_LOCATION_CHECK_JOB_LOCATION_PREFIX, locationId, "Post action Taken");

					gmbLocationAuditService.create(gmbPageFromSocial.getId(), gmbPageFromGoogle.toString(), gmbPageFromSocial.getLocationId(), eventTriggered);

					gmbPageService.updateSyncStatusForLocation(refreshTokenId, locationId, GMBLocationJobStatus.COMPLETE);
					logger.info(Constants.GMB_LOCATION_CHECK_JOB_LOCATION_PREFIX, locationId, "Status Updated in DB");

				} catch (Exception e) {
					gmbPageService.updateSyncStatusForLocation(refreshTokenId, locationId, GMBLocationJobStatus.FAILED);
					logger.error(Constants.GMB_LOCATION_CHECK_JOB_LOCATION_PREFIX, locationId, "Failure Updated in DB");
				}finally {
					gmbLocationMapFromSocial.remove(locationId);
				}
			});
			
			logger.info(Constants.GMB_LOCATION_SYNC_JOB_REFRESH_TOKEN_PREFIX, refreshTokenId,"Location Count deleted from Google " + gmbLocationMapFromSocial.size());

			gmbLocationMapFromSocial.values().forEach(gmbLocationFromSocial -> {
				gmbLocationAuditService.create(gmbLocationFromSocial.getId(), null, gmbLocationFromSocial.getLocationId(), Constants.GMB_GOOGLE_LOCATION_NOT_PRESENT);

			});
			gmbPageService.updateGMBLocationBatches(refreshTokenId,  Lists.newArrayList(gmbLocationMapFromSocial.keySet()), GMBLocationJobStatus.FAILED);
            	
		} catch (Exception e) {
			gmbPageService.updateGMBAccountLocationBatches(refreshTokenId, accountId, GMBLocationJobStatus.FAILED);
			logger.info(Constants.GMB_LOCATION_SYNC_JOB_REFRESH_TOKEN_PREFIX, refreshTokenId, e);
		}
	}

	@Override
	public void fetchGMBLocationsForAccount(GMBAccountSyncRequest gmbAccountSyncRequest) {
		try{
			logger.info("GMB Setup received account information to fetch pages for gmb account for businessGetPageRequestId : {} , {}",gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId());
			Map<String, GoogleMyBusinessPagesDTO> data = gmbPageService.processAllGMBLocationsForAccount(gmbAccountSyncRequest.getGmbAccount(),gmbAccountSyncRequest.getGoogleAuthToken());
			logger.info("GMB Setup pages fetch for account for gmb account for businessGetPageRequestId : {} , {}",gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId());
			gmbPageService.postFetchPageProcess(data,gmbAccountSyncRequest.getBusinessGetPageRequestId(),gmbAccountSyncRequest.getGmbAccount().getAccountId());
			logger.info("Pages fetched for gmb account {}  get pages id: {} ",gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId());
		}catch (Exception ex){
			logger.error("GMB Setup Error occurred while fetching gmb account pages for accountID and businessGetpageRequestId : {} , {}" ,gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId(),ex);
			BusinessGetPageRequest request = businessGetPageService.findById(gmbAccountSyncRequest.getBusinessGetPageRequestId());
				String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(request.getEnterpriseId()));
				logger.info("GMB Setup All account data has been processed , inside error block for businessGetPageReq and account id :  {} {}",gmbAccountSyncRequest.getBusinessGetPageRequestId(),gmbAccountSyncRequest.getGmbAccount().getAccountId());
				if(Objects.isNull(request.getTotalPages()) || request.getTotalPages() == 0){
					request.setStatus(Status.NO_PAGES_FOUND.getName());
					redisService.release(key);
				}else{
					request.setStatus(Status.FETCHED.getName());
				}
				pushCheckStatusInFirebase(SocialChannel.GMB.getName(),request.getRequestType(),request.getStatus(), request.getEnterpriseId());

			setErrorLog(gmbAccountSyncRequest, request, "Fetch gmb account failed for :");
			businessGetPageService.saveAndFlush(request);
		}
	}

	public void fetchGMBLocationsForResellerAccount(GMBAccountSyncRequest gmbAccountSyncRequest) {
		try{
			logger.info("GMB Setup received account information to fetch pages for gmb account for businessGetPageRequestId : {} , {}",gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId());
			Map<String, GoogleMyBusinessPagesDTO> data = gmbPageService.processAllGMBLocationsForAccount(gmbAccountSyncRequest.getGmbAccount(),gmbAccountSyncRequest.getGoogleAuthToken());
			logger.info("GMB Setup pages fetch for account for gmb account for businessGetPageRequestId : {} , {}",gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId());
			gmbPageService.postFetchPageProcessForReseller(data,gmbAccountSyncRequest.getBusinessGetPageRequestId(),gmbAccountSyncRequest.getGmbAccount().getAccountId());
			logger.info("Pages fetched for gmb account {}  get pages id: {} ",gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId());
		}catch (Exception ex){
			logger.error("GMB Setup Error occurred while fetching gmb account pages for accountID and businessGetpageRequestId : {} , {}" ,gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId(),ex);
			BusinessGetPageRequest request = businessGetPageService.findById(gmbAccountSyncRequest.getBusinessGetPageRequestId());

			String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(request.getResellerId()));
			logger.info("GMB Setup All account data has been processed , inside error block for businessGetPageReq and account id :  {} {}",gmbAccountSyncRequest.getBusinessGetPageRequestId(),gmbAccountSyncRequest.getGmbAccount().getAccountId());
			if(Objects.isNull(request.getTotalPages()) || request.getTotalPages() == 0){
				request.setStatus(Status.NO_PAGES_FOUND.getName());
				redisService.release(key);
			}else{
				request.setStatus(Status.FETCHED.getName());
			}
			pushCheckStatusInFirebase(SocialChannel.GMB.getName(),request.getRequestType(),request.getStatus(), request.getResellerId());

			setErrorLog(gmbAccountSyncRequest, request, "Fetch gmb account failed for :");
			businessGetPageService.saveAndFlush(request);
		}
	}

	@Override
	public void fetchGMBLocationsForReconnect(GMBAccountSyncRequest gmbAccountSyncRequest) {
		try{
			logger.info("GMB Reconnect received account information to fetch pages for gmb account for businessGetPageRequestId : {} , {}",gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId());
			Map<String, GoogleMyBusinessPagesDTO> data = googleSocialAccountService.processAllGMBLocationsForAccount(gmbAccountSyncRequest.getGmbAccount(),gmbAccountSyncRequest.getGoogleAuthToken(),CacheManager.getInstance().getCache(SystemPropertiesCache.class).getGMBAPILocatonLimit());
			logger.info("GMB Reconnect pages fetch for account for gmb account for businessGetPageRequestId : {} , {}",gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId());
			if(gmbAccountSyncRequest.getType().equalsIgnoreCase(Constants.RESELLER)){
				googleSocialAccountService.postFetchPageReconnectProcessForReseller(data,gmbAccountSyncRequest,gmbAccountSyncRequest.getType());
			}else{
				googleSocialAccountService.postFetchPageReconnectProcess(data,gmbAccountSyncRequest,gmbAccountSyncRequest.getType());
			}
			logger.info("GMB Reconnect event processed for account for gmb account for businessGetPageRequestId : {} , {}",gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId());
		}catch (Exception ex){
			logger.error("GMB Reconnect Error occurred while fetching gmb account pages for accountID and businessGetpageRequestId : {} , {}" ,gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId(),ex);
			Boolean isEligible = redisExternalService.checkIfAllProcessedAfterCurrentForGMB(gmbAccountSyncRequest.getBusinessGetPageRequestId(), gmbAccountSyncRequest.getGmbAccount().getAccountId());
			BusinessGetPageRequest request = businessGetPageService.findById(gmbAccountSyncRequest.getBusinessGetPageRequestId());
			Long parentId  = gmbAccountSyncRequest.getType().equalsIgnoreCase(Constants.RESELLER) ? request.getResellerId(): request.getEnterpriseId();
			if(isEligible){
				String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(parentId));
				logger.info("GMB Reconnect All account data has been processed , inside error block for businessGetPageReq and account id :  {} {}",gmbAccountSyncRequest.getBusinessGetPageRequestId(),gmbAccountSyncRequest.getGmbAccount().getAccountId());
				request.setStatus(Status.FETCHED.getName());
				pushCheckStatusInFirebase(SocialChannel.GMB.getName(),request.getRequestType(),request.getStatus(), parentId);
				redisService.release(key);
			}
			setErrorLog(gmbAccountSyncRequest, request, "gmb reconnect account fetch failed for :");
			businessGetPageService.saveAndFlush(request);
		}
	}

	private void setErrorLog(GMBAccountSyncRequest gmbAccountSyncRequest, BusinessGetPageRequest request, String s) {
		StringBuilder builder;
		if (request.getErrorLog() == null) {
			builder = new StringBuilder();
			builder.append(s);
		} else {
			builder = new StringBuilder(request.getErrorLog());
			builder.append(",");
		}
		builder.append(gmbAccountSyncRequest.getGmbAccount().getAccountId());
		request.setErrorLog(builder.toString());
	}

	@Async
	@Override
	public void processGMBLocationsForAccountForOpenUrl(GMBAccountSyncRequest gmbAccountSyncRequest) {
		try{
			logger.info("received account information to fetch pages for gmb account for businessGetPageRequestId : {} , {}",gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId());
			Map<String, GoogleMyBusinessPagesDTO> data = gmbPageService.processAllGMBLocationsForAccount(gmbAccountSyncRequest.getGmbAccount(),gmbAccountSyncRequest.getGoogleAuthToken());
			logger.info("pages fetch for account for gmb account for businessGetPageRequestId : {} , {}",gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId());
			gmbPageService.postFetchPageProcessForOpenUrl(data,gmbAccountSyncRequest.getBusinessGetPageRequestId(),gmbAccountSyncRequest.getGmbAccount().getAccountId());
			logger.info("event processed for account for gmb account for businessGetPageRequestId : {} , {}",gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId());
		}catch (Exception ex) {
			logger.error("Error occurred while fetching gmb account pages for accountID and businessGetpageRequestId : {} , {}", gmbAccountSyncRequest.getGmbAccount().getAccountId(), gmbAccountSyncRequest.getBusinessGetPageRequestId(), ex);
			Boolean isEligible = redisExternalService.checkIfAllProcessedAfterCurrentForGMB(gmbAccountSyncRequest.getBusinessGetPageRequestId(), gmbAccountSyncRequest.getGmbAccount().getAccountId());
			if (isEligible) {
				BusinessGetPageOpenUrlRequest request = businessGetPageOpenUrlReqRepo.findOne(gmbAccountSyncRequest.getBusinessGetPageRequestId());
				String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(request.getFirebaseKey()));
				logger.info("GMB Setup All account data has been processed , inside error block for businessGetPageReq and account id :  {} {}", gmbAccountSyncRequest.getBusinessGetPageRequestId(), gmbAccountSyncRequest.getGmbAccount().getAccountId());
				if (Objects.isNull(request.getTotalPages()) || request.getTotalPages() == 0) {
					request.setStatus(Status.NO_PAGES_FOUND.getName());
					redisService.release(key);
				} else {
					request.setStatus(Status.FETCHED.getName());
				}
				businessGetPageOpenUrlReqRepo.saveAndFlush(request);
			}
		}
	}

	private void processGMBLocationsForAccountForOpenUrl(GMBAccountSyncRequest gmbAccountSyncRequest, String firebaseKey) {
		try{
			logger.info("received account information to fetch pages for gmb account for businessGetPageRequestId : {} , {}",gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId());
			Map<String, GoogleMyBusinessPagesDTO> data = gmbPageService.processAllGMBLocationsForAccount(gmbAccountSyncRequest.getGmbAccount(),gmbAccountSyncRequest.getGoogleAuthToken());
			logger.info("pages fetch for account for gmb account for businessGetPageRequestId : {} , {}",gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId());
			gmbPageService.postFetchPageProcessForOpenUrlV1(data,gmbAccountSyncRequest.getBusinessGetPageRequestId(),gmbAccountSyncRequest.getGmbAccount().getAccountId());
			logger.info("event processed for account for gmb account for businessGetPageRequestId : {} , {}",gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId());
		}catch (Exception ex){
			logger.error("Error occurred while fetching gmb account pages for accountID and businessGetpageRequestId : {} , {}" ,gmbAccountSyncRequest.getGmbAccount().getAccountId(),gmbAccountSyncRequest.getBusinessGetPageRequestId(),ex);
			BusinessGetPageOpenUrlRequest request = businessGetPageOpenUrlReqRepo.findOne(gmbAccountSyncRequest.getBusinessGetPageRequestId());
			String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(request.getFirebaseKey()));
			logger.info("GMB Setup All account data has been processed , inside error block for businessGetPageReq and account id :  {} {}",gmbAccountSyncRequest.getBusinessGetPageRequestId(),gmbAccountSyncRequest.getGmbAccount().getAccountId());
			if(Objects.isNull(request.getTotalPages()) || request.getTotalPages() == 0){
				request.setStatus(Status.NO_PAGES_FOUND.getName());
				redisService.release(key);
			}else{
				request.setStatus(Status.FETCHED.getName());
			}
			nexusService.updateMapInFirebase("socialOpenUrl/"+ firebaseKey,firebaseKey,request.getStatus());
			businessGetPageOpenUrlReqRepo.saveAndFlush(request);
		}
	}

//	private void submitGmbNotificationSubscription(GMBAccountSyncRequest gmbAccountSyncRequest) {
//		GMBNotificationRequest notificationSubmit = new GMBNotificationRequest();
//		notificationSubmit.setAccountId(Arrays.asList(gmbAccountSyncRequest.getGmbAccount().getAccountId()));
//		notificationSubmit.setAccessToken(gmbAccountSyncRequest.getGoogleAuthToken().getAccess_token());
//		notificationSubmit.setRequestId(gmbAccountSyncRequest.getBusinessGetPageRequestId());
//		producer.sendObject(Constants.ENABLE_NOTIFICATION_TOPIC, notificationSubmit);
//	}

	/**
	 * Method to create Set of type Set<TypedTuple<Object>> which is needed by sorted set add operation in Redis
	 * @param snapshot
	 * @return
	 */
	private Set<TypedTuple<Object>> createTypedTupleSet(List<Integer> snapshot){
		Set<TypedTuple<Object>> set = new HashSet<TypedTuple<Object>>();
		
		for(int i = 0 ; i < snapshot.size() ; i ++) {
			set.add(
					new DefaultTypedTuple<Object>(snapshot.get(i),
							snapshot.get(i).doubleValue())
					);
		}
		return set;
	}

	private Set<TypedTuple<Object>> createTypedTupleLongSet(List<Long> snapshot){
		Set<TypedTuple<Object>> set = new HashSet<TypedTuple<Object>>();

		for(int i = 0 ; i < snapshot.size() ; i ++) {
			set.add(
					new DefaultTypedTuple<Object>(snapshot.get(i),
							snapshot.get(i).doubleValue())
			);
		}
		return set;
	}

	@Override
	public Integer getGMBLocationByPlaceId(String placeId) {
		List<BusinessGMBLocationAggregation> businessGMBLocationAggregations = socialGmbRepo.findByPlaceId(placeId);
		if(CollectionUtils.isNotEmpty(businessGMBLocationAggregations)){
			BusinessGMBLocationAggregation businessGMBLocationAggregation = businessGMBLocationAggregations.get(0);
			return businessGMBLocationAggregation.getBusinessId();
		}
		return null;
	}

	@Override
	public String getGMBIntegrationStatus(Integer businessId) {
		String integrationStatus = "";
		BusinessGoogleMyBusinessLocation businessGMBLocations = socialGmbRepo.findByBusinessId(businessId);
		if(Objects.nonNull(businessGMBLocations)) {
			integrationStatus = SocialIntegrationStatus.getIntegrationStatus(businessGMBLocations.getIsValid()).getStatus();
		}else{
			integrationStatus = SocialIntegrationStatus.getIntegrationStatus(null).getStatus();
		}
		return integrationStatus;
	}

	@Override
	public BusinessGMBLocationRawRepository.IntegrationSummary getInValidAndTotalCount(List<Integer> businessIdList) {
		return socialGmbRepo.getInvalidAndTotalCount(businessIdList).get(0);
	}

	@Override
	public void gmbPageFetchByAccount(GMBAccountDTO gmbAccountDTO, Long parentId,String type) {
		BusinessGetPageRequest businessGetPageRequest = type.equalsIgnoreCase(Constants.RESELLER) ?
				businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(parentId,SocialChannel.GMB.getName(),Constants.CONNECT):
				businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(parentId,SocialChannel.GMB.getName(),Constants.CONNECT);
		if(Objects.isNull(businessGetPageRequest)){
			throw new BirdeyeSocialException(ErrorCodes.NO_BUSINESS_GET_PAGE_REQUEST_FOUND);
		}else if(!businessGetPageRequest.getStatus().equalsIgnoreCase(Status.ACCOUNT_FETCHED.getName())){
			throw new BirdeyeSocialException(ErrorCodes.BUSINESS_GET_PAGE_REQUEST_STATUS_MOVED);
		}else{
			redisService.release(SocialChannel.GOOGLE_PLUS_GMB.getName().concat("_group_").concat(String.valueOf(parentId)));
			String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(parentId));
			boolean lock = redisService.tryToAcquireLock(key);
			logger.info("GMB Page fetch by account lock acquired for reseller : {} {}",parentId,lock);
			if(lock){
				try{
					pushCheckStatusInFirebase(SocialChannel.GMB.getName(), businessGetPageRequest.getRequestType(),Status.INITIAL.getName(), parentId);
					businessGetPageRequest.setStatus(Status.INITIAL.getName());
					businessGetPageRequest.setGmbAccount(gmbAccountDTO.getAccountId());
					businessGetPageService.saveAndFlush(businessGetPageRequest);
					GoogleAccount googleAccount = null;
					List<GoogleAccount> googleAccountList = googleAccountService.findByAccountIdAndBusinessGetPageReqIdOrderByIdDesc(businessGetPageRequest.getGmbAccount(), businessGetPageRequest.getId().toString());
					if(CollectionUtils.isNotEmpty(googleAccountList)) {
						googleAccount = googleAccountList.get(0);
					}
					if(Objects.isNull(googleAccount)){
						throw new BirdeyeSocialException(ErrorCodes.GMB_ACCOUNT_NOT_FOUND);
					}
					GMBAccountSyncRequest gmbAccountSyncRequest = new GMBAccountSyncRequest();
					gmbAccountSyncRequest.setGmbAccount(gmbAccountDTO);
					gmbAccountSyncRequest.setBusinessGetPageRequestId(businessGetPageRequest.getId().toString());
					gmbAccountSyncRequest.setEmail(businessGetPageRequest.getEmail());
					GoogleAuthToken googleAuthToken = googleAuthenticationService.getGoogleAuthTokens(googleAccount.getRefreshToken());
					googleAuthToken.setRefreshTokenId(googleAccount.getRefreshToken());
					gmbAccountSyncRequest.setGoogleAuthToken(googleAuthToken);
					socialProxyHandler.runInAsync(()-> {
						if(type.equalsIgnoreCase(Constants.RESELLER)){
							this.fetchGMBLocationsForResellerAccount(gmbAccountSyncRequest);
						}else{
							this.fetchGMBLocationsForAccount(gmbAccountSyncRequest);
						}
						return true;
					});

				}catch (Exception ex){
					logger.error("Exception occurred while trying to fetch gmb pages by accountId, redis key released for business {} and exception",parentId,ex);
					redisService.release(key);
					businessGetPageRequest.setStatus(Status.CANCEL.getName());
					businessGetPageRequest.setErrorLog(ex.getMessage()!=null?ex.getMessage().substring(0,Math.min(ex.getMessage().length(), 4000)):"Failed to fetch page for account :"+gmbAccountDTO.getAccountId());
					businessGetPageService.saveAndFlush(businessGetPageRequest);
					pushCheckStatusInFirebase(SocialChannel.GMB.getName(), businessGetPageRequest.getRequestType(),Status.COMPLETE.getName(), parentId,true);
				}
			}else{
				logger.info("[Redis Lock] (Google account page) Lock is already acquired for business {}", parentId);
				pushCheckStatusInFirebase(SocialChannel.GMB.getName(),businessGetPageRequest.getRequestType(),Status.COMPLETE.getName(),parentId);
			}
		}
	}

	@Override
	public void gmbPageFetchByAccountForOpenUrl(GMBAccountDTO gmbAccountDTO, Long parentId, String firebaseKey) {
		BusinessGetPageOpenUrlRequest businessGetPageOpenUrlRequest = businessGetPageOpenUrlReqRepo.findFirstByFirebaseKeyOrderByCreatedDescIdDesc(firebaseKey);

		if(Objects.isNull(businessGetPageOpenUrlRequest)){
			throw new BirdeyeSocialException(ErrorCodes.NO_BUSINESS_GET_PAGE_REQUEST_FOUND);
		}else if(!businessGetPageOpenUrlRequest.getStatus().equalsIgnoreCase(Status.ACCOUNT_FETCHED.getName())){
			throw new BirdeyeSocialException(ErrorCodes.BUSINESS_GET_PAGE_REQUEST_STATUS_MOVED);
		}else{
			redisService.release(SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(firebaseKey)));
			String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(firebaseKey));
			boolean lock = redisService.tryToAcquireLock(key);
			logger.info("GMB Page fetch by account lock acquired for openurl business : {} {}",parentId,lock);
			if(lock){
				try{
					//pushCheckStatusInFirebase(SocialChannel.GMB.getName(), businessGetPageOpenUrlRequest.getRequestType(),Status.INITIAL.getName(), parentId);
					nexusService.updateMapInFirebase("socialOpenUrl/"+ firebaseKey,firebaseKey,Status.INITIAL.getName());
					businessGetPageOpenUrlRequest.setStatus(Status.INITIAL.getName());
					businessGetPageOpenUrlRequest.setGmbAccount(gmbAccountDTO.getAccountId());
					businessGetPageOpenUrlReqRepo.saveAndFlush(businessGetPageOpenUrlRequest);
					GoogleAccount googleAccount = null;
					List<GoogleAccount> googleAccountList = googleAccountService.findByAccountIdAndBusinessGetPageReqIdOrderByIdDesc(businessGetPageOpenUrlRequest.getGmbAccount(), businessGetPageOpenUrlRequest.getId().toString());
					if(CollectionUtils.isNotEmpty(googleAccountList)) {
						googleAccount = googleAccountList.get(0);
					}
					if(Objects.isNull(googleAccount)){
						throw new BirdeyeSocialException(ErrorCodes.GMB_ACCOUNT_NOT_FOUND);
					}
					GMBAccountSyncRequest gmbAccountSyncRequest = new GMBAccountSyncRequest();
					gmbAccountSyncRequest.setGmbAccount(gmbAccountDTO);
					gmbAccountSyncRequest.setBusinessGetPageRequestId(businessGetPageOpenUrlRequest.getId().toString());
					gmbAccountSyncRequest.setEmail(businessGetPageOpenUrlRequest.getEmail());
					GoogleAuthToken googleAuthToken = googleAuthenticationService.getGoogleAuthTokens(googleAccount.getRefreshToken());
					googleAuthToken.setRefreshTokenId(googleAccount.getRefreshToken());
					gmbAccountSyncRequest.setGoogleAuthToken(googleAuthToken);
					processGMBLocationsForAccountForOpenUrl(gmbAccountSyncRequest, firebaseKey);

				}catch (Exception ex){
					logger.error("Exception occurred while trying to fetch gmb pages by accountId, redis key released for business {} and exception {}",parentId,ex);
					redisService.release(key);
					businessGetPageOpenUrlRequest.setStatus(Status.CANCEL.getName());
					int logLength = Math.min(ex.getMessage().length(), 4000);
					businessGetPageOpenUrlRequest.setErrorLog(ex.getMessage()!=null?ex.getMessage().substring(0,logLength):"Failed to fetch page for account :"+gmbAccountDTO.getAccountId());
					businessGetPageOpenUrlReqRepo.saveAndFlush(businessGetPageOpenUrlRequest);
					//pushCheckStatusInFirebase(SocialChannel.GMB.getName(), businessGetPageOpenUrlRequest.getRequestType(),Status.COMPLETE.getName(), parentId,true);
					nexusService.updateMapInFirebase("socialOpenUrl/"+ firebaseKey,firebaseKey,Status.CANCEL.getName());
				}
			}else{
				logger.info("[Redis Lock] (Google account page) Lock is already acquired for business {}", parentId);
				//pushCheckStatusInFirebase(SocialChannel.GMB.getName(),businessGetPageOpenUrlRequest.getRequestType(),Status.COMPLETE.getName(),parentId);
				nexusService.updateMapInFirebase("socialOpenUrl/"+ firebaseKey,firebaseKey,Status.COMPLETE.getName());
			}
		}
	}

	public void fetchPagesForFreemium(){

	}

	@Override
	public void validityCheckForGMB(String locationId,boolean isViaNotification) {
		if(Objects.isNull(locationId)){
			logger.info("Location id can not be null");
			return;
		}
		logger.info("Request received to update validity column for location ids: {}",locationId);
		List<BusinessGoogleMyBusinessLocation> pages = socialGmbRepo.findByLocationId(locationId);

		if(CollectionUtils.isEmpty(pages)){
			logger.info("Unable to get page for location id:{}",locationId);
			return;
		}
		BusinessGoogleMyBusinessLocation page = pages.get(0);
		Integer isValid = page.getIsValid();
		updateVoiceOdMerchant(page, isViaNotification);
		if(page.getIsValid().equals(0)) {
			logger.info("Page disconnected with VOM update");
			commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.VOICE_OF_MERCHANT_DISCONNECT.name(), Collections.singletonList(page),
					page.getUserId(), page.getBusinessId(), page.getEnterpriseId());
			if(isValid == 1)
				brokenIntegrationService.pushValidIntegrationStatus(page.getEnterpriseId(),SocialChannel.GMB.getName(),page.getId(),0,page.getLocationId());
		} else if(isValid == 0) {
			logger.info("Page reconnected with VOM update");
			commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.VOICE_OF_MERCHANT_RECONNECT.name(), Collections.singletonList(page),
					page.getUserId(), page.getBusinessId(), page.getEnterpriseId());
			BAMUpdateRequest payload =
					new BAMUpdateRequest("gmb", page.getBusinessId(), page.getLocationMapUrl(), page.getLocationId(), page.getPlaceId());

			producer.sendWithKey(Constants.INTEGRATION_TOPIC, String.valueOf(page.getBusinessId()), payload);
		}
	}

	@Override
	public void updateVoiceOdMerchant(BusinessGoogleMyBusinessLocation page,boolean isViaNotification) {
		try {
			String accessToken = gAccessToken.getGoogleAccessTokenByRefreshToken(page);
			gmbPageService.getVoiceOfMerchant(accessToken, page,isViaNotification);
		} catch (Exception e) {
			logger.info("Unable to get access token for account id:{}", page.getLocationId());
		}
		checkValidity(page);
		Integer canPost = googleSocialAccountService.getGMBPostPermission(Collections.singletonList(page),
				Collections.singletonList(SocialMessageModule.PUBLISH.name())) ? 1 : 0;
		socialGmbRepo.updateGMBPage(page.getIsValid(), page.getIsVerified(), page.getLocationState(), page.getValidType(), page.getLocationId(),canPost);
	}


	@Override
	public List<String> getAllLocations(List<Integer> businessIds) {
		if(CollectionUtils.isEmpty(businessIds)) return new ArrayList<>();
		List<String> locationIds = socialGmbRepo.findDistinctGmbLocationIdByBusinessIdInAndIsValid(businessIds);
		return locationIds;
	}

	@Async
	@Override
	public void getLocationCustomerMedia(Integer businessId, Integer requestIdentifier, String requestType) {
		logger.info("get customer media request for businessId: {}", businessId);
		if (Objects.isNull(businessId)) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid args present");
		}

		final BusinessGoogleMyBusinessLocation gmbLocation = socialGmbRepo.findByBusinessId(businessId);
		if (Objects.isNull(gmbLocation))
			throw new BirdeyeSocialException(ErrorCodes.GMB_MAPPING_NOT_FOUND, "Mapping is not present for given businessId");
		if (gmbLocation.getIsValid() != 1)
			throw new BirdeyeSocialException(ErrorCodes.GMB_MAPPING_INVALID, "GMB mapping is invalid");
		logger.info("gmbLocation: gmbLocation {}", gmbLocation.getLocationId());
		final BusinessGoogleMyBusinessLocation gmbRawLoc = findByLocationId(gmbLocation.getLocationId());
		if (gmbRawLoc.getIsValid() != 1)
			throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN, "GMB Raw location is invalid");


		Integer batchSize = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getGmbCustomerMediaBatchSize();
		producer.sendObject(Constants.CUSTOMER_MEDIA_FROM_GMB, new GMBGetMediaRequest(gmbRawLoc.getLocationUrl(),
				batchSize, null, null, gmbRawLoc.getRefreshTokenId(),
				businessId, requestIdentifier, requestType,null,null,0));
	}

	@Override
	public void getCustomerMediaFromGMB(GMBGetMediaRequest getCustomerMediaRequest) throws IOException {
			final String accessToken = socialProxyHandler.runWithRetryableBirdeyeException(
					() ->  googleAuthenticationService.getGoogleAccessToken(getCustomerMediaRequest.getRefreshTokenId())
			);
			googleService.getGoogleCustomerMedia(getCustomerMediaRequest, accessToken);
	}

	@Deprecated
	@Override
	public void syncGMBAgentAndLocation(UpdateLocationStateRequest request) {
		googleSocialAccountService.updateAgentState(request.getEnterpriseId());
		googleSocialAccountService.updateLocationState(request);
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("uuuu/MM/dd HH:mm:ss");
		logger.info("Finished executing request {} in time {}", request, dtf.format(LocalDateTime.now()));
	}

	@Override
	@Deprecated
	public void updateLocationStatus(UpdateLocationStateRequest request) {
		googleSocialAccountService.updateLocationState(request);
	}

	public void checkValidity(BusinessGoogleMyBusinessLocation pages){
		if(Objects.nonNull(pages)){
			logger.info("Page ids to update validity : {}",pages.getLocationId());
				try {
					Long enterpriseId = pages.getEnterpriseId();
					Boolean googleMessageEnabled = false;
					if(Objects.nonNull(enterpriseId))
						googleMessageEnabled = googleSocialAccountService.isGoogleMessageEnabled(null,enterpriseId);
					Boolean finalGoogleMessageEnabled = googleMessageEnabled;
					googleSocialAccountService.getValidity(pages,finalGoogleMessageEnabled);
				}catch (Exception e){
					logger.info("Error from core unable to update validity type {}",e.getMessage());
				}
			}
	}

	@Override
	public GMBReportAnalyticsResponse getGMBPageAnalytics(GMBPageAnalyticsRequestDTO analyticsRequestDTO,
			Integer businessId, String startDate, String endDate,String accessToken) {
		validateGMBPageAnalyticsRequestDTO(analyticsRequestDTO, startDate, endDate);
		GMBReportAnalyticsResponse gmbReportAnalyticsResponse = googleService.getGMBPageAnalytics(accessToken,
				analyticsRequestDTO);
		return gmbReportAnalyticsResponse;
	}

	@Override
	public String generateAccessToken(BusinessGoogleMyBusinessLocation gmbLocation, Integer businessId) {
		// comment
		if (gmbLocation == null) {
			logger.error(
					"Error while fetching location report insights as gmb location not found/invalid for business id {}",
					businessId);
			throw new BirdeyeSocialException(ErrorCodes.GMB_PAGE_NOT_FOUND,
					"GMB location isn't integrated or inactive");
		}
		String locationName = gmbLocation.getLocationUrl();
		if (StringUtils.isEmpty(locationName)) {
			logger.error("getBusinessReportInsights Location name is null or empty for businessId {} ", businessId);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GET_LOCATION_NAME,
					"LocationName is null or empty for this businessId");
		}
		// Cache access token
		String accessToken = gAccessToken.getGoogleAccessToken(gmbLocation);
		/// comment till here
		// uncomment
//				String accessToken="************************************************************************************************************************************************************************************************************************";
		if (accessToken == null) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GENERATE_ACCESS_TOKEN_ON_GMB,
					"Exception occured while generating accessToken on gmb");
		}
		return accessToken;
	}


	@Override
	public LocalDate getGMBKeywordAnalytics(BusinessGoogleMyBusinessLocation gmbLocation, LocalDate date, LocalDate nextSyncDate, String accessToken) throws Exception {
		String pageId = gmbLocation.getLocationId();
		Integer businessId = gmbLocation.getBusinessId();
		LocalDate iDate = nextSyncDate;
		Integer apiLimit = getGmbKeyWordLimit();
		if(apiLimit==-1) apiLimit = Integer.MAX_VALUE;
		while(!iDate.isAfter(date)) {
			LocalDate pDate = iDate.minusMonths(1);
			GMBKeywordAnalyticsRequestDTO keywordAnalyticsRequestDTO = GMBKeywordAnalyticsRequestDTO.builder()
					.pageId(pageId)
					.startMonthYear(pDate.getYear())
					.startMonth(pDate.getMonthValue())
					.endMonthYear(pDate.getYear())
					.endMonth(pDate.getMonthValue())
					.build();

			try {
				List<SearchKeyword> searchKeywords = new ArrayList<>();
				GMBReportKeywordResponse gmbReportKeywordResponse = null;
				try {
					gmbReportKeywordResponse  = googleService.getGMBKeywordAnalytics(accessToken, keywordAnalyticsRequestDTO, null);
				} catch (Exception e) {
					logger.error("failed to fetch keyword response for business_id: {} due to error: {}", businessId, e.getMessage());
				}
				String nextPageToken = null;
				if(Objects.nonNull(gmbReportKeywordResponse) && CollectionUtils.isNotEmpty(gmbReportKeywordResponse.getSearchKeywordsCounts())) {
					searchKeywords.addAll(gmbReportKeywordResponse.getSearchKeywordsCounts());
					nextPageToken = gmbReportKeywordResponse.getNextPageToken();
				}
				Integer count = 0;
				while(nextPageToken!=null && count<apiLimit-1) {
					GMBReportKeywordResponse gmbReportKeywordResponsePaginated = null;
					try {
						gmbReportKeywordResponsePaginated = googleService
								.getGMBKeywordAnalytics(accessToken, keywordAnalyticsRequestDTO, nextPageToken);
					} catch (BirdeyeSocialException e) {
						if(e.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value()) {
							accessToken = gAccessToken.getGoogleAccessToken(gmbLocation);
							try {
								gmbReportKeywordResponsePaginated = googleService
										.getGMBKeywordAnalytics(accessToken, keywordAnalyticsRequestDTO, nextPageToken);
							} catch (Exception ce) {
								logger.error("failed to fetch keyword response for business_id: {} due to error: {} breaking loop", businessId, e.getMessage());
							}
						} else {
							logger.error("failed to fetch keyword response for business_id: {} due to error: {} breaking loop", businessId, e.getMessage());
						}
					} catch(Exception e) {
						logger.error("failed to fetch keyword response for business_id: {} due to error: {} breaking loop", businessId, e.getMessage());
					}
					if(Objects.nonNull(gmbReportKeywordResponsePaginated) && CollectionUtils.isNotEmpty(gmbReportKeywordResponsePaginated.getSearchKeywordsCounts())) {
						searchKeywords.addAll(gmbReportKeywordResponsePaginated.getSearchKeywordsCounts());
						nextPageToken = gmbReportKeywordResponsePaginated.getNextPageToken();
					} else {
						nextPageToken = null;
					}
					count++;
				}
				logger.info("keyword job completed for business_id: {} and for date: {}", businessId, pDate);
				boolean limitReached = count>=apiLimit-1 && nextPageToken!=null;
				sendKeywordsToKafkaAndSaveToDb(pageId, businessId, iDate, pDate, searchKeywords, limitReached);
			} catch (BirdeyeSocialException e) {
				logger.error("not able to fetch keyword for business_id: {} for date: {}", businessId, pDate);
			} catch (Exception e) {
				throw new Exception(e.getMessage());
			}
			iDate = iDate.plusMonths(1);
		}
		return iDate;
	}

	private void sendKeywordsToKafkaAndSaveToDb(String pageId, Integer businessId, LocalDate date, LocalDate pDate, List<SearchKeyword> searchKeywordList, Boolean limitReached) throws JsonProcessingException {
		List<GmbSearchKeywordInsights> gmbSearchKeywordInsightList = new ArrayList<>();
		for(SearchKeyword searchKeyword: searchKeywordList) {
			String insightValue = null;
			if(Objects.nonNull(searchKeyword.getInsightsValue())) {
				if(Objects.nonNull(searchKeyword.getInsightsValue().getValue())) {
					insightValue = searchKeyword.getInsightsValue().getValue();
				} else {
					insightValue = searchKeyword.getInsightsValue().getThreshold();
				}
			}
			GmbSearchKeywordInsights gmbSearchKeywordInsights = new GmbSearchKeywordInsights(searchKeyword.getSearchKeyword(), insightValue);
			gmbSearchKeywordInsightList.add(gmbSearchKeywordInsights);
		}

		GMBKeywordKafkaResponse gmbKeywordKafkaResponse = GMBKeywordKafkaResponse.builder()
				.businessId(businessId)
				.pageId(pageId)
				.keywordInsights(gmbSearchKeywordInsightList)
				.month(pDate.getMonthValue())
				.year(pDate.getYear())
				.limitReached(limitReached)
				.build();

		kafkaProducer.sendObjectV1(GMB_KEYWORD_KAFKA_TOPIC, gmbKeywordKafkaResponse);
		Map<String, Object> metadataMap = new HashMap<>();
		metadataMap.put("limitReached", limitReached);
		String metadata = JSONUtils.toJSON(metadataMap);
		GMBKeywordAnalytics gmbKeywordAnalytics = GMBKeywordAnalytics.builder()
				.businessId(businessId)
				.pageId(pageId)
				.responsData(new ObjectMapper().writeValueAsString(searchKeywordList))
				.date(Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()))
				.metadata(metadata)
				.created(new Date())
				.build();
		gmbKeywordAnalyticsRepository.saveAndFlush(gmbKeywordAnalytics);
	}

	private void validateGMBPageAnalyticsRequestDTO(GMBPageAnalyticsRequestDTO analyticsRequestDTO, String startDate,
			String endDate) {
		logger.info("Page ids to update validity :analyticsRequestDTO  {} startDate {} endDate{}",analyticsRequestDTO,startDate,endDate);
		if (!MetricEnum.contains(analyticsRequestDTO.getDailyMetric())) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Metrics should be of type MetricEnum.");
		}
		if (analyticsRequestDTO.getStartDay() == null || analyticsRequestDTO.getStartMonth() == null
				|| analyticsRequestDTO.getStartYear() == null || analyticsRequestDTO.getEndMonth() == null
				|| analyticsRequestDTO.getEndYear() == null || analyticsRequestDTO.getEndDay() == null)
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST,
					"StartDate or EndDate cannot be null or empty.");
		LocalDate startTime = LocalDate.parse(startDate);
		logger.info("startTime : {} analyticsRequestDTO : {}",startTime,analyticsRequestDTO);
	    LocalDate endTime = LocalDate.parse(endDate);
	    if(endTime.isEqual(startTime))
	      throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT,"EndDate cannot be equal or before startDate");
	    LocalDate currentDate = LocalDate.now(ZoneOffset.UTC);
	    currentDate = currentDate.minusMonths(15L);
	    currentDate = currentDate.plusDays(1L);
	    logger.info("currentDate : {} analyticsRequestDTO {}",startTime,analyticsRequestDTO);
	    if (startTime.isBefore(currentDate) || startTime.isEqual(currentDate))
	      throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Maximum startDate can be 15 months ago.");
	}

	private Integer getGmbKeyWordLimit() {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("listing.keyword.gmb.api.limit", 10);
	}

	@Override
	public GMBLocationUpdateResponse getGMBPageUpdateInfoForBusiness(Integer businessId, String readMask) throws Exception {
		logger.info("getGMBPageUpdateInfoForBusiness: businessId {}", businessId);
		if ( businessId == null )
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid args present");
		final BusinessGoogleMyBusinessLocation gmbLocation = socialGmbRepo.findByBusinessId(businessId);
		
		if (Objects.isNull(gmbLocation))
			throw new BirdeyeSocialException(ErrorCodes.GMB_MAPPING_NOT_FOUND,
					"Mapping is not present for given businessId");
		if (gmbLocation.getIsValid() != 1)
			throw new BirdeyeSocialException(ErrorCodes.GMB_MAPPING_INVALID, "GMB mapping is invalid");
		logger.info("gmbLocation: gmbLocation {}", gmbLocation.getLocationId());
		 
		final String accessToken = gAccessToken.getGoogleAccessToken(gmbLocation);
		if(StringUtils.isEmpty(accessToken)) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GENERATE_ACCESS_TOKEN_ON_GMB, "Failed to generate access token");
		}

		GMBLocationUpdateResponse gmbPageUpdateLocationMask;
	
			gmbPageUpdateLocationMask = googleService.getGMBLocationUpdateDetails(accessToken,gmbLocation.getLocationId(), readMask);
			gmbPageUpdateLocationMask.setBusinessId(businessId.toString());
		
		return gmbPageUpdateLocationMask;
	}

	/**
	 * @param businessId
	 * @return
	 */
	@Override
	public FoodMenusDTO getGmbFoodMenus(Integer businessId) throws Exception {
		logger.info("Received request to get GMB Food Menus: {}", businessId);
		if(Objects.isNull(businessId)) {
			logger.error("Invalid request, businessId cannot be null");
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Invalid request, businessId cannot be null");
		}
		final BusinessGoogleMyBusinessLocation gmbLocation = socialGmbRepo.findByBusinessId(businessId);
		if (Objects.isNull(gmbLocation))
			throw new BirdeyeSocialException(ErrorCodes.GMB_MAPPING_NOT_FOUND,
					"Mapping is not present for given businessId");

		if (gmbLocation.getIsValid() != 1)
			throw new BirdeyeSocialException(ErrorCodes.GMB_MAPPING_INVALID, "GMB page is invalid");

		final String accessToken = gAccessToken.getGoogleAccessToken(gmbLocation);
		if(StringUtils.isEmpty(accessToken)) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GENERATE_ACCESS_TOKEN_ON_GMB, "Failed to generate access token");
		}

		FoodMenusDTO foodMenu = googleService.getGMBFoodMenu(gmbLocation.getAccountId(), gmbLocation.getLocationId(), accessToken);
		logger.info("Food Menu: {}", foodMenu);
		return foodMenu;
	}

	@Override
	@Async
	public void updateGmbFoodMenus(FoodMenusDTO foodMenus) {
		logger.info("Received request to update GMB Food Menus: {}", foodMenus.getBusinessId());
		FoodMenusDTO foodMenuResponse = new FoodMenusDTO();
		if(Objects.isNull(foodMenus.getBusinessId())) {
			logger.error("Invalid request, businessId cannot be null");
			sendFoodMenuObjectToKafka(foodMenus, foodMenuResponse, "Invalid request, businessId cannot be null");
			return;
		}

		final BusinessGoogleMyBusinessLocation gmbLocation = socialGmbRepo.findByBusinessId(foodMenus.getBusinessId());
		if (Objects.isNull(gmbLocation)) {
			logger.error("Page is not mapped for given businessId");
			sendFoodMenuObjectToKafka(foodMenus, foodMenuResponse, "Page is not mapped for given businessId");
			return;
		}

		if (gmbLocation.getIsValid() != 1) {
			logger.error("GMB page is invalid");
			sendFoodMenuObjectToKafka(foodMenus, foodMenuResponse, "GMB mapping is invalid");
			return;
		}

		final String accessToken = gAccessToken.getGoogleAccessToken(gmbLocation);
		if(StringUtils.isEmpty(accessToken)) {
			logger.error("Failed to generate access token");
			sendFoodMenuObjectToKafka(foodMenus, foodMenuResponse, "Failed to generate access token");
			return;
		}
		try {
			createMediaKey(foodMenus, gmbLocation.getLocationUrl(), accessToken);
			foodMenuResponse = googleService.updateGMBFoodMenu(gmbLocation.getAccountId(), gmbLocation.getLocationId(), foodMenus, accessToken);
		} catch (TooManyRequestException tooManyRequestException) {
			try {
				logger.error("TooManyRequestException in rest template while updateGMBFoodMenu: ", tooManyRequestException);
				commonService.retryForAsyncRequest(new AsyncRetryRequestWrapper<>(foodMenus), "retry-gmb-menu-sync", tooManyRequestException);
				return;
			} catch (TooManyRequestException exception) {
				sendFoodMenuObjectToKafka(foodMenus, foodMenuResponse, exception.getMessage());
				return;
			}
		} catch (Exception e) {
			sendFoodMenuObjectToKafka(foodMenus, foodMenuResponse, e.getMessage());
			return;
		}
		sendFoodMenuObjectToKafka(foodMenus, foodMenuResponse, null);
	}



	private void createMediaKey(FoodMenusDTO foodMenus, String locationURI, String accessToken) {
		List<String> errors = CollectionUtils.isNotEmpty(foodMenus.getError()) ? foodMenus.getError() : new ArrayList<>();
		List<Menus> menus = foodMenus.getMenus();

		for(Menus menu : menus) {
			List<Sections> sections = menu.getSections();

			for(Sections section : sections) {
				List<Items> items = section.getItems();

				for(Items item : items) {
					List<Object> mediaInfos = item.getAttributes().getMediaKeys();
					List<Object> keys = new ArrayList<>();
					if (CollectionUtils.isNotEmpty(mediaInfos)) {
						for (Object mediaInfo : mediaInfos) {
							if (mediaInfo instanceof HashMap) {// New request for creating media
								GMBMediaItem mediaItem = createMediaItem(mediaInfo);
								try {
									GMBMediaItem media = googleService.createGoogleMedia(locationURI, accessToken, mediaItem);
									if (Objects.nonNull(media)) {
										String key = media.getName().split("/media/")[1];
										keys.add(key);
									}
								} catch (Exception e) {
									String error = getErrorDetails(item.getLabels().get(0).getDisplayName(), e);
									logger.error("Error while creating media: {}", error);
									errors.add(error);
								}
							} else if (mediaInfo instanceof String) { //Already present media keys
								keys.add(mediaInfo);
							}
						}
						item.getAttributes().setMediaKeys(keys); //Adding all the keys
					}
				}
			}
		}
		foodMenus.setError(errors); //Setting error messages for partial updation
	}


	private String getErrorDetails(String displayName, Exception e) {
		StringBuilder builder = new StringBuilder("Unable to create media for GMB item: ")
				.append(displayName).append(", error: ");
		String error = e.getMessage();
		if(e instanceof SocialPageUpdateException) {
			SocialPageUpdateException exception = (SocialPageUpdateException) e;
			Optional<List<GoogleErrorResponse.Details>> details = Optional.ofNullable(exception.getData())
					.map(data -> (List<GoogleErrorResponse.Details>) data.get("details"));

			GoogleErrorResponse.Details detail = details
					.flatMap(list -> list.isEmpty() ? Optional.empty() : Optional.of(list.get(0)))
					.orElse(null);

			error = Optional.ofNullable(detail)
					.map(d -> Optional.ofNullable(d.getErrorDetails()).orElse(Collections.emptyList())) // If error details is null, return empty list
					.filter(list -> !list.isEmpty()) // Ensure the list is not empty
					.map(errorMap -> errorMap.get(0).getMessage())
					.orElse(e.getMessage()); // Fallback to exception message if errorDetails is null
		}
		return builder.append(error).toString();
	}

	private GMBMediaItem createMediaItem(Object mediaItem) {
		GMBMediaItem media = new GMBMediaItem();
		try {
			Map<String, String> mediaMap = (Map<String, String>) mediaItem;

			if (Objects.nonNull(mediaMap)) {
				media.setSourceUrl(mediaMap.get("sourceUrl"));
				if (StringUtils.isNotEmpty(mediaMap.get("description"))) {
					media.setDescription(mediaMap.get("description"));
				}
				media.setMediaFormat(mediaMap.get("mediaFormat"));
				GMBMediaItem.LocationAssociation locationAssociation = new GMBMediaItem.LocationAssociation(mediaMap.get("category"));
				media.setLocationAssociation(locationAssociation);
			}
		} catch (Exception e) {
			logger.error("Error converting media info: {}", e.getMessage());
		}
		return media;
	}

	private void sendFoodMenuObjectToKafka(FoodMenusDTO foodMenusDTO, FoodMenusDTO foodMenuResponse, String error) {

		if(CollectionUtils.isNotEmpty(foodMenusDTO.getError())) { // Set errors in case partial update
			foodMenuResponse.setError(foodMenusDTO.getError());
		}
		if(StringUtils.isNotEmpty(error)) {
			List<String> errors = CollectionUtils.isNotEmpty(foodMenuResponse.getError()) ? foodMenuResponse.getError() : new ArrayList<>();
			errors.add(error);
			foodMenuResponse.setError(errors);
		}
		producer.sendObjectWithKeyV1(foodMenusDTO.getCorrelationId(), "social-listing-gmb-menu-response-sync", foodMenuResponse);
	}

	@Override
	public GMBProfileLocation updateGmbLocationProfile(GMBProfileLocation location, Integer businessId, String updateMask) {
		GMBProfileLocation locationResponse = new GMBProfileLocation();
		if (businessId == null)
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid args present");
		final BusinessGoogleMyBusinessLocation gmbLocation = socialGmbRepo.findByBusinessId(businessId);
		if (Objects.isNull(gmbLocation))
			throw new BirdeyeSocialException(ErrorCodes.GMB_MAPPING_NOT_FOUND,
					"Mapping is not present for given businessId");
		if (gmbLocation.getIsValid() != 1)
			throw new BirdeyeSocialException(ErrorCodes.GMB_MAPPING_INVALID, "GMB mapping is invalid");
		logger.info("gmbLocation for businessId {}:  is {}", businessId, gmbLocation.getLocationId());
		final String accessToken = gAccessToken.getGoogleAccessToken(gmbLocation);
		if (StringUtils.isEmpty(accessToken)) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GENERATE_ACCESS_TOKEN_ON_GMB, "Failed to generate access token");
		}
		return googleService.updateGmbLocationProfile(gmbLocation.getAccountId(), gmbLocation.getLocationId(), location, accessToken, updateMask);
	}
}
