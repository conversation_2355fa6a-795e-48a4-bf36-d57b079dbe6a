package com.birdeye.social.service.SocialReportService.Converter;

import com.birdeye.social.constant.MetricEnum;
import com.birdeye.social.constant.MetricOptionEnum;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.dto.EsPostDataPoint;
import com.birdeye.social.dto.EsPostTagDataPoint;
import com.birdeye.social.entities.*;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.facebook.NewFbPostData;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.PageInsightEsData;
import com.birdeye.social.insights.ES.PageInsightV2EsData;
import com.birdeye.social.insights.ES.Request.DataModel;
import com.birdeye.social.insights.ES.Request.ESPageRequest;
import com.birdeye.social.insights.ES.Request.InsightsESRequest;
import com.birdeye.social.insights.ES.SearchTemplate;
import com.birdeye.social.insights.Facebook.ExternalAPIResponse.Data;
import com.birdeye.social.insights.Facebook.ExternalAPIResponse.Value;
import com.birdeye.social.insights.Facebook.FacebookInsightRequest;
import com.birdeye.social.insights.Instagram.ExternalApiResponse.IgData;
import com.birdeye.social.insights.Instagram.InstagramInsightRequest;
import com.birdeye.social.insights.Instagram.InstagramPostInsightRequest;
import com.birdeye.social.insights.constants.*;
import com.birdeye.social.linkedin.response.LinkedInElement;
import com.birdeye.social.linkedin.response.LinkedInFollower;
import com.birdeye.social.linkedin.response.LinkedInStat;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.GMBReportInsightsRequest;
import com.birdeye.social.model.GMBReportInsightsResponse;
import com.birdeye.social.model.MetricsResponse;
import com.birdeye.social.model.Youtube.YoutubeAnalytics.YoutubeAnalyticResponse;
import com.birdeye.social.model.Youtube.YoutubeAnalytics.YoutubeAnalyticsMetricsResponse;
import com.birdeye.social.service.GoogleAccessTokenCache;
import com.birdeye.social.service.SocialReportService.ES.ReportsEsService;
import com.birdeye.social.service.SocialTagService;
import com.birdeye.social.tiktok.TikTokPageData;
import com.birdeye.social.trends.ReportTrendsDataPoint;
import com.birdeye.social.trends.SocialTrendsReportResponse;
import com.birdeye.social.trends.TrendsReportRequest;
import com.birdeye.social.twitter.TwitterHistoricalPostInsightResponse;
import com.birdeye.social.utils.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.*;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.*;

@Service
public class ReportDataConverterImpl implements ReportDataConverter {


    @Autowired
    private SocialFBPageRepository socialFBPageRepository;

    @Autowired
    private SocialPostInfoRepository socialPostInfoRepository;

    @Autowired
    private GoogleAccessTokenCache googleAccessToken;

    @Autowired
    private ReportsEsService reportsEsService;

    @Autowired
    private SocialTagService socialTagService;

    private static final Logger log = LoggerFactory.getLogger(ReportDataConverterImpl.class);

    private static final String DATE_FORMAT_STRING = "yyyy-MM-dd'T'hh:mm:ssZ";

    private static final String DATE_FORMATTER_STRING = "yyyy-MM-dd HH:mm:ss";

    private static final String DATE_FORMATTER_STRING_FEED = "yyyy-MM-dd'T'HH:mm:ss";

    private static final String DATE_FOR_STRING = "MM/dd/yyyy";

    private static final String DATE_WITH_DASH_STRING = "yyyy-MM-dd";
    private static final String FOLLOW_GAIN = "follow_gain";
    private static final String FOLLOW_LOST = "follow_lost";
    private static final String TOTAL_FOLLOWERS = "total_follower";
    private static final String LIKES_LOST = "likes_lost";
    private static final String LIKES_GAIN = "likes_gain";
    private static final String TOTAL_LIKES = "total_likes";
    private static final String POST_ENGAGEMENT = "post_engagement";
    private static final String POST_LIKE_COUNT = "post_like_count";
    private static final String POST_COMMENT_COUNT = "post_comment_count";
    private static final String POST_SHARE_COUNT = "post_share_count";
    private static final String LINK_CLICK_COUNT = "post_click_count";
    private static final String OTHER_CLICK_COUNT = "other_click_count";
    private static final String POST_IMPRESSIONS = "post_impressions";
    private static final String POST_REACH = "post_reach";
    private static final String TOTAL_POST = "total_post";

    private final String linkClicks = "link clicks";

    private final String VIDEO_VIEWS = "video_views";

    private final String MESSAGE_SENT = "message_sent";

    private final String MESSAGE_RECEIVED = "message_received";

    @Override
    public InsightsESRequest createESRequestForPage(InsightsRequest insights,String index,List<String> pageIdList, Integer sourceId) {
        InsightsESRequest request = new InsightsESRequest();
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insights.getReportType());
        if(SearchTemplate.REPORT_PUBLISHED_POSTS.equals(searchTemplate)) {
            index = ElasticConstants.POST_INSIGHTS.getName();
        }
        request.setIndex(index);
        request.setRouting(insights.getEnterpriseId().toString());
        request.setSearchTemplate(SearchTemplate.searchTemplate(insights.getReportType()));
        //request.setIncludeExtendedBounds(insights.getIncludeExtendedBounds());
        String businessIds = insights.getBusinessIds().stream().map(Object::toString).collect(Collectors.joining(","));
        String pageIds =  pageIdList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","));
        SimpleDateFormat dateFormatter = new SimpleDateFormat(DATE_FORMATTER_STRING);
        DataModel dataModel = new DataModel(businessIds, pageIds, insights.getEnterpriseId(),
                dateFormatter.format(insights.getStartDate()),dateFormatter.format(insights.getEndDate()),
                convertDate(insights.getStartDate(), insights.getEndDate()),GroupByType.getByType(insights.getGroupByType()).getType(),
                String.valueOf(sourceId), insights.getIncludeExtendedBounds());
        request.setDataModel(dataModel);
        return  request;
    }

    @Override
    public InsightsESRequest createESRequestForMessage(InsightsRequest insights,String index,List<String> pageIdList, Integer sourceId, List<String> sentFeedTypeList,
                                                       List<String> sentSubFeedTypeList, List<String> receiveFeedTypeList,
                                                       List<String> receiveSubFeedTypeList) {
        InsightsESRequest request = new InsightsESRequest();
        request.setIndex(index);
        request.setRouting(insights.getEnterpriseId().toString());
        request.setSearchTemplate(SearchTemplate.searchTemplate(insights.getReportType()));
        //request.setIncludeExtendedBounds(insights.getIncludeExtendedBounds());
        String pageIds =  pageIdList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","));
        String sentFeedTypes = sentFeedTypeList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","));
        String receiveFeedTypes = receiveFeedTypeList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","));
        String sentSubFeedTypes = null;
        String receiveSubFeedTypes = null;
        if(CollectionUtils.isNotEmpty(sentSubFeedTypeList)) {
            sentSubFeedTypes = sentSubFeedTypeList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","));
        }
        if(CollectionUtils.isNotEmpty(receiveSubFeedTypeList)) {
            receiveSubFeedTypes = receiveSubFeedTypeList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","));
        }
        SimpleDateFormat dateFormatter = new SimpleDateFormat(DATE_FORMATTER_STRING_FEED);
        SimpleDateFormat dateFormatterForBound = new SimpleDateFormat(DATE_FORMATTER_STRING);

        DataModel dataModel = DataModel.builder()
                .pageIds(pageIds)
                .startDate(dateFormatter.format(insights.getStartDate()))
                .endDate(dateFormatter.format(insights.getEndDate()))
                .type(GroupByType.getByType(insights.getGroupByType()).getType())
                .sourceIds(String.valueOf(sourceId))
                .includeExtendedBounds(insights.getIncludeExtendedBounds())
                .startDateForBound(dateFormatterForBound.format(insights.getStartDate()))
                .endDateForBound(dateFormatterForBound.format(insights.getEndDate()))
                .sentFeedTypes(sentFeedTypes)
                .receiveFeedTypes(receiveFeedTypes)
                .sentSubFeedTypes(sentSubFeedTypes)
                .receiveSubFeedTypes(receiveSubFeedTypes)
                .build();
        request.setDataModel(dataModel);
        return  request;
    }

    private String convertDate(Date startDate,Date endDate) {
        long oneDayInMillis = 86400000l;
        long diff = endDate.getTime() - startDate.getTime();
        long re = diff % oneDayInMillis;
        if(oneDayInMillis - re == 1000l) {
            diff+=1000l;
        }
        int noOfDaysBetween = Math.toIntExact(TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS));
        return new SimpleDateFormat(DATE_FORMATTER_STRING).format(DateUtils.addDays(startDate,-noOfDaysBetween));
    }

    @Override
    public InsightsESRequest createESRequestForPost(InsightsPostRequest insightsPostRequest, String index) {
        InsightsESRequest request = new InsightsESRequest();
        request.setIndex(index);
        request.setRouting(insightsPostRequest.getEnterpriseId().toString());
        Set<Long> tagIds = new HashSet<>();
        Set<Integer> publisherIds = insightsPostRequest.getPublisherIds();
        if(socialTagService.isUntaggedRequest(insightsPostRequest.getTagIds())) {
            Set<Long> tagIdsWithoutUntagged = insightsPostRequest.getTagIds().stream().filter(s->!s.equals(-1l)).collect(Collectors.toSet());
            request.setSearchTemplate(CollectionUtils.isEmpty(tagIdsWithoutUntagged)?SearchTemplate.POST_INSIGHTS_WITHOUT_TAG_ID:
                    SearchTemplate.POST_INSIGHTS_WITH_TAG_ID_OR_UNTAGGED);
            tagIds = tagIdsWithoutUntagged;
        } else {
            request.setSearchTemplate(CollectionUtils.isEmpty(insightsPostRequest.getTagIds())?SearchTemplate.POST_INSIGHTS:
                    SearchTemplate.POST_INSIGHTS_WITH_TAG_ID);
            tagIds = insightsPostRequest.getTagIds();

        }
        String businessIds = insightsPostRequest.getBusinessIds().stream().map(Object::toString).collect(Collectors.joining(","));
        String pageIds =  insightsPostRequest.getPageIds().stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","));
        SimpleDateFormat dateFormatter = new SimpleDateFormat(DATE_FORMATTER_STRING);
        DataModel dataModel = new DataModel(businessIds, pageIds, insightsPostRequest.getEnterpriseId(),
                dateFormatter.format(insightsPostRequest.getStartDate()),dateFormatter.format(insightsPostRequest.getEndDate()), insightsPostRequest.getSourceIds(),
                insightsPostRequest.getSortParam(),insightsPostRequest.getSortOrder()
                ,String.valueOf(insightsPostRequest.getStartIndex()*insightsPostRequest.getPageSize()),
                insightsPostRequest.getPageSize(),
                (insightsPostRequest.getStartIndex()+1)*insightsPostRequest.getPageSize(),
                insightsPostRequest.getIncludeExtendedBounds(), insightsPostRequest.getDeletedFilter());
        if(CollectionUtils.isNotEmpty(tagIds)) {
            dataModel.setTagIds(tagIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        if(Objects.nonNull(insightsPostRequest.getEnterpriseId())) {
            dataModel.setEnterpriseId(insightsPostRequest.getEnterpriseId());
        }
        if(CollectionUtils.isNotEmpty(publisherIds)) {
            dataModel.setPublisherIds(publisherIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        request.setDataModel(dataModel);
        return  request;
    }

    @Override
    public InsightsESRequest createESRequestForPostForAllChannel(InsightsPostRequest insightsPostRequest, String index) {
        InsightsESRequest request = new InsightsESRequest();
        request.setIndex(index);
        request.setRouting(insightsPostRequest.getEnterpriseId().toString());
        Set<Long> tagIds = new HashSet<>();
        Set<Integer> publisherIds = insightsPostRequest.getPublisherIds();
        if(socialTagService.isUntaggedRequest(insightsPostRequest.getTagIds())) {
            Set<Long> tagIdsWithoutUntagged = insightsPostRequest.getTagIds().stream().filter(s->!s.equals(-1l)).collect(Collectors.toSet());
            request.setSearchTemplate(CollectionUtils.isEmpty(tagIdsWithoutUntagged)?SearchTemplate.POST_INSIGHT_ALL_CHANNEL_WITHOUT_TAG_ID:
                    SearchTemplate.POST_INSIGHT_ALL_CHANNEL_WITH_TAG_ID_OR_UNTAGGED);
            tagIds = tagIdsWithoutUntagged;
        } else {
            request.setSearchTemplate(CollectionUtils.isEmpty(insightsPostRequest.getTagIds())?SearchTemplate.POST_INSIGHT_ALL_CHANNEL:
                    SearchTemplate.POST_INSIGHT_ALL_CHANNEL_WITH_TAG_ID);
            tagIds = insightsPostRequest.getTagIds();
        }
        String businessIds = insightsPostRequest.getBusinessIds().stream().map(Object::toString).collect(Collectors.joining(","));
        String pageIds =  insightsPostRequest.getPageIds().stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","));
        SimpleDateFormat dateFormatter = new SimpleDateFormat(DATE_FORMATTER_STRING);
        DataModel dataModel = new DataModel(businessIds, pageIds, insightsPostRequest.getEnterpriseId(),
                dateFormatter.format(insightsPostRequest.getStartDate()),dateFormatter.format(insightsPostRequest.getEndDate()), insightsPostRequest.getSourceIds(),
                insightsPostRequest.getSortParam(),insightsPostRequest.getSortOrder()
                ,String.valueOf(insightsPostRequest.getStartIndex()*insightsPostRequest.getPageSize()),insightsPostRequest.getPageSize(),
                (insightsPostRequest.getStartIndex()+1)*insightsPostRequest.getPageSize(), insightsPostRequest.getIncludeExtendedBounds(), insightsPostRequest.getDeletedFilter());
        if(CollectionUtils.isNotEmpty(tagIds)) {
            dataModel.setTagIds(tagIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        if(CollectionUtils.isNotEmpty(publisherIds)) {
            dataModel.setPublisherIds(publisherIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        request.setDataModel(dataModel);
        return  request;
    }

    public PageInsightDataPoint preparePageReport(Histogram.Bucket bucket, Date date, SearchTemplate searchTemplate, GroupByType groupByType) throws ParseException {
        PageInsightDataPoint pageInsightDataPoint = new PageInsightDataPoint();
        DecimalFormat df = new DecimalFormat("#.#");
        SimpleDateFormat dateFor = new SimpleDateFormat(DATE_FOR_STRING);
        ParsedSum s1,s2,s3,s4,s5,s6;
        switch (searchTemplate){
            case PAGE_FOLLOWER_INSIGHTS:
                s1 = bucket.getAggregations().get(FOLLOW_GAIN);
                pageInsightDataPoint.setFollowersGain((int)s1.getValue());
                s2 = bucket.getAggregations().get(FOLLOW_LOST);
                pageInsightDataPoint.setFollowersLost((int)s2.getValue());
                pageInsightDataPoint.setNet(pageInsightDataPoint.getFollowersGain() - pageInsightDataPoint.getFollowersLost());
                break;
            case PAGE_LIKES_INSIGHTS:
                s1 = bucket.getAggregations().get("like_gain");
                pageInsightDataPoint.setLikesGain((int)s1.getValue());
                s2 = bucket.getAggregations().get("like_lost");
                pageInsightDataPoint.setLikesLost((int)s2.getValue());
                pageInsightDataPoint.setNet(pageInsightDataPoint.getLikesGain() - pageInsightDataPoint.getLikesLost());
                break;
            case PAGE_POST_ENGAGEMENT:
                s2 = bucket.getAggregations().get(TOTAL_POST);
                pageInsightDataPoint.setTotalPosts((int)s2.getValue());
                s3 = bucket.getAggregations().get(POST_ENGAGEMENT);
                pageInsightDataPoint.setPostEngagements((int)s3.getValue());
                s4 = bucket.getAggregations().get(POST_REACH);
                pageInsightDataPoint.setPostReach((int)s4.getValue());
                s5 = bucket.getAggregations().get(POST_IMPRESSIONS);
                pageInsightDataPoint.setPostImpressions((int)s5.getValue());
                Double engRate = formatToTwoDecimalPlaces(reportsEsService.calculateEngagementRate((int)s3.getValue(), (int)s5.getValue()));
                pageInsightDataPoint.setPostEngRate(engRate);
                break;
            case PAGE_POST_METRIC:
                s1 = bucket.getAggregations().get(POST_ENGAGEMENT);
                pageInsightDataPoint.setPostEngagements((int)s1.getValue());
                s2 = bucket.getAggregations().get(POST_REACH);
                pageInsightDataPoint.setPostReach((int)s2.getValue());
                s3 = bucket.getAggregations().get(POST_IMPRESSIONS);
                pageInsightDataPoint.setPostImpressions((int)s3.getValue());
                s4 = bucket.getAggregations().get(POST_LIKE_COUNT);
                pageInsightDataPoint.setLikeCount((int)s4.getValue());
                s5 = bucket.getAggregations().get(POST_COMMENT_COUNT);
                pageInsightDataPoint.setCommentCount((int)s5.getValue());
                s6 = bucket.getAggregations().get(POST_SHARE_COUNT);
                pageInsightDataPoint.setShareCount((int)s6.getValue());
                break;
            default:
                break;
        }
        pageInsightDataPoint.setStartDate(dateFor.format(date));
        pageInsightDataPoint.setLabel(dateFor.format(date));
        pageInsightDataPoint.setEndDate(endDateConverter(date,groupByType));
        return pageInsightDataPoint;
    }

    public List<PageInsightV2DataPoint> generateDataPointsForReports(Map<String, PageInsightV2EsData> map, SearchTemplate searchTemplate, GroupByType groupByType, int bucketSize) throws ParseException {
        List<PageInsightV2DataPoint> dataPoints = new ArrayList<>();
        SimpleDateFormat dateFor = new SimpleDateFormat(DATE_FOR_STRING);
        for (int i = 0; i < bucketSize; i++) {
            PageInsightV2DataPoint pageInsightDataPoint = new PageInsightV2DataPoint();
            switch (searchTemplate) {
                case PAGE_FOLLOWER_INSIGHTS:
                    createFollowerResponseForAllChannel(map, i, pageInsightDataPoint, groupByType, dateFor);
                    break;
                case PAGE_POST_METRIC:
                    createImpressionResponseForAllChannel(map, i, pageInsightDataPoint, groupByType, dateFor);
                    break;
                case PPR_PAGE_POST_ENG_METRIC:
                    createEngResponseForAllChannel(map, i, pageInsightDataPoint, groupByType, dateFor);
                    break;
                case PPR_PAGE_POST_ENG_RATE_METRIC:
                    createEngRateResponseForAllChannel(map, i, pageInsightDataPoint, groupByType, dateFor);
                    break;
                case PPR_PAGE_VIDEO_VIEWS_METRIC:
                    createVideoViewsResponseForAllChannel(map, i, pageInsightDataPoint, groupByType, dateFor);
                    break;
                case PPR_PAGE_POST_PUBLISHED_METRIC:
                    createPublishedPostResponseForAllChannel(map, i, pageInsightDataPoint, groupByType, dateFor);
                    break;
                case PPR_PAGE_POST_MESSAGE_METRIC:
                    return getMessageVolumeDataPoints(map, groupByType, dateFor);
                case FB_STORY_PERFORMANCE:
                    createDataPointsForStoryPerformance(map, i, pageInsightDataPoint, groupByType, dateFor);
                    break;
                case FB_POST_PUBLISHING_BEHAVIOUR:
                    createDataPointsForPostPublishingBehaviour(map, i, pageInsightDataPoint, groupByType, dateFor);
                    break;
                default:
                    break;
            }

            dataPoints.add(pageInsightDataPoint);
        }

        return dataPoints;
    }

    /**
     * Aggregates video performance data across all buckets into a single data point
     *
     *
     * @param aggregatedDataPoint The data point to populate with aggregated data
     */
    private void aggregateVideoPerformanceData(PageInsightsV2Response response, PageInsightV2DataPoint aggregatedDataPoint) {
        PageInsightV2DataPoint.CountByViews countByViews = new PageInsightV2DataPoint.CountByViews();
        countByViews.setFull(response.getFullVideoViews());
        countByViews.setPartial(response.getVideoPartialViews());
        aggregatedDataPoint.setCountByViews(countByViews);
    }

    private void createDataPointsForStoryPerformance(Map<String, PageInsightV2EsData> map, int i, PageInsightV2DataPoint pageInsightDataPoint,
                                                     GroupByType groupByType, SimpleDateFormat dateFor) throws ParseException {
        for(Map.Entry<String,PageInsightV2EsData> entry : map.entrySet()) {
            Histogram.Bucket bucket = map.get(entry.getKey()) != null && CollectionUtils.isNotEmpty(map.get(entry.getKey()).getBuckets())
                    && map.get(entry.getKey()).getBuckets().size() > i ? map.get(entry.getKey()).getBuckets().get(i) : null;
            pageInsightDataPoint.setTotal(0);
            if (Objects.nonNull(bucket)) {
                Date date = new SimpleDateFormat(DATE_FORMATTER_STRING).parse(bucket.getKeyAsString().trim());
                ParsedSum s1 = bucket.getAggregations().get("published_story_count");
                pageInsightDataPoint.setStartDate(dateFor.format(date));
                pageInsightDataPoint.setLabel(dateFor.format(date));
                pageInsightDataPoint.setEndDate(endDateConverter(date, groupByType));
                pageInsightDataPoint.setPublishedStories((int) s1.getValue());
            }
        }
    }

    private void createDataPointsForPostPublishingBehaviour(Map<String, PageInsightV2EsData> map, int i, PageInsightV2DataPoint pageInsightDataPoint,
                                                            GroupByType groupByType, SimpleDateFormat dateFor) throws ParseException {
        for (Map.Entry<String, PageInsightV2EsData> entry : map.entrySet()) {
            Histogram.Bucket bucket = map.get(entry.getKey()) != null && CollectionUtils.isNotEmpty(map.get(entry.getKey()).getBuckets())
                    && map.get(entry.getKey()).getBuckets().size() > i ? map.get(entry.getKey()).getBuckets().get(i) : null;
            pageInsightDataPoint.setTotal(0);
            if (Objects.nonNull(bucket)) {
                Date date = new SimpleDateFormat(DATE_FORMATTER_STRING).parse(bucket.getKeyAsString().trim());
                ParsedFilter s1 = bucket.getAggregations().get("text_posts");
                ParsedFilter s2 = bucket.getAggregations().get("video_posts");
                ParsedFilter s3 = bucket.getAggregations().get("image_posts");
                ParsedFilter s4 = bucket.getAggregations().get("link_posts");
                pageInsightDataPoint.setStartDate(dateFor.format(date));
                pageInsightDataPoint.setLabel(dateFor.format(date));
                pageInsightDataPoint.setEndDate(endDateConverter(date, groupByType));

                // Set values with null checks - using docCount for ParsedFilter
                int textPostCount = (s1 != null) ? (int) s1.getDocCount() : 0;
                int videoPostCount = (s2 != null) ? (int) s2.getDocCount() : 0;
                int imagePostCount = (s3 != null) ? (int) s3.getDocCount() : 0;
                int linkPostCount = (s4 != null) ? (int) s4.getDocCount() : 0;

                pageInsightDataPoint.setPublishedTextPosts(textPostCount);
                pageInsightDataPoint.setPublishedVideoPosts(videoPostCount);
                pageInsightDataPoint.setPublishedImagePosts(imagePostCount);
                pageInsightDataPoint.setPublishedLinkPosts(linkPostCount);
                pageInsightDataPoint.setTotalPosts(textPostCount + videoPostCount + imagePostCount + linkPostCount);
            }
        }
    }

    private List<PageInsightV2DataPoint> getMessageVolumeDataPoints(Map<String, PageInsightV2EsData> map, GroupByType groupByType, SimpleDateFormat dateFor) {
        List<PageInsightV2DataPoint> response = new ArrayList<>();
        List<PageInsightV2DataPoint> sent = new ArrayList<>();
        List<PageInsightV2DataPoint> received = new ArrayList<>();

        List<Histogram.Bucket> sentBuckets = new ArrayList<>();
        List<Histogram.Bucket> receivedBuckets = new ArrayList<>();
        List<Histogram.Bucket> postDataSentBucket = new ArrayList<>();

        for(Map.Entry<String,PageInsightV2EsData> entry : map.entrySet()) {
            List<? extends Histogram.Bucket> msgSentBucket = map.get(entry.getKey()) != null && CollectionUtils.isNotEmpty(map.get(entry.getKey()).getMsgSentBuckets())
                    ?  map.get(entry.getKey()).getMsgSentBuckets() : null;
            List<? extends Histogram.Bucket> msgReceivedBucket = map.get(entry.getKey()) != null && CollectionUtils.isNotEmpty(map.get(entry.getKey()).getMsgReceivedBuckets())
                    ? map.get(entry.getKey()).getMsgReceivedBuckets() : null;
            List<? extends Histogram.Bucket> postSentBucket = map.get(entry.getKey()) != null && CollectionUtils.isNotEmpty(map.get(entry.getKey()).getBuckets())
                    ? map.get(entry.getKey()).getBuckets() : null;

            if(CollectionUtils.isNotEmpty(msgSentBucket)) {
                sentBuckets.addAll(msgSentBucket);
            }
            if(CollectionUtils.isNotEmpty(msgReceivedBucket)) {
                receivedBuckets.addAll(msgReceivedBucket);
            }
            if(CollectionUtils.isNotEmpty(postSentBucket)) {
                postDataSentBucket.addAll(postSentBucket);
            }
        }

        if(CollectionUtils.isEmpty(sentBuckets) && CollectionUtils.isEmpty(receivedBuckets) && CollectionUtils.isEmpty(postDataSentBucket)) {
            return response;
        }

        for(Histogram.Bucket bucket : sentBuckets) {
            if(repeatedDataPointsForSentMessage(sent, bucket, dateFor)) {
                continue;
            };
            PageInsightV2DataPoint pageInsightDataPoint = new PageInsightV2DataPoint();
            pageInsightDataPoint.setSentMsgs((int) bucket.getDocCount());
            Date date = new Date();
            try {
                date = new SimpleDateFormat(DATE_FORMATTER_STRING).parse(bucket.getKeyAsString().trim());
            } catch (ParseException e) {
                log.error("Error while parsing date",e);
            }

            pageInsightDataPoint.setStartDate(dateFor.format(date));
            pageInsightDataPoint.setLabel(dateFor.format(date));
            pageInsightDataPoint.setEndDate(endDateConverter(date, groupByType));
            pageInsightDataPoint.setDate(bucket.getKeyAsString());
            sent.add(pageInsightDataPoint);
        }


        for(Histogram.Bucket bucket : receivedBuckets) {
            if(repeatedDataPointsForReceivedMessage(received, bucket, dateFor)) {
                continue;
            };
            PageInsightV2DataPoint pageInsightDataPoint = new PageInsightV2DataPoint();
            pageInsightDataPoint.setReceivedMsgs((int) bucket.getDocCount());
            Date date = new Date();
            try {
                date = new SimpleDateFormat(DATE_FORMATTER_STRING).parse(bucket.getKeyAsString().trim());
            } catch (ParseException e) {
                log.error("Error while parsing date",e);
            }
            pageInsightDataPoint.setStartDate(dateFor.format(date));
            pageInsightDataPoint.setLabel(dateFor.format(date));
            pageInsightDataPoint.setEndDate(endDateConverter(date, groupByType));
            pageInsightDataPoint.setDate(bucket.getKeyAsString());
            received.add(pageInsightDataPoint);
        }

        List<PageInsightV2DataPoint> result = mergeObjects(sent, received);

        if(CollectionUtils.isNotEmpty(postDataSentBucket)) {
            postDataSentBucket.stream().forEach(v -> repeatedDataPointsForPosts(result, v, dateFor, groupByType));
        }

        return result;
    }

    private boolean repeatedDataPointsForSentMessage(List<PageInsightV2DataPoint> dataPoints, Histogram.Bucket identifier, SimpleDateFormat dateFor ) {
        boolean skipIteration = false;
        Date date = new Date();
        try {
            date = new SimpleDateFormat(DATE_FORMATTER_STRING).parse(identifier.getKeyAsString().trim());
        } catch (ParseException e) {
            log.error("Error while parsing date",e);
        }
        String identifierLabel = dateFor.format(date);
        for(PageInsightV2DataPoint dataPoint : dataPoints) {
            if(dataPoint.getLabel().equalsIgnoreCase(identifierLabel)) {
                dataPoint.setSentMsgs(dataPoint.getSentMsgs() + (int) identifier.getDocCount());
                skipIteration = true;
            }
        }
        return skipIteration;
    }

    private void repeatedDataPointsForPosts(List<PageInsightV2DataPoint> dataPoints, Histogram.Bucket identifier, SimpleDateFormat dateFor, GroupByType groupByType) {
        boolean entryFound = false;

        for(PageInsightV2DataPoint dataPoint : dataPoints) {
            if(dataPoint.getDate().equalsIgnoreCase(identifier.getKeyAsString())) {
                dataPoint.setSentMsgs(dataPoint.getSentMsgs() + (int) identifier.getDocCount());
                entryFound = true;
            }
        }

        Date date = new Date();
        try {
            date = new SimpleDateFormat(DATE_FORMATTER_STRING).parse(identifier.getKeyAsString().trim());
        } catch (ParseException e) {
            log.error("Error while parsing date",e);
        }

        if(!entryFound) {
            PageInsightV2DataPoint pageInsightDataPoint = new PageInsightV2DataPoint();
            pageInsightDataPoint.setSentMsgs((int) identifier.getDocCount());
            pageInsightDataPoint.setReceivedMsgs(0);
            pageInsightDataPoint.setStartDate(dateFor.format(date));
            pageInsightDataPoint.setLabel(dateFor.format(date));
            pageInsightDataPoint.setEndDate(endDateConverter(date, groupByType));
            pageInsightDataPoint.setDate(identifier.getKeyAsString());
            dataPoints.add(pageInsightDataPoint);
        }

    }

    private boolean repeatedDataPointsForReceivedMessage(List<PageInsightV2DataPoint> dataPoints, Histogram.Bucket identifier, SimpleDateFormat dateFor ) {
        boolean skipIteration = false;
        Date date = new Date();
        try {
            date = new SimpleDateFormat(DATE_FORMATTER_STRING).parse(identifier.getKeyAsString().trim());
        } catch (ParseException e) {
            log.error("Error while parsing date",e);
        }
        String identifierLabel = dateFor.format(date);
        for(PageInsightV2DataPoint dataPoint : dataPoints) {
            if(dataPoint.getLabel().equalsIgnoreCase(identifierLabel)) {
                dataPoint.setReceivedMsgs(dataPoint.getReceivedMsgs() + (int) identifier.getDocCount());
                skipIteration = true;
            }
        }
        return skipIteration;
    }

    public static List<PageInsightV2DataPoint> mergeObjects(List<PageInsightV2DataPoint> sent, List<PageInsightV2DataPoint> received) {
        Map<String, PageInsightV2DataPoint> map = new HashMap<>();
        List<PageInsightV2DataPoint> resultList = new ArrayList<>();

        // Populate map with elements from list1
        for (PageInsightV2DataPoint obj : sent) {
            map.put(obj.getLabel(), obj);
        }

        // Merge elements from list2
        for (PageInsightV2DataPoint obj : received) {
            if (map.containsKey(obj.getLabel())) {
                PageInsightV2DataPoint mergedObject = new PageInsightV2DataPoint(); // Merge logic
                PageInsightV2DataPoint existingObject = map.get(obj.getLabel());
                mergedObject.setSentMsgs(existingObject.getSentMsgs());
                mergedObject.setReceivedMsgs(obj.getReceivedMsgs());
                mergedObject.setStartDate(existingObject.getStartDate());
                mergedObject.setLabel(existingObject.getLabel());
                mergedObject.setEndDate(existingObject.getEndDate());
                mergedObject.setDate(existingObject.getDate());

                resultList.add(mergedObject);
                map.remove(obj.getLabel());
            } else {
                obj.setSentMsgs(0);
                resultList.add(obj);
            }
        }

        // Add elements from list1 that are not present in list2
        if(MapUtils.isNotEmpty(map)) {
            for (Map.Entry<String, PageInsightV2DataPoint> entry : map.entrySet()) {
                entry.getValue().setReceivedMsgs(0);
                resultList.add(entry.getValue());
            }
        }

        return resultList;
    }

    private PageInsightV2DataPoint createFollowerResponseForAllChannel(Map<String, PageInsightV2EsData> map, int i, PageInsightV2DataPoint pageInsightDataPoint, GroupByType groupByType, SimpleDateFormat dateFor) throws ParseException {
        for(Map.Entry<String,PageInsightV2EsData> entry : map.entrySet()) {
            ParsedSum s1, s2, s3, s4;
            int net = 0;
            String channel = entry.getKey();
            Histogram.Bucket bucket = map.get(entry.getKey()) != null && CollectionUtils.isNotEmpty(map.get(entry.getKey()).getBuckets())
                    && map.get(entry.getKey()).getBuckets().size() > i ? map.get(entry.getKey()).getBuckets().get(i) : null;
            pageInsightDataPoint.setTotal(0);
            if (Objects.nonNull(bucket)) {
                Date date = new SimpleDateFormat(DATE_FORMATTER_STRING).parse(bucket.getKeyAsString().trim());

                s1 = bucket.getAggregations().get(FOLLOW_GAIN);
                s2 = bucket.getAggregations().get(FOLLOW_LOST);
                net = (int) s1.getValue() - (int) s2.getValue();
                pageInsightDataPoint.setStartDate(dateFor.format(date));
                pageInsightDataPoint.setLabel(dateFor.format(date));
                pageInsightDataPoint.setEndDate(endDateConverter(date, groupByType));
            }
            if(channel.equalsIgnoreCase(SocialChannel.FACEBOOK.getName())){
                pageInsightDataPoint.setFbNetGrowth(net);
                if(Objects.nonNull(bucket)) {
                    s3 = bucket.getAggregations().get(TOTAL_FOLLOWERS);
                    s4 = bucket.getAggregations().get(TOTAL_LIKES);
                    pageInsightDataPoint.setTotalFollowers((int) s3.getValue());
                    pageInsightDataPoint.setTotalLikes((int) s4.getValue());
                }
            } else if(channel.equalsIgnoreCase(SocialChannel.INSTAGRAM.getName())){
                pageInsightDataPoint.setIgNetGrowth(net);
            }else if(channel.equalsIgnoreCase(SocialChannel.LINKEDIN.getName())){
                pageInsightDataPoint.setLnNetGrowth(net);
            }else if(channel.equalsIgnoreCase(SocialChannel.TWITTER.getName())){
                pageInsightDataPoint.setTwNetGrowth(net);
            }else if(channel.equalsIgnoreCase(SocialChannel.YOUTUBE.getName())) {
                pageInsightDataPoint.setYtNetGrowth(net);
            } else if(channel.equalsIgnoreCase(SocialChannel.TIKTOK.getName())) {
                pageInsightDataPoint.setTtNetGrowth(net);
            }
        }
        pageInsightDataPoint.setTotal((Objects.nonNull(pageInsightDataPoint.getFbNetGrowth()) ? pageInsightDataPoint.getFbNetGrowth() : 0)
                + (Objects.nonNull(pageInsightDataPoint.getIgNetGrowth()) ? pageInsightDataPoint.getIgNetGrowth() : 0)
                + (Objects.nonNull(pageInsightDataPoint.getLnNetGrowth()) ? pageInsightDataPoint.getLnNetGrowth() : 0)
                + (Objects.nonNull(pageInsightDataPoint.getTwNetGrowth()) ? pageInsightDataPoint.getTwNetGrowth() : 0)
                + (Objects.nonNull(pageInsightDataPoint.getYtNetGrowth()) ? pageInsightDataPoint.getYtNetGrowth() : 0)
                + (Objects.nonNull(pageInsightDataPoint.getTtNetGrowth()) ? pageInsightDataPoint.getTtNetGrowth() : 0)
        );
        return pageInsightDataPoint;
    }
    private PageInsightV2DataPoint createImpressionResponseForAllChannel(Map<String, PageInsightV2EsData> map, int i, PageInsightV2DataPoint pageInsightDataPoint, GroupByType groupByType, SimpleDateFormat dateFor) throws ParseException {
        pageInsightDataPoint.setTotal(0);
        for(Map.Entry<String,PageInsightV2EsData> entry : map.entrySet()) {
            ParsedSum s1, s2;
            int impression = 0;
            int reach = 0;
            String channel = entry.getKey();
            Histogram.Bucket bucket = map.get(entry.getKey()) != null && CollectionUtils.isNotEmpty(map.get(entry.getKey()).getBuckets())
                    && map.get(entry.getKey()).getBuckets().size() > i ? map.get(entry.getKey()).getBuckets().get(i) : null;
            if (Objects.nonNull(bucket)) {
                Date date = new SimpleDateFormat(DATE_FORMATTER_STRING).parse(bucket.getKeyAsString().trim());

                s1 = bucket.getAggregations().get(POST_IMPRESSIONS);
                impression = (int) s1.getValue();
                s2 = bucket.getAggregations().get(POST_REACH);
                reach = (int) s2.getValue();
                pageInsightDataPoint.setStartDate(dateFor.format(date));
                pageInsightDataPoint.setLabel(dateFor.format(date));
                pageInsightDataPoint.setEndDate(endDateConverter(date, groupByType));
            }
            if(channel.equalsIgnoreCase(SocialChannel.FACEBOOK.getName())){
                pageInsightDataPoint.setFbPostImpressions(impression);
                pageInsightDataPoint.setFbPostReach(reach);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getFbPostImpressions());
            }
            else if(channel.equalsIgnoreCase(SocialChannel.INSTAGRAM.getName())){
                pageInsightDataPoint.setIgPostImpressions(impression);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getIgPostImpressions());
            }else if(channel.equalsIgnoreCase(SocialChannel.LINKEDIN.getName())){
                pageInsightDataPoint.setLnPostImpressions(impression);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getLnPostImpressions());
            }else if(channel.equalsIgnoreCase(SocialChannel.TWITTER.getName())){
                pageInsightDataPoint.setTwPostImpressions(impression);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getTwPostImpressions());
            } else if(channel.equalsIgnoreCase(SocialChannel.YOUTUBE.getName())){
                pageInsightDataPoint.setYtPostImpressions(impression);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getYtPostImpressions());
            } else if(channel.equalsIgnoreCase(SocialChannel.TIKTOK.getName())){
                pageInsightDataPoint.setTtPostImpressions(impression);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getTtPostImpressions());
            }
        }
        return pageInsightDataPoint;
    }

    private PageInsightV2DataPoint createEngRateResponseForAllChannel(Map<String, PageInsightV2EsData> map, int i, PageInsightV2DataPoint pageInsightDataPoint, GroupByType groupByType, SimpleDateFormat dateFor) throws ParseException {
        pageInsightDataPoint.setTotalEngRate(0.0);
        for(Map.Entry<String,PageInsightV2EsData> entry : map.entrySet()) {
            ParsedSum s1, s2;
            Double engRate = 0.0;
            String channel = entry.getKey();
            Histogram.Bucket bucket = map.get(entry.getKey()) != null && CollectionUtils.isNotEmpty(map.get(entry.getKey()).getBuckets())
                    && map.get(entry.getKey()).getBuckets().size() > i ? map.get(entry.getKey()).getBuckets().get(i) : null;
            if (Objects.nonNull(bucket)) {
                Date date = new SimpleDateFormat(DATE_FORMATTER_STRING).parse(bucket.getKeyAsString().trim());

                s1 = bucket.getAggregations().get(POST_ENGAGEMENT);
                s2 = bucket.getAggregations().get(POST_IMPRESSIONS);
                engRate = formatToTwoDecimalPlaces(reportsEsService.calculateEngagementRate((int)s1.getValue(), (int)s2.getValue()));
                pageInsightDataPoint.setStartDate(dateFor.format(date));
                pageInsightDataPoint.setLabel(dateFor.format(date));
                pageInsightDataPoint.setEndDate(endDateConverter(date, groupByType));
            }

            if(channel.equalsIgnoreCase(SocialChannel.FACEBOOK.getName())){
                pageInsightDataPoint.setFbPostEngRate(engRate);
                pageInsightDataPoint.setTotalEngRate(formatToTwoDecimalPlaces(pageInsightDataPoint.getTotalEngRate() +
                        pageInsightDataPoint.getFbPostEngRate()));
            }else if(channel.equalsIgnoreCase(SocialChannel.INSTAGRAM.getName())){
                pageInsightDataPoint.setIgPostEngRate(engRate);
                pageInsightDataPoint.setTotalEngRate(formatToTwoDecimalPlaces(pageInsightDataPoint.getTotalEngRate() +
                        pageInsightDataPoint.getIgPostEngRate()));
            }else if(channel.equalsIgnoreCase(SocialChannel.LINKEDIN.getName())){
                pageInsightDataPoint.setLnPostEngRate(engRate);
                pageInsightDataPoint.setTotalEngRate(formatToTwoDecimalPlaces(pageInsightDataPoint.getTotalEngRate()
                        + pageInsightDataPoint.getLnPostEngRate()));
            }else if(channel.equalsIgnoreCase(SocialChannel.TWITTER.getName())){
                pageInsightDataPoint.setTwPostEngRate(engRate);
                pageInsightDataPoint.setTotalEngRate(formatToTwoDecimalPlaces(pageInsightDataPoint.getTotalEngRate() +
                        pageInsightDataPoint.getTwPostEngRate()));
            } else if(channel.equalsIgnoreCase(SocialChannel.YOUTUBE.getName())){
                pageInsightDataPoint.setYtPostEngRate(engRate);
                pageInsightDataPoint.setTotalEngRate(formatToTwoDecimalPlaces(pageInsightDataPoint.getTotalEngRate() +
                        pageInsightDataPoint.getYtPostEngRate()));
            } else if(channel.equalsIgnoreCase(SocialChannel.TIKTOK.getName())){
                pageInsightDataPoint.setTtPostEngRate(engRate);
                pageInsightDataPoint.setTotalEngRate(formatToTwoDecimalPlaces(pageInsightDataPoint.getTotalEngRate() +
                        pageInsightDataPoint.getTtPostEngRate()));
            }
        }
        return pageInsightDataPoint;
    }

    private PageInsightV2DataPoint createPublishedPostResponseForAllChannel(Map<String, PageInsightV2EsData> map, int i, PageInsightV2DataPoint pageInsightDataPoint, GroupByType groupByType, SimpleDateFormat dateFor) throws ParseException {
        pageInsightDataPoint.setTotal(0);
        for(Map.Entry<String,PageInsightV2EsData> entry : map.entrySet()) {
            ParsedSum s1, s2;
            Integer totalPosts = 0;
            String channel = entry.getKey();
            Histogram.Bucket bucket = map.get(entry.getKey()) != null && CollectionUtils.isNotEmpty(map.get(entry.getKey()).getBuckets())
                    && map.get(entry.getKey()).getBuckets().size() > i ? map.get(entry.getKey()).getBuckets().get(i) : null;
            if (Objects.nonNull(bucket)) {
                Date date = new SimpleDateFormat(DATE_FORMATTER_STRING).parse(bucket.getKeyAsString().trim());

                if(SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)) {
                    s1 = bucket.getAggregations().get(TOTAL_POST);
                    totalPosts = (int) s1.getValue();
                } else {
                    totalPosts = (int) bucket.getDocCount();
                }
                pageInsightDataPoint.setStartDate(dateFor.format(date));
                pageInsightDataPoint.setLabel(dateFor.format(date));
                pageInsightDataPoint.setEndDate(endDateConverter(date, groupByType));
            }

            if(channel.equalsIgnoreCase(SocialChannel.FACEBOOK.getName())){
                pageInsightDataPoint.setFbPostCount(totalPosts);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getFbPostCount());
            }else if(channel.equalsIgnoreCase(SocialChannel.INSTAGRAM.getName())){
                pageInsightDataPoint.setIgPostCount(totalPosts);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getIgPostCount());
            }else if(channel.equalsIgnoreCase(SocialChannel.LINKEDIN.getName())){
                pageInsightDataPoint.setLnPostCount(totalPosts);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getLnPostCount());
            }else if(channel.equalsIgnoreCase(SocialChannel.TWITTER.getName())){
                pageInsightDataPoint.setTwPostCount(totalPosts);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getTwPostCount());
            } else if(channel.equalsIgnoreCase(SocialChannel.YOUTUBE.getName())){
                pageInsightDataPoint.setYtPostCount(totalPosts);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getYtPostCount());
            }
            else if(channel.equalsIgnoreCase(SocialChannel.TIKTOK.getName())){
                pageInsightDataPoint.setTtPostCount(totalPosts);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getTtPostCount());
            }
        }
        return pageInsightDataPoint;
    }

    private PageInsightV2DataPoint createEngResponseForAllChannel(Map<String, PageInsightV2EsData> map, int i, PageInsightV2DataPoint pageInsightDataPoint, GroupByType groupByType, SimpleDateFormat dateFor) throws ParseException {
        pageInsightDataPoint.setTotal(0);
        for (Map.Entry<String, PageInsightV2EsData> entry : map.entrySet()) {
            ParsedSum s1, s2, s3, s4, s5, s6;
            int eng = 0, likes = 0, comments = 0, shares = 0, linkClickCount = 0, otherClickCount = 0;
            String channel = entry.getKey();
            Histogram.Bucket bucket = map.get(entry.getKey()) != null && CollectionUtils.isNotEmpty(map.get(entry.getKey()).getBuckets())
                    && map.get(entry.getKey()).getBuckets().size() > i ? map.get(entry.getKey()).getBuckets().get(i) : null;
            if (Objects.nonNull(bucket)) {
                Date date = new SimpleDateFormat(DATE_FORMATTER_STRING).parse(bucket.getKeyAsString().trim());

                s1 = bucket.getAggregations().get(POST_ENGAGEMENT);
                eng = (int) s1.getValue();
                s2 = bucket.getAggregations().get(POST_LIKE_COUNT);
                likes = Objects.nonNull(s2) ? (int) s2.getValue() : 0;
                s3 = bucket.getAggregations().get(POST_COMMENT_COUNT);
                comments = Objects.nonNull(s3) ? (int) s3.getValue() : 0;
                s4 = bucket.getAggregations().get(POST_SHARE_COUNT);
                shares = Objects.nonNull(s4) ? (int) s4.getValue() : 0;
                s5 = bucket.getAggregations().get(LINK_CLICK_COUNT);
                linkClickCount = Objects.nonNull(s5) ? (int) s5.getValue() : 0;
                s6 = bucket.getAggregations().get(OTHER_CLICK_COUNT);
                otherClickCount = Objects.nonNull(s6) ? (int) s6.getValue() : 0;
                pageInsightDataPoint.setStartDate(dateFor.format(date));
                pageInsightDataPoint.setLabel(dateFor.format(date));
                pageInsightDataPoint.setEndDate(endDateConverter(date, groupByType));
            }
            if (channel.equalsIgnoreCase(SocialChannel.FACEBOOK.getName())) {
                pageInsightDataPoint.setFbPostEngagements(eng);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getFbPostEngagements());
                pageInsightDataPoint.setClickCount(linkClickCount);
                pageInsightDataPoint.setOtherClickCount(otherClickCount);
                pageInsightDataPoint.setLikeCount(likes);
                pageInsightDataPoint.setShareCount(shares);
                pageInsightDataPoint.setCommentCount(comments);
            } else if (channel.equalsIgnoreCase(SocialChannel.INSTAGRAM.getName())) {
                pageInsightDataPoint.setIgPostEngagements(eng);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getIgPostEngagements());
            } else if (channel.equalsIgnoreCase(SocialChannel.LINKEDIN.getName())) {
                pageInsightDataPoint.setLnPostEngagements(eng);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getLnPostEngagements());
            } else if (channel.equalsIgnoreCase(SocialChannel.TWITTER.getName())) {
                pageInsightDataPoint.setTwPostEngagements(eng);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getTwPostEngagements());
            } else if (channel.equalsIgnoreCase(SocialChannel.YOUTUBE.getName())) {
                pageInsightDataPoint.setYtPostEngagements(eng);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getYtPostEngagements());
            } else if (channel.equalsIgnoreCase(SocialChannel.TIKTOK.getName())) {
                pageInsightDataPoint.setTtPostEngagements(eng);
                pageInsightDataPoint.setLikeCount(likes);
                pageInsightDataPoint.setShareCount(shares);
                pageInsightDataPoint.setCommentCount(comments);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getTtPostEngagements());
            }
        }
        return pageInsightDataPoint;
    }

    private PageInsightV2DataPoint createVideoViewsResponseForAllChannel(Map<String, PageInsightV2EsData> map, int i, PageInsightV2DataPoint pageInsightDataPoint, GroupByType groupByType, SimpleDateFormat dateFor) throws ParseException {
        pageInsightDataPoint.setTotal(0);
        for(Map.Entry<String,PageInsightV2EsData> entry : map.entrySet()) {
            ParsedSum s1;
            int videoViews = 0;
            String channel = entry.getKey();
            Histogram.Bucket bucket = map.get(entry.getKey()) != null && CollectionUtils.isNotEmpty(map.get(entry.getKey()).getBuckets())
                    && map.get(entry.getKey()).getBuckets().size() > i ? map.get(entry.getKey()).getBuckets().get(i) : null;
            if (Objects.nonNull(bucket)) {
                Date date = new SimpleDateFormat(DATE_FORMATTER_STRING).parse(bucket.getKeyAsString().trim());

                s1 = bucket.getAggregations().get(VIDEO_VIEWS);
                videoViews = (int) s1.getValue();
                pageInsightDataPoint.setStartDate(dateFor.format(date));
                pageInsightDataPoint.setLabel(dateFor.format(date));
                pageInsightDataPoint.setEndDate(endDateConverter(date, groupByType));
            }
            if(channel.equalsIgnoreCase(SocialChannel.FACEBOOK.getName())){
                pageInsightDataPoint.setFbVideoViews(videoViews);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getFbVideoViews());
            } else if(channel.equalsIgnoreCase(SocialChannel.INSTAGRAM.getName())){
                pageInsightDataPoint.setIgVideoViews(videoViews);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getIgVideoViews());
            }else if(channel.equalsIgnoreCase(SocialChannel.LINKEDIN.getName())){
                pageInsightDataPoint.setLnVideoViews(videoViews);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getLnVideoViews());
            }else if(channel.equalsIgnoreCase(SocialChannel.TWITTER.getName())){
                pageInsightDataPoint.setTwVideoViews(videoViews);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getTwVideoViews());
            } else if(channel.equalsIgnoreCase(SocialChannel.YOUTUBE.getName())){
                pageInsightDataPoint.setYtVideoViews(videoViews);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getYtVideoViews());
            }
            else if(channel.equalsIgnoreCase(SocialChannel.TIKTOK.getName())){
                pageInsightDataPoint.setTtVideoViews(videoViews);
                pageInsightDataPoint.setTotal(pageInsightDataPoint.getTotal() + pageInsightDataPoint.getTtVideoViews());
            }
        }
        return pageInsightDataPoint;
    }

    private String endDateConverter(Date date, GroupByType groupByType) {
        return new SimpleDateFormat(DATE_FOR_STRING).format(DateUtils.addDays(date, groupByType.getDays()));
    }


    @Override
    public List<ESPageRequest> createPageInsightsObject(PageInsights pageInsights) {
        List<ESPageRequest> esPageRequests = new ArrayList<>();
        if (CollectionUtils.isEmpty(pageInsights.getPageInsights())) {
            return esPageRequests;
        }

        String channel = pageInsights.getChannel();

        pageInsights.getPageInsights().forEach(pageInsight -> {
            double engRate;
            double storyEngRate;
            if (Objects.nonNull(pageInsight.getPostEngagements()) && Objects.nonNull(pageInsight.getPostImpressions())) {
                engRate = (pageInsight.getPostImpressions() != 0 ? ((double) pageInsight.getPostEngagements() / pageInsight.getPostImpressions()) * 100 : 0d);
            } else {
                engRate = 0;
            }
            if (Objects.nonNull(pageInsight.getStoryEngagements()) && Objects.nonNull(pageInsight.getStoryImpressions())) {
                storyEngRate = (pageInsight.getStoryImpressions() != 0 ? ((double) pageInsight.getStoryEngagements() / pageInsight.getStoryImpressions()) * 100 : 0d);
            } else {
                storyEngRate = 0;
            }
            ESPageRequest.ESPageRequestBuilder builder = ESPageRequest.builder()
                    .page_id(pageInsights.getPageId())
                    .business_id(pageInsights.getBusinessId())
                    .day(new SimpleDateFormat(DATE_WITH_DASH_STRING).format(pageInsight.getDate()) + " 00:00:00")
                    .ent_id(pageInsights.getEnterpriseId())
                    .follower_gain(pageInsight.getFollowerGainCount())
                    .like_lost(pageInsight.getLikesLostCount())
                    .follower_lost(pageInsight.getFollowerLostCount())
                    .like_gain(pageInsight.getLikesGainCount())
                    .post_engagement(pageInsight.getPostEngagements())
                    .post_reach(pageInsight.getPostReach())
                    .post_impressions(pageInsight.getPostImpressions())
                    .post_eng_rate(engRate)
                    .total_follower_count(pageInsight.getTotalFollower())
                    .total_like(pageInsight.getTotalLikes())
                    .post_total_count(pageInsight.getPostTotalCount())
                    .post_engagement_total(pageInsight.getPostEngagementTotal())
                    .post_impression_total(pageInsight.getPostImpressionTotal())
                    .post_count(pageInsight.getPostCount())
                    .click_count(pageInsight.getLinkClickCount())
                    .other_click_count(pageInsight.getOtherClickCount())
                    .video_views(pageInsight.getProfileVideoViews())
                    .total_video_views(pageInsight.getTotalProfileVideoViews())
                    .post_like_count(pageInsight.getPagePostLikeCount())
                    .post_comment_count(pageInsight.getCommentCount())
                    .post_share_count(pageInsight.getShareCount())
                    .total_post_comment_count(pageInsight.getTotalPostCommentCount())
                    .total_post_like_count(pageInsight.getTotalPostLikeCount())
                    .total_post_share_count(pageInsight.getTotalPostShareCount())
                    .audience_activity(pageInsight.getAudienceActivity())
                    .audience_countries(pageInsight.getAudienceCountries())
                    .audience_cities(pageInsight.getAudienceCities())
                    .audience_ages(pageInsight.getAudienceAges())
                    .audience_genders(pageInsight.getAudienceGenders())
                    .is_business_account(pageInsight.getIsBusinessAccount());

            if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
                builder
                        .video_complete_views_30s(pageInsight.getVideoCompleteViews30s())
                        .video_partial_views(pageInsight.getVideoPartialViews())
                        .video_views_click_to_play(pageInsight.getVideoViewsClickToPlay())
                        .video_complete_views_30s_click_to_play(pageInsight.getVideoCompleteViews30sClickToPlay())
                        .video_partial_views_click_to_play(pageInsight.getVideoPartialViewsClickToPlay())
                        .video_views_autoplay(pageInsight.getVideoViewsAutoplayed())
                        .video_complete_views_30s_autoplay(pageInsight.getVideoCompleteViews30sAutoplayed())
                        .video_partial_views_autoplay(pageInsight.getVideoPartialViewsAutoplayed())
                        .published_story_count(pageInsight.getFbStoryCount())
                        .story_impressions(pageInsight.getStoryImpressions())
                        .story_engagements(pageInsight.getStoryEngagements())
                        .story_engagement_rate(storyEngRate)
                        .story_reach(pageInsight.getStoryReach())
                        .story_likes(pageInsight.getStoryLikes())
                        .story_comments(pageInsight.getStoryComments())
                        .story_shares(pageInsight.getStoryShares());
            }
            esPageRequests.add(builder.build());
        });
        return esPageRequests;
    }

    /**
     * Prepares day-wise data for Facebook pages from insights data
     *
     * @param dataList            List of data points from Facebook insights API
     * @param fbPage              Facebook page information
     * @param linkClickCount      Count of link clicks
     * @param otherClickCount     Count of other clicks
     * @param publishedStoryCount Count of published stories
     * @param storyImpressions    Story impressions count
     * @param storyEngagements    Story engagements count
     * @param storyReach          Story reach count
     * @param storyLikes          Story likes count
     * @param storyComments       Story comments count
     * @param storyShares         Story shares count
     * @return List of PageLevelMetaData objects with processed insights
     */
    @Override
    public List<PageLevelMetaData> prepareDayWiseDataForFacebook(List<Data> dataList, BusinessFBPage fbPage,
                                                                 Integer linkClickCount, Integer otherClickCount,
                                                                 Integer publishedStoryCount, Integer storyImpressions,
                                                                 Integer storyEngagements, Integer storyReach,
                                                                 Integer storyLikes, Integer storyComments, Integer storyShares) {
        // Return empty list if input data is empty or null
        if (CollectionUtils.isEmpty(dataList) || Objects.isNull(dataList.get(0))) {
            return new ArrayList<>();
        }

        // Map to store data by date
        Map<Date, PageLevelMetaData> pageLevelMetaDataMap = new HashMap<>();

        // Process each data point
        dataList.forEach(data -> processDataPoint(data, pageLevelMetaDataMap));

        // Calculate derived video metrics
        calculateDerivedVideoMetrics(pageLevelMetaDataMap);

        // If map is empty, return empty list
        if (pageLevelMetaDataMap.isEmpty()) {
            log.info("Page level meta data is empty");
            return new ArrayList<>();
        }

        // Set additional metrics for the most recent date
        Date maxKey = Collections.max(pageLevelMetaDataMap.keySet());
        log.info("Max key: {}", maxKey);

        // Set story and click metrics for the most recent date
        setAdditionalMetrics(pageLevelMetaDataMap.get(maxKey), linkClickCount, otherClickCount,
                publishedStoryCount, storyImpressions, storyEngagements, storyReach,
                storyLikes, storyComments, storyShares);

        // Calculate engagement breakdown and persist previous data
        calculatePagePostsEngagementBreakdown(fbPage, pageLevelMetaDataMap, maxKey);
        persistPreviousPagePostInsightsData(fbPage, pageLevelMetaDataMap);

        return new ArrayList<>(pageLevelMetaDataMap.values());
    }

    /**
     * Processes a single data point from Facebook insights
     *
     * @param data                 The data point to process
     * @param pageLevelMetaDataMap Map to store processed data by date
     */
    private void processDataPoint(Data data, Map<Date, PageLevelMetaData> pageLevelMetaDataMap) {
        String name = data.getName();

        // Process based on metric name
        switch (name) {
            case InsightsConstants.page_post_engagements:
                processValues(data, pageLevelMetaDataMap, (value, metaData) -> {
                    metaData.setPostEngagements((Integer) value.getValue());
                    metaData.setPostCount(0);
                });
                break;

            case InsightsConstants.page_impressions_unique:
                processValues(data, pageLevelMetaDataMap, (value, metaData) ->
                        metaData.setPostReach((Integer) value.getValue()));
                break;

            case InsightsConstants.page_impressions:
                processValues(data, pageLevelMetaDataMap, (value, metaData) ->
                        metaData.setPostImpressions((Integer) value.getValue()));
                break;

            case InsightsConstants.page_fan_adds:
                processValues(data, pageLevelMetaDataMap, (value, metaData) ->
                        metaData.setLikesGainCount((Integer) value.getValue()));
                break;

            case InsightsConstants.page_fans:
                processValues(data, pageLevelMetaDataMap, (value, metaData) ->
                        metaData.setTotalLikes((Integer) value.getValue()));
                break;

            case InsightsConstants.page_fan_removes:
                processValues(data, pageLevelMetaDataMap, (value, metaData) ->
                        metaData.setLikesLostCount((Integer) value.getValue()));
                break;

            case InsightsConstants.page_daily_follows:
                processValues(data, pageLevelMetaDataMap, (value, metaData) ->
                        metaData.setFollowerGainCount((Integer) value.getValue()));
                break;

            case InsightsConstants.page_daily_unfollows:
                processValues(data, pageLevelMetaDataMap, (value, metaData) ->
                        metaData.setFollowerLostCount((Integer) value.getValue()));
                break;

            case InsightsConstants.page_follows:
                processValues(data, pageLevelMetaDataMap, (value, metaData) ->
                        metaData.setTotalFollower((Integer) value.getValue()));
                break;

            case InsightsConstants.page_video_views:
                processValues(data, pageLevelMetaDataMap, (value, metaData) ->
                        metaData.setProfileVideoViews((Integer) value.getValue()));
                break;

            case InsightsConstants.page_video_complete_views_30s:
                processValues(data, pageLevelMetaDataMap, (value, metaData) -> {
                    Integer completeViews = (Integer) value.getValue();
                    metaData.setVideoCompleteViews30s(completeViews != null ? completeViews : 0);
                });
                break;

            case InsightsConstants.page_video_views_click_to_play:
                processValues(data, pageLevelMetaDataMap, (value, metaData) -> {
                    Integer clickToPlayViews = (Integer) value.getValue();
                    metaData.setVideoViewsClickToPlay(clickToPlayViews != null ? clickToPlayViews : 0);
                });
                break;

            case InsightsConstants.page_video_views_autoplayed:
                processValues(data, pageLevelMetaDataMap, (value, metaData) -> {
                    Integer autoplayedViews = (Integer) value.getValue();
                    metaData.setVideoViewsAutoplayed(autoplayedViews != null ? autoplayedViews : 0);
                });
                break;

            case InsightsConstants.page_video_complete_views_30s_click_to_play:
                processValues(data, pageLevelMetaDataMap, (value, metaData) -> {
                    Integer completeClickToPlayViews = (Integer) value.getValue();
                    metaData.setVideoCompleteViews30sClickToPlay(completeClickToPlayViews != null ? completeClickToPlayViews : 0);
                });
                break;

            case InsightsConstants.page_video_complete_views_30s_autoplayed:
                processValues(data, pageLevelMetaDataMap, (value, metaData) -> {
                    Integer completeAutoplayedViews = (Integer) value.getValue();
                    metaData.setVideoCompleteViews30sAutoplayed(completeAutoplayedViews != null ? completeAutoplayedViews : 0);
                });
                break;

            case InsightsConstants.page_actions_post_reactions_total:
                processValues(data, pageLevelMetaDataMap, (value, metaData) -> {
                    Integer pagePostLikeCount = 0;
                    if (value.getValue() instanceof Map) {
                        Map<String, Integer> reactions = (Map<String, Integer>) value.getValue();
                        for (Integer reactionCount : reactions.values()) {
                            pagePostLikeCount += reactionCount;
                        }
                    }
                    metaData.setPagePostLikeCount(pagePostLikeCount);
                });
                break;

            default:
                break;
        }

        // Initialize click counts for all dates
        processValues(data, pageLevelMetaDataMap, (value, metaData) -> {
            metaData.setLinkClickCount(0);
            metaData.setOtherClickCount(0);
            metaData.setFbStoryCount(0);
            metaData.setStoryImpressions(0);
            metaData.setStoryEngagements(0);
            metaData.setStoryReach(0);
            metaData.setStoryLikes(0);
            metaData.setStoryComments(0);
            metaData.setStoryShares(0);
        });
    }

    /**
     * Processes values for a specific metric and applies the setter function
     *
     * @param data                 The data point containing values
     * @param pageLevelMetaDataMap Map to store processed data
     * @param setter               Function to apply to each value
     */
    private void processValues(Data data, Map<Date, PageLevelMetaData> pageLevelMetaDataMap,
                               BiConsumer<Value, PageLevelMetaData> setter) {
        data.getValues().forEach(value -> {
            PageLevelMetaData pageLevelMetaData = getPageLevelData(value, pageLevelMetaDataMap);
            setter.accept(value, pageLevelMetaData);
            pageLevelMetaDataMap.put(pageLevelMetaData.getDate(), pageLevelMetaData);
        });
    }

    /**
     * Calculates derived video metrics for all dates
     *
     * @param pageLevelMetaDataMap Map containing data by date
     */
    private void calculateDerivedVideoMetrics(Map<Date, PageLevelMetaData> pageLevelMetaDataMap) {
        pageLevelMetaDataMap.values().forEach(metaData -> {
            // Calculate partial video views (total views - full views)
            if (metaData.getProfileVideoViews() != null && metaData.getVideoCompleteViews30s() != null) {
                int partialViews = Math.max(0, metaData.getProfileVideoViews() - metaData.getVideoCompleteViews30s());
                metaData.setVideoPartialViews(partialViews);
            }

            // Calculate partial click-to-play views
            if (metaData.getVideoViewsClickToPlay() != null && metaData.getVideoCompleteViews30sClickToPlay() != null) {
                int partialClickToPlay = Math.max(0, metaData.getVideoViewsClickToPlay() - metaData.getVideoCompleteViews30sClickToPlay());
                metaData.setVideoPartialViewsClickToPlay(partialClickToPlay);
            }

            // Calculate partial auto-played views
            if (metaData.getVideoViewsAutoplayed() != null && metaData.getVideoCompleteViews30sAutoplayed() != null) {
                int partialAutoPlayed = Math.max(0, metaData.getVideoViewsAutoplayed() - metaData.getVideoCompleteViews30sAutoplayed());
                metaData.setVideoPartialViewsAutoplayed(partialAutoPlayed);
            }
        });
    }

    /**
     * Sets additional metrics for the most recent date entry
     *
     * @param metaData            The metadata object to update
     * @param linkClickCount      Count of link clicks
     * @param otherClickCount     Count of other clicks
     * @param publishedStoryCount Count of published stories
     * @param storyImpressions    Story impressions count
     * @param storyEngagements    Story engagements count
     * @param storyReach          Story reach count
     * @param storyLikes          Story likes count
     * @param storyComments       Story comments count
     * @param storyShares         Story shares count
     */
    private void setAdditionalMetrics(PageLevelMetaData metaData, Integer linkClickCount, Integer otherClickCount,
                                      Integer publishedStoryCount, Integer storyImpressions, Integer storyEngagements,
                                      Integer storyReach, Integer storyLikes, Integer storyComments, Integer storyShares) {
        metaData.setLinkClickCount(linkClickCount);
        metaData.setOtherClickCount(otherClickCount);
        metaData.setFbStoryCount(publishedStoryCount);
        metaData.setStoryImpressions(storyImpressions);
        metaData.setStoryEngagements(storyEngagements);
        metaData.setStoryReach(storyReach);
        metaData.setStoryLikes(storyLikes);
        metaData.setStoryComments(storyComments);
        metaData.setStoryShares(storyShares);
    }

    private void persistPreviousPagePostInsightsData(BusinessFBPage fbPage, Map<Date, PageLevelMetaData> pageLevelMetaDataMap) {
        /* For previous date entries, the engagement breakdowns is resetting to null, bcz of overriding
            1. So fetch the previous records in page insights
            2. Match the date in pageLevelMetaDataMap, and update engagement breakdown entries
        */
        Date minKey = Collections.min(pageLevelMetaDataMap.keySet());
        List<PagePostInsightsData> dayWisePagePostInsightsData = prepareDayWisePageInsightDataFromEs(fbPage.getEnterpriseId(), InsightsConstants.FACEBOOK_PAGE_INSIGHTS, fbPage.getFacebookPageId(), minKey);
        pageLevelMetaDataMap.forEach((date, value) -> dayWisePagePostInsightsData.forEach(pagePostInsightsData -> {
            if(DateTimeUtils.isSameDay(date, pagePostInsightsData.getDate())){
                value.setCommentCount(pagePostInsightsData.getCommentCount());
                value.setShareCount(pagePostInsightsData.getShareCount());
                value.setLinkClickCount(pagePostInsightsData.getLinkClickCount());
                value.setOtherClickCount(pagePostInsightsData.getOtherClickCount());

                value.setTotalPostCommentCount(pagePostInsightsData.getTotalPagePostComments());
                value.setTotalPostShareCount(pagePostInsightsData.getTotalPagePostShares());
            }
        }));
    }

    private void calculatePagePostsEngagementBreakdown(BusinessFBPage fbPage, Map<Date,PageLevelMetaData> pageLevelMetaDataMap, Date maxKey) {
        /* Engagement breakdown summary
            1. pageLevelMetaDataMap contains data of previous dates, so we will update entry new data using maxKey
            2. Get previous like, comment, share, count from facebook_page_insight
            3. Get current like, comment, share, count from post_insight
            4. Calculate likeGain, commentGain, shareGain and store it in maxKey entry
         */
        // Step 2
        Date endDate = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        SimpleDateFormat dateFormatter = new SimpleDateFormat(DATE_FORMATTER_STRING);
        DataModel dataModel = DataModel.builder()
                .pageIds(fbPage.getFacebookPageId())
                .endDate("\""+dateFormatter.format(endDate)+"\"")
                .sourceIds(Integer.toString(SocialChannel.FACEBOOK.getId()))
                .build();

        InsightsESRequest insightsESRequest = new InsightsESRequest(dataModel, SearchTemplate.PAGE_INSIGHT_FOR_PREV_DATE);
        insightsESRequest.setIndex(ElasticConstants.FACEBOOK_PAGE_INSIGHTS.getName());
        insightsESRequest.setRouting(fbPage.getEnterpriseId().toString());
        List<PageInsightDataPoint> pageInsightDataPoints = reportsEsService.getPageInsightHitsDataFromEs(insightsESRequest);

        // Step 3
        DataModel engagementDataModel = DataModel.builder()
                .pageIds(fbPage.getFacebookPageId())
                .build();
        InsightsESRequest engagementRequest = new InsightsESRequest(engagementDataModel, SearchTemplate.SUM_POST_INSIGHTS_PAGE_ID);
        engagementRequest.setRouting(fbPage.getEnterpriseId().toString());
        engagementRequest.setIndex(ElasticConstants.POST_INSIGHTS.getName());
        PostInsightsDataPerPage postInsightsDataPerPage = reportsEsService.getSumPostInsightsOnPageId(engagementRequest);

        // Step 4
        calculatePageLevelInsightDelta(pageInsightDataPoints, postInsightsDataPerPage, pageLevelMetaDataMap.get(maxKey));
    }

    private void calculatePageLevelInsightDelta(List<PageInsightDataPoint> pageInsightDataPoints, PostInsightsDataPerPage postInsightsDataPerPage, PageLevelMetaData pageLevelMetaData) {
        if(CollectionUtils.isEmpty(pageInsightDataPoints)){
            log.info("Page Insights data is empty");
            return;
        }
        PagePostInsightsTotalData prevPagePostInsightsTotalData = convertPageInsightDataPoint(pageInsightDataPoints.get(0));
        PagePostInsightsGainData pagePostInsightsGainData = convertToPagePostInsightsGain(prevPagePostInsightsTotalData, postInsightsDataPerPage);
        // Set comment, share gain count (Like gain is fetched directly from FB)
        pageLevelMetaData.setCommentCount(pagePostInsightsGainData.getPostCommentCountGain());
        pageLevelMetaData.setShareCount(pagePostInsightsGainData.getPostShareCountGain());
        // Set total comment, share count
        pageLevelMetaData.setTotalPostCommentCount(postInsightsDataPerPage.getCommentCount());
        pageLevelMetaData.setTotalPostShareCount(postInsightsDataPerPage.getShareCount());
    }

    /**
     *
     * @param value - returned from facebook api
     * @param pageLevelMetaDataMap - map to get date based data
     * @return PageLevelMetaData : checks if the date is already present in map then return the value of given date if not then it will create new object
     */
    private PageLevelMetaData getPageLevelData(Value value,Map<Date,PageLevelMetaData> pageLevelMetaDataMap){
        SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT_STRING);
        try {
            Date valueDate =dateFormat.parse(value.getEndTime());
            valueDate =DateTimeUtils.addTimeInMinutes(valueDate,-24*60);
            PageLevelMetaData pageLevelMetaData = new PageLevelMetaData();
            if(Objects.isNull(pageLevelMetaDataMap.get(valueDate))){
                pageLevelMetaData.setDate(valueDate);
            }else {
                pageLevelMetaData = pageLevelMetaDataMap.get(valueDate);
            }
            return pageLevelMetaData;
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    private List<PageLevelMetaData> setPostCount(Map<String,Integer> postMapCount, Map<Date,PageLevelMetaData> pageLevelMetaDataMap){
        for(Map.Entry<Date,PageLevelMetaData> entry : pageLevelMetaDataMap.entrySet()){
            Date min = DateUtils.addDays(entry.getKey(),-1);
            Integer postCount = postMapCount.get(new SimpleDateFormat(DATE_WITH_DASH_STRING).format(min));
            if(Objects.nonNull(postCount)) {
                PageLevelMetaData pageLevelMetaData = pageLevelMetaDataMap.get(entry.getKey());
                pageLevelMetaData.setPostCount(postCount);
                pageLevelMetaDataMap.put(entry.getKey(),pageLevelMetaData);
            }
        }
        return new ArrayList<>(pageLevelMetaDataMap.values());
    }



    @Override
    public List<PageLevelMetaData> prepareDayWiseDataForInstagram(List<IgData> dataList, String instagramAccountId, Integer businessId) {
        Map<Date,PageLevelMetaData> pageLevelMetaDataMap = new HashMap<>();
        if(CollectionUtils.isEmpty(dataList) || Objects.isNull(dataList.get(0))) {
            return new ArrayList<>();
        }
        dataList.forEach(data -> {
            String name = data.getName();
            switch (name){
                case InsightsConstants.REACH:
                    data.getValues().forEach(value -> {
                        PageLevelMetaData pageLevelMetaData = getPageLevelData(value,pageLevelMetaDataMap);
                        pageLevelMetaData.setPostReach((Integer) value.getValue());
                        pageLevelMetaDataMap.put(pageLevelMetaData.getDate(), pageLevelMetaData);
                    });
                    break;
                case InsightsConstants.IMPRESSIONS:
                    data.getValues().forEach(value -> {
                        PageLevelMetaData pageLevelMetaData = getPageLevelData(value,pageLevelMetaDataMap);
                        pageLevelMetaData.setPostImpressions((Integer) value.getValue());
                        pageLevelMetaDataMap.put(pageLevelMetaData.getDate(), pageLevelMetaData);
                    });
                    break;
                case InsightsConstants.FOLLOWER_COUNT:
                    data.getValues().forEach(value -> {
                        PageLevelMetaData pageLevelMetaData = getPageLevelData(value,pageLevelMetaDataMap);
                        pageLevelMetaData.setFollowerGainCount((Integer) value.getValue());
                        pageLevelMetaDataMap.put(pageLevelMetaData.getDate(), pageLevelMetaData);
                    });
                    break;
            }
        });
        return new ArrayList<>(pageLevelMetaDataMap.values());
    }

    private Integer calculateEngagement(LinkedInStat stats) {
        return stats.getClickCount() + stats.getCommentCount() + stats.getLikeCount() + stats.getShareCount();
    }

    public List<PageLevelMetaData> prepareDayWiseDataForLinkedin(List<LinkedInElement> insights, List<LinkedInElement> follower, Date end) {
        Map<Date,PageLevelMetaData> pageLevelMetaDataMap = new HashMap<>();
        if(CollectionUtils.isEmpty(insights) || Objects.isNull(insights.get(0))) {
            return new ArrayList<>();
        }

        insights.forEach(data -> {
            LinkedInStat stats = data.getTotalShareStatistics();
            PageLevelMetaData pageLevelMetaData = setDateForPageLevelData(data, pageLevelMetaDataMap);
            pageLevelMetaData.setPostImpressions(stats.getImpressionCount());
            pageLevelMetaData.setTotalLikes(stats.getLikeCount());
            pageLevelMetaData.setPostEngagements(calculateEngagement(stats));
            pageLevelMetaData.setShareCount(stats.getShareCount());
            pageLevelMetaData.setPostEngagementRate(stats.getEngagement() * 100);
            pageLevelMetaData.setLinkClickCount(stats.getClickCount());
            pageLevelMetaData.setCommentCount(stats.getCommentCount());
            pageLevelMetaData.setPagePostLikeCount(stats.getLikeCount());
            pageLevelMetaDataMap.put(pageLevelMetaData.getDate(), pageLevelMetaData);
        });

        if(CollectionUtils.isNotEmpty(follower)) {
            follower.forEach(data -> {
                LinkedInFollower linkedInFollower = data.getFollowerGains();
                PageLevelMetaData pageLevelMetaData = setDateForPageLevelData(data, pageLevelMetaDataMap);
                Integer followerGain = linkedInFollower.getOrganicFollowerGain() + linkedInFollower.getPaidFollowerGain();
                pageLevelMetaData.setFollowerGainCount(followerGain>=0?followerGain:0);
                pageLevelMetaDataMap.put(pageLevelMetaData.getDate(), pageLevelMetaData);
            });
        }

        return new ArrayList<>(pageLevelMetaDataMap.values());
    }

    private PageLevelMetaData setDateForPageLevelData(LinkedInElement insight, Map<Date, PageLevelMetaData> pageLevelMetaDataMap) {
        PageLevelMetaData pageLevelMetaData = new PageLevelMetaData();
        Date date = new Date(insight.getTimeRange().getStart());
        if(Objects.isNull(pageLevelMetaDataMap.get(date))){
            pageLevelMetaData.setDate(date);
        }else {
            pageLevelMetaData = pageLevelMetaDataMap.get(date);
        }
        return pageLevelMetaData;
    }

    @Override
    public FacebookInsightRequest createFacebookRequest(BusinessFBPage fbPage, Date endDate, Date startDate, List<String> matrix) {
        FacebookInsightRequest facebookInsightRequest = new FacebookInsightRequest();
        facebookInsightRequest.setSince(startDate.toInstant().getEpochSecond());
        facebookInsightRequest.setUntil(endDate.toInstant().getEpochSecond());
        facebookInsightRequest.setAccessToken(fbPage.getPageAccessToken());
        facebookInsightRequest.setPageId(fbPage.getFacebookPageId());
        if(CollectionUtils.isNotEmpty(matrix))
            facebookInsightRequest.setMetric(StringUtils.join(matrix.toArray(),","));
        else
            facebookInsightRequest.setMetric(Metric.getListOfMetric());
        facebookInsightRequest.setPeriod(GroupByType.DAY.getType());
        return facebookInsightRequest;
    }

    @Override
    public InstagramInsightRequest createInstagramRequest(BusinessInstagramAccount igPage, Date endDate, Date startDate) {

        return InstagramInsightRequest.builder()
                .since(startDate.toInstant().getEpochSecond())
                .until(endDate.toInstant().getEpochSecond())
                .accessToken(igPage.getPageAccessToken())
                .pageId(igPage.getInstagramAccountId())
                .metric(InstagramPageInsightMetric.getListOfMetricAsString())
                .period(GroupByType.DAY.getType())
                .build();
    }

    @Override
    public InstagramPostInsightRequest createInstagramPostRequest(BusinessInstagramAccount igPage, String postId, Boolean isReel, String postUrl, Boolean isStory) {
        return InstagramPostInsightRequest.builder()
                .postId(postId)
                .metric(isStory ? InstagramPostInsightMetric.getStoryMetrics() :
                        isReel ? InstagramPostInsightMetric.getReelMetrics() : InstagramPostInsightMetric.getListOfMetric(postUrl))
                .accessToken(igPage.getPageAccessToken())
                .build();
    }

    @Override
    public PageInsightsResponse prepareFBPageInsightResponse(PageInsightEsData pageInsightEsData, SearchTemplate searchTemplate) {
        PageInsightData currentData = pageInsightEsData.getCurrentData();
        PageInsightData prevData = pageInsightEsData.getPrevData();
        log.info("current data : {} prev data : {}",currentData,prevData);
        PageInsightsResponse response = new PageInsightsResponse();
        switch (searchTemplate){
            case PAGE_LIKES_INSIGHTS:
                response.setLikeGainPer(getPercentage(currentData.getLikeGain(),prevData.getLikeGain(),currentData.getDocCount(),prevData.getDocCount()));
                response.setLikeLostPer(getPercentage(currentData.getLikeLost(),prevData.getLikeLost(),currentData.getDocCount(),prevData.getDocCount()));
                response.setLikeLost(currentData.getLikeLost());
                response.setLikeGain(currentData.getLikeGain());
                response.setNet(currentData.getLikeGain() - currentData.getLikeLost());
                break;
            case PAGE_FOLLOWER_INSIGHTS:
                response.setFollowerGainPer(getPercentage(currentData.getFollowerGain(),prevData.getFollowerGain(),currentData.getDocCount(),prevData.getDocCount()));
                response.setFollowerLostPer(getPercentage(currentData.getFollowerLost(),prevData.getFollowerLost(),currentData.getDocCount(),prevData.getDocCount()));
                response.setFollowerGain(currentData.getFollowerGain());
                response.setFollowerLost(currentData.getFollowerLost());
                response.setNet(currentData.getFollowerGain() - currentData.getFollowerLost());
                break;
            case PAGE_POST_METRIC:
                response.setEngagementPer(getPercentage(currentData.getEngagements(),prevData.getEngagements(),currentData.getDocCount(),prevData.getDocCount()));
                response.setReachPer(getPercentage(currentData.getReach(),prevData.getReach(),currentData.getDocCount(),prevData.getDocCount()));
                response.setImpressionPer(getPercentage(currentData.getImpressions(),prevData.getImpressions(),currentData.getDocCount(), prevData.getDocCount()));
                response.setEngagements(currentData.getEngagements());
                response.setReach(currentData.getReach());
                response.setImpressions(currentData.getImpressions());
                response.setLikeCount(currentData.getLikeCount());
                response.setCommentCount(currentData.getCommentCount());
                response.setShareCount(currentData.getShareCount());
                break;
            case PAGE_POST_ENGAGEMENT:
                Double curEngRate = reportsEsService.calculateEngagementRate(currentData.getEngagements(), currentData.getImpressions());
                Double prevEngRate = reportsEsService.calculateEngagementRate(prevData.getEngagements(), prevData.getImpressions());
                response.setEngRatePer(getEngPer(curEngRate,prevEngRate,currentData.getDocCount(),prevData.getDocCount()));
                response.setTotalPostPer(getPercentage(currentData.getTotalPost(),prevData.getTotalPost(),currentData.getDocCount(),prevData.getDocCount()));
                response.setTotalPost(currentData.getTotalPost());
                response.setEngRate(formatToTwoDecimalPlaces(curEngRate));
                break;
        }
        if(CollectionUtils.isNotEmpty(pageInsightEsData.getPointsList())) {
            pageInsightEsData.getPointsList().sort((m1, m2) -> {
                try {
                    Date first = new SimpleDateFormat(DATE_FOR_STRING).parse(m1.getLabel());
                    Date second = new SimpleDateFormat(DATE_FOR_STRING).parse(m2.getLabel());
                    return first.compareTo(second);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                return 0;
            });
        }
        response.setDataPoints(pageInsightEsData.getPointsList());
        return response;
    }

    @Override
    public PageInsightsV2Response prepareCommonInsightResponse(Map<String, PageInsightV2EsData> map, InsightsRequest insightsRequest, int bucketSize) throws ParseException {
        PageInsightsV2Response response = new PageInsightsV2Response();
        Map<String, ChannelWisePageInsightData> channelWiseDataMap = new HashMap<>();
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        Integer currEngagement = 0;
        Integer currImpression = 0;
        try {
            for (Map.Entry<String, PageInsightV2EsData> entry : map.entrySet()) {
                String channel = entry.getKey();
                PageInsightV2EsData pageInsightEsData = entry.getValue();
                PageInsightData currentData = pageInsightEsData.getCurrentData();
                PageInsightData prevData = pageInsightEsData.getPrevData();
                ChannelWisePageInsightData channelWisePageInsightData = new ChannelWisePageInsightData();
                log.info("current data : {} prev data : {}", currentData, prevData);
                switch (searchTemplate) {
                    case PAGE_FOLLOWER_INSIGHTS:
                        populateAudienceGrowthData(currentData, prevData, pageInsightEsData, response,
                                channelWisePageInsightData, channelWiseDataMap, channel);
                        break;
                    case PAGE_POST_METRIC:
                        Integer impression = Objects.nonNull(response.getImpressions()) ? response.getImpressions() : 0;
                        Integer reach = Objects.nonNull(response.getReach()) ? response.getReach() : 0;
                        if (Objects.nonNull(currentData)) {
                            Integer currentImpression = currentData.getImpressions();
                            Integer currentReach = currentData.getReach();
                            response.setImpressions(impression + currentImpression);
                            response.setReach(reach + currentReach);
                            channelWisePageInsightData.setImpressions(currentImpression);
                            channelWisePageInsightData.setReach(currentReach);
                        } else {
                            response.setImpressions(impression);
                            response.setReach(reach);
                            channelWisePageInsightData.setImpressions(0);
                            channelWisePageInsightData.setReach(0);
                        }
                        channelWiseDataMap.put(channel, channelWisePageInsightData);
                        break;
                    case PPR_PAGE_POST_ENG_METRIC:
                        Integer eng = Objects.nonNull(response.getEngagements()) ? response.getEngagements() : 0;
                        Integer likeCount = Objects.nonNull(response.getLikeCount()) ? response.getLikeCount() : 0;
                        Integer commentCount = Objects.nonNull(response.getCommentCount()) ? response.getCommentCount() : 0;
                        Integer shareCount = Objects.nonNull(response.getShareCount()) ? response.getShareCount() : 0;
                        Integer linkClickCount = Objects.nonNull(response.getLinkClickCount()) ? response.getLinkClickCount() : 0;
                        Integer otherClickCount = Objects.nonNull(response.getOtherClickCount()) ? response.getOtherClickCount() : 0;
                        if (Objects.nonNull(currentData)) {
                            Integer current = currentData.getEngagements() != null ? currentData.getEngagements() : 0;
                            Integer likes = currentData.getLikeCount() != null ? currentData.getLikeCount() : 0;
                            Integer comments = currentData.getCommentCount() != null ? currentData.getCommentCount() : 0;
                            Integer shares = currentData.getShareCount() != null ? currentData.getShareCount() : 0;
                            Integer linkClicks = currentData.getLinkClickCount() != null ? currentData.getLinkClickCount() : 0;
                            Integer otherClicks = currentData.getOtherClickCount() != null ? currentData.getOtherClickCount() : 0;
                            response.setEngagements(eng + current);
                            response.setLikeCount(likeCount + likes);
                            response.setCommentCount(commentCount + comments);
                            response.setShareCount(shareCount + shares);
                            response.setLinkClickCount(linkClickCount + linkClicks);
                            response.setOtherClickCount(otherClickCount + otherClicks);
                            channelWisePageInsightData.setEngagements(current);
                            channelWisePageInsightData.setLikeCount(likes);
                            channelWisePageInsightData.setCommentCount(comments);
                            channelWisePageInsightData.setShareCount(shares);
                            channelWisePageInsightData.setLinkClickCount(linkClicks);
                            channelWisePageInsightData.setOtherClickCount(otherClicks);
                        } else {
                            response.setEngagements(eng);
                            response.setLikeCount(likeCount);
                            response.setCommentCount(commentCount);
                            response.setShareCount(shareCount);
                            response.setLinkClickCount(linkClickCount);
                            response.setOtherClickCount(otherClickCount);
                            channelWisePageInsightData.setEngagements(0);
                            channelWisePageInsightData.setLikeCount(0);
                            channelWisePageInsightData.setCommentCount(0);
                            channelWisePageInsightData.setShareCount(0);
                            channelWisePageInsightData.setLinkClickCount(0);
                            channelWisePageInsightData.setOtherClickCount(0);
                        }
                        channelWiseDataMap.put(channel, channelWisePageInsightData);
                        break;
                    case PPR_PAGE_POST_ENG_RATE_METRIC:
                        double engRate = Objects.nonNull(response.getEngRate()) ? response.getEngRate() : 0;
                        if (Objects.nonNull(currentData) && Objects.nonNull(currentData.getEngagements()) && Objects.nonNull(currentData.getImpressions())) {
                            currEngagement += currentData.getEngagements();
                            currImpression += currentData.getImpressions();
                            Double current = reportsEsService.calculateEngagementRate(currentData.getEngagements(), currentData.getImpressions());
//                            response.setEngRate(formatToTwoDecimalPlaces(engRate + current));
                            channelWisePageInsightData.setEngRate(formatToTwoDecimalPlaces(current));
                        } else {
//                            response.setEngRate(engRate);
                            channelWisePageInsightData.setEngRate(0.0);
                        }
                        channelWiseDataMap.put(channel, channelWisePageInsightData);
                        break;
                    case PPR_PAGE_POST_PUBLISHED_METRIC:
                        Integer totalPost = Objects.nonNull(response.getPostCount()) ? response.getPostCount() : 0;
                        if (Objects.nonNull(currentData)) {
                            Integer current = 0;
                            if (SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)) {
                                current = currentData.getPostCount() != null ? currentData.getPostCount() : 0;
                            } else {
                                current = currentData.getDocCount() != null ? currentData.getDocCount() : 0;
                            }
                            response.setPostCount(totalPost + current);
                            channelWisePageInsightData.setPostCount(current);
                        } else {
                            response.setPostCount(totalPost);
                            channelWisePageInsightData.setPostCount(0);
                        }
                        channelWiseDataMap.put(channel, channelWisePageInsightData);

                        break;
                    case PPR_PAGE_VIDEO_VIEWS_METRIC:
                        Integer videoViews = Objects.nonNull(response.getVideoViews()) ? response.getVideoViews() : 0;
                        if (Objects.nonNull(currentData)) {
                            Integer currentVideoViews = Objects.nonNull(currentData.getVideoViews()) ? currentData.getVideoViews() : 0;
                            response.setVideoViews(videoViews + currentVideoViews);
                            channelWisePageInsightData.setVideoViews(currentVideoViews);
                            channelWiseDataMap.put(channel, channelWisePageInsightData);
                        } else {
                            response.setImpressions(videoViews);
                        }
                        break;
                    case PPR_PAGE_POST_MESSAGE_METRIC:

                        Integer totalPosts = Objects.nonNull(response.getPostCount()) ? response.getPostCount() : 0;
                        Integer current = 0;
                        if (Objects.nonNull(currentData)) {
                            if (SocialChannel.YOUTUBE.getName().equalsIgnoreCase(channel)) {
                                current = currentData.getPostCount() != null ? currentData.getPostCount() : 0;
                            } else {
                                current = currentData.getDocCount() != null ? currentData.getDocCount() : 0;
                            }
                            response.setPostCount(totalPosts + current);
                        }


                        Integer messageSent = Objects.nonNull(pageInsightEsData.getTotalMsgSent()) ? pageInsightEsData.getTotalMsgSent() : 0;
                        Integer messageReceived = Objects.nonNull(pageInsightEsData.getTotalMsgReceived()) ? pageInsightEsData.getTotalMsgReceived() : 0;
                        Integer currentMessageSent = Objects.nonNull(response.getSentMsgs()) ? response.getSentMsgs() : 0;
                        Integer currMessageReceived = Objects.nonNull(response.getReceivedMsgs()) ? response.getReceivedMsgs() : 0;

                        // channel level metric
                        channelWisePageInsightData.setSentMsgs(messageSent + current);
                        channelWisePageInsightData.setReceivedMsgs(messageReceived);

                        channelWiseDataMap.put(channel, channelWisePageInsightData);

                        break;
                    case FB_VIDEO_PERFORMANCE:
                        populateFbVideoPerformance(response, channelWisePageInsightData, currentData, channelWiseDataMap, channel);
                        break;
                    case FB_STORY_PERFORMANCE:
                        populateFbStoryPerformance(response, channelWisePageInsightData, currentData, channelWiseDataMap, channel);
                        break;
                    case  FB_POST_PUBLISHING_BEHAVIOUR:
                        populateFbPostPublishingBehaviour(response, channelWisePageInsightData, currentData, channelWiseDataMap, channel);
                        break;
                    default:
                        log.info("Profile report : Invalid search template {}", searchTemplate);
                        break;

                }
                response.setChannelWiseData(channelWiseDataMap);
                response.setDateDiff(Objects.nonNull(pageInsightEsData.getDateDiff()) ? pageInsightEsData.getDateDiff() : 0);
            }

            if (searchTemplate.equals(SearchTemplate.PPR_PAGE_POST_MESSAGE_METRIC)) {
                response.setReceivedMsgs(
                        channelWiseDataMap.values().stream()
                                .mapToInt(channelWisePageInsightData ->
                                        Objects.nonNull(channelWisePageInsightData.getReceivedMsgs()) ? channelWisePageInsightData.getReceivedMsgs() : 0
                                )
                                .sum() + (Objects.nonNull(response.getReceivedMsgs()) ? response.getReceivedMsgs() : 0)
                );

                response.setSentMsgs(
                        channelWiseDataMap.values().stream()
                                .mapToInt(channelWisePageInsightData ->
                                        Objects.nonNull(channelWisePageInsightData.getSentMsgs()) ? channelWisePageInsightData.getSentMsgs() : 0
                                )
                                .sum() + (Objects.nonNull(response.getSentMsgs()) ? response.getSentMsgs() : 0)
                );
            }

            GroupByType type = GroupByType.getByName(insightsRequest.getGroupByType());
            response.setGroupByType(type.getType());
            List<PageInsightV2DataPoint> allDataPoint = new ArrayList<>();
            // Special handling for FB_VIDEO_PERFORMANCE - create a single aggregated data point
            if (searchTemplate == SearchTemplate.FB_VIDEO_PERFORMANCE) {
                PageInsightV2DataPoint aggregatedDataPoint = new PageInsightV2DataPoint();
                aggregateVideoPerformanceData(response, aggregatedDataPoint);
                allDataPoint.add(aggregatedDataPoint);
            } else {
                allDataPoint = generateDataPointsForReports(map, searchTemplate, type, bucketSize);
            }
            //reportsEsService.updatePostCountForDates(request, pageInsightDataFromEs, pageIds, insights.getStartDate());
            if(searchTemplate.equals(SearchTemplate.PPR_PAGE_POST_ENG_RATE_METRIC)){
                response.setEngRate(reportsEsService.calculateEngagementRate(currEngagement, currImpression));
            }
            response.setDataPoints(allDataPoint);
            if (CollectionUtils.isNotEmpty(allDataPoint)) {
                customizedDataForFirstIndex(insightsRequest.getEndDate(), type, response, allDataPoint.get(0));
            }
            response.setDataPoints(allDataPoint);
        }catch (Exception e){
            log.info("Error in getting profile report response for request {} : exception {}", insightsRequest, e);
        }
        return response;
    }

    /**
     * Populates audience growth data in the response object based on current and previous data.
     *
     * @param currentData Current period data containing metrics
     * @param prevData Previous period data for comparison
     * @param pageInsightEsData ES data containing total audience information
     * @param response Response object to be populated
     * @param channelWisePageInsightData Channel-wise data to be populated
     * @param channelWiseDataMap Map of channel-wise data
     * @param channel Current channel being processed
     */
    private void populateAudienceGrowthData(PageInsightData currentData, PageInsightData prevData,
                                            PageInsightV2EsData pageInsightEsData,
                                            PageInsightsV2Response response,
                                            ChannelWisePageInsightData channelWisePageInsightData,
                                            Map<String, ChannelWisePageInsightData> channelWiseDataMap,
                                            String channel) {
        // Initialize response values if null
        Integer net = Objects.nonNull(response.getNet()) ? response.getNet() : 0;
        Integer followerLost = Objects.nonNull(response.getFollowerLost()) ? response.getFollowerLost() : 0;
        Integer followerGain = Objects.nonNull(response.getFollowerGain()) ? response.getFollowerGain() : 0;
        Integer totalFollowers = Objects.nonNull(response.getTotalFollowers()) ? response.getTotalFollowers() : 0;
        Integer totalAudience = Objects.nonNull(response.getTotalAudience()) ? response.getTotalAudience() : 0;
        Integer totalLikes = Objects.nonNull(response.getTotalLikes()) ? response.getTotalLikes() : 0;
        Integer likeGain = Objects.nonNull(response.getLikeGain()) ? response.getLikeGain() : 0;
        Integer likeLost = Objects.nonNull(response.getLikeLost()) ? response.getLikeLost() : 0;

        if (Objects.nonNull(currentData)) {
            // Calculate net follower growth
            Integer currentFollowerGain = Objects.nonNull(currentData.getFollowerGain()) ? currentData.getFollowerGain() : 0;
            Integer currentFollowerLost = Objects.nonNull(currentData.getFollowerLost()) ? currentData.getFollowerLost() : 0;
            Integer currentNet = currentFollowerGain - currentFollowerLost;

            // Calculate net page likes
            Integer currentLikeGain = Objects.nonNull(currentData.getLikeGain()) ? currentData.getLikeGain() : 0;
            Integer currentLikeLost = Objects.nonNull(currentData.getLikeLost()) ? currentData.getLikeLost() : 0;

            // Get total followers and likes
            Integer currentTotalFollowers = Objects.nonNull(currentData.getTotalFollowers()) ? currentData.getTotalFollowers() : 0;
            Integer currentTotalLikes = Objects.nonNull(currentData.getTotalLikes()) ? currentData.getTotalLikes() : 0;

            // Update response aggregates
            response.setNet(net + currentNet);
            response.setFollowerGain(followerGain + currentFollowerGain);
            response.setFollowerLost(followerLost + currentFollowerLost);
            response.setNetFollowerGrowth(response.getFollowerGain() - response.getFollowerLost());

            response.setLikeGain(likeGain + currentLikeGain);
            response.setLikeLost(likeLost + currentLikeLost);
            response.setNetPageLikes(response.getLikeGain() - response.getLikeLost());

            // Update total followers and likes
            response.setTotalFollowers(totalFollowers + currentTotalFollowers);
            response.setTotalLikes(totalLikes + currentTotalLikes);
            if (Objects.nonNull(pageInsightEsData.getTotal())) {
                response.setTotalAudience(totalAudience + pageInsightEsData.getTotal());
            } else {
                response.setTotalAudience(totalAudience);
            }

            // Populate channel-wise data
            channelWisePageInsightData.setNet(currentNet);
            channelWisePageInsightData.setFollowerLost(currentFollowerLost);
            channelWisePageInsightData.setTotalAudience(pageInsightEsData.getTotal() != null ? pageInsightEsData.getTotal() : 0);

        } else {
            // Set default values for channel-wise data if no current data
            channelWisePageInsightData.setTotalAudience(0);
            channelWisePageInsightData.setNet(0);
            channelWisePageInsightData.setFollowerLost(0);
        }

        // Add channel-wise data to the map
        channelWiseDataMap.put(channel, channelWisePageInsightData);
    }

    private void populateFbVideoPerformance(PageInsightsV2Response response, ChannelWisePageInsightData channelWisePageInsightData,
                                            PageInsightData currentData, Map<String, ChannelWisePageInsightData> channelWiseDataMap,
                                            String channel) {
        // --- Core Metrics (Total, Full, Partial) ---
        // Total Video Views
        Integer totalVideoViews = Objects.nonNull(response.getVideoViews()) ? response.getVideoViews() : 0;
        if (Objects.nonNull(currentData)) {
            Integer currentVideoViews = Objects.nonNull(currentData.getVideoViews()) ? currentData.getVideoViews() : 0;
            response.setTotalVideoViews(totalVideoViews + currentVideoViews);
            channelWisePageInsightData.setVideoViews(currentVideoViews);
        } else {
            response.setTotalVideoViews(totalVideoViews);
            channelWisePageInsightData.setVideoViews(0);
        }

        // Full Video Views (30s)
        Integer fullVideoViews = Objects.nonNull(response.getVideoCompleteViews30s()) ? response.getVideoCompleteViews30s() : 0;
        if (Objects.nonNull(currentData)) {
            Integer currentFullViews = Objects.nonNull(currentData.getVideoCompleteViews30s()) ? currentData.getVideoCompleteViews30s() : 0;
            response.setFullVideoViews(fullVideoViews + currentFullViews);
            channelWisePageInsightData.setVideoCompleteViews30s(currentFullViews);
        } else {
            response.setFullVideoViews(fullVideoViews);
            channelWisePageInsightData.setVideoCompleteViews30s(0);
        }

        // Partial Views (Total)
        response.setVideoPartialViews(Math.max(0, response.getTotalVideoViews() - response.getFullVideoViews()));
        channelWisePageInsightData.setVideoPartialViews(
                Math.max(0, channelWisePageInsightData.getVideoViews() - channelWisePageInsightData.getVideoCompleteViews30s())
        );

        // --- Click-to-Play Metrics ---
        Integer clickToPlayViews = Objects.nonNull(response.getVideoViewsClickToPlay()) ? response.getVideoViewsClickToPlay() : 0;
        Integer clickToPlayFullViews = Objects.nonNull(response.getVideoCompleteViews30sClickToPlay()) ? response.getVideoCompleteViews30sClickToPlay() : 0;
        Integer clickToPlayPartialViews = Math.max(0, clickToPlayViews - clickToPlayFullViews);

        if (Objects.nonNull(currentData)) {
            Integer currentClickToPlayViews = Objects.nonNull(currentData.getVideoViewsClickToPlay()) ? currentData.getVideoViewsClickToPlay() : 0;
            Integer currentClickToPlayFullViews = Objects.nonNull(currentData.getVideoCompleteViews30sClickToPlay()) ? currentData.getVideoCompleteViews30sClickToPlay() : 0;

            channelWisePageInsightData.setVideoViewsClickToPlay(currentClickToPlayViews);
            channelWisePageInsightData.setVideoCompleteViews30sClickToPlay(currentClickToPlayFullViews);
            channelWisePageInsightData.setVideoPartialViewsClickToPlay(Math.max(0, currentClickToPlayViews - currentClickToPlayFullViews));

            // Aggregate for breakdown (current + new)
            clickToPlayViews += currentClickToPlayViews;
            clickToPlayFullViews += currentClickToPlayFullViews;
            clickToPlayPartialViews = Math.max(0, clickToPlayViews - clickToPlayFullViews);
        } else {
            channelWisePageInsightData.setVideoViewsClickToPlay(0);
            channelWisePageInsightData.setVideoCompleteViews30sClickToPlay(0);
            channelWisePageInsightData.setVideoPartialViewsClickToPlay(0);
        }

        // --- Autoplay Metrics ---
        Integer autoplayViews = Objects.nonNull(response.getVideoViewsAutoplayed()) ? response.getVideoViewsAutoplayed() : 0;
        Integer autoplayFullViews = Objects.nonNull(response.getVideoCompleteViews30sAutoplayed()) ? response.getVideoCompleteViews30sAutoplayed() : 0;
        Integer autoplayPartialViews = Math.max(0, autoplayViews - autoplayFullViews);

        if (Objects.nonNull(currentData)) {
            Integer currentAutoplayViews = Objects.nonNull(currentData.getVideoViewsAutoplayed()) ? currentData.getVideoViewsAutoplayed() : 0;
            Integer currentAutoplayFullViews = Objects.nonNull(currentData.getVideoCompleteViews30sAutoplayed()) ? currentData.getVideoCompleteViews30sAutoplayed() : 0;

            channelWisePageInsightData.setVideoViewsAutoplayed(currentAutoplayViews);
            channelWisePageInsightData.setVideoCompleteViews30sAutoplayed(currentAutoplayFullViews);
            channelWisePageInsightData.setVideoPartialViewsAutoplayed(Math.max(0, currentAutoplayViews - currentAutoplayFullViews));

            // Aggregate for breakdown (current + new)
            autoplayViews += currentAutoplayViews;
            autoplayFullViews += currentAutoplayFullViews;
            autoplayPartialViews = Math.max(0, autoplayViews - autoplayFullViews);
        } else {
            channelWisePageInsightData.setVideoViewsAutoplayed(0);
            channelWisePageInsightData.setVideoCompleteViews30sAutoplayed(0);
            channelWisePageInsightData.setVideoPartialViewsAutoplayed(0);
        }

        // --- Set Breakdown (using AGGREGATED values) ---
        PageInsightsV2Response.VideoBreakdown breakdown = new PageInsightsV2Response.VideoBreakdown();

        // Click Plays Breakdown
        PageInsightsV2Response.VideoViewsData clickPlaysData = new PageInsightsV2Response.VideoViewsData();
        clickPlaysData.setFullVideoViews(clickToPlayFullViews);
        clickPlaysData.setPartialVideoViews(clickToPlayPartialViews);
        breakdown.setClickPlays(clickPlaysData);

        // Autoplay Breakdown
        PageInsightsV2Response.VideoViewsData autoPlaysData = new PageInsightsV2Response.VideoViewsData();
        autoPlaysData.setFullVideoViews(autoplayFullViews);
        autoPlaysData.setPartialVideoViews(autoplayPartialViews);
        breakdown.setAutoPlays(autoPlaysData);

        response.setBreakDown(breakdown);

        // Add channel-wise data
        channelWiseDataMap.put(channel, channelWisePageInsightData);
    }

    /**
     * Populates Facebook story performance metrics in the response
     *
     * @param response The response object to populate
     * @param channelWisePageInsightData Channel-wise data to update
     * @param currentData Current data from ES
     * @param channelWiseDataMap Map to store channel-wise data
     * @param channel The channel name
     */
    private void populateFbStoryPerformance(PageInsightsV2Response response, ChannelWisePageInsightData channelWisePageInsightData,
                                            PageInsightData currentData, Map<String, ChannelWisePageInsightData> channelWiseDataMap,
                                            String channel) {

        // --- Published Stories Count ---
        Integer publishedStories = Objects.nonNull(response.getPublishedStories()) ? response.getPublishedStories() : 0;
        if (Objects.nonNull(currentData)) {
            Integer currentPublishedStories = Objects.nonNull(currentData.getPublishedStories()) ? currentData.getPublishedStories() : 0;
            response.setPublishedStories(publishedStories + currentPublishedStories);
            channelWisePageInsightData.setPublishedStories(currentPublishedStories);
        } else {
            response.setPublishedStories(publishedStories);
            channelWisePageInsightData.setPublishedStories(0);
        }

        // --- Story Impressions ---
        Integer storyImpressions = Objects.nonNull(response.getStoryImpressions()) ? response.getStoryImpressions() : 0;
        if (Objects.nonNull(currentData)) {
            Integer currentStoryImpressions = Objects.nonNull(currentData.getStoryImpressions()) ? currentData.getStoryImpressions() : 0;
            response.setStoryImpressions(storyImpressions + currentStoryImpressions);
            channelWisePageInsightData.setStoryImpressions(currentStoryImpressions);
        } else {
            response.setStoryImpressions(storyImpressions);
            channelWisePageInsightData.setStoryImpressions(0);
        }

        // --- Story Reach ---
        Integer storyReach = Objects.nonNull(response.getStoryReach()) ? response.getStoryReach() : 0;
        if (Objects.nonNull(currentData)) {
            Integer currentStoryReach = Objects.nonNull(currentData.getStoryReach()) ? currentData.getStoryReach() : 0;
            response.setStoryReach(storyReach + currentStoryReach);
            channelWisePageInsightData.setStoryReach(currentStoryReach);
        } else {
            response.setStoryReach(storyReach);
            channelWisePageInsightData.setStoryReach(0);
        }

        // --- Story Engagement ---
        Integer storyEngagement = Objects.nonNull(response.getStoryEngagement()) ? response.getStoryEngagement() : 0;
        if (Objects.nonNull(currentData)) {
            Integer currentStoryEngagement = Objects.nonNull(currentData.getStoryEngagement()) ? currentData.getStoryEngagement() : 0;
            response.setStoryEngagement(storyEngagement + currentStoryEngagement);
            channelWisePageInsightData.setStoryEngagement(currentStoryEngagement);
        } else {
            response.setStoryEngagement(storyEngagement);
            channelWisePageInsightData.setStoryEngagement(0);
        }

        // --- Story Likes ---
        Integer storyLikes = Objects.nonNull(response.getStoryLike()) ? response.getStoryLike() : 0;
        if (Objects.nonNull(currentData)) {
            Integer currentStoryLikes = Objects.nonNull(currentData.getStoryLike()) ? currentData.getStoryLike() : 0;
            response.setStoryLike(storyLikes + currentStoryLikes);
            channelWisePageInsightData.setStoryLike(currentStoryLikes);
        } else {
            response.setStoryLike(storyLikes);
            channelWisePageInsightData.setStoryLike(0);
        }

        // --- Story Replies (Comments) ---
        Integer storyReplies = Objects.nonNull(response.getStoryReply()) ? response.getStoryReply() : 0;
        if (Objects.nonNull(currentData)) {
            Integer currentStoryComments = Objects.nonNull(currentData.getStoryReply()) ? currentData.getStoryReply() : 0;
            response.setStoryReply(storyReplies + currentStoryComments);
            channelWisePageInsightData.setStoryReply(currentStoryComments);
        } else {
            response.setStoryReply(storyReplies);
            channelWisePageInsightData.setStoryReply(0);
        }

        // --- Story Shares ---
        Integer storyShares = Objects.nonNull(response.getStoryShare()) ? response.getStoryShare() : 0;
        if (Objects.nonNull(currentData)) {
            Integer currentStoryShares = Objects.nonNull(currentData.getStoryShare()) ? currentData.getStoryShare() : 0;
            response.setStoryShare(storyShares + currentStoryShares);
            channelWisePageInsightData.setStoryShare(currentStoryShares);
        } else {
            response.setStoryShare(storyShares);
            channelWisePageInsightData.setStoryShare(0);
        }

        // --- Calculate Story Engagement Rate ---
        // Calculate engagement rate for the aggregated data
        if (response.getStoryImpressions() != null && response.getStoryImpressions() > 0) {
            double engRate = (double) response.getStoryEngagement() / response.getStoryImpressions() * 100;
            response.setStoryEngagementRate((int) Math.round(engRate));
        } else {
            response.setStoryEngagementRate(0);
        }

        // Calculate engagement rate for the channel-specific data
        if (channelWisePageInsightData.getStoryImpressions() != null && channelWisePageInsightData.getStoryImpressions() > 0) {
            double channelEngRate = (double) channelWisePageInsightData.getStoryEngagement() / channelWisePageInsightData.getStoryImpressions() * 100;
            channelWisePageInsightData.setStoryEngagementRate((int) Math.round(channelEngRate));
        } else {
            channelWisePageInsightData.setStoryEngagementRate(0);
        }

        // Add channel-wise data to the map
        channelWiseDataMap.put(channel, channelWisePageInsightData);
    }

    private void populateFbPostPublishingBehaviour(PageInsightsV2Response response, ChannelWisePageInsightData channelWisePageInsightData,
                                                   PageInsightData currentData, Map<String, ChannelWisePageInsightData> channelWiseDataMap,
                                                   String channel) {
        // Initialize post type counts in response if null
        Integer publishedTextPosts = Objects.nonNull(response.getPublishedTextPosts()) ? response.getPublishedTextPosts() : 0;
        Integer publishedImagePosts = Objects.nonNull(response.getPublishedImagePosts()) ? response.getPublishedImagePosts() : 0;
        Integer publishedVideoPosts = Objects.nonNull(response.getPublishedVideoPosts()) ? response.getPublishedVideoPosts() : 0;
        Integer publishedLinkPosts = Objects.nonNull(response.getPublishedLinkPosts()) ? response.getPublishedLinkPosts() : 0;

        if (Objects.nonNull(currentData)) {
            // Get post counts by type from current data
            Integer currentTextPosts = Objects.nonNull(currentData.getPublishedTextPosts()) ? currentData.getPublishedTextPosts() : 0;
            Integer currentImagePosts = Objects.nonNull(currentData.getPublishedImagePosts()) ? currentData.getPublishedImagePosts() : 0;
            Integer currentVideoPosts = Objects.nonNull(currentData.getPublishedVideoPosts()) ? currentData.getPublishedVideoPosts() : 0;
            Integer currentLinkPosts = Objects.nonNull(currentData.getPublishedLinkPosts()) ? currentData.getPublishedLinkPosts() : 0;

            // Update response aggregates
            response.setPublishedTextPosts(publishedTextPosts + currentTextPosts);
            response.setPublishedImagePosts(publishedImagePosts + currentImagePosts);
            response.setPublishedVideoPosts(publishedVideoPosts + currentVideoPosts);
            response.setPublishedLinkPosts(publishedLinkPosts + currentLinkPosts);
            response.setTotalPost(response.getPublishedTextPosts() + response.getPublishedImagePosts() +
                    response.getPublishedVideoPosts() + response.getPublishedLinkPosts());

            // Set channel-wise data
            channelWisePageInsightData.setPublishedTextPosts(currentTextPosts);
            channelWisePageInsightData.setPublishedImagePosts(currentImagePosts);
            channelWisePageInsightData.setPublishedVideoPosts(currentVideoPosts);
            channelWisePageInsightData.setPublishedLinkPosts(currentLinkPosts);
        } else {
            // Set default values for channel-wise data if no current data
            channelWisePageInsightData.setPublishedTextPosts(0);
            channelWisePageInsightData.setPublishedImagePosts(0);
            channelWisePageInsightData.setPublishedVideoPosts(0);
            channelWisePageInsightData.setPublishedLinkPosts(0);
        }

        // Add channel-wise data to the map
        channelWiseDataMap.put(channel, channelWisePageInsightData);
    }

    private Double getPercentage(Integer curData,Integer prevData,int currDocCount, int prevDocCount){
        DecimalFormat df = new DecimalFormat("#.#");
        if(prevDocCount == 0 || currDocCount == 0) {
            return 0.0;
        }
        return Double.valueOf(df.format(prevData != 0 && curData != 0 ? ((double)(curData - prevData)/ prevData)*100 : 0.0));
    }

    private Double getEngPer(Double curDate,Double prevDate,int currDocCount, int prevDocCount){
        log.info("current eng data : {} prev eng data : {}",curDate,prevDate);
        DecimalFormat df = new DecimalFormat("#.#");
        if(prevDocCount == 0 || currDocCount == 0){
            return 0.0;
        }
        return Double.valueOf(df.format(prevDate != 0  && curDate != 0 ? ((curDate - prevDate)/ prevDate)*100 : 0.0));

    }

    private Double formatToTwoDecimalPlaces(Double number) {
        if(number == null) number = 0d;
        return Double.valueOf(new DecimalFormat("#.#").format(number));
    }

    @Override
    public PageInsightData convertEsData(ParsedFilter bucket, SearchTemplate searchTemplate) {
        PageInsightData pageInsightData = new PageInsightData();
        ParsedSum s1,s2,s3,s4,s5,s6,s7,s8,s9;
        pageInsightData.setDocCount((int)bucket.getDocCount());
        switch (searchTemplate){
            // used for audience growth metrics
            case PAGE_FOLLOWER_INSIGHTS:
                s1 = bucket.getAggregations().get(FOLLOW_GAIN);
                pageInsightData.setFollowerGain((int) s1.getValue());
                s2 = bucket.getAggregations().get(FOLLOW_LOST);
                pageInsightData.setFollowerLost((int) s2.getValue());
                s3 = bucket.getAggregations().get(TOTAL_FOLLOWERS);
                pageInsightData.setTotalFollowers((int) s3.getValue());
                s4 = bucket.getAggregations().get(TOTAL_LIKES);
                pageInsightData.setTotalLikes((int) s4.getValue());
                s5 = bucket.getAggregations().get(LIKES_GAIN);
                pageInsightData.setLikeGain((int) s5.getValue());
                s6 = bucket.getAggregations().get(LIKES_LOST);
                pageInsightData.setLikeLost((int) s6.getValue());
                break;
            case PAGE_LIKES_INSIGHTS:
                s1 = bucket.getAggregations().get("like_gain");
                pageInsightData.setLikeGain((int) s1.getValue());
                s2 = bucket.getAggregations().get("like_lost");
                pageInsightData.setLikeLost((int) s2.getValue());
                break;
            case PAGE_POST_ENGAGEMENT:
                s2 = bucket.getAggregations().get(TOTAL_POST);
                pageInsightData.setPostCount((int) s2.getValue());
                s3 = bucket.getAggregations().get(POST_ENGAGEMENT);
                pageInsightData.setEngagements((int)s3.getValue());
                s4 = bucket.getAggregations().get(POST_REACH);
                pageInsightData.setReach((int)s4.getValue());
                s5 = bucket.getAggregations().get(POST_IMPRESSIONS);
                pageInsightData.setImpressions((int)s5.getValue());
                Double engRate = formatToTwoDecimalPlaces(reportsEsService.calculateEngagementRate((int)s3.getValue(), (int)s5.getValue()));
                pageInsightData.setEngRate(engRate);
                break;
            case PAGE_POST_METRIC:
                s1 = bucket.getAggregations().get(POST_ENGAGEMENT);
                pageInsightData.setEngagements((int)s1.getValue());
                s2 = bucket.getAggregations().get(POST_REACH);
                pageInsightData.setReach((int)s2.getValue());
                s3 = bucket.getAggregations().get(POST_IMPRESSIONS);
                pageInsightData.setImpressions((int)s3.getValue());
                s4 = bucket.getAggregations().get(POST_LIKE_COUNT);
                pageInsightData.setLikeCount((int)s4.getValue());
                s5 = bucket.getAggregations().get(POST_COMMENT_COUNT);
                pageInsightData.setCommentCount((int)s5.getValue());
                s6 = bucket.getAggregations().get(POST_SHARE_COUNT);
                pageInsightData.setShareCount((int)s6.getValue());
                break;
            case PPR_PAGE_POST_ENG_METRIC:
                s1 = bucket.getAggregations().get(POST_ENGAGEMENT);
                pageInsightData.setEngagements((int)s1.getValue());
                s2 = bucket.getAggregations().get(POST_LIKE_COUNT);
                pageInsightData.setLikeCount((int)s2.getValue());
                s3 = bucket.getAggregations().get(POST_COMMENT_COUNT);
                pageInsightData.setCommentCount((int)s3.getValue());
                s4 = bucket.getAggregations().get(POST_SHARE_COUNT);
                pageInsightData.setShareCount((int)s4.getValue());
                s5 = bucket.getAggregations().get(LINK_CLICK_COUNT);
                pageInsightData.setLinkClickCount((int)s5.getValue());
                s6 = bucket.getAggregations().get(OTHER_CLICK_COUNT);
                pageInsightData.setOtherClickCount((int)s6.getValue());
                break;
            case PPR_PAGE_POST_ENG_RATE_METRIC:
                s3 = bucket.getAggregations().get(POST_ENGAGEMENT);
                pageInsightData.setEngagements((int)s3.getValue());
                s5 = bucket.getAggregations().get(POST_IMPRESSIONS);
                pageInsightData.setImpressions((int)s5.getValue());
                Double engRate1 = formatToTwoDecimalPlaces(reportsEsService.calculateEngagementRate((int)s3.getValue(), (int)s5.getValue()));
                pageInsightData.setEngRate(engRate1);
                break;
            case PPR_PAGE_POST_PUBLISHED_METRIC:
            case PPR_PAGE_POST_PUBLISHED_METRIC_CHANNEL_INDEX:
                s2 = bucket.getAggregations().get(TOTAL_POST);
                pageInsightData.setPostCount((int) s2.getValue());
                break;
            case PPR_PAGE_VIDEO_VIEWS_METRIC:
                s3 = bucket.getAggregations().get(VIDEO_VIEWS);
                pageInsightData.setVideoViews((int)s3.getValue());
                break;
            case FB_VIDEO_PERFORMANCE:
                s1 = bucket.getAggregations().get("video_views");
                pageInsightData.setVideoViews(s1 != null ? (int) s1.getValue() : 0);

                s2 = bucket.getAggregations().get("video_complete_views_30s");
                pageInsightData.setVideoCompleteViews30s(s2 != null ? (int) s2.getValue() : 0);

                s3 = bucket.getAggregations().get("video_partial_views");
                pageInsightData.setVideoPartialViews(s3 != null ? (int) s3.getValue() : 0);

                s4 = bucket.getAggregations().get("video_views_click_to_play");
                pageInsightData.setVideoViewsClickToPlay(s4 != null ? (int) s4.getValue() : 0);

                s5 = bucket.getAggregations().get("video_complete_views_30s_click_to_play");
                pageInsightData.setVideoCompleteViews30sClickToPlay(s5 != null ? (int) s5.getValue() : 0);

                s6 = bucket.getAggregations().get("video_partial_views_click_to_play");
                pageInsightData.setVideoPartialViewsClickToPlay(s6 != null ? (int) s6.getValue() : 0);

                s7 = bucket.getAggregations().get("video_views_autoplay");
                pageInsightData.setVideoViewsAutoplayed(s7 != null ? (int) s7.getValue() : 0);

                s8 = bucket.getAggregations().get("video_complete_views_30s_autoplay");
                pageInsightData.setVideoCompleteViews30sAutoplayed(s8 != null ? (int) s8.getValue() : 0);

                s9 = bucket.getAggregations().get("video_partial_views_autoplay");
                pageInsightData.setVideoPartialViewsAutoplayed(s9 != null ? (int) s9.getValue() : 0);
                break;
            case FB_STORY_PERFORMANCE:
                s1 = bucket.getAggregations().get("published_story_count");
                pageInsightData.setPublishedStories(s1 != null ? (int) s1.getValue() : 0);

                s2 = bucket.getAggregations().get("story_impressions");
                pageInsightData.setStoryImpressions(s2 != null ? (int) s2.getValue() : 0);

                s3 = bucket.getAggregations().get("story_engagements");
                pageInsightData.setStoryEngagement(s3 != null ? (int) s3.getValue() : 0);

                s4 = bucket.getAggregations().get("story_reach");
                pageInsightData.setStoryReach(s4 != null ? (int) s4.getValue() : 0);

                s5 = bucket.getAggregations().get("story_likes");
                pageInsightData.setStoryLike(s5 != null ? (int) s5.getValue() : 0);

                s6 = bucket.getAggregations().get("story_comments");
                pageInsightData.setStoryReply(s6 != null ? (int) s6.getValue() : 0);

                s7 = bucket.getAggregations().get("story_shares");
                pageInsightData.setStoryShare(s7 != null ? (int) s7.getValue() : 0);
                break;
            case FB_POST_PUBLISHING_BEHAVIOUR:
                // Get aggregations for each post type with null checks
                ParsedFilter textPosts = bucket.getAggregations().get("text_posts");
                ParsedFilter videoPosts = bucket.getAggregations().get("video_posts");
                ParsedFilter imagePosts = bucket.getAggregations().get("image_posts");
                ParsedFilter linkPosts = bucket.getAggregations().get("link_posts");

                // Set values with null checks - using docCount for ParsedFilter
                int textPostCount = (textPosts != null) ? (int)textPosts.getDocCount() : 0;
                int videoPostCount = (videoPosts != null) ? (int)videoPosts.getDocCount() : 0;
                int imagePostCount = (imagePosts != null) ? (int)imagePosts.getDocCount() : 0;
                int linkPostCount = (linkPosts != null) ? (int)linkPosts.getDocCount() : 0;

                // Set individual post type counts
                pageInsightData.setPublishedTextPosts(textPostCount);
                pageInsightData.setPublishedVideoPosts(videoPostCount);
                pageInsightData.setPublishedImagePosts(imagePostCount);
                pageInsightData.setPublishedLinkPosts(linkPostCount);

                // Calculate and set total post count
                pageInsightData.setTotalPost(textPostCount + videoPostCount + imagePostCount + linkPostCount);
                break;
            default:
                log.info("Profile report : Invalid search template {}", searchTemplate);
                break;
        }

        return pageInsightData;
    }

    @Override
    public List<EsPostDataPoint> preparePostEsDTO(PostData postData) {
        List<EsPostDataPoint> esPostDataPoints = new ArrayList<>();
        SimpleDateFormat dateFormatter = new SimpleDateFormat(DATE_FORMATTER_STRING);
        double engRate;
        if(Objects.nonNull(postData.getEngagement()) && Objects.nonNull(postData.getImpression())) {
            engRate = postData.getImpression() != 0 ? (double) (postData.getEngagement() / postData.getImpression()) : 0d;
        } else {
            engRate = 0d;
        }
        List<EsPostTagDataPoint> esPostTagDataPoint = null;
//        if(CollectionUtils.isNotEmpty(postData.getTags())) {
//            esPostTagDataPoint = postData.getTags().stream().map(s->new EsPostTagDataPoint(s.getId(), s.getName()))
//                    .collect(Collectors.toList());
//        }
        Boolean isBePost = Objects.equals(postData.getSourceId(), SocialChannel.APPLE_CONNECT.getId()) ||
                Objects.nonNull(postData.getBePostId()) && !Objects.equals(postData.getPostId(), postData.getBePostId().toString());
        EsPostDataPoint esPostDataPoint = EsPostDataPoint.builder()
                .post_id(postData.getPostId())
                .posted_date(dateFormat(postData.getPostingDate(),dateFormatter))
                .post_end_date(dateFormat(postData.getPostEndDate(),dateFormatter))
                .engagement(Objects.isNull(postData.getEngagement())?0:postData.getEngagement())
                .business_id(postData.getBusinessId())
                .ent_id(postData.getEnterpriseId())
                .reach(Objects.isNull(postData.getReach())?0:postData.getReach())
                .impression(Objects.isNull(postData.getImpression())?0:postData.getImpression())
                .image_urls(postData.getImages())
                .video_urls(postData.getVideos())
                .page_id(postData.getPageId())
                .source_id(postData.getSourceId())
                .be_post_id(postData.getBePostId() != null ? postData.getBePostId() : postData.getPostId())
                .post_content(postData.getPostText())
                .day(dateFormatter.format(new Date()))
                .engagement_rate(engRate)
                .page_name(postData.getPageName())
                .like_count(postData.getLikeCount())
                .link_click_count(postData.getLinkClickCount())
                .other_click_count(postData.getOtherClickCount())
                .share_count(postData.getShareCount())
                .comment_count(postData.getCommentCount())
                .comment_mentions_count(postData.getCommentMentionsCount())
                .is_be_post(isBePost)
                .post_type(postData.getPostType())
                .tagIds(postData.getTagIds())
                .click_count(postData.getClickCount())
                .video_views(postData.getVideoViews())
                .publisher_id(postData.getPublisherId())
                .publisher_name(isBePost ? postData.getPublisherName() : EXTERNAL_USER)
                .publisher_email(postData.getPublisherEmail())
                // posts that contain one or more photos, but no videos.
                .image_url_count(CollectionUtils.isNotEmpty(postData.getImages()) && CollectionUtils.isEmpty(postData.getVideos()) ?
                        postData.getImages().size() : 0)
                // posts that contain one or more videos.
                .video_url_count(CollectionUtils.isNotEmpty(postData.getVideos()) ? postData.getVideos().size() : 0)
                // posts that contain a link, but no photo or video.
                .link_count(CollectionUtils.isEmpty(postData.getVideos()) && CollectionUtils.isEmpty(postData.getImages()) ?
                        postLinkCount(postData.getPostText()) : 0)
                // posts that contain only text.
                .post_text_count(postLinkCount(postData.getPostText()) == 0 && CollectionUtils.isEmpty(postData.getImages()) && CollectionUtils.isEmpty(postData.getVideos()) ?
                        postTypeTextOnly(postData.getPostText()) : 0)
                .post_url(postData.getPostUrl())
                .plays(postData.getPlays())
                .avg_minutes_viewed(postData.getAvg_minutes_viewed())
                .minutes_viewed(postData.getMinutes_viewed())
                .post_category(calculatePostCategory(postData))
                .build();
        esPostDataPoints.add(esPostDataPoint);
        return esPostDataPoints;
    }

    private String calculatePostCategory(PostData postData) {
        boolean hasVideos = CollectionUtils.isNotEmpty(postData.getVideos());
        boolean hasImages = CollectionUtils.isNotEmpty(postData.getImages());
        boolean hasLinks = postLinkCount(postData.getPostText()) > 0;
        boolean hasText = postTypeTextOnly(postData.getPostText()) > 0;

        // Priority order: Video > Image > Link > Text
        if (hasVideos) {
            return "video";
        } else if (hasImages) {
            return "image";
        } else if (hasLinks) {
            return "link";
        } else if (hasText) {
            return "text";
        } else {
            return "unknown";
        }
    }

    private Integer postLinkCount(String content) {
        if(StringUtils.isEmpty(content)) {
            return 0;
        }

        String regex = "\\b((http|https):\\/\\/)?([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,6}(\\/\\S*)?\\b";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        int count = 0;
        while (matcher.find()) {
            count++;
        }
        return count;
    }

    private Integer postTypeTextOnly(String text) {
        if (StringUtils.isEmpty(text)) {
            return 0;
        }

        // Regular expression for identifying URLs
        String urlRegex = "\\b((http|https):\\/\\/)?([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,6}(\\/\\S*)?\\b";
        Pattern urlPattern = Pattern.compile(urlRegex);
        Matcher urlMatcher = urlPattern.matcher(text);

        // Remove all URLs from the text
        String textWithoutUrls = urlMatcher.replaceAll("").trim();

        // Check if there are any non-whitespace characters left
        return textWithoutUrls.isEmpty() ? 0 : 1;
    }

    private String dateFormat(Date postingDate,SimpleDateFormat dateFormatter) {
        return Objects.isNull(postingDate) ? null : dateFormatter.format(postingDate);
    }

    @Override
    public String convertListToString(List<String> data) {
        return CollectionUtils.isNotEmpty(data) ? data.stream().map(Object::toString)
                .collect(Collectors.joining(", ")) : null;
    }

    /**
     *
     //     * @param value - returned from facebook api
     //     * @param pageLevelMetaDataMap - map to get date based data
     //     * @param pageId - page id for facebook page
     //     * @param businessId - business id associated with page
     * @return PageLevelMetaData : checks if the date is already present in map then return the value of given date if not then it will create new object
     */
//    private PageLevelMetaData getPageLevelData(Value value,Map<Date,PageLevelMetaData> pageLevelMetaDataMap,String pageId,Integer businessId){
//        SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT_STRING);
//        PageLevelMetaData pageLevelMetaData = new PageLevelMetaData();
//        try {
//            if(Objects.isNull(pageLevelMetaDataMap.get(dateFormat.parse(value.getEndTime())))){
//                pageLevelMetaData.setDate(dateFormat.parse(value.getEndTime()));
//            }else {
//                pageLevelMetaData = pageLevelMetaDataMap.get(dateFormat.parse(value.getEndTime()));
//            }
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
//        return pageLevelMetaData;
//    }

    @Override
    public PostData convertBusinessPostDataToPostData(BusinessPosts businessPosts) {
        PostData postData = new PostData();
        postData.setId(businessPosts.getId());
        postData.setBePostId(Objects.nonNull(businessPosts.getBePostId())?businessPosts.getBePostId().toString()
                :null);
        postData.setPostId(businessPosts.getPostId());
        postData.setSourceId(businessPosts.getSourceId());
        postData.setPageName(businessPosts.getPageName());
        postData.setPageId(businessPosts.getExternalPageId());
        postData.setBusinessId(businessPosts.getBusinessId());
        postData.setEnterpriseId(businessPosts.getEnterpriseId());
        postData.setPostUrl(businessPosts.getPostUrl());
        postData.setPostText(businessPosts.getPostText());
        postData.setPostingDate(businessPosts.getPublishDate());
        postData.setImages(convertStringToList(businessPosts.getImageUrls()));
        postData.setVideos(convertStringToList(businessPosts.getVideoUrls()));
        postData.setEngagement(0);
        postData.setImpression(0);
        postData.setReach(0);
        if(Objects.nonNull(postData.getEngagement()) && Objects.nonNull(postData.getReach()) && postData.getReach()!=0) {
            postData.setEngagementRate((double)postData.getEngagement()/postData.getReach());
        }
        return postData;
    }

    @Override
    public ESPageRequest createNewObject(NewFbPostData newFbPostData) {
        ESPageRequest esPageRequest = new ESPageRequest();
        esPageRequest.setPage_id(newFbPostData.getPageId());
        esPageRequest.setBusiness_id(newFbPostData.getBusinessId());
        esPageRequest.setDay(new SimpleDateFormat(DATE_FORMATTER_STRING).format(newFbPostData.getPublishDate()));
        esPageRequest.setPost_total_count(1);
        esPageRequest.setEnt_id(newFbPostData.getEnterpriseId());
        return esPageRequest;
    }

    @Override
    public void setGroupByType(InsightsRequest insights,Long noOfDaysBetween) {
        if(Objects.nonNull(insights.getGroupByType()) && StringUtils.isNotEmpty(insights.getGroupByType())){
            return;
        }
        GroupByType groupBy = GroupByType.fromString(insights.getGroupByType());
        groupBy = GroupByType.validateGroupingType(groupBy, noOfDaysBetween);
        GroupByType groupByType = GroupByType.getByType(groupBy.getType());
        Optional<GroupingType> optionalGrouping = GroupingType.getByName(groupByType.getType());
        if (!optionalGrouping.isPresent()) {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_GROUPING_TYPE);
        }
        insights.setGroupByType(optionalGrouping.get().getName());
    }

    @Override
    public void setGroupByType(TrendsReportRequest insights, Long noOfDaysBetween) {
        if(Objects.nonNull(insights.getGroupByType()) && StringUtils.isNotEmpty(insights.getGroupByType())){
            return;
        }
        GroupByType groupBy = GroupByType.fromString(insights.getGroupByType());
        groupBy = GroupByType.validateGroupingType(groupBy, noOfDaysBetween);
        GroupByType groupByType = GroupByType.getByType(groupBy.getType());
        Optional<GroupingType> optionalGrouping = GroupingType.getByName(groupByType.getType());
        if (!optionalGrouping.isPresent()) {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_GROUPING_TYPE);
        }
        insights.setGroupByType(optionalGrouping.get().getName());
    }

    private List<String> convertStringToList(String url) {
        return StringUtils.isNotEmpty(url) ? Arrays.asList(url.split("\\s*,\\s*")) : new ArrayList<>();
    }


    @Override
    public void customizedDataForFirstIndex(Date endDate, GroupByType type, PageInsightsResponse pageInsightsResponse, List<PageInsightDataPoint> dataPoints) throws Exception {
        Date startDate;
        SimpleDateFormat dateFormatter = new SimpleDateFormat("MM/dd/yyyy");
        PageInsightDataPoint pageInsightDataPoint = dataPoints.get(0);
        startDate = dateFormatter.parse(pageInsightDataPoint.getLabel());
        InsightsReportUtil.customizeLabelsOld(pageInsightsResponse.getDataPoints(), startDate, endDate, type, false);
        PageInsightDataPoint dataPoint = new PageInsightDataPoint();
        dataPoint.setLabel(InsightsConstants.TOTAL);
        dataPoint.setFollowersGain(pageInsightsResponse.getFollowerGain());
        dataPoint.setFollowersLost(pageInsightsResponse.getFollowerLost());
        dataPoint.setLikesLost(pageInsightsResponse.getLikeLost());
        dataPoint.setLikesGain(pageInsightsResponse.getLikeGain());
        dataPoint.setPostEngagements(pageInsightsResponse.getEngagements());
        dataPoint.setPostEngRate(pageInsightsResponse.getEngRate());
        dataPoint.setPostImpressions(pageInsightsResponse.getImpressions());
        dataPoint.setPostReach(pageInsightsResponse.getReach());
        dataPoint.setTotalPosts(pageInsightsResponse.getPostCount());
        dataPoint.setNet(calculateNetForTotal(pageInsightsResponse));
        dataPoint.setVideoViews(pageInsightsResponse.getVideoViews());
        dataPoint.setLikeCount(pageInsightsResponse.getLikeCount());
        dataPoint.setCommentCount(pageInsightsResponse.getCommentCount());
        dataPoint.setShareCount(pageInsightsResponse.getShareCount());
        dataPoints.add(0, dataPoint);
    }
    public void customizedDataForFirstIndex(Date endDate, GroupByType type, PageInsightsV2Response pageInsightsResponse, PageInsightV2DataPoint dataPoint) throws Exception {
        if(Objects.nonNull(dataPoint.getLabel())) {
            SimpleDateFormat dateFormatter = new SimpleDateFormat("MM/dd/yyyy");
            try {
                Date startDate = dateFormatter.parse(dataPoint.getLabel());
                InsightsReportUtil.customizeLabelsOld(pageInsightsResponse.getDataPoints(), startDate, endDate, type, false);
            } catch(ParseException ex) {
                log.warn("ReportDataConverterImpl#customizedDataForFirstIndex() Error in parsing date field for Business Id {} , exception : {}",pageInsightsResponse.getBusinessId(),ex.getMessage());
            }
        }
    }

    @Override
    public void customizedDataForFirstIndex(Date endDate, GroupByType type, SocialTrendsReportResponse trendsResponse,
                                            List<ReportTrendsDataPoint> dataPoints) throws Exception {
        Date startDate;
        SimpleDateFormat dateFormatter = new SimpleDateFormat("MM/dd/yyyy");
        ReportTrendsDataPoint trendsDataPoint = dataPoints.get(0);
        startDate = dateFormatter.parse(trendsDataPoint.getLabel());
        TrendsReportUtil.customizeLabelsOld(trendsResponse.getDataPoints(), startDate, endDate, type, false);
    }

    @Override
    public PageInsightsResponse prepareTiktokPageInsightResponse(List<ESPageRequest> esData, InsightsRequest insightsRequest ) {
        PageInsightsResponse pageInsightsResponse = new PageInsightsResponse();
        List<PageInsightDataPoint> pageInsightDataPoints;
        switch (insightsRequest.getReportType()) {
            case "gender_based_insights" : {
                PageInsightDataPoint pageInsightDataPoint = new PageInsightDataPoint();
                DemographicsInsightDataPoint.Data genderData = populateGenderData(esData, pageInsightDataPoint);
                if(Objects.nonNull(genderData)) {
                    pageInsightDataPoint.setCountByGender(genderData);
                    pageInsightsResponse.setDataPoints(Collections.singletonList(pageInsightDataPoint));
                    pageInsightsResponse.setTotal(pageInsightDataPoint.getTotalFollowers());
                } else {
                    log.info("gender data is null from populateGenderData");
                    pageInsightsResponse.setDataPoints(new ArrayList<>());
                }
                return pageInsightsResponse;
            }
            case "country_based_insights" : {
                pageInsightDataPoints = populateCountriesData(esData, insightsRequest);
                pageInsightsResponse.setDataPoints(pageInsightDataPoints);
                return pageInsightsResponse;
            }
            case "city_based_insights" : {
                pageInsightDataPoints = populateCitiesData(esData, insightsRequest);
                pageInsightsResponse.setDataPoints(pageInsightDataPoints);
                return pageInsightsResponse;
            }
        }
        return null;
    }

    private List<PageInsightDataPoint> populateCountriesData(List<ESPageRequest> esPageRequests, InsightsRequest insightsRequest) {
        if(CollectionUtils.isNotEmpty(esPageRequests)) {
            List<PageInsightDataPoint> pageInsightDataPoints = new ArrayList<>();
            PageInsightDataPoint dataPoint;
            int i = 0;
            for(ESPageRequest esData : esPageRequests) {
                if (CollectionUtils.isNotEmpty(esData.getAudience_countries())) {
                    for(DemographicsInsightDataPoint.Data contryData : esData.getAudience_countries()) {
                        if(!contryData.getLabel().equalsIgnoreCase("Others")) {
                            dataPoint = new PageInsightDataPoint();
                            dataPoint.setLabel(contryData.getLabel());
                            dataPoint.setTotal(contryData.getTotal());
                            dataPoint.setPercentage(contryData.getPercentage());
                            pageInsightDataPoints.add(dataPoint);
                            i++;
                            if(Objects.nonNull(insightsRequest.getSize()) && i == insightsRequest.getSize())
                                return sortData(insightsRequest, pageInsightDataPoints);
                        }
                    }
                }
            }
            return sortData(insightsRequest, pageInsightDataPoints);
        }
        return null;
    }

    private List<PageInsightDataPoint> sortData(InsightsRequest insightsRequest, List<PageInsightDataPoint> pageInsightDataPoints) {
        Comparator<PageInsightDataPoint> comparator = Comparator.comparing(PageInsightDataPoint::getTotal);
        if(insightsRequest.getSortBy().equalsIgnoreCase("countryName")
                || insightsRequest.getSortBy().equalsIgnoreCase("cityName")) {
            comparator = Comparator.comparing(PageInsightDataPoint::getLabel);
        }
        if (Objects.equals(insightsRequest.getSortingOrder(), 1)) {
            comparator = comparator.reversed();
        }
        pageInsightDataPoints = pageInsightDataPoints.stream()
                .sorted(comparator)
                .collect(Collectors.toList());
        return pageInsightDataPoints;
    }

    private List<PageInsightDataPoint> populateVideoViews( PageInsightEsData pageInsightDataFromEs ) {
        if(Objects.nonNull(pageInsightDataFromEs) && CollectionUtils.isNotEmpty(pageInsightDataFromEs.getPointsList())) {
            List<PageInsightDataPoint> pageInsightDataPoints = new ArrayList<>();
//            PageInsightDataPoint dataPoint;
//            for(ESPageRequest esData : esPageRequests) {
//                if (CollectionUtils.isNotEmpty(esData.getAudience_countries())) {
//                    for(DemographicsInsightDataPoint.Data cityData : esData.getAudience_cities()) {
//                        dataPoint = new PageInsightDataPoint();
//                        dataPoint.setLabel(cityData.getLabel());
//                        dataPoint.setTotal(cityData.getTotal());
//                        dataPoint.setPercentage(cityData.getPercentage());
//                        pageInsightDataPoints.add(dataPoint);
//                    }
//                }
//            }
//            return sortData(insightsRequest, pageInsightDataPoints);
        }
        return null;
    }

    private List<PageInsightDataPoint> populateCitiesData(List<ESPageRequest> esPageRequests, InsightsRequest insightsRequest) {
        if(CollectionUtils.isNotEmpty(esPageRequests)) {
            List<PageInsightDataPoint> pageInsightDataPoints = new ArrayList<>();
            PageInsightDataPoint dataPoint;
            for(ESPageRequest esData : esPageRequests) {
                if (CollectionUtils.isNotEmpty(esData.getAudience_countries())) {
                    for(DemographicsInsightDataPoint.Data cityData : esData.getAudience_cities()) {
                        if(StringUtils.isNotEmpty(cityData.getLabel())
                                && !cityData.getLabel().toLowerCase().contains("other")) {
                            dataPoint = new PageInsightDataPoint();
                            dataPoint.setLabel(cityData.getLabel());
                            dataPoint.setTotal(cityData.getTotal());
                            dataPoint.setPercentage(cityData.getPercentage());
                            pageInsightDataPoints.add(dataPoint);
                        }
                    }
                }
            }
            return sortData(insightsRequest, pageInsightDataPoints);
        }
        return null;
    }

    private  DemographicsInsightDataPoint.Data populateGenderData(List<ESPageRequest> esPageRequests,
                                                                  PageInsightDataPoint pageInsightDataPoint) {
        if(CollectionUtils.isNotEmpty(esPageRequests)) {
            DemographicsInsightDataPoint.Data data = new DemographicsInsightDataPoint.Data(0,0,0);
            Integer totalFollowers = 0;
            for(ESPageRequest esData : esPageRequests) {
                if (CollectionUtils.isNotEmpty(esData.getAudience_genders())) {
                    for(DemographicsInsightDataPoint.Data genderData : esData.getAudience_genders()) {
                        totalFollowers += genderData.getTotal();
                        switch (genderData.getLabel().toLowerCase()) {
                            case "male" : {
                                data.setMen(data.getMen() + genderData.getTotal());
                                break;
                            }
                            case "female" : {
                                data.setWomen(data.getWomen() + genderData.getTotal());
                                break;
                            }
                            default : {
                                data.setOther(data.getOther() + genderData.getTotal());
                                break;
                            }
                        }
                    }
                } else {
                    log.info("Aud gender is empty");
                }
            }
            if(Objects.equals(totalFollowers,0)) {
                log.info("Total Followers are zero");
                return null;
            }
            pageInsightDataPoint.setTotalFollowers(totalFollowers);
            return data;
        }
        return null;
    }

    private Double roundToTwoDecimalPlaces(Double value) {
        if(value == null) return null;
        return Math.round(value * 10.00) / 10.00;
    }

    private Integer calculateNetForTotal(PageInsightsResponse pageInsightsResponse) {
        int net = 0;
        if(Objects.isNull(pageInsightsResponse)){
            return 0;
        }
        if(Objects.nonNull(pageInsightsResponse.getFollowerGain()) && Objects.nonNull(pageInsightsResponse.getFollowerLost())){
            net = pageInsightsResponse.getFollowerGain() - pageInsightsResponse.getFollowerLost();
        }
        if(Objects.nonNull(pageInsightsResponse.getLikeGain()) && Objects.nonNull(pageInsightsResponse.getLikeLost())){
            net = pageInsightsResponse.getLikeGain() - pageInsightsResponse.getLikeLost();
        }
        return net;
    }


    @Override
    public GMBReportInsightsRequest createGmbLocationRequest(String location, String startDate, String endDate ) {
        GMBReportInsightsRequest reportInsightsRequest = new GMBReportInsightsRequest();
        List<String> metricOptions = new ArrayList<>();
        List<String> metric = new ArrayList<>();

        metricOptions.add(MetricOptionEnum.AGGREGATED_DAILY.name());
        metric.add(MetricEnum.LOCAL_POST_VIEWS_SEARCH.name()); // The total no. of times all the posts were seen by the network users.
        metric.add(MetricEnum.VIEWS_SEARCH.name()); // The total no. of times the GBP has shown in search results

        // The search query that lead to the GBP showing as a result. i.e reach
        metric.add(MetricEnum.QUERIES_INDIRECT.name());
        metric.add(MetricEnum.QUERIES_DIRECT.name());
        metric.add(MetricEnum.QUERIES_CHAIN.name());

        // The total no. of interactions users made with a post made by the business on the network.
        metric.add(MetricEnum.LOCAL_POST_ACTIONS_CALL_TO_ACTION.name());



        reportInsightsRequest.setStartDate(startDate);
        reportInsightsRequest.setEndDate(endDate);
        reportInsightsRequest.setMetric(metric);
        reportInsightsRequest.setMetricOption(metricOptions);
        return reportInsightsRequest;

    }

    @Override
    public GMBReportInsightsRequest createGmbPostRequest(String location, String startDate, String endDate ) {
        GMBReportInsightsRequest reportInsightsRequest = new GMBReportInsightsRequest();
        List<String> metricOptions = new ArrayList<>();
        List<String> metric = new ArrayList<>();


        metricOptions.add(MetricOptionEnum.AGGREGATED_TOTAL.name()); // getting aggregation total
        metric.add(MetricEnum.LOCAL_POST_VIEWS_SEARCH.name()); // No. of times a post was seen


        // No. of interactions made on the post
        metric.add(MetricEnum.LOCAL_POST_ACTIONS_CALL_TO_ACTION.name());

        reportInsightsRequest.setStartDate(startDate);
        reportInsightsRequest.setEndDate(endDate);
        reportInsightsRequest.setMetric(metric);
        reportInsightsRequest.setMetricOption(metricOptions);
        return reportInsightsRequest;

    }

    @Override
    public List<PageLevelMetaData> prepareDayWiseDataForGmb(GMBReportInsightsResponse gmbReportInsightsResponse) {
        List<PageLevelMetaData> pageLevelMetaData = new ArrayList<>();
        if(Objects.isNull(gmbReportInsightsResponse) || CollectionUtils.isEmpty(gmbReportInsightsResponse.getLocationMetrices())) {
            return pageLevelMetaData;
        }
        for (MetricsResponse metricsResponse : gmbReportInsightsResponse.getLocationMetrices().get(0).getMetrices()) {
            PageLevelMetaData pageLevelData = new PageLevelMetaData();
            try {
                pageLevelData.setDate(new SimpleDateFormat(DATE_WITH_DASH_STRING).parse(metricsResponse.getDate()));
                pageLevelData.setPostImpressions(metricsResponse.getLocalPostViews());
                pageLevelData.setPostEngagements(metricsResponse.getLocalPostCta());
                Integer reach = Objects.isNull(metricsResponse.getQueriesChain()) ? 0  : metricsResponse.getQueriesChain()
                        + (Objects.isNull(metricsResponse.getQueriesDirect()) ? 0 : metricsResponse.getQueriesDirect())
                        + (Objects.isNull(metricsResponse.getQueriesIndirect()) ? 0 : metricsResponse.getQueriesIndirect());
                pageLevelData.setPostReach(reach);

                // Impressions (search result), Search queries, Total views (GBP) to be added
                pageLevelMetaData.add(pageLevelData);
            } catch (ParseException e) {
                // add log
            }
        }
        return pageLevelMetaData;
    }
    public Map<String, Integer> convertMapOfPostCountAndDate(List<Data> postData) {
        Map<String,Integer> dateIntegerMap = new LinkedHashMap<>();
        postData.forEach(post -> {
            try {
                Date createdTime = DateUtils.truncate(new SimpleDateFormat(DATE_FORMAT_STRING).parse(post.getCreatedTime()), Calendar.DATE);
                String finalDate = new SimpleDateFormat(DATE_WITH_DASH_STRING).format(createdTime);
                if(dateIntegerMap.containsKey(finalDate)){
                    dateIntegerMap.put(finalDate,dateIntegerMap.get(finalDate)+1);
                }else{
                    dateIntegerMap.put(finalDate,1);
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
        });
        return dateIntegerMap;
    }


    @Override
    public Date getStartDate(InsightsRequest filter, List<Integer> businessIds,String lastDate) throws Exception {
        Date startDate = filter.getStartDate();
        Calendar oldestMetricCalender = Calendar.getInstance();
        TimeZoneUtil.convertToSpecificTimeZone(oldestMetricCalender,"UTC");
        try {
            String oldestMetricDateAsString = "";
            Date oldestMetricDate = null;
            oldestMetricDateAsString = lastDate;
            if (Objects.nonNull(oldestMetricDateAsString) && !oldestMetricDateAsString.isEmpty()) {
                oldestMetricCalender.setTime(new SimpleDateFormat("yyyy-MM-dd").parse(oldestMetricDateAsString));
                oldestMetricDate = oldestMetricCalender.getTime();
            }
            if(Objects.isNull(startDate) || (startDate.before(oldestMetricDate) && StringUtils.isEmpty(filter.getCustomStartDate()))
                    &&  filter.getEndDate().after(oldestMetricDate)) {
                startDate = oldestMetricDate;
            }
            log.info("[getStartDate] startDate is {}", startDate);
        } catch (Exception error) {
            log.error("[getStartDate] , Exception: {}", error);
            throw error;
        }
        return startDate;
    }

    @Override
    public List<String> getAnalyticsMetricList() {
        List<String> metricEnums = new ArrayList<>(Arrays.asList(MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_MAPS.name(),
                MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_MAPS.name(),
                MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_SEARCH.name(),
                MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_SEARCH.name(), MetricEnum.WEBSITE_CLICKS.name(),
                MetricEnum.CALL_CLICKS.name(), MetricEnum.BUSINESS_DIRECTION_REQUESTS.name()));
        return metricEnums;
    }

    @Override
    public List<PageLevelMetaData> prepareDayWiseEngDataForLinkedin(List<LinkedInElement> insights) {
        Map<Date,PageLevelMetaData> pageLevelMetaDataMap = new HashMap<>();
        if(CollectionUtils.isEmpty(insights) || Objects.isNull(insights.get(0))) {
            return new ArrayList<>();
        }

        insights.forEach(data -> {
            LinkedInStat stats = data.getTotalShareStatistics();
            PageLevelMetaData pageLevelMetaData = setDateForPageLevelData(data, pageLevelMetaDataMap);
            pageLevelMetaData.setPostEngagements(calculateEngagement(stats));
            pageLevelMetaDataMap.put(pageLevelMetaData.getDate(), pageLevelMetaData);
        });

        return new ArrayList<>(pageLevelMetaDataMap.values());
    }

    @Override
    public  Map<String, PageLevelMetaData> prepareDayWiseDataForYoutubeReport(YoutubeAnalyticResponse dataList) {
        Map<String, PageLevelMetaData> pageLevelMetaDataList = new HashMap<>();
        if(Objects.isNull(dataList) || CollectionUtils.isEmpty(dataList.getRows())) {
            return null;
        }

        List<String> metrics = dataList.getColumnHeaders().stream().map(YoutubeAnalyticsMetricsResponse::getName).collect(Collectors.toList());
        int day = metrics.indexOf("day");

        int subGained = metrics.indexOf(YoutubeReportMetric.subscribersGained.name());
        int subLost = metrics.indexOf(YoutubeReportMetric.subscribersLost.name());
        int views = metrics.indexOf(YoutubeReportMetric.views.name());
        int shares = metrics.indexOf(YoutubeReportMetric.shares.name());
        int likes = metrics.indexOf(YoutubeReportMetric.likes.name());
//        int dislikes = metrics.indexOf(YoutubeReportMetric.dislikes.name());
        int comments = metrics.indexOf(YoutubeReportMetric.comments.name());
//        int annotationClicks = metrics.indexOf(YoutubeReportMetric.annotationClicks.name());
//        int cardClicks = metrics.indexOf(YoutubeReportMetric.cardClicks.name());


        dataList.getRows().forEach(data -> {
            if(day == -1) {
                log.info("Cannot find date for values {} ", data);
                return;
            }
            Integer engageCount = (shares == -1 ? 0: (Integer) data.get(shares)) + (likes == -1 ? 0: (Integer) data.get(likes))
                    + (comments == -1 ? 0: (Integer) data.get(comments))
                    + (subGained == -1 ? 0: (Integer) data.get(subGained)) + (views == -1 ? 0: (Integer) data.get(views));

            PageLevelMetaData pageLevelMetaData = new PageLevelMetaData();
            String date = (String) data.get(day);
            pageLevelMetaData.setDate(DateTimeUtils.parseYoutubeReportDateFormat(date));
            pageLevelMetaData.setFollowerGainCount(subGained == -1  ? null : (Integer) data.get(subGained));
            pageLevelMetaData.setFollowerLostCount(subLost == -1 ? null : (Integer) data.get(subLost));
            pageLevelMetaData.setProfileVideoViews(views == -1 ? null: (Integer) data.get(views));
            pageLevelMetaData.setCommentCount(comments);
            pageLevelMetaData.setPagePostLikeCount(likes);
            pageLevelMetaData.setShareCount(shares);
            pageLevelMetaData.setPostEngagements(engageCount);

            pageLevelMetaDataList.put(date, pageLevelMetaData);
        });

        return pageLevelMetaDataList;
    }

    @Override
    public InsightsESRequest createESRequestForPerformanceData(InsightsRequest insights,String index,List<String> pageIdList, Integer sourceId) {
        InsightsESRequest request = new InsightsESRequest();
        request.setIndex(index);
        request.setRouting(insights.getEnterpriseId().toString());
        request.setSearchTemplate(SearchTemplate.PAGE_SUMMARY_METRIC);
        String pageIds =  pageIdList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","));
        SimpleDateFormat dateFormatter = new SimpleDateFormat(DATE_FORMATTER_STRING);
        DataModel dataModel = new DataModel(pageIds,
                dateFormatter.format(insights.getStartDate()),dateFormatter.format(insights.getEndDate()),String.valueOf(sourceId));
        request.setDataModel(dataModel);
        return  request;
    }

    @Override
    public Integer postLinkClickCountSummary(Long enterpriseId, String index, String pageId) {
        InsightsESRequest request = new InsightsESRequest();
        request.setIndex(index);
        request.setRouting(enterpriseId.toString());
        request.setSearchTemplate(SearchTemplate.POST_LINK_CLICK_COUNT_SUMMARY_METRIC);
        DataModel dataModel = new DataModel(pageId);
        request.setDataModel(dataModel);
        return reportsEsService.getPostLinkClickCountTotal(request);
    }

    @Override
    public Integer postOtherClickCountSummary(Long enterpriseId, String index, String pageId) {
        InsightsESRequest request = new InsightsESRequest();
        request.setIndex(index);
        request.setRouting(enterpriseId.toString());
        request.setSearchTemplate(SearchTemplate.POST_OTHER_CLICK_COUNT_SUMMARY_METRIC);
        DataModel dataModel = new DataModel(pageId);
        request.setDataModel(dataModel);
        return reportsEsService.getPostOtherClickCountTotal(request);
    }

    @Override
    public Map<String, Integer> fetchSummationOfFbStoryInsights(Long enterpriseId, String index, String pageId, SearchTemplate searchTemplate) {
        InsightsESRequest request = new InsightsESRequest();
        request.setIndex(index);
        request.setRouting(enterpriseId.toString());
        request.setSearchTemplate(searchTemplate);
        DataModel dataModel = new DataModel(pageId);
        request.setDataModel(dataModel);
        return reportsEsService.getSummationOfFbStoryInsights(request);
    }

    @Override
    public Integer prevPageLinkClickCount(Long enterpriseId, String index, String pageId) {
        InsightsESRequest request = new InsightsESRequest();
        request.setIndex(index);
        request.setRouting(enterpriseId.toString());
        request.setSearchTemplate(SearchTemplate.LAST_NON_ZERO_POST_LINK_CLICK_COUNT);
        DataModel dataModel = new DataModel(pageId);
        request.setDataModel(dataModel);
        return reportsEsService.prevLinkClickCount(request);
    }

    @Override
    public Integer prevPageOtherClickCount(Long enterpriseId, String index, String pageId) {
        InsightsESRequest request = new InsightsESRequest();
        request.setIndex(index);
        request.setRouting(enterpriseId.toString());
        request.setSearchTemplate(SearchTemplate.LAST_NON_ZERO_POST_OTHER_CLICK_COUNT);
        DataModel dataModel = new DataModel(pageId);
        request.setDataModel(dataModel);
        return reportsEsService.prevOtherClickCount(request);
    }

    @Override
    public Integer prevPageStoryInsights(Long enterpriseId, String index, String pageId, SearchTemplate searchTemplate, String insightName) {
        InsightsESRequest request = new InsightsESRequest();
        request.setIndex(index);
        request.setRouting(enterpriseId.toString());
        request.setSearchTemplate(searchTemplate);
        DataModel dataModel = new DataModel(pageId);
        dataModel.setReportInsightName(insightName);
        request.setDataModel(dataModel);
        return reportsEsService.prevPageStoryInsights(request, insightName);
    }

    @Override
    public Map<Date, Integer> pageClickCountAtGivenDate(Long enterpriseId, String index, String pageId, Date date){
        SimpleDateFormat inputFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy");
        SimpleDateFormat esDateFormat = new SimpleDateFormat(DATE_FORMATTER_STRING);
        String esDate = date.toString();
        try {
            Date parsedDate = inputFormat.parse(date.toString());
            // Set the parsed date to midnight (00:00:00)
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(parsedDate);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            // Format the adjusted date to the desired format
            esDate = esDateFormat.format(calendar.getTime());
        } catch (ParseException e) {
            log.warn("Error parsing date: {}", date);
        }
        InsightsESRequest request = new InsightsESRequest();
        request.setIndex(index);
        request.setRouting(enterpriseId.toString());
        request.setSearchTemplate(SearchTemplate.PAGE_INSIGHT_AFTER_GIVEN_DATE);
        DataModel dataModel = new DataModel(pageId, esDate);
        request.setDataModel(dataModel);
        return reportsEsService.datevsClickCountMap(request);
    }

    @Override
    public List<PagePostInsightsData> prepareDayWisePageInsightDataFromEs(Long enterpriseId, String index, String pageId, Date date) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy");
        SimpleDateFormat esDateFormat = new SimpleDateFormat(DATE_FORMATTER_STRING);
        String esDate = date.toString();
        try {
            Date parsedDate = inputFormat.parse(date.toString());
            Date adjustedDate = DateTimeUtils.setTimeToMidnight(parsedDate);

            // Format the adjusted date to the desired format
            esDate = esDateFormat.format(adjustedDate.getTime());
        } catch (ParseException e) {
            log.warn("Error parsing date: {}", date);
        }
        InsightsESRequest request = new InsightsESRequest();
        request.setIndex(index);
        request.setRouting(enterpriseId.toString());
        request.setSearchTemplate(SearchTemplate.PAGE_INSIGHT_AFTER_GIVEN_DATE);
        DataModel dataModel = new DataModel(pageId, esDate);
        request.setDataModel(dataModel);
        return reportsEsService.getDayWisePageInsightDataMapFromES(request);
    }

    @Override
    public List<BackfillInsightReq> conversionToFBScanEventDTO(List<BusinessFBPage> fbPages, PageInsightsRequest pageInsights) {
        List<BackfillInsightReq> scanEventList = new ArrayList<>();

        for(BusinessFBPage fbPage : fbPages) {
            BackfillInsightReq scanEventDTO = new BackfillInsightReq();
            scanEventDTO.setExternalId(fbPage.getFacebookPageId());
            scanEventDTO.setSourceName(SocialChannel.FACEBOOK.getName());
            scanEventDTO.setSourceId(SocialChannel.FACEBOOK.getId());
            scanEventDTO.setStartDate(pageInsights.getStartDate());
            scanEventDTO.setEndDate(pageInsights.getEndDate());
            scanEventDTO.setMatrix(pageInsights.getMatrix());
            scanEventList.add(scanEventDTO);
        }
        log.info("BackfillInsightReq dto ready for {}", scanEventList);
        return scanEventList;
    }

    @Override
    public List<BackfillInsightReq> conversionToIGScanEventDTO(List<BusinessInstagramAccount> igPages, PageInsightsRequest pageInsights) {
        List<BackfillInsightReq> scanEventList = new ArrayList<>();

        for(BusinessInstagramAccount igPage : igPages) {
            BackfillInsightReq scanEventDTO = new BackfillInsightReq();
            scanEventDTO.setSourceId(SocialChannel.INSTAGRAM.getId());
            scanEventDTO.setSourceName(SocialChannel.INSTAGRAM.getName());
            scanEventDTO.setExternalId(igPage.getInstagramAccountId());
            scanEventDTO.setStartDate(pageInsights.getStartDate());
            scanEventDTO.setEndDate(pageInsights.getEndDate());
            scanEventDTO.setMatrix(pageInsights.getMatrix());
            scanEventList.add(scanEventDTO);
        }
        log.info("Scan event dto ready for {}", scanEventList);
        return scanEventList;
    }

    @Override
    public List<BackfillInsightReq> conversionToLNScanEventDTO(List<BusinessLinkedinPage> pages, PageInsightsRequest pageInsights) {
        List<BackfillInsightReq> scanEventList = new ArrayList<>();
        if(CollectionUtils.isEmpty(pages)){
            return scanEventList;
        }
        for(BusinessLinkedinPage linkedInPage : pages) {
            BackfillInsightReq scanEventDTO = new BackfillInsightReq();
            scanEventDTO.setExternalId(linkedInPage.getProfileId());
            scanEventDTO.setSourceName(SocialChannel.LINKEDIN.getName());
            scanEventDTO.setSourceId(SocialChannel.LINKEDIN.getId());
            scanEventDTO.setStartDate(pageInsights.getStartDate());
            scanEventDTO.setEndDate(pageInsights.getEndDate());
            scanEventDTO.setMatrix(pageInsights.getMatrix());
            scanEventList.add(scanEventDTO);
        }
        log.info("BusinessLinkedinPage Scan event dto ready for {}", scanEventList);
        return scanEventList;
    }

    @Override
    public List<BackfillInsightReq> conversionToXScanEventDTO(List<BusinessTwitterAccounts> twitterPages, PageInsightsRequest pageInsights) {
        List<BackfillInsightReq> scanEventList = new ArrayList<>();

        if(CollectionUtils.isEmpty(twitterPages)){
            return scanEventList;
        }
        for(BusinessTwitterAccounts twitterPage : twitterPages) {
            BackfillInsightReq scanEventDTO = getSocialScanEventDTO(twitterPage,pageInsights);
            scanEventList.add(scanEventDTO);
        }
        log.info("BusinessTwitterAccounts Scan event dto ready for {}", scanEventList);

        return scanEventList;
    }

    @Override
    public List<PageLevelMetaData> prepareDayWiseDataXHistorical(TwitterHistoricalPostInsightResponse postInsightResponse, Long profileId, Integer businessId) {
        List<PageLevelMetaData> pageLevelMetaDataList = new ArrayList<>();
        postInsightResponse.getInsights().getEngagements().forEach((k,v)->{
            PageLevelMetaData pageLevelMetaData = new PageLevelMetaData();
            pageLevelMetaData.setDate(DateTimeUtils.parseYoutubeReportDateFormat(k));
            pageLevelMetaData.setPostImpressions(Integer.parseInt(postInsightResponse.getInsights().getImpressions().get(k)));
            pageLevelMetaData.setPostEngagements(Integer.parseInt(v));
            pageLevelMetaData.setProfileVideoViews(Integer.parseInt(postInsightResponse.getInsights().getVideoViews().get(k)));
            pageLevelMetaData.setPostEngagementTotal(0);
            pageLevelMetaData.setPostTotalCount(0);
            pageLevelMetaData.setPostImpressionTotal(0);
            pageLevelMetaData.setTotalFollower(0);
            pageLevelMetaData.setPostCount(0);
            pageLevelMetaData.setFollowerGainCount(0);
            pageLevelMetaData.setLinkClickCount(0);
            pageLevelMetaData.setTotalProfileVideoViews(0);
            pageLevelMetaDataList.add(pageLevelMetaData);
        });
        return pageLevelMetaDataList;
    }

    @Override
    public PagePostInsightsTotalData convertPageInsightDataPoint(PageInsightDataPoint pageInsightDataPoint) {
        PagePostInsightsTotalData pagePostInsightsTotalData = new PagePostInsightsTotalData();
        pagePostInsightsTotalData.setTotalPagePostComments(pageInsightDataPoint.getTotalPostCommentCount());
        pagePostInsightsTotalData.setTotalPagePostLikes(pageInsightDataPoint.getTotalPostLikeCount());
        pagePostInsightsTotalData.setTotalPagePostShares(pageInsightDataPoint.getTotalPostShareCount());
        return pagePostInsightsTotalData;
    }

    @Override
    public PagePostInsightsGainData convertToPagePostInsightsGain(PagePostInsightsTotalData pagePostInsightsTotalData,
                                                                  PostInsightsDataPerPage currentPostPageInsights) {
        PagePostInsightsGainData pagePostInsightsGainData = new PagePostInsightsGainData();
        pagePostInsightsGainData.setPostCommentCountGain(pagePostInsightsTotalData.getTotalPagePostComments() == null ?
                0 : currentPostPageInsights.getCommentCount() - pagePostInsightsTotalData.getTotalPagePostComments());
        pagePostInsightsGainData.setPostLikeCountGain(pagePostInsightsTotalData.getTotalPagePostLikes() == null ?
                0 : currentPostPageInsights.getLikeCount() - pagePostInsightsTotalData.getTotalPagePostLikes());
        pagePostInsightsGainData.setPostShareCountGain(pagePostInsightsTotalData.getTotalPagePostShares() == null ?
                0 : currentPostPageInsights.getShareCount() - pagePostInsightsTotalData.getTotalPagePostShares());
        return pagePostInsightsGainData;
    }

    @Override
    public List<PageLevelMetaData> prepareDayWiseDataForTiktok(TikTokPageData tikTokPageData, BusinessTiktokAccounts businessTiktokAccounts) {
        List<PageLevelMetaData> pageLevelMetaDataList = new ArrayList<>();
        if(Objects.isNull(tikTokPageData)
                || Objects.isNull(tikTokPageData.getData())
                || CollectionUtils.isEmpty(tikTokPageData.getData().getMetrics())) {
            return new ArrayList<>();
        }
        try {
            tikTokPageData.getData().getMetrics().forEach(data -> {
                try {
                    PageLevelMetaData pageLevelMetaData = new PageLevelMetaData();
                    SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_WITH_DASH_STRING);

                    pageLevelMetaData.setDate(dateFormat.parse(data.getDate()));

                    int followerGain = 0, followerLost = 0;
                    if(data.getFollowersCount() > 0 )
                        followerGain = data.getFollowersCount();
                    else if(data.getFollowersCount() < 0)
                        followerLost = data.getFollowersCount() * (-1);
                    int engagements = data.getShares() + data.getLikes() + data.getComments();
                    pageLevelMetaData.setPostEngagements(engagements);
                    pageLevelMetaData.setPostReach(data.getUniqueVideoViews());
                    pageLevelMetaData.setPostImpressions(data.getVideoViews());
                    pageLevelMetaData.setShareCount(data.getShares());
                    pageLevelMetaData.setCommentCount(data.getComments());
                    pageLevelMetaData.setLikesGainCount(data.getLikes());
                    pageLevelMetaData.setFollowerGainCount(followerGain);
                    pageLevelMetaData.setFollowerLostCount(followerLost);
                    pageLevelMetaData.setTotalFollower(tikTokPageData.getData().getFollowersCount());
                    pageLevelMetaData.setProfileVideoViews(data.getVideoViews());
                    pageLevelMetaData.setPagePostLikeCount( data.getLikes());
                    pageLevelMetaData.setDateString(data.getDate());
                    pageLevelMetaData.setAudienceActivity(data.getAudienceActivity());
                    populateDemographicsAndCalculations(pageLevelMetaData, tikTokPageData);
                    pageLevelMetaData.setIsBusinessAccount(pageLevelMetaData.getIsBusinessAccount());
                    pageLevelMetaDataList.add(pageLevelMetaData);
                } catch (ParseException e) {
                    log.error("Date Parsing Exception occurred {}", e.getMessage(), e);
                }
            });
        } catch (Exception e) {
            log.error("Exception occurred {}", e.getMessage(), e);
        }
        return pageLevelMetaDataList;
    }

    private void populateDemographicsAndCalculations(PageLevelMetaData pageLevelMetaData, TikTokPageData tikTokPageData) {
        try {
            List<DemographicsInsightDataPoint.Data> demographicDataPoints;
            if (Objects.nonNull(pageLevelMetaData)
                    && Objects.nonNull(tikTokPageData)
                    && Objects.nonNull(tikTokPageData.getData())) {
                if (CollectionUtils.isNotEmpty(tikTokPageData.getData().getAudienceGenders())) {
                    demographicDataPoints = new ArrayList<>();
                    DemographicsInsightDataPoint.Data demographicsInsightDataPoint = new DemographicsInsightDataPoint.Data();
                    Map<String, DemographicsInsightDataPoint.Data> dataMap  = new HashMap<>();
                    for(TikTokPageData.AudienceGenders data : tikTokPageData.getData().getAudienceGenders()) {
                        demographicsInsightDataPoint = setGenderData(data, pageLevelMetaData.getTotalFollower());
                        demographicDataPoints.add(demographicsInsightDataPoint);
                    }
                    pageLevelMetaData.setAudienceGenders(demographicDataPoints);
                }
                if (CollectionUtils.isNotEmpty(tikTokPageData.getData().getAudienceCountries())) {
                    demographicDataPoints = new ArrayList<>();
                    DemographicsInsightDataPoint.Data demographicsInsightDataPoint;
                    for(TikTokPageData.AudienceCountries audienceCountries : tikTokPageData.getData().getAudienceCountries()){
                        demographicsInsightDataPoint = setCountryData(audienceCountries, pageLevelMetaData.getTotalFollower());
                        demographicDataPoints.add(demographicsInsightDataPoint);
                    }
                    pageLevelMetaData.setAudienceCountries(demographicDataPoints);
                }
                if (CollectionUtils.isNotEmpty(tikTokPageData.getData().getAudienceCities())) {
                    demographicDataPoints = new ArrayList<>();
                    DemographicsInsightDataPoint.Data demographicsInsightDataPoint;
                    for(TikTokPageData.AudienceCities audienceCities : tikTokPageData.getData().getAudienceCities()){
                        demographicsInsightDataPoint = setCityData(audienceCities, pageLevelMetaData.getTotalFollower());
                        demographicDataPoints.add(demographicsInsightDataPoint);
                    }
                    pageLevelMetaData.setAudienceCities(demographicDataPoints);
                }
                if (CollectionUtils.isNotEmpty(tikTokPageData.getData().getAudienceAges())) {
                    demographicDataPoints = new ArrayList<>();
                    DemographicsInsightDataPoint.Data demographicsInsightDataPoint;
                    for(TikTokPageData.AudienceAges audienceAges : tikTokPageData.getData().getAudienceAges()){
                        demographicsInsightDataPoint = setAgeData(audienceAges, pageLevelMetaData.getTotalFollower());
                        demographicDataPoints.add(demographicsInsightDataPoint);
                    }
                    pageLevelMetaData.setAudienceAges(demographicDataPoints);
                }
            }
        } catch (Exception e) {
            log.error("Exception occurred while calculating percentage", e);
        }
    }

    private DemographicsInsightDataPoint.Data setCountryData(TikTokPageData.AudienceCountries audienceCountries, Integer total) {
        DemographicsInsightDataPoint.Data data = new DemographicsInsightDataPoint.Data();
        data.setCode(audienceCountries.getCountry());
        data.setPercentage(audienceCountries.getPercentage()* 100);
        data.setTotal(getTotalFromPercentage(audienceCountries.getPercentage(), total));
        try {
            String name = getIsoCountryName(audienceCountries.getCountry());
            if(name.equalsIgnoreCase("Others"))
                name = "Others";
            data.setLabel(name);
            data.setName(name);
        } catch (Exception e){
            log.error("Exception occurred while setting country in getISO3Country");
        }

        return data;
    }

    private String getIsoCountryName(String name) {
        return new Locale("en", name).getDisplayCountry();
    }

    private DemographicsInsightDataPoint.Data setCityData(TikTokPageData.AudienceCities audienceCities, Integer total) {
        DemographicsInsightDataPoint.Data demographicsInsightDataPoint = new DemographicsInsightDataPoint.Data();
        demographicsInsightDataPoint.setPercentage(audienceCities.getPercentage()* 100);
        demographicsInsightDataPoint.setTotal(getTotalFromPercentage(audienceCities.getPercentage(), total));
        demographicsInsightDataPoint.setLabel(getCityLabel(audienceCities.getCity()));
        demographicsInsightDataPoint.setName(audienceCities.getCity());
        return demographicsInsightDataPoint;
    }

    private String getCityLabel(String name) {
        if(StringUtils.isNotEmpty(name) && name.contains(" ")) {
            String[] parts = name.split(" ", 2);
            String countryCode = parts[0];
            String countryName = getIsoCountryName(countryCode);
            if(parts.length > 1)
                return parts[1] + ", " + countryName;
        }
        return name;
    }

    private DemographicsInsightDataPoint.Data setAgeData(TikTokPageData.AudienceAges audienceAges, Integer total) {
        DemographicsInsightDataPoint.Data demographicsInsightDataPoint = new DemographicsInsightDataPoint.Data();
        demographicsInsightDataPoint.setName(audienceAges.getAge());
        demographicsInsightDataPoint.setLabel(audienceAges.getAge());
        demographicsInsightDataPoint.setTotal(getTotalFromPercentage(audienceAges.getPercentage(), total));
        demographicsInsightDataPoint.setPercentage(audienceAges.getPercentage()* 100);
        return demographicsInsightDataPoint;
    }

    private DemographicsInsightDataPoint.Data setGenderData(TikTokPageData.AudienceGenders audienceGenders, Integer totalFollower) {
        DemographicsInsightDataPoint.Data data = new DemographicsInsightDataPoint.Data();
        data.setName(audienceGenders.getGender());
        data.setLabel(audienceGenders.getGender());
        data.setPercentage(audienceGenders.getPercentage() * 100);
        data.setTotal(getTotalFromPercentage(audienceGenders.getPercentage(), totalFollower));
        return data;
    }

    private Integer getTotalFromPercentage(Double percent, Integer totalValue) {
        double n = (percent) * totalValue;
        return Integer.parseInt(String.valueOf(Math.round(n)));
    }

    @NotNull
    private BackfillInsightReq getSocialScanEventDTO(BusinessTwitterAccounts twitterPage, PageInsightsRequest pageInsights) {
        BackfillInsightReq scanEventDTO = new BackfillInsightReq();
        scanEventDTO.setExternalId(String.valueOf(twitterPage.getProfileId()));
        scanEventDTO.setSourceName(SocialChannel.TWITTER.getName());
        scanEventDTO.setSourceId(SocialChannel.TWITTER.getId());
        scanEventDTO.setStartDate(pageInsights.getStartDate());
        scanEventDTO.setEndDate(pageInsights.getEndDate());
        scanEventDTO.setMatrix(pageInsights.getMatrix());
        return scanEventDTO;
    }

    @Override
    public InsightsESRequest createESRequestForExecutiveSumData(InsightsRequest insights, String index, List<String> pageIdList) {
        InsightsESRequest request = new InsightsESRequest();
        request.setIndex(index);
        request.setRouting(insights.getEnterpriseId().toString());
        request.setSearchTemplate(SearchTemplate.POST_EXEC_SUMMARY_METRIC);
        String pageIds =  pageIdList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","));
        SimpleDateFormat dateFormatter = new SimpleDateFormat(DATE_FORMATTER_STRING);
        DataModel dataModel = new DataModel(pageIds,
                dateFormatter.format(insights.getStartDate()),dateFormatter.format(insights.getEndDate()));
        request.setDataModel(dataModel);
        return  request;
    }

    @Override
    public LinkedHashMap<String, LeadershipByPostsResponse> convertSearchResponseToLeadership(SearchResponse response) {
        LinkedHashMap<String, LeadershipByPostsResponse> responseMap = new LinkedHashMap<>();
        Aggregations aggregations = response.getAggregations();
        Terms pages = aggregations.get("pages");

        for (Terms.Bucket bucket : pages.getBuckets()) {
            LeadershipByPostsResponse postsResponse = new LeadershipByPostsResponse();
            String pageId = bucket.getKeyAsString();
            log.info("Prepare response for page id :{}",pageId);
            // Extract nested "current_data" aggregations
            ParsedFilter currentDataAgg = bucket.getAggregations().get("current_data");
            Aggregations currentDataAggs = currentDataAgg.getAggregations();
            // Create a map to hold aggregated values
            postsResponse.setEngagements((int) ((Sum) currentDataAggs.get(POST_ENGAGEMENT)).getValue());
            postsResponse.setNet((int)((Sum) currentDataAggs.get(FOLLOWER_GAIN)).getValue());
            postsResponse.setImpressions((int)((Sum) currentDataAggs.get(POST_IMPRESSIONS)).getValue());
            double value = ((Avg) currentDataAggs.get(POST_ENG_RATE)).getValue();
            postsResponse.setEngRate(String.format("%.2f", value).equalsIgnoreCase("Infinity") ? 0.0
                    : Double.parseDouble(String.format("%.2f", value)));
            responseMap.put(pageId, postsResponse);
        }
        return responseMap;
    }

    @Override
    public void updateResponseWithPostCount(SearchResponse response, SearchResponse searchResponseForFollowers,
                                            LinkedHashMap<String, LeadershipByPostsResponse> leadershipByPostsResponseMap) {
        Aggregations aggregations = response.getAggregations();
        if (aggregations == null) {
            return;
        }
        Terms pagesAgg = aggregations.get("pages");
        if (pagesAgg == null) {
            return;
        }
        for (Terms.Bucket bucket : pagesAgg.getBuckets()) {
            String pageId = bucket.getKeyAsString();
            log.info("Page id for postCount:{}",pageId);
            LeadershipByPostsResponse postsResponse = leadershipByPostsResponseMap.get(pageId);
            if(Objects.isNull(postsResponse)) {
                log.info("No data found for page id :{} in map",pageId);
                continue;
            }
            ParsedFilter currentDataAgg = bucket.getAggregations().get("current_data");
            if (currentDataAgg == null) continue;
            ParsedValueCount postCountAgg = currentDataAgg.getAggregations().get("post_count");
            long postCount = postCountAgg != null ? postCountAgg.getValue() : 0;
            postsResponse.setPostCount((int)postCount);
            postsResponse.setTotalAudience(0);
        }
        Terms pageIdAgg = searchResponseForFollowers.getAggregations().get(TOTAL_FOLLOWERS_COUNT);
        for (Terms.Bucket bucket : pageIdAgg.getBuckets()) {
            TopHits latestTimestamp = bucket.getAggregations().get("latest_time_stamp");
            ESPageRequest esPageRequest = JSONUtils.fromJSON(latestTimestamp.getHits().getAt(0).getSourceAsString(),ESPageRequest.class);
            if(Objects.isNull(esPageRequest)){
                continue;
            }
            LeadershipByPostsResponse postsResponse = leadershipByPostsResponseMap.get(esPageRequest.getPage_id());
            postsResponse.setTotalAudience(esPageRequest.getTotal_follower_count());
        }
    }

    @Override
    public InsightsESRequest createESRequestForTopPosts(InsightsPostRequest insightsPostRequest, String index) {
        InsightsESRequest request = new InsightsESRequest();
        request.setIndex(index);
        request.setRouting(insightsPostRequest.getEnterpriseId().toString());
        Set<Long> tagIds = new HashSet<>();
        Set<Integer> publisherIds = insightsPostRequest.getPublisherIds();
        request.setSearchTemplate(SearchTemplate.POST_INSIGHT_ALL_CHANNEL_TOP_POST);
        String businessIds = insightsPostRequest.getBusinessIds().stream().map(Object::toString).collect(Collectors.joining(","));
        String pageIds =  insightsPostRequest.getPageIds().stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","));
        SimpleDateFormat dateFormatter = new SimpleDateFormat(DATE_FORMATTER_STRING);
        DataModel dataModel = new DataModel(businessIds, pageIds, insightsPostRequest.getEnterpriseId(),
                dateFormatter.format(insightsPostRequest.getStartDate()),dateFormatter.format(insightsPostRequest.getEndDate()), insightsPostRequest.getSourceIds(),
                insightsPostRequest.getSortParam(),insightsPostRequest.getSortOrder()
                ,String.valueOf(insightsPostRequest.getStartIndex()*insightsPostRequest.getPageSize()),insightsPostRequest.getPageSize(),
                (insightsPostRequest.getStartIndex()+1)*insightsPostRequest.getPageSize(), insightsPostRequest.getIncludeExtendedBounds(), insightsPostRequest.getDeletedFilter());
        if(CollectionUtils.isNotEmpty(tagIds)) {
            dataModel.setTagIds(tagIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        if(CollectionUtils.isNotEmpty(publisherIds)) {
            dataModel.setPublisherIds(publisherIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        request.setDataModel(dataModel);
        return  request;
    }

}