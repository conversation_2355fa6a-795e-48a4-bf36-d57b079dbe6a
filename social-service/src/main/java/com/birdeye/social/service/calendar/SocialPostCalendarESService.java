package com.birdeye.social.service.calendar;

import com.birdeye.social.constant.FilterPostType;
import com.birdeye.social.constant.SocialTagEntityType;
import com.birdeye.social.entities.PostLibMaster;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.SocialPostScheduleInfo;
import com.birdeye.social.entities.SocialTag;
import com.birdeye.social.model.ApprovalMetadata;
import com.birdeye.social.model.ApprovalWorkflowEvent;
import com.birdeye.social.model.SocialPostEsSyncRequest;
import com.birdeye.social.model.es.SocialPostCalendarMessage;
import com.birdeye.social.sro.SocialPostAIImage;
import com.birdeye.social.sro.SocialPostAIImageDescription;
import com.birdeye.social.sro.SocialTagBasicDetail;
import com.birdeye.social.sro.SocialTagEntityMappingRequest;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface SocialPostCalendarESService {

    List<SocialPostCalendarMessage> searchFromEsIndex(Integer enterpriseId, Date startDate,
                                                      Date endDate, List<Integer> sourceIds,
                                                      List<Integer> publishStateIds, List<Integer> creators, List<Integer> approvals,
                                                      Integer postId, Set<Long> tagIds,
                                                      List<FilterPostType> postType, List<FilterPostType> postContent);

    List<SocialPostCalendarMessage> searchFromEsIndex(List<Integer> enterpriseIds, Date startDate,
                                                      Date endDate, List<Integer> sourceIds,
                                                      List<Integer> publishStateIds, Set<Long> tagIds);

    SocialPostCalendarMessage searchFromEsIndexByAiPostId(Integer aiPostId);

    List<SocialPostCalendarMessage> getESCalendarSaveObj(SocialPost socialPost, List<SocialPostScheduleInfo> socialPostScheduleInfoList,
                                                         SocialTagEntityMappingRequest socialTagEntityMappingRequest,
                                                         List<SocialTagBasicDetail> tags, List<String> failedPageIds);

    List<SocialPostCalendarMessage> getESCalendarSaveObj(List<SocialPost> socialPosts, List<SocialPostScheduleInfo> socialPostScheduleInfoList,
                                                         SocialTagEntityMappingRequest socialTagEntityMappingRequest);

    void updatePostStatusOnES(Integer socialPostId, List<SocialPostScheduleInfo> enterpriseId);

    void updatePostStatusOnESInBulk(Integer socialPostId, List<SocialPostScheduleInfo> enterpriseId);

    void updatePageIdsOnES(Integer socialPostId, Integer socialPostScheduleInfoId, List<String> pageIds);

    void save(List<SocialPostCalendarMessage> socialPostCalendarMessages);

    void updateApprovalRecordOnES(ApprovalMetadata approvalMetadata, List<SocialPostCalendarMessage> socialPostCalendarMessageList,
                                  String status);

    void updateTagsRecordOnES(List<SocialPostCalendarMessage> socialPostInputMessageRequestList, SocialTagEntityMappingRequest socialTagEntityMappingRequest, Map<Long, SocialTag> allExistingTagIdToSocialTagMap);

    String getId(Integer socialPostId, Integer scheduleInfoId);

    void deleteRecord(Integer socialPostid, Integer scheduleInfoId);
    void deleteRecordInBulk(List<SocialPostScheduleInfo> oldScheduledPosts);

    void deleteRecord(List<String> esIds);

    void updateTagMappings(Set<Long> toBeOperatedOnTagIds , Map<Long, SocialTag> allExistingTagIdToSocialTagMap,
                           Long postId, List<Integer> scheduleInfoIds, SocialTagEntityType entityType);

    void updatePostApprovalOnES(SocialPost id, ApprovalMetadata approvalMetadata, ApprovalWorkflowEvent approvalWorkflowEvent, List<SocialPostScheduleInfo> bySocialPostId);
    List<SocialPostCalendarMessage> createEsUpdateObject(List<SocialPostScheduleInfo> fetchedScheduleList);

    void updateDocument(List<SocialPostCalendarMessage> socialPostCalendarMessageList);

    List<SocialPostCalendarMessage> findByEsIds(List<SocialPostScheduleInfo> socialPostScheduleInfoList);

    boolean isGetFlag(Integer userId);
    boolean isPostFlag(Integer userId);

    List<SocialPostCalendarMessage> getCalendarResellerPostSavingObject(SocialPost socialPost, List<SocialPostScheduleInfo> socialPostScheduleInfoList,
                                                                        SocialTagEntityMappingRequest socialTagEntityMappingRequest,
                                                                        List<SocialTagBasicDetail> tags, List<String> failedPageIds, Integer resellerAccountId);


    void saveSuspendedDeletedPost(SocialPostEsSyncRequest socialPostsESSyncRequest) throws Exception;

    List<SocialPostEsSyncRequest> getDeletedScheduledPosts(Long enterpriseId);

    List<SocialPostCalendarMessage> getESCalendarSaveObjForAi(Map<String, PostLibMaster> savedPostMap, Map<String, Long> tagMap);

    List<SocialPostCalendarMessage> searchFromEsIndexByAiPost(String postId);


}