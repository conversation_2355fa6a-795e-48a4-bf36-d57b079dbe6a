package com.birdeye.social.service;

import com.birdeye.social.bam.BAMAggregationService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.SocialSetupAuditEnum;
import com.birdeye.social.constant.Status;
import com.birdeye.social.dao.*;
import com.birdeye.social.dto.ValidityRequestDTO;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.request.facebook.FbProfileInfoRequest;
import com.birdeye.social.external.service.FbMessengerExternalService;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.facebook.*;
import com.birdeye.social.facebook.response.FacebookBaseResponse;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.instagram.TokenUpdateRequest;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.platform.dao.BusinessFacebookPageRepository;
import com.birdeye.social.platform.dao.BusinessFacebookPageRepository.BFP;
import com.birdeye.social.platform.dao.BusinessRepository;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.sro.SocialChannelSyncRequest;
import com.birdeye.social.utils.BusinessUtilsService;
import com.birdeye.social.utils.SocialProxyHandler;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.FB_SYSTEM_USER_PPCA_TOKEN;
import static com.birdeye.social.constant.Constants.VALIDATE_PAGE_REQUEST;

@Service("FBPageService")
public class FacebookPageServiceImpl extends SocialAccountSetupCommonService implements FacebookPageService {
	
	//External Service
	@Autowired
	private FacebookService fbService;
	
	// FB mapped page service
	@Autowired
	private BusinessFacebookPageRepository businessFbPageRepo;
	
	// FB raw page service
	@Autowired
	private SocialFBPageRepository socialFBPageRepository;

	@Autowired
	private BusinessGetPageReqRepo getReqRepo;

	
	@Autowired
	private BusinessUtilsService businessUtilsService;
	
	@Autowired
	private IRedisLockService				redisService;
	
	@Autowired
	private BusinessGetPageReqRepo			businessGetPageReqRepo;
	
	@Autowired
	private IBusinessCachedService businessService;
	
	@Autowired
	private ISocialAppService socialAppService;
	
	@Autowired
	private FacebookSocialService facebookCacheService;

	@Autowired
	private IRoleMappingService iRoleMappingService;
	
	@Autowired
	private BAMAggregationService	bamAggregationService;
	
	@Autowired
	private KafkaProducerService	kafkaProducer;
	
	@Autowired
	private CommonService commonService;
	
	@Autowired
	private FbMessengerExternalService fbMsgService;
	
	@Autowired
	private BusinessRepository businessRepo;

	@Autowired
	private BusinessGetPageOpenUrlReqRepo businessGetPageOpenUrlReqRepo;

	@Autowired
	private NexusService nexusService;

	@Autowired
	private IBrokenIntegrationService brokenIntegrationService;

	@Autowired
	private SocialProxyHandler proxyHandler;

	@Autowired
	private InvalidSocialIntegrationAuditRepo invalidSocialIntegrationAuditRepo;

	@Autowired
	private SocialTempFBPageRepository tempFBPageRepository;

	@Autowired
	private SocialReservedAccountsRepo reservedAccountsRepo;

	@Autowired
	private IPermissionMappingService permissionMappingService;


	private static Logger LOGGER = LoggerFactory.getLogger(FacebookPageServiceImpl.class);

	private static final String MESSENGER = "messenger/";
	private static final String PAGES_MESSAGING = "pages_messaging";

	private Map<String, List<FacebookPageInfo>> getChildLocation(Map<String, String> pageToPageURL) {
		Map<String, List<FacebookPageInfo>> pageToPagePageInfo = new HashMap<>();
		try {
			for (Entry<String, String> enrty : pageToPageURL.entrySet()) {
				if (enrty.getKey() != null && enrty.getValue() != null) {
					FbPage page = fbService.getPageDetails(replaceLimit(enrty.getValue()));
					if (page != null) {
						getPageInfo(page, pageToPagePageInfo, enrty.getKey());
						PagingInfo paging = page.getPaging();
						while (paging != null && paging.getNext() != null) {
							FbPage childPage = fbService.getPageDetails(replaceLimit(paging.getNext()));
							getPageInfo(childPage, pageToPagePageInfo, enrty.getKey());
							paging = childPage.getPaging();
						}
					}
				}
				LOGGER.info("Total {} child pages are fetched for parent page {}", pageToPagePageInfo.get(enrty.getKey()) != null ? pageToPagePageInfo.get(enrty.getKey()).size() :"0", enrty.getKey());
			}
		} catch (Exception exe) {
			LOGGER.info("error {}", exe.getMessage());
		}
		return pageToPagePageInfo;
	}

	private void addPageInfotoUiPage(Map<String, FbPageInfo> pages, Map<String, List<FacebookPageInfo>> pagesInfos) {
		for (Entry<String, List<FacebookPageInfo>> entry : pagesInfos.entrySet()) {
			for (FacebookPageInfo fbPage : entry.getValue()) {
				FbPageInfo fbPageInfo = new FbPageInfo();
				fbPageInfo.setId(fbPage.getId());
				fbPageInfo.setLink(fbPage.getLink());
				fbPageInfo.setName(fbPage.getName());
				fbPageInfo.setPicture(fbPage.getPicture().getUrl());
				fbPageInfo.setAddress(extractSingleLineAddressFromLocation(fbPage));
				fbPageInfo.setAccessToken(fbPage.getAccess_token());
				pages.put(fbPage.getId(), fbPageInfo);
			}
		}
	}

	@Override
	public void getPageChildLocation(Map<String, FbPageInfo> pages, Map<String, String> pageToPageURL, FbUserProfileInfo user, String baseUrl, String extendedToken) {
		Map<String, List<FacebookPageInfo>> pageToPagePageInfo = getChildLocation(pageToPageURL);
		addPageInfotoUiPage(pages, pageToPagePageInfo);
	}

	@Async
	@Override
	public void getAccessTokenAndSaveToDB(List<FbPageInfo> pages, FbUserProfileInfo user, String baseUrl, String extendedToken, Boolean updateScope) {
		try {
			if (CollectionUtils.isNotEmpty(pages)) {
				pages.parallelStream().filter(page -> page != null).forEach(p -> savePageData(p, user, baseUrl, extendedToken,updateScope));
			}
		} catch (Exception exe) {
			LOGGER.error("Error {}", exe.getMessage());
		}
	}

	@Async
	@Override
	public void saveFBPageforMigratedFlow(FbPageInfo page, String userId, UserProfileInfo user, String baseUrl, String extendedToken, Business enterprise, String permToken) {
		try {
			//TODO: Use business cache and change it to SystemProperty service
			Boolean updateScoppe =  businessUtilsService.isUpdateScopeEnabled(enterprise.getBusinessId());
			List<BusinessFBPage> bFBPages = socialFBPageRepository.findByFacebookPageId(page.getId());
			BusinessFBPage bFBPage = null;
			if (CollectionUtils.isNotEmpty(bFBPages)) {
				bFBPage = bFBPages.get(0);
			} else {
				bFBPage = new BusinessFBPage();
			}

			prepareBusinessFBPage(bFBPage, page, userId, user.getFirstName(), user.getLastName(), user.getProfilePictureUrl(), enterprise.getBusinessId(), enterprise.getId());
			bFBPage.setIsSelected(1);
			bFBPage.setPageAccessToken(permToken);
			if(updateScoppe) {
				Map<String, FbPageInfo> pageInfo = new HashMap<>();
				pageInfo.put(page.getId(), page);
				bFBPage = updateFacebookRoleNew(bFBPage, pageInfo);
			}
			socialFBPageRepository.saveAndFlush(bFBPage);
			pushToKafkaForValidity(Constants.FACEBOOK, Arrays.asList(bFBPage.getFacebookPageId()));

		} catch (Exception exe) {
			LOGGER.error("Error {}", exe.getMessage());
		}

	}

	private void savePageData(FbPageInfo pgInfo, FbUserProfileInfo user, String baseUrl, String extendedToken, Boolean updateScope) {
		try {
			BusinessFBPage bFBPage = new BusinessFBPage();
			prepareBusinessFBPage(bFBPage, pgInfo, user.getId(), user.getFirst_name(), user.getLast_name(), user.getPicture().getUrl(), null, null);
			bFBPage.setPageAccessToken(getFBAccessToken(pgInfo.getId(), baseUrl, extendedToken));
			if(updateScope) {
				Map<String, FbPageInfo> pages = new HashMap<>();
				pages.put(pgInfo.getId(), pgInfo);
				bFBPage = updateFacebookRoleNew(bFBPage, pages);
			}

			socialFBPageRepository.saveAndFlush(bFBPage);
			pushToKafkaForValidity(Constants.FACEBOOK, Arrays.asList(bFBPage.getFacebookPageId()));
		} catch (Exception exe) {
			LOGGER.error("For page id {} Error while saving the data {}", pgInfo.getId(), exe.getMessage());
		}
	}

	private void prepareBusinessFBPage(BusinessFBPage bFBPage, FbPageInfo pgInfo, String userId, String userFirstName, String userLastName,
									   String userPictureUrl, Long enterpriseId, Integer accountId) {
		bFBPage.setUserId(userId);
		// for enterprise setup during page fetching no need to save enterprise Id as during connect page api (selected page save) we will save enterprise id.
		if (enterpriseId != null) {
			bFBPage.setEnterpriseId(enterpriseId);
		}
		bFBPage.setAccountId(accountId);
		bFBPage.setIsSelected(0);
		bFBPage.setFirstName(userFirstName);
		bFBPage.setLastName(userLastName);
		bFBPage.setLink(pgInfo.getLink());
		bFBPage.setHandle(pgInfo.getHandle());
		bFBPage.setFacebookPageId(pgInfo.getId());
		bFBPage.setPictureUrl(userPictureUrl);
		bFBPage.setFacebookPagePictureUrl(pgInfo.getPicture());
		bFBPage.setSingleLineAddress(pgInfo.getAddress());
		bFBPage.setFacebookPageName(pgInfo.getName());
		if(pgInfo.getId() == null ){
			bFBPage.setIsValid(0);
			brokenIntegrationService.pushValidIntegrationStatus(bFBPage.getEnterpriseId(),SocialChannel.FACEBOOK.getName(), bFBPage.getId(),bFBPage.getIsValid(),bFBPage.getFacebookPageId());
			commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(bFBPage), bFBPage.getUserId(),bFBPage.getBusinessId(), bFBPage.getEnterpriseId());
		} else {
			bFBPage.setIsValid(1);
		}

	}

	@Override
	public String getFBAccessToken(String pageId, String baseUrl, String extendedToken) throws Exception {
		return fbService.getPermToken(pageId, extendedToken, baseUrl);
	}

	private String replaceLimit(String input) {
		String pageLimit = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("social.fb.pages.limit", "100");
		return (input.replace("limit=25", "limit="+pageLimit));
	}

	private void getPageInfo(FbPage page, Map<String, List<FacebookPageInfo>> pageToPagePageInfo, String key) {
		if (page != null) {
			if (pageToPagePageInfo.get(key) != null) {
				List<FacebookPageInfo> list = prepareFacebookPageInfo(page.getData());
				if (list != null) {
					pageToPagePageInfo.get(key).addAll(list);
				}
			} else {
				List<FacebookPageInfo> list = prepareFacebookPageInfo(page.getData());
				if (list != null) {
					pageToPagePageInfo.put(key, list);
				}
			}
		}
	}

	private List<FacebookPageInfo> prepareFacebookPageInfo(List<FacebookPageInfo> data) {
		List<FacebookPageInfo> outList = null;
		if (CollectionUtils.isNotEmpty(data)) {
			outList = new ArrayList<>();
			for (FacebookPageInfo fpi : data) {
				FacebookPageInfo out = new FacebookPageInfo();
				out.setId(fpi.getId());
				out.setLink(fpi.getLink());
				out.setName(fpi.getName());
				out.setPicture(fpi.getPicture());
				out.setSingle_line_address(extractSingleLineAddressFromLocation(fpi));
				out.setHandle(fpi.getHandle());
				out.setAccess_token(fpi.getAccess_token());
				outList.add(out);
			}
		}
		return outList;

	}

	@Override
	public Integer getBusinessSelectedPagesCount(Long enterpriseId)
	{
		Integer pagesCount=socialFBPageRepository.countByEnterpriseIdAndIsSelected(enterpriseId, 1);
		return (pagesCount!=null?pagesCount:0);
	}
	
	@Override
	public void updateFacebookPageInvalidStatus(String fbPageId,Integer inValidStatus)
	{
		LOGGER.info("Marking FB page {} invalid", fbPageId);
		try
		{
			List<BusinessFBPage> fbPages=socialFBPageRepository.findByFacebookPageId(fbPageId);
			if(CollectionUtils.isNotEmpty(fbPages))
			{
				fbPages.stream().forEach(fbPage->{
					fbPage.setIsValid(inValidStatus);
					if(inValidStatus.equals(0)) {
						brokenIntegrationService.pushValidIntegrationStatus(fbPage.getEnterpriseId(),SocialChannel.FACEBOOK.getName(),fbPage.getId(),0,fbPage.getFacebookPageId());
					}
					fbPage.setUpdatedAt(new Date());
					socialFBPageRepository.saveAndFlush(fbPage);
					pushToKafkaForValidity(Constants.FACEBOOK, Arrays.asList(fbPageId) );
					commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(fbPage), fbPage.getUserId(),fbPage.getBusinessId(), fbPage.getEnterpriseId());
				});
				
			}
			else
			{
				LOGGER.error("No Fb Page found for page id {}",fbPageId);
			}
		}
		catch(Exception exe)
		{
			LOGGER.error("Error while updating the invalid status for page id {}",fbPageId);
		}

	}

	@Override
	public void updateFacebookPageIsSelectedStatus(String fbPageId, Integer isSelected) {
		try {
			List<BusinessFBPage> fbPages = socialFBPageRepository.findByFacebookPageId(fbPageId);
			if (CollectionUtils.isNotEmpty(fbPages)) {
				fbPages.stream().forEach(fbPage -> {
					fbPage.setIsSelected(isSelected);
					fbPage.setEnterpriseId(null);
					socialFBPageRepository.saveAndFlush(fbPage);
				});

			} else {
				LOGGER.error("No Fb Page found for page id {}", fbPageId);
			}
		} catch (Exception exe) {
			LOGGER.error("Error while updating the Selected status for page id {}", fbPageId);
		}

	}

	private void updateExistingFbPages(List<BusinessFBPage> existingFBPages, String requestId, Map<String, FbPageInfo> pages,
									   FbUserProfileInfo user, Business business, String userEmailId, String userAccessToken, Boolean isSystemUserRequest,
									   Boolean isOpenUrl) {
		LOGGER.info("inside updateExistingFbPages: {}",requestId);
		Set<String> oldRequestIds = new HashSet<>();
		existingFBPages.stream().forEach(oldpage -> {
			oldRequestIds.add(oldpage.getRequestId());
			oldpage.setRequestId(requestId);
			oldpage.setUpdatedBy(null);
			if( isSystemUserRequest || oldpage.getEnterpriseId() == null) {
				FbPageInfo fbPage = pages.get(oldpage.getFacebookPageId());
				oldpage.setFacebookPageName(fbPage.getName());
				oldpage.setHandle(fbPage.getHandle());
				oldpage.setIsValid(1);
				oldpage.setLink(fbPage.getLink());
			    oldpage.setPageAccessToken(fbPage.getAccessToken());
				oldpage.setPictureUrl(fbPage.getPicture());
				oldpage.setSingleLineAddress(fbPage.getAddress());
				oldpage.setFirstName(user.getFirst_name());
				oldpage.setLastName(user.getLast_name());
				oldpage.setUserId(user.getId());
				oldpage.setPrimaryPhone(fbPage.getPhone());
				oldpage = updateFacebookRoleNew(oldpage, pages);
				oldpage.setUserEmailId(userEmailId);
			}

		});

		String tokenPermissions = null;
		DebugTokenResponse tokenResponse = null;
		LOGGER.info("Fetching token permission for userAccessToken: {}",userAccessToken);
		try {
			tokenResponse = getPageToken(business, userAccessToken);
			if (Objects.nonNull(tokenResponse) && Objects.nonNull(tokenResponse.getData())) {
				tokenPermissions = String.join(",", tokenResponse.getData().getScopes());
			}
		} catch (Exception e) {
			LOGGER.error("For page id {} Error while saving the data ", userAccessToken, e);
		}
		String finalTokenPermissions = tokenPermissions;
		DebugTokenResponse finalTokenResponse = tokenResponse;
		existingFBPages.forEach(c -> {

			c.setPagePermissions(finalTokenPermissions);
			if(Objects.nonNull(finalTokenResponse) && Objects.nonNull(finalTokenResponse.getData())) {
				List<String> granularPermissions = commonService.getFilteredScopeForPage(finalTokenResponse, Collections.singletonList(c.getFacebookPageId()));
				c.setGranularPagePermissions(String.join(",", granularPermissions));
			}
			commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.UPDATE_PAGE.name(), Arrays.asList(c),
					String.valueOf(user.getId()), c.getBusinessId(), c.getEnterpriseId());

		});
		LOGGER.info("Existing page data being saved for pages: {}",existingFBPages.size());
		socialFBPageRepository.save(existingFBPages);
		existingFBPages.forEach(businessFBPage -> commonService.uploadPageImageToCDN(businessFBPage));
		List fbPagesIds = existingFBPages.stream().map(BusinessFBPage::getFacebookPageId).collect(Collectors.toList());
		pushToKafkaForValidity(Constants.FACEBOOK, fbPagesIds);
		socialFBPageRepository.flush();
		if(CollectionUtils.isNotEmpty(oldRequestIds)) { // send event to check invalid get page requests and mark them as cancelled
			kafkaProducer.sendObjectV1(VALIDATE_PAGE_REQUEST, new CheckInvalidGetPageState(Constants.FACEBOOK, oldRequestIds));
		}
	}



	@Override
	public void manualFetch(Integer bgpId) {
		try {
		BusinessGetPageRequest bgpr = businessGetPageReqRepo.findByIdAndStatusAndRequestType(bgpId, "cancel", "connect");
		if(null == bgpr) {
			LOGGER.info("No business find request found with id {}", bgpId);
			return;
		}
		Long parentId = null;
		String type = null;
		if (bgpr.getEnterpriseId() !=null) {
			parentId = bgpr.getEnterpriseId();
			type = Constants.ENTERPRISE;
		} else if(bgpr.getResellerId() !=null){
			parentId = bgpr.getResellerId();
			type = Constants.RESELLER;
		} else {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Invalid Request");
		}
		Business business = businessRepo.findByBusinessId(parentId);
		FbUserProfileInfo user = fbService.getUserDetails(bgpr.getUserAccessToken());
		fetchPages(bgpr, bgpr.getUserAccessToken(), user, business, type, parentId, false); // donot want it to be asyn here.
		
		} catch (Exception e) {
			LOGGER.error("Exception while manually fetching fb pages {}", e);
		}
		
	}


	@Override
	public void manualReconnect(Integer bgpId) {
		try {
			BusinessGetPageRequest bgpr = businessGetPageReqRepo.findByIdAndStatusAndRequestType(bgpId, "complete", "reconnect");
			if(null == bgpr) {
				LOGGER.info("No business find request found with id {}", bgpId);
				return;
			}
			Long parentId = null;
			List<String> fbPageIds = new ArrayList<>();
			if (bgpr.getEnterpriseId() !=null) {
				parentId = bgpr.getEnterpriseId();
				fbPageIds = socialFBPageRepository.findByEnterpriseIdAndIsInValid(parentId);
			} else if(bgpr.getResellerId() !=null){
				parentId = bgpr.getResellerId();
				fbPageIds = socialFBPageRepository.findByResellerIdAndIsInValid(parentId);
			} else {
				throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Invalid Request");
			}
			Business business = businessRepo.findByBusinessId(parentId);
			FbUserProfileInfo user = fbService.getUserDetails(bgpr.getUserAccessToken());

			String baseUrl = getFacebookGraphApiBaseUrl();

			getFbPagesAndReconnect(business, fbPageIds, bgpr.getBirdeyeUserId(), bgpr.getUserAccessToken(), baseUrl, user, bgpr);

		} catch (Exception e) {
			LOGGER.error("Exception while manually fetching fb pages {}", e);
		}

	}

	@Async
	@Override
	public void fetchPages(BusinessGetPageRequest request, String extendedToken, FbUserProfileInfo user, Business business, String type, Long parentId, Boolean isSystemUserRequest) throws BirdeyeSocialException, Exception {
		String baseUrl = getFacebookGraphApiBaseUrl();
		Integer pageCount = 0;
		Integer totalCount = 0;
		String key = SocialChannel.FACEBOOK.getName().concat(String.valueOf(parentId));
		try {
			Map<String, FbPageInfo> pages = getPageDetailsMapV1(extendedToken, user.getId(),SocialChannel.FACEBOOK.getName());
			LOGGER.info("For user profile {} and business {} Total pages are {}", request.getSocialUserId(), request.getEnterpriseId(), (!pages.isEmpty() ? pages.size() : 0));
			if (pages != null && !pages.isEmpty()) {
				// get Existing pages form DB
				List<BusinessFBPage> existingFBPages = socialFBPageRepository.findByFacebookPageIdIn(new ArrayList<>(pages.keySet()));
				Map<String,BusinessFBPage> existingFBPagesMap = existingFBPages.stream().collect(Collectors.toMap(BusinessFBPage :: getFacebookPageId, Function.identity()));
				LOGGER.info("Existing page found in DB: {}",existingFBPages.size());
				if (CollectionUtils.isNotEmpty(existingFBPages)) {
					updateExistingFbPages(existingFBPages, request.getId().toString(), pages, user,
							business, request.getEmail(), request.getUserAccessToken(), isSystemUserRequest, false);

					List pageIds = existingFBPages.stream().map(BusinessFBPage::getFacebookPageId).collect(Collectors.toList());
					pushToKafkaForValidity(Constants.FACEBOOK, pageIds);
				}
				LOGGER.info("Processing done for existing pages");
				List<FbPageInfo> newFBPages = new ArrayList<>();
				for (Entry<String, FbPageInfo> entry : pages.entrySet()) {
					if (!existingFBPagesMap.containsKey(entry.getKey())) {
						newFBPages.add(entry.getValue());
					}
				}
				LOGGER.info("New page found: {}",newFBPages.size());
				if(CollectionUtils.isNotEmpty(newFBPages)) {
					pageCount = newFBPages.size();
					totalCount = pageCount;
					getAccessTokenAndSave(newFBPages, user, baseUrl, extendedToken, Integer.valueOf(request.getId()), request.getBirdeyeUserId(), business, request.getEmail(), request.getUserAccessToken());
				}
				if(CollectionUtils.isNotEmpty(existingFBPages))
					totalCount += existingFBPages.size();
			}
			request.setPageCount(pageCount);
			request.setTotalPages(totalCount);
			if(Objects.isNull(request.getTotalPages()) || request.getTotalPages() == 0){
				request.setStatus(Status.NO_PAGES_FOUND.getName());
				redisService.release(key);
			}else{
				request.setStatus(Status.FETCHED.getName());
			}
			LOGGER.info("Saving info for reqId: {}",request.getId());
			pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(), request.getRequestType(), request.getStatus(), parentId);
			getReqRepo.saveAndFlush(request);
		} catch (Exception ex) {
			redisService.release(key);
			LOGGER.error("[Redis Lock] (Facebook) Lock released for business {}, error {}", parentId, ex);
			BusinessGetPageRequest req = checkInProgressBusinessRequests(parentId, type);
			if(req!=null){
				req.setStatus(Status.CANCEL.getName());
				req.setErrorLog(ex.getMessage()!=null?ex.getMessage().substring(0, Math.min(ex.getMessage().length(), 4000)):null);
				businessGetPageReqRepo.saveAndFlush(req);
				pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),req.getRequestType(),Status.COMPLETE.getName(), parentId,true);
			}else{
				pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),"connect",Status.COMPLETE.getName(), parentId,true);
			}
		}
	}

	@Async
	 void addPageDetailsToTemp(List<FacebookPageInfo> pages, String userId) {
		try {

			boolean fbTempFlag = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("social.temp.fb.page.enable", "true").equalsIgnoreCase("true");

			if(!fbTempFlag) {
				LOGGER.info("Not saving data in temp fb table");
				return;
			}

			List<FbPageInfo> fbPageInfos = pages.stream()
					.map(page -> getFacebookPageInfoFromFbPage(page)).collect(Collectors.toList());
			List<TempBusinessFBPage> businessFBPageList = new ArrayList<>();
			fbPageInfos.forEach(v -> {
				TempBusinessFBPage tempBusinessFBPage = new TempBusinessFBPage();

				tempBusinessFBPage.setFacebookPageId(v.getId());
				tempBusinessFBPage.setFacebookPageName(v.getName());
				tempBusinessFBPage.setPageAccessToken(v.getAccessToken());
				tempBusinessFBPage.setLink(v.getLink());
				tempBusinessFBPage.setFacebookPagePictureUrl(v.getPicture());
				tempBusinessFBPage.setHandle(v.getHandle());
				tempBusinessFBPage.setSingleLineAddress(v.getAddress());
				tempBusinessFBPage.setPrimaryPhone(v.getPhone());
				tempBusinessFBPage.setIsSelected(0);
				tempBusinessFBPage.setIsValid(0);
				tempBusinessFBPage.setUserId(userId);


				businessFBPageList.add(tempBusinessFBPage);
			});

			tempFBPageRepository.save(businessFBPageList);

		} catch (Exception ex) {
			LOGGER.error("Something went wrong while adding pages to temp fb {}, error {}", pages,  ex.getMessage());

		}
	}


	@Override
	public Map<String, FbPageInfo> getPageDetailsMapV1(String extendedToken, String userId, String channel)  {
		Map<String, FbPageInfo> pages = new HashMap<String,FbPageInfo>();
		try {
			List<FbPage> pageList = getPageDetailsListV1(extendedToken, userId,channel);
			//List<FbPage> pageList = fbService.getPageDetailsListV1(extendedToken, userId,channel);
			pages = pageList.stream().flatMap(l -> l.getData().stream())
					.map(page -> getFacebookPageInfoFromFbPage(page)).collect(Collectors.toMap(FbPageInfo::getId, Function.identity(),(x,y)-> x));
			 LOGGER.info("[Facebook] Total page size  for User {} with token {} is {}",userId,extendedToken,pages.size());
		} catch (Exception e) {
			LOGGER.error("[Facebook] Error in fetching pages {} ",e.getMessage());
			
		}
		return pages;
	}

	@Override
	public String extractSingleLineAddressFromLocation(FacebookPageInfo fbPageInfo) {
		if(Objects.nonNull(fbPageInfo) && Objects.nonNull(fbPageInfo.getLocation())) {
			FBPageInput.Location location = fbPageInfo.getLocation();
			String singleLineAddress = "";
			if(StringUtils.isNotEmpty(location.getStreet())) {
				singleLineAddress += location.getStreet();
			}

			if(StringUtils.isNotEmpty(location.getCity())) {
				if(StringUtils.isNotEmpty(singleLineAddress)) {
					singleLineAddress += ","+" ";
				}
				singleLineAddress += location.getCity();
			}

			if(StringUtils.isNotEmpty(location.getState())) {
				if(StringUtils.isNotEmpty(singleLineAddress)) {
					singleLineAddress += ","+" ";
				}
				singleLineAddress += location.getState();
			}

			if(StringUtils.isNotEmpty(location.getZip())) {
				if(StringUtils.isNotEmpty(singleLineAddress)) {
					singleLineAddress += " ";
				}
				singleLineAddress += location.getZip();
			}
			return singleLineAddress;
		}
		return null;
	}

	@Override
	public List<FbPage> getPageDetailsListV1(String accessToken, String userId,String channel) {
		// Parent pages
		List<FbPage> parentPages = getPageDetailsList(accessToken, userId, channel);
		List<FbPage> allPages = new ArrayList<FbPage>();
		allPages.addAll(parentPages);
		//TODO: We need to filter pages where child pages are not present.
		// Child Pages.
		parentPages.stream().flatMap(fbp -> fbp.getData().stream()).forEach(p -> {
			List<FbPage> childPage;
			try {
				childPage = getChildPageDetailsList(p.getId(), p.getAccess_token(),channel, userId);
				allPages.addAll(childPage);
			} catch (Exception e) {
				LOGGER.error("Exception while fetching  child pages for page {}", p.getId(), e);
			}
		});
		return allPages;
	}

	//TODO: add validation for requestId.
	@Override
	public void updateAccessToken(Long enterpriseId, Integer lastReconnectReqId) {
		BusinessGetPageRequest lastReconnectRequests = businessGetPageReqRepo.findOne(lastReconnectReqId);

		if(Objects.isNull(lastReconnectRequests) || !"reconnect".equalsIgnoreCase(lastReconnectRequests.getRequestType())){
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST,"Request Id doesn't match the latest request id for request type reconnect");
		}

		List<BusinessFBPage> facebookPages = socialFBPageRepository.findByEnterpriseIdAndIsValid(enterpriseId,0);
		List<String> pageIds = new ArrayList<>();
 		if(CollectionUtils.isEmpty(facebookPages)){
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST,"No invalid page found for the given enterprise "+enterpriseId);
		}
		try {
			FbUserProfileInfo user = fbService.getUserDetails(lastReconnectRequests.getUserAccessToken());
			Business business = businessRepo.findByBusinessId(enterpriseId);
			Boolean updateScope =  businessUtilsService.isUpdateScopeEnabled(enterpriseId);
			facebookPages.forEach(page -> {
				try {
					Map<String, String> response = fbService.getFacebookAccessTokenByPageId(page.getFacebookPageId(), lastReconnectRequests.getUserAccessToken());
					String accessToken = response.get("access_token");
					if (StringUtils.isNotEmpty(accessToken)) {
						String permissions = getPageTokenPermissions(business, accessToken, Collections.singletonList(page.getFacebookPageId()));
						if(StringUtils.isNotEmpty(permissions)) {
							page.setPagePermissions(permissions);
						}
						if(updateScope) {
							List<FbRoleInfo> fbRoleInfos = fbService.getTasksForPage(accessToken,page.getFacebookPageId());
							if(CollectionUtils.isNotEmpty(fbRoleInfos)){
								fbRoleInfos.forEach(role -> {
									if(role.isActive() && Objects.equals(role.getId(),lastReconnectRequests.getSocialUserId())){
										updateFacebookTasksAndRole(role.getTasks(),page);
									}
								});
							}
						}
						page.setIsValid(1);
						if (!user.getId().equalsIgnoreCase(page.getUserId())) {
							page.setUserId(user.getId());
							page.setFirstName(user.getFirst_name());
							page.setLastName(user.getLast_name());
							page.setPictureUrl(user.getPicture().getUrl());
						}
						page.setUpdatedBy(lastReconnectRequests.getBirdeyeUserId());
						page.setUpdatedAt(new Date());
						page.setRequestId(String.valueOf(lastReconnectReqId));
						page.setPageAccessToken(accessToken);
						socialFBPageRepository.save(page);
						//TODO: to get it reviewed
						performPostMappingAction(page);
						pageIds.add(page.getFacebookPageId());
						//validity check event

					}
				} catch (Exception e) {
					LOGGER.info("Unable to fetch access token for page id : {}", page.getFacebookPageId());
				}
			});
			pushToKafkaForValidity(SocialChannel.FACEBOOK.getName(),pageIds);
		}catch (Exception e){
			LOGGER.info("Unknown exception occurred while fetching data for user with request id : {}",lastReconnectReqId);
		}
	}

	@Override
	public void addPageDetails(FBConnectManualRequest request) {
		try {
			BusinessGetPageRequest businessGetPageRequest = businessGetPageReqRepo.findOne(request.getRequestId());
			if(Objects.isNull(businessGetPageRequest) || !"connect".equalsIgnoreCase(businessGetPageRequest.getRequestType())) {
				throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST,"Request Id doesn't match the request id for request type");
			}

			Business business = businessRepo.findByBusinessId(businessGetPageRequest.getEnterpriseId());
			List<BusinessFBPage> existingPages = socialFBPageRepository.findByFacebookPageIdIn(request.getPagesIds());

			request.getPagesIds().forEach(pageId -> {
				try {
					FacebookPageInfo page = proxyHandler.runWithRetryable(() ->
							fbService.getPageDetailsByPageId(pageId, businessGetPageRequest.getUserAccessToken()));

					if(Objects.isNull(page.getAccess_token())) {
						LOGGER.info("No page access token provided for {}", page);
						return;
					}

					Optional<BusinessFBPage> p = existingPages.stream().filter(v -> v.getFacebookPageId().equalsIgnoreCase(pageId)).findFirst();
					if(p.isPresent()) {
						BusinessFBPage existingPage = p.get();
						if (StringUtils.isEmpty(existingPage.getPageAccessToken()) || Objects.nonNull(existingPage.getEnterpriseId())) return;
						BusinessFBPage fbPage = convertPageDetailsToDB(business, businessGetPageRequest, existingPage, page);
						socialFBPageRepository.saveAndFlush(fbPage);
						return;
					}
					BusinessFBPage newPage = new BusinessFBPage();

					newPage = convertPageDetailsToDB(business, businessGetPageRequest, newPage, page);
					socialFBPageRepository.saveAndFlush(newPage);
					return;

				} catch (Exception e) {
					LOGGER.info("Unable to fetch access token for page id : {}", pageId);
				}
			});
			pushToKafkaForValidity(SocialChannel.FACEBOOK.getName(), request.getPagesIds());
		} catch (Exception ex) {
			LOGGER.info("Something went wrong while adding details for request {} with error {}", request, ex);
		}
	}

	private BusinessFBPage convertPageDetailsToDB(Business business, BusinessGetPageRequest pageRequest, BusinessFBPage fbPage, FacebookPageInfo data) {
		try {
			fbPage.setFacebookPageId(data.getId());
			fbPage.setFacebookPageName(data.getName());
			fbPage.setHandle(data.getUsername());
			fbPage.setLink(data.getLink());
			if(Objects.nonNull(data.getPicture()) && Objects.nonNull(data.getPicture().getData())) {
				fbPage.setFacebookPagePictureUrl(data.getPicture().getData().getUrl());
			}
			fbPage.setSingleLineAddress(extractSingleLineAddressFromLocation(data));
			fbPage.setPageAccessToken(data.getAccess_token());
			fbPage.setUserEmailId(CollectionUtils.isNotEmpty(data.getEmails()) ? data.getEmails().get(0): null);
			fbPage.setPrimaryPhone(data.getPhone());
			fbPage.setIsValid(1);
			fbPage.setIsSelected(1);
			fbPage.setRequestId(pageRequest.getId().toString());
			fbPage.setEnterpriseId(pageRequest.getEnterpriseId());
			fbPage.setAccountId(business.getId());
			fbPage.setResellerId(pageRequest.getResellerId());
			fbPage.setCreatedBy(pageRequest.getBirdeyeUserId());
			fbPage.setUpdatedBy(pageRequest.getBirdeyeUserId());
//			fbPage.setUserId(pageRequest.getSocialUserId());

			String permissions = getPageTokenPermissions(business, data.getAccess_token(), Collections.singletonList(fbPage.getFacebookPageId()));
			fbPage.setPagePermissions(permissions);

			updateFacebookRole(fbPage);

			FbUserProfileInfo user = fbService.getUserDetails(pageRequest.getUserAccessToken());
			if (!user.getId().equalsIgnoreCase(fbPage.getUserId())) {
				fbPage.setUserId(user.getId());
				fbPage.setFirstName(user.getFirst_name());
				fbPage.setLastName(user.getLast_name());
				fbPage.setPictureUrl(user.getPicture().getUrl());
			}

		} catch (Exception ex) {
			LOGGER.info("Page details cannnot be updated {}", ex);
		}
		return fbPage;
	}



	public BusinessFBPage updateFacebookRoleAdmin(BusinessFBPage oldpage,  List<String> tasks) {
		try {
			List<String> responseR = tasks;

			List<RoleMapping> roleMappings = iRoleMappingService.getDataByChannel(SocialChannel.FACEBOOK.getName());
			Map<String, RoleMapping> roleMappingsMap = roleMappings.stream().collect(Collectors.toMap(RoleMapping:: getRole, Function.identity()));

			if (responseR != null ) {
				BusinessFBPage finalOldpage = oldpage;

				String allPermission = String.join( ",", responseR);
				RoleMapping fetchedRole = new RoleMapping();
				for (RoleMapping roleMapping: roleMappings) {
					if (allPermission.contains(roleMapping.getUniqueTask())) {
						fetchedRole = roleMapping;
						break;
					}
				}
				LOGGER.info("Role fetched: {} for business: {}",fetchedRole.getRole(),oldpage.getEnterpriseId());
				oldpage.setScope(StringUtils.left((fetchedRole.getRole() + " - (" + allPermission + ")"), 255));
				if (fetchedRole.getErrorCode() == null) {
					finalOldpage.setIsManaged(1);
				} else {
					finalOldpage.setIsManaged(0);
				}

				if(finalOldpage.getIsManaged() == null) {
					String defaultRole = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultFbInstaRole();
					oldpage.setScope(StringUtils.left((defaultRole + " - (" + StringUtils.join(roleMappingsMap.get(defaultRole).getTasks(),",") + ")"), 255));
					oldpage.setIsManaged(1);
				}
			} else {
				String defaultRole = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultFbInstaRole();
				oldpage.setScope(StringUtils.left((defaultRole + " - (" + StringUtils.join(roleMappingsMap.get(defaultRole).getTasks(),",") + ")"), 255));
				oldpage.setIsManaged(1);
			}
		} catch (Exception ex) {
			LOGGER.warn("Error in fetching role for Facebook page id: {}",oldpage.getFacebookPageId());
		}
		return oldpage;
	}


	public List<FbPage> getPageDetailsList(String accessToken, String userId,String channel){
		FbPage page = getPageDetails(accessToken, userId,channel);
		List<FbPage> list = new ArrayList<>();
		if (page != null) {
			list.add(page);
			addPageDetailsToTemp(page.getData(), userId);
		}
		while (page != null && page.getPaging() != null && page.getPaging().getNext() != null) {
			page = getPageDetails(page.getPaging().getNext());
			if (page != null) {
				list.add(page);
				addPageDetailsToTemp(page.getData(), userId);
			}
		}
		return list;
	}

	private FbPage getPageDetails(String accessToken, String userId,String channel) {
		FbPage fbpage = proxyHandler.runWithConfigRetryableBirdeyeEx(() -> fbService.getPageDetails(accessToken, userId,channel) );
		return fbpage;
	}

	private FbPage getPageDetails(String nextUrl) {
		FbPage fbPage = proxyHandler.runWithConfigRetryableBirdeyeEx(() -> fbService.getPageDetails(nextUrl));
		return fbPage;
	}
	
	private List<FbPage> getChildPageDetailsList(String fbPageId, String pageAccessToken,String channel, String userId) throws Exception {
		FbPage page = fbService.getChildLoactionsFromFB(fbPageId, pageAccessToken,channel);
		int total = 0;
		List<FbPage> list = new ArrayList<>();
		if (page != null) {
			if (page.getData() != null && !page.getData().isEmpty()) {
				total = page.getData().size();
				list.add(page);
				addPageDetailsToTemp(page.getData(), userId);
			}
			
		}
		while (page != null && page.getPaging() != null && page.getPaging().getNext() != null) {
			String nextPageUrl = page.getPaging().getNext();
			LOGGER.info("Parent page Id {} and next Page Url {}  ", fbPageId, nextPageUrl);
			page = fbService.getPageDetails(nextPageUrl);
			if (page != null) {
				if (page.getData() != null && !page.getData().isEmpty()) {
					total += page.getData().size();
					list.add(page);
					addPageDetailsToTemp(page.getData(), userId);
				}
			
			}
		}
		if (total > 0) {
			LOGGER.info("Child Page size {} for parent page {}  ", total, fbPageId);

		} else {
			LOGGER.info("No Child Page found for parent page {} with token {} ", fbPageId, pageAccessToken);
		}
		return list;
	}

	@Override
	public FbUserProfileInfo getPagePhoneNumber(String extendedToken) {
		try {
			FbUserProfileInfo pageList = fbService.getPagePhoneNumber(extendedToken);
			return pageList;
		} catch (Exception e) {
			LOGGER.error("[Facebook] Error in fetching page phone number {} ",e.getMessage());
			return null;
		}
	}


	@Async
	@Override
	public void fetchPagesForOpenUrl(BusinessGetPageOpenUrlRequest request, String extendedToken, FbUserProfileInfo user, Business business,String firebaseKey) throws BirdeyeSocialException, Exception {
		String baseUrl = getFacebookGraphApiBaseUrl();
		Integer pageCount = 0;
		Integer totalCount = 0;
		String key = SocialChannel.FACEBOOK.getName().concat(String.valueOf(request.getFirebaseKey()));
		try {
			Map<String, FbPageInfo> pages = getPageDetailsMapV1(extendedToken, user.getId(),SocialChannel.FACEBOOK.getName());
			LOGGER.info("For user profile {} and business {} Total pages are {}", request.getSocialUserId(), request.getEnterpriseId(), (!pages.isEmpty() ? pages.size() : 0));
			if (pages != null && !pages.isEmpty()) {

				List<BusinessFBPage> existingFBPages = socialFBPageRepository.findByFacebookPageIdIn(new ArrayList<>(pages.keySet()));
				Map<String,BusinessFBPage> existingFBPagesMap = existingFBPages.stream().collect(Collectors.toMap(BusinessFBPage :: getFacebookPageId, Function.identity()));
				if (CollectionUtils.isNotEmpty(existingFBPages)) {
					updateExistingFbPages(existingFBPages,request.getId(),pages,user,business, request.getEmail(), request.getUserAccessToken(), false, true);
					List pageIds = existingFBPages.stream().map(BusinessFBPage::getFacebookPageId).collect(Collectors.toList());
					pushToKafkaForValidity(Constants.FACEBOOK,pageIds );
				}

				List<FbPageInfo> newFBPages = new ArrayList<>();
				for (Entry<String, FbPageInfo> entry : pages.entrySet()) {
					if (!existingFBPagesMap.containsKey(entry.getKey())) {
						newFBPages.add(entry.getValue());
					}
				}
				if(CollectionUtils.isNotEmpty(newFBPages)) {
					pageCount = newFBPages.size();
					totalCount = pageCount;
					getAccessTokenAndSaveForOpenUrl(newFBPages, user, baseUrl, extendedToken, request.getId(),null, business, request.getEmail());
				}
				LOGGER.info("post accesstoken");
				if(CollectionUtils.isNotEmpty(existingFBPages))
					totalCount += existingFBPages.size();
			}
			request.setPageCount(pageCount);
			request.setTotalPages(totalCount);

			if(Objects.isNull(request.getTotalPages()) || request.getTotalPages() == 0){
				request.setStatus(Status.NO_PAGES_FOUND.getName());
				nexusService.updateMapInFirebase("socialOpenUrl/"+ firebaseKey,firebaseKey ,Status.NO_PAGES_FOUND.getName());
			}else{
				request.setStatus(Status.FETCHED.getName());
				nexusService.updateMapInFirebase("socialOpenUrl/"+ firebaseKey,firebaseKey ,Status.FETCHED.getName());
			}
			redisService.release(key);
			businessGetPageOpenUrlReqRepo.saveAndFlush(request);
		} catch (Exception ex) {
			nexusService.updateMapInFirebase("socialOpenUrl/"+ firebaseKey,firebaseKey ,Status.CANCEL.getName());
			redisService.release(key);
			LOGGER.error("[Redis Lock] (Facebook) Lock released for business {}, error {}", request.getEnterpriseId(), ex);
			BusinessGetPageOpenUrlRequest req = checkInProgressRequestsOpenUrl(request.getEnterpriseId());
			if (req != null) {
				req.setStatus(Status.CANCEL.getName());
				businessGetPageOpenUrlReqRepo.saveAndFlush(req);
			}
		}
	}

	@Override
	public Map<String, FbPageInfo> getPageListMap(List<FbPage> pagesList, Map<String, String> pageToPageURL) {
		Map<String, FbPageInfo> pages = new HashMap<>();
		FbPageInfo fbPageInfo;
		for (FbPage page : pagesList) {
			List<FacebookPageInfo> pageInfo = page.getData();
			LOGGER.info("Number of Parent pages : {}", pageInfo.size());
			if (CollectionUtils.isEmpty(pageInfo)) {
				return pages;
			}
			for (FacebookPageInfo fbPage : pageInfo) {
				fbPageInfo = getFacebookPageInfoFromFbPage(fbPage);
				pages.put(fbPage.getId(), fbPageInfo);
				getChildPages(fbPage, pages, pageToPageURL);
			}
		}
		return pages;
	}

	private void getChildPages(FacebookPageInfo parent, Map<String, FbPageInfo> fbPages, Map<String, String> pageToPageURL) {
		FbPageInfo fbPageInfo;
		if (parent.getLocations() == null) {
			return;
		}
		// getting list for all pages
		if (pageToPageURL != null && parent.getLocations().getPaging() != null) {
			pageToPageURL.put(parent.getId(), parent.getLocations().getPaging().getNext());
		}

		for (FacebookPageInfo fbPage : parent.getLocations().getData()) {
			fbPageInfo = getFacebookPageInfoFromFbPage(fbPage);
			fbPages.put(fbPage.getId(), fbPageInfo);
			getChildPages(fbPage, fbPages, pageToPageURL);
		}
	}
	
	/**
	 * @param fbPage
	 * @return
	 */
	private FbPageInfo getFacebookPageInfoFromFbPage(FacebookPageInfo fbPage) {
		if (fbPage == null) {
			return null;
		}
		FbPageInfo fbPageInfo = new FbPageInfo();
		fbPageInfo.setId(fbPage.getId());
		fbPageInfo.setLink(fbPage.getLink());
		fbPageInfo.setName(fbPage.getName());
		fbPageInfo.setHandle(fbPage.getUsername());
		fbPageInfo.setPicture(fbPage.getPicture()!=null?fbPage.getPicture().getUrl():null);
		fbPageInfo.setAddress(extractSingleLineAddressFromLocation(fbPage));
		fbPageInfo.setAccessToken(fbPage.getAccess_token());
		fbPageInfo.setInstagramId(fbPage.getInstagram_business_account()!=null?fbPage.getInstagram_business_account().getId():null);
		fbPageInfo.setPhone(fbPage.getPhone());
		fbPageInfo.setTasks(fbPage.getTasks());
		return fbPageInfo;
	}
	
	private void getAccessTokenAndSave(List<FbPageInfo> pages, FbUserProfileInfo user, String baseUrl, String extendedToken, Integer requestId, Integer userId, Business business, String userEmailId, String userAccessToken) {
		try {
			if (CollectionUtils.isNotEmpty(pages)) {
				//Fetching permissions from Page Access Token
				//String tokenPermissions = getPageTokenPermissions(business, userAccessToken);
				//pages.parallelStream().filter(Objects::nonNull).forEach(p -> savePageData(p, user, baseUrl, extendedToken,requestId, userId, business, userEmailId, tokenPermissions));
				/*Map<String,List<String>> tokenPermissionMap = getUserPageTokenPermissions(business, userAccessToken);

				pages.parallelStream().filter(page -> page != null).forEach(p -> {
					String tokenPermissions=filterScopes(tokenPermissionMap,p.getId());
					savePageData(p, user, baseUrl, extendedToken,requestId, userId, business, userEmailId, tokenPermissions);
				});
				List fbPageIds = pages.stream().map(FbPageInfo::getId).collect(Collectors.toList());
				pushToKafkaForValidity(Constants.FACEBOOK,fbPageIds );*/
				DebugTokenResponse tokenResponse = getPageToken(business,userAccessToken);
				pages.parallelStream().filter(Objects::nonNull).forEach(p -> savePageData(p, user, baseUrl, extendedToken,requestId, userId, business, userEmailId, tokenResponse));
				List fbPageIds = pages.stream().map(FbPageInfo::getId).collect(Collectors.toList());
				pushToKafkaForValidity(Constants.FACEBOOK,fbPageIds );
			}
		} catch (Exception exe) {
			LOGGER.error("Error ", exe);
		}
	}

	private void getAccessTokenAndSaveForOpenUrl(List<FbPageInfo> pages, FbUserProfileInfo user, String baseUrl, String extendedToken, String requestId, Integer userId, Business business, String userEmailId) {
		try {
			if (CollectionUtils.isNotEmpty(pages)) {
				pages.parallelStream().filter(Objects::nonNull).forEach(p -> savePageDataForOpenUrl(p, user, baseUrl, extendedToken,requestId, userId, business, userEmailId));
				List fbPageIds = pages.stream().map(FbPageInfo::getId).collect(Collectors.toList());
				pushToKafkaForValidity(Constants.FACEBOOK,fbPageIds );
			}
		} catch (Exception exe) {
			LOGGER.error("Error {}", exe.getMessage());
		}
	}
	
	private void savePageData(FbPageInfo pgInfo, FbUserProfileInfo user, String baseUrl, String extendedToken, Integer requestId, Integer userId, Business business, String userEmailId, DebugTokenResponse tokenResponse) {
		try {
			BusinessFBPage bFBPage = new BusinessFBPage();
			prepareBusinessFBPage(bFBPage, pgInfo, user.getId(), user.getFirst_name(), user.getLast_name(), user.getPicture().getUrl(), null,requestId.toString(), userEmailId, null);
			if(Objects.nonNull(tokenResponse) && Objects.nonNull(tokenResponse.getData())) {
				bFBPage.setPagePermissions(String.join(",",tokenResponse.getData().getScopes()));
				List<String> granularPermissions = commonService.getFilteredScopeForPage(tokenResponse, Collections.singletonList(bFBPage.getFacebookPageId()));
				bFBPage.setGranularPagePermissions(String.join(",",granularPermissions));
			} else {
				bFBPage.setPagePermissions(null);
				bFBPage.setGranularPagePermissions(null);
			}
			bFBPage.setCreatedBy(userId);
			bFBPage.setUpdatedBy(userId);
			socialFBPageRepository.saveAndFlush(bFBPage);
			commonService.uploadPageImageToCDN(bFBPage);
			commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.ADD_PAGES.name(), Arrays.asList(bFBPage), String.valueOf(userId), bFBPage.getBusinessId(), business.getBusinessId());
		} catch (Exception exe) {
			socialFBPageRepository.updateRequestIdByFacebookPageId(requestId.toString(), pgInfo.getId());
			LOGGER.error("For page id {} Error while saving the data {}", pgInfo.getId(), exe.getMessage());
		}
	}

	private void savePageDataForOpenUrl(FbPageInfo pgInfo, FbUserProfileInfo user, String baseUrl, String extendedToken, String requestId, Integer userId, Business business, String userEmailId) {
		try {
			BusinessFBPage bFBPage = new BusinessFBPage();
			prepareBusinessFBPage(bFBPage, pgInfo, user.getId(), user.getFirst_name(), user.getLast_name(), user.getPicture().getUrl(), null,requestId, userEmailId, null);

			//Fetching permissions from Page Access Token
			DebugTokenResponse tokenResponse = getPageToken(business, bFBPage.getPageAccessToken());
			if(Objects.nonNull(tokenResponse) && Objects.nonNull(tokenResponse.getData())) {
				bFBPage.setPagePermissions(String.join(",", tokenResponse.getData().getScopes()));
				List<String> granularPermissions = commonService.getFilteredScopeForPage(tokenResponse,Collections.singletonList(bFBPage.getFacebookPageId()));
				bFBPage.setGranularPagePermissions(String.join(",",granularPermissions));
			} else {
				bFBPage.setPagePermissions(null);
				bFBPage.setGranularPagePermissions(null);
			}
			bFBPage.setCreatedBy(userId);
			bFBPage.setUpdatedBy(userId);
			socialFBPageRepository.saveAndFlush(bFBPage);
			commonService.uploadPageImageToCDN(bFBPage);
			commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.ADD_PAGES.name(), Arrays.asList(bFBPage), String.valueOf(userId), bFBPage.getBusinessId(), business.getBusinessId());
		} catch (Exception exe) {
			socialFBPageRepository.updateRequestIdByFacebookPageId(requestId, pgInfo.getId());
			LOGGER.error("For page id {} Error while saving the data {}", pgInfo.getId(), exe.getMessage());
		}
	}

	private void prepareBusinessFBPage(BusinessFBPage bFBPage, FbPageInfo pgInfo, String userId, String userFirstName, String userLastName,
									   String userPictureUrl, Long enterpriseId, String requestId, String userEmailId, Integer accountId) {
		bFBPage.setUserId(userId);

		// for enterprise setup during page fetching no need to save enterprise Id as during connect page api (selected page save) we will save enterprise id.
		if (enterpriseId != null) {
			bFBPage.setEnterpriseId(enterpriseId);
		}
		bFBPage.setAccountId(accountId);
		bFBPage.setIsSelected(0);
		bFBPage.setFirstName(userFirstName);
		bFBPage.setLastName(userLastName);
		bFBPage.setLink(pgInfo.getLink());
		bFBPage.setHandle(pgInfo.getHandle());
		bFBPage.setFacebookPageId(pgInfo.getId());
		bFBPage.setPictureUrl(userPictureUrl);
		bFBPage.setFacebookPagePictureUrl(pgInfo.getPicture());
		bFBPage.setSingleLineAddress(pgInfo.getAddress());
		bFBPage.setFacebookPageName(pgInfo.getName());
		bFBPage.setIsValid(1);
		bFBPage.setRequestId(requestId);
		bFBPage.setPageAccessToken(pgInfo.getAccessToken());
		bFBPage.setUserEmailId(userEmailId);
		bFBPage.setPrimaryPhone(pgInfo.getPhone());

	}

	private BusinessGetPageRequest checkInProgressBusinessRequests(Long parentId, String type) {
		LOGGER.info("Exception occurred in connect facebook, Checking for in progress request for business id {}", parentId);
		List<BusinessGetPageRequest> underProcessRequests = getRequestForBusiness(parentId, Status.INITIAL.getName(), type);
		if (CollectionUtils.isNotEmpty(underProcessRequests)) {
			return underProcessRequests.get(0);
		}
		return null;
	}

	private List<BusinessGetPageRequest> getRequestForBusiness(Long parentId, String status, String type) {

		return type == Constants.RESELLER ? businessGetPageReqRepo.findByResellerIdAndStatusAndChannel(parentId, status, SocialChannel.FACEBOOK.getName())
		: businessGetPageReqRepo.findByEnterpriseIdAndStatusAndChannel(parentId, status, SocialChannel.FACEBOOK.getName());
	}

	
	public void checkInvalidPages(Integer accountId) {
		LOGGER.info(
				"Checking Invalid facebook pages that are mapped to birdeye locations");

		long start = System.currentTimeMillis();

		// TODO: Pagination is slow, Try to use seek predicate.
		// https://blog.jooq.org/2013/10/26/faster-sql-paging-with-jooq-using-the-seek-method/
		List<BFP> pages = null;
		// Dataset to consider
		if (accountId == null) {
			// Look for 1000 invalid entries.
			pages = businessFbPageRepo.findInvalidPages(new PageRequest(0, 1000));
		} else {
			List<Integer> businessIds = businessService.getLocationIdsForAccount(accountId);
			pages = businessFbPageRepo.findInvalidPagesByBusinessIdIn(businessIds,new PageRequest(0, 5000));
		}
		
		LOGGER.info("Total invalid pages {} in {} seconds ", pages.size(),(System.currentTimeMillis() - start)/1000);
		start=System.currentTimeMillis();
		// Initializing fixed pool task executor. Should be shared pool for public usage.
		ExecutorService taskExecutor = Executors.newFixedThreadPool(20);
		CompletionService<Boolean> ecs = new ExecutorCompletionService<Boolean>(
				taskExecutor);
		
		pages.stream().forEach(bfp -> ecs.submit(() -> {
			return checkFacebookPageValidity(bfp);
		}));
		int successful = 0;
		int failure = 0;
		
		//Process Results.
		for (int i = 0; i < pages.size(); i++) {
			try {
				boolean result = ecs.take().get();
				if(result){
					successful++;
				}else{
					failure++;
				}
			} catch (InterruptedException | ExecutionException e) {
			}
		}
		LOGGER.info("Total Success {} & Failures {} in {} seconds",
				successful, failure,(System.currentTimeMillis() - start)/1000);
	}


	@Override
	public SocialAppCredsInfo getSocialAppCredsForBusiness(){
		SocialAppCredsInfo socialAppCreds = null;
		try {
			socialAppCreds = socialAppService.getFacebookAppSettings();
		} catch (Exception e) {
			LOGGER.error("[Facebook] Error in fetching social app creds {}",e);
		}
		return socialAppCreds;
	}
	
	@Override
	public boolean checkFacebookPageValidity(BFP fbPage) {
	
		SocialAppCredsInfo socialAppCreds = getSocialAppCredsForBusiness();
		if (socialAppCreds == null) {
			throw new BirdeyeSocialException("Facbook App settings are not available");
		}
		
		String appAccessToken = socialAppCreds.getChannelAccessToken();
		String pageAccessToken = fbPage.getAccessToken();
		
		DebugTokenResponse tokenResponse = null;

		try {
			tokenResponse = fbService.getTokenDetails(pageAccessToken, appAccessToken);
			if (tokenResponse.getData().getError() != null && 190 == tokenResponse.getData().getError().getCode()) {
				LOGGER.error("For fbpage: {}, DebugToken API error response:  {} ",fbPage.getFacebookPageId(),tokenResponse.getData().getError());
			}else{
				LOGGER.info("Integration is valid for {}",fbPage);
				return true;
			}
		}catch(Exception e){
			LOGGER.error("Error in calling FB API for fbpage {} with message {} ",fbPage.getFacebookPageId(),e.getMessage());
		}
		return false;
	}
	
	@Async
	@Override
	public void getFbPagesAndReconnect(Business business, List<String> fbPageIds, Integer userId, String extendedToken, String baseUrl, FbUserProfileInfo user, BusinessGetPageRequest request)
			throws Exception {
		try {
			Map<String, FbPageInfo> pages = getPageDetailsMapV1(extendedToken, user.getId(),SocialChannel.FACEBOOK.getName());

			boolean updateScope = businessUtilsService.isUpdateScopeEnabled(business.getBusinessId());
			Map<String, FbPageInfo> duplicatePages=new HashMap<>();
			duplicatePages.putAll(pages);
			duplicatePages.keySet().removeIf(fbPageIds::contains);
			List<String> facebookPageIds= duplicatePages.keySet().stream().collect(Collectors.toList());

			reconnectPagesAsync(updateScope,business.getBusinessId(),facebookPageIds,userId,request);
			List<BusinessFBPage> updatedRawFbPages = updateRawFacebookPages(business, pages, user, updateScope, userId, fbPageIds, request);
			if (CollectionUtils.isEmpty(updatedRawFbPages)) {
				LOGGER.info("No Raw Pages found for Reconnection process {}", business.getBusinessId());
			}

			List<FbPageInfo> newFBPages = new ArrayList<>();
			for (Entry<String, FbPageInfo> entry : pages.entrySet()) {
				newFBPages.add(entry.getValue());
			}
			
			request.setTotalPages(newFBPages.size());
		//	if(request.getPageCount().equals(0)) {
				//request.setStatus(Status.NO_PAGES_FOUND.getName());
		//	} else {
				request.setStatus(Status.COMPLETE.getName());
		//	}
			businessGetPageReqRepo.saveAndFlush(request);
			pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(), request.getRequestType(),request.getStatus(),request.getEnterpriseId());
			redisService.release(SocialChannel.FACEBOOK.getName().concat(String.valueOf(business.getBusinessId())));

		} catch (Exception e) {
			// Cleanup redis cache for error cases. 
			redisService.release(SocialChannel.FACEBOOK.getName().concat(String.valueOf(business.getBusinessId())));
			LOGGER.error("[Redis Lock] (Facebook Reconnect) Lock released for business {} and exception {}", business.getBusinessId(), e);
			//Cleanup business get pages request table
			BusinessGetPageRequest req = checkInProgressRequests(business.getBusinessId(), "reconnect");
			if (req != null) {
				req.setStatus(Status.CANCEL.getName());
				businessGetPageReqRepo.saveAndFlush(req);
			}
			pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(), req.getRequestType(), Status.COMPLETE.getName(),request.getEnterpriseId(),true);

		}
	}
	private void reconnectPagesAsync(boolean updateScope,Long businessId,List<String> pages,Integer userId,BusinessGetPageRequest request) {
		if (CollectionUtils.isEmpty(pages)) {
			return ;
		}
		LOGGER.info("Inside reconnectPagesAsync method");
		kafkaProducer.sendObject(Constants.RECONNECT_ACCESS_TOKEN,
				new AccessTokenUpdateRequest(updateScope,pages,userId,request.getId(),businessId));
	}

	@Async
	@Override
	public void getFbPagesAndReconnectForReseller(Business business, List<String> fbPageIds, Integer userId, String extendedToken, String baseUrl, FbUserProfileInfo user, BusinessGetPageRequest request)
			throws Exception {
		try {
			Map<String, FbPageInfo> pages = getPageDetailsMapV1(extendedToken, user.getId(),SocialChannel.FACEBOOK.getName());

			boolean updateScope = businessUtilsService.isUpdateScopeEnabled(business.getBusinessId());
			Map<String, FbPageInfo> duplicatePages=new HashMap<>();
			duplicatePages.putAll(pages);
			duplicatePages.keySet().removeIf(fbPageIds::contains);
			List<String> facebookPageIds= duplicatePages.keySet().stream().collect(Collectors.toList());
			reconnectPagesAsync(updateScope,business.getBusinessId(),facebookPageIds,userId,request);
			List<BusinessFBPage> updatedRawFbPages = updateRawFacebookPagesForReseller(business, pages, user, updateScope, userId, fbPageIds, request);
			if (CollectionUtils.isEmpty(updatedRawFbPages)) {
				LOGGER.info("No Raw Pages found for Reconnection process {}", business.getBusinessId());
			}

			List<FbPageInfo> newFBPages = new ArrayList<>();
			for (Entry<String, FbPageInfo> entry : pages.entrySet()) {
				newFBPages.add(entry.getValue());
			}

			request.setTotalPages(newFBPages.size());
			request.setStatus(Status.COMPLETE.getName());
			businessGetPageReqRepo.saveAndFlush(request);
			pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(), request.getRequestType(),request.getStatus(),request.getResellerId());
			redisService.release(SocialChannel.FACEBOOK.getName().concat(String.valueOf(business.getBusinessId())));
		} catch (Exception e) {
			// Cleanup redis cache for error cases.
			redisService.release(SocialChannel.FACEBOOK.getName().concat(String.valueOf(business.getBusinessId())));
			LOGGER.error("[Redis Lock] (Facebook Reconnect) Lock released for business {} and exception {}", business.getBusinessId(), e);
			//Cleanup business get pages request table
			BusinessGetPageRequest req = checkInProgressRequestsForReseller(business.getBusinessId(), "reconnect");
			if (req != null) {
				req.setStatus(Status.CANCEL.getName());
				businessGetPageReqRepo.saveAndFlush(req);
			}
			pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(), req.getRequestType(), Status.COMPLETE.getName(),request.getResellerId(),true);

		}
	}

	private List<BusinessFBPage> updateRawFacebookPages(Business business, Map<String, FbPageInfo> pages, FbUserProfileInfo user, boolean updateScope, Integer userId, List<String> fbPageIds, BusinessGetPageRequest request) throws Exception {
		if (MapUtils.isEmpty(pages)) {
			return Collections.emptyList();
		}
		Integer reconnectedCount = 0;
		List<BusinessFBPage> rawFbPage = socialFBPageRepository.findByFacebookPageIdIn(fbPageIds);
		List<BusinessFBPage> updatedPages = new ArrayList<>();
		for (BusinessFBPage page : rawFbPage) {
			FbPageInfo newFbPageInfo = pages.get(page.getFacebookPageId());
			if (newFbPageInfo != null) {
				page.setPageAccessToken(newFbPageInfo.getAccessToken());
				kafkaProducer.sendObject(Constants.UPDATE_ACCESS_TOKEN,
						new TokenUpdateRequest(page.getFacebookPageId(),page.getPageAccessToken(),
								SocialChannel.INSTAGRAM.getName(),page.getUserId()));
				page.setFacebookPageName(newFbPageInfo.getName());
				page.setFacebookPagePictureUrl(newFbPageInfo.getPicture());
				page.setHandle(newFbPageInfo.getHandle());
				page.setSingleLineAddress(newFbPageInfo.getAddress());
				page.setIsValid(1);
				if (!user.getId().equalsIgnoreCase(page.getUserId())) {
					page.setUserId(user.getId());
					page.setFirstName(user.getFirst_name());
					page.setLastName(user.getLast_name());
					page.setPictureUrl(user.getPicture().getUrl());
				}
				page.setCreatedBy(userId);
				page.setUpdatedBy(userId);
				page.setUpdatedAt(new Date());
				page.setRequestId(request.getId().toString());
				LOGGER.info("Starting Update Scope accessToken {} EnterpriseID {}", page.getPageAccessToken(), page.getEnterpriseId());
				if(updateScope)
				{
					LOGGER.info("Request received to Update Scope accessToken {} EnterpriseID {}", page.getPageAccessToken(), page.getEnterpriseId());
					page = updateFacebookRoleNew(page, pages);
					LOGGER.info("Request completed to Update Scope accessToken {} EnterpriseID {} scope {}", page.getPageAccessToken(), page.getEnterpriseId(), page.getScope());
				}
				String pageTokenPermissions = null;
				String granularPagePermission = null;
				DebugTokenResponse tokenResponse;
				try {
					tokenResponse = getPageToken(business, page.getPageAccessToken());
					if(Objects.nonNull(tokenResponse) && Objects.nonNull(tokenResponse.getData())) {
						pageTokenPermissions = String.join(",", tokenResponse.getData().getScopes());
						List<String> granularPermissionList = commonService.getFilteredScopeForPage(tokenResponse, Collections.singletonList(page.getFacebookPageId()));
						granularPagePermission = String.join(",",granularPermissionList);
					}

				} catch (Exception exe) {
					LOGGER.error("For page id {} Error while saving the token permissions {}", page.getFacebookPageId(), exe.getMessage());
				}
				page.setPagePermissions(pageTokenPermissions);
				page.setGranularPagePermissions(granularPagePermission);
				socialFBPageRepository.saveAndFlush(page);
				commonService.uploadPageImageToCDN(page);
				reconnectedCount++;
				facebookCacheService.getFacebookPageMetadataCached(page);
				updatedPages.add(page);

				SocialChannelSyncRequest socialChannelSyncRequest = new SocialChannelSyncRequest(SocialChannel.FACEBOOK.getName(),page.getFacebookPageId());
				kafkaProducer.sendObject(Constants.SOCIAL_PAGE_SYNC,socialChannelSyncRequest);

				commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.RECONNECT_PAGE.name(), Arrays.asList(page), String.valueOf(userId), page.getBusinessId(), page.getEnterpriseId());

				brokenIntegrationService.pushValidIntegrationStatus(page.getEnterpriseId(), SocialChannel.FACEBOOK.getName(),page.getId(),1,page.getFacebookPageId());

				performPostMappingAction(page);
			} else {
				//disconnected page not found in fetch pages list for reconnection
			}
		}
		request.setPageCount(reconnectedCount);
		pushToKafkaForValidity(Constants.FACEBOOK, fbPageIds);
		return updatedPages;
	}


	private List<BusinessFBPage> updateRawFacebookPagesForReseller(Business business, Map<String, FbPageInfo> pages, FbUserProfileInfo user, boolean updateScope, Integer userId, List<String> fbPageIds, BusinessGetPageRequest request) throws Exception {
		if (MapUtils.isEmpty(pages)) {
			return Collections.emptyList();
		}
		LOGGER.info("GET fbPageIds in updateRawFacebookPagesForReseller {}", fbPageIds);
		Integer reconnectedCount = 0;
		List<BusinessFBPage> rawFbPage = socialFBPageRepository.findByFacebookPageIdIn(fbPageIds);
		List<BusinessFBPage> updatedPages = new ArrayList<>();
		for (BusinessFBPage page : rawFbPage) {
			FbPageInfo newFbPageInfo = pages.get(page.getFacebookPageId());
			if (newFbPageInfo != null) {
				page.setPageAccessToken(newFbPageInfo.getAccessToken());
				kafkaProducer.sendObject(Constants.UPDATE_ACCESS_TOKEN,
						new TokenUpdateRequest(page.getFacebookPageId(),page.getPageAccessToken(),
								SocialChannel.INSTAGRAM.getName(),page.getUserId()));
				page.setFacebookPageName(newFbPageInfo.getName());
				page.setFacebookPagePictureUrl(newFbPageInfo.getPicture());
				page.setHandle(newFbPageInfo.getHandle());
				page.setSingleLineAddress(newFbPageInfo.getAddress());
				page.setIsValid(1);
				page.setUserEmailId(user.getEmail());
				if (!user.getId().equalsIgnoreCase(page.getUserId())) {
					page.setUserId(user.getId());
					page.setFirstName(user.getFirst_name());
					page.setLastName(user.getLast_name());
					page.setPictureUrl(user.getPicture().getUrl());
				}
				page.setCreatedBy(userId);
				page.setUpdatedBy(userId);
				page.setUpdatedAt(new Date());
				page.setRequestId(request.getId().toString());
				LOGGER.info("Starting Update Scope accessToken {} ResellerId {}", page.getPageAccessToken(), page.getResellerId());
				if(updateScope)
				{
					LOGGER.info("Request received to Update Scope accessToken {} ResellerId {}", page.getPageAccessToken(), page.getResellerId());
					page = updateFacebookRoleNew(page, pages);
					LOGGER.info("Request completed to Update Scope accessToken {} ResellerId {} scope {}", page.getPageAccessToken(), page.getResellerId(), page.getScope());
				}
				String pageTokenPermissions = null;
				String granularPagePermission = null;
				DebugTokenResponse tokenResponse;
				try {
					tokenResponse = getPageToken(business, page.getPageAccessToken());
					if(Objects.nonNull(tokenResponse) && Objects.nonNull(tokenResponse.getData())) {
						pageTokenPermissions = String.join(",", tokenResponse.getData().getScopes());
						List<String> granularPermissionList = commonService.getFilteredScopeForPage(tokenResponse, Collections.singletonList(page.getFacebookPageId()));
						granularPagePermission = String.join(",",granularPermissionList);
					}

				} catch (Exception exe) {
					LOGGER.error("For page id {} Error while saving the token permissions {}", page.getFacebookPageId(), exe.getMessage());
				}
				page.setPagePermissions(pageTokenPermissions);
				page.setGranularPagePermissions(granularPagePermission);
				socialFBPageRepository.saveAndFlush(page);
				reconnectedCount++;
				facebookCacheService.getFacebookPageMetadataCached(page);
				updatedPages.add(page);

				SocialChannelSyncRequest socialChannelSyncRequest = new SocialChannelSyncRequest(SocialChannel.FACEBOOK.getName(),page.getFacebookPageId());
				kafkaProducer.sendObject(Constants.SOCIAL_PAGE_SYNC,socialChannelSyncRequest);

				commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.RECONNECT_PAGE.name(), Arrays.asList(page),String.valueOf(user.getId()), page.getBusinessId(), page.getEnterpriseId());

				brokenIntegrationService.pushValidIntegrationStatus(page.getResellerId(), SocialChannel.FACEBOOK.getName(),page.getId(),1,page.getFacebookPageId());
				if(Objects.nonNull(page.getBusinessId()) && Objects.nonNull(page.getEnterpriseId())){
					performPostMappingAction(page);
				}
			} else {
				LOGGER.info("disconnected page not found in fetch pages list for reconnection");
			}
		}
		request.setPageCount(reconnectedCount);
		pushToKafkaForValidity(Constants.FACEBOOK, fbPageIds);
		return updatedPages;
	}

	private void subscribeFBWebhooks(BusinessFBPage page) {
		FacebookBaseResponse response = null;
		if(Objects.isNull(page.getPagePermissions())){
			return;
		}
		boolean messagingOn = page.getPagePermissions().contains(PAGES_MESSAGING);
		String subscriptionFields = messagingOn ? Constants.FB_MESSENGER_RATINGS_SUBSCRIPTION_FIELDS
				: Constants.FB_RATINGS_SUBSCRIPTION_FIELDS;
		subscriptionFields = subscriptionFields.concat(",").concat(Constants.FB_FEED_SUBSCRIPTION_FIELD);
		subscriptionFields = subscriptionFields.concat(",").concat(Constants.FB_MENTION_SUBSCRIPTION_FIELD);
		try {
			response = fbMsgService.fbPageSubscribeApps(page.getFacebookPageId(), page.getPageAccessToken(),
					subscriptionFields);
			if (response.isSuccess()) {
				LOGGER.info("FB webhook subscribed successfully for for businessId: {} and pageId: {}", page.getBusinessId(),
						page.getFacebookPageId());
				page.setRatingsOpted(1);
				if (messagingOn) {
					page.setMessengerOpted(1);
				}
				socialFBPageRepository.saveAndFlush(page);
			}
		} catch (Exception e) {
			LOGGER.error("FB webhook subscribe flow failed for businessId: {} and pageId: {}", page.getBusinessId(),
					page.getFacebookPageId());
		}
	}

	/**
	 * Method to check in progress request for a business in case an exception occurred
	 * @param businessId
	 * @param requestType 
	 * @return
	 */
	private BusinessGetPageRequest checkInProgressRequests(Long businessId, String requestType) {
		LOGGER.info("Exception occurred in connect facebook, Checking for in progress request for business id {}", businessId);
		List<BusinessGetPageRequest> underProcessRequests = businessGetPageReqRepo.findByEnterpriseIdAndStatusAndChannelAndRequestType(businessId, Status.INITIAL.getName(), SocialChannel.FACEBOOK.getName(), requestType);
		if (CollectionUtils.isNotEmpty(underProcessRequests)) {
			return underProcessRequests.get(0);
		}
		return null;
	}

	private BusinessGetPageRequest checkInProgressRequestsForReseller(Long resellerId, String requestType) {
		LOGGER.info("Exception occurred in connect facebook, Checking for in progress request for business id {}", resellerId);
		List<BusinessGetPageRequest> underProcessRequests = businessGetPageReqRepo.findByResellerIdAndStatusAndChannelAndRequestType(resellerId, Status.INITIAL.getName(), SocialChannel.FACEBOOK.getName(), requestType);
		if (CollectionUtils.isNotEmpty(underProcessRequests)) {
			return underProcessRequests.get(0);
		}
		return null;
	}

	public SocialFBPageRepository.IntegrationSummary getInValidAndTotalCount(List<Integer> businessIdList) {
		return socialFBPageRepository.getInvalidAndTotalCount(businessIdList).get(0);

	}

	@Override
	public BusinessFBPage updateFacebookRole(BusinessFBPage oldpage) {
		try {
			Permission responseR = fbService.fetchRole(oldpage.getFacebookPageId(),oldpage.getPageAccessToken());

			List<RoleMapping> roleMappings = iRoleMappingService.getDataByChannel(SocialChannel.FACEBOOK.getName());
			Map<String, RoleMapping> roleMappingsMap = roleMappings.stream().collect(Collectors.toMap(RoleMapping:: getRole, Function.identity()));

			if (responseR != null && responseR.getRoles() != null) {
				List<PermissionAccess> allAccess = responseR.getRoles().getData();
				BusinessFBPage finalOldpage = oldpage;
				allAccess.stream().forEach(access -> {
					String name = finalOldpage.getFirstName().concat(" ").concat(finalOldpage.getLastName());
					if (access.getName().equalsIgnoreCase(name)) {
						String allPermission = StringUtils.join(access.getTasks(), ",");
						RoleMapping fetchedRole = new RoleMapping();
						for (RoleMapping roleMapping: roleMappings) {
							if (allPermission.contains(roleMapping.getUniqueTask())) {
								fetchedRole = roleMapping;
								break;
							}
						}
						LOGGER.info("Role fetched: {} for business: {}",fetchedRole.getRole(),oldpage.getEnterpriseId());
						oldpage.setScope(StringUtils.left((fetchedRole.getRole() + " - (" + allPermission + ")"), 255));
						if (fetchedRole.getErrorCode() == null) {
							finalOldpage.setIsManaged(1);
						} else {
							finalOldpage.setIsManaged(0);
						}
					}
				});
				if(finalOldpage.getIsManaged() == null) {
					String defaultRole = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultFbInstaRole();
					oldpage.setScope(StringUtils.left((defaultRole + " - (" + StringUtils.join(roleMappingsMap.get(defaultRole).getTasks(),",") + ")"), 255));
					oldpage.setIsManaged(1);
				}
			} else {
				String defaultRole = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultFbInstaRole();
				oldpage.setScope(StringUtils.left((defaultRole + " - (" + StringUtils.join(roleMappingsMap.get(defaultRole).getTasks(),",") + ")"), 255));
				oldpage.setIsManaged(1);
			}
		} catch (Exception ex) {
			LOGGER.warn("Error in fetching role for Facebook page id: {}",oldpage.getFacebookPageId());
		}
		return oldpage;
	}

	public BusinessFBPage updateFacebookRoleNew(BusinessFBPage oldpage,  Map<String, FbPageInfo> pages) {
		if(CollectionUtils.isNotEmpty(pages.values()) && Objects.nonNull(pages.get(oldpage.getFacebookPageId()).getTasks())) {
			String[] responseR = pages.get(oldpage.getFacebookPageId()).getTasks();
			return updateFacebookTasksAndRole(responseR, oldpage);
		}
		return oldpage;
	}

	private BusinessFBPage updateFacebookTasksAndRole(String[] responseR, BusinessFBPage oldpage){
		List<RoleMapping> roleMappings = iRoleMappingService.getDataByChannel(SocialChannel.FACEBOOK.getName());
		Map<String, RoleMapping> roleMappingsMap = roleMappings.stream().collect(Collectors.toMap(RoleMapping::getRole, Function.identity()));
		try {
			if (responseR != null) {
				BusinessFBPage finalOldpage = oldpage;
				String allPermission = StringUtils.join(responseR, ",");
				RoleMapping fetchedRole = new RoleMapping();
				for (RoleMapping roleMapping : roleMappings) {
					if (allPermission.contains(roleMapping.getUniqueTask())) {
						fetchedRole = roleMapping;
						break;
					}
				}
				LOGGER.info("Role fetched: {} for business: {}", fetchedRole.getRole(), oldpage.getEnterpriseId());
				oldpage.setScope(StringUtils.left((fetchedRole.getRole() + " - (" + allPermission + ")"), 255));
				if (fetchedRole.getErrorCode() == null) {
					finalOldpage.setIsManaged(1);
				} else {
					finalOldpage.setIsManaged(0);
				}
				if (finalOldpage.getIsManaged() == null) {
					String defaultRole = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultFbInstaRole();
					oldpage.setScope(StringUtils.left((defaultRole + " - (" + StringUtils.join(roleMappingsMap.get(defaultRole).getTasks(), ",") + ")"), 255));
					oldpage.setIsManaged(1);
				}
			} else {
				String defaultRole = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultFbInstaRole();
				oldpage.setScope(StringUtils.left((defaultRole + " - (" + StringUtils.join(roleMappingsMap.get(defaultRole).getTasks(), ",") + ")"), 255));
				oldpage.setIsManaged(1);
			}
		} catch (Exception ex) {
			LOGGER.warn("Error in fetching role for Facebook page id: {}", oldpage.getFacebookPageId());
		}
		return oldpage;
	}

	@Override
	public void performPostMappingAction(BusinessFBPage fbPage) throws Exception {
		updateBusinessAggregationFB(fbPage.getFacebookPageId(), fbPage.getLink(), fbPage.getBusinessId());
		//installTabOnPage(fbPage.getBusinessId(), fbPage.getFacebookPageId(), fbPage.getPageAccessToken());
		subscribeFBWebhooks(fbPage);
		if (Objects.nonNull(fbPage.getEnterpriseId())) {
			Business business = businessRepo.findByBusinessId(fbPage.getEnterpriseId());
			pushFbStatusInFirebase(business.getId(), fbPage.getBusinessId(), commonService.getFacebookIntegrationStatus(fbPage));
		}
	}

	@Override
	public void performPostMappingAction(String facebookPageId) {
		BusinessFBPage fbPage = socialFBPageRepository.findFirstByFacebookPageId(facebookPageId);
		updateBusinessAggregationFB(fbPage.getFacebookPageId(), fbPage.getLink(), fbPage.getBusinessId());
		//installTabOnPage(fbPage.getBusinessId(), fbPage.getFacebookPageId(), fbPage.getPageAccessToken());
		subscribeFBWebhooks(fbPage);
	}

	private void pushFbStatusInFirebase(Integer enterpriseId, Integer locationId, String status) {
		String topicSocial = MESSENGER+enterpriseId+"/"+locationId;
		nexusService.insertMapInFirebase(topicSocial,"socialIntegrationStatus", status);

	}

	@Deprecated
	private void installTabOnPage(Integer businessId, String fbPageId, String permToken) {
		try {
			SocialAppCredsInfo socialAppCreds = getSocialAppCredsForBusiness();
			FacebookCreds fbCreds = new FacebookCreds(socialAppCreds.getChannelAccessToken(),
					socialAppCreds.getChannelClientSecret(), permToken, getFacebookGraphApiBaseUrl() + fbPageId);
			fbCreds.setPageId(fbPageId);
			fbService.installTabOnPage(fbCreds);
		} catch (Exception e) {
			LOGGER.error(
					"[Facebook] Exception while calling facebook installTabOnPage for pageId {} and businessId {} :: {}",
					fbPageId, businessId, e);
		}
	}

	@Override
	public void updateEligiblePages(Integer count, Date currentDate, Date minDate) {
		try {

			if(Objects.nonNull(minDate)) {
				Page<InvalidSocialIntegrationAudit> integrationAuditList = invalidSocialIntegrationAuditRepo.
						findByMarkedInvalidAndNextSyncDateIsLessThanEqualAndMarkedAtIsGreaterThanEqualOrderByNextSyncDateAsc(1, currentDate, minDate, new PageRequest(0, count));

				if(CollectionUtils.isEmpty(integrationAuditList.getContent())) {
					LOGGER.info("No pages found to be scanned for time span {}", minDate);
				}

				validateFbToken(integrationAuditList.getContent(), currentDate);
				return;
			}

			// TODO @ME: update syncDate for all unique entries at 1 go.
			Page<InvalidSocialIntegrationAudit> pages = invalidSocialIntegrationAuditRepo.
					findByMarkedInvalidAndNextSyncDateIsLessThanEqualOrderByNextSyncDateAsc(1, currentDate,new PageRequest(0, count));
			LOGGER.info("Pages fetched to scan {}", pages);

			if(CollectionUtils.isNotEmpty(pages.getContent())) {
				List<InvalidSocialIntegrationAudit> invalidPageList = pages.getContent();
				validateFbToken(invalidPageList, currentDate);
			} else {
				LOGGER.info("No pages found to be scanned");
			}
		} catch (Exception ex) {
			LOGGER.info("Something went wrong while updating page validity {}", ex);
		}

	}

	@Async
	@Override
	public void reconnectFBValidPages(AccessTokenUpdateRequest accessTokenUpdateRequest) {

		LOGGER.info("Inside method reconnectFBValidPages for reconnecting facebook pages");
		List<String> facebookPageId = accessTokenUpdateRequest.getPageIds();

		BusinessGetPageRequest lastReconnectRequests = businessGetPageReqRepo.findOne(accessTokenUpdateRequest.getBusinessRequestId());
		if (Objects.isNull(lastReconnectRequests) || !"reconnect".equalsIgnoreCase(lastReconnectRequests.getRequestType())) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Request Id doesn't match the latest request id for request type reconnect");
		}
		List<BusinessFBPage> rawFbPage = socialFBPageRepository.findByFacebookPageIdIn(facebookPageId);
		if(CollectionUtils.isNotEmpty(rawFbPage)) {
			LOGGER.info("filtering out system user token, current page list size: {}", rawFbPage.size());
			rawFbPage = rawFbPage.stream().filter(s->(StringUtils.isEmpty(s.getFirstName()) || !s.getFirstName().equalsIgnoreCase("FACEBOOK_SYSTEM_USER"))).collect(Collectors.toList());
			LOGGER.info("page list size after filter: {}", rawFbPage.size());
		}
		if (CollectionUtils.isEmpty(rawFbPage)) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "No invalid facebook page found for requestId" + accessTokenUpdateRequest.getBusinessRequestId());
		}
		try {
			FbUserProfileInfo user = fbService.getUserDetails(lastReconnectRequests.getUserAccessToken());
			Business business = businessRepo.findByBusinessId(accessTokenUpdateRequest.getBusinessId());
			List<String> pageIds = new ArrayList<>();
			rawFbPage.forEach(page -> {
				try {
					final int isValid = page.getIsValid();
					Map<String, String> response = fbService.getFacebookAccessTokenByPageId(page.getFacebookPageId(), lastReconnectRequests.getUserAccessToken());
					String accessToken = response.get("access_token");
					if (StringUtils.isNotEmpty(accessToken)) {
						String permissions = getPageTokenPermissions(business, accessToken, Collections.singletonList(page.getFacebookPageId()));
						if (StringUtils.isNotEmpty(permissions)) {
							page.setPagePermissions(permissions);
						}
						page.setPageAccessToken(accessToken);
						kafkaProducer.sendObject(Constants.UPDATE_ACCESS_TOKEN,
								new TokenUpdateRequest(page.getFacebookPageId(), page.getPageAccessToken(),
										SocialChannel.INSTAGRAM.getName(), page.getUserId()));
						page.setIsValid(1);

						if (accessTokenUpdateRequest.isUpdateScope()) {
							List<FbRoleInfo> fbRoleInfos = fbService.getTasksForPage(accessToken, page.getFacebookPageId());
							if (CollectionUtils.isNotEmpty(fbRoleInfos)) {
								fbRoleInfos.forEach(role -> {
									if (role.isActive() && Objects.equals(role.getId(), lastReconnectRequests.getSocialUserId())) {
										updateFacebookTasksAndRole(role.getTasks(), page);
									}
								});
							}

						}
						if (!user.getId().equalsIgnoreCase(page.getUserId())) {
							page.setUserId(user.getId());
							page.setFirstName(user.getFirst_name());
							page.setLastName(user.getLast_name());
							page.setPictureUrl(user.getPicture().getUrl());
						}
						page.setCreatedBy(accessTokenUpdateRequest.getUserId());
						page.setUpdatedBy(accessTokenUpdateRequest.getUserId());
						page.setUpdatedAt(new Date());
						page.setRequestId(accessTokenUpdateRequest.getBusinessRequestId().toString());
						socialFBPageRepository.save(page);
						if(isValid != 1 && page.getIsValid() == 1) {
							commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.RECONNECT_UPDATE_PAGE.name(), Collections.singletonList(page),
									Objects.nonNull(lastReconnectRequests.getBirdeyeUserId()) ? lastReconnectRequests.getBirdeyeUserId().toString() : null
									, page.getBusinessId(), page.getEnterpriseId());
							if(Objects.nonNull(page.getBusinessId()) && Objects.nonNull(page.getEnterpriseId())){
								performReconnectPostMappingAction(page);
							} else {
								LOGGER.info("Unmapped page found in fetch pages list for reconnection of valid pages");
							}
						}
						else{
							commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.UPDATE_PAGE.name(), Arrays.asList(page),
									Objects.nonNull(lastReconnectRequests.getBirdeyeUserId()) ? lastReconnectRequests.getBirdeyeUserId().toString() : null
									, page.getBusinessId(), page.getEnterpriseId());
						}
						pageIds.add(page.getFacebookPageId());

					}
				} catch (Exception e) {
					LOGGER.info("Unable to fetch access token for pageId:{} with exception:{}", page.getFacebookPageId(), e);
				}
			});
			pushToKafkaForValidity(SocialChannel.FACEBOOK.getName(),pageIds);
		} catch (Exception e) {
			LOGGER.info("Error occurred while reconnecting valid pages in method reconnectValidPages with exception:{}", e);
		}
	}

	private void performReconnectPostMappingAction(BusinessFBPage fbPage) {
		subscribeFBWebhooks(fbPage);
	}

		private void validateFbToken(List<InvalidSocialIntegrationAudit> invalidPageList, Date currentDate) throws ParseException {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

		Date nextDate = simpleDateFormat.parse(simpleDateFormat.format(nextScanDate(currentDate)));

		invalidPageList.forEach(page -> {
			boolean isValid = checkFacebookPageValidity(page.getAccessToken());
			if(isValid) {
				markPageAsValid(page.getPageId());
			}
			// TODO @ME: once retry success is valid=true, it should not retry again
			page.setRetrySuccess(isValid ? 1 : 0);
			page.setNextSyncDate(nextDate);
		});

		invalidSocialIntegrationAuditRepo.save(invalidPageList);
	}

	@Async
	void markPageAsValid(String facebookPageId) {
		try {
			if(StringUtils.isEmpty(facebookPageId)) return;

			LOGGER.info("page marked as valid for facebook page Id {}", facebookPageId);
			socialFBPageRepository.updateAccessTokenByPageId(facebookPageId);
		} catch (Exception ex) {
			LOGGER.info("something went wrong while marking the page as valid with error {}", ex);

		}
	}

	private static Date nextScanDate(Date date){
		return new Date(date.getTime() + (1000 * 60 * 60 * 24));
	}

	private boolean checkFacebookPageValidity(String pageAccessToken) {

		SocialAppCredsInfo socialAppCreds = getSocialAppCredsForBusiness();
		if (socialAppCreds == null) {
			throw new BirdeyeSocialException("Facbook App settings are not available");
		}

		String appAccessToken = socialAppCreds.getChannelAccessToken();
		DebugTokenResponse tokenResponse = null;

		try {
			tokenResponse = fbService.getTokenDetails(pageAccessToken, appAccessToken);
			if (tokenResponse.getData().getError() != null && 190 == tokenResponse.getData().getError().getCode()) {
				LOGGER.info("DebugToken API error response:  {} ",tokenResponse.getData().getError());
			}else{
				LOGGER.info("Integration is valid for token {}", pageAccessToken);
				return true;
			}
		}catch(Exception e){
			LOGGER.error("Error in calling FB API with message {} ",e.getMessage());
		}
		return false;
	}

	// TODO: should be combined with location mapping event.
	private void updateBusinessAggregationFB(String fbPageId, String link, Integer locationId) {
		LOGGER.info("updateBusinessAggregationFB: Request Received to update Review Scanned for FB through BAM for fbPageId {} link {} locationId {}", fbPageId, link, locationId);
		if ( fbPageId != null && !fbPageId.isEmpty() && locationId != null ) {
			// Pushing Business into kafka to be consumed by Listing and BAM
			BAMUpdateRequest payload = new BAMUpdateRequest(SocialChannel.FACEBOOK.getName(), locationId, link, fbPageId);
			kafkaProducer.sendWithKey(Constants.INTEGRATION_TOPIC, String.valueOf(locationId), payload);
		} else {
			LOGGER.info("updateBusinessAggregationFB: Invalid parameters ");
		}
	}

	private void pushToKafkaForValidity(String channel, Collection<String>locationIds) {
		ValidityRequestDTO validityRequestDTO = new ValidityRequestDTO();
		validityRequestDTO.setChannel(channel);
		validityRequestDTO.setFacebookPageId(locationIds);
		kafkaProducer.sendObject(Constants.CHECK_VALIDITY,validityRequestDTO);
	}

	@Override
	public void updateFacebookGranularScope(GranularScopeUpdateRequest request) {
		List<BusinessFBPage> businessFBPages;
		if(CollectionUtils.isNotEmpty(request.getPageIds())){
			businessFBPages=socialFBPageRepository.findByFacebookPageIdInAndIsValid(request.getPageIds(),1);
		}
		else{
			businessFBPages=socialFBPageRepository.findByFacebookPageIdInAndIsValid(Collections.singleton(request.getExternalId()),1);
		}
		if(CollectionUtils.isEmpty(businessFBPages)) {
			LOGGER.info("No valid page found for given payload!!");
			return;
		}
		List<BusinessFBPage> updatedFbPages = new ArrayList<>();
		businessFBPages.forEach(fbPage -> {
			String tokenPermissions = null;
			try {
				tokenPermissions = commonService.getPageTokenGranularPermissions(fbPage.getPageAccessToken(), Collections.singletonList(fbPage.getFacebookPageId()));
				fbPage.setGranularPagePermissions(tokenPermissions);
				updatedFbPages.add(fbPage);
				commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.UPDATE_PERMISSION.name(), Collections.singletonList(fbPage), "ADMIN",fbPage.getBusinessId(), fbPage.getEnterpriseId());
			} catch (Exception e) {
				LOGGER.error("For page id {} Error while getting permission with exception : ", fbPage.getFacebookPageId(),e);
			}
		});
		if(CollectionUtils.isNotEmpty(updatedFbPages)) {
			socialFBPageRepository.save(updatedFbPages);
			socialFBPageRepository.flush();
		}
	}

	private String getPageIdFromUrl(String profileUrl) {
		if(StringUtils.isEmpty(profileUrl)) {
			throw new BirdeyeSocialException("Invalid profileUrl");
		}
		if (profileUrl.endsWith("/")) {
			profileUrl = profileUrl.substring(0, profileUrl.length() - 1);
		}
		int lastSlashIndex = profileUrl.lastIndexOf('/');
		return profileUrl.substring(lastSlashIndex + 1);
	}

	@Override
	public String getPPCASystemUserToken() {
		LOGGER.info("Fetching PPCASystemUserToken");
		String token="";
		List<SocialReservedAccounts> reservedAccounts = reservedAccountsRepo.findBySourceIdAndName(SocialChannel.FACEBOOK.getId(), FB_SYSTEM_USER_PPCA_TOKEN);
		if(CollectionUtils.isNotEmpty(reservedAccounts)) {
			token = reservedAccounts.get(0).getAccessToken();
		}
		return token;
	}


	@Override
	public FbPublicProfileInfo facebookProfileInfo(FbProfileInfoRequest fbProfileInfoRequest) {
		LOGGER.info("Request received to get profile info for: {}", fbProfileInfoRequest);
		try {
			FbPublicProfileInfo result = getFacebookProfileInfoCommon(fbProfileInfoRequest, true);
			return result;
		} catch (Exception e) {
			LOGGER.error("Exception while getting profile info: ", e);
			throw new BirdeyeSocialException(ErrorCodes.FB_PUBLIC_PROFILE_ERROR, e.getMessage());
		}
	}

	@Override
	public FbPublicProfileInfo facebookProfileDetails(FbProfileInfoRequest fbProfileInfoRequest) {
		LOGGER.info("Request received to get profile info (no Kafka) for: {}", fbProfileInfoRequest);
		try {
			FbPublicProfileInfo result = getFacebookProfileInfoCommon(fbProfileInfoRequest, false);
			return result;
		} catch (Exception e) {
			LOGGER.error("Exception while getting profile info (no Kafka): ", e);
			throw new BirdeyeSocialException(ErrorCodes.FB_PUBLIC_PROFILE_ERROR, e.getMessage());
		}
	}

	private FbPublicProfileInfo getFacebookProfileInfoCommon(FbProfileInfoRequest fbProfileInfoRequest, boolean sendToKafka) throws Exception {
		if(!Objects.nonNull(fbProfileInfoRequest)) {
			throw new BirdeyeSocialException("Invalid fbProfileInfoRequest");
		}

		Integer businessId = fbProfileInfoRequest.getBusinessId();

		String pageId, accessToken;
		BusinessFBPage fbPage = null;
		if(Objects.nonNull(businessId)) {
			List<BusinessFBPage> businessFBPageList = socialFBPageRepository.findByBusinessId(businessId);
			if(CollectionUtils.isNotEmpty(businessFBPageList)) {
				fbPage = businessFBPageList.get(0);
			}
		}

		if(Objects.nonNull(fbPage) && Objects.equals(fbPage.getIsValid(), 1)) {
			pageId = fbPage.getFacebookPageId();
			accessToken = fbPage.getPageAccessToken();
			FbProfileInfo response = fbService.getProfileInfoListing(accessToken, pageId);
			response.setBusinessId(businessId);

			if(sendToKafka) {
				kafkaProducer.sendObject(Constants.FB_PROFILE_RESPONSE, response);
			}

			// Convert FbProfileInfo to FbPublicProfileInfo for consistent return type
			return convertToFbPublicProfileInfo(response);
		} else {
			pageId = getPageIdFromUrl(fbProfileInfoRequest.getProfileUrl());
			accessToken = getPPCASystemUserToken();
			if(StringUtils.isEmpty(accessToken)) {
				throw new Exception("Unable to get System user access token!!");
			}
			return fbService.getPublicProfileInfo(accessToken, pageId);
		}
	}

	private FbPublicProfileInfo convertToFbPublicProfileInfo(FbProfileInfo fbProfileInfo) {
		FbPublicProfileInfo publicProfileInfo = new FbPublicProfileInfo();
		publicProfileInfo.setId(fbProfileInfo.getId());
		publicProfileInfo.setName(fbProfileInfo.getName());
		publicProfileInfo.setLink(fbProfileInfo.getLink());
		publicProfileInfo.setAbout(fbProfileInfo.getAbout());
		publicProfileInfo.setCategory(fbProfileInfo.getCategory());
		publicProfileInfo.setDescription(fbProfileInfo.getDescription());
		publicProfileInfo.setEmails(fbProfileInfo.getEmails());
		publicProfileInfo.setFollowers_count(fbProfileInfo.getFollowers_count());
		publicProfileInfo.setSingle_line_address(fbProfileInfo.getSingle_line_address());
		publicProfileInfo.setUsername(fbProfileInfo.getUsername());
		publicProfileInfo.setWebsite(fbProfileInfo.getWebsite());
		publicProfileInfo.setHours(fbProfileInfo.getHours());
		publicProfileInfo.setLocation(fbProfileInfo.getLocation());
		publicProfileInfo.setPicture(fbProfileInfo.getPicture());
		publicProfileInfo.setCover(fbProfileInfo.getCover());
		publicProfileInfo.setVerification_status(fbProfileInfo.getVerification_status());
		publicProfileInfo.setPhone(fbProfileInfo.getPhone());
		publicProfileInfo.setIs_always_open(fbProfileInfo.getIs_always_open());
		return publicProfileInfo;
	}
}