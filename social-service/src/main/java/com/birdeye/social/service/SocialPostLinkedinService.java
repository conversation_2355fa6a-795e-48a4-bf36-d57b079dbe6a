package com.birdeye.social.service;

import com.birdeye.social.entities.BusinessLinkedinPage;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.entities.mediaupload.SocialAssetChunkInfo;
import com.birdeye.social.entities.mediaupload.SocialMediaUploadInfo;
import com.birdeye.social.entities.mediaupload.SocialMediaUploadRequest;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.external.request.linkedin.TargetAudienceResponse;
import com.birdeye.social.external.request.mediaupload.MediaUploadRequest;
import com.birdeye.social.external.service.SocialRawPageDetail;
import com.birdeye.social.linkedin.LinkedInPost.SocialLinkedInPostRequest;
import com.birdeye.social.linkedin.LinkedinProfileDetails;
import com.birdeye.social.linkedin.response.LinkedInAPIResponse;
import com.birdeye.social.model.*;
import com.birdeye.social.model.FbNotification.EngageNotificationDetails;
import com.birdeye.social.model.engageV2.EngageCommentRequest;
import com.birdeye.social.platform.entities.Business;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface SocialPostLinkedinService {
	
	public static final String DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";
	
	public String getAuthorizationUrl(Integer businessId, String redirectUri) throws Exception;

	@Deprecated
	public LinkedinInstallInfo getAccessToken(String sessionToken, String code, String redirectUri) throws Exception;
	
	public void postOnLinkedIn(SocialPostPublishInfo publishInfo, File videoFile) throws Exception;
	
	public void saveProfileAndCompanyPageInfo(String sessionToken, LinkedinInstallInfo linkedInInfo) throws Exception;

	public SocialTimeline getFeed(Business business, BusinessLinkedinPage linkedinPage, Integer startIndex)
			throws JsonParseException, JsonMappingException, IOException;

	public Boolean postComment(SocialPostComment postComment, BusinessLinkedinPage linkedinPage);
	
	public ActivityResponse shareReview(String reviewContent, String reviewUrl, BusinessLinkedinPage page);
	
	public ActivityResponse shareReview(String reviewContent, String reviewUrl, Integer pageId);

	/**
	 * @param pageId
	 * @param code
	 * @param redirectUri
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	boolean reconnectLinkedinPage(String pageId, String code, String redirectUri) throws Exception;
	
	/**
	 *
	 */
	void updateAllLinkedinPagesWithProfileInformation();

    boolean deleteObject(BusinessLinkedinPage linkedInPage, String postIds) throws Exception;

	boolean deletePost(BusinessLinkedinPage linkedInPage, String postId) throws Exception;

	List<TargetAudienceResponse> getTargetAudienceList(String pageId, String category);

    LinkedInAPIResponse getLinkedInPostsForUser(Integer businessId, Date lastPostDate, boolean allPosts);

	LinkedInAPIResponse getLinkedInPostsForReporting(Integer businessId, Date lastPostDate);

	boolean deletePostByPostId(Integer businessId, Integer postId);

	boolean updatePostById(Integer businessId, Integer postId, SocialLinkedInPostRequest socialLinkedInPostRequest);

	public void updatePostTextById(SocialPostPublishInfo publishInfo);

	List<SocialMentionData> searchPageMentions(String search, Long enterpriseId);

	List<Feed> getCommentsOnPost(BusinessLinkedinPage page, String postId) throws Exception;

	List<Feed> getCommentsOnComment(BusinessLinkedinPage page, String accessToken, String parentComment, Map<String, LinkedinProfileDetails> personIds) throws Exception;

	String addCommentOnComment(SocialPostComment comment, BusinessLinkedinPage linkedinPage, Business business) throws Exception;

	String addCommentOnPost(SocialPostComment comment, BusinessLinkedinPage page, Business business) throws Exception;

	boolean editComment(SocialSteamEditCommentRequest request);

	boolean deleteComment(BusinessLinkedinPage linkedInPage, String objectId, String postId) throws Exception;

	boolean addReaction(BusinessLinkedinPage linkedInPage, String objectId) throws Exception;

	boolean deleteReaction(BusinessLinkedinPage one, String objectId) throws Exception;

    boolean editPost(SocialSteamEditPostRequest postRequest);

	SocialTimeline getLinkedinFeed(Business business, BusinessLinkedinPage linkedinPage, String nextToken, Date lastScanDate) throws Exception;
	LinkedinMentionEventRequest getLinkedinFeedV2(Business business, BusinessLinkedinPage linkedinPage, String nextToken, Date lastScanDate, Integer postCount) throws Exception;

	List<EngageNotificationDetails> getCommentsOnPostEngage(BusinessLinkedinPage page, EngageCommentRequest request, int commentCount) throws Exception;

    boolean registerMedia(Integer id, SocialRawPageDetail socialRawPageDetail, MediaUploadRequest mediaInitiateRequest);

	void uploadChunk(SocialAssetChunkInfo socialAssetChunkInfo, SocialMediaUploadInfo socialMediaUploadInfo,
					 SocialRawPageDetail socialRawPageDetail, SocialMediaUploadRequest socialMediaUploadRequest, boolean isV2request);

    void uploadCaption(SocialMediaUploadRequest socialMediaUploadRequest, SocialRawPageDetail socialRawPageDetail);

	void finalizeVideoUpload(SocialMediaUploadRequest socialMediaUploadRequest, SocialRawPageDetail socialRawPageDetail,List<String> eTags);

	MediaUploadRequest checkStatus(SocialRawPageDetail socialRawPageDetail, MediaUploadRequest mediaUploadRequest);

	void birdeyeExceptionHandler(BirdeyeSocialException bse,Integer publishInfoId,String pageId);

	void generalExceptionHandler(String message, Integer publishInfoId);

	void postContentWithMedia(MediaUploadRequest request, String pageId,SocialPostPublishInfo publishInfoId) throws Exception;
}
