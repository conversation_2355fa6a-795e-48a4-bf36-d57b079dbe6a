/**
 * 
 */
package com.birdeye.social.service;

import com.birdeye.social.constant.MappingStatus;
import com.birdeye.social.constant.PageSortDirection;
import com.birdeye.social.constant.ResellerSearchType;
import com.birdeye.social.constant.ResellerSortType;
import com.birdeye.social.dto.*;
import com.birdeye.social.elasticdto.SocialElasticDto;
import com.birdeye.social.entities.*;
import com.birdeye.social.external.request.google.SocialAuditRequest;
import com.birdeye.social.model.*;
import com.birdeye.social.model.notification.SocialNotificationAudit;
import com.birdeye.social.sro.*;
import com.birdeye.social.sro.googlemessage.LocationBulkUnlaunchRequest;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
public interface GoogleSocialAccountService {

	public String getGoogleAuthUrl(Long businessId, Boolean redirectToSetup) throws Exception;

	public String getYoutubeAuthUrl(Long businessId, Boolean redirectToSetup,String domainName, String origin) throws Exception;

	public LocationPageMapping getLocationMappingPages(Long enterpriseId, Integer userId, List <Integer> businessIds, Set<String> status,String type,Integer page,Integer size,String search, List<String> includeModules ) throws Exception;

	List<SocialPageListInfo> getUnmappedGMBPagesByEnterpriseId(Long enterpriseId);

	public void saveGMBLocationMapping(Integer locationId, String pageId, Integer userId,String type, Long resellerId);

	public void savePlatformMapping(BusinessGoogleMyBusinessLocation gmbPage, Integer locationId, Integer userId);

	public void removeGMBLocationPageMappings(List<LocationPageMappingRequest> locationPageMappings,String type, boolean unlink) throws Exception;

	public void removeGMBPage(List<String> pageIds, Long enterpriseId);

	public void updateGMBLocationIsValidStatus(String locationId, Integer isValid);

	public void cancelRequest(String channel, Long businessId, Boolean forceCancel);

	ChannelPageInfo connectGooglePagesV1(Map<String, List<String>> pageIds, Long enterpriseId, Integer accountId);

	ChannelPageInfo connectFreemium(FreemiumConnectRequest freemiumConnectRequest);

	FreemiumStatusResponse fetchStatusForRequest(Integer sessionId);
	ChannelPageInfo connectGooglePagesForReseller(List<String> pageIds, Long enterpriseId,Boolean selectAll, String searchStr);

	//void connectGooglePagesForReseller(Map<String, List<String>> pageIds, Long enterpriseId,boolean selectAll);

	public OpenUrlPagesInfo connectPagesFetchedByOpenUrl(Long enterpriseId, OpenUrlConnectRequest connectRequest, Integer userId) throws Exception;
	
	Boolean checkForAutoMapping(List<String> pageIds, Long enterpriseId);
	
	void triggerAutoMapping(Long enterpriseId);
	
	public void removeGMBInactiveIntegration(String channel, Long enterpriseId);

	@Deprecated
	public ChannelPageInfo getGoogleIntegrationRequestInfo(Long businessId, String requestType) throws Exception;

	ChannelLocationInfo getSingleLocationMappingPages(Integer businessId);
	
	public void backupGMBPages(SocialPagesAudit socialPagesAudit);

	ConnectedPages getPages(Long enterpriseId, PageConnectionStatus pageConnectionStatus);

	ConnectedPages getPagesForPostReconnect(Long enterpriseId, PageConnectionStatus pageConnectionStatus, SocialPostPageConnectRequest request);

	public OpenUrlPagesInfo getPagesFetchedByOpenUrl(Long enterpriseId) throws Exception;

	public boolean isPageBusinessPlaceIdSame(Integer locationId, String pageId, Integer userId) throws Exception;

	boolean checkIfAccountExistsByAccountId(Long accountId);

	public void processAutoMappingInitRequest(Long enterpriseId);

	public void processAutoMappingMatchedRequest(AutoMappingMatchedRequest request);
	
	void submitFetchPageRequest(Long businessId, Integer birdeyeUserId, String googlePlusCode,
			String redirectUri) throws Exception;

	void reconnectGMBPagesEnhancedFlow(Long businessId, List<String> pageIds, String accessToken, String redirectUri,
			Integer userId,String type) throws Exception;

	void reconnectGMBPagesEnhancedFlowForReseller(Long resellerId,String accessToken, String redirectUri,
									   Integer userId,String type,Integer limit) ;

	public void fetchDisconnectedAndStore() ;

	public ChannelSetupStatus.PageSetupStatus getPageSetupStatus(Long enterpriseId);

	public ChannelSetupStatus.PageSetupStatus getPageConnectionSetupStatus(Long enterpriseId);

	BusinessIntegrationStatus.ChannelIntegrationInfo getPageIntegrationStatus(Integer businessId);

	Map<String, GoogleMyBusinessPagesDTO> processAllGMBLocationsForAccount(GMBAccountDTO gmbAccount, GoogleAuthToken gmbAuth, int pageSize);

	void postFetchPageReconnectProcess(Map<String, GoogleMyBusinessPagesDTO> data,GMBAccountSyncRequest gmbAccountSyncRequest,String type);

	void postFetchPageReconnectProcessForReseller(Map<String, GoogleMyBusinessPagesDTO> data,GMBAccountSyncRequest gmbAccountSyncRequest,String type);


	void initUnLaunchAgent(Long enterpriseId);
	
	public String getConnectCTA();
	
	OpenUrlFetchPageResponse submitFetchPageRequestForOpenURL(Long businessId,  ChannelAuthOpenUrlRequest authRequest) throws Exception;

	public SmbUnmappedDataResponse getUnmappedGMBPagesForSmb();

	void setupLocation(CreateLocationRequest req) throws Exception;

	void unLaunchLocation(LocationUnlaunchRequest request) throws Exception;

	void unLaunchLocationByAgentId(LocationBulkUnlaunchRequest request,List<BusinessGoogleMyBusinessLocation> gmbPages) throws Exception;

	void unLaunchLocationAndUpdateAgentId(LocationUnlaunchRequest request);

	BusinessGoogleMyBusinessLocation createLocation(CreateLocationRequest req) throws Exception;

	void requestLocationVerification(BusinessGoogleMyBusinessLocation gmbPage, CreateLocationRequest request) throws Exception;

	void requestLocationLaunch(BusinessGoogleMyBusinessLocation gmbPage, CreateLocationRequest request) throws Exception;

	boolean isBusinessNotMappedToGMBPage(BusinessLiteDTO business);

	void initSetupLocation(Long enterpriseId, GoogleMessagesAgent googleMessagesAgent, List<BusinessGoogleMyBusinessLocation> gmPages, Boolean postAutoMapping);

	void initSetupLocationForBusinessIds(Integer agentIds, Long enterpriseId, List<BusinessGoogleMyBusinessLocation> gmPages, Boolean postAutoMapping);
	
	public void removeBusinessGMBInactiveIntegration(String name, Integer businessId);

	public void  moveGmbAccountLocation(Long sourceEnterpriseNumber, Long targetEnterpriseNumber, Integer businessId, boolean isMultiLocationSource, Integer accountId);
    
	public void removeUnmappedByEnterprise(Long enterpriseNumber, Integer businessId);

    void restoreBusinessInactiveIntegration(String name, Integer businessId);

    Integer getUnmappedLocationCount(UnmappedLocationMappingReq request);
	List<? extends Object> fetchRawPages(List<String> integrationIds);

    List<Number> fetchRawPagesId(List<String> integrationIds);

	List<SocialElasticDto> fetchPagesEsDto();
	List<SocialElasticDto> fetchPagesEsDto(Integer id);

    void checkGmbTokenAndUpdate(List<Integer> rawIds, Boolean sync );

	Boolean isGoogleMessageEnabled(Integer businessId, Long enterpriseId);

	Map<String, List<ChannelAccountInfo>> getPages(BusinessGetPageRequest requestMap, Long enterpriseId);

    void submitFetchAccountRequest(ChannelAuthRequest authRequest,String type);

	SSOResponse getGoogleSSOAuthDetails(GoogleSSOAuthRequest authRequest);

	GoogleWhiteLabelAccountsResponse getGoogleWhiteLabelAccounts(GoogleWhiteLabelAccountsRequest request);

	void addWhiteLabelAccounts(List<String> urls);

	OpenUrlFetchPageResponse submitFetchAccountRequestOpenUrl(ChannelAuthOpenUrlRequest authRequest,Long businessId);

	void fetchAccountsFreemium(FreemiumSetupRequest authRequest);

	FreemiumFetchPageResponse fetchPages(Integer SessionId);

	GmbAccountInfo getGmbAccount(Long parentId,String type);

	GmbAccountInfo getGmbAccountForOpenUrl(Long parentId, String firebaseKey);

    void refreshGmbUserAccount(String userEmail, Long parentId,String type);

	void refreshGmbUserAccountForOpenUrl(String userEmail, Long parentId,String firebaseKey);

    void gmbBackStatus(Long businessId,String type);

	void gmbBackStatusForOpenUrl(Long businessId);

	FetchPageResponse getIntegrationPage(BusinessGetPageRequest businessGetPageRequest, Long enterpriseId);

	public void updateLocationState(UpdateLocationStateRequest request);

	void updateLocationStateByAgentId(UpdateLocationStateRequest request, Integer agentId);

	/**
	 * Given an accountId and List of Birdeye LocationIds return invalid/unmapped locations.
	 * This function will fetch relevent data from business_google_mybusiness_location table and compute the unmapped
	 * locations based on received data(socialBusinessIds) from table and input values(BusinessIds)
	 * @param accountId : enterpriseId
	 * @param businessIds : input businessIds for filter
	 * @return
	 */
    Integer getIntegrationStatus(Long accountId, List<Integer> businessIds);

	void migrateGmbPermission(List<Integer> rawIds, Boolean sync);

	void savePlatformMapping(LocationPageMappingRequest locationPageMappingRequest);

	void removePlatformMapping(List<LocationPageMappingRequest> input);

	void removePlatformEntry(List<ChannelPageRemoved> input);

	void updateGmbBusinessIsValid(SocialEsValidRequest socialEsValidRequest);

    void updatePlatformMapping(SocialChannelSyncRequest socialChannelSyncRequest);
    
    ConnectedPages getConnectedPages(Long enterpriseId, Map<Integer, BusinessEntity> idToBusinessMap);

    void updateLocationStatusAndName(UpdateGMBLocationAndNameRequest req);

	Map<String, Object> getPaginatedPages(BusinessGetPageRequest businessGetPageRequest, Integer pageNumber, Integer pageSize,String search);

	void launchLocations(LocationsLaunchAndUnLaunchRequest req);

	void unLaunchLocations(LocationsLaunchAndUnLaunchRequest req);

	void removeGMBPageForReseller(List<String> strings,Integer limit);

	PaginatedConnectedPages getResellerPages(Long resellerId, PageConnectionStatus status, Integer page, Integer size, String search,
											 ResellerSearchType searchType, PageSortDirection sortDirection, ResellerSortType sortParam,
											 List<Integer> locationIds, MappingStatus mappingStatus, List<Integer> userIds, Boolean locationFilterSelected);

	ConnectPagesResponse getPagesAfterConnect(Long resellerId, Integer size, Integer page);

	void updateEnterpriseWithNoReseller();

	void upgradeNotificationsAPI();

	public void upgradeNotificationsAPIForTopic();

	void validityCheckForAllGMBPages();

	void upgradeNotifications(List<String> accountIds);

	void updateLocationStateForPages(Long enterpriseId);

	void updateLocationStateByAgentId(Integer agentId);

	Map<String ,List<BusinessGoogleMyBusinessLocation>> generateMapForGoogleAccountId(List<BusinessGoogleMyBusinessLocation> pages);

	Map<Long, List<BusinessGoogleMyBusinessLocation>> generateMapForGoogleMessageEnabled(List<BusinessGoogleMyBusinessLocation> pages);

	BusinessGoogleMyBusinessLocation getValidity(BusinessGoogleMyBusinessLocation page, Boolean finalGoogleMessageEnabled);

	void removePageMap(String name, Integer businessId);
    List<SocialNotificationAudit> auditNotifications(Object notificationObject);

	List<String> getPlaceIds(Long enterpriseId);

	void updateAgentState(Long enterpriseId);

	GMBLocationMessageAgentResponse getAllLocations(Long businessNumber);

	AgentDetailsResponse getAllAgentDetails (Long enterpriseId);

	SocialGenericResponse deleteBrandInformation(Integer brandId);

	void updateLocationStateOfLocations(Integer agentId, String locationStatus);

	void setDefaultMessageAndLogo(GoogleMessagesAgent messagesAgent, AgentResponse agentResponse, Long enterpriseId);

	boolean getGMBPermission(Integer accountId, List<String> modules);

	boolean getGMBPostPermission(List<BusinessGoogleMyBusinessLocation> gmbPage, List<String> modules);

	void validateSocialServiceTokens(SocialTokenValidationDTO payload);

	void upsertAutoLaunchStatus(Long enterpriseId, boolean enabled);

    String getGoogleAccessToken(String locationId);

    void checkGMBPageValid(SocialAuditRequest socialAuditRequest);

	void initiateDPSync();

	void syncGmbDP(DpSyncRequest gmbDpSyncRequest);

	String googleAuthUrlV2(String origin, Boolean isLogin) throws Exception;

	void removeGMBPageByPageIds(List<String> pagesIds);
    List<ApprovalPageInfo> findByGMBLocationId(String pageId);

    List<String> findByBusinessIdIn(List<Integer> businessIds);

    List<SocialBusinessPageInfo> findByBusinessIds(List<Integer> businessIds);

	List<Integer> getMappedResellerLeafLocations(List<Integer> resellerLeafLocationIds);

	List<String> getMappedRequestIds(Set<String> requestIds);

	Validity fetchValidityAndErrorMessage(BusinessGoogleMyBusinessLocation page, Boolean googleMessageEnabled);

	List<ChannelAccountInfo> getAccountInfoForGmb(List<BusinessGoogleMyBusinessLocation> gmbpages, Long enterpriseId);
}

