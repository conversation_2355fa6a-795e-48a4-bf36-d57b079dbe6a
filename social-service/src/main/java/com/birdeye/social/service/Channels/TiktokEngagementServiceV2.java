package com.birdeye.social.service.Channels;

import com.birdeye.social.constant.EngageV2FeedSubTypeEnum;
import com.birdeye.social.constant.EngageV2FeedTypeEnum;
import com.birdeye.social.constant.FBNotificationKafkaEventNameEnum;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessTiktokAccountsRepository;
import com.birdeye.social.entities.BusinessTiktokAccounts;
import com.birdeye.social.entities.EngageFeedDetails;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.FbNotification.EngageNotificationDetails;
import com.birdeye.social.model.FbNotification.FreshPostNotificationRequest;
import com.birdeye.social.model.engageV2.EngageBusinessDetails;
import com.birdeye.social.model.engageV2.EngageCommentRequest;
import com.birdeye.social.model.engageV2.EngageWebhookSubscriptionRequest;
import com.birdeye.social.model.engageV2.PageDetailsData;
import com.birdeye.social.model.engageV2.message.ExternalServiceEvent;
import com.birdeye.social.model.engageV2.message.InboxMessageRequest;
import com.birdeye.social.model.tiktok.TikTokCommentResponse;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.SocialEngageService.SocialEngageV2;
import com.birdeye.social.service.SocialEngageService.converter.EngageConverterService;
import com.birdeye.social.service.tiktok.SocialTiktokService;
import com.birdeye.social.service.tiktok.validation.TiktokValidator;
import com.birdeye.social.tiktok.TikTokCreateCommentResponse;
import com.birdeye.social.tiktok.TikTokFeedData;
import com.birdeye.social.tiktok.TikTokLikeUnlikeResponse;
import com.birdeye.social.utils.DateTimeUtils;
import com.birdeye.social.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.birdeye.social.constant.Constants.INVALID_PAGE;

@Slf4j
@Service
@RequiredArgsConstructor
public class TiktokEngagementServiceV2 implements SocialEngageV2 {

    private final RestTemplate restTemplate;
    private final BusinessTiktokAccountsRepository businessTiktokAccountsRepository;
    private final CommonService commonService;
    private final SocialTiktokService socialTiktokService;
    private final KafkaProducerService kafkaProducerService;
    private final EngageConverterService engageConverterService;
    private final TiktokValidator tiktokValidator;

    public static final String NO_VALID_PAGE_FOUND_FOR_PAGE_ID = "No valid page found for pageId {}";
    public static final String INVALID_PAGE_MSG = "Invalid page";
    public static final String INTERNAL_ERROR_LOG_MSG = "Something went wrong while call tiktok API with error {} for request {}";
    public static final String LIKE = "LIKE";
    public static final String UNLIKE = "UNLIKE";
    public static final String UNHIDE = "UNHIDE";
    public static final String HIDE = "HIDE";

    private static final String TIKTOK_COMMENT_API_URL = "https://business-api.tiktok.com/open_api/v1.3/business/comment/create/";
    private static final String TIKTOK_COMMENT_REPLY_API_URL = "https://business-api.tiktok.com/open_api/v1.3/business/comment/reply/create/";
    private static final String TIKTOK_LIKE_UNLIKE_API_URL = "https://business-api.tiktok.com/open_api/v1.3/business/comment/like/";
    private static final String TIKTOK_HIDE_UNHIDE_API_URL = "https://business-api.tiktok.com/open_api/v1.3/business/comment/hide/";
    private static final String TIKTOK_DELETE_API_URL = "https://business-api.tiktok.com/open_api/v1.3/business/comment/delete/";

    @Override
    public String channelName() {
        return SocialChannel.TIKTOK.getName();
    }

    @Override
    public SocialTimeline getFeedData(String pageId, EngageFeedDetails feedDetails, String type) {
        return null;
    }

    @Override
    public List<EngageNotificationDetails> getFeedEngagement(String pageId) {
        return Collections.emptyList();
    }

    @Override
    public List<EngageNotificationDetails> getCommentData(EngageCommentRequest request) {
        try {
            BusinessTiktokAccounts businessTiktokAccounts = businessTiktokAccountsRepository.findFirstByProfileIdAndIsValid(request.getPageId(), 1);
            if(ObjectUtils.isEmpty(businessTiktokAccounts)) {
                log.info(NO_VALID_PAGE_FOUND_FOR_PAGE_ID, request.getPageId());
                return null;
            }
            String accessToken = socialTiktokService.getAccessToken(businessTiktokAccounts, false);

            List<TikTokCommentResponse.TikTokComment> comments =  socialTiktokService.getCommentsOnTiktokVideo(accessToken, businessTiktokAccounts.getProfileId(), request.getObjectId());

            List<EngageNotificationDetails> engageNotificationDetails = new ArrayList<>();
            if (Objects.nonNull(comments) && CollectionUtils.isNotEmpty(comments)) {
                for (TikTokCommentResponse.TikTokComment comment : comments) {
                    // Comment
                    engageNotificationDetails.add(convertTiktokCommentToEngage(comment, businessTiktokAccounts, request.getObjectId()));
                    // SubComments
                    if (CollectionUtils.isNotEmpty(comment.getReplyList()) && comment.getReplies() > 0) {
                        for (TikTokCommentResponse.TikTokComment tiktokCommentReply : comment.getReplyList()) {
                            engageNotificationDetails.add(convertTiktokCommentToEngage(tiktokCommentReply, businessTiktokAccounts, request.getObjectId()));
                        }
                    }
                }
            }
            return engageNotificationDetails;
        } catch (Exception ex) {
            log.info("Something went wrong while fetching comment for request {} with error", request, ex);
            return null;
        }
    }

    private EngageNotificationDetails convertTiktokCommentToEngage(TikTokCommentResponse.TikTokComment comment, BusinessTiktokAccounts tiktokAccounts,
                                                                         String postId) throws ParseException {
        EngageNotificationDetails notificationDetails = new EngageNotificationDetails();
        notificationDetails.setChannel(SocialChannel.TIKTOK.getLabel());
        notificationDetails.setSourceId(SocialChannel.TIKTOK.getId());
        notificationDetails.setPageId(tiktokAccounts.getProfileId());
        notificationDetails.setPostId(postId);
        notificationDetails.setLocationId(tiktokAccounts.getBusinessId());
        notificationDetails.setAccountId(tiktokAccounts.getAccountId());
        notificationDetails.setEngageFeedId(postId);
        notificationDetails.setText(comment.getText());
        boolean isParentComment = Objects.isNull(comment.getParentCommentId());
        notificationDetails.setIsParentComment(isParentComment);
        notificationDetails.setEventParentId(isParentComment ? postId : comment.getParentCommentId());
        notificationDetails.setSubType(isParentComment ? null : EngageV2FeedSubTypeEnum.REPLY.name());
        long timestamp = Long.parseLong(comment.getCreateTime());
        notificationDetails.setFeedDate(new Date(timestamp * 1000));
        notificationDetails.setType(EngageV2FeedTypeEnum.COMMENT.name());
        notificationDetails.setFeedId(comment.getCommentId());
        notificationDetails.setIsHidden(comment.getStatus().equals("HIDDEN"));
        notificationDetails.setLikeCount(comment.getLikes());
        notificationDetails.setCommentCount(comment.getReplies());
        notificationDetails.setCanReplyPrivately(!comment.isOwner());
        notificationDetails.setIsAdminComment(comment.isOwner());
        //user details
        notificationDetails.setAuthorName(comment.getDisplayName());
        notificationDetails.setAuthorUsername(comment.getUsername());
        notificationDetails.setAuthorProfileImage(comment.getProfileImage());
        if(Objects.isNull(comment.getProfileImage()) && StringUtils.isNotEmpty(comment.getUsername())
            && comment.getUsername().equals(tiktokAccounts.getProfileUsername())) {
            notificationDetails.setAuthorProfileImage(tiktokAccounts.getProfileImageUrl());
        }
        notificationDetails.setAuthorId(comment.getUniqueIdentifier());
        return notificationDetails;
    }

    @Override
    public EngageNotificationDetails prepareAdditionalParamentToEs(EngageNotificationDetails request) {
        return request;
    }

    @Override
    public Feed getPostDetails(FreshPostNotificationRequest request) {
        try {
            Feed feed = new Feed();
            BusinessTiktokAccounts businessTiktokAccount = businessTiktokAccountsRepository.findFirstByProfileIdAndIsValid(request.getPageId(), 1);
            if(Objects.isNull(businessTiktokAccount)) {
                log.info("[getPostDetails] Tiktok Page not found for pageId: {}", request.getPageId());
                return null;
            }
            String accessToken = socialTiktokService.getAccessToken(businessTiktokAccount, false);
            TikTokFeedData postDetails =
                    socialTiktokService.getTiktokPostData(businessTiktokAccount.getProfileId(), request.getPostId(), accessToken);
            if(Objects.isNull(postDetails)) {
                log.info("Cannot fetch post details for tiktok, postId {}", request.getPostId());
                return null;
            }
            List<TikTokFeedData.TikTokVideo> videos = postDetails.getData().getVideos();
            TikTokFeedData.TikTokVideo tiktokPost = CollectionUtils.isNotEmpty(videos) ? videos.get(0) : null;
            feed.setFeedId(tiktokPost.getVideoId());
            feed.setFeedText(tiktokPost.getDescription());
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            String formattedDate = dateFormat.format(new Date(tiktokPost.getCreateTime() * 1000L));
            feed.setDatePublished(formattedDate);
            feed.setLikes(tiktokPost.getLikeCount());
            feed.setComments(tiktokPost.getCommentCount());
            feed.setShares(tiktokPost.getShareCount());
            feed.setFeedUrl(tiktokPost.getShareUrl().substring(0, tiktokPost.getShareUrl().indexOf('?')));
            feed.setImages(Collections.singletonList(tiktokPost.getCoverImageUrl()));
            feed.setVideos(Collections.singletonList(tiktokPost.getEmbedLink().substring(0, tiktokPost.getEmbedLink().indexOf('?'))));
            feed.setAccountId(businessTiktokAccount.getAccountId());
            feed.setBusinessId(businessTiktokAccount.getBusinessId());
            feed.setPublisherName(businessTiktokAccount.getProfileName());
            feed.setPublisherHandle(businessTiktokAccount.getProfileUsername());
            feed.setProfileImage(businessTiktokAccount.getProfileImageUrl());
            log.info("Tiktok post cover image: {}", tiktokPost.getCoverImageUrl());
            feed.setPublisherId(businessTiktokAccount.getProfileId());
            feed.setPageName(businessTiktokAccount.getProfileUsername());
            return feed;
        } catch (Exception ex) {
            log.info("Something went wrong while fetching comment for request {} with error", request, ex);
            return null;
        }
    }

    @Override
    public void startBackFill(GenericScriptRequest scriptRequest) {

    }

    @Override
    public void saveMessages(InboxMessageRequest inboxMessageRequest) {

    }

    @Override
    public EngageBusinessDetails getChannelEnterpriseIdByPageId(String pageId) {
        EngageBusinessDetails engageBusinessDetails = new EngageBusinessDetails();
        BusinessTiktokAccounts tiktokAccounts = businessTiktokAccountsRepository.findByProfileId(pageId);

        if(Objects.isNull(tiktokAccounts)) {
            log.info("No page found for pageId {}", pageId);
            return null;
        }

        engageBusinessDetails.setEnterpriseId(tiktokAccounts.getEnterpriseId());
        engageBusinessDetails.setBusinessIds(Objects.nonNull(tiktokAccounts.getBusinessId()) ? Collections.singletonList(tiktokAccounts.getBusinessId()) : null);
        return engageBusinessDetails;
    }

    @Override
    public Feed getFeedDetails(String pageId, Feed feed) {
        return null;
    }

    @Override
    public void hideWallPost(SocialEngageObjectRequest request) {

    }

    @Override
    public void hidePostComment(SocialEngageObjectRequest request) {
        try {
            BusinessTiktokAccounts businessTiktokAccounts = businessTiktokAccountsRepository.findFirstByProfileIdAndIsValid(request.getPageId(), 1);
            if(Objects.isNull(businessTiktokAccounts)) {
                throw new BirdeyeSocialException(INVALID_PAGE_MSG);
            }
            String accessToken = socialTiktokService.getAccessToken(businessTiktokAccounts, false);
            String action = UNHIDE;
            if(request.getHide())
                action = HIDE;
            hideUnhideComment(accessToken, businessTiktokAccounts.getProfileId(), action, request);
        } catch (Exception ex) {
            log.info(INTERNAL_ERROR_LOG_MSG, ex, request);
        }
    }

    private void hideUnhideComment(String accessToken, String profileId,  String action,SocialEngageObjectRequest request) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Access-Token", accessToken);
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("business_id", profileId);
            requestBody.put("comment_id", request.getFeedId());
            requestBody.put("action",action);
            requestBody.put("video_id", request.getEngageFeedId());
            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<TikTokLikeUnlikeResponse> response = restTemplate.exchange(
                    TIKTOK_HIDE_UNHIDE_API_URL, HttpMethod.POST, requestEntity, TikTokLikeUnlikeResponse.class);
            log.info("Successfully hide {} comment id {} with response: {}",request.getHide(), request, response.getBody());
        } catch (HttpStatusCodeException e) {
            log.error("Error from TikTok API: Status - {}, Response - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new BirdeyeSocialException("Failed to hide/unhide comment on tiktok video: " + e.getResponseBodyAsString(), e);
        } catch (Exception ex) {
            log.error("[LikeUnlikeComment] Unexpected error occurred", ex);
            throw new BirdeyeSocialException("An unexpected error occurred while hiding/unhiding comment on tiktok", ex);
        }
    }

    @Override
    public void blockUserFromPage(SocialEngageObjectRequest request, ExternalServiceEvent blockUserEvent) {

    }

    @Override
    public void unBlockUserFromPage(SocialEngageObjectRequest request, ExternalServiceEvent blockUserEvent) {

    }

    @Override
    public void likePageContent(SocialEngageObjectRequest request, ExternalServiceEvent externalServiceEvent) {
        try {
            BusinessTiktokAccounts businessTiktokAccounts = businessTiktokAccountsRepository.findFirstByProfileIdAndIsValid(request.getPageId(), 1);
            if(Objects.isNull(businessTiktokAccounts)) {
                throw new BirdeyeSocialException(INVALID_PAGE_MSG);
            }
            String accessToken = socialTiktokService.getAccessToken(businessTiktokAccounts, false);
            likeUnlinkeComment(accessToken, businessTiktokAccounts.getProfileId(), LIKE, request.getFeedId());
        } catch (Exception ex) {
            log.info(INTERNAL_ERROR_LOG_MSG, ex, request);
        }
    }

    private void likeUnlinkeComment(String accessToken, String profileId, String action, String commentId) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Access-Token", accessToken);
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("business_id", profileId);
            requestBody.put("comment_id", commentId);
            requestBody.put("action", action);
            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<TikTokLikeUnlikeResponse> response = restTemplate.exchange(
                    TIKTOK_LIKE_UNLIKE_API_URL, HttpMethod.POST, requestEntity, TikTokLikeUnlikeResponse.class);
            log.info("Successfully {} comment id {} with response: {}",action, commentId, response.getBody());
        } catch (HttpStatusCodeException e) {
            log.error("Error from TikTok API: Status - {}, Response - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new BirdeyeSocialException("Failed to like comment on tiktok video: " + e.getResponseBodyAsString(), e);
        } catch (Exception ex) {
            log.error("[LikeUnlikeComment] Unexpected error occurred", ex);
            throw new BirdeyeSocialException("An unexpected error occurred while liking/unliking comment on tiktok", ex);
        }
    }

    @Override
    public void unLikePageContent(SocialEngageObjectRequest request, ExternalServiceEvent externalServiceEvent) {
        try {
            BusinessTiktokAccounts businessTiktokAccounts = businessTiktokAccountsRepository.findFirstByProfileIdAndIsValid(request.getPageId(), 1);
            if(Objects.isNull(businessTiktokAccounts)) {
                throw new BirdeyeSocialException(INVALID_PAGE_MSG);
            }
            String accessToken = socialTiktokService.getAccessToken(businessTiktokAccounts, false);
            likeUnlinkeComment(accessToken, businessTiktokAccounts.getProfileId(), UNLIKE, request.getFeedId());
        } catch (Exception ex) {
            log.info(INTERNAL_ERROR_LOG_MSG, ex, request);
        }
    }

    @Override
    public void commentPageContent(SocialEngageObjectRequest request) throws Exception {
        try {
            BusinessTiktokAccounts tiktokAccount = businessTiktokAccountsRepository.findFirstByProfileIdAndIsValid(request.getPageId(), 1);
            if (Objects.isNull(tiktokAccount)) {
                throw new SocialBirdeyeException(ErrorCodes.INVALID_PAGE, INVALID_PAGE);
            } else {
                commonService.checkRequestFromAuthorizedSourceUsingBusinessNumber(tiktokAccount.getEnterpriseId(), request.getBusinessNumber());
            }
            String accessToken = socialTiktokService.getAccessToken(tiktokAccount, false);
            TikTokCreateCommentResponse tikTokCreateCommentResponse = createComment(accessToken, tiktokAccount.getProfileId(), request);
            if (Objects.isNull(tikTokCreateCommentResponse) || ObjectUtils.isEmpty(tikTokCreateCommentResponse.getData())) {
                log.info("Status for posting data to twitter is null");
                return;
            }
            request.setAccountId(tiktokAccount.getAccountId());
            request.setAuthorProfileImage(tiktokAccount.getProfileImageUrl());
            request.setBusinessId(tiktokAccount.getBusinessId());
            request.setCommentId(tikTokCreateCommentResponse.getData().getCommentId());
            request.setFeedDate(DateTimeUtils.localToUTCSqlFormat(new Date()));
        } catch(Exception ex){
            log.info("Something went wrong while calling tiktok comment API with error {} for request {}", ex, request);
            throw ex;
        }
    }

    @Override
    public void deletePageContent(SocialEngageObjectRequest request) throws Exception {
        try {
            BusinessTiktokAccounts businessTiktokAccounts = businessTiktokAccountsRepository.findFirstByProfileIdAndIsValid(request.getPageId(), 1);
            if(Objects.isNull(businessTiktokAccounts)) {
                throw new BirdeyeSocialException(INVALID_PAGE_MSG);
            }
            String accessToken = socialTiktokService.getAccessToken(businessTiktokAccounts, false);
            deleteComment(accessToken, businessTiktokAccounts.getProfileId(), request.getFeedId());
        } catch (Exception ex) {
            log.info(INTERNAL_ERROR_LOG_MSG, ex, request);
        }
    }

    private void deleteComment(String accessToken, String profileId, String commentId) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Access-Token", accessToken);
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("business_id", profileId);
            requestBody.put("comment_id", commentId);
            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<TikTokLikeUnlikeResponse> response = restTemplate.exchange(
                    TIKTOK_DELETE_API_URL, HttpMethod.POST, requestEntity, TikTokLikeUnlikeResponse.class);
            log.info("Successfully deleted comment id {} with response: {}", commentId, response.getBody());
        } catch (HttpStatusCodeException e) {
            log.error("Error from TikTok API: Status - {}, Response - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new BirdeyeSocialException("Failed to like comment on tiktok video: " + e.getResponseBodyAsString(), e);
        } catch (Exception ex) {
            log.error("[LikeUnlikeComment] Unexpected error occurred", ex);
            throw new BirdeyeSocialException("An unexpected error occurred while deleting comment on tiktok", ex);
        }
    }

    @Override
    public Boolean subscribeNotificationWebhook(String pageId) {
        return null;
    }

    @Override
    public void unSubscribeNotificationWebhook(String pageId) {

    }

    @Override
    public void followUser(SocialEngageObjectRequest request) {

    }

    @Override
    public void unfollowUser(SocialEngageObjectRequest request) {

    }

    @Override
    public void shareComment(SocialEngageObjectRequest request) {

    }

    @Override
    public void unShareComment(SocialEngageObjectRequest request) {

    }

    @Override
    public void saveCommentInDbAndInES(SocialEngageObjectRequest request, EngageNotificationDetails documentFromEs) {
        EngageNotificationDetails commentPayload = engageConverterService.convertAndSaveToES(request,documentFromEs);
        kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC.getName(), commentPayload);
    }

    @Override
    public void likeMessage(SocialEngageObjectRequest request, ExternalServiceEvent externalServiceEvent) {

    }

    @Override
    public void unLikeMessage(SocialEngageObjectRequest request, ExternalServiceEvent externalServiceEvent) {

    }

    @Override
    public boolean isPageValid(String pageId) {
        BusinessTiktokAccounts businessTiktokAccounts = businessTiktokAccountsRepository.findFirstByProfileIdAndIsValid(pageId, 1);
        return !Objects.isNull(businessTiktokAccounts);
    }

    @Override
    public List<String> getChannelPageIdsByEnterpriseId(Long enterpriseId) {
        return Collections.emptyList();
    }

    @Override
    public PageDetailsData getChannelPageIdsByEnterpriseIds(List<Long> enterpriseId) {
        return null;
    }

    @Override
    public void subscribeEngageNotificationWebhook(String pageId) {

    }

    @Override
    public void unSubscribeEngageNotificationWebhook(String pageId) {

    }

    @Override
    public void removePageNotificationWebhook(EngageWebhookSubscriptionRequest request) {

    }

    @Override
    public String getReviewerUrlByPageId(String pageId) {
        return "";
    }

    @Override
    public EngagePageDetails getRawPageDetails(String pageId) {
        BusinessTiktokAccounts businessTiktokAccounts = businessTiktokAccountsRepository.findFirstByProfileIdAndIsValid(pageId, 1);
        if(Objects.nonNull(businessTiktokAccounts))
        {
            return EngagePageDetails.builder()
                    .pageName(businessTiktokAccounts.getProfileUsername())
                    .pageId(businessTiktokAccounts.getProfileId())
                    .profilePicUrl(businessTiktokAccounts.getProfileImageUrl())
                    .username(businessTiktokAccounts.getProfileUsername())
                    .build();
        }
        return EngagePageDetails.builder().build();
    }

    public TikTokCreateCommentResponse createComment(String accessToken, String businessId, SocialEngageObjectRequest request) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Access-Token", accessToken);

            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("business_id", businessId);
            requestBody.put("video_id", request.getPostId());
            requestBody.put("text", request.getCommentMsg());
            if(request.getType().equals("COMMENT")) {
                requestBody.put("comment_id", request.getFeedId());
            }

            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<TikTokCreateCommentResponse> response;
            if(request.getType().equals("COMMENT")) {
                response = restTemplate.exchange(
                        TIKTOK_COMMENT_REPLY_API_URL, HttpMethod.POST, requestEntity, TikTokCreateCommentResponse.class);
            } else {
                response = restTemplate.exchange(
                        TIKTOK_COMMENT_API_URL, HttpMethod.POST, requestEntity, TikTokCreateCommentResponse.class);
            }

            log.info("Successfully created comment on tiktok video {} with response: {}", request.getPostId(), response.getBody());
            return response.getBody();
        } catch (HttpStatusCodeException e) {
            log.error("Error from TikTok API: Status - {}, Response - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new BirdeyeSocialException("Failed to create comment on tiktok video: " + e.getResponseBodyAsString(), e);
        } catch (Exception ex) {
            log.error("[createComment] Unexpected error occurred", ex);
            throw new BirdeyeSocialException("An unexpected error occurred while creating comment on tiktok video", ex);
        }
    }

    @Override
    public boolean validateImageUrls(List<String> imageUrls) {
        return tiktokValidator.validateImageUrls(imageUrls);
    }

    @Override
    public boolean validateCommentDetails(List<EngageNotificationDetails> comments, String commentId) {
        return tiktokValidator.validateTiktokComments(comments, commentId);
    }
}
