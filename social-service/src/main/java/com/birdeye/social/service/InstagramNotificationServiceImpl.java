package com.birdeye.social.service;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessInstagramAccountRepository;
import com.birdeye.social.dao.EngageFeedDetailsRepo;
import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.service.InstagramMentionExternalService;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.facebook.response.InstagramUserDetailsResponse;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.instagram.InstagramCommentResponse;
import com.birdeye.social.instagram.InstagramExternalService;
import com.birdeye.social.instagram.InstagramMediaTagRequest;
import com.birdeye.social.instagram.response.*;
import com.birdeye.social.model.FbNotification.EngageNotificationDetails;
import com.birdeye.social.model.FbNotification.FreshPostNotificationRequest;
import com.birdeye.social.model.InstagramChanges;
import com.birdeye.social.model.InstagramEventRequest;
import com.birdeye.social.model.InstagramMessaging;
import com.birdeye.social.model.InstagramValue;
import com.birdeye.social.service.SocialEngageService.SocialEngagementV2Service;
import com.birdeye.social.service.SocialEngageService.converter.EngageConverterServiceImpl;
import com.birdeye.social.service.instagram.impl.IInstagramService;
import com.birdeye.social.utils.JSONUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
public class InstagramNotificationServiceImpl implements IInstagramNotificationService{
    private static final Logger LOG = LoggerFactory.getLogger(InstagramNotificationServiceImpl.class);

    @Autowired
    private IInstagramService instagramService;

    @Autowired
    private BusinessInstagramAccountRepository businessInstagramAccountRepository;

    @Autowired
    private InstagramMentionExternalService igMentionExternalService;

    @Autowired
    private IInstagramNotificationService igNotificationService;

    @Autowired
    private InstagramExternalService instagramExternalService;

    @Autowired
    private SocialEngagementV2Service socialEngagementV2Service;
    @Autowired
    private EngageFeedDetailsRepo engageFeedDetailsRepo;

    @Autowired
    private EsService esService;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private InstagramSocialService instagramSocialService;
    @Autowired
    private EngageConverterServiceImpl engageConverterService;
    @Autowired
    private KafkaExternalService kafkaExternalService;

    private static final String TOKEN_INVALID_ERROR = "Error validating access token: The session has been invalidated because the user changed their password or Facebook has changed the session for security reasons.";


    @Override
    public void processMentionsEvent(InstagramEventRequest igEventRequest) {
        AtomicReference<String> igAccountId = new AtomicReference<>();
        try {
            LOG.info("IG mentions event received : {}", igEventRequest);
            igEventRequest.getEntry().forEach(instagramEntry -> {
                Long instagramId = instagramEntry.getId();
                BusinessInstagramAccount instagramAccount = validateAndGetInstagramAccount(instagramId);
                if(Objects.isNull(instagramAccount)){
                    LOG.info("No valid IG account found for instagram account id : {}", instagramId);
                    return;
                }
                igAccountId.set(instagramAccount.getInstagramAccountId());

                InstagramChanges instagramChanges = instagramEntry.getChanges().get(0);
                InstagramValue instagramValue = instagramChanges.getValue();

                if (Objects.nonNull(instagramValue)) {
                    if (Objects.nonNull(instagramValue.getMedia_id()) && Objects.nonNull(instagramValue.getComment_id())) {
                        LOG.info("This is a mention in comments of another account's post");
                        //mention in comment is not covered in engage 2.0
                        handleMentionInCommentsCase(instagramAccount, instagramValue.getComment_id());
                        return;
                    }

                    String mentionedMediaId = instagramValue.getMedia_id();
                    InstagramMentionedMediaResponse mentionedMediaResponse;

                    mentionedMediaResponse = igMentionExternalService.getMentionedMediaInfo(instagramAccount, mentionedMediaId);

                    EngageNotificationDetails notificationDetails = getMentionsNotificationDetails(mentionedMediaResponse, instagramAccount.getInstagramHandle());
                    notificationDetails.setAccountId(instagramAccount.getAccountId());
                    notificationDetails.setLocationId(instagramAccount.getBusinessId());
                    saveInDbAndEs(notificationDetails, String.valueOf(instagramId));
                }
            });
        }  catch (BirdeyeSocialException ex) {
            if(ex.getCode() == ErrorCodes.NO_SUCH_INSTAGRAM_ACCOUNT_EXIST.value()) {
                LOG.warn("IG account does not exist for mentions event: {}", ex.getMessage());
            } else if (ex.getCode() == ErrorCodes.INSTAGRAM_MENTION_ERROR.value() && Objects.nonNull(ex.getMessage())) {
                if (ex.getMessage().contains(TOKEN_INVALID_ERROR)) {
                    LOG.warn("IG account found invalid due to error: {}", ex.getMessage());
                    kafkaExternalService.markPageInvalid(SocialChannel.INSTAGRAM.getName(), igAccountId.get());
                }else if (ex.getMessage().contains("(#10) This is not a valid organic media.")) {
                    LOG.warn("Getting not a valid organic media error for IG account: {}", igAccountId.get());
                }else{
                    throw ex;
                }
            } else {
                throw ex;
            }
        } catch (SocialBirdeyeException e) {
            if(e.getCode() == ErrorCodes.NO_SUCH_INSTAGRAM_ACCOUNT_EXIST.value()){
                LOG.warn("IG account does not exist for mentions event: {}", e.getMessage());
            } else {
                throw e;
            }
        } catch (Exception e) {
                LOG.error("Error while processing IG mention: {}", e);
        }
    }

    private void handleMentionInCommentsCase(BusinessInstagramAccount instagramAccount, String commentId) {
        InstagramMentionedCommentResponse mentionedCommentResponse =
                igMentionExternalService.getMentionedCommentInfo(instagramAccount, commentId);

        EngageNotificationDetails notificationDetails = new EngageNotificationDetails();
        notificationDetails.setPageId(mentionedCommentResponse.getId());
        notificationDetails.setLocationId(instagramAccount.getBusinessId());
        notificationDetails.setAccountId(instagramAccount.getAccountId());

        MentionedComment mentionedComment = mentionedCommentResponse.getMentioned_comment();
        if (Objects.nonNull(mentionedComment)) {
            notificationDetails.setSourceId(SocialChannel.INSTAGRAM.getId());
            notificationDetails.setChannel(SocialChannel.INSTAGRAM.getName());
            notificationDetails.setType(EngageV2FeedTypeEnum.COMMENT.name());
            notificationDetails.setSubType(EngageV2FeedSubTypeEnum.MENTION.name());
            notificationDetails.setIsParentComment(true);
            notificationDetails.setText(mentionedComment.getText());
            notificationDetails.setFeedId(mentionedComment.getId());
            notificationDetails.setAuthorUsername(mentionedComment.getUsername());
            updateNotificationDetailsWithUsername(notificationDetails);

            notificationDetails.setFeedDate(mentionedComment.getTimestamp());
            notificationDetails.setPageId(mentionedCommentResponse.getId()); // our page id, not the one who mentioned us
            notificationDetails.setMentionedName(instagramAccount.getInstagramHandle());
            notificationDetails.setPageName(instagramAccount.getInstagramHandle());
            notificationDetails.setIsCompleted(false);


            InstagramTimelineResponse mentionedMedia = mentionedComment.getMedia();
            if (Objects.nonNull(mentionedMedia)) {
                notificationDetails.setPostUrl(mentionedMedia.getPermalink());
                notificationDetails.setPostId(mentionedMedia.getId());
                notificationDetails.setParentPostText(mentionedMedia.getCaption());
                notificationDetails.setEventParentId(mentionedMedia.getId());
            }
        }
        saveInDbAndEs(notificationDetails, instagramAccount.getInstagramAccountId());

        if(Objects.nonNull(mentionedComment) && Objects.nonNull(mentionedComment.getMedia())) {
            InstagramTimelineResponse mentionedMedia = mentionedComment.getMedia();
            EngageNotificationDetails mediaData = getMediaDetails(mentionedMedia.getId(), null);
            if (Objects.nonNull(mediaData)) {
                return;
            }
            //save media now
            EngageNotificationDetails mediaNotificationDetails = new EngageNotificationDetails();
            mediaNotificationDetails.setSourceId(SocialChannel.INSTAGRAM.getId());
            mediaNotificationDetails.setChannel(SocialChannel.INSTAGRAM.getName());
            mediaNotificationDetails.setType(EngageV2FeedTypeEnum.POST.name());
            mediaNotificationDetails.setSubType(EngageV2FeedSubTypeEnum.MENTION.name());
            mediaNotificationDetails.setPostUrl(mentionedMedia.getPermalink());
            mediaNotificationDetails.setFeedUrl(mentionedMedia.getPermalink());
            mediaNotificationDetails.setPostId(mentionedMedia.getId());
            mediaNotificationDetails.setText(mentionedMedia.getCaption());
            mediaNotificationDetails.setAuthorUsername(mentionedMedia.getUsername());
            mediaNotificationDetails.setFeedId(mentionedMedia.getId());
            updateNotificationDetailsWithUsername(mediaNotificationDetails);
            mediaNotificationDetails.setPageName(mentionedMedia.getUsername());
            mediaNotificationDetails.setIsCompleted(false);
            mediaNotificationDetails.setAccountId(instagramAccount.getAccountId());
            mediaNotificationDetails.setLocationId(instagramAccount.getBusinessId());

            mediaNotificationDetails.setFeedDate(mentionedMedia.getTimestamp());
            mediaNotificationDetails.setPageId(mentionedCommentResponse.getId()); // our page id, not the one who mentioned us
            if (Objects.nonNull(mentionedMedia.getOwner())) {
                mediaNotificationDetails.setAuthorId(mentionedMedia.getOwner().getId());
            }

            attachMediaToNotificationDetails(mediaNotificationDetails, mentionedMedia);
            saveInDbAndEs(mediaNotificationDetails, instagramAccount.getInstagramAccountId());
        }
    }

    private void updateNotificationDetailsWithUsername(EngageNotificationDetails notificationDetails) {
        String userName = notificationDetails.getAuthorUsername();
        try {
            SearchRequest searchRequest = new SearchRequest(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            BoolQueryBuilder b = new BoolQueryBuilder();
            b.must(QueryBuilders.matchQuery("authorUsername", userName));
            b.must(QueryBuilders.existsQuery("authorProfileImage"));

            searchSourceBuilder.query(b);
            searchRequest.source(searchSourceBuilder);
            SearchResponse response = esService.search(searchRequest);
            SearchHit[] searchHits = response.getHits().getHits();
            if(searchHits.length > 0) {
                for(SearchHit hit : searchHits) {
                    EngageNotificationDetails from = JSONUtils.fromJSON(hit.getSourceAsString(), EngageNotificationDetails.class);
                    notificationDetails.setAuthorName(from.getAuthorName());
                    notificationDetails.setAuthorProfileImage(from.getAuthorProfileImage());
                }
            } else {
                notificationDetails.setAuthorName(userName);
            }

        } catch (Exception ex) {
            LOG.info("Something went wrong while fetching data from ES with an error", ex);
        }
    }

    @Override
    public void processCommentsEvent(InstagramEventRequest igEventRequest) {
        LOG.info("IG comments event received : {}", igEventRequest);
        try {
            igEventRequest.getEntry().forEach(instagramEntry -> {
                Long instagramId = instagramEntry.getId();
                BusinessInstagramAccount instagramAccount = validateAndGetInstagramAccount(instagramId);
                if(Objects.isNull(instagramAccount)){
                    LOG.info("No valid IG account found for instagram account id : {}", instagramId);
                    return;
                }
                // There are no post notifications for instagram. So, we'll first find whether the media exists in ES or not.
                // If yes, just save the comment. Otherwise, publish to kafka to save post and its comments.

                InstagramChanges instagramChanges = instagramEntry.getChanges().get(0);
                InstagramValue instagramValue = instagramChanges.getValue();
                String mediaId = instagramValue.getMedia().getId();
                EngageNotificationDetails mediaData = getMediaDetails(mediaId, instagramAccount.getInstagramAccountId());

                if (Objects.isNull(mediaData)) {
                    String commentId = null;
                    if (!instagramValue.getFrom().getId().equalsIgnoreCase(instagramAccount.getInstagramAccountId())) {
                        commentId = instagramValue.getId();
                    }
                    trigggerPostSaveForEngage(instagramAccount.getInstagramAccountId(), mediaId, commentId);
                    return;
                }

                EngageNotificationDetails existingComment = getMediaDetails(instagramValue.getId(), null);
                if (Objects.nonNull(existingComment)) {
                    LOG.info("Comment with id already exists in ES: {}", instagramValue.getId());
                    return;
                }
                EngageNotificationDetails notificationDetails = getEngagementCommentNotificationDetails(instagramAccount,
                        instagramValue, instagramEntry.getTime());
                notificationDetails.setParentPostText(mediaData.getText());
                notificationDetails.setPostUrl(mediaData.getPostUrl());

                String parentCommentId = getParentCommentId(instagramAccount, instagramValue.getId());
                notificationDetails.setEventParentId(Objects.isNull(parentCommentId) ? mediaData.getFeedId() : parentCommentId);
                notificationDetails.setIsParentComment(Objects.isNull(parentCommentId));
                notificationDetails.setSubType(Objects.isNull(parentCommentId) ? null : EngageV2FeedSubTypeEnum.REPLY.name());
                notificationDetails.setCanReplyPrivately(!notificationDetails.getIsAdminComment());
                notificationDetails.setLocationId(instagramAccount.getBusinessId());
                notificationDetails.setAccountId(instagramAccount.getAccountId());

                // Updating commentCount of post
                if(instagramValue.getMedia()!=null && instagramValue.getMedia().getId()!=null){
                    EngageNotificationDetails postFeed = engageConverterService.fetchEsDocByFeedIdAndPageId(instagramValue.getMedia().getId(), instagramAccount.getInstagramAccountId());
                    if (Objects.isNull(postFeed)) {
                        LOG.info("Cannot find feed for parentPost {}", instagramValue.getMedia().getId());
                    } else {
                        // Both comments and replies are counted as comment for post.
                        postFeed.setCommentCount(postFeed.getCommentCount() == null ? 1 : postFeed.getCommentCount() + 1);
                        kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC_UPDATE.getName(), postFeed);
                    }
                }
                // Updating commentCount of parentComment
                if(Objects.nonNull(parentCommentId)){
                    EngageNotificationDetails parentCommentFeed = engageConverterService.fetchEsDocByFeedIdAndPageId(parentCommentId, instagramAccount.getInstagramAccountId());
                    if (Objects.isNull(parentCommentFeed)) {
                        LOG.info("Cannot find feed for parentComment {}", parentCommentId);
                    } else {
                        parentCommentFeed.setCommentCount(parentCommentFeed.getCommentCount() == null ? 1 : parentCommentFeed.getCommentCount() + 1);
                        kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC_UPDATE.getName(), parentCommentFeed);
                    }
                }
                saveInDbAndEs(notificationDetails, String.valueOf(instagramId));
            });
        } catch (Exception e) {
            if (e instanceof SocialBirdeyeException
                    && ((SocialBirdeyeException) e).getCode() == ErrorCodes.NO_SUCH_INSTAGRAM_ACCOUNT_EXIST.value()) {
                LOG.warn("IG account does not exist for comment event: {}", e.getMessage());
            } else {
                LOG.error("Error while processing IG comment: ", e);
            }
        }
    }

    private String getParentCommentId(BusinessInstagramAccount instagramAccount, String commentId) {
        InstagramCommentResponse commentResponse = instagramService.getCommentDetails(instagramAccount.getPageAccessToken(), commentId);
        return commentResponse.getParent_id();
    }

    private EngageNotificationDetails getMediaDetails(String mediaId, String pageId) {
        try {
            SearchRequest searchRequest = new SearchRequest(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            BoolQueryBuilder b = new BoolQueryBuilder();
            b.must(QueryBuilders.matchQuery("feedId", mediaId));
            if(StringUtils.isNotEmpty(pageId)) {
                b.must(QueryBuilders.matchQuery("pageId.keyword", pageId));
            }

            searchSourceBuilder.query(b);
            searchRequest.source(searchSourceBuilder);
            SearchResponse response = esService.search(searchRequest);
            SearchHit[] searchHits = response.getHits().getHits();
            return Objects.nonNull(searchHits) && searchHits.length > 0
                    ? JSONUtils.fromJSON(searchHits[0].getSourceAsString(), EngageNotificationDetails.class)
                    : null;
        } catch (Exception ex) {
            LOG.info("Something went wrong while fetching data from ES with an error", ex);
        }
        return null;
    }

    private void trigggerPostSaveForEngage(String instagramId, String postId, String commentId) {
        FreshPostNotificationRequest request = new FreshPostNotificationRequest();
        request.setPageId(instagramId);
        request.setPostId(postId);
        request.setType(EngageV2FeedTypeEnum.POST.name());
        request.setChannel(SocialChannel.INSTAGRAM.getName());
        request.setNewCommentId(commentId);
        kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_GENERATE_TOPIC.getName(),  request);
    }

    private void saveInDbAndEs(EngageNotificationDetails notificationDetails, String accountId) {

        boolean force = EngageV2FeedSubTypeEnum.MENTION.name().equalsIgnoreCase(notificationDetails.getSubType()) ? true : false;

        Integer rawFeedId = socialEngagementV2Service.checkAndSaveEngageFeed(notificationDetails, accountId, force);
        if(Objects.isNull(rawFeedId)) {
            LOG.info("This event is already processed, must be duplicate. exiting process");
            return;
        }
        notificationDetails.setRawFeedId(rawFeedId);
        kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC.getName(), notificationDetails);
    }

    private EngageNotificationDetails getEngagementCommentNotificationDetails(BusinessInstagramAccount instagramAccount,
                                                                              InstagramValue instagramValue, Long feedTime) {
        EngageNotificationDetails notificationDetails = new EngageNotificationDetails();

        notificationDetails.setPageId(instagramAccount.getInstagramAccountId());
        notificationDetails.setPageHandleName(instagramAccount.getInstagramHandle());
        notificationDetails.setText(instagramValue.getText());
        notificationDetails.setFeedId(instagramValue.getId()); // commentId
        notificationDetails.setType(EngageV2FeedTypeEnum.COMMENT.name());
        notificationDetails.setSourceId(SocialChannel.INSTAGRAM.getId());
        notificationDetails.setChannel(SocialChannel.INSTAGRAM.getName());
        notificationDetails.setFeedDate(new Date(feedTime * 1000));
        notificationDetails.setIsLikedByAdmin(false);
        notificationDetails.setIsCompleted(false);
        notificationDetails.setHideOnThread(false);
        notificationDetails.setIsEdited(false);
        notificationDetails.setPageName(instagramAccount.getInstagramAccountName());

        if (Objects.nonNull(instagramValue.getFrom())) {
            notificationDetails.setAuthorId(instagramValue.getFrom().getId());
            notificationDetails.setAuthorUsername(instagramValue.getFrom().getUsername());
            notificationDetails.setAuthorName(instagramValue.getFrom().getUsername());
            notificationDetails.setIsAdminComment(notificationDetails.getAuthorId().equalsIgnoreCase(instagramAccount.getInstagramAccountId()));
            notificationDetails.setReviewerUrl(Constants.INSTAGRAM_URL + instagramValue.getFrom().getUsername());
        }

        if (Objects.nonNull(instagramValue.getMedia())) {
            notificationDetails.setPostId(instagramValue.getMedia().getId()); // mediaId
            notificationDetails.setEngageFeedId(instagramValue.getMedia().getId());
        }

        updateAuthorDetails(notificationDetails, instagramAccount);

        return notificationDetails;
    }

    @Override
    public void updateAuthorDetails(EngageNotificationDetails notificationDetails, BusinessInstagramAccount instagramAccount) {
        InstagramUserDetailsResponse userDetailsResponse = null;
        if(Objects.nonNull(notificationDetails.getAuthorId()) && notificationDetails.getAuthorId().equalsIgnoreCase(instagramAccount.getInstagramAccountId())){
            notificationDetails.setAuthorProfileImage(instagramAccount.getInstagramAccountPictureUrl());
        }
        try {
            userDetailsResponse = Objects.isNull(notificationDetails.getAuthorId()) ? null:
                    instagramExternalService.getInstagramUserDetails(instagramAccount.getPageAccessToken(), notificationDetails.getAuthorId());
        } catch (Exception e) {
            LOG.info("Could not find instagram user details for user id {}", notificationDetails.getAuthorId());
        }

        if (Objects.nonNull(userDetailsResponse) && StringUtils.isNotEmpty(userDetailsResponse.getName())) {
            notificationDetails.setAuthorName(userDetailsResponse.getName());
            notificationDetails.setAuthorProfileImage(userDetailsResponse.getProfile_pic());
        } else {
            notificationDetails.setAuthorName(Constants.INSTAGRAM_USER);
            notificationDetails.setAuthorUsername(Constants.INSTAGRAM_USER);
        }
    }

    private BusinessInstagramAccount validateAndGetInstagramAccount(Long instagramId) {
        LOG.info("Validating instagram account for id {}", instagramId);
        BusinessInstagramAccount businessInstagramAccount=null;
        List<BusinessInstagramAccount> instagramAccounts = businessInstagramAccountRepository.findByInstagramAccountIdAndIsValid(instagramId.toString(),1);
        if (CollectionUtils.isNotEmpty(instagramAccounts)) {
            businessInstagramAccount=instagramAccounts.get(0);
        }
        return businessInstagramAccount;
    }

    @Override
    public void processDirectMessageEvents(InstagramEventRequest igEventRequest) {
        LOG.info("IG direct messaging event received : {}", igEventRequest);
        igEventRequest.getEntry().forEach(instagramEntry -> {

            List<InstagramMessaging> igMessages = instagramEntry.getMessaging();

            igMessages.forEach(igMessage -> {
                if (Boolean.parseBoolean(igMessage.getMessage().getIs_unsupported())) {
                    LOG.info("The dm is unsupported, hence returning for message id {}", igMessage.getMessage().getMid());
                }
            });
        });
    }

    private EngageNotificationDetails getMentionsNotificationDetails(InstagramMentionedMediaResponse mentionedMediaResponse, String accountName) {
        InstagramTimelineResponse mentionedMedia = mentionedMediaResponse.getMentioned_media();
        EngageNotificationDetails notificationDetails = new EngageNotificationDetails();

        notificationDetails.setChannel(SocialChannel.INSTAGRAM.getName());
        notificationDetails.setSourceId(SocialChannel.INSTAGRAM.getId());
        notificationDetails.setPostId(mentionedMedia.getId()); // mediaId
        notificationDetails.setFeedId(mentionedMedia.getId()); // mediaId
        notificationDetails.setPageId(mentionedMediaResponse.getId()); // our pageId, not the one who mentioned us
        notificationDetails.setType(EngageV2FeedTypeEnum.POST.name());
        notificationDetails.setIsLikedByAdmin(false);
        notificationDetails.setPostUrl(mentionedMedia.getPermalink()); // mediaUrl
        notificationDetails.setFeedUrl(mentionedMedia.getPermalink()); // mediaUrl
        notificationDetails.setText(mentionedMedia.getCaption());
        notificationDetails.setAuthorUsername(mentionedMedia.getUsername());
        notificationDetails.setReviewerUrl(Constants.INSTAGRAM_URL + mentionedMedia.getUsername());
        updateNotificationDetailsWithUsername(notificationDetails);
        notificationDetails.setFeedDate(mentionedMedia.getTimestamp());
        notificationDetails.setSubType(EngageV2FeedSubTypeEnum.MENTION.name());
        notificationDetails.setCanReplyPrivately(true);
        notificationDetails.setPageName(accountName);
        notificationDetails.setMentionedName(accountName);
        notificationDetails.setIsCompleted(false);
        attachMediaToNotificationDetails(notificationDetails, mentionedMedia);
        return notificationDetails;
    }

    private void attachMediaToNotificationDetails(EngageNotificationDetails notificationDetails, InstagramTimelineResponse mentionedMedia) {
        if (Constants.IMAGE.equalsIgnoreCase(mentionedMedia.getMedia_type())) {
            notificationDetails.setImageUrls(Collections.singletonList(mentionedMedia.getMedia_url()));
            return;
        } else if (Constants.VIDEO.equalsIgnoreCase(mentionedMedia.getMedia_type()) || Constants.REEL.equalsIgnoreCase(mentionedMedia.getMedia_type())) {
            notificationDetails.setVideoUrls(Collections.singletonList(mentionedMedia.getMedia_url()));
            return;
        }

        List<String> images = new ArrayList<>();
        List<String> videos = new ArrayList<>();

        if (Objects.nonNull(mentionedMedia.getChildren()) && CollectionUtils.isNotEmpty(mentionedMedia.getChildren().getData())) {
            mentionedMedia.getChildren().getData().forEach(childMedia -> {
                if (Constants.IMAGE.equalsIgnoreCase(childMedia.getMedia_type())) {
                    images.add(childMedia.getMedia_url());
                } else if (Constants.VIDEO.equalsIgnoreCase(childMedia.getMedia_type()) || Constants.REEL.equalsIgnoreCase(childMedia.getMedia_type())) {
                    videos.add(childMedia.getMedia_url());
                }
            });
        }
        notificationDetails.setImageUrls(images);
        notificationDetails.setVideoUrls(videos);
    }

    @Override
    public void fetchTaggedIGPostsSync(){
        LOG.info("Initiating IG media tags");
        try {
            int hours = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIGMediaTagEngageHours();
            int count = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIGMediaTagEngageAccountsCount();
            LocalDateTime localDateTime = LocalDateTime.now().minusHours(hours);
            Date date = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
            Page<BusinessInstagramAccount> pages = businessInstagramAccountRepository.
                    findByIsValidAndBusinessIdNotNullAndEngageSyncDateIsLessThanEqualOrderByEngageSyncDateAsc(1, date, new PageRequest(0, count));
            if (CollectionUtils.isNotEmpty(pages.getContent())) {
                pages.getContent().forEach(igAccount -> {
                    boolean hasEngagePermission = instagramSocialService.getInstagramPostPermission(Collections.singletonList(igAccount),
                            Collections.singletonList(Constants.SOCIAL_MODULE_ENGAGE));
                    if (hasEngagePermission) {
                        InstagramMediaTagRequest request = new InstagramMediaTagRequest(igAccount.getInstagramAccountId());
                        kafkaProducerService.sendObject(KafkaTopicEnum.IG_MEDIA_TAG_SYNC.getName(), request);
                    } else {
                        LOG.warn("[fetchTaggedIGPostsSync] Insufficient permission for IG account to fetch IG tags: {}", igAccount.getInstagramAccountId());
                    }
                });
                updateEngageSyncDate(pages.getContent());
            }
        } catch (Exception e) {
            LOG.info("Exception while initiating IG media tag fetch: ",e);
        }
    }
    private void updateEngageSyncDate(List<BusinessInstagramAccount> instagramAccounts) {
        List<Integer> ids = instagramAccounts.stream().map(BusinessInstagramAccount::getId).collect(Collectors.toList());
        businessInstagramAccountRepository.updateEngageSyncDate(ids,new Date());
    }
    @Override
    public void fetchTaggedIGPosts(InstagramMediaTagRequest request) {
        LOG.info("Request received to get Tagged posts for IG media accountId: {}",request.getId());
        String accountId = request.getId();
        try {
            BusinessInstagramAccount instagramAccount = validateAndGetInstagramAccount(Long.valueOf(accountId));
            if (Objects.nonNull(instagramAccount)) {
                InstagramTaggedPosts taggedPosts = igMentionExternalService.getTaggedPosts(instagramAccount);
                if (CollectionUtils.isNotEmpty(taggedPosts.getData())) {
                    List<String> previousTagged = engageFeedDetailsRepo.findFeedIdByPageIdAndType(accountId, EngageV2FeedTypeEnum.MEDIA_TAGS.name(), 50);
                    taggedPosts.getData().forEach(timelineResponse -> {
                        if(previousTagged.contains(timelineResponse.getId())) {
                            return;
                        }
                        EngageNotificationDetails notificationDetails = convertTaggedInfoToEsRequest(timelineResponse, instagramAccount);
                        saveInDbAndEs(notificationDetails, accountId);
                    });
                }
            }else{
                LOG.info("No valid IG account found for instagram account id : {}", accountId);
            }
        } catch (Exception e) {
            LOG.info("Error fetching tagged iG posts for accountId: {}",accountId,e);
        }
    }

    @Override
    public void sendEventToIgNotification(Map<String, Object> eventRequest) {
        kafkaProducerService.sendObjectV1("instagram-notification",eventRequest);
    }

    private EngageNotificationDetails convertTaggedInfoToEsRequest(InstagramTimelineResponse timelineResponse, BusinessInstagramAccount instagramAccount) {
        EngageNotificationDetails notificationDetails = new EngageNotificationDetails();
        List<String> images = new ArrayList<>();
        List<String> videos = new ArrayList<>();

        notificationDetails.setChannel(SocialChannel.INSTAGRAM.getName());
        notificationDetails.setSourceId(SocialChannel.INSTAGRAM.getId());
        notificationDetails.setLocationId(instagramAccount.getBusinessId());
        notificationDetails.setAccountId(instagramAccount.getAccountId());
        notificationDetails.setPostId(timelineResponse.getId()); // mediaId
        notificationDetails.setFeedId(timelineResponse.getId()); // mediaId
        notificationDetails.setPageId(instagramAccount.getInstagramAccountId());// our pageId, not the one who mentioned us
        notificationDetails.setIsCompleted(false);
        notificationDetails.setType(EngageV2FeedTypeEnum.MEDIA_TAGS.name());
        notificationDetails.setIsDeleted(false);
        notificationDetails.setCanReplyPrivately(false);
        notificationDetails.setIsLikedByAdmin(false);
        notificationDetails.setHideOnThread(false);
        notificationDetails.setIsBlocked(false);
        notificationDetails.setPostUrl(timelineResponse.getPermalink()); // mediaUrl
        notificationDetails.setFeedUrl(timelineResponse.getPermalink()); // mediaUrl
        notificationDetails.setImageUrls(Collections.singletonList(timelineResponse.getMedia_url()));
        //Not a real Time event,flag for Engage emailAlert
        notificationDetails.setIsRealTimeNotification(false);
        if (Constants.IMAGE.equalsIgnoreCase(timelineResponse.getMedia_type())) {
            notificationDetails.setImageUrls(Collections.singletonList(timelineResponse.getMedia_url()));
        } else if (Constants.VIDEO.equalsIgnoreCase(timelineResponse.getMedia_type()) || Constants.REEL.equalsIgnoreCase(timelineResponse.getMedia_type())) {
            notificationDetails.setVideoUrls(Collections.singletonList(timelineResponse.getMedia_url()));
        }else if (Objects.nonNull(timelineResponse.getChildren()) && CollectionUtils.isNotEmpty(timelineResponse.getChildren().getData())) {
            timelineResponse.getChildren().getData().forEach(childMedia -> {
                if (Constants.IMAGE.equalsIgnoreCase(childMedia.getMedia_type())) {
                    images.add(childMedia.getMedia_url());
                } else if (Constants.VIDEO.equalsIgnoreCase(childMedia.getMedia_type()) || Constants.REEL.equalsIgnoreCase(childMedia.getMedia_type())) {
                    videos.add(childMedia.getMedia_url());
                }
            });
            notificationDetails.setImageUrls(images);
            notificationDetails.setVideoUrls(videos);
        }
        notificationDetails.setText(timelineResponse.getCaption());
        notificationDetails.setAuthorUsername(timelineResponse.getUsername());
        notificationDetails.setReviewerUrl(Constants.INSTAGRAM_URL + timelineResponse.getUsername());
        notificationDetails.setAuthorName((timelineResponse.getUsername()));
        notificationDetails.setFeedDate(timelineResponse.getTimestamp());
        notificationDetails.setPageName(instagramAccount.getInstagramHandle());
        notificationDetails.setMentionedName(instagramAccount.getInstagramHandle());

        return notificationDetails;

    }
}
