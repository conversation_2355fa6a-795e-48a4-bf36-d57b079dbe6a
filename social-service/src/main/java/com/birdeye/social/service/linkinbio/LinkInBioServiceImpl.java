package com.birdeye.social.service.linkinbio;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.SocialPropertyStatusEnum;
import com.birdeye.social.dto.BusinessLinksInfo;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.BusinessSocialEnabled;
import com.birdeye.social.dto.LocationDetailsDTO;
import com.birdeye.social.entities.SocialBusinessProperty;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.linkinbio.SocialLinkInBioInfo;
import com.birdeye.social.entities.linkinbio.SocialLinkInfo;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.exception.ExternalAPIErrorCode;
import com.birdeye.social.external.exception.ExternalAPIException;
import com.birdeye.social.model.SocialPostSchedulerMetadata;
import com.birdeye.social.model.linkinbio.*;
import com.birdeye.social.platform.dao.BusinessRepository;
import com.birdeye.social.service.SocialBusinessService;
import com.birdeye.social.service.SocialPostService;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;

@Service
public class LinkInBioServiceImpl implements LinkInBioService{

    private static final Logger logger	= LoggerFactory.getLogger(LinkInBioServiceImpl.class);

    @Autowired
    LinkInBioInternalService linkInBioInternalService;

    @Autowired
    IBusinessCoreService businessCoreService;

    @Autowired
    SocialPostService socialPostService;

    @Autowired
    SocialBusinessService socialBusinessService;
    @Autowired
    private BusinessRepository businessRepo;

    @Override
    public LinkInBioResponse createLinkInBio(LinkInBioRequest linkInBioRequest) {
        logger.info("Prepare Link in bio info for request : {}",linkInBioRequest);
        SocialLinkInBioInfo socialLinkInBioInfo = new SocialLinkInBioInfo();
        boolean isUrlPresent = linkInBioInternalService.isUrlPresent(linkInBioRequest.getPageUrl());
        if(isUrlPresent){
            logger.info("Url already exist in DB for given url : {}",linkInBioRequest.getLogoUrl());
            throw new SocialBirdeyeException(ErrorCodes.DUPLICATE_URL_FOUND,"Duplicate Url found");
        }
        return createOrUpdateLinkInBio(linkInBioRequest,socialLinkInBioInfo);
    }

    private LinkInBioResponse createOrUpdateLinkInBio(LinkInBioRequest linkInBioRequest, SocialLinkInBioInfo socialLinkInBioInfo) {
        linkInBioInternalService.saveLinkInBioInfo(linkInBioRequest,socialLinkInBioInfo);
        logger.info("Data save to table SocialLinkInBioInfo with id :{}",socialLinkInBioInfo.getId());
        return linkInBioInternalService.prepareLinkInBioResponse(socialLinkInBioInfo,false);
    }

    @Override
    public LinkInBioWrapperResponse getLinkInBioDetails(Long enterpriseId,LinkInBioGetRequest request) {
        LinkInBioWrapperResponse wrapperResponse = new LinkInBioWrapperResponse();
        logger.info("Get link in bio response for enterprise id : {} and request : {}",enterpriseId,request);
        Sort.Direction direction = Objects.equals(request.getSortOrderEnum(),SortOrderEnum.ASC) ? Sort.Direction.ASC : Sort.Direction.DESC;
        Page<SocialLinkInBioInfo> socialLinkInBioInfoList;
        if(StringUtils.isNotEmpty(request.getSearch())){
            socialLinkInBioInfoList = linkInBioInternalService
                    .searchByPageName(enterpriseId,request.getSearch(),new PageRequest(request.getPage(), request.getSize()));
        }else {
            socialLinkInBioInfoList = linkInBioInternalService.getLinkInBioByEnterpriseId(enterpriseId,
                    new PageRequest(request.getPage(), request.getSize(), direction, request.getSortOn().name()));
        }
        if(CollectionUtils.isEmpty(socialLinkInBioInfoList.getContent())){
            logger.info("No Link in Bio found for enterprise id : {}",enterpriseId);
            return wrapperResponse;
        }
        List<LinkInBioResponse> linkInBioResponses = new ArrayList<>();
        socialLinkInBioInfoList.getContent().forEach(socialLinkInBioInfo -> {
            LinkInBioResponse link = linkInBioInternalService.prepareLinkInBioResponse(socialLinkInBioInfo,false);
            linkInBioResponses.add(link);
        });
        wrapperResponse.setTotalPages(socialLinkInBioInfoList.getTotalPages());
        wrapperResponse.setTotalElements(socialLinkInBioInfoList.getTotalElements());
        wrapperResponse.setLinks(linkInBioResponses);
        return wrapperResponse;
    }

    @Override
    public LinkValidationResponse checkIfLinkIsPresent(String url) {
        LinkValidationResponse response = new LinkValidationResponse();
        logger.info("Validation check for link in bio url : {}",url);
        response.setUrl(url);
        response.setAvailable(!linkInBioInternalService.isUrlPresent(url));
        return response;
    }

    @Override
    public List<LinkValidationResponse> validateCreatePostLinks(Long enterpriseId,Integer businessId,List<LinkValidationRequest> request) {
        logger.info("Request received to check url validation with payload : {}",request);
        List<LinkValidationResponse> linkValidationResponses = new ArrayList<>();
        request.forEach(urlRequest ->{
            logger.info("Checking the url : {}",urlRequest);
            if(Objects.isNull(urlRequest.getLinkInBioId())){
                logger.info("Link in bio id is null for SMB : {}",enterpriseId);
                urlRequest.setLinkInBioId(linkInBioInternalService.findFirstIdByEnterpriseId(enterpriseId));
            }
            if(Objects.isNull(urlRequest.getLinkInBioId())){
                logger.info("No id found to check url exist for url : {}",urlRequest);
                createLinkInBioIfSMB(enterpriseId,businessId);
            }
            LinkValidationResponse response = new LinkValidationResponse();
            response.setAvailable(linkInBioInternalService.checkIfLinkExistsForId(urlRequest.getUrl(),urlRequest.getLinkInBioId()));
            response.setUrl(urlRequest.getUrl());
            linkValidationResponses.add(response);
        });
        return linkValidationResponses;
    }

    @Override
    public void updateLinksForLinkInBio(Integer linkInBioId, LinkInBioRequest linkInBioRequest) {
        SocialLinkInBioInfo socialLinkInBioInfo = linkInBioInternalService.findById(linkInBioId);
        if(Objects.isNull(socialLinkInBioInfo)){
            logger.info("No socialLinkInBioInfo found for id : {}",linkInBioId);
            throw new SocialBirdeyeException(ErrorCodes.REQUEST_ENTITY_NOT_FOUND,String.format("No link in bio found with id %s",linkInBioId));
        }
        SocialLinkInBioInfo socialLinkInBioInfo1 = linkInBioInternalService.findByLinkInBioUrl(linkInBioRequest.getPageUrl());
        if(Objects.nonNull(socialLinkInBioInfo1) && !Objects.equals(socialLinkInBioInfo1.getId(),socialLinkInBioInfo.getId())){
            logger.info("Url already taken : {}",linkInBioRequest.getPageUrl());
            throw new SocialBirdeyeException(ErrorCodes.DUPLICATE_URL_FOUND,"Url already taken");
        }
        linkInBioInternalService.saveLinkInBioInfo(linkInBioRequest,socialLinkInBioInfo);
        List<SocialLinkInfo> socialLinkInfoList = linkInBioInternalService.findByLinkInBioId(linkInBioId);
        linkInBioInternalService.saveLinkInfo(socialLinkInfoList,linkInBioRequest.getLinks(),linkInBioId);
    }

    @Override
    public LinkInBioResponse getLinksOfLinkInBio(Integer linkInBioId) {
        logger.info("Get links for link in bio id : {}",linkInBioId);
        SocialLinkInBioInfo linkInBioInfo = linkInBioInternalService.findById(linkInBioId);
        if(Objects.isNull(linkInBioInfo)){
            logger.info("No link in bio found for given id : {}",linkInBioId);
            throw new SocialBirdeyeException(ErrorCodes.REQUEST_ENTITY_NOT_FOUND,"No data available for the given id");
        }
        return linkInBioInternalService.prepareLinkInBioResponse(linkInBioInfo,true);
    }

    @Override
    public LinkInBioResponse getLinksForSmb(Long enterpriseId,Integer businessId) {
        SocialLinkInBioInfo linkInBioInfo = linkInBioInternalService.findFirstByEnterpriseId(enterpriseId);
        if(Objects.isNull(linkInBioInfo)){
            logger.info("No link in bio found for given enterprise id : {}",enterpriseId);
            createLinkInBioIfSMB(enterpriseId,businessId);
            linkInBioInfo = linkInBioInternalService.findFirstByEnterpriseId(enterpriseId);
            if(Objects.isNull(linkInBioInfo)){
                return new LinkInBioResponse();
            }
        }
        return linkInBioInternalService.prepareLinkInBioResponse(linkInBioInfo,true);
    }

    private void createLinkInBioIfSMB(Long enterpriseId,Integer businessId) {
        BusinessPropertyEventRequest request = new BusinessPropertyEventRequest();
        request.setBusinessId(businessId);
        request.setBusinessNumber(enterpriseId);
        BusinessSocialEnabled businessSocialEnabled = businessCoreService.getEnabledProperty(enterpriseId);
        if(Objects.isNull(businessSocialEnabled) || Objects.isNull(businessSocialEnabled.getIsSocialEnabled())
                || Objects.isNull(businessSocialEnabled.getIsSocialEnabled().getEnabled())
                || Objects.equals(0,SocialPropertyStatusEnum.reportingStatus(businessSocialEnabled.getIsSocialEnabled().getEnabled()))) {
            return;
        }
        request.setIsSocialEnabled(businessSocialEnabled.getIsSocialEnabled().getEnabled());
        createLinkInBioViaSocialEnabled(request);
    }

    @Override
    public void updateSocialBusinessProperty(BusinessPropertyEventRequest request) {
        if(Objects.isNull(request) || Objects.isNull(request.getBusinessNumber()) || Objects.isNull(request.getIsSocialEnabled()) ){
            logger.info("Request id null");
            return;
        }
        logger.info("Request received to update business property : {}",request);
        socialBusinessService.updateBusinessProperty(request);
    }

    @Override
    public void createLinkInBioViaSocialEnabled(BusinessPropertyEventRequest request) {
        if(Objects.isNull(request) || Objects.isNull(request.getBusinessNumber())
                || Objects.equals(0,SocialPropertyStatusEnum.reportingStatus(request.getIsSocialEnabled()))){
            logger.info("Request is null");
            return;
        }
        logger.info("Social enable event with request : {}",request);
        SocialLinkInBioInfo socialLinkInBioInfoList = linkInBioInternalService.findFirstByEnterpriseId(request.getBusinessNumber());
        if(Objects.nonNull(socialLinkInBioInfoList)){
            logger.info("Link in bio already created for given request : {}",request);
            return;
        }
        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(request.getBusinessId(), false);
        if(linkInBioInternalService.checkBusinessSMB(businessLiteDTO)) {
            BusinessLinksInfo businessLinksInfo = businessCoreService.getBusinessLinksById(request.getBusinessId());
            if(Objects.isNull(businessLinksInfo)){
                logger.info("No links found for the smb with business id :{}",request.getBusinessId());
                return;
            }
            linkInBioInternalService.saveLinkInBioInfoViaSocialEnabled(businessLiteDTO,businessLinksInfo,businessLiteDTO);
            return;
        }
        logger.info("Business id not SMB :{}",businessLiteDTO.getBusinessNumber());
        List<BusinessLiteDTO> getBusinessLocations = businessCoreService.getBusinessLiteListByEnterpriseId(request.getBusinessId());
        if(CollectionUtils.isEmpty(getBusinessLocations)){
            logger.info("No business found for given enterprise : {}",request.getBusinessId());
            return;
        }
        for(BusinessLiteDTO businessLite : getBusinessLocations){
            if(Objects.equals(businessLite.getActivationStatus(),"suspended")){
                logger.info("The account is suspended : {}",businessLite);
                continue;
            }
            BusinessLinksInfo businessLinksInfo = businessCoreService.getBusinessLinksById(businessLite.getBusinessId());
            if(Objects.isNull(businessLinksInfo)){
                logger.info("No businessLinksInfo found for the business id :{}",businessLite.getBusinessId());
                continue;
            }
            linkInBioInternalService.saveLinkInBioInfoViaSocialEnabled(businessLiteDTO,businessLinksInfo,businessLite);
        }
    }

    @Override
    public void deleteLinkInBio(Integer linkInBioId) {
        linkInBioInternalService.deleteLinkInBio(linkInBioId);
    }

    @Override
    public void deleteLinkForLinkInBio(Integer linkInBioId, Integer linkId) {
        logger.info("Request received to delete link in bio : {} link :{}",linkInBioId,linkId);
        linkInBioInternalService.deleteLinkForLinkInBio(linkInBioId,linkId);
    }

    @Override
    public LinkInBioPublicResponse getLinkInBioResponse(String linkInBioUrl) {
        SocialLinkInBioInfo socialLinkInBioInfo = linkInBioInternalService.findByLinkInBioUrl(linkInBioUrl);
        if (Objects.isNull(socialLinkInBioInfo)) {
            logger.info("No link in bio found for given url : {}", linkInBioUrl);
            throw new SocialBirdeyeException(ErrorCodes.REQUEST_ENTITY_NOT_FOUND, "No data available for the given url");
        }
        try {
            LinkInBioResponse linkInBioInfo = linkInBioInternalService.prepareLinkInBioResponse(socialLinkInBioInfo, true);
            BusinessLiteDTO businessDetails = businessCoreService.getBusinessLiteByNumber(socialLinkInBioInfo.getEnterpriseId());
            return linkInBioInternalService.prepareLinkInBioPublicUrlResponse(linkInBioInfo, businessDetails);
        }catch (ExternalAPIException ex){
            logger.info("Error while fetching link in bio info for url : {} with error code : {}",linkInBioUrl,ex.getLocalizedMessage());
            if(Objects.equals(ExternalAPIErrorCode.CLIENT_ERROR, ex.getCode()) && ex.getMessage().contains("Forbidden")) {
                throw new SocialBirdeyeException(ErrorCodes.INACTIVE_BUSINESS, "Business is inactive");
            }else{
                throw new SocialBirdeyeException(ErrorCodes.REQUEST_ENTITY_NOT_FOUND, "No data available for the given url");
            }
        }catch (Exception e){
            logger.info("Error while fetching link in bio info for url : {} with error : {}",linkInBioUrl,e.getMessage());
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Internal server error occurred");
        }
    }

    @Override
    @Transactional
    public void updateClickCount(Integer buttonId) {
        logger.info("Update click count for button id : {}",buttonId);
        linkInBioInternalService.updateClickCount(buttonId);
    }

    @Override
    public void updateViewCount(Integer linkId, Long enterpriseId) {
        logger.info("Update view count for link id : {} and enterprise id : {}",linkId,enterpriseId);
        linkInBioInternalService.updateViewCount(linkId,enterpriseId);
    }

    @Override
    public LinkInBioWrapperResponse getReportsData(Long enterpriseId, LinkInBioGetRequest request) {
        logger.info("Get total views and total count for enterprise id : {}",enterpriseId);
        return getLinkInBioDetails(enterpriseId,request);
    }

    @Override
    public void createLinkInBioForCoreEvent(BusinessSignUpEvent businessSignUpEvent) {
        logger.info("Create link in bio for request : {}",businessSignUpEvent);
        BusinessSocialEnabled businessSocialEnabled = null;
        try {
            businessSocialEnabled = businessCoreService.getEnabledProperty(businessSignUpEvent.getBusinessNumber());
        }catch (Exception e){
            logger.info("Error while fetching business social enabled property for business number : {} {}",businessSignUpEvent.getBusinessNumber(),e.getMessage());
        }
        if(Objects.isNull(businessSocialEnabled) || Objects.isNull(businessSocialEnabled.getIsSocialEnabled())){
            logger.info("Social is not enabled for enterprise : {} with businessSocialEnabled : {}",businessSignUpEvent,businessSocialEnabled);
            return;
        }
        if(businessSignUpEvent.getIsSMB() || businessSignUpEvent.getIsEnterprise()){
            BusinessPropertyEventRequest event = new BusinessPropertyEventRequest();
            event.setBusinessNumber(businessSignUpEvent.getBusinessNumber());
            event.setBusinessId(businessSignUpEvent.getBusinessId());
            event.setIsSocialEnabled(businessSocialEnabled.getIsSocialEnabled().getEnabled());
            socialBusinessService.updateBusinessPropertyBySignUpEvent(event);
        }
        if(Objects.equals(0,SocialPropertyStatusEnum.reportingStatus(businessSocialEnabled.getIsSocialEnabled().getEnabled()))
                || !businessSignUpEvent.getBusinessType().equalsIgnoreCase("Business")){
            logger.info("The type of business is enterprise or reseller  : {} ",businessSignUpEvent);
            return;
        }
        BusinessLinksInfo businessLinksInfo = businessCoreService.getBusinessLinksById(businessSignUpEvent.getBusinessId());
        if(Objects.isNull(businessLinksInfo)){
            logger.info("No links found for the business id :{}",businessSignUpEvent.getBusinessId());
            return;
        }
        BusinessLiteDTO businessLiteDTO = new BusinessLiteDTO();
        if(Boolean.FALSE.equals(businessSignUpEvent.getIsSMB())) {
            logger.info("Business is enterprise with id:{}",businessSignUpEvent.getBusinessId());
            businessLiteDTO = businessCoreService.getBusinessLite(businessSignUpEvent.getEnterpriseId(), false);
        }
        linkInBioInternalService.saveLinkInBio(businessLinksInfo,businessSignUpEvent,businessLiteDTO);
    }

    @Override
    public LinkInBioResponse updateLinkInBioInfo(LinkInBioRequest linkInBioRequest, Integer linkId) {
        logger.info("Update link in Bio for id : {} and request : {}",linkId,linkInBioRequest);
        SocialLinkInBioInfo socialLinkInBioInfo = linkInBioInternalService.findById(linkId);
        if(Objects.isNull(socialLinkInBioInfo)){
            throw new SocialBirdeyeException(ErrorCodes.REQUEST_ENTITY_NOT_FOUND,"No Link in bio found with given id");
        }
        SocialLinkInBioInfo socialLinkInBioInfo1 = linkInBioInternalService.findByLinkInBioUrl(linkInBioRequest.getPageUrl());
        if(Objects.nonNull(socialLinkInBioInfo1) && !Objects.equals(socialLinkInBioInfo1.getId(),socialLinkInBioInfo.getId())){
            throw new SocialBirdeyeException(ErrorCodes.DUPLICATE_URL_FOUND,"Url already taken");
        }
        return createOrUpdateLinkInBio(linkInBioRequest,socialLinkInBioInfo);
    }

    @Override
    public void createLinksIfNotPresent(LinkInfoEventRequest request) {
        SocialPost socialPost = socialPostService.findByPostId(request.getPostId());
        if(Objects.isNull(socialPost) || Objects.isNull(socialPost.getPostMetadata())){
            logger.info("No data found for post id : {}",request);
            return;
        }
        if(StringUtils.isEmpty(request.getImage()) && Objects.nonNull(socialPost.getVideoIds())){
            String thumbnail = socialPostService.findThumbnailById(socialPost.getVideoIds());
            request.setImage(thumbnail);
        }
        try {
            SocialPostSchedulerMetadata metadata = JSONUtils.fromJSON(socialPost.getPostMetadata(),SocialPostSchedulerMetadata.class);
            if(Objects.nonNull(metadata) && StringUtils.isNotEmpty(metadata.getLinkInBioDetails())){
                List<LinkInBioPostMetaData> linkInBioPostMetaData = JSONUtils.collectionFromJSON(metadata.getLinkInBioDetails(),LinkInBioPostMetaData.class);
                if(CollectionUtils.isEmpty(linkInBioPostMetaData)){
                    logger.info("No metadata for request found : {}",request);
                    return;
                }
                BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(request.getEnterpriseId(),false);
                if(Objects.isNull(businessLiteDTO)){
                    logger.info("No business found with business id : {}",request.getBusinessId());
                    return;
                }
                boolean checkSMB = linkInBioInternalService.checkBusinessSMB(businessLiteDTO);
                for (LinkInBioPostMetaData link : linkInBioPostMetaData){
                    linkInBioInternalService.addLinkToLinkInfo(link,checkSMB,businessLiteDTO.getBusinessNumber(),request.getImage());
                }
            }
        } catch (Exception e) {
            logger.error("Error while parsing link in bio meta data",e);
        }
    }

    @Override
    public void postLinkInBioForBusiness(BusinessSignUpEvent event) {
        logger.info("Create link in bio for request : {}",event);
        SocialLinkInBioInfo socialLinkInBioInfo = linkInBioInternalService.findByBusinessId(event.getBusinessId());
        if(Objects.nonNull(socialLinkInBioInfo)){
            logger.info("Info already exist in link in bio info : {}",event);
            return;
        }
        BusinessLinksInfo businessLinksInfo = businessCoreService.getBusinessLinksById(event.getBusinessId());
        if(Objects.isNull(businessLinksInfo)){
            logger.info("No links found for the business id :{}",event.getBusinessId());
            return;
        }
        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(event.getBusinessId(), false);
        if(Objects.isNull(businessLiteDTO)){
            logger.info("Business lite dto is null");
            return;
        }
        // Use business number from previous API.
        boolean checkSmb = linkInBioInternalService.checkBusinessSMB(businessLiteDTO);
        event.setIsSMB(checkSmb);
        if(!checkSmb){
            businessLiteDTO = businessCoreService.getBusinessLite(event.getEnterpriseId(), false);
        }
        linkInBioInternalService.saveLinkInBio(businessLinksInfo,event,businessLiteDTO);
    }

    @Override
    public LinkInBioWrapperResponse getLinkInBioResponseForEnterprise(Long businessNumber) {
        logger.info("Get link in bio info for enterprise id : {}",businessNumber);
        LinkInBioWrapperResponse wrapperResponse = new LinkInBioWrapperResponse();
        List<SocialLinkInBioInfo> linkInBioInfos = linkInBioInternalService.findByEnterpriseId(businessNumber);
        if(CollectionUtils.isEmpty(linkInBioInfos)){
            logger.info("No Link in Bio found for enterprise id : {}",businessNumber);
            return wrapperResponse;
        }
        List<LinkInBioResponse> linkInBioResponses = new ArrayList<>();
        linkInBioInfos.forEach(socialLinkInBioInfo -> {
            logger.info("Prepare response for id : {}",socialLinkInBioInfo.getId());
            LinkInBioResponse link = linkInBioInternalService.prepareLinkInBioResponse(socialLinkInBioInfo,false);
            linkInBioResponses.add(link);
        });
        wrapperResponse.setLinks(linkInBioResponses);
        return wrapperResponse;
    }

    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }

    @Async
    @Override
    public void removeDuplicate(List<Long> businessNumberList) {
        logger.info("Request received to delete duplicate entries from social_business_property table: {}", businessNumberList);
        if(CollectionUtils.isEmpty(businessNumberList)) {
            businessNumberList = new ArrayList<>(socialBusinessService.getDuplicateEnterpriseIds());
        }

        List<List<Long>> businessNumberBatches = partitionList(businessNumberList, 100);
        for (List<Long> batch : businessNumberBatches) {
            logger.info("Processing batch of size: {}", batch.size());
            Map<Long,Integer> mp = new HashMap<>();
            batch.forEach(businessNumber-> {
                try {
                    Integer socialValue =  businessRepo.socialEnabledByBusinessId(businessNumber);
                    mp.put(businessNumber, socialValue);
                } catch (Exception e) {
                    logger.info("Core call failed for businessNumber: {}", businessNumber);
                }
            });
            try {
                List<SocialBusinessProperty> businessPropertyList = new ArrayList<>();
                for (Long businessNumber : batch) {
                    try {
                        if(!mp.containsKey(businessNumber)) {
                            continue;
                        }
                        SocialBusinessProperty businessProperty = new SocialBusinessProperty();
                        businessProperty.setEnterpriseId(businessNumber);
                        businessProperty.setSocialEnabled(mp.get(businessNumber));
                        businessPropertyList.add(businessProperty);
                    } catch (Exception e) {
                        logger.info("Exception while processing entry: {}", businessNumber);
                    }
                }

                socialBusinessService.deleteDataForEnterprise(batch);
                socialBusinessService.saveBusinessPropertyBulk(businessPropertyList);

            } catch (Exception e) {
                logger.error("Exception while processing batch: {}", e.getMessage());
            }
        }

        logger.info("Process complete for removing duplicate!!");

    }

    @Override
    public void deleteScheduledPostsOnSocialDisbale(BusinessPropertyEventRequest request) {
        logger.info("LinkInBioServiceImpl#deleteScheduledPostsOnSocialDisbale(): received request for scheduled post purge on social disable : {}",request);
        if (isStatusChangeToSocialDisable(request)) {
            logger.info("LinkInBioServiceImpl#deleteScheduledPostsOnSocialDisbale(): Social Status is changed from enabled to disabled for enterprise : {} ", request.getBusinessNumber());
            logger.info("LinkInBioServiceImpl#deleteScheduledPostsOnSocialDisbale(): Deleting scheduled posts for enterprise : {}", request.getBusinessNumber());
            socialPostService.deleteScheduledPosts(request.getBusinessId().intValue(), true);
        }
        else {
            logger.info("LinkInBioServiceImpl#deleteScheduledPostsOnSocialDisbale(): No deletion of scheduled posts required  for enterprise : {} as social status is not changed from enabled to disabled", request.getBusinessNumber());
        }
    }

    private boolean isStatusChangeToSocialDisable(BusinessPropertyEventRequest request) {
        Integer oldSocialEnabled = request.getOldSocialEnabled();
        Integer newSocialEnabled = request.getIsSocialEnabled();
        Boolean oldSocialEnableStatus = SocialPropertyStatusEnum.socialStatus(oldSocialEnabled);
        Boolean newSocialEnableStatus = SocialPropertyStatusEnum.socialStatus(newSocialEnabled);
        return (Boolean.TRUE.equals(oldSocialEnableStatus) && Boolean.FALSE.equals(newSocialEnableStatus));
    }
}
