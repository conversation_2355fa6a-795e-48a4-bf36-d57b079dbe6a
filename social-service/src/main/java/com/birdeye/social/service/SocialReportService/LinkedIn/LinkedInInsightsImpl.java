package com.birdeye.social.service.SocialReportService.LinkedIn;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessLinkedinPageRepository;
import com.birdeye.social.dao.SocialPostRepository;
import com.birdeye.social.dao.reports.BusinessPostsRepository;
import com.birdeye.social.dao.reports.LinkedInPageInsightRepo;
import com.birdeye.social.dao.reports.PostInsightAuditRepo;
import com.birdeye.social.dao.reports.SocialReportPropertyRepository;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.dto.EsPostDataPoint;
import com.birdeye.social.dto.PostInsightDTO;
import com.birdeye.social.dto.*;
import com.birdeye.social.dto.report.BusinessPostMetadata;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessLinkedinPage;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.entities.report.LinkedInPageInsight;
import com.birdeye.social.entities.report.PostInsightAudit;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.insights.ES.*;
import com.birdeye.social.insights.ES.Request.DataModel;
import com.birdeye.social.insights.ES.Request.ESPageRequest;
import com.birdeye.social.insights.ES.Request.InsightsESRequest;
import com.birdeye.social.insights.Facebook.PostDataAndInsightResponse;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.insights.constants.GroupByType;
import com.birdeye.social.insights.constants.InsightsConstants;
import com.birdeye.social.linkedin.ILinkedinConnectService;
import com.birdeye.social.linkedin.LinkedinService;
import com.birdeye.social.linkedin.response.LinkedInAPIResponse;
import com.birdeye.social.linkedin.response.LinkedInElement;
import com.birdeye.social.linkedin.response.LinkedInStat;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.BackfillRequest;
import com.birdeye.social.model.EngagementUpdateRequest;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.EsService;
import com.birdeye.social.service.SocialBusinessPropertyService;
import com.birdeye.social.service.SocialReportService.Converter.DbDataConverter;
import com.birdeye.social.service.SocialReportService.Converter.LinkedInInsightConverter;
import com.birdeye.social.service.SocialReportService.Converter.ReportDataConverter;
import com.birdeye.social.service.SocialReportService.ES.ReportsEsService;
import com.birdeye.social.service.SocialTagService;
import com.birdeye.social.utils.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Functions;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.time.DateUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.BATCH_SIZE_FOR_TOP_POSTS;
import static com.birdeye.social.constant.Constants.THREADPOOL_SIZE_FOR_TOP_POSTS;


@Service
public class LinkedInInsightsImpl implements LinkedInInsights{

    public static final String LINKEDIN_BACKFILL_LIMIT = "linkedin.backfill.limit";
    @Autowired
    private BusinessLinkedinPageRepository businessLinkedinPageRepo;

    @Autowired
    private LinkedinService linkedinService;

    @Autowired
    private BusinessPostsRepository businessPostsRepo;

    @Autowired
    private PostInsightAuditRepo postInsightAuditRepo;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private LinkedInInsightConverter linkedInInsightConverter;

    @Autowired
    private LinkedInESService linkedInESService;

    @Autowired
    private LinkedInPageService linkedInPageService;

    @Autowired
    private ReportDataConverter reportDataConverter;

    @Autowired
    private ReportsEsService reportsEsService;

    @Autowired
    private ILinkedinConnectService linkedinConnectService;

    @Autowired
    private LinkedInPageInsightRepo linkedInPageInsightRepo;

    @Autowired
    private DbDataConverter dbDataConverter;
    @Autowired
    private CommonService commonService;
    @Autowired
    private EsService esService;

    @Autowired
    private SocialTagService socialTagService;
    @Autowired
    private SocialReportPropertyRepository reportPropertyRepository;

    @Autowired
    private SocialBusinessPropertyService socialBusinessPropertyService;

    @Autowired
    private IBusinessCoreService coreService;

    @Autowired
    private SocialPostRepository socialPostRepository;

    private static final Logger LOG = LoggerFactory.getLogger(LinkedInInsightsImpl.class);

    private final String dateFormatterString = "yyyy-MM-dd HH:mm:ss";

    private static final String SOCIAL_PAGE_SCAN_EVENT = "page_scanned";
    private static final String SOCIAL_BACKFILL_PAGE_SCAN_EVENT = "linkedin_backfill_page_scanned";
   // private static final SimpleDateFormat DATE_FORMATTER = new SimpleDateFormat("yyyy-MM-dd");

    private static final String VIDEO_METRICS = "VIDEO_VIEW";


    @Override
    public PageInsightsResponse getLinkedInInsightsForPage(InsightsRequest insights) throws Exception {
        LOG.info("Request to get insight for pages from business ids with enterprise id: {}", insights.getEnterpriseId());
        List<String> pageIds = getPageIdsFromBids(insights.getBusinessIds());
        Date queryStartDate = insights.getStartDate();
        String index = ElasticConstants.LINKEDIN_PAGE_INSIGHTS.getName();

        if(CollectionUtils.isEmpty(pageIds)){
            return new PageInsightsResponse();
        }
        String startDate  = reportsEsService.getStartDate(pageIds,index);

        insights.setStartDate(reportDataConverter.getStartDate(insights,insights.getBusinessIds(),startDate));
        if(SearchTemplate.PAGE_POST_ENGAGEMENT.getName().equalsIgnoreCase(insights.getReportType())) {
            insights.setStartDate(reportsEsService.getFirstPostDate(insights, pageIds, queryStartDate));
        }
        long diff = insights.getEndDate().getTime() - insights.getStartDate().getTime();
        long noOfDaysBetween = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        reportDataConverter.setGroupByType(insights,noOfDaysBetween);
        InsightsESRequest request = reportDataConverter.createESRequestForPage(insights, ElasticConstants.LINKEDIN_PAGE_INSIGHTS.getName(),
                pageIds, SocialChannel.LINKEDIN.getId());
        PageInsightEsData pageInsightDataFromEs = reportsEsService.getPageInsightDataFromEs(request);
        reportsEsService.updatePostCountForDates(request, pageInsightDataFromEs, pageIds, queryStartDate);
        GroupByType type =  GroupByType.getByName(insights.getGroupByType());
        LOG.info("Fetched insight from es for pages");
        PageInsightsResponse pageInsightsResponse = reportDataConverter.prepareFBPageInsightResponse(pageInsightDataFromEs, SearchTemplate.searchTemplate(insights.getReportType()));
        List<PageInsightDataPoint> dataPoints = pageInsightDataFromEs.getPointsList();
        pageInsightsResponse.setDateDiff(reportsEsService.getTimeDifference(request));
        pageInsightsResponse.setGroupByType(type.getType());
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(dataPoints)){
            reportDataConverter.customizedDataForFirstIndex(insights.getEndDate(),type,pageInsightsResponse,dataPoints);
        }
        pageInsightsResponse.setDataPoints(dataPoints);
        return pageInsightsResponse;
    }

    @Override
    public PageInsightV2EsData getPageInsightsESData(InsightsRequest insights) throws Exception {
        LOG.info("getPageInsightsESData Request to get insight for pages from business ids with enterprise id: {}", insights.getEnterpriseId());
        List<String> pageIds = getPageIdsFromBids(insights.getBusinessIds());
        Date queryStartDate = insights.getStartDate();
        String index = ElasticConstants.LINKEDIN_PAGE_INSIGHTS.getName();

        if(CollectionUtils.isEmpty(pageIds)){
            return new PageInsightV2EsData();
        }
        /*String startDate  = reportsEsService.getStartDate(pageIds,index);

        insights.setStartDate(reportDataConverter.getStartDate(insights,insights.getBusinessIds(),startDate));
        if(SearchTemplate.PAGE_POST_ENGAGEMENT.getName().equalsIgnoreCase(insights.getReportType())) {
            insights.setStartDate(reportsEsService.getFirstPostDate(insights, pageIds, queryStartDate));
        }*/
        long diff = insights.getEndDate().getTime() - insights.getStartDate().getTime();
        long noOfDaysBetween = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        reportDataConverter.setGroupByType(insights,noOfDaysBetween);


        InsightsESRequest request = reportDataConverter.createESRequestForPage(insights, ElasticConstants.LINKEDIN_PAGE_INSIGHTS.getName(),
                pageIds, SocialChannel.LINKEDIN.getId());
        PageInsightV2EsData pageInsightDataFromEs = reportsEsService.getPageInsightDataFromEsChannelWise(request);
        if(SearchTemplate.PAGE_FOLLOWER_INSIGHTS.getName().equalsIgnoreCase(insights.getReportType())){
            request.setSearchTemplate(SearchTemplate.PAGE_FOLLOWER_TOTAL_INSIGHTS);
            pageInsightDataFromEs = reportsEsService.getTotalAudFromEsChannelWise(request, pageInsightDataFromEs);
        }
        pageInsightDataFromEs.setDateDiff(reportsEsService.getTimeDifference(request));

        return pageInsightDataFromEs;
    }
    private List<String> getPageIdsFromBids(List<Integer> businessIds) {
        return businessLinkedinPageRepo.findDistinctPageIdByBusinessIdInAndIsValid(businessIds);
    }

    @Override
    public  PageInsightV2EsData getLinkedInInsightsForMessageSent(InsightsRequest insights)  throws Exception {
        LOG.info("getPageInsightsESData Request to get insight for pages from business ids with enterprise id: {}", insights.getEnterpriseId());
        List<String> pageIds = getPageIdsFromBids(insights.getBusinessIds());

        if(CollectionUtils.isEmpty(pageIds)){
            return new PageInsightV2EsData();
        }

        long diff = insights.getEndDate().getTime() - insights.getStartDate().getTime();
        long noOfDaysBetween = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        reportDataConverter.setGroupByType(insights,noOfDaysBetween);

        List<String> sentFeedType = Arrays.asList(EngageV2FeedTypeEnum.COMMENT.name());
        List<String> receiveFeedType = Arrays.asList(EngageV2FeedTypeEnum.COMMENT.name());

        InsightsESRequest request = reportDataConverter.createESRequestForMessage(insights, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(),
                pageIds, SocialChannel.LINKEDIN.getId(), sentFeedType, null, receiveFeedType, null);

        request.setSearchTemplate(SearchTemplate.PPR_PAGE_POST_MESSAGE_METRIC);
        PageInsightV2EsData pageInsightDataFromEs = new PageInsightV2EsData();
        pageInsightDataFromEs = reportsEsService.getPageInsightDataFromEsChannelWise(request);
        pageInsightDataFromEs.setDateDiff(reportsEsService.getFeedTimeDifference(request));

        return pageInsightDataFromEs;

    }

    @Override
    public PageInsightV2EsData getLinkedInInsightsForPublishPost(InsightsRequest insights)  throws Exception {
        LOG.info("Request to get insight for pages from business ids with enterprise id: {}", insights.getEnterpriseId());
        String index = ElasticConstants.POST_INSIGHTS.getName();
        List<String> pageIds = getPageIdsByBids(insights.getBusinessIds());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(pageIds)){
            return new PageInsightV2EsData();
        }
        long diff = insights.getEndDate().getTime() - insights.getStartDate().getTime();
        long noOfDaysBetween = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        reportDataConverter.setGroupByType(insights,noOfDaysBetween);
        InsightsESRequest request = reportDataConverter.createESRequestForPage(insights,index,pageIds, SocialChannel.LINKEDIN.getId());
        request.setSearchTemplate(SearchTemplate.PPR_PAGE_POST_PUBLISHED_METRIC);
        PageInsightV2EsData pageInsightDataFromEs = reportsEsService.getPageInsightDataFromEsChannelWise(request);
        pageInsightDataFromEs.setDateDiff(reportsEsService.getTimeDifference(request));
        LOG.info("Fetched insight from es for pages");

        return pageInsightDataFromEs;

    }

    @Override
    public PostDataAndInsightResponse getLinkedInInsightsForPost(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        LOG.info("Request to get post insight with enterprise id: {}", insightsRequest.getEnterpriseId());
        PostDataAndInsightResponse response = new PostDataAndInsightResponse();
        InsightsPostRequest insightsPostRequest = new InsightsPostRequest(insightsRequest,startIndex,pageSize,
                sortParam,sortOrder,Integer.toString(SocialChannel.LINKEDIN.getId()), getPageIdsByBids(insightsRequest.getBusinessIds()));
        InsightsESRequest request = reportDataConverter.createESRequestForPost(insightsPostRequest, ElasticConstants.POST_INSIGHTS.getName());
        LOG.info("InsightsESRequest with page ids : {}",request.getDataModel());
        response = reportsEsService.getPostDataFromEs(request, excelDownload);
        return response;
    }

    private List<String> getPageIdsByBids(List<Integer> businessIds){
        return Lists.transform(businessLinkedinPageRepo.findDistinctPageIdByBusinessIdInAndIsValid(businessIds),
                Functions.toStringFunction());
    }

    @Override
    public void postLinkedInPageInsightToES(PageInsights pageInsights) {
        String index = InsightsConstants.LINKEDIN_PAGE_INSIGHTS;
        LOG.info("Started es upload for business id : {}", pageInsights.getBusinessId());
        List<ESPageRequest> esPageRequest = linkedInInsightConverter.createPageInsightsObject(pageInsights);
        linkedInESService.bulkPostPageInsights(esPageRequest,index);
        LOG.info("Executed es upload for business id :{}",pageInsights.getBusinessId());
    }

    @Override
    public void getPageInsightsFromLinkedin(SocialScanEventDTO socialScanEventDTO) {
        Date startDate;
        Integer limit = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getSocialPageInsightsLimit(socialScanEventDTO.getSourceName().toLowerCase());
        Integer backFillLimitForLinkedin = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty(LINKEDIN_BACKFILL_LIMIT);
        // taking end date as next day because linkedin's end date is exclusive while fetching insights
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DATE, 1);
        Date endDate = c.getTime();
        List<LinkedInPageInsight> linkedInPageInsights = linkedInPageInsightRepo.findByPageIdOrderByIdDesc(socialScanEventDTO.getExternalId());
        if(Objects.nonNull(socialScanEventDTO.getStartDate() ) && Objects.nonNull(socialScanEventDTO.getEndDate())){
            startDate = socialScanEventDTO.getStartDate();
            endDate = socialScanEventDTO.getEndDate();
        }else  if(org.apache.commons.collections4.CollectionUtils.isEmpty(linkedInPageInsights)) {
            startDate = DateUtils.addDays(endDate,-limit); // todo @ashu confirm start date in this case
        }else {
            LinkedInPageInsight linkedInPageInsight = linkedInPageInsights.get(0);
            if (isDifferenceMoreThan14Months(linkedInPageInsight.getLastSyncDate(), endDate)) {
                LOG.info("The difference is more than 14 months.");
                // Adjust the first date to ensure the difference is less than 14 months
                startDate = adjustFirstDate(endDate);
                LOG.info("Adjusted first date: " + startDate);
            } else {
                startDate = DateUtils.addDays(linkedInPageInsight.getLastSyncDate(),-backFillLimitForLinkedin);
            }
        }
        List<BusinessLinkedinPage> businessLinkedinPages = businessLinkedinPageRepo.findByBusinessId(socialScanEventDTO.getBusinessId());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(businessLinkedinPages) || startDate.after(endDate)){
            return;
        }
        Date finalEndDate = endDate;
        businessLinkedinPages.forEach(linkedinPage -> pushDataToKafkaForEsUpdate(linkedinPage, startDate, finalEndDate,backFillLimitForLinkedin));
    }

    public static Date adjustFirstDate(Date secondDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(secondDate);
        cal.add(Calendar.MONTH, -12);
        return cal.getTime();
    }

    // Function to check if the difference between two dates is more than 14 months
    public static boolean isDifferenceMoreThan14Months(Date firstDate, Date secondDate) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(firstDate);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(secondDate);

        int yearDiff = cal2.get(Calendar.YEAR) - cal1.get(Calendar.YEAR);
        int monthDiff = (yearDiff * 12) + cal2.get(Calendar.MONTH) - cal1.get(Calendar.MONTH);

        return monthDiff > 14;
    }


    private void pushDataToKafkaForEsUpdate(BusinessLinkedinPage linkedinPage, Date start, Date end,Integer backFillLimitForLinkedin) {
        try {
            if(!isEligible(linkedinPage,EnabledTasks.INSIGHTS) || "profile".equalsIgnoreCase(linkedinPage.getPageType())){
                LOG.info("Page insights are impacted for page with id {}",linkedinPage.getUrn());
                return;
            }
            LinkedInAPIResponse data = linkedinConnectService.getTimeBoundPageInsightResponse(linkedinPage.getAccessToken(), linkedinPage.getUrn(), start.getTime(), end.getTime(), LinkedInTimeGranularity.DAY);
            LinkedInAPIResponse followerTimeBased = linkedinService.getTimeBasedFollowerCountForOrganization(linkedinPage.getAccessToken(), linkedinPage.getUrn(), start.getTime(), end.getTime(),
                                                        LinkedInTimeGranularity.DAY,false);
            LinkedInAPIResponse followerCount = linkedinService.getFollowerCountForOrganization(linkedinPage.getAccessToken(), linkedinPage.getUrn());
            List<PageLevelMetaData> pageLevelMetaDataList = reportDataConverter.prepareDayWiseDataForLinkedin(data.getElements(), followerTimeBased.getElements(), end);

            if (CollectionUtils.isEmpty(pageLevelMetaDataList)) {
                LOG.info("Page insights not found for linked in page {}", linkedinPage.getProfileId());
                return;
            }

            DataModel videoViewsDataModel = DataModel.builder()
                    .pageIds("\""+linkedinPage.getProfileId()+"\"")
                    .build();

            InsightsESRequest videoViewsESRequest = new InsightsESRequest(videoViewsDataModel, SearchTemplate.POST_INSIGHT_PAGE_ID_VIDEO_VIEWS_SUM);
            videoViewsESRequest.setIndex(ElasticConstants.POST_INSIGHTS.getName());
            videoViewsESRequest.setRouting(linkedinPage.getEnterpriseId().toString());

            Integer currVideoViews = reportsEsService.getSumVideoViewsOnPageId(videoViewsESRequest);

            // fetching page insight (likes, comment, follower, post gain/lost) prev day data from ES
            SimpleDateFormat dateFormatter = new SimpleDateFormat(dateFormatterString);
            DataModel dataModel = DataModel.builder()
                    .pageIds(linkedinPage.getProfileId())
                    .startDate("\""+dateFormatter.format(start)+"\"")
                    .endDate("\""+dateFormatter.format(end)+"\"") //TODO: end date is +1 @Ashutosh
                    .sourceIds(Integer.toString(SocialChannel.LINKEDIN.getId()))
                    .build();
            InsightsESRequest insightsESRequest = new InsightsESRequest(dataModel, SearchTemplate.PAGE_INSIGHT_FOR_PREV_DATE);
            insightsESRequest.setIndex(ElasticConstants.LINKEDIN_PAGE_INSIGHTS.getName());
            insightsESRequest.setRouting(linkedinPage.getEnterpriseId().toString());
            List<PageInsightDataPoint> pageInsightDataPoints = reportsEsService.getPageInsightHitsDataFromEs(insightsESRequest);
            LOG.info("pageInsightDataPoints :{}",pageInsightDataPoints);
            LOG.info("pageLevelMetaDataList : {}",pageLevelMetaDataList);
            String ids = pageLevelMetaDataList.stream()
                    .map(pageLevelMetaData -> getIdFromPageLevel(pageLevelMetaData.getDate(), linkedinPage.getProfileId(),true))
                    .collect(Collectors.joining(", "));
            LOG.info("Page ids with timestamp :{}",ids);
            insightsESRequest.setSearchTemplate(SearchTemplate.PAGE_INSIGHT_BY_IDS);
            dataModel.setPageIds(ids);
            dataModel.setSize(backFillLimitForLinkedin);
            List<PageInsightDataPoint> pageInsightDataPointsByIds = reportsEsService.getPageInsightHitsDataFromEs(insightsESRequest);
            LOG.info(pageInsightDataPointsByIds.toString());
            // media count is number of posts
            calculatePageLevelDelta(pageLevelMetaDataList, pageInsightDataPoints, followerCount.getFirstDegreeSize(),
                    currVideoViews,pageInsightDataPointsByIds,linkedinPage.getProfileId());

//            Map<Date, Integer> postCountMap = reportsEsService.getPostCountForDates(insightsESRequest, dateFormatter.format(start), dateFormatter.format(new Date()));

//            PageInsights pageInsightsPostCountUpdate = new PageInsights(linkedinPage.getEnterpriseId(),linkedinPage.getProfileId(),
//                    linkedinPage.getBusinessId(), SocialChannel.LINKEDIN.getName(), reportsEsService.mergeAndGetUpdatePageInsightList(pageLevelMetaDataList, postCountMap));
            PageInsights pageInsights = new PageInsights(linkedinPage.getEnterpriseId(), linkedinPage.getProfileId(), linkedinPage.getBusinessId(), SocialChannel.LINKEDIN.getName(), pageLevelMetaDataList);
            kafkaProducerService.sendObjectV1(Constants.LINKEDIN_PAGE_INSIGHTS, pageInsights);
//            kafkaProducerService.sendObjectV1(Constants.PAGE_INSIGHTS_POSTCOUNT_UPDATE, pageInsightsPostCountUpdate);
            upsertPageInsights(pageLevelMetaDataList, linkedinPage.getProfileId(), linkedinPage.getBusinessId(),linkedinPage.getEnterpriseId(),end);
            // addInsightsToDB(pageInsights, end);
        } catch(Exception e) {
            LOG.error("Exception while fetching and storing page insights for profile id {}, business id {}", linkedinPage.getProfileId(), linkedinPage.getBusinessId(),e);
        }
    }

    public Map<Date, List<LinkedInPageInsight>> transformRecords(List<LinkedInPageInsight> existingRecords) {
        return existingRecords.stream()
                .map(record -> new AbstractMap.SimpleEntry<>(extractMaxDate(record), record))
                .filter(entry -> entry.getKey() != null)
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                ));

    }

    private Date extractMaxDate(LinkedInPageInsight record) {
        try {
            PageInsights pageInsights = JSONUtils.fromJSON(record.getData(), PageInsights.class);

            return pageInsights.getPageInsights().stream()
                    .map(PageLevelMetaData::getDate)
                    .max(Date::compareTo)
                    .orElse(null);
        } catch (Exception e) {
            LOG.info("Exception occurred while extracting linkedin max date for id :{} and exception:{}",record.getId(),e.getMessage());
            return null;
        }
    }

    public void upsertPageInsights(List<PageLevelMetaData> pageLevelMetaDataList, String pageId, Integer businessId, Long enterpriseId, Date end) {

        if (pageLevelMetaDataList == null || pageLevelMetaDataList.isEmpty()) {
            return;
        }
        Date minDate = pageLevelMetaDataList.stream()
                .map(PageLevelMetaData::getDate)
                .min(Date::compareTo)
                .orElseThrow(() -> new IllegalArgumentException("No valid dates found"));

        Date startOfMinDate = TimeZoneUtil.normalizeToStartOfDay(minDate);

        Date endOfMaxDate = TimeZoneUtil.normalizeToEndOfDay(new Date());

        List<LinkedInPageInsight> existingRecords = linkedInPageInsightRepo.findByPageIdAndDateBetween(pageId, startOfMinDate, endOfMaxDate);

        Map<Date, List<LinkedInPageInsight>> existingRecordMap = transformRecords(existingRecords);

        List<LinkedInPageInsight> toUpdate = new ArrayList<>();
        List<LinkedInPageInsight> toInsert = new ArrayList<>();

        Date now = new Date();
        Date nextSyncDate = DateUtils.addDays(now, 1);

        for (PageLevelMetaData metaData : pageLevelMetaDataList) {
            PageInsights pageInsights = new PageInsights(
                    enterpriseId,
                    pageId,
                    businessId,
                    SocialChannel.LINKEDIN.getName(),
                    Arrays.asList(metaData)
            );
            List<LinkedInPageInsight> existingPageInsight = existingRecordMap.get(metaData.getDate());

            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(existingPageInsight)) {
                List<LinkedInPageInsight> updatedInsights = existingPageInsight.stream()
                        .map(pageInsight -> {
                            pageInsight.setBusinessId(businessId);
                            pageInsight.setEnterpriseId(enterpriseId);
                            pageInsight.setLastSyncDate(now);
                            pageInsight.setNextSyncDate(nextSyncDate);
                            pageInsight.setData(JSONUtils.toJSON(pageInsights));
                            return pageInsight;
                        })
                        .collect(Collectors.toList());
                toUpdate.addAll(updatedInsights);
            } else {
                LinkedInPageInsight newPageInsight = dbDataConverter.convertPageInsightForLinkedin(pageInsights,end);
                toInsert.add(newPageInsight);
            }
        }
        if (!toUpdate.isEmpty()) {
            linkedInPageInsightRepo.save(toUpdate);
        }
        if (!toInsert.isEmpty()) {
            linkedInPageInsightRepo.save(toInsert);
        }
    }



    private String getIdFromPageLevel(Date date, String instagramAccountId,boolean isQuoteRequired) {
        SimpleDateFormat DATE_FORMATTER = new SimpleDateFormat("yyyy-MM-dd");
        DATE_FORMATTER.setTimeZone(TimeZone.getTimeZone("UTC"));
        String day = DATE_FORMATTER.format(date) + " 00:00:00";
        try {
            if(isQuoteRequired)
                return "\"" + DATE_FORMATTER.parse(day).getTime() +"_" +instagramAccountId + "\"";
            else
                return DATE_FORMATTER.parse(day).getTime() +"_" +instagramAccountId;
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }
    private void calculatePageLevelDelta(List<PageLevelMetaData> pageLevelMetaDataList, List<PageInsightDataPoint> pageInsightDataPoints,
                                         Integer currFollowerCount, Integer currVideoViews, List<PageInsightDataPoint> pageInsightDataPointsByIds,
                                         String profileId) {
        Integer prevFollowerCount = 0;
        Integer prevLikeCount = 0;
        Integer totalVideoView = 0;


        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(pageInsightDataPoints)) {
            PageInsightDataPoint pageInsightDataPoint = pageInsightDataPoints.get(0);
            prevFollowerCount = pageInsightDataPoint.getTotalFollowers() == null ? 0 : pageInsightDataPoint.getTotalFollowers();
            prevLikeCount = pageInsightDataPoint.getTotalLikes() == null ? 0 : pageInsightDataPoint.getTotalLikes();
            totalVideoView = pageInsightDataPoint.getTotalVideoViews() == null ? 0 : pageInsightDataPoint.getTotalVideoViews();
        }
        Map<String,PageInsightDataPoint> pageInsightMap = pageInsightDataPointsByIds.stream().collect(Collectors.toMap(
                obj ->  getIdFromPageLevelDay(obj.getDate(),profileId), pageInsight -> pageInsight));
        Integer netChange = currFollowerCount - prevFollowerCount;
        Integer videoViewsGain = totalVideoView == currVideoViews ? 0 : currVideoViews - totalVideoView;

        for(PageLevelMetaData pageLevelMetaData: pageLevelMetaDataList) {
            // fetch insights from ES for post level data to fetch likes and
//            pageLevelMetaData.setDate(new Date());
            String key = getIdFromPageLevel(pageLevelMetaData.getDate(), profileId,false);
            if(pageInsightMap.containsKey(key)){
                PageInsightDataPoint dataPoint = pageInsightMap.get(key);
                pageLevelMetaData.setLikesGainCount(dataPoint.getLikesGain()  == null ? 0 : dataPoint.getLikesGain());
                pageLevelMetaData.setProfileVideoViews(dataPoint.getVideoViews() == null ? 0 :dataPoint.getVideoViews());
                pageLevelMetaData.setTotalProfileVideoViews(dataPoint.getTotalVideoViews() == null ? 0 :dataPoint.getTotalVideoViews());
                pageLevelMetaData.setTotalFollower(dataPoint.getTotalFollowers() == null ? 0 :dataPoint.getTotalFollowers());
                pageLevelMetaData.setFollowerLostCount(dataPoint.getFollowersLost() == null ? 0 :dataPoint.getFollowersLost());
            }else {
                int followerGain = pageLevelMetaData.getFollowerGainCount() == null ? 0 : pageLevelMetaData.getFollowerGainCount();
                int followerLost = followerGain - netChange;
                pageLevelMetaData.setFollowerLostCount(followerLost > 0 ? followerLost : 0);
                int totalLikes = pageLevelMetaData.getTotalLikes() == null ? 0 : pageLevelMetaData.getTotalLikes();
                pageLevelMetaData.setLikesGainCount(totalLikes - prevLikeCount);
                pageLevelMetaData.setProfileVideoViews(videoViewsGain);
                pageLevelMetaData.setTotalProfileVideoViews(currVideoViews);
                pageLevelMetaData.setTotalFollower(currFollowerCount);
            }
        }
    }
    private String getIdFromPageLevelDay(String day, String instagramAccountId) {
        SimpleDateFormat DATE_FORMATTER = new SimpleDateFormat("yyyy-MM-dd");
        DATE_FORMATTER.setTimeZone(TimeZone.getTimeZone("UTC"));
        try {
            return DATE_FORMATTER.parse(day).getTime() +"_" +instagramAccountId;
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void getPostInsights(BusinessPosts businessPosts, Boolean isFreshRequest) {
        BusinessLinkedinPage linkedinPage = businessLinkedinPageRepo.findFirstByProfileIdAndIsValid(businessPosts.getExternalPageId(),1);
        if(Objects.isNull(linkedinPage) || businessPosts.getIsDeleted() == 1) {
            LOG.info("No data found for page id: {} or post is deleted : {}",businessPosts.getExternalPageId(),businessPosts.getId());
            return;
        }
        if(StringUtils.isNotEmpty(linkedinPage.getModuleImpacted()) && !isEligible(linkedinPage,EnabledTasks.INSIGHTS)){
            LOG.info("Page {} is restricted to get posts",linkedinPage.getUrn());
            return;
        }
        if(Objects.nonNull(linkedinPage.getEnterpriseId()) && !socialBusinessPropertyService
                .isSocialPostingAndReportingEnabled(linkedinPage.getEnterpriseId())){
            LOG.info("Social reporting is not enabled for enterprise id : {}", linkedinPage.getEnterpriseId());
            return;
        }
        try {
            // fetching insights for that post
            LinkedInAPIResponse postInsightResponse = linkedinService.getPostInsightResponse(linkedinPage.getAccessToken(), businessPosts.getPostId(), linkedinPage.getUrn());
            LOG.info("postInsightResponse: {}", postInsightResponse);

            Integer videoViewsCount = 0;

            if(StringUtils.isNotEmpty(businessPosts.getVideoUrls())) {
                LOG.info("Video found for post id: {}", businessPosts.getPostId());
                LinkedInAPIResponse videoViews = linkedinService.getPostVideoViewResponse(linkedinPage.getAccessToken(), businessPosts.getPostId());

                if(videoViews != null && videoViews.getElements() != null && videoViews.getElements().size() > 0) {
                    String videoStat = videoViews.getElements().get(0).getStatisticsType();
                    if(VIDEO_METRICS.equalsIgnoreCase(videoStat)) {
                        videoViewsCount = videoViews.getElements().get(0).getValue();
                    }
                }
            }


            PostData postData;
            PostInsightDTO postInsightDTO;
            if (!ObjectUtils.isEmpty(postInsightResponse) && !ObjectUtils.isEmpty(postInsightResponse.getElements())) {
                // storing insight data for that post in DB
                postInsightDTO = getPostInsightInfo(postInsightResponse.getElements().get(0).getTotalShareStatistics(), videoViewsCount);
                postData = createPostData(businessPosts, postInsightResponse.getElements().get(0), postInsightDTO, videoViewsCount);
            } else {
                postInsightDTO = getPostInsightInfoV1();
                postData = createPostData(businessPosts, null, postInsightDTO, videoViewsCount);
                LOG.info("No response received from linkedin for business post {}", businessPosts.getPostId());
            }

            LOG.info("Post data: {}", postData);
            ObjectMapper objectMapper = new ObjectMapper();
            String postInsightJson = objectMapper.writeValueAsString(postInsightDTO);
            businessPosts.setResponse(postInsightJson);
            businessPostsRepo.saveAndFlush(businessPosts);
            auditPostInsight(businessPosts, false);
            kafkaProducerService.sendObjectV1(KafkaTopicEnum.LINKEDIN_POST_SYNC_ENGAGE.getName(), postData);
            if(!isFreshRequest || (CollectionUtils.isEmpty(postData.getImages()) && CollectionUtils.isEmpty(postData.getVideos()))) {
                sendPostDataToKafka(postData);
            } else {
                commonService.uploadMediaToPicturesque(postData, businessPosts);
                saveCDNPostToES(businessPosts);
            }
        } catch (BirdeyeSocialException e){
            LOG.info("BirdeyeSocialException occurred while getting data for postId: {} of LinkedIn page: {}", businessPosts.getPostId(), linkedinPage.getBusinessGetPageId());
            if(e.getErrorCode() == 400 || e.getErrorCode() == 404){
                businessPosts.setIsDeleted(1);
                BusinessPostMetadata businessPostMetadata = new BusinessPostMetadata();
                if(StringUtils.isNotEmpty(businessPosts.getMetadata())){
                    businessPostMetadata = JSONUtils.fromJSON(businessPosts.getMetadata(),BusinessPostMetadata.class);
                }
                if (Objects.isNull(businessPostMetadata)){
                    businessPostMetadata = new BusinessPostMetadata();
                }
                businessPostMetadata.setReason(DisabledReason.IS_DELETED.name());
                businessPosts.setMetadata(JSONUtils.toJSON(businessPostMetadata));
            }
            updateBusinessPost(businessPosts,e);
        } catch (Exception e) {
            updateBusinessPost(businessPosts,e);
            LOG.info("Exception occurred while getting data for postId: {} of LinkedIn page: {}", businessPosts.getPostId(), linkedinPage.getBusinessGetPageId());
        }
    }

    private void updateBusinessPost(BusinessPosts businessPosts,Exception e) {
        businessPosts.setError(e.getMessage());
        businessPostsRepo.saveAndFlush(businessPosts);
        auditPostInsight(businessPosts, true);
    }

    private void sendPostDataToKafka(PostData postData) {
        String key = postData.getPostId().concat("_").concat(postData.getPostingDate().toString());
        kafkaProducerService.sendWithKey(InsightsConstants.LINKEDIN_POST_INSIGHT_ES, key, postData);
    }

    private void auditPostInsight(BusinessPosts businessPosts, boolean isFailed) {
        PostInsightAudit postInsightAudit = new PostInsightAudit();
        postInsightAudit.setPostId(businessPosts.getPostId());
        postInsightAudit.setSourceId(businessPosts.getSourceId());
        postInsightAudit.setPageId(businessPosts.getExternalPageId());
        if(isFailed) {
            postInsightAudit.setResponse(businessPosts.getError());
        } else {
            postInsightAudit.setResponse(businessPosts.getResponse());
        }
        postInsightAuditRepo.saveAndFlush(postInsightAudit);
    }

    @Override
    public PostData createPostData(BusinessPosts businessPosts, LinkedInElement insight, PostInsightDTO postInsightDTO, Integer videoViewsCount) {
        List<String> media = new ArrayList<>();
        PostData postData = new PostData();
        postData.setId(businessPosts.getId());
        postData.setBePostId(businessPosts.getBePostId()!=null?businessPosts.getBePostId().toString()
                :null);
        postData.setPostId(businessPosts.getPostId());
        postData.setSourceId(businessPosts.getSourceId());
        postData.setPageId(businessPosts.getExternalPageId());
        postData.setBusinessId(businessPosts.getBusinessId());
        postData.setEnterpriseId(businessPosts.getEnterpriseId());
        postData.setPostUrl(businessPosts.getPostUrl());
        postData.setPostText(businessPosts.getPostText());
        postData.setPostingDate(businessPosts.getPublishDate());
        postData.setPageName(businessPosts.getPageName());
        postData.setPostType(businessPosts.getPostType());
        if(StringUtils.isNotEmpty(businessPosts.getImageUrls())) {
            media = Arrays.asList(businessPosts.getImageUrls().split(","));
            postData.setImages(media);
        }
        if( StringUtils.isNotEmpty(businessPosts.getVideoUrls())) {
            media = Arrays.asList(businessPosts.getVideoUrls().split(","));
            postData.setVideos(media);
        }
        postData.setEngagementRate(Objects.isNull(insight) ? null : insight.getTotalShareStatistics().getEngagement() * 100);
        postData.setImpression(postInsightDTO.getImpression());
        postData.setEngagement(postInsightDTO.getEngagement());

        commonService.prepareDeltaInsightsForPost(postData, businessPosts);
        postData.setLikeCount(Objects.isNull(insight) ? null : insight.getTotalShareStatistics().getLikeCount());
        postData.setClickCount(Objects.isNull(insight) ? null : insight.getTotalShareStatistics().getClickCount());
        postData.setShareCount(Objects.isNull(insight) ? null : insight.getTotalShareStatistics().getShareCount());
        postData.setCommentCount(Objects.isNull(insight) ? null : insight.getTotalShareStatistics().getCommentCount());
        postData.setCommentMentionsCount(Objects.isNull(insight) ? null : insight.getTotalShareStatistics().getCommentMentionsCount());
        if (Objects.nonNull(businessPosts.getBePostId())) {
            postData.setTagIds(socialTagService.getTagIdsFromEntityId(Long.valueOf(businessPosts.getBePostId()), SocialTagEntityType.POST));
        }
        postData.setVideoViews(videoViewsCount);
        // add post author details to be shown on analyze tab
        addPostAuthorDetails(businessPosts, postData);
        return postData;
    }

    @Override
    public void backfillEngagementBreakdown(BackfillInsightReq request) {
        List<BusinessLinkedinPage> businessLinkedinPages = new ArrayList<>();
        if(request.isLimit()) {
            businessLinkedinPages = businessLinkedinPageRepo.findByProfileIdIn(Collections.singletonList(request.getPageId()));
            BackfillRequest backfillRequest = new BackfillRequest();
            backfillRequest.setBackfillInsightReq(request);
            backfillRequest.setBusinessLinkedinPage(businessLinkedinPages.get(0));
            kafkaProducerService.sendObjectV1(KafkaTopicEnum.BACKFILL_ENGAGEMENT_BREAKDOWN.getName(),backfillRequest);
        } else {
            int page = request.getPage();
            int size = request.getSize();
            while (true){
                businessLinkedinPages = businessLinkedinPageRepo.findAllPagesByIsValidAndModuleImpactedNotIn(new PageRequest(page,size));
                if(CollectionUtils.isEmpty(businessLinkedinPages)){
                    break;
                }
                page += 10;
                BackfillRequest backfillRequest = new BackfillRequest();
                backfillRequest.setBackfillInsightReq(request);
                for(BusinessLinkedinPage linkedinPage :  businessLinkedinPages){
                    backfillRequest.setBusinessLinkedinPage(linkedinPage);
                    kafkaProducerService.sendObjectV1(KafkaTopicEnum.BACKFILL_ENGAGEMENT_BREAKDOWN.getName(),backfillRequest);
                }
            }
        }
    }

    @Override
    public void backfillEngagementBreakdownPageWise(BackfillRequest backfillRequest) {
        BoolQueryBuilder boolQueryBuilder = reportsEsService.prepareQueryToGetData(backfillRequest,
                backfillRequest.getBusinessLinkedinPage().getProfileId());
        String index = ElasticConstants.LINKEDIN_PAGE_INSIGHTS.getName();
        int size = 30;
        List<ESPageRequest> esPageDataPoints = reportsEsService.getDataFromEsIndex(boolQueryBuilder,size,index);
        if(CollectionUtils.isEmpty(esPageDataPoints)){
            LOG.info("No data found in ES,{}",backfillRequest);
            return;
        }
        esPageDataPoints.forEach(point -> {
            point.setPost_comment_count(point.getComment_count());
            point.setPost_like_count(point.getLike_gain());
            point.setPost_share_count(point.getShare_count());
        });
        reportsEsService.bulkPostPageInsights(esPageDataPoints,index);
    }


    @Override
    public LeadershipByPostsDataPoints getPostLeadershipReport(LocationReportRequest insightsRequest,
                                                               ReportSortingCriteria postSortingCriteria, SortOrder order,
                                                               Integer startIndex, Integer pageSize) {
        List<BusinessLinkedinPageRepository.BLP> linkedinPages =
                businessLinkedinPageRepo.findByPageBusinessIds(insightsRequest.getBusinessIds());
        if(CollectionUtils.isEmpty(linkedinPages)) return new LeadershipByPostsDataPoints();
        Map<String, BusinessLinkedinPageRepository.BLP> pageMap = getPageIdBLPMap(linkedinPages);
        SearchResponse searchResponseForPage =
                reportsEsService.getDataFromESPageIndex(insightsRequest,postSortingCriteria,order,
                        startIndex,pageSize,new ArrayList<>(pageMap.keySet()),ElasticConstants.LINKEDIN_PAGE_INSIGHTS.getName());
        LinkedHashMap<String,LeadershipByPostsResponse> leadershipByPostsResponseMap =
                reportDataConverter.convertSearchResponseToLeadership(searchResponseForPage);
        SearchResponse searchResponseForPost =
                reportsEsService.getDataFromESPostIndex(insightsRequest,postSortingCriteria,order,startIndex,pageSize,
                        new ArrayList<>(leadershipByPostsResponseMap.keySet()),SocialChannel.LINKEDIN.getId());
        SearchResponse searchResponseForFollowers = reportsEsService.getDataFromESFollowers(insightsRequest,
                postSortingCriteria,order,startIndex,pageSize, new ArrayList<>(leadershipByPostsResponseMap.keySet()),
                ElasticConstants.LINKEDIN_PAGE_INSIGHTS.getName());
        reportDataConverter.updateResponseWithPostCount(searchResponseForPost,searchResponseForFollowers,leadershipByPostsResponseMap);
        LeadershipByPostsDataPoints postsDataPoints = new LeadershipByPostsDataPoints();
        setLocationData(leadershipByPostsResponseMap, linkedinPages);
        postsDataPoints.setDataPoints(new ArrayList<>(leadershipByPostsResponseMap.values()));
        postsDataPoints.setTotalRecords(pageMap.size());
        return postsDataPoints;
    }


    private Map<Integer, String> getBusinessIdAndPageIdMap(List<String> pageIds,
                                                           List<BusinessLinkedinPageRepository.BLP> pages) {
        return pages.stream().filter(p -> pageIds.contains(p.getProfileId())).
                collect(Collectors.toMap(BusinessLinkedinPageRepository.BLP::getBusinessId,
                        BusinessLinkedinPageRepository.BLP::getProfileId));
    }

    @NotNull
    private Map<String, BusinessLinkedinPageRepository.BLP> getPageIdBLPMap(List<BusinessLinkedinPageRepository.BLP> pages) {
        return pages.stream().collect(Collectors.toMap(BusinessLinkedinPageRepository.BLP::getProfileId, page -> page));
    }

    private void setLocationData(Map<String, LeadershipByPostsResponse> leadershipByPostsResponseMap,
                                 List<BusinessLinkedinPageRepository.BLP> pages) {
        Map<Integer,String> mapOfBusinessIdAndPageId =
                getBusinessIdAndPageIdMap(new ArrayList<>(leadershipByPostsResponseMap.keySet()), pages);
        List<BusinessBizLiteDto> listOfBusinessLite =
                coreService.getBusinessLiteDtoByBusinessIds(new ArrayList<>(mapOfBusinessIdAndPageId.keySet()));
        listOfBusinessLite.forEach(lite -> {
            String pageId = mapOfBusinessIdAndPageId.get(lite.getId());
            LeadershipByPostsResponse response = leadershipByPostsResponseMap.get(pageId);
            response.setLabel(StringUtils.isEmpty(lite.getAlias1()) ? lite.getName() : lite.getAlias1());
            response.setLocation(lite.getBusinessNumber());
        });
    }

    private void addPostAuthorDetails(BusinessPosts businessPosts, PostData postData) {
        boolean isBePost = Objects.equals(postData.getSourceId(), SocialChannel.APPLE_CONNECT.getId()) ||
                Objects.nonNull(postData.getBePostId()) && !Objects.equals(postData.getPostId(), postData.getBePostId());
        // Check if the author details are missing in BusinessPosts
        boolean isAuthorDetailsMissing = Objects.isNull(businessPosts.getCreatedByBeUserId())
                || Objects.isNull(businessPosts.getCreatedByBeUserName())
                || Objects.isNull(businessPosts.getCreatedByBeUserEmail());

        BusinessCoreUser businessCoreUser = null;

        if (isBePost && isAuthorDetailsMissing) {
            businessCoreUser = getBusinessCoreUser(businessPosts);
        }

        // Update BusinessPosts with the author details
        if (isAuthorDetailsMissing && Objects.nonNull(businessCoreUser)) {
            businessPosts.setCreatedByBeUserId(businessCoreUser.getId());
            businessPosts.setCreatedByBeUserName(businessCoreUser.getName());
            businessPosts.setCreatedByBeUserEmail(businessCoreUser.getEmailId());
        }

        // Set author details in postData which is later updated in ES
        postData.setPublisherId(Objects.nonNull(businessCoreUser) ? businessCoreUser.getId() : businessPosts.getCreatedByBeUserId());
        postData.setPublisherName(Objects.nonNull(businessCoreUser) ? businessCoreUser.getName() : businessPosts.getCreatedByBeUserName());
        postData.setPublisherEmail(Objects.nonNull(businessCoreUser) ? businessCoreUser.getEmailId() : businessPosts.getCreatedByBeUserEmail());
    }

    private BusinessCoreUser getBusinessCoreUser(BusinessPosts businessPosts) {
        Integer createdById = getCreatedById(businessPosts);
        // createdById is 0 for PUBLIC-POSTING
        if (Objects.nonNull(createdById) && createdById != 0) {
            return coreService.getUserInfo(createdById);
        }
        return null;
    }

    private Integer getCreatedById(BusinessPosts businessPosts) {
        if (Objects.isNull(businessPosts.getCreatedByBeUserId())) {
            SocialPost socialPost = socialPostRepository.findById(businessPosts.getBePostId());
            if (Objects.nonNull(socialPost) && Objects.nonNull(socialPost.getCreatedBy())) {
                return socialPost.getCreatedBy();
            }
        }
        return businessPosts.getCreatedByBeUserId();
    }

    private PostData createPostDataForCDN(BusinessPosts businessPosts) {
        PostData postData = new PostData();
        List<String> media;
        postData.setId(businessPosts.getId());
        postData.setBePostId(businessPosts.getBePostId()!=null?businessPosts.getBePostId().toString()
                :null);
        postData.setPostId(businessPosts.getPostId());
        postData.setSourceId(businessPosts.getSourceId());
        postData.setPageId(businessPosts.getExternalPageId());
        postData.setBusinessId(businessPosts.getBusinessId());
        postData.setEnterpriseId(businessPosts.getEnterpriseId());
        postData.setPostUrl(businessPosts.getPostUrl());
        postData.setPostText(businessPosts.getPostText());
        postData.setPostingDate(businessPosts.getPublishDate());
        postData.setPageName(businessPosts.getPageName());
        postData.setPublisherId(businessPosts.getCreatedByBeUserId());
        postData.setPublisherEmail(businessPosts.getCreatedByBeUserEmail());
        postData.setPublisherName(businessPosts.getCreatedByBeUserName());
        postData.setPostType(businessPosts.getPostType());
        if(StringUtils.isNotEmpty(businessPosts.getImageUrls())) {
            media = Arrays.asList(businessPosts.getImageUrls().split(","));
            postData.setImages(media);
        }
        if( StringUtils.isNotEmpty(businessPosts.getVideoUrls())) {
            media = Arrays.asList(businessPosts.getVideoUrls().split(","));
            postData.setVideos(media);
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String insightData = businessPosts.getResponse();
            PostInsightDTO insightDTO = objectMapper.readValue(insightData, PostInsightDTO.class);
            postData.setEngagement(insightDTO.getEngagement());
            postData.setImpression(insightDTO.getImpression());
            postData.setEngagementRate(insightDTO.getEngagementRate());
            commonService.prepareDeltaInsightsForPost(postData,businessPosts);
            postData.setLikeCount(insightDTO.getLikeCount());
            postData.setClickCount(insightDTO.getClickCount());
            postData.setShareCount(insightDTO.getShareCount());
            postData.setCommentCount(insightDTO.getCommentCount());
            postData.setCommentMentionsCount(insightDTO.getCommentMentionsCount());
        } catch (Exception e) {
            LOG.info("Exception while converting response to PostInsightDTO: ",e);
        }

        return postData;
    }

    private Integer calculateEngagement(LinkedInStat stats) {
        return stats.getClickCount() + stats.getCommentCount() + stats.getLikeCount() + stats.getShareCount();
    }

    private PostInsightDTO getPostInsightInfo(LinkedInStat insightData, Integer videoViews) {
        PostInsightDTO postInsightDTO = new PostInsightDTO();
        postInsightDTO.setEngagement(calculateEngagement(insightData));
        postInsightDTO.setImpression(insightData.getImpressionCount());
        postInsightDTO.setEngagementRate(insightData.getEngagement()*100);
        postInsightDTO.setLikeCount(insightData.getLikeCount());
        postInsightDTO.setClickCount(insightData.getClickCount());
        postInsightDTO.setShareCount(insightData.getShareCount());
        postInsightDTO.setCommentCount(insightData.getCommentCount());
        postInsightDTO.setCommentMentionsCount(insightData.getCommentMentionsCount());
        postInsightDTO.setVideoViews(videoViews);
        return postInsightDTO;
    }
    private PostInsightDTO getPostInsightInfoV1() {
        PostInsightDTO postInsightDTO = new PostInsightDTO();
        postInsightDTO.setEngagement(0);
        postInsightDTO.setImpression(0);
        postInsightDTO.setEngagementRate(0.0);
        postInsightDTO.setLikeCount(0);
        postInsightDTO.setClickCount(0);
        postInsightDTO.setShareCount(0);
        postInsightDTO.setCommentCount(0);
        postInsightDTO.setCommentMentionsCount(0);
        return postInsightDTO;
    }

    /* public void getInsightsForOrganisation(Integer businessId) {
        List<BusinessLinkedinPage> linkedinPages = businessLinkedinPageRepo.findByBusinessId(businessId);
        if(CollectionUtils.isEmpty(linkedinPages)) {
            LOG.info("No data found for business id: {}", businessId);
            return;
        }

        int followerCount = linkedinPages.stream().map(linkedinPage ->
                linkedinService.getFollowerCountForOrganization(linkedinPage.getAccessToken(), linkedinPage.getUrn()))
                .filter(response -> !ObjectUtils.isEmpty(response))
                .mapToInt(LinkedInAPIResponse::getFirstDegreeSize).sum();
        LOG.info("Follower count for business id: {} is {}", businessId, followerCount);
    }*/

    @Override
    public void postLinkedinPostInsightsToEs(PostData postData) {
        String index = ElasticConstants.POST_INSIGHTS.getName();
        List<EsPostDataPoint> esPostDataPoints = reportDataConverter.preparePostEsDTO(postData);
        LOG.info("Pushing post data to ES for post id {} and source id {}", postData.getPostId(), postData.getSourceId());
        reportsEsService.bulkPostPagePostDataToES(esPostDataPoints,index);
    }

    @Override
    public void startScanForPosts(String pageId) {
        BusinessLinkedinPage linkedinPage = businessLinkedinPageRepo.findByProfileId(pageId);
        if(Objects.isNull(linkedinPage)) {
            return;
        }

        SocialScanEventDTO scanEventDTO = new SocialScanEventDTO();
        scanEventDTO.setSourceId(SocialChannel.LINKEDIN.getId());
        scanEventDTO.setSourceName(SocialChannel.LINKEDIN.getName());
        scanEventDTO.setChannelPrimaryId(linkedinPage.getId());
        scanEventDTO.setBusinessId(linkedinPage.getBusinessId());
        scanEventDTO.setEnterpriseId(linkedinPage.getEnterpriseId());
        scanEventDTO.setExternalId(linkedinPage.getProfileId());
        scanEventDTO.setPageName(linkedinPage.getCompanyName());

        kafkaProducerService.sendObjectV1(scanEventDTO.getSourceName().concat("_").concat(SOCIAL_PAGE_SCAN_EVENT), scanEventDTO);
    }

    public void addInsightsToDB(PageInsights pageInsights, Date end) {
        Calendar c = Calendar.getInstance();
        c.setTime(end);
        c.add(Calendar.DATE, -1);
        LinkedInPageInsight linkedInPageInsight = LinkedInPageInsight.builder()
                .date(new Date())
                .pageId(pageInsights.getPageId())
                .data(JSONUtils.toJSON(pageInsights))
                .enterpriseId(pageInsights.getEnterpriseId())
                .businessId(pageInsights.getBusinessId())
                .lastSyncDate(c.getTime())
                .nextSyncDate(end)
                .build();
        linkedInPageInsightRepo.save(linkedInPageInsight);
    }

    @Override
    public void saveCDNPostToES(BusinessPosts businessPosts) {
        PostData postData = createPostDataForCDN(businessPosts);
        sendPostDataToKafka(postData);
    }

    @Override
    public Boolean isEligible(BusinessLinkedinPage linkedinPage,EnabledTasks enabledTasks){
        if(StringUtils.isNotEmpty(linkedinPage.getModuleImpacted())){
            List<String> moduleImpacted = Arrays.asList(linkedinPage.getModuleImpacted().split(","));
            if(moduleImpacted.contains(enabledTasks.name())){
                LOG.info("Page {} is restricted to get posts",linkedinPage.getUrn());
                return false;
            }
        }
        return true;
    }

    @Override
    public void updateEngagement(EngagementUpdateRequest request) {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DATE, 1);
        Date endDate = c.getTime();
        for (Long businessNumber : request.getBusinessNumbers()) {
            List<BusinessLinkedinPage> businessLinkedinPages = businessLinkedinPageRepo.findValidPages(businessNumber);
            for (BusinessLinkedinPage linkedinPage : businessLinkedinPages) {

                if (!isEligible(linkedinPage, EnabledTasks.INSIGHTS) || "profile".equalsIgnoreCase(linkedinPage.getPageType())) {
                    LOG.info("Page insights are impacted for page with id {}", linkedinPage.getUrn());
                    return;
                }
                Date startDate = getEngagementStartDate(linkedinPage.getProfileId(),ElasticConstants.LINKEDIN_PAGE_INSIGHTS.getName());
                if(Objects.isNull(startDate) ){
                    LOG.info("startDate is null for profile Id {}", linkedinPage.getProfileId());
                    return;
                }
                Calendar startCalendar = Calendar.getInstance();
                startCalendar.setTime(startDate);
                Calendar endCalendar = Calendar.getInstance();
                endCalendar.setTime(endDate);

                int monthDiff = 0;
                while (startCalendar.before(endCalendar)) {
                    startCalendar.add(Calendar.MONTH, 1);
                    if (startCalendar.before(endCalendar) || startCalendar.equals(endCalendar)) {
                        monthDiff++;
                    }
                }
                LOG.info("Month difference: {}",monthDiff);
                if(monthDiff>14){
                    startCalendar.setTime(endDate);
                    startCalendar.add(Calendar.MONTH, -12);
                    startDate = startCalendar.getTime();
                    LOG.info("updated start date {}", startDate);
                }
                //  LinkedInPageInsight linkedInPageInsight = linkedInPageInsightRepo.findFirstByPageIdOrderByIdAsc(linkedinPage.getProfileId());

                    LinkedInAPIResponse data = linkedinConnectService.getTimeBoundPageInsightResponse(linkedinPage.getAccessToken(),
                            linkedinPage.getUrn(), startDate.getTime(), endDate.getTime(), LinkedInTimeGranularity.DAY);
                    List<PageLevelMetaData> pageLevelMetaDataList = reportDataConverter.prepareDayWiseEngDataForLinkedin(data.getElements());

                    if (CollectionUtils.isEmpty(pageLevelMetaDataList)) {
                        LOG.info("Page insights not found for linked in page {}", linkedinPage.getProfileId());
                        return;
                    }
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

                    pageLevelMetaDataList.forEach(page -> {
                        Map<String, Object> params = new HashMap<>();
                        params.put("post_engagement", page.getPostEngagements().toString());
                        String day = new SimpleDateFormat(dateFormatterString).format(page.getDate());

                        try {
                            esService.updateEngagementData(page.getPostEngagements(), "linkedin_page_insight", (dateFormat.parse(day).getTime() + "_" + linkedinPage.getProfileId()));
                        } catch (ParseException e) {
                            LOG.info("Exception in parsing data :{}", e.getMessage());
                        }
                    });
                }

            }
        }

    @Override
    public PerformanceSummaryResponse getLinkedInPerformanceData(InsightsRequest insights)  {
        try {
            LOG.info("Request to get insight for pages from business ids with enterprise id: {}", insights.getEnterpriseId());
            String index = ElasticConstants.LINKEDIN_PAGE_INSIGHTS.getName();
            List<String> pageIds = getPageIdsFromBids(insights.getBusinessIds());
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(pageIds)) {
                LOG.info("No valid pageId found for businessIds {}", insights.getBusinessIds());
                return null;
            }
            InsightsESRequest request = reportDataConverter.createESRequestForPerformanceData(insights,index,pageIds, SocialChannel.LINKEDIN.getId());
            return reportsEsService.getPagePerformanceDataFromEsChannelWise(request);

        } catch (Exception ex) {
            LOG.error("Something went wrong while parsing data for performance summary for request {}", insights, ex);
            return null;
        }
    }

    @Override
    public boolean backfillProfilePagesToEs(PageInsightsRequest pageInsights) {
        List<BusinessLinkedinPage> pages =  businessLinkedinPageRepo.findByProfileIdInAndBusinessIdIsNotNullAndEligible(pageInsights.getPageIds());
        if(!CollectionUtils.isEmpty(pages)){
            List<BackfillInsightReq> eligibleEvents = reportDataConverter.conversionToLNScanEventDTO(pages,pageInsights);
            eligibleEvents.parallelStream().forEach(this::sendSocialRecordScannedEvent);
            return true;
        }
        return false;
    }

    private void sendSocialRecordScannedEvent(BackfillInsightReq payload) {
        kafkaProducerService.sendObjectV1(SOCIAL_BACKFILL_PAGE_SCAN_EVENT, payload);
    }


    private Date getEngagementStartDate(String profileId, String name) {
        Date startDate=null;
        try {

            String firstDate  = reportsEsService.getStartDate(Collections.singletonList(profileId),name);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            startDate = dateFormat.parse(firstDate);
        } catch (ParseException|IOException e) {
            LOG.info("Exception in parsing date :{}", e.getMessage());
            return null;
        }
        LOG.info("startDate :{} calculated for pageId: {}",startDate,profileId);
        return startDate;
    }

    @Override
    public PageReportEsData getLinkedInInsightsReportData(InsightsRequest insights) throws Exception {
        LOG.info("Request to get insight for pages from business ids with enterprise id: {}", insights.getEnterpriseId());
        String index = ElasticConstants.LINKEDIN_PAGE_INSIGHTS.getName();
        List<String> pageIds = getPageIdsByBids(insights.getBusinessIds());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(pageIds)){
            return new PageReportEsData();
        }

        long diff = insights.getEndDate().getTime() - insights.getStartDate().getTime();
        long noOfDaysBetween = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        reportDataConverter.setGroupByType(insights,noOfDaysBetween);
        InsightsESRequest request = reportDataConverter.createESRequestForPage(insights,index,pageIds, SocialChannel.LINKEDIN.getId());
        PageReportEsData pageInsightDataFromEs = reportsEsService.getPageReportDataFromEsChannelWise(request);
        LOG.info("Fetched insight from es for pages");

        return pageInsightDataFromEs;
    }

    @Override
    public PageReportEsData getMessageVolumeInsightsReportData(InsightsRequest insights) throws Exception {
        LOG.info("Request to get insight for pages from business ids with enterprise id: {}", insights.getEnterpriseId());
        PageReportEsData reportEsData = new PageReportEsData();
        List<String> pageIds = getPageIdsByBids(insights.getBusinessIds());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(pageIds)){
            return reportEsData;
        }

        long diff = insights.getEndDate().getTime() - insights.getStartDate().getTime();
        long noOfDaysBetween = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        reportDataConverter.setGroupByType(insights,noOfDaysBetween);

        List<String> sentFeedType = Arrays.asList(EngageV2FeedTypeEnum.POST.name(),EngageV2FeedTypeEnum.AD_POST.name(),
                EngageV2FeedTypeEnum.COMMENT.name(), EngageV2FeedTypeEnum.AD_COMMENT.name());
        List<String> receiveFeedType = Arrays.asList(EngageV2FeedTypeEnum.POST.name(), EngageV2FeedTypeEnum.COMMENT.name());

        InsightsESRequest sentMessageRequest = reportDataConverter.createESRequestForMessage(insights, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(),
                pageIds, SocialChannel.LINKEDIN.getId(), sentFeedType, null, receiveFeedType, null);

        sentMessageRequest.setSearchTemplate(SearchTemplate.REPORT_SENT_MESSAGE_VOLUME);
        PageReportEsData sentMessageEsData = reportsEsService.getPageReportDataFromEsChannelWise(sentMessageRequest);
        reportEsData.setSentMessage(sentMessageEsData.getSentMessage());

        sentMessageRequest.setSearchTemplate(SearchTemplate.REPORT_RECEIVED_MESSAGE_VOLUME);
        PageReportEsData receivedMessageEsData = reportsEsService.getPageReportDataFromEsChannelWise(sentMessageRequest);
        reportEsData.setReceivedMessage(receivedMessageEsData.getReceivedMessage());
        LOG.info("Fetched insight from es for pages");
        return reportEsData;
    }

    @Override
    public void backfillLnInsight(BackfillInsightReq socialScanEventDTO) {
        Integer minBackfillDays= reportPropertyRepository.findMinDaysForSourceIdAndReportType(
                socialScanEventDTO.getSourceId(),"profile_matric");
        if(InsightsReportUtil.validateStartAndEndDate(socialScanEventDTO.getStartDate(),socialScanEventDTO.getEndDate(),minBackfillDays)) {
            BusinessLinkedinPage businessLinkedinPage = businessLinkedinPageRepo.findByProfileId(socialScanEventDTO.getExternalId());
            long diff = socialScanEventDTO.getEndDate().getTime() - socialScanEventDTO.getStartDate().getTime();
            int daysLeft = (int) TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
            Date initialDate = socialScanEventDTO.getStartDate();
            do {
                if (daysLeft <= 30) {
                    PageInsights pageInsights = getInsightForLnAccount(businessLinkedinPage,initialDate,socialScanEventDTO.getEndDate());
                    if(Objects.nonNull(pageInsights)) {
                        kafkaProducerService.sendObjectV1(Constants.LINKEDIN_PAGE_INSIGHTS, pageInsights);
                        addInsightsToDB(pageInsights, socialScanEventDTO.getEndDate());
                    }
                    return;
                }
                Date newEndDate = DateUtils.addDays(initialDate, 30);
                PageInsights pageInsights = getInsightForLnAccount(businessLinkedinPage,initialDate,newEndDate);
                initialDate = newEndDate;
                daysLeft -= 30;
                if(Objects.nonNull(pageInsights)) {
                    kafkaProducerService.sendObjectV1(Constants.LINKEDIN_PAGE_INSIGHTS, pageInsights);
                    addInsightsToDB(pageInsights, newEndDate);
                }
            }while(true);
        }
    }

    private PageInsights getInsightForLnAccount(BusinessLinkedinPage linkedinPage, Date startDate, Date endDate) {
        try {
            LinkedInAPIResponse data = linkedinConnectService.getTimeBoundPageInsightResponse(linkedinPage.getAccessToken(),
                    linkedinPage.getUrn(), startDate.getTime(), endDate.getTime(), LinkedInTimeGranularity.DAY);
            LinkedInAPIResponse followerTimeBased = linkedinService.getTimeBasedFollowerCountForOrganization(linkedinPage.getAccessToken(),
                    linkedinPage.getUrn(), startDate.getTime(), endDate.getTime(), LinkedInTimeGranularity.DAY,true);
            List<PageLevelMetaData> pageLevelMetaDataList = reportDataConverter.prepareDayWiseDataForLinkedin(data.getElements(),
                    followerTimeBased.getElements(), endDate);

            if (CollectionUtils.isEmpty(pageLevelMetaDataList)) {
                LOG.info("Page insights not found for linked in page {}", linkedinPage.getProfileId());
                return null;
            }
            pageLevelMetaDataList.forEach(pageLevelMetaData -> {
                pageLevelMetaData.setFollowerLostCount( 0);
                pageLevelMetaData.setLikesGainCount(0);
                pageLevelMetaData.setProfileVideoViews(0);
                pageLevelMetaData.setTotalProfileVideoViews(0);
            });
            return new PageInsights(linkedinPage.getEnterpriseId(), linkedinPage.getProfileId(), linkedinPage.getBusinessId(), SocialChannel.LINKEDIN.getName(), pageLevelMetaDataList);
        } catch(Exception e) {
            LOG.error("Exception while fetching and storing page insights for profile id {}, business id {}", linkedinPage.getProfileId(), linkedinPage.getBusinessId());
        }
        return null;
    }

    @Override
    public PostDataAndInsightResponse getLinkedInInsightsForTopPost(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        LOG.info("Request to get post insight with enterprise id: {} for linkedIn", insightsRequest.getEnterpriseId());
        List<List<Integer>> batches = TopPostsReportUtils.splitListIntoBatches(insightsRequest.getBusinessIds(), BATCH_SIZE_FOR_TOP_POSTS);
        ExecutorService executorService = Executors.newFixedThreadPool(THREADPOOL_SIZE_FOR_TOP_POSTS);
        List<Future<PostDataAndInsightResponse>> futures = new ArrayList<>();
        for (List<Integer> batch : batches) {
            List<String> pageIds = getPageIdsFromBids(batch);
            InsightsPostRequest insightsPostRequest = new InsightsPostRequest(insightsRequest, startIndex, pageSize, sortParam, sortOrder, Integer.toString(SocialChannel.LINKEDIN.getId()), pageIds);
            futures.add(executorService.submit(() -> {
                InsightsESRequest request = reportDataConverter.createESRequestForTopPosts(insightsPostRequest, ElasticConstants.POST_INSIGHTS.getName());
                return reportsEsService.getPostDataFromEs(request, excelDownload);
            }));
        }
        List<PostDataAndInsightResponse> combinedResponses = new ArrayList<>();
        try {
            for (Future<PostDataAndInsightResponse> future : futures) {
                combinedResponses.add(future.get());
            }
        } catch (InterruptedException | ExecutionException e) {
            LOG.error("Async Exception occurred in [getLinkedInInsightsForTopPost] for enterpriseId : {}, exception :  {}", insightsRequest.getEnterpriseId(), e.getMessage());
        } finally {
            executorService.shutdown();
        }
        return TopPostsReportUtils.getSortedInsightResponse(combinedResponses, pageSize, sortParam, sortOrder);
    }
}
