package com.birdeye.social.service;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.*;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.request.facebook.FacebookOverallRatingRequest;
import com.birdeye.social.external.service.FbMessengerExternalService;
import com.birdeye.social.external.service.IFacebookReviewService;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.facebook.*;
import com.birdeye.social.facebook.FacebookPost.FacebookPostDetails;
import com.birdeye.social.facebook.notification.FbSubscribeWebhookResponse;
import com.birdeye.social.facebook.response.FacebookBaseResponse;
import com.birdeye.social.facebook.response.FacebookErrorResponse;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.model.*;
import com.birdeye.social.model.FbNotification.EngageNotificationDetails;
import com.birdeye.social.model.FbNotification.FbAdPostDetails;
import com.birdeye.social.model.FbNotification.FreshPostNotificationRequest;
import com.birdeye.social.model.FbNotification.SubscribeToWebhookRequest;
import com.birdeye.social.model.engageV2.EngageWebhookSubscriptionRequest;
import com.birdeye.social.model.engageV2.message.EventUpdateRequest;
import com.birdeye.social.model.notification.Entry;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.service.SocialEngageService.SocialEngagementV2Service;
import com.birdeye.social.service.SocialEngageService.converter.EngageConverterServiceImpl;
import com.birdeye.social.utils.DateTimeUtils;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.SocialProxyHandler;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.time.LocalDateTime;
import java.util.Base64;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class FbNotificationServiceImpl implements IFbNotificationService {

    private static final Logger LOG = LoggerFactory.getLogger(FbNotificationServiceImpl.class);

    private static final String COMMENT = "comment";
    private static final String FB_URL = "https://facebook.com/";
    private static final String SOMETHING_WENT_WRONG = "Something went wrong while subscribing to webhook with error ";

    private static final String VALUE = "value";
    private static final String PAINLESS  = "painless";
    private static final String SOURCE_DELETE  = "ctx._source.isDeleted = params.value";
    private static final String MESSENGER = "messenger/";
    public static final String PHOTO = "photo";
    public static final String VIDEO = "video";
    public static final String MULTI_SHARE_NO_CARD = "multi_share_no_end_card";
    public static final String SHARE = "share";
    public static final String ALBUM = "album";
    public static final String VIDEO_AUTOPLAY = "video_autoplay";
    public static final String VIDEO_INLINE = "video_inline";
    public static final String ANIMATED_IMAGE_SHARE = "animated_image_share";

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private KafkaExternalService kafkaExternalService;

    @Autowired
    private SocialProxyHandler proxyHandler;

    @Autowired
    private FBReviewService		fbReviewService;

    @Autowired
    FacebookMessengerService fbMsgService;

    @Autowired
    IFacebookReviewService rawReviewService;

    @Autowired
    private IRedisExternalService redisExternalService;

    @Autowired
    private SocialFBPageRepository socialFbPageRepo;

    @Autowired
    private IFBReviewNotificationInfoService fbReviewNotificationInfoService;

    @Autowired
    private FacebookService facebookService;

    @Autowired
    private FBPostConversationRepo fbPostConversationRepo;

    @Autowired
    private SocialEngagementService engagementService;

    @Autowired
    private FbMessengerExternalService fbMessengerExternalService;

    @Autowired
    private EngageFeedDetailsRepo engageFeedDetailsRepo;

    @Autowired
    private NexusService nexusService;

    @Autowired
    private SocialEngagementV2Service socialEngagementV2Service;

    @Autowired
    private EsService esService;

    @Autowired
    private SocialBusinessService socialBusinessService;


    @Autowired
    private BusinessInstagramAccountRepository instagramAccountRepository;
    @Autowired
    private EngageConverterServiceImpl engageConverterService;

    @Autowired
    private ISocialAppService socialAppService;

    @Autowired
    private FacebookPageService facebookPageService;

    @Autowired
    @Lazy
    private FacebookSocialAccountService fbSocialAccountService;

    @Autowired
    private IInstragramSetupService socialInstagramService;

    @Autowired
    private BusinessInstagramAccountRepository instagramAccountRepo;

    @Autowired
    private SocialDataDeletionAuditRepository socialDataDeletionAuditRepository;


    @Override
    public void processFbEvent(FacebookEventRequest facebookEventRequest) {
        boolean sendNotificationEvent = !CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDisableFbRequestToBam();
        if(Objects.nonNull(facebookEventRequest) && !CollectionUtils.isEmpty(facebookEventRequest.getEntry())){
                facebookEventRequest.getEntry().forEach(entry -> {
                    List<Integer> businessIds =  socialFbPageRepo.findBusinessIdByFacebookPageIdAndIsValid(entry.getId().toString(),1);
                    if(CollectionUtils.isEmpty(businessIds)){
                        fbReviewNotificationInfoService.saveNotificationRequest(businessIds,facebookEventRequest,
                                entry.getChanges().get(0).getValue().getVerb(),entry.getId().toString(),
                                "REJECTED","Rejecting because facebook page is inactive/no mapping found",
                                null, null);
                        return;
                    }
                    if(!CollectionUtils.isEmpty(entry.getChanges())){
                        entry.getChanges().forEach(changes -> {
                            updateReactionInES(changes,entry.getId());
                            if(null !=changes.getField()
                                    && "ratings".equalsIgnoreCase(changes.getField())
                                    && changes.getValue()!=null
                                    && "rating".equalsIgnoreCase(changes.getValue().getItem())
                                    && ("add".equalsIgnoreCase(changes.getValue().getVerb())
                                    || "edit".equalsIgnoreCase(changes.getValue().getVerb()))){
                                try {
                                    changes.getValue().setBusinessId(businessIds.get(0));
                                    LOG.info("fetching overlay details from facebook for pageID {}",entry.getId());
                                    changes.getValue().setOverlayDetails(getOverlayDetailFromFacebook(entry.getId()));
                                    LOG.info("fetching reviewer url details from facebook for pageID {} and userId {}",entry.getId(),changes.getValue().getReviewer_id());
                                    changes.getValue().setReviewerImageUrl(getReviewerUrlFromFacebook(entry.getId().toString(),changes.getValue().getReviewer_id()));
                                    LOG.info("sending data to bam for facebook for pageID {} and userId {}",entry.getId(),changes.getValue().getReviewer_id());
                                    boolean isValidChangeEvent = isValidChangeEvent(String.valueOf(entry.getId()), changes);
                                    if(sendNotificationEvent && isValidChangeEvent) {
                                        kafkaProducerService.sendObject(Constants.FB_REVIEW_NOTIFICATION_TOPIC,facebookEventRequest);
                                    }
                                    fbReviewNotificationInfoService.saveNotificationRequest(businessIds, facebookEventRequest,
                                            changes.getValue().getVerb(), entry.getId().toString(),"SUCCESS",
                                            sendNotificationEvent ? (isValidChangeEvent ? "Data sent to BAM" : "Data not sent to BAM (FB event is invalid)")
                                                    : "Data not sent to BAM (Social-BAM FB notifications are disabled)",
                                            changes.getValue().getOpen_graph_story_id(),
                                            changes.getValue().getReviewer_id());
                                } catch (Exception e) {
                                    LOG.error("Exception occurred while fetching overlay/reviewer url details from facebook for pageID {} and exception {}",entry.getId(),e);
                                    fbReviewNotificationInfoService.saveNotificationRequest(businessIds, facebookEventRequest, changes.getValue().getVerb(),
                                            entry.getId().toString(),"FAILED","Exception occurred",
                                            changes.getValue().getOpen_graph_story_id(), changes.getValue().getReviewer_id());
                                    return;
                                }
                            }else{
                                LOG.debug("ignoring facebook event for page {} because its field is other than ratings =  {}",entry.getId(),changes.getField());
                            }
                        });
                    }
                });
        }
    }

    private void updateReactionInES(Changes changes,Long pageId) {
        LOG.info("Update reaction for page id : {}",pageId);
        if(Objects.isNull(changes.getField())
                || !FbNotificationFieldEnum.FEED.getName().equalsIgnoreCase(changes.getField())
                || Objects.isNull(changes.getValue()) || Objects.isNull(changes.getValue().getItem())
                || !FbNotificationFieldEnum.REACTION.getName().equalsIgnoreCase(changes.getValue().getItem())){
            LOG.info("This is not a reaction event for page id : {}",pageId);
            return;
        }
        Value value = changes.getValue();
        if(Objects.isNull(value.getFrom().getId())){
            LOG.info("Page Id {} and receiver id {} are not same",pageId,value.getFrom().getId());
            return;
        }
        EventUpdateRequest eventUpdateRequest = new EventUpdateRequest();
        if(Objects.nonNull(value.getComment_id())) {
            eventUpdateRequest.setEventId(value.getComment_id());
        }else if(Objects.nonNull(value.getPost_id())){
            eventUpdateRequest.setEventId(value.getPost_id());
        }
        FBNotificationEventTypeEnum eventTypeEnum = FBNotificationEventTypeEnum.getEnumByName(value.getVerb());
        if(Objects.isNull(eventTypeEnum)){
            LOG.info("No event type accepted with value : {} for id : {}",value.getVerb(),eventUpdateRequest.getEventId());
            return;
        }
        LOG.info("Facebook event type is : {} for page id : {}",eventTypeEnum,pageId);
        switch (eventTypeEnum){
            case ADD:
                eventUpdateRequest.setEventType(EngageActionsEnum.LIKE);
                break;
            case REMOVE:
                eventUpdateRequest.setEventType(EngageActionsEnum.UNLIKE);
                break;
            default:
                break;
        }
        eventUpdateRequest.setFrom(value.getFrom().getId());
//        kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_SAVE_REACTION.getName(),eventUpdateRequest);
    }

    @Override
    public void processFbPostEvent(FacebookEventRequest facebookEventRequest) {
        try {
            LOG.info("FB notification received {}", facebookEventRequest);
            List<Entry> entry = facebookEventRequest.getEntry();
            entry.stream().forEach(v -> {
                String pageId = v.getId().toString();

                v.getChanges().stream().forEach(change -> {
                    updateReactionInES(change,v.getId());
                    if(!change.getField().equalsIgnoreCase("feed")) {
                        LOG.info("Received {} event in FB feed type event", change.getField());
                        return;
                    }
                    String payload =  JSONUtils.toJSON(facebookEventRequest);

                    if(FBNotificationEventTypeEnum.REMOVE.getName().equalsIgnoreCase(change.getValue().getVerb())) {
                        // remove from ES
                        FbAdPostDetails adPostDetails = new FbAdPostDetails();
                        adPostDetails.setCommentId(change.getValue().getComment_id());
                        adPostDetails.setEventAction(FBNotificationEventTypeEnum.REMOVE.getName());
                        kafkaProducerService.sendObjectV1("update-fb-comment-es",  adPostDetails);
                        LOG.info("Post delete from ES {}", change.getValue().getComment_id());
                        return;
                    }


                    if(Objects.nonNull(change.getValue().getPost())) {
                        // this is an ad comment event.


                        FbAdPostDetails adPostDetails = new FbAdPostDetails();
                        adPostDetails.setPageId(pageId);
                        adPostDetails.setPostId(change.getValue().getPost_id());
                        adPostDetails.setCommentId(change.getValue().getComment_id());
                        adPostDetails.setEventParentId(change.getValue().getParent_id());
                        adPostDetails.setReviewerId(change.getValue().getFrom().getId());
                        adPostDetails.setReviewerComment(change.getValue().getMessage());
                        adPostDetails.setEventType(change.getValue().getPost().getIs_published() ? "feed" : "ad");
                        adPostDetails.setPostUrl(FB_URL + change.getValue().getPost_id());
                        adPostDetails.setReviewerUrl(FB_URL + change.getValue().getFrom().getId());
                        adPostDetails.setReviewerName(change.getValue().getFrom().getName());
                        adPostDetails.setImageUrl(change.getValue().getPhoto());
                        adPostDetails.setEventAction(change.getValue().getVerb());
                        adPostDetails.setAdminLike(true);
                        adPostDetails.setMarkAsComplete(false);
                        adPostDetails.setIsAdminComment(pageId.equalsIgnoreCase(change.getValue().getFrom().getId()) ? true : false);
                        adPostDetails.setIsParentComment(change.getValue().getParent_id().equalsIgnoreCase(change.getValue().getPost_id())
                                ? true : false);


                        if(FBNotificationEventTypeEnum.UPDATE.getName().equalsIgnoreCase(change.getValue().getVerb())) {
                            adPostDetails.setIsEdited(true);
                            adPostDetails.setId(change.getValue().getComment_id());
                        }
                        if(FBNotificationEventTypeEnum.ADD.getName().equalsIgnoreCase(change.getValue().getVerb())) {
                            // convert comment date to UTC
                            Date date = new Date(Long.parseLong(change.getValue().getCreated_time().toString()) * 1000);
                            adPostDetails.setCommentDate(DateTimeUtils.localToUTCSqlFormat(date));
                            adPostDetails.setIsEdited(false);

                        }


                        // save to DB and send event to ES save with DB acknowledgement.
                        Integer acknowledgementId = validateFBPostDetails(adPostDetails, change.getValue().getVerb());

                        adPostDetails.setAcknowledgementId(acknowledgementId);

                        if( FBNotificationEventTypeEnum.UPDATE.getName().equalsIgnoreCase(change.getValue().getVerb())) {
                            adPostDetails.setEventAction(FBNotificationEventTypeEnum.UPDATE.getName());
                            kafkaProducerService.sendObjectV1("update-fb-comment-es",  adPostDetails);
                        }
                        if(FBNotificationEventTypeEnum.ADD.getName().equalsIgnoreCase(change.getValue().getVerb())) {
                            kafkaProducerService.sendObjectV1("add-fb-comment-es", adPostDetails);

                        }
                    }
                });

            });

        } catch (Exception e) {
            LOG.info("Something went wrong while parsing post event ", e);
            String pageId = facebookEventRequest.getEntry().get(0).getId().toString();
            String payload = JSONUtils.toJSON(facebookEventRequest);
        }
    }

    @Override
    public void subscribeEngageToWebhook(SubscribeToWebhookRequest request) {
        try {
            List<BusinessFBPage> fbPages = socialFbPageRepo.findByBusinessIdAndIsValid(request.getBusinessId(), 1);

            if(CollectionUtils.isEmpty(fbPages)) {
                LOG.info("No valid FB page found for stream webhook subscription for businessId {}", request.getBusinessId());
                throw new BirdeyeSocialException("No valid FB page found for stream webhook subscription.");
            }

            BusinessFBPage fbPage = fbPages.get(0);

            FbSubscribeWebhookResponse response = fbMessengerExternalService.fbPageGetSubscribeFields(fbPage.getFacebookPageId(), fbPage.getPageAccessToken());

            if(Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getData())
                    && response.getData().get(0).getSubscribed_fields().contains(Constants.FB_FEED_SUBSCRIPTION_FIELD)
                    && response.getData().get(0).getSubscribed_fields().contains(Constants.FB_MENTION_SUBSCRIPTION_FIELD)) {
                LOG.info("Page is already subscribed for feed webhook, skip this step");
                return;
            }

            if(Objects.nonNull(response) && Objects.nonNull(response.getData())) {
                List<String> addedSubscribeFields = new ArrayList<>();
                if(!CollectionUtils.isEmpty(response.getData())) {
                    addedSubscribeFields.addAll(response.getData().get(0).getSubscribed_fields());
                }
                if(!addedSubscribeFields.contains(Constants.FB_FEED_SUBSCRIPTION_FIELD)) {
                    addedSubscribeFields.add(Constants.FB_FEED_SUBSCRIPTION_FIELD);
                }
                if(!addedSubscribeFields.contains(Constants.FB_MENTION_SUBSCRIPTION_FIELD)) {
                    addedSubscribeFields.add(Constants.FB_MENTION_SUBSCRIPTION_FIELD);
                }

                String payload = StringUtils.join(addedSubscribeFields, ",");
                FacebookBaseResponse baseResponse = fbMessengerExternalService.fbPageSubscribeApps(fbPage.getFacebookPageId(), fbPage.getPageAccessToken(), payload);

                if (Objects.nonNull(baseResponse.getError())) {
                    LOG.info("Cannot subscribe to webhook for business id {} with error {}",request.getBusinessId(), baseResponse);
                    throw new BirdeyeSocialException("Cannot subscribe to webhook for business");
                }
            }

        } catch (BirdeyeSocialException e) {
            LOG.info(SOMETHING_WENT_WRONG, e);
            throw new BirdeyeSocialException(ErrorCodes.PAGE_UNABLE_TO_SUBSCRIBE_FB_APP, SOMETHING_WENT_WRONG);
        } catch (Exception ex) {
            LOG.info(SOMETHING_WENT_WRONG, ex);
            throw new BirdeyeSocialException(ErrorCodes.PAGE_UNABLE_TO_SUBSCRIBE_FB_APP, SOMETHING_WENT_WRONG);
        }
    }

    @Override
    public void unSubscribeToWebhook(SubscribeToWebhookRequest request) {
        try {
            List<BusinessFBPage> fbPages = socialFbPageRepo.findByFacebookPageIdAndIsValid(request.getPageId(), 1);

            if(CollectionUtils.isEmpty(fbPages)) {
                LOG.info("No valid FB page found for stream webhook subscription for pageId {}", request.getPageId());
                throw new BirdeyeSocialException("No valid FB page found for stream webhook subscription.");
            }

            BusinessFBPage fbPage = fbPages.get(0);

            if (fbPage.getMessengerOpted() == 1) {
                List<BusinessInstagramAccount> account = instagramAccountRepository.findByFacebookPageIdAndBusinessIdIsNotNull(fbPage.getFacebookPageId());
                if(org.apache.commons.collections4.CollectionUtils.isEmpty(account)) {

                    FacebookBaseResponse response = fbMessengerExternalService.fbPageUnsubscribeApps(fbPage.getFacebookPageId(), fbPage.getPageAccessToken());
                    if (response.isSuccess()) {
                        LOG.info("Messenger unsubscribed for pageId: {}", fbPage.getFacebookPageId());
                        //log into BFacebookPage table
                        fbPage.setMessengerOpted(0);
                        fbPage.setRatingsOpted(0);
                        socialFbPageRepo.saveAndFlush(fbPage);
                    }
                }
            }
        } catch (Exception ex) {
            LOG.info(SOMETHING_WENT_WRONG, ex);
        }
    }

    @Override
    public void removePageEngageToWebhook(EngageWebhookSubscriptionRequest request) {
        try {
            String pageId = request.getPageId();
            String pageAccessToken = request.getPageAccessToken();

            List<BusinessInstagramAccount> account = instagramAccountRepository.findByFacebookPageIdAndBusinessIdIsNotNull(pageId);
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(account)) {
                FacebookBaseResponse response = fbMessengerExternalService.fbPageUnsubscribeApps(pageId, pageAccessToken);
                if (response.isSuccess()) {
                    LOG.info("Messenger unsubscribed for pageId: {}", pageId);
                }
            }
        } catch (Exception ex) {
            LOG.info(SOMETHING_WENT_WRONG, ex);
        }
    }

    private void pushFbStatusInFirebase(Integer enterpriseId, Integer locationId, String status) {
        String topicSocial = MESSENGER+enterpriseId+"/"+locationId;
        nexusService.insertMapInFirebase(topicSocial,"socialIntegrationStatus", status);
    }

    @Override
    public void processFbEngageEvent(FacebookEventRequest facebookEventRequest) {
        LOG.info("FB engage notification received {}", facebookEventRequest);
        try {
            List<Entry> entry = facebookEventRequest.getEntry();
            entry.stream().forEach(v -> {
                String pageId = v.getId().toString();
                List<BusinessFBPage> fbPages = socialFbPageRepo.findByFacebookPageIdAndIsValid(pageId,1);
                if(CollectionUtils.isEmpty(fbPages)) {
                    LOG.info("No valid FB page found for stream webhook subscription for pageId {}", pageId);
                    return;
                }
                v.getChanges().stream().forEach(change -> {
                    if(CollectionUtils.isEmpty(fbPages)){
                        LOG.info("[ProcessFbEngageEvent] Rejecting webhook event because either the  page doesn't exists or the page is marked invalid. pageId: {}", pageId);
                        return;
                    }
                    if(!FbNotificationFieldEnum.REACTION.getName().equalsIgnoreCase(change.getValue().getItem())
                            && (FBNotificationEventTypeEnum.REMOVE.getName().equalsIgnoreCase(change.getValue().getVerb())
                            || FBNotificationEventTypeEnum.EDITED.getName().equalsIgnoreCase(change.getValue().getVerb()))){
                        updateFeedEvent(change);
                        return;
                    }
                    boolean postExists =  Objects.nonNull(engageFeedDetailsRepo.findFirstByEngageIdAndTypeAndPageId(change.getValue().getPost_id(), EngageV2FeedTypeEnum.POST.name(), pageId));
                    /** MENTION case start* */
                    if(!postExists &&
                            FBNotificationEventTypeEnum.ADD.getName().equalsIgnoreCase(change.getValue().getVerb()) &&
                            change.getField().equalsIgnoreCase(Constants.ENGAGE_FEED_TYPE_MENTION) &&
                            change.getValue().getItem().equalsIgnoreCase("POST")){// item

                        /** if need to fetch post data from facebook end**/
                        createFBMentionPostDataToES(change, pageId, fbPages);
                    }

                    /** MENTION case end* */

                    // wall post webhook
                    if(!postExists &&
                            FBNotificationEventTypeEnum.ADD.getName().equalsIgnoreCase(change.getValue().getVerb()) &&
                            (change.getValue().getItem().equalsIgnoreCase("POST")
                                    || change.getValue().getItem().equalsIgnoreCase("status")
                            || change.getValue().getItem().equalsIgnoreCase("share")
                                    || change.getValue().getItem().equalsIgnoreCase("video")
                                    ||   change.getValue().getItem().equalsIgnoreCase("photo"))) {
                        FreshPostNotificationRequest request = new FreshPostNotificationRequest();
                        request.setPageId(pageId);
                        request.setPostId(change.getValue().getPost_id());
                        request.setType(EngageV2FeedTypeEnum.POST.name());
                        request.setChannel(SocialChannel.FACEBOOK.getName());
                        kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_GENERATE_TOPIC.getName(),  request);
                        return;
                    }

                    // type - POST , subType - null
                    // check if ad post exists in our db, skip if present. post comment will be fetched via API now. we can ignore it.
                    if(!postAlreadyExists(change.getValue().getPost_id(), EngageV2FeedTypeEnum.AD_POST.name(), pageId) && change.getValue().getPost() !=null &&!change.getValue().getPost().getIs_published()) {
                        FreshPostNotificationRequest request = new FreshPostNotificationRequest();
                        request.setPageId(pageId);
                        request.setPostId(change.getValue().getPost_id());
                        request.setType(EngageV2FeedTypeEnum.AD_POST.name());
                        request.setChannel(SocialChannel.FACEBOOK.getName());
                        kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_GENERATE_TOPIC.getName(),  request);
                        return;
                    }
                    if(FbNotificationFieldEnum.REACTION.getName().equalsIgnoreCase(change.getValue().getItem())){
                        processLikeEvent(change, pageId);
                    }

                    if((!change.getField().equalsIgnoreCase(Constants.ENGAGE_FEED_TYPE_MENTION)) && Objects.nonNull(change.getValue().getPost())) {
                        // this is a comment event.
                        processCommentEvent(change, pageId, fbPages,facebookEventRequest.isAddedFromDashboard());
                    }
                });

            });

        }catch (BirdeyeSocialException ex){
            Map<String, Object> errorData = ex.getData();

            String pageId = facebookEventRequest.getEntry().get(0).getId().toString();
            Boolean markPageInactive = errorData.get("mark_page_inactive") != null && (Boolean) errorData.get("mark_page_inactive");
            Integer fbErrorCode = (Integer) errorData.get("errorCode");
            Integer fbErrorSubCode = (Integer) errorData.get("errorSubCode");
            LOG.info("BirdEyeSocialException occurred while processing FB notification. pageId: {}, errorCode: {}, errorSubCode: {}, markPageInactive: {}",
                    pageId, fbErrorCode, fbErrorSubCode, markPageInactive);
            if (null != fbErrorCode && fbErrorCode == 200) {
                LOG.info("Facebook permission error for pageId: {}. Exception: {}",
                        facebookEventRequest.getEntry().get(0).getId(), ex.getMessage());
                kafkaExternalService.markPageInvalid(SocialChannel.FACEBOOK.getName(), pageId);
            }else {
                LOG.info("[FB Notification] Something went wrong while parsing comment event : {}", ex.getMessage());
            }
        } catch (Exception e) {
            LOG.info("Something went wrong while parsing post event : {}", e.getMessage());
        }
    }

    @Override
    public Boolean subscribeToWebhookV2(SubscribeToWebhookRequest request) {
        List<BusinessFBPage> fbPages = socialFbPageRepo.findByBusinessIdAndIsValid(request.getBusinessId(), 1);

        if (CollectionUtils.isEmpty(fbPages)) {
            LOG.info("No valid FB page found for stream webhook subscription for businessId {}", request.getBusinessId());
            throw new BirdeyeSocialException("No valid FB page found for stream webhook subscription.");
        }

        BusinessFBPage page = fbPages.get(0);

        FacebookBaseResponse response = null;
        if (Objects.isNull(page.getPagePermissions())) {
            return false;
        }
        boolean messagingOn = page.getPagePermissions().contains(Constants.PAGES_MESSAGING);
        String subscriptionFields = messagingOn ? Constants.FB_MESSENGER_RATINGS_SUBSCRIPTION_FIELDS
                : Constants.FB_RATINGS_SUBSCRIPTION_FIELDS;
        Boolean isEngageEnabled = socialBusinessService.isEngageEnabled(page.getEnterpriseId());

        //TODO : add engage enabled check for below 2 lines, Also, cater when social property of engage enabled changes
        if (isEngageEnabled) {
            subscriptionFields = subscriptionFields.concat(",").concat(Constants.FB_FEED_SUBSCRIPTION_FIELD);
            subscriptionFields = subscriptionFields.concat(",").concat(Constants.FB_MENTION_SUBSCRIPTION_FIELD);
        }
        try {
            response = fbMessengerExternalService.fbPageSubscribeApps(page.getFacebookPageId(), page.getPageAccessToken(),
                    subscriptionFields);
            if (response.isSuccess()) {
                LOG.info("FB webhook subscribed successfully for for businessId: {} and pageId: {}", page.getBusinessId(),
                        page.getFacebookPageId());
                page.setRatingsOpted(1);
                if (messagingOn) {
                    page.setMessengerOpted(1);
                }
                socialFbPageRepo.saveAndFlush(page);
            }
        } catch (Exception e) {
            LOG.error("FB webhook subscribe flow failed for businessId: {} and pageId: {}", page.getBusinessId(), page.getFacebookPageId());
            if (CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBooleanProperty(Constants.SET_FAILURE_REDIS)) {
                redisExternalService.setKeyAndValue(Constants.FB_FAILURE_REDIS_KEY, page.getFacebookPageId());
                LOG.info("Get key FB_FAILURE_REDIS_KEY: {}", redisExternalService.get(Constants.FB_FAILURE_REDIS_KEY));
            }
        }
        if (isEngageEnabled) {
            return true;
        }
        return false;
    }

    @Override
    public void unSubscribeEngageToWebhook(SubscribeToWebhookRequest request) {
        try {
            List<BusinessFBPage> fbPages = socialFbPageRepo.findByFacebookPageIdAndIsValid(request.getPageId(), 1);

            if(org.springframework.util.CollectionUtils.isEmpty(fbPages)) {
                LOG.info("No valid FB page found for stream webhook subscription for businessId {}", request.getBusinessId());
                throw new BirdeyeSocialException("No valid FB page found for stream webhook subscription.");
            }

            BusinessFBPage fbPage = fbPages.get(0);

            FbSubscribeWebhookResponse response = fbMessengerExternalService.fbPageGetSubscribeFields(fbPage.getFacebookPageId(), fbPage.getPageAccessToken());

            if(Objects.nonNull(response) && response.getData().get(0).getSubscribed_fields().contains(Constants.FB_FEED_SUBSCRIPTION_FIELD)) {

                List<String> subscribeFields = response.getData().get(0).getSubscribed_fields()
                        .stream()
                        .filter(e -> !e.equalsIgnoreCase(Constants.FB_FEED_SUBSCRIPTION_FIELD))
                        .filter(e -> !e.equalsIgnoreCase(Constants.FB_MENTION_SUBSCRIPTION_FIELD))
                        .collect(Collectors.toList());

                if(org.springframework.util.CollectionUtils.isEmpty(subscribeFields)) {
                    LOG.info("No field available to subscribe");
                }

                FacebookBaseResponse baseResponse =
                        fbMessengerExternalService.fbPageSubscribeApps(fbPage.getFacebookPageId(), fbPage.getPageAccessToken(), org.apache.commons.lang3.StringUtils.join(subscribeFields, ","));

                if (Objects.nonNull(baseResponse.getError())) {
                    LOG.info("Cannot unsubscribe to webhook for business id {} with error {}",request.getBusinessId(), baseResponse);
                    throw new BirdeyeSocialException("Cannot unsubscribe to webhook for business");
                }

                return;
            }

        } catch (Exception ex) {
            LOG.info(SOMETHING_WENT_WRONG, ex);
        }
    }

    private void processCommentEvent(Changes change, String pageId, List<BusinessFBPage> fbPages, boolean isAddedFromDashboard) {
        if (Objects.nonNull(change) && Objects.nonNull(change.getValue()) && StringUtils.isNotEmpty(change.getValue().getComment_id()) &&
                StringUtils.isNotEmpty(pageId)) {
            EngageFeedDetails existingPost = engageFeedDetailsRepo.findFirstByFeedIdAndPageId(change.getValue().getComment_id(), pageId);
            if (Objects.nonNull(existingPost)) {
                LOG.info("This comment is already processed, must be duplicate. exiting process");
                return;
            }
        }
        EngageNotificationDetails notificationDetails = new EngageNotificationDetails();
        Comment comment = proxyHandler.runWithRetryableBirdeyeException(() ->
                facebookService.getCommentById(change.getValue().getComment_id(), fbPages.get(0).getPageAccessToken())
        );
        notificationDetails.setPageId(pageId);
        if(!CollectionUtils.isEmpty(fbPages)) {
            notificationDetails.setLocationId(fbPages.get(0).getBusinessId());
            notificationDetails.setAccountId(fbPages.get(0).getAccountId());
        }
        notificationDetails.setPostId(change.getValue().getPost_id());
        notificationDetails.setAddedFromDashboard(isAddedFromDashboard);
        try {
            EngageNotificationDetails engageNotificationDetails = fetchEsDocByFeedIdAndPageId(change.getValue().getPost_id(), pageId);
            if(Objects.isNull(engageNotificationDetails)) {
                LOG.info("Cannot find feed for postId {}",change.getValue().getPost_id());
                FreshPostNotificationRequest request = new FreshPostNotificationRequest();
                request.setPageId(pageId);
                request.setPostId(change.getValue().getPost_id());
                request.setType(EngageV2FeedTypeEnum.POST.name());
                request.setChannel(SocialChannel.FACEBOOK.getName());
                kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_GENERATE_TOPIC.getName(),  request);
            }
            notificationDetails.setFeedId(change.getValue().getComment_id());
            notificationDetails.setEventParentId(change.getValue().getParent_id());
            notificationDetails.setAuthorId(change.getValue().getFrom().getId());
            notificationDetails.setText(change.getValue().getMessage());
            notificationDetails.setPostUrl(FB_URL + change.getValue().getPost_id());
            notificationDetails.setReviewerUrl(FB_URL + change.getValue().getFrom().getId());
            notificationDetails.setAuthorName(change.getValue().getFrom().getName());
            notificationDetails.setAuthorUsername(change.getValue().getFrom().getName());

            notificationDetails.setAuthorProfileImage(null);
            if (Objects.nonNull(change.getValue().getPhoto())) {
                notificationDetails.setImageUrls(Arrays.asList(change.getValue().getPhoto()));
            }
            notificationDetails.setEventAction(change.getValue().getVerb());
            notificationDetails.setIsLikedByAdmin(false);
            notificationDetails.setIsCompleted(false);
            notificationDetails.setIsAdminComment(pageId.equalsIgnoreCase(change.getValue().getFrom().getId()) ? true : false);
            notificationDetails.setIsParentComment(change.getValue().getParent_id().equalsIgnoreCase(change.getValue().getPost_id())
                    ? true : false);
            notificationDetails.setEngageFeedId(change.getValue().getParent_id());
            notificationDetails.setSourceId(SocialChannel.FACEBOOK.getId());
            notificationDetails.setChannel(SocialChannel.FACEBOOK.getName());
            //
            notificationDetails.setType(change.getValue().getPost().getIs_published() ? EngageV2FeedTypeEnum.COMMENT.name() :
                    EngageV2FeedTypeEnum.AD_COMMENT.name());

            if (notificationDetails.getType().equals(EngageV2FeedTypeEnum.COMMENT.name())
                    && !CollectionUtils.isEmpty(fbPages)) {
                if (Objects.nonNull(engageNotificationDetails) && Objects.nonNull(engageNotificationDetails.getSubType())
                        && engageNotificationDetails.getSubType().equalsIgnoreCase(EngageV2FeedSubTypeEnum.MENTION.name())) {
                    notificationDetails.setCanReplyPrivately(true);
                } else {
                    if (Objects.nonNull(comment)) {
                        notificationDetails.setCanReplyPrivately(comment.getCan_reply_privately());
                        if (Objects.isNull(notificationDetails.getImageUrls()) && Objects.nonNull(comment.getAttachment()) && Objects.nonNull(comment.getAttachment().getMedia()) &&
                                Objects.nonNull(comment.getAttachment().getMedia().getImage()) &&
                                Objects.nonNull(comment.getAttachment().getMedia().getImage().getSrc())) {
                            notificationDetails.setImageUrls(Collections.singletonList(comment.getAttachment().getMedia().getImage().getSrc()));
                        }
                    }
                }
            }
            if (Boolean.FALSE.equals(notificationDetails.getIsParentComment())) {
                notificationDetails.setSubType(EngageV2FeedSubTypeEnum.REPLY.name());
//            EngageNotificationDetails parentComment = fetchEsDocByFeedIdAndPageId(change.getValue().getParent_id(), pageId);
//            if(Objects.isNull(parentComment)) {
//                LOG.info("Cannot find feed for parentComment {}",change.getValue().getComment_id());
//            }else{
//                parentComment.setCommentCount(parentComment.getCommentCount()==null? 1 : parentComment.getCommentCount()+1);
//                kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC_UPDATE.getName(), parentComment);
//            }
            }

            //
            notificationDetails.setHideOnThread(notificationDetails.getIsAdminComment());

            if (FBNotificationEventTypeEnum.ADD.getName().equalsIgnoreCase(change.getValue().getVerb())) {
                // convert comment date to UTC
                Date date = new Date(Long.parseLong(change.getValue().getCreated_time().toString()) * 1000);
                notificationDetails.setFeedDate(DateTimeUtils.localToUTCSqlFormat(date));
                notificationDetails.setIsEdited(false);

            }


            // save to DB and send event to ES save with DB acknowledgement.
            Integer acknowledgementId = validateFBContentDetails(notificationDetails, pageId, true);

            if (Objects.isNull(acknowledgementId)) {
                LOG.info("This event is already processed, must be duplicate. exiting process");
                return;
            }

            notificationDetails.setRawFeedId(acknowledgementId);
            notificationDetails.setMessageTags(Objects.isNull(comment) ? null : comment.getMessage_tags());
            if (FBNotificationEventTypeEnum.ADD.getName().equalsIgnoreCase(change.getValue().getVerb())) {
                kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC.getName(), notificationDetails);
            }
        } catch (Exception e) {
            LOG.info("Exception while processing comment for FB {}", e.toString());
        }
    }

    private void processLikeEvent(Changes change, String pageId) {
        // handled in @PutMapping(path ="/update/event")
//        try {
//            String feedId;
//            if(Objects.nonNull(change.getValue().getComment_id())) {
//                // Comment/Reply like Event
//                feedId = change.getValue().getComment_id();
//            } else {
//                // Post like event
//                feedId = change.getValue().getPost_id();
//            }
//            EngageNotificationDetails feed = fetchEsDocByFeedIdAndPageId(feedId, pageId);
//            FBNotificationEventTypeEnum fbNotificationEventTypeEnum =
//                    FBNotificationEventTypeEnum.getEnumByName(change.getValue().getVerb());
//            if (Objects.isNull(feed)
//                    || (((!feed.getIsLikedByAdmin() && Objects.equals(fbNotificationEventTypeEnum, FBNotificationEventTypeEnum.REMOVE))
//                    || (feed.getIsLikedByAdmin() && Objects.equals(fbNotificationEventTypeEnum, FBNotificationEventTypeEnum.ADD)))
//                    && Objects.equals(change.getValue().getFrom().getId(),pageId))) {
//                LOG.info("No feed found with feed ID {}", feedId);
//                return;
//            }
//            if(Objects.isNull(fbNotificationEventTypeEnum)) return;
//            if(Objects.isNull(feed.getLikeCount())) feed.setLikeCount(0);
//            int count = Objects.equals(fbNotificationEventTypeEnum,
//                    FBNotificationEventTypeEnum.ADD) ? feed.getLikeCount() + 1 : feed.getLikeCount() - 1;
//            feed.setLikeCount(Math.max(count, 0));
//            kafkaProducerService.sendObjectV1(
//                    FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC_UPDATE.getName(), feed);
//        }catch (Exception e){
//            LOG.error("Exception while processing like for FB",e);
//        }
    }

    private void updateFeedEvent(Changes change) {
            String feedId = change.getValue().getComment_id();
            String updatedText = change.getValue().getMessage();

            if(Objects.isNull(feedId)) {
                feedId = change.getValue().getPost_id();
            }

            if(Objects.isNull(updatedText)) {
                markDeleteESContent(feedId);
            } else {
                updateESContent(feedId, updatedText);
            }

            LOG.info("Feed delete from ES {}", change.getValue().getComment_id());
    }

    private void markDeleteESContent(String feedId) {
        try {

            List<EngageNotificationDetails> documentFromEsList = getFeedDocumentFromEs(feedId);

            if(Objects.isNull(documentFromEsList)) {
                LOG.info("No doc found in ES to be updated for feedId {}", feedId);
                return;
            }
            for(EngageNotificationDetails documentFromEs : documentFromEsList) {

                if (EngageV2FeedTypeEnum.POST.name().equalsIgnoreCase(documentFromEs.getType()) ||
                        EngageV2FeedTypeEnum.AD_POST.name().equalsIgnoreCase(documentFromEs.getType())) {
                    updateIsDeletedEsDocumentByPostId(documentFromEs.getPostId(), true);
                } else if ((EngageV2FeedTypeEnum.COMMENT.name().equalsIgnoreCase(documentFromEs.getType()) ||
                        EngageV2FeedTypeEnum.AD_COMMENT.name().equalsIgnoreCase(documentFromEs.getType())) &&
                        Objects.isNull(documentFromEs.getSubType())) {
                    updateIsDeletedEsDocumentByEventParentId(feedId, true);

                    documentFromEs.setIsDeleted(true);
                    String doc = JSONUtils.toJSON(documentFromEs, JsonInclude.Include.NON_NULL);
                    esService.updateDocument(doc, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), documentFromEs.getRawFeedId());
                } else {
                    documentFromEs.setIsDeleted(true);
                    String doc = JSONUtils.toJSON(documentFromEs, JsonInclude.Include.NON_NULL);
                    esService.updateDocument(doc, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), documentFromEs.getRawFeedId());
                }
                socialEngagementV2Service.handleDeleteEngageCase(documentFromEs);
            }
        } catch (Exception ex) {
            LOG.info("Delete content process could not complete ", ex);
        }
    }

    private void updateESContent(String feedId, String message) {
        try {
            List<EngageNotificationDetails> documentFromEsList = getFeedDocumentFromEs(feedId);

            if(Objects.isNull(documentFromEsList)) {
                LOG.info("No doc found in ES to be updated for feedId {}", feedId);
                return;
            }
            for(EngageNotificationDetails documentFromEs : documentFromEsList) {
                documentFromEs.setText(message);
                documentFromEs.setIsEdited(true);
                String doc = JSONUtils.toJSON(documentFromEs, JsonInclude.Include.NON_NULL);
                esService.updateDocument(doc, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), documentFromEs.getRawFeedId());
            }
        } catch (Exception ex) {
            LOG.info("Update Es process could not complete ", ex);
        }
    }

    private EngageNotificationDetails fetchEsDocByFeedIdAndPageId(String feedId, String pageId) throws IOException {
        return engageConverterService.fetchEsDocByFeedIdAndPageId(feedId, pageId);
    }
    private List<EngageNotificationDetails> getFeedDocumentFromEs(String feedId) throws IOException {
        return engageConverterService.fetchEsDocByFeedId(feedId);
    }

    private void updateIsDeletedEsDocumentByEventParentId(String parentId, Boolean isDeleted) throws IOException {
        try {
            UpdateByQueryRequest updateRequest = new UpdateByQueryRequest(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());
            updateRequest.setQuery(QueryBuilders.matchQuery("eventParentId.keyword", parentId));

            Map<String, Object> data = new HashMap<>();
            data.put(VALUE, isDeleted);
            Script inline = new Script(ScriptType.INLINE, PAINLESS, SOURCE_DELETE, data);
            updateRequest.setScript(inline);
            LOG.info("Query documents: {} " , updateRequest);

            BulkByScrollResponse response = esService.updateByQueryRequest(updateRequest);

            long updatedDocuments = response.getUpdated();
            LOG.info("Updated documents: {} " , updatedDocuments);


        } catch (Exception ex) {
            LOG.info("No parentId found to be updated {}", parentId);
        }

    }


    private void updateIsDeletedEsDocumentByPostId(String postId, Boolean isDeleted) throws IOException {
        try {
            UpdateByQueryRequest updateRequest = new UpdateByQueryRequest(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());
            updateRequest.setQuery(QueryBuilders.matchQuery("postId", postId));

            Map<String, Object> data = new HashMap<>();
            data.put(VALUE, isDeleted);
            Script inline = new Script(ScriptType.INLINE, PAINLESS, SOURCE_DELETE, data);
            updateRequest.setScript(inline);
            LOG.info("Query documents: {} " , updateRequest);

            BulkByScrollResponse response = esService.updateByQueryRequest(updateRequest);

            long updatedDocuments = response.getUpdated();
            LOG.info("Updated documents: {} " , updatedDocuments);


        } catch (Exception ex) {
            LOG.info("No postId found to be updated {}", postId);
        }

    }



    private void createFBMentionPostDataToES(Changes change, String pageId, List<BusinessFBPage> fbPages) {

        if(Objects.nonNull(change) && Objects.nonNull(change.getValue()) && StringUtils.isNotEmpty(change.getValue().getPost_id()) &&
                StringUtils.isNotEmpty(pageId)) {
            EngageFeedDetails existingPost = engageFeedDetailsRepo.findFirstByFeedIdAndPageId(change.getValue().getPost_id(), pageId);
            if(Objects.nonNull(existingPost)) {
                LOG.info("This event is already processed, must be duplicate. exiting process");
                return;
            }
        }

        EngageNotificationDetails notificationDetails = new EngageNotificationDetails();
        notificationDetails.setPageId(pageId);
        notificationDetails.setPostId(change.getValue().getPost_id());
        notificationDetails.setFeedId(change.getValue().getPost_id());
        notificationDetails.setEventParentId(change.getValue().getPost_id());
        if(!CollectionUtils.isEmpty(fbPages)) {
            notificationDetails.setLocationId(fbPages.get(0).getBusinessId());
            notificationDetails.setAccountId(fbPages.get(0).getAccountId());
        }

        notificationDetails.setText(change.getValue().getMessage());
        notificationDetails.setPostUrl(FB_URL + change.getValue().getPost_id());
        notificationDetails.setEventAction(change.getValue().getVerb());
        notificationDetails.setIsLikedByAdmin(false);
        notificationDetails.setIsCompleted(false);
        notificationDetails.setEngageFeedId(change.getValue().getParent_id());
        notificationDetails.setSourceId(SocialChannel.FACEBOOK.getId());
        notificationDetails.setChannel(SocialChannel.FACEBOOK.getName());
        notificationDetails.setType(EngageV2FeedTypeEnum.POST.name());
        notificationDetails.setSubType(EngageV2FeedSubTypeEnum.MENTION.name());
        notificationDetails.setHideOnThread(false);
        if(FBNotificationEventTypeEnum.ADD.getName().equalsIgnoreCase(change.getValue().getVerb())) {
            Date date = new Date(Long.parseLong(change.getValue().getCreated_time().toString()) * 1000);
            notificationDetails.setFeedDate(DateTimeUtils.localToUTCSqlFormat(date));
            notificationDetails.setIsEdited(false);
        }
        try{
            if(!CollectionUtils.isEmpty(fbPages)){
                notificationDetails.setPageName(fbPages.get(0).getFacebookPageName());
                //String pageToken="EAABm1gNd1MUBO2Mr1EjnMuRbXDkxGTtDep0dTF0Ka1ijDsKsVuoTwbuYzMTiox84iCZAP8ZAMmZBTmdMZCcNb2mThBWmqwbIDRp43pklczIiBvQfTLbPIZAvoVomFA6FszG9ASrQ2shavkHkliVIBbxzqfjiuIbksfRB3tdpVu6sss3ToUkZCYJxlLgMe1fXMZD";
                FbUserProfileInfo details = facebookService.getUserDetails(null, change.getValue().getPost_id().split("_")[0], fbPages.get(0).getPageAccessToken());
                if(details != null) {
                    notificationDetails.setAuthorProfileImage(details.getPicture().getUrl());
                    notificationDetails.setAuthorName(details.getName());
                    notificationDetails.setPageHandleName(fbPages.get(0).getHandle());
                    notificationDetails.setAuthorId(details.getId());
                    notificationDetails.setReviewerUrl(FB_URL + details.getId());
                    notificationDetails.setAuthorUsername(details.getName());
                }
                /** get post details for attachments**/
                FacebookPostDetails postDetails =
                        facebookService.getPostDataForEngageMention(fbPages.get(0).getPageAccessToken(), change.getValue().getPost_id());
                if(Objects.isNull(postDetails)) {
                    LOG.info("Cannot fetch post details for postId {}", change.getValue().getPost_id());
                }else{
                    attachPostMediaDetails(postDetails, notificationDetails);
                    notificationDetails.setMessageTags(notificationDetails.getMessageTags());
                }
            }
        }catch(Exception e){
            LOG.info("Something went wrong while parsing mention event profile pic with error : {}", e.getMessage());
        }
        // save to DB and send event to ES save with DB acknowledgement.
        Integer acknowledgementId = validateFBContentDetails(notificationDetails, pageId, true);
        if(Objects.isNull(acknowledgementId)) {
            LOG.info("This event is already processed, must be duplicate. exiting process");
            return;
        }
        notificationDetails.setRawFeedId(acknowledgementId);
        if(FBNotificationEventTypeEnum.ADD.getName().equalsIgnoreCase(change.getValue().getVerb())) {
            kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC.getName(), notificationDetails);
        }

    }

    private void attachPostMediaDetails(FacebookPostDetails postDetails, EngageNotificationDetails notificationDetails) {

        if (postDetails.getAttachments()!=null && (PHOTO.equalsIgnoreCase(postDetails.getAttachments().getData().get(0).getType())
                || SHARE.equalsIgnoreCase(postDetails.getAttachments().getData().get(0).getType())
                || MULTI_SHARE_NO_CARD.equalsIgnoreCase(postDetails.getAttachments().getData().get(0).getType())
                || ANIMATED_IMAGE_SHARE.equalsIgnoreCase(postDetails.getAttachments().getData().get(0).getType())
                ||ALBUM.equalsIgnoreCase(postDetails.getAttachments().getData().get(0).getType()) )) {
            List<String> images = new ArrayList<>();
            List<String> videos = new ArrayList<>();
            addAttachmentData(postDetails, images, videos);
            notificationDetails.setImageUrls(images);
            notificationDetails.setVideoUrls(videos);
        }  else if (postDetails.getAttachments()!=null && (VIDEO.equalsIgnoreCase(postDetails.getAttachments().getData().get(0).getType())
                || VIDEO_INLINE.equalsIgnoreCase(postDetails.getAttachments().getData().get(0).getType())
                || VIDEO_AUTOPLAY.equalsIgnoreCase(postDetails.getAttachments().getData().get(0).getType()))) {
            List<String> videos = new ArrayList<>();
            videos.add(postDetails.getAttachments().getData().get(0).getMedia().getSource());
            notificationDetails.setVideoUrls(videos);
        }

    }
    private void addAttachmentData(FacebookPostDetails postDetails, List<String> images, List<String> videos ) {
        for (Attachement data : postDetails.getAttachments().getData()) {
            if (ALBUM.equalsIgnoreCase(data.getType())
                    || MULTI_SHARE_NO_CARD.equalsIgnoreCase(data.getType())
                    || PHOTO.equalsIgnoreCase(data.getType())
                    || ANIMATED_IMAGE_SHARE.equalsIgnoreCase(data.getType())
                    || SHARE.equalsIgnoreCase(data.getType())) {
                LOG.info("Getting data for FB attachment : {}", postDetails);
                FbAttachement subAttachment = data.getSubattachments();
                if (null != subAttachment) {
                    addSubAttachmentData(subAttachment, images, videos);
                } else {
                    if (data.getMedia() != null && data.getMedia().getImage() != null && data.getMedia().getImage().getSrc() != null) {
                        images.add(data.getMedia().getImage().getSrc());
                    }
                }
            }
        }
    }
    private void addSubAttachmentData(FbAttachement subAttachment, List<String> images, List<String> videos) {
        for (Attachement data1 : subAttachment.getData()) {
            if(VIDEO.equalsIgnoreCase(data1.getType()) || VIDEO_AUTOPLAY.equalsIgnoreCase(data1.getType())) {
                videos.add(data1.getMedia().getSource());
            } else {
                images.add(data1.getMedia().getImage().getSrc());
            }
        }
    }


    private Integer validateFBPostDetails(FbAdPostDetails details, String eventAction) {


        FBPostConversation existingPost = fbPostConversationRepo.findByCommentId(details.getCommentId());

        if(Objects.isNull(existingPost)) { // create new entry in DB
            FBPostConversation data = new FBPostConversation();

            data.setPostId(details.getPostId());
            data.setReviewerComment(details.getReviewerComment());
            data.setFacebookPageId(details.getPageId());
            data.setCommentId(details.getCommentId());
            data.setReviewerId(details.getReviewerId());
            data.setParentId(details.getEventParentId());
            data.setType(details.getEventType());
            data.setPostUrl(details.getPostUrl());
            data.setReviewerName(details.getReviewerName());
            data.setReviewerUrl(details.getReviewerUrl());
            data.setCommentDate(details.getCommentDate());
            data.setImageUrl(details.getImageUrl());

           return fbPostConversationRepo.save(data).getId();
        }
        else { //look for event type, update or delete comment.

            if(FBNotificationEventTypeEnum.REMOVE.getName().equalsIgnoreCase(eventAction)) {
                // do something
                fbPostConversationRepo.delete(existingPost.getId());
            } else if(FBNotificationEventTypeEnum.UPDATE.getName().equalsIgnoreCase(eventAction)) {

                existingPost.setReviewerComment(details.getReviewerComment());
                existingPost.setImageUrl(details.getImageUrl());
                fbPostConversationRepo.save(existingPost);
            }
        }
        return null;
    }

    private boolean postAlreadyExists(String postId, String type, String pageId) {
        return Objects.nonNull(engageFeedDetailsRepo.findFirstByEngageIdAndTypeAndPageId(postId, type, pageId));
    }

    @Override
    public Integer validateFBContentDetails(EngageNotificationDetails details, String pageId, boolean existingPostChecked) {

        EngageFeedDetails existingPost = null;

        if(!existingPostChecked) {
            existingPost = engageFeedDetailsRepo.findFirstByFeedIdAndPageId(details.getFeedId(), pageId);
        }

        if(Objects.isNull(existingPost)) { // create new entry in DB
            EngageFeedDetails data = new EngageFeedDetails();
//
            data.setEngageId(details.getPostId());
            data.setChannel(SocialChannel.FACEBOOK.getName());
            data.setText(details.getText());
            data.setPageId(details.getPageId());
            data.setFeedId(details.getFeedId());
            data.setAuthorId(details.getAuthorId());
            data.setParentId(details.getEventParentId());
            data.setType(details.getType());
            data.setSubType(details.getSubType());
            data.setFeedUrl(details.getPostUrl());
            data.setAuthorName(details.getAuthorName());
            data.setAuthorUrl(details.getReviewerUrl());
            data.setFeedDate(details.getFeedDate());
            data.setImageUrls(Objects.nonNull(details.getImageUrls()) ? details.getImageUrls().toString() : null);
            return engageFeedDetailsRepo.save(data).getId();
        }
        return null;
    }


    private boolean isValidChangeEvent(String fbPageId, Changes changes) {
        if (StringUtils.isNotEmpty(fbPageId) && changes != null && changes.getValue() != null) {
            Value value = changes.getValue();
            // For current "edit" event, we check if there exists an old 'add' event with same 'facebookReviewId' such that
            // 'facebookReviewerId' for that old event is not equal to current 'facebookReviewerId'
            if (StringUtils.isNotEmpty(value.getVerb()) && value.getVerb().equalsIgnoreCase("edit")) {
                String fbReviewId = value.getOpen_graph_story_id();
                String fbReviewerId = value.getReviewer_id();
                String fbReviewerName = value.getReviewer_name();
                if (StringUtils.isNotEmpty(fbReviewId) && StringUtils.isNotEmpty(fbReviewerId)) {
                    // Fetch the "add" event of current reviewId
                    return checkReviewerDetails(fbPageId,fbReviewId, fbReviewerId, fbReviewerName);
                } else {
                    LOG.warn("isValidChangeEvent: fbReviewId {} fbReviewerId {}", fbReviewId, fbReviewerId);
                    return false;
                }
            }
        } else {
            LOG.warn("isValidChangeEvent: fbPageId {} changes {}", fbPageId, changes);
            return false;
        }
        return true;
    }

    private boolean checkReviewerDetails(String fbPageId,
                                         String fbReviewId, String fbReviewerId, String fbReviewerName ) {
        FBReviewNotificationInfo fbReviewNotificationInfo = fbReviewNotificationInfoService.findFirstByFacebookReviewIdAndNotificationTypeAndStatus(
                fbReviewId, "add", "SUCCESS");
        if (fbReviewNotificationInfo == null) {
            try {
                // Call Facebook graph API to get reviewer id of the original reviewer who posted the review
                String actualReviewerId = fbReviewService.getFbReviewerId(fbPageId, fbReviewId);
                if (StringUtils.isNotEmpty(actualReviewerId)) {
                    if (!actualReviewerId.equals(fbReviewerId)) {
                        LOG.warn("isValidChangeEvent: Received facebook edit event with different reviewerId. Ignoring event.");
                        LOG.warn("isValidChangeEvent: Original reviewerId is {} and EDIT event reviewerId is {}",
                                actualReviewerId, fbReviewerId);
                        return false;
                    }
                } else if (StringUtils.isNotEmpty(fbReviewerName)) {
                    // Check if Facebook Page name matches with fbReviewerName
                    BusinessFBPage fbPage = socialFbPageRepo.findFirstByFacebookPageId(fbPageId);
                    if (fbPage != null && fbReviewerName.equalsIgnoreCase(fbPage.getFacebookPageName())) {
                        LOG.warn("isValidChangeEvent: Reviewer name is same as Page name {}. Ignoring event.",
                                fbPage.getFacebookPageName());
                        return false;
                    }
                }
            } catch (Exception exp) {
                LOG.warn("Exception in isValidChangeEvent : {}", exp.getMessage());
            }
        } else if (StringUtils.isNotEmpty(fbReviewNotificationInfo.getFbReviewerId())
                && !fbReviewNotificationInfo.getFbReviewerId().equalsIgnoreCase(fbReviewerId)) {
            LOG.warn("isValidChangeEvent: Received facebook edit event with different reviewerId. Ignoring event.");
            LOG.warn("isValidChangeEvent: Original reviewerId is {} and EDIT event reviewerId is {}",
                    fbReviewNotificationInfo.getFbReviewerId(), fbReviewerId);
            return false;
        }
        return false;
    }

    private Map<String, Object> getOverlayDetailFromFacebook(Long pageId) throws IOException {
        LOG.info("fetching overlay details from facebook for pageId {}",pageId);
        String accessToken = fbReviewService.getAccessToken(pageId.toString());
        FacebookOverallRatingRequest request = new FacebookOverallRatingRequest(pageId.toString(), accessToken);
        return rawReviewService.getOverallRatingAndReviewCount(request);
    }

    private String getReviewerUrlFromFacebook(String pageId , String userId) throws IOException {
        FbMessengerUserDetailsRequest fbMessengerUserDetailsRequest = new FbMessengerUserDetailsRequest();
        fbMessengerUserDetailsRequest.setPageId(pageId);
        fbMessengerUserDetailsRequest.setUserId(userId);
        String accessToken = fbReviewService.getAccessToken(pageId);
        LOG.info("fetching profile pic for..pageid {}",pageId);
        String url = facebookService.getUserProfilePicture(accessToken,userId);
        LOG.info("fetching profile pic for..pageid {} is {}",pageId,url);
        return url;
    }

    public void handleDataDeletionCallback(FacebookDataDeletionPayload request) {
        String error;
        try {
            String signedRequest = request.getSignedRequest();
            // Step 1: Split the signed request
            String[] split = signedRequest.split("\\.");
            if (split.length != 2) {
                error = createErrorResponse("Invalid signed request format");
                LOG.error(error);
            }

            String encodedSig = split[0];
            String payload = split[1];

            // Step 2: Decode the payload
            String decodedPayload = new String(Base64.getUrlDecoder().decode(payload));
            LOG.info("[handleDataDeletionCallback] Decoded Payload: {}", decodedPayload);

            // Step 3: Validate the signature
            SocialAppCredsInfo socialAppFbCreds = socialAppService.getFacebookAppSettings();
            if (!validateSignature(encodedSig, payload, socialAppFbCreds.getChannelClientSecret())) {
                error = createErrorResponse("Invalid signature");
                LOG.error(error);
            }

            // Step 4: Extract user ID from the payload
            String userId = extractUserId(decodedPayload);
            if (userId == null) {
                error = createErrorResponse("User ID not found in payload");
                LOG.error(error);
            }

            // Step 5: Perform data deletion
            boolean deletionSuccess = deleteFbOrIgUserData(userId, request.getConfirmationCode());
            if (!deletionSuccess) {
                error = createErrorResponse("Failed to delete user data");
                LOG.error(error);
            }
        } catch (Exception e) {
            error = createErrorResponse("An error occurred: " + e.getMessage());
            LOG.error(error);
        }
    }

    private boolean validateSignature(String encodedSig, String payload, String appSecret) throws NoSuchAlgorithmException, InvalidKeyException {
        byte[] sig = Base64.getUrlDecoder().decode(encodedSig);
        Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKey = new SecretKeySpec(appSecret.getBytes(), "HmacSHA256");
        sha256_HMAC.init(secretKey);
        byte[] expectedSig = sha256_HMAC.doFinal(payload.getBytes());
        return java.security.MessageDigest.isEqual(sig, expectedSig);
    }

    private String extractUserId(String decodedPayload) {
        try {
            FacebookPayload payload = objectMapper.readValue(decodedPayload, FacebookPayload.class);
            return payload.getUser_id();
        } catch (Exception e) {
            LOG.error("Error occurred while extracting the user id: {}", e.getMessage());
            return null;
        }
    }

    private boolean deleteFbOrIgUserData(String userId, String confirmationCode) {
        LOG.info("Deleting data for user: {} with confirmationCode: {}", userId, confirmationCode);

        boolean isDeleted = false;

        List<BusinessFBPage> businessFBPages = socialFbPageRepo.findByUserId(userId);
        if (!CollectionUtils.isEmpty(businessFBPages)) {
            // if token is invalid delete pages
            if (!fbSocialAccountService.isTokenValid(businessFBPages.get(0).getPageAccessToken())) {
                fbSocialAccountService.processDeletePageEvent(SocialChannel.FACEBOOK.getName(), businessFBPages);
                storeAuditData(SocialChannel.FACEBOOK.getId(), userId, businessFBPages.stream()
                        .map(BusinessFBPage::getFacebookPageId).collect(Collectors.toList()), confirmationCode);
                isDeleted = true;
            }
        }

        List<BusinessInstagramAccount> businessInstagramAccounts = instagramAccountRepo.findByUserId(userId);
        if (!CollectionUtils.isEmpty(businessInstagramAccounts)) {
            if (!fbSocialAccountService.isTokenValid(businessInstagramAccounts.get(0).getPageAccessToken())) {
                socialInstagramService.processDeletePageEvent(SocialChannel.INSTAGRAM.getName(), businessInstagramAccounts);
                storeAuditData(SocialChannel.INSTAGRAM.getId(), userId, businessInstagramAccounts.stream()
                        .map(BusinessInstagramAccount::getFacebookPageId).collect(Collectors.toList()), confirmationCode);
                isDeleted = true;
            }
        }

        return isDeleted;
    }

    private void storeAuditData(Integer sourceId, String userId, List<String> pageIds, String confirmationCode) {
        LOG.info("create audit entry for userId: {}, sourceId: {}", userId, sourceId);
        SocialDataDeletionAudit audit = new SocialDataDeletionAudit();
        audit.setSourceId(sourceId);
        audit.setUserId(userId);
        audit.setPageIds(pageIds);
        audit.setConfirmationCode(confirmationCode);
        socialDataDeletionAuditRepository.save(audit);
    }

    private String createErrorResponse(String errorMessage) {
        try {
            FacebookErrorResponse errorResponse = new FacebookErrorResponse();
            errorResponse.setMessage(errorMessage);
            return objectMapper.writeValueAsString(errorResponse);
        } catch (Exception e) {
            return "{\"error\":\"Failed to create error response\"}";
        }
    }
}
