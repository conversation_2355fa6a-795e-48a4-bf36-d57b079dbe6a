package com.birdeye.social.service.SocialCompetitorService;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.KafkaTopicEnum;
import com.birdeye.social.constant.MediaAssetEntityType;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.competitor.CompetitorProfileInfoRepo;
import com.birdeye.social.entities.competitor.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.request.media.PicturesqueMediaUploadRequest;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.external.service.PicturesqueGen;
import com.birdeye.social.insights.ES.Request.ESPageRequest;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.insights.constants.InsightsConstants;
import com.birdeye.social.model.*;
import com.birdeye.social.model.competitorProfile.*;
import com.birdeye.social.service.CacheService;
import com.birdeye.social.service.EsService;
import com.birdeye.social.utils.DateTimeUtils;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.*;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SocialCompetitorHelperService {

    @Autowired
    private CacheService cacheService;

    @Autowired
    private PicturesqueGen picturesqueService;

    @Autowired
    KafkaProducerService kafkaProducerService;

    @Autowired
    private CompetitorProfileInfoRepo competitorProfileInfoRepo;

    @Autowired
    private EsService esService;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private CompetitorFactory competitorFactory;

    public static final String COMPETITOR_CACHE_UPDATE_TOPIC = "competitor-cache-update-event";

    private static final String COMPETITOR_CACHE = "competitor_cache";

    private static final String REPORT_DATE = "reportDate";

    public static final String FOLLOWER_COUNTS = "followerCounts";

    public static final String FOLLOWER_COUNT = "followerCount";

    public static final String NET_FOLLOWER_COUNT = "netFollowerCount";

    public static final String NET_FOLLOWER_COUNTS = "netFollowerCounts";

    public static final String BY_DATE = "by_date";

    public static final String BY_PAGE_ID = "by_pageId";

    public static final String NET_FOLLOWER_GAIN_COUNTS = "netFollowerGainCounts";

    public static final String NET_FOLLOWER_LOST_COUNTS = "netFollowerLostCounts";

    public static final String FOLLOWER_GAIN = "follower_gain";

    public static final String FOLLOWER_LOST = "follower_lost";
    
    public static final String TOTAL_FOLLOWER_COUNT = "total_follower_count";

    public static final String IMAGE_URL_COUNT = "imageUrlCount";

    public static final String VIDEO_URL_COUNT = "videoUrlCount";

    public static final String LINK_COUNT = "linkCount";

    public static final String POST_COUNT = "postCount";

    public static final String LIKE_COUNT = "likeCount";

    public static final String SHARE_COUNT = "shareCount";

    public static final String COMMENT_COUNT = "commentCount";

    public static final String TEXT_POST_COUNT = "textPostCount";

    public static final String EMPTY_AGGREGATIONS_LOG = "Aggregations are empty";
    
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static final String START_DATE_FOLLOWER_DATA = "startDateFollowerData";

    public static final String END_DATE_FOLLOWER_DATA = "endDateFollowerData";

    public static final Integer MAX_SELF_PAGE_LIMIT = 10000;

    private static final Logger LOG = LoggerFactory.getLogger(SocialCompetitorHelperService.class);

    public void clearCache(SocialChannel channel, Long businessNumber) {
        cacheService.clearCacheByKey(COMPETITOR_CACHE,channel.getName()+"_"+businessNumber);
    }

    public void updateFacebookCache(SocialChannel channel, Long businessNumber, List<FacebookCompetitorInfo> facebookCompetitorInfos) {
        cacheService.putInCache(COMPETITOR_CACHE, channel.getName()+"_"+businessNumber, new ArrayList<>(facebookCompetitorInfos));
    }

    public void updateInstagramCache(SocialChannel channel, Long businessNumber, List<InstagramCompetitorInfo> instagramCompetitorInfos) {
        cacheService.putInCache(COMPETITOR_CACHE, channel.getName()+"_"+businessNumber, new ArrayList<>(instagramCompetitorInfos));
    }

    public void updateTwitterCache(SocialChannel channel, Long businessNumber, List<TwitterCompetitorInfo> twitterCompetitorInfos) {
        cacheService.putInCache(COMPETITOR_CACHE, channel.getName()+"_"+businessNumber, new ArrayList<>(twitterCompetitorInfos));
    }

    public void sendUpdateCacheEvent(SocialChannel channel, Long businessNumber) {
        UpdateCompetitorCacheEvent updateCompetitorCacheEvent = new UpdateCompetitorCacheEvent(channel, businessNumber);
        kafkaProducerService.sendObjectV1(COMPETITOR_CACHE_UPDATE_TOPIC, updateCompetitorCacheEvent);
    }

    public Object getCompetitorListFromCache(SocialChannel channel, Long businessNumber) {
        return cacheService.get(COMPETITOR_CACHE, channel.getName()+"_"+businessNumber);
    }

    public List<String> splitMediaUrls(String mediaUrls) {
        List<String> mediaUrlList = new ArrayList<>();
        if(StringUtils.isEmpty(mediaUrls)) {
            return mediaUrlList;
        }

        try {
            String[] mediaUrlArray = mediaUrls.split(",");
            if(Objects.isNull(mediaUrlArray)) {
                return mediaUrlList;
            }
            mediaUrlList = Arrays.asList(mediaUrlArray);
            return mediaUrlList;
        } catch(Exception e) {
            return mediaUrlList;
        }
    }

    public Integer calculateEngagement(Integer likeCount, Integer commentCount, Integer shareCount) {
        Integer engagement = 0;
        engagement += Objects.isNull(likeCount)?0:likeCount;
        engagement += Objects.isNull(commentCount)?0:commentCount;
        engagement += Objects.isNull(shareCount)?0:shareCount;

        return engagement;
    }

    public CompetitorPostData convertEsResponseToPostDataResponse(CompetitorPostsEsDocument competitorPostsEsDocument, Map<SocialChannel, Map<String, CompetitorBasicDetail>> channelAndPageWiseMap) {
        SocialChannel channel = SocialChannel.getSocialChannelById(competitorPostsEsDocument.getSourceId());
        CompetitorBasicDetail competitorBasicDetail = null;
        if(MapUtils.isNotEmpty(channelAndPageWiseMap) && channelAndPageWiseMap.containsKey(channel)) {
            Map<String, CompetitorBasicDetail> pageWiseMap = channelAndPageWiseMap.get(channel);
            if(MapUtils.isNotEmpty(pageWiseMap) && pageWiseMap.containsKey(competitorPostsEsDocument.getPageId())) {
                competitorBasicDetail = pageWiseMap.get(competitorPostsEsDocument.getPageId());
            }
        }

        CompetitorPostData competitorPostData = CompetitorPostData.builder()
                .postId(competitorPostsEsDocument.getPostId())
                .pageId(competitorPostsEsDocument.getPageId())
                .postUrl(competitorPostsEsDocument.getPostUrl())
                .postText(competitorPostsEsDocument.getPostText())
                .imageUrls(competitorPostsEsDocument.getImageUrls())
                .videoUrls(competitorPostsEsDocument.getVideoUrls())
                .thumbnailUrls(competitorPostsEsDocument.getThumbnailUrls())
                .likeCount(competitorPostsEsDocument.getLikeCount())
                .commentCount(competitorPostsEsDocument.getCommentCount())
                .shareCount(competitorPostsEsDocument.getShareCount())
                .engagement(competitorPostsEsDocument.getEngagement())
                .channel(SocialChannel.getSocialChannelNameById(competitorPostsEsDocument.getSourceId()))
                .publishedDate(Objects.isNull(competitorPostsEsDocument.getPublishedDate())
                        ?null
                        :competitorPostsEsDocument.getPublishedDate().getTime())
                .name(Objects.isNull(competitorBasicDetail)?null:competitorBasicDetail.getName())
                .userName(Objects.isNull(competitorBasicDetail)?null:competitorBasicDetail.getUserName())
                .profilePictureUrl(Objects.isNull(competitorBasicDetail)?null:competitorBasicDetail.getProfilePictureUrl())
                .imageMetaData(competitorPostsEsDocument.getImageMediaData())
                .videoMetaData(competitorPostsEsDocument.getVideoMediaData())
                .thumbnailMetaData(competitorPostsEsDocument.getThumbnailMediaData())
                .isVerified(Objects.isNull(competitorBasicDetail)?null:competitorBasicDetail.getIsVerified())
                .build();

        return competitorPostData;
    }

    public String getCompetitorPostMetaData(CompetitorPosts competitorPosts) {
        CompetitorPostMetaData competitorPostMetaData = new CompetitorPostMetaData();
        List<String> imageUrls = competitorPosts.getImageUrls();
        if(CollectionUtils.isNotEmpty(imageUrls)) {
            List<MediaData> imageMetaData = new ArrayList<>();
            for(String imageUrl: imageUrls) {
                imageMetaData.add(new MediaData(imageUrl, null));
            }
            competitorPostMetaData.setImageMetaData(imageMetaData);
        }
        List<String> videoUrls = competitorPosts.getVideoUrls();
        if(CollectionUtils.isNotEmpty(videoUrls)) {
            List<MediaData> videoMetaData = new ArrayList<>();
            for(String videoUrl: videoUrls) {
                videoMetaData.add(new MediaData(videoUrl, null));
            }
            competitorPostMetaData.setVideoMetaData(videoMetaData);
        }

        List<String> thumbnailUrls = competitorPosts.getThumbnailUrls();
        if(CollectionUtils.isNotEmpty(thumbnailUrls)) {
            List<MediaData> thumbnailMetaData = new ArrayList<>();
            for(String thumbnailUrl: thumbnailUrls) {
                thumbnailMetaData.add(new MediaData(thumbnailUrl, null));
            }
            competitorPostMetaData.setThumbnailMetaData(thumbnailMetaData);
        }

        try {
            return JSONUtils.toJSON(competitorPostMetaData);
        } catch (Exception e) {
            LOG.info("error occuered while converting comp metadata, error: {}", e.getMessage());
            return null;
        }
    }

    public void picturesqueService(String url, String id, Integer callbackId, String callbackUrl) {
        try {
            PicturesqueMediaUploadRequest req = new PicturesqueMediaUploadRequest();
            req.setImgPath(url);
            req.setBusinessNumber(id);
            req.setSource("other");
            req.setMimeType("");
            req.setReqMetaData(true);
            req.setCallbackUrl(callbackUrl);
            req.setCallbackId(callbackId);
            picturesqueService.uploadMediaWithMetaDataInAsync(req);
        } catch (Exception ex) {
            LOG.info("Something went wrong while uploading media to service for url {} with error {}", url, ex.getMessage());
            return;
        }
    }

    public void sendEsSyncEvent(CompetitorPosts competitorPosts) {
        if(Objects.isNull(competitorPosts)) return;
        SyncCompetitorPostEsRequest syncCompetitorPostEsRequest = new SyncCompetitorPostEsRequest(competitorPosts.getId());
        kafkaProducerService.sendObjectWithKeyV1(Integer.toString(competitorPosts.getId()),KafkaTopicEnum.COMP_POSTS_ES_SYNC.getName(), syncCompetitorPostEsRequest);
    }

    public void sendEventToCallPicturesQueue(Integer id, MediaAssetEntityType mediaAssetEntityType, SocialChannel channel, Long enterpriseId) {
        PicturesqueCompRequest picturesqueCompRequest = PicturesqueCompRequest.builder()
                .id(id)
                .mediaAssetEntityType(mediaAssetEntityType)
                .channel(channel)
                .businessNumber(enterpriseId)
                .build();
        kafkaProducerService.sendObjectWithKeyV1(Integer.toString(id), KafkaTopicEnum.COMP_CALL_PICTURESQUE.getName(), picturesqueCompRequest);
    }

            /*
                    {
                      "size": 1,
                      "query": {
                        "term": {
                          "rawCompetitorId": {
                            "value": 123
                          }
                        }
                      },
                      "sort": [
                        {
                          "reportDate": {
                            "order": "desc"
                          }
                        }
                      ]
                    }
         */
    public Optional<Integer> getLastFollowerCountForCompetitorProfile(Integer rawCompetitorId, Integer sourceId){
        try {
            String esIndex = getCompetitorProfileEsIndex(sourceId);

            SearchRequest searchRequest = new SearchRequest(esIndex);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            TermQueryBuilder termQueryBuilder = new TermQueryBuilder("rawCompetitorId", rawCompetitorId);

            searchSourceBuilder.query(termQueryBuilder);
            searchSourceBuilder.size(1);
            searchSourceBuilder.sort(REPORT_DATE, SortOrder.DESC);
            searchRequest.source(searchSourceBuilder);

            SearchResponse response = esService.search(searchRequest);
            SearchHit[] searchHits = response.getHits().getHits();
            if(searchHits.length > 0) {
                for(SearchHit hit : searchHits) {
                    CompetitorProfileInsightsES profileInsightsES = JSONUtils.fromJSON(hit.getSourceAsString(), CompetitorProfileInsightsES.class);
                     return Optional.of(Objects.nonNull(profileInsightsES) ? profileInsightsES.getFollowerCount() : 0);
                }
            }
        } catch (Exception ex) {
            LOG.info("error occurred while fetching comp profile data, error: {}", ex.getMessage());
        }
        return Optional.empty();
    }

    public void sendEsProfileSyncEvent(CompetitorProfileInsightsES competitorProfileInsightsES) {
        if(Objects.isNull(competitorProfileInsightsES)) return;
        kafkaProducerService.sendObjectWithKeyV1(Integer.toString(competitorProfileInsightsES.getRawCompetitorId()),
                KafkaTopicEnum.COMP_PROFILE_ES_SYNC.getName(), competitorProfileInsightsES);
    }
    public CompetitorProfileInsightsES saveCompetitorInsightInDB(Integer currentFollowerCount, CompetitorRequestDTO competitorRequestDTO) {
        // check from ES if previous data is present. if present, calculate delta i.e, follower count in ES - follower count in API = net follower count.
        Integer netFollowerGrowth = 0;
        Optional<Integer> lastFollowerCountOptional = getLastFollowerCountForCompetitorProfile(competitorRequestDTO.getRawId(), competitorRequestDTO.getSourceId());
        Integer lastFollowerCount = lastFollowerCountOptional.orElse(null);
        LOG.info("lastFollowerCount: {}", lastFollowerCount);
        if(Objects.nonNull(lastFollowerCount)) {
            netFollowerGrowth = lastFollowerCount - currentFollowerCount;
        } else {
            netFollowerGrowth = currentFollowerCount;
        }

        CompetitorProfileInsightsES insightsES = CompetitorProfileInsightsES.builder()
                .netFollowerCount(netFollowerGrowth)
                .followerCount(currentFollowerCount)
                .build();

        CompetitorProfileInfo competitorProfileInfo = CompetitorProfileInfo
                .builder()
                .pageId(competitorRequestDTO.getPageId())
                .sourceId(competitorRequestDTO.getSourceId())
                .rawCompetitorId(competitorRequestDTO.getRawId())
                .insightData(JSONUtils.toJSON(insightsES))
                .build();

        Integer id =  competitorProfileInfoRepo.save(competitorProfileInfo).getId();


        insightsES.setRawCompetitorId(competitorRequestDTO.getRawId());
        insightsES.setPageId(competitorRequestDTO.getPageId());
        insightsES.setReportDate(DateTimeUtils.localToESFormat(new Date()));
        insightsES.setSourceId(competitorRequestDTO.getSourceId());
        insightsES.setAuditId(id);

        return insightsES;
    }


    // return false when same day record not present, else false
    public boolean checkDuplicateRequestForProfileFetch(CompetitorRequestDTO competitorRequestDTO) {
        CompetitorProfileInfo competitorProfileInfo =
                competitorProfileInfoRepo.findFirstByPageIdAndSourceIdOrderByIdDesc(competitorRequestDTO.getPageId(), competitorRequestDTO.getSourceId());

        if(Objects.isNull(competitorProfileInfo)) {
            return false;
        }

        return DateTimeUtils.isSameDay(competitorProfileInfo.getCreatedAt(), new Date());
    }


    public void auditProfileRequest(CompetitorRequestDTO competitorRequestDTO, String action, String errorMessage) {
        try {
            CompetitorProfileInfo competitorProfileInfo = competitorProfileInfoRepo
                    .findFirstByPageIdAndSourceIdOrderByIdDesc(competitorRequestDTO.getPageId(), competitorRequestDTO.getSourceId());

            // save audit if not present, else update audit
            if(Objects.isNull(competitorProfileInfo)) {
                CompetitorProfileInfo auditRequest = CompetitorProfileInfo
                        .builder()
                        .pageId(competitorRequestDTO.getPageId())
                        .sourceId(competitorRequestDTO.getSourceId())
                        .rawCompetitorId(competitorRequestDTO.getRawId())
                        .action(action)
                        .build();

                competitorProfileInfoRepo.save(auditRequest);
                return;
            }

            String prevAction = competitorProfileInfo.getAction();

            // save audit action
            competitorProfileInfo.setAction( StringUtils.isEmpty(prevAction) ? action : ( prevAction + " | " + action));

            if(StringUtils.isNotEmpty(errorMessage)) {
                competitorProfileInfo.setErrorLog(errorMessage);
            }
            competitorProfileInfoRepo.save(competitorProfileInfo);
        } catch (Exception ex) {
            LOG.info("Error occurred while saving audit for request {} with error", competitorRequestDTO, ex );
        }
    }

    @Nullable
    public static List<String> getCompetitorProfileEsIndex(List<String> channels) {
        List<String> indexName = new ArrayList<>();

        for(String channel: channels ) {
            if(Objects.equals(SocialChannel.FACEBOOK.getName(), channel)) {
                indexName.add(ElasticConstants.COMPETITOR_FB_PROFILE_INSIGHTS.getName());
            } else if(Objects.equals(SocialChannel.INSTAGRAM.getName(), channel)) {
                indexName.add(ElasticConstants.COMPETITOR_INSTA_PROFILE_INSIGHTS.getName());
            }  else if(Objects.equals(SocialChannel.TWITTER.getName(), channel)) {
                indexName.add(ElasticConstants.COMPETITOR_TWITTER_PROFILE_INSIGHTS.getName());
            }
        }

        return indexName;
    }

    @Nullable
    public static List<String> getSelfProfileEsIndices(List<String> channels) {
        List<String> indexName = new ArrayList<>();

        for(String channel: channels ) {
            if (Objects.equals(SocialChannel.FACEBOOK.getName(), channel)) {
                indexName.add(ElasticConstants.FACEBOOK_PAGE_INSIGHTS.getName());
            } else if (Objects.equals(SocialChannel.INSTAGRAM.getName(), channel)) {
                indexName.add(ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName());
            } else if (Objects.equals(SocialChannel.TWITTER.getName(), channel)) {
                indexName.add(ElasticConstants.TWITTER_PAGE_INSIGHTS.getName());
            }
        }
        return indexName;
    }

    @Nullable
    public static String getCompetitorProfileEsIndex(Integer sourceId) {
        String indexName = null;

        if(SocialChannel.FACEBOOK.getId() == sourceId) {
            indexName = ElasticConstants.COMPETITOR_FB_PROFILE_INSIGHTS.getName();
        } else if(SocialChannel.INSTAGRAM.getId() == sourceId) {
            indexName = ElasticConstants.COMPETITOR_INSTA_PROFILE_INSIGHTS.getName();
        }  else if(SocialChannel.TWITTER.getId() == sourceId) {
            indexName = ElasticConstants.COMPETITOR_TWITTER_PROFILE_INSIGHTS.getName();
        }
        return indexName;
    }

    @Nullable
    public static String getSelfProfileEsIndex(Integer sourceId) {
        String indexName = null;

        if(SocialChannel.FACEBOOK.getId() == sourceId) {
            indexName = ElasticConstants.FACEBOOK_PAGE_INSIGHTS.getName();
        } else if(SocialChannel.INSTAGRAM.getId() == sourceId) {
            indexName = ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName();
        }  else if(SocialChannel.TWITTER.getId() == sourceId) {
            indexName = ElasticConstants.TWITTER_PAGE_INSIGHTS.getName();
        }
        return indexName;
    }

    public List<String> getCompetitorProfileSourceName(ChannelWisePage channelWisePage) {
        List<String> channels = new ArrayList<>();

        if(Objects.isNull(channelWisePage)) return channels;

        if(Objects.nonNull(channelWisePage.getFacebook())) {
            channels.add(SocialChannel.FACEBOOK.getName());
        }
        if(Objects.nonNull(channelWisePage.getInstagram())) {
            channels.add(SocialChannel.INSTAGRAM.getName());
        }
        if(Objects.nonNull(channelWisePage.getTwitter())) {
            channels.add(SocialChannel.TWITTER.getName());
        }
        return channels;
    }

    public SearchResponse getCompetitorPostDataES(Date startDate, Date endDate, List<String> pageIds, List<String> indices) {
        try {
            SearchRequest searchRequest = new SearchRequest(ElasticConstants.COMPETITOR_POSTS.getName());
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            // Building the query
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termsQuery("pageId.keyword", pageIds))
                    .filter(QueryBuilders.rangeQuery("publishedDate")
                            .from(DateTimeUtils.localToESFormat(startDate))
                            .to(DateTimeUtils.localToESFormat(endDate))
                            .includeLower(true)
                            .includeUpper(true));

            // Aggregation builders
            DateHistogramAggregationBuilder dateHistogram = AggregationBuilders.dateHistogram("d_histogram")
                    .field("publishedDate")
                    .dateHistogramInterval(DateHistogramInterval.DAY)
                    .format("yyyy-MM-dd")
                    .subAggregation(AggregationBuilders.count(POST_COUNT).field("_index"))
                    .subAggregation(AggregationBuilders.sum(LIKE_COUNT).field("likeCount"))
                    .subAggregation(AggregationBuilders.sum(COMMENT_COUNT).field("commentCount"))
                    .subAggregation(AggregationBuilders.sum(SHARE_COUNT).field("shareCount"))
                    .subAggregation(AggregationBuilders.sum(IMAGE_URL_COUNT).field("image_url_count"))
                    .subAggregation(AggregationBuilders.sum(VIDEO_URL_COUNT).field("video_url_count"))
                    .subAggregation(AggregationBuilders.sum(LINK_COUNT).field("link_count"))
                    .subAggregation(AggregationBuilders.sum(TEXT_POST_COUNT).field("post_text_count"))
                    .subAggregation(AggregationBuilders.max("sourceId").field("sourceId"));;

            TermsAggregationBuilder termsAggregation = AggregationBuilders.terms("by_pageId")
                    .field("pageId.keyword")
                    .size(MAX_SELF_PAGE_LIMIT)
                    .order(BucketOrder.count(false)) // Descending order by count
                    .order(BucketOrder.key(true))   // Ascending order by key
                    .subAggregation(dateHistogram);

            // Construct the search source
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                    .query(boolQuery)
                    .size(0)
                    .aggregation(termsAggregation);

            // Create the search request
            searchRequest.source(sourceBuilder);

            // Execute the search
            return esService.search(searchRequest);

        } catch (Exception ex) {
            LOG.info("Exception occurred at fetching profile ES with error", ex);
            return null;
        }
    }

    public SearchResponse getSelfPostInsightDataES(Date startDate, Date endDate, List<String> pageIds) {
        try {
            SearchRequest searchRequest = new SearchRequest(ElasticConstants.POST_INSIGHTS.getName());
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            // Building the query
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termsQuery("page_id", pageIds))
                    .filter(QueryBuilders.rangeQuery("posted_date")
                            .from(DateTimeUtils.localToESFormat(startDate))
                            .to(DateTimeUtils.localToESFormat(endDate))
                            .includeLower(true)
                            .includeUpper(true));

            // Aggregation builders
            DateHistogramAggregationBuilder dateHistogram = AggregationBuilders.dateHistogram("d_histogram")
                    .field("posted_date")
                    .dateHistogramInterval(DateHistogramInterval.DAY)
                    .format("yyyy-MM-dd")
                    .subAggregation(AggregationBuilders.count(POST_COUNT).field("_index"))
                    .subAggregation(AggregationBuilders.sum(LIKE_COUNT).field("like_count"))
                    .subAggregation(AggregationBuilders.sum(COMMENT_COUNT).field("comment_count"))
                    .subAggregation(AggregationBuilders.sum(SHARE_COUNT).field("share_count"))
                    .subAggregation(AggregationBuilders.sum(IMAGE_URL_COUNT).field("image_url_count"))
                    .subAggregation(AggregationBuilders.sum(VIDEO_URL_COUNT).field("video_url_count"))
                    .subAggregation(AggregationBuilders.sum(LINK_COUNT).field("link_count"))
                    .subAggregation(AggregationBuilders.sum(TEXT_POST_COUNT).field("post_text_count"))
                    .subAggregation(AggregationBuilders.max("sourceId").field("source_id"));;

            TermsAggregationBuilder termsAggregation = AggregationBuilders.terms("by_pageId")
                    .field("page_id")
                    .size(MAX_SELF_PAGE_LIMIT)
                    .order(BucketOrder.count(false)) // Descending order by count
                    .order(BucketOrder.key(true))   // Ascending order by key
                    .subAggregation(dateHistogram);

            // Construct the search source
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                    .query(boolQuery)
                    .size(0)
                    .aggregation(termsAggregation);

            // Create the search request
            searchRequest.source(sourceBuilder);

            // Execute the search
            return esService.search(searchRequest);

        } catch (Exception ex) {
            LOG.info("Exception occurred at fetching profile ES with error", ex);
            return null;
        }
    }


    public List<CompetitorExcelESData> getCompetitorsProfileDataES(Date startDate, Date endDate, List<String> pageIds, List<String> indices, Integer pageSize, Integer startIndex) {
        List<CompetitorExcelESData> response = new ArrayList<>();
        try {
            SearchRequest searchRequest = new SearchRequest(indices.stream()
                    .collect(Collectors.joining(",")));
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            // Building the bool query
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termsQuery("pageId", pageIds))
                    .filter(QueryBuilders.rangeQuery("reportDate")
                            .from(DateTimeUtils.localToESFormat(startDate))
                            .to(DateTimeUtils.localToESFormat(endDate))
                            .includeLower(true)
                            .includeUpper(true)
                            .format(YYYY_MM_DD_HH_MM_SS));

            // Building the search source
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.sort("reportDate", SortOrder.DESC);
            searchSourceBuilder.from(startIndex * pageSize); // Offset for pagination
            searchSourceBuilder.size(pageSize); // Number of results per page

            // Setting the search source to the search request
            searchRequest.source(searchSourceBuilder);

            SearchResponse searchResponse = esService.search(searchRequest);

            SearchHit[] searchHits = searchResponse.getHits().getHits();
            if(searchHits.length > 0) {
                String lastRecordStr = searchHits[searchHits.length - 1].getSourceAsString();

                CompetitorProfileInsightsES lastRecode = JSONUtils.fromJSON(lastRecordStr, CompetitorProfileInsightsES.class);

                String lastRecordDate = lastRecode.getReportDate();

                for(SearchHit hit : searchHits) {
                    CompetitorProfileInsightsES postInsightsES = JSONUtils.fromJSON(hit.getSourceAsString(), CompetitorProfileInsightsES.class);
                    if(Objects.isNull(postInsightsES)) {
                        LOG.info("Post insights ES data is null for hit: {}", hit.getId());
                        continue;
                    }

                    int followerCount = postInsightsES.getFollowerCount();
                    int netGrowth = postInsightsES.getNetFollowerCount();

                    if(lastRecordDate.equals(postInsightsES.getReportDate())) {
                        netGrowth = 0;
                    }

                    double netFollowerGrowth = followerCount == 0 ? 0.0 : ((double) netGrowth /followerCount) * 100;


                    CompetitorExcelESData excelESData = CompetitorExcelESData.builder()
                            .date(postInsightsES.getReportDate())
                            .followers(followerCount)
                            .netFollowersGrowth(netGrowth)
                            .followersGrowthPercentage(String.valueOf(roundToTwoDecimalPlaces(netFollowerGrowth)).concat("%"))
                            .profile(postInsightsES.getPageId())
                            .network(SocialChannel.getSocialChannelNameById(postInsightsES.getSourceId()))
                            .build();
                    response.add(excelESData);
                }
            }

        } catch (Exception ex) {
            LOG.info("Exception occurred at fetching profile ES with error", ex);
        }
        return response;
    }

    public List<CompetitorExcelESData> getSelfProfileDataES(Date startDate, Date endDate, List<String> pageIds, List<String> indices, Integer pageSize, Integer startIndex) {
        List<CompetitorExcelESData> response = new ArrayList<>();
        try {
            SearchRequest searchRequest = new SearchRequest(indices.stream()
                    .collect(Collectors.joining(",")));
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            // Building the bool query
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termsQuery("page_id", pageIds))
                    .filter(QueryBuilders.rangeQuery("day")
                            .from(DateTimeUtils.localToESFormat(startDate))
                            .to(DateTimeUtils.localToESFormat(endDate))
                            .includeLower(true)
                            .includeUpper(true)
                            .format(YYYY_MM_DD_HH_MM_SS));

            // Building the search source
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.sort("day", SortOrder.DESC);
            searchSourceBuilder.from(startIndex * pageSize); // Offset for pagination
            searchSourceBuilder.size(pageSize); // Number of results per page

            // Setting the search source to the search request
            searchRequest.source(searchSourceBuilder);

            SearchResponse searchResponse = esService.search(searchRequest);

            SearchHit[] searchHits = searchResponse.getHits().getHits();
            if(searchHits.length > 0) {
                String lastRecordStr = searchHits[searchHits.length - 1].getSourceAsString();

                ESPageRequest lastRecode = JSONUtils.fromJSON(lastRecordStr, ESPageRequest.class);

                String lastRecordDate = lastRecode.getDay();


                for(SearchHit hit : searchHits) {
                    ESPageRequest postInsightsES = JSONUtils.fromJSON(hit.getSourceAsString(), ESPageRequest.class);

                    int followerCount = Objects.isNull(postInsightsES.getTotal_follower_count()) ? 0 : postInsightsES.getTotal_follower_count();
                    double netAudienceGrowth = getNetAudienceGrowth(postInsightsES, followerCount);

                    if(postInsightsES.getDay().equalsIgnoreCase(lastRecordDate)) {
                        netAudienceGrowth = 0;
                    }

                    int followerGain = Objects.isNull(postInsightsES.getFollower_gain()) ? 0 : postInsightsES.getFollower_gain();
                    int followerLost = Objects.isNull(postInsightsES.getFollower_lost()) ? 0 : postInsightsES.getFollower_lost();
                    int netGrowth = followerGain - followerLost;

                    CompetitorExcelESData excelESData = CompetitorExcelESData.builder()
                            .date(postInsightsES.getDay())
                            .followers(followerCount)
                            .netFollowersGrowth(netGrowth)
                            .followersGrowthPercentage(String.valueOf(roundToTwoDecimalPlaces(netAudienceGrowth)).concat("%"))
                            .profile(postInsightsES.getPage_id())
                            .build();

                    response.add(excelESData);
                }
            }

        } catch (Exception ex) {
            LOG.info("Exception occurred at fetching profile ES with error", ex);
        }
        return response;
    }

    public CompetitorProfileReportESData processCompetitorProfileDataChannelWise(CompetitorProfileReportESData competitorRequestDTO, Map<String, CompetitorPageDetails> pageIdVsPageName, String channel) {
        // Build competitor profile data
        CompetitorProfileReportESData competitorProfileData = CompetitorProfileReportESData.builder()
                .data(new HashMap<>())
                .build();



        pageIdVsPageName.forEach((pageId, pageDetail) -> {
            if(competitorRequestDTO.getData().containsKey(pageId)){
                String pageName = pageIdVsPageName.get(pageId).getPageName();
                competitorProfileData.getData().put(pageName, competitorRequestDTO.getData().get(pageId));
            } else if(channel.equals(pageDetail.getChannel())) {
                CompetitorProfileESData profileESData = CompetitorProfileESData.builder()
                        .followerCount(null)
                        .followerGrowthPercent(null)
                        .netFollowerCount(null)
                        .profilePictureUrl(pageIdVsPageName.get(pageId).getProfilePictureUrl())
                        .pageLink(pageIdVsPageName.get(pageId).getPageLink())
                        .build();
                competitorProfileData.getData().put(pageIdVsPageName.get(pageId).getPageName(), profileESData);
            }
        });
        competitorProfileData.getData().put("total", getAggregatedProfileDataForChannel(competitorProfileData));
        return competitorProfileData;
    }

    public CompetitorProfileESData getAggregatedProfileDataForChannel(CompetitorProfileReportESData competitorRequestDTO){
        Map<String, CompetitorProfileESData> dataMap = competitorRequestDTO.getData();
        boolean totalFollowerPresent = dataMap.values().stream()
                .map(CompetitorProfileESData::getFollowerCount)
                .allMatch(Objects::isNull);

        boolean totalNetFollowerPresent = dataMap.values().stream()
                .map(CompetitorProfileESData::getNetFollowerCount)
                .allMatch(Objects::isNull);


        double totalFollowerCount = dataMap.values().stream()
                .filter(v -> v.getFollowerCount() != null)
                .mapToDouble(CompetitorProfileESData::getFollowerCount).sum();
        double totalNetFollowerCount = dataMap.values().stream()
                .filter(v -> v.getNetFollowerCount() != null)
                .mapToDouble(CompetitorProfileESData::getNetFollowerCount).sum();
        double totalStartFollowerCount = dataMap.values().stream()
                .filter(v -> v.getNetFollowerCount() != null && v.getFollowerGrowthPercent() != null)
                .mapToDouble(data -> (data.getNetFollowerCount()*100.0/data.getFollowerGrowthPercent())).sum();

        int dataSize = dataMap.size();
        double averageFollowerCount = (dataSize > 0) ? totalFollowerCount / dataSize : 0;
        double averageNetFollowerCount = (dataSize > 0) ? totalNetFollowerCount / dataSize : 0;
        double averageFollowerGrowthPercentage;
        if(totalStartFollowerCount == 0) {
            if(totalNetFollowerCount < 0){
                averageFollowerGrowthPercentage = -100;
            } else {
                averageFollowerGrowthPercentage = 0.0;
            }
        }else {
            averageFollowerGrowthPercentage = (double) (totalNetFollowerCount * 100) / totalStartFollowerCount;
        }

        return CompetitorProfileESData.builder()
                .followerCount(totalFollowerPresent ? null : roundToTwoDecimalPlaces(averageFollowerCount))
                .netFollowerCount(totalNetFollowerPresent ? null : roundToTwoDecimalPlaces(averageNetFollowerCount))
                .followerGrowthPercent(totalNetFollowerPresent ? null : roundToTwoDecimalPlaces(averageFollowerGrowthPercentage))
                .build();
    }
    public CompetitorPublishingBehaviourReportESData processCompetitorPublishingDataChannelWise(CompetitorPublishingBehaviourReportESData competitorRequestDTO, Map<String, CompetitorPageDetails> pageIdVsPageName, String channel) {
        // Build competitor profile data
        CompetitorPublishingBehaviourReportESData competitorProfileData = CompetitorPublishingBehaviourReportESData.builder()
                .publishingBehaviourData(new HashMap<>())
                .build();

        pageIdVsPageName.forEach((pageId, pageDetail) -> {
            if(competitorRequestDTO.getPublishingBehaviourData().containsKey(pageId)){
                String pageName = pageIdVsPageName.get(pageId).getPageName();
                competitorProfileData.getPublishingBehaviourData().put(pageName, competitorRequestDTO.getPublishingBehaviourData().get(pageId));
            } else if(channel.equals(pageDetail.getChannel())) {
                CompetitorPublishingBehaviourESData profileESData = CompetitorPublishingBehaviourESData.builder()
                        .publishedLinks(null)
                        .publishedPosts(null)
                        .publishedPhotos(null)
                        .publishedVideos(null)
                        .publishedTexts(null)
                        .profilePictureUrl(pageIdVsPageName.get(pageId).getProfilePictureUrl())
                        .pageLink(pageIdVsPageName.get(pageId).getPageLink())
                        .build();
                competitorProfileData.getPublishingBehaviourData().put(pageIdVsPageName.get(pageId).getPageName(), profileESData);
            }
        });

        competitorProfileData.getPublishingBehaviourData().put("total", getAggregatedPublishingDataForChannel(competitorProfileData));
        return competitorProfileData;
    }

    public CompetitorEngagementReportESData processCompetitorEngagementDataChannelWise(CompetitorEngagementReportESData reportESData, Map<String, CompetitorPageDetails> pageIdVsPageName, String channel) {
        // Build competitor profile data
        CompetitorEngagementReportESData competitorProfileData = CompetitorEngagementReportESData.builder()
                .data(new HashMap<>())
                .build();


        pageIdVsPageName.forEach((pageId, pageDetail) -> {
            if(reportESData.getData().containsKey(pageId)) {
                competitorProfileData.getData().put(pageDetail.getPageName(), reportESData.getData().get(pageId));
            } else if(channel.equals(pageDetail.getChannel())) {
                CompetitorEngagementESData profileESData = CompetitorEngagementESData.builder()
                        .shares(null)
                        .comments(null)
                        .totalEngagement(null)
                        .publicEngagementPerPost(null)
                        .reactions(null)
                        .profilePictureUrl(pageIdVsPageName.get(pageId).getProfilePictureUrl())
                        .pageLink(pageIdVsPageName.get(pageId).getPageLink())
                        .build();
                competitorProfileData.getData().put(pageIdVsPageName.get(pageId).getPageName(), profileESData);
            }
        });

        competitorProfileData.getData().put("total", getAggregatedEngagementDataForChannel(competitorProfileData));
        return competitorProfileData;
    }

    public CompetitorPublishingBehaviourESData getAggregatedPublishingDataForChannel(CompetitorPublishingBehaviourReportESData competitorRequestDTO){
        Map<String, CompetitorPublishingBehaviourESData> dataMap = competitorRequestDTO.getPublishingBehaviourData();
        boolean totalPostsPresent = dataMap.values().stream()
                .map(CompetitorPublishingBehaviourESData::getPublishedPosts)
                .allMatch(Objects::isNull);

        boolean totalVideosPresent = dataMap.values().stream()
                .map(CompetitorPublishingBehaviourESData::getPublishedVideos)
                .allMatch(Objects::isNull);

        boolean totalPhotosPresent = dataMap.values().stream()
                .map(CompetitorPublishingBehaviourESData::getPublishedPhotos)
                .allMatch(Objects::isNull);

        boolean publishLinkPresent = dataMap.values().stream()
                .map(CompetitorPublishingBehaviourESData::getPublishedLinks)
                .allMatch(Objects::isNull);

        boolean totalTextPresent = dataMap.values().stream()
                .map(CompetitorPublishingBehaviourESData::getPublishedTexts)
                .allMatch(Objects::isNull);

        double totalPublishedPosts = dataMap.values().stream()
                .filter(v -> v.getPublishedPosts() != null)
                .mapToDouble(CompetitorPublishingBehaviourESData::getPublishedPosts).sum();
        double totalPublishedVideos = dataMap.values().stream()
                .filter(v -> v.getPublishedVideos() != null)
                .mapToDouble(CompetitorPublishingBehaviourESData::getPublishedVideos).sum();
        double totalPublishedPhotos = dataMap.values().stream()
                .filter(v -> v.getPublishedPhotos() != null)
                .mapToDouble(CompetitorPublishingBehaviourESData::getPublishedPhotos).sum();
        double totalPublishedLinks = dataMap.values().stream()
                .filter(v -> v.getPublishedLinks() != null)
                .mapToDouble(CompetitorPublishingBehaviourESData::getPublishedLinks).sum();
        double totalPublishedTextPosts = dataMap.values().stream()
                .filter(v -> v.getPublishedTexts() != null)
                .mapToDouble(CompetitorPublishingBehaviourESData::getPublishedTexts).sum();

        int dataSize = dataMap.size();
        double averagePublishedPosts = (dataSize > 0) ? (totalPublishedPosts / dataSize) : 0;
        double averagePublishedVideos = (dataSize > 0) ? (totalPublishedVideos / dataSize) : 0;
        double averagePublishedPhotos = (dataSize > 0) ? (totalPublishedPhotos / dataSize) : 0;
        double averagePublishedLinks = (dataSize > 0) ? (totalPublishedLinks / dataSize) : 0;
        double averagePublishedTextPosts = (dataSize > 0) ? (totalPublishedTextPosts / dataSize) : 0;

        return CompetitorPublishingBehaviourESData.builder()
                .publishedPosts(totalPostsPresent ? null : roundToTwoDecimalPlaces(averagePublishedPosts))
                .publishedVideos(totalVideosPresent ? null : roundToTwoDecimalPlaces(averagePublishedVideos))
                .publishedPhotos(totalPhotosPresent ? null : roundToTwoDecimalPlaces(averagePublishedPhotos))
                .publishedLinks(publishLinkPresent ? null : roundToTwoDecimalPlaces(averagePublishedLinks))
                .publishedTexts(totalTextPresent ? null : roundToTwoDecimalPlaces(averagePublishedTextPosts))
                .build();
    }

    public CompetitorPublishingBehaviourReportESData getCompetitorPublishingDataES(Date startDate, Date endDate, List<String> pageIds, String channel, Boolean isCompetitor) {
        return null;
    }

    public CompetitorEngagementESData getAggregatedEngagementDataForChannel(CompetitorEngagementReportESData reportESData){
        Map<String, CompetitorEngagementESData> dataMap = reportESData.getData();

        boolean totalEngagementPresent = dataMap.values().stream()
                .map(CompetitorEngagementESData::getTotalEngagement)
                .allMatch(Objects::isNull);

        boolean totalPublicEngagementPresent = dataMap.values().stream()
                .map(CompetitorEngagementESData::getPublicEngagementPerPost)
                .allMatch(Objects::isNull);

        boolean totalReactionPresent = dataMap.values().stream()
                .map(CompetitorEngagementESData::getReactions)
                .allMatch(Objects::isNull);

        boolean totalSharePresent = dataMap.values().stream()
                .map(CompetitorEngagementESData::getShares)
                .allMatch(Objects::isNull);

        boolean totalCommentPresent = dataMap.values().stream()
                .map(CompetitorEngagementESData::getComments)
                .allMatch(Objects::isNull);

        double totalEngagement = dataMap.values().stream()
                .filter(v -> v.getTotalEngagement() != null)
                .mapToDouble(CompetitorEngagementESData::getTotalEngagement).sum();
        double totalEngagementPerPost = dataMap.values().stream()
                .filter(v -> v.getPublicEngagementPerPost() != null)
                .mapToDouble(CompetitorEngagementESData::getPublicEngagementPerPost).sum();
        double totalLikes = dataMap.values().stream()
                .filter(v -> v.getReactions() != null)
                .mapToDouble(CompetitorEngagementESData::getReactions).sum();
        double totalShares = dataMap.values().stream()
                .filter(v -> v.getShares() != null)
                .mapToDouble(CompetitorEngagementESData::getShares).sum();
        double totalComment = dataMap.values().stream()
                .filter(v -> v.getComments() != null)
                .mapToDouble(CompetitorEngagementESData::getComments).sum();

        int dataSize = dataMap.size();

        double averageTotalEngagement = (dataSize > 0) ? (totalEngagement / dataSize) : 0;
        double averageTotalEngagementPerPost = (dataSize > 0) ? (totalEngagementPerPost / dataSize) : 0;
        double averageTotalLikes = (dataSize > 0) ? (totalLikes / dataSize) : 0;
        double averageTotalShares = (dataSize > 0) ? (totalShares / dataSize) : 0;
        double averageTotalComments= (dataSize > 0) ? (totalComment / dataSize) : 0;

        return CompetitorEngagementESData.builder()
                .totalEngagement(totalEngagementPresent ? null :  roundToTwoDecimalPlaces(averageTotalEngagement))
                .publicEngagementPerPost(totalPublicEngagementPresent ? null : roundToTwoDecimalPlaces(averageTotalEngagementPerPost))
                .reactions(totalReactionPresent ? null : roundToTwoDecimalPlaces(averageTotalLikes))
                .shares(totalSharePresent ? null : roundToTwoDecimalPlaces(averageTotalShares))
                .comments(totalCommentPresent ? null : roundToTwoDecimalPlaces(averageTotalComments))
                .build();
    }


    public SearchResponse createEsRequestForAudienceData(Date startDate, Date endDate, List<String> pageId, Integer sourceId) {
        try {
            String indexName = getCompetitorProfileEsIndex(sourceId);
            SearchRequest searchRequest = new SearchRequest(indexName);
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            // Building the query
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termsQuery("pageId", pageId))
                    .filter(QueryBuilders.rangeQuery(REPORT_DATE)
                            .gte(DateTimeUtils.localToESFormat(startDate))
                            .lte(DateTimeUtils.localToESFormat(endDate)));


            // Aggregations for startDateFollowerData
            TopHitsAggregationBuilder startDateFollowerData = AggregationBuilders.topHits(START_DATE_FOLLOWER_DATA)
                    .size(1)
                    .sort(REPORT_DATE, SortOrder.ASC);

            // Aggregations for endDateFollowerData
            TopHitsAggregationBuilder endDateFollowerData = AggregationBuilders.topHits(END_DATE_FOLLOWER_DATA)
                    .size(1)
                    .sort(REPORT_DATE, SortOrder.DESC);

            // Terms aggregation
            TermsAggregationBuilder byPageId = AggregationBuilders.terms(BY_PAGE_ID)
                    .field("pageId")
                    .size(10000)
                    .subAggregation(startDateFollowerData)
                    .subAggregation(endDateFollowerData);


            // Building the search source
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.aggregation(byPageId);
            searchSourceBuilder.size(0);

            // Setting the search source to the search request
            searchRequest.source(searchSourceBuilder);

            return esService.search(searchRequest);

        } catch (Exception ex) {
            LOG.info("Exception occurred while creating template for request ", ex);
            return null;
        }
    }

    public SearchResponse createEsRequestForSelfPageAudienceData(Date startDate, Date endDate, List<String> pageIds, Integer sourceId) {
        try {
            String indexName = getSelfProfileEsIndex(sourceId);
            SearchRequest searchRequest = new SearchRequest(indexName);
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            // Building the query
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termsQuery("page_id", pageIds))
                    .filter(QueryBuilders.rangeQuery("day")
                            .from(DateTimeUtils.localToESFormat(startDate))
                            .to(DateTimeUtils.localToESFormat(endDate))
                            .includeLower(true)
                            .includeUpper(true));


            // Aggregations for startDateFollowerData
            TopHitsAggregationBuilder startDateFollowerData = AggregationBuilders.topHits(START_DATE_FOLLOWER_DATA)
                    .size(1)
                    .sort("day", SortOrder.ASC);

            // Aggregations for endDateFollowerData
            TopHitsAggregationBuilder endDateFollowerData = AggregationBuilders.topHits(END_DATE_FOLLOWER_DATA)
                    .size(1)
                    .sort("day", SortOrder.DESC);

            // Terms aggregation
            TermsAggregationBuilder byPageId = AggregationBuilders.terms(BY_PAGE_ID)
                    .field("page_id")
                    .size(10000)
                    .subAggregation(startDateFollowerData)
                    .subAggregation(endDateFollowerData);

            // Building the search source
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.aggregation(byPageId);
            searchSourceBuilder.size(0);

            // Setting the search source to the search request
            searchRequest.source(searchSourceBuilder);

            // Executing the search
            return esService.search(searchRequest);

        } catch (Exception ex) {
            LOG.info("Exception occurred while creating template for request ", ex);
            return null;
        }
    }

    public CompetitorProfileReportESData createEsReportDataForAudience(SearchResponse searchResponse, Date startDate, Date endDate, Map<String, CompetitorPageDetails> pageIdVsName) {
        Map<String, CompetitorProfileESData> data = new HashMap<>();
        try {
            Aggregations aggregations = searchResponse.getAggregations();
            if(Objects.isNull(aggregations)){
                LOG.info(EMPTY_AGGREGATIONS_LOG);
                return null;
            }

            ParsedStringTerms dataByPageId = aggregations.get(BY_PAGE_ID);

            List<? extends Terms.Bucket> buckets = dataByPageId.getBuckets();
            if(Objects.isNull(buckets)) {
                return null;
            }

            for (Terms.Bucket bucket : buckets) {
                try {
                    // pageId
                    String pageId = bucket.getKeyAsString();

                    ParsedTopHits startAggregation = bucket.getAggregations().get("startDateFollowerData");

                    ParsedTopHits endAggregation = bucket.getAggregations().get("endDateFollowerData");

                    CompetitorProfileInsightsES startData = JSONUtils.fromJSON(startAggregation.getHits().getAt(0).getSourceAsString(), CompetitorProfileInsightsES.class);

                    CompetitorProfileInsightsES endData = JSONUtils.fromJSON(endAggregation.getHits().getAt(0).getSourceAsString(), CompetitorProfileInsightsES.class);

                    double minDateFollowerCount = 0;
                    double maxDateFollowerCount = 0;


                    double netFollowerGrowth = 0.0;

                    double startFollowerCount = startData.getFollowerCount();
                    double endFollowerCount = endData.getFollowerCount();

                    double netFollowers = endFollowerCount - startFollowerCount;

                    if(minDateFollowerCount > 0) {
                        startFollowerCount = minDateFollowerCount;
                    }
                    if(maxDateFollowerCount > 0) {
                        endFollowerCount = maxDateFollowerCount;
                    }
                    // (ending value - starting value) / starting value * 100

                    if(endFollowerCount == 0) {
                        if(netFollowers < 0){
                            netFollowerGrowth = -100;
                        } else {
                            netFollowerGrowth = 0.0;
                        }
                    }else {
                        netFollowerGrowth = startFollowerCount == 0 ? 0 : ( netFollowers / startFollowerCount) * 100;
                    }

                    String profilePictureUrl = Objects.nonNull(pageIdVsName.get(pageId)) ? pageIdVsName.get(pageId).getProfilePictureUrl() : null;
                    String pageLink = Objects.nonNull(pageIdVsName.get(pageId)) ? pageIdVsName.get(pageId).getPageLink() : null;

                    CompetitorProfileESData profileESData = CompetitorProfileESData.builder()
                            .followerCount(endFollowerCount)
                            .netFollowerCount(netFollowers)
                            .followerGrowthPercent(roundToTwoDecimalPlaces(netFollowerGrowth))
                            .profilePictureUrl(profilePictureUrl)
                            .pageLink(pageLink)
                            .build();

                    data.put(pageId, profileESData);

                } catch (Exception e) {
                    LOG.info("getTotalAudience CompetitorEsChannelWise Parse exception occurred : {}", e.getMessage());
                }
            }

        } catch (Exception ex) {
            LOG.info("getTotalAudience CompetitorEsChannelWise exception occurred : {}", ex.getMessage());
        }
        return CompetitorProfileReportESData.builder().data(data).build();
    }

    public CompetitorProfileReportESData createEsReportDataForSelfPageAudience(SearchResponse searchResponse, Date startDate, Date endDate, Map<String, CompetitorPageDetails> pageIdVsName) {
        Map<String, CompetitorProfileESData> data = new HashMap<>();
        try {
            Aggregations aggregations = searchResponse.getAggregations();
            if(Objects.isNull(aggregations)){
                LOG.info(EMPTY_AGGREGATIONS_LOG);
                return null;
            }

            ParsedStringTerms dataByPageId = aggregations.get(BY_PAGE_ID);

            List<? extends Terms.Bucket> buckets = dataByPageId.getBuckets();
            if(Objects.isNull(buckets)) {
                return null;
            }

            for (Terms.Bucket bucket : buckets) {
                try {
                    // pageId
                    String pageId = bucket.getKeyAsString();

                    ParsedTopHits startAggregation = bucket.getAggregations().get("startDateFollowerData");

                    ParsedTopHits endAggregation = bucket.getAggregations().get("endDateFollowerData");

                    ESPageRequest startData = JSONUtils.fromJSON(startAggregation.getHits().getAt(0).getSourceAsString(), ESPageRequest.class);

                    ESPageRequest endData = JSONUtils.fromJSON(endAggregation.getHits().getAt(0).getSourceAsString(), ESPageRequest.class);


                    double minDateFollowerCount = 0;
                    double maxDateFollowerCount = 0;


                    double netFollowerGrowth = 0.0;

                    double startFollowerCount = Objects.isNull(startData.getTotal_follower_count()) ? 0 : startData.getTotal_follower_count();
                    double endFollowerCount = Objects.isNull(endData.getTotal_follower_count()) ? 0 : endData.getTotal_follower_count();

                    double netFollowers = endFollowerCount - startFollowerCount;

                    if(minDateFollowerCount > 0) {
                        startFollowerCount = minDateFollowerCount;
                    }
                    if(maxDateFollowerCount > 0) {
                        endFollowerCount = maxDateFollowerCount;
                    }
                    // (ending value - starting value) / starting value * 100

                    // (ending value - starting value) / starting value * 100
                    if(endFollowerCount == 0) {
                        if(netFollowers < 0){
                            netFollowerGrowth = -100;
                        } else {
                            netFollowerGrowth = 0.0;
                        }
                    }else {
                        netFollowerGrowth = startFollowerCount == 0 ? 0 : ( netFollowers / startFollowerCount) * 100;
                    }

                    String profilePictureUrl = Objects.nonNull(pageIdVsName.get(pageId)) ? pageIdVsName.get(pageId).getProfilePictureUrl() : null;
                    String pageLink = Objects.nonNull(pageIdVsName.get(pageId)) ? pageIdVsName.get(pageId).getPageLink() : null;


                    if(endFollowerCount == 0 && netFollowers == 0 && netFollowerGrowth == 0) {
                        CompetitorProfileESData profileESData = CompetitorProfileESData.builder()
                                .profilePictureUrl(profilePictureUrl)
                                .build();

                        data.put(pageId, profileESData);
                    } else {
                        CompetitorProfileESData profileESData = CompetitorProfileESData.builder()
                                .netFollowerCount(netFollowers)
                                .followerCount(endFollowerCount)
                                .followerGrowthPercent(roundToTwoDecimalPlaces(netFollowerGrowth))
                                .profilePictureUrl(profilePictureUrl)
                                .pageLink(pageLink)
                                .build();

                        data.put(pageId, profileESData);
                    }

                } catch (Exception e) {
                    LOG.info("getTotalAudience CompetitorEsChannelWise Parse exception occurred : {}", e.getMessage());
                }
            }

        } catch (Exception ex) {
            LOG.info("getTotalAudience CompetitorEsChannelWise exception occurred : {}", ex.getMessage());
        }
        return CompetitorProfileReportESData.builder().data(data).build();
    }


//    public Map<String, CompetitorPageDetails> pageIdToLocationName(Map<Integer, String> locationIdVsPageId) {
//        Map<String, CompetitorPageDetails> response = new HashMap<>();
//        try {
//            List<Integer> locationIds = locationIdVsPageId.keySet().stream().filter(Objects::nonNull).collect(Collectors.toList());
//            List<BusinessBizLiteDto> businessBizLiteDtos = businessCoreService.getBusinessLiteDtoByBusinessIds(locationIds);
//
//            if (CollectionUtils.isEmpty(businessBizLiteDtos)) {
//                LOG.info("No business details found for locationIds {}", locationIds);
//            }
//
//            for (BusinessBizLiteDto businessDetails : businessBizLiteDtos) {
//                if (Objects.nonNull(locationIdVsPageId.get(businessDetails.getId()))) {
//                    response.put(locationIdVsPageId.get(businessDetails.getId()),
//                            Objects.nonNull(businessDetails.getName()) ? businessDetails.getName() : businessDetails.getAlias1());
//                }
//            }
//        }catch (Exception ex){
//            LOG.info("Exception occurred while fetching location details from core {}", ex.getMessage());
//        }
//        return response;
//    }
    public Map<String, CompetitorPageDetails> processPageIdVsName(List<String> channels, CompetitorProfileDetailsRequestDTO requestDTO){
        Map<String, CompetitorPageDetails> pageIdVsName = new HashMap<>();
        Map<String, CompetitorPageDetails> locationIdVsPageId = new HashMap<>();
        for (String channel : channels) {
            SocialCompetitor execute = competitorFactory.getSocialCompetitorChannel(SocialChannel.getSocialChannelName(channel))
                    .orElseThrow(() -> new IllegalArgumentException("Invalid Channel Name"));
            List<String> pageIds = requestDTO.getChannelWisePage().getPageIdsByChannel(channel);
            if(Boolean.TRUE.equals(requestDTO.getIsCompetitor())){
                pageIdVsName.putAll(execute.getPageIdVsPageName(pageIds));
            }else {
                pageIdVsName.putAll(execute.getLocationIdVsPageId(pageIds));
                LOG.info("Channel : {}, locationIdVsPageId: {}", channel, locationIdVsPageId);
            }
        }
        return pageIdVsName;
    }

    public Map<String, CompetitorPageDetails> processPageIdVsNameExcel(List<String> channels, ChannelWisePage channelWisePage, Boolean isCompetitor){
        Map<String, CompetitorPageDetails> pageIdVsName = new HashMap<>();
        Map<String, CompetitorPageDetails> locationIdVsPageId = new HashMap<>();
        for (String channel : channels) {
            SocialCompetitor execute = competitorFactory.getSocialCompetitorChannel(SocialChannel.getSocialChannelName(channel))
                    .orElseThrow(() -> new IllegalArgumentException("Invalid Channel Name"));
            List<String> pageIds = channelWisePage.getPageIdsByChannel(channel);
            if(Boolean.TRUE.equals(isCompetitor)) {
                pageIdVsName.putAll(execute.getPageIdVsPageName(pageIds));
            } else {
                pageIdVsName.putAll(execute.getLocationIdVsPageId(pageIds));
                LOG.info("Channel : {}, locationIdVsPageId: {}", channel, locationIdVsPageId);
            }
        }
        return pageIdVsName;
    }

    public Map<String, CompetitorPageDetails> processPageIdVsNameTopPost(List<String> channels, CompetitorAllProfilesDetailsRequestDTO requestDTO, boolean isCompetitor){
        Map<String, CompetitorPageDetails> pageIdVsName = new HashMap<>();
        Map<String, CompetitorPageDetails> locationIdVsPageId = new HashMap<>();
        for (String channel : channels) {
            SocialCompetitor execute = competitorFactory.getSocialCompetitorChannel(SocialChannel.getSocialChannelName(channel))
                    .orElseThrow(() -> new IllegalArgumentException("Invalid Channel Name"));
            if(Boolean.TRUE.equals(isCompetitor)){
                List<String> pageIds = requestDTO.getCompetitorChannelWisePage().getPageIdsByChannel(channel);
                pageIdVsName.putAll(execute.getPageIdVsPageName(pageIds));
            }else {
                List<String> pageIds = requestDTO.getSelfChannelWisePage().getPageIdsByChannel(channel);

                pageIdVsName.putAll(execute.getLocationIdVsPageId(pageIds));
                LOG.info("Channel : {}, locationIdVsPageId: {}", channel, locationIdVsPageId);
            }
        }
        return pageIdVsName;
    }

    public SearchResponse createEsRequestForSelfPublishedPosts(Date startDate, Date endDate, List<String> pageIds, String channel) {
        try {
            SearchRequest searchRequest = new SearchRequest(InsightsConstants.POST_INSIGHT);
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            // Building the bool query
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termsQuery("page_id", pageIds))
                    .filter(QueryBuilders.termQuery("source_id", SocialChannel.getSocialChannelByName(channel).getId()))
                    .filter(QueryBuilders.rangeQuery("posted_date")
                            .from(DateTimeUtils.localToESFormat(startDate))
                            .to(DateTimeUtils.localToESFormat(endDate))
                            .includeLower(true)
                            .includeUpper(true)
                            .format(YYYY_MM_DD_HH_MM_SS));

            // Building the aggregations
            ValueCountAggregationBuilder postCountAgg = AggregationBuilders.count(POST_COUNT).field("_index");

            SumAggregationBuilder imageUrlCountAgg = AggregationBuilders.sum(IMAGE_URL_COUNT).field("image_url_count");
            SumAggregationBuilder videoUrlCountAgg = AggregationBuilders.sum(VIDEO_URL_COUNT).field("video_url_count");
            SumAggregationBuilder linkCountAgg = AggregationBuilders.sum(LINK_COUNT).field("link_count");
            SumAggregationBuilder postTextAgg = AggregationBuilders.sum(TEXT_POST_COUNT).field("post_text_count");


            TermsAggregationBuilder byPageIdAgg = AggregationBuilders.terms(BY_PAGE_ID)
                    .field("page_id")
                    .size(MAX_SELF_PAGE_LIMIT)
                    .subAggregation(postCountAgg)
                    .subAggregation(imageUrlCountAgg)
                    .subAggregation(videoUrlCountAgg)
                    .subAggregation(linkCountAgg)
                    .subAggregation(postTextAgg);

            // Building the search source
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.aggregation(byPageIdAgg);
            searchSourceBuilder.size(0);

            // Setting the search source to the search request
            searchRequest.source(searchSourceBuilder);

            return esService.search(searchRequest);

        } catch (Exception ex) {
            LOG.info("Exception occur while creating self page published post template for request ", ex);
            return null;
        }
    }

    public CompetitorPublishingBehaviourReportESData createEsReportDataForSelfPublishedPosts(SearchResponse searchResponse, Map<String, CompetitorPageDetails> pageIdVsName) {
        Map<String, CompetitorPublishingBehaviourESData> data = new HashMap<>();
        try {
            Aggregations aggregations = searchResponse.getAggregations();
            if(Objects.isNull(aggregations)){
                LOG.info(EMPTY_AGGREGATIONS_LOG);
                return null;
            }

            ParsedStringTerms dataByPageId = aggregations.get(BY_PAGE_ID);

            List<? extends Terms.Bucket> buckets = dataByPageId.getBuckets();
            if(Objects.isNull(buckets)) {
                return null;
            }

            for (Terms.Bucket bucket : buckets) {
                try {
                    // pageId
                    String pageId = bucket.getKeyAsString();

                    ParsedSum imageUrlCount = bucket.getAggregations().get(IMAGE_URL_COUNT);
                    ParsedSum videoUrlCount = bucket.getAggregations().get(VIDEO_URL_COUNT);
                    ParsedValueCount postCount = bucket.getAggregations().get(POST_COUNT);
                    ParsedSum linkCount = bucket.getAggregations().get(LINK_COUNT);
                    ParsedSum textPostsCount = bucket.getAggregations().get(TEXT_POST_COUNT);

                    String profilePictureUrl = Objects.isNull(pageIdVsName.get(pageId)) ? null : pageIdVsName.get(pageId).getProfilePictureUrl();
                    String pageLink = Objects.nonNull(pageIdVsName.get(pageId)) ? pageIdVsName.get(pageId).getPageLink() : null;
                    String channel = Objects.nonNull(pageIdVsName.get(pageId)) ? pageIdVsName.get(pageId).getChannel() : null;


                    CompetitorPublishingBehaviourESData profileESData = CompetitorPublishingBehaviourESData.builder()
                            .publishedPhotos(Objects.nonNull(imageUrlCount.getValue()) ? (double) imageUrlCount.value() : 0)
                            .publishedVideos(Objects.nonNull(videoUrlCount.getValue()) ? (double) videoUrlCount.value() : 0)
                            .publishedPosts(Objects.nonNull(postCount.getValue()) ? (double) postCount.value() : 0)
                            .publishedLinks(SocialChannel.INSTAGRAM.getName().equals(channel) ? null :
                                    Objects.nonNull(linkCount.getValue()) ? (double) linkCount.value() : 0)
                            .publishedTexts(SocialChannel.INSTAGRAM.getName().equals(channel) ? null :
                                    Objects.nonNull(textPostsCount.getValue()) ? (double) textPostsCount.getValue() : 0)
                            .profilePictureUrl(profilePictureUrl)
                            .pageLink(pageLink)
                            .build();

                    data.put(pageId, profileESData);

                } catch (Exception e) {
                    LOG.info("getTotalAudience CompetitorEsChannelWise Parse exception occurred : {}", e.getMessage());
                }
            }

        } catch (Exception ex) {
            LOG.info("getTotalAudience CompetitorEsChannelWise exception occurred : {}", ex.getMessage());
        }
        return CompetitorPublishingBehaviourReportESData.builder().publishingBehaviourData(data).build();
    }

    public SearchResponse createEsRequestForPublishedPosts(Date startDate, Date endDate, List<String> pageIds, String channel) {
        try {
            Integer accountMaxLimit =  CacheManager.getInstance().getCache(SystemPropertiesCache.class).getCompetitorAccountMaxSize();

            LOG.info("Index : {}", ElasticConstants.COMPETITOR_POSTS.getName());
            SearchRequest searchRequest = new SearchRequest(ElasticConstants.COMPETITOR_POSTS.getName());
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            // Building the bool query
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termsQuery("pageId", pageIds))
                    .filter(QueryBuilders.termQuery("sourceId", SocialChannel.getSocialChannelByName(channel).getId()))
                    .filter(QueryBuilders.rangeQuery("publishedDate")
                            .from(DateTimeUtils.localToESFormat(startDate))
                            .to(DateTimeUtils.localToESFormat(endDate))
                            .includeLower(true)
                            .includeUpper(true)
                            .format(YYYY_MM_DD_HH_MM_SS));

            // Building the aggregations
            ValueCountAggregationBuilder postCountAgg = AggregationBuilders.count(POST_COUNT).field("_index");

            SumAggregationBuilder imageUrlCountAgg = AggregationBuilders.sum(IMAGE_URL_COUNT).field("image_url_count");
            SumAggregationBuilder videoUrlCountAgg = AggregationBuilders.sum(VIDEO_URL_COUNT).field("video_url_count");
            SumAggregationBuilder linkCountAgg = AggregationBuilders.sum(LINK_COUNT).field("link_count");
            SumAggregationBuilder postTextAgg = AggregationBuilders.sum(TEXT_POST_COUNT).field("post_text_count");

            TermsAggregationBuilder byPageIdAgg = AggregationBuilders.terms(BY_PAGE_ID)
                    .field("pageId.keyword")
                    .size(accountMaxLimit)
                    .subAggregation(postCountAgg)
                    .subAggregation(imageUrlCountAgg)
                    .subAggregation(videoUrlCountAgg)
                    .subAggregation(linkCountAgg)
                    .subAggregation(postTextAgg);

            // Building the search source
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.aggregation(byPageIdAgg);
            searchSourceBuilder.size(0);

            // Setting the search source to the search request
            searchRequest.source(searchSourceBuilder);

            return esService.search(searchRequest);

        } catch (Exception ex) {
            LOG.info("Exception occurred while creating self page published post template for request ", ex);
            return null;
        }
    }

    public SearchResponse createEsRequestForEngagement(Date startDate, Date endDate, List<String> pageIds) {
        try {
            Integer accountMaxLimit =  CacheManager.getInstance().getCache(SystemPropertiesCache.class).getCompetitorAccountMaxSize();

            LOG.info("Index: {}", ElasticConstants.COMPETITOR_POSTS.getName());
            SearchRequest searchRequest = new SearchRequest(ElasticConstants.COMPETITOR_POSTS.getName());
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            // Building the bool query
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termsQuery("pageId", pageIds))
                    .filter(QueryBuilders.rangeQuery("publishedDate")
                            .from(DateTimeUtils.localToESFormat(startDate))
                            .to(DateTimeUtils.localToESFormat(endDate))
                            .includeLower(true)
                            .includeUpper(true)
                            .format(YYYY_MM_DD_HH_MM_SS));

            // Building the aggregations
            ValueCountAggregationBuilder postCountAgg = AggregationBuilders.count(POST_COUNT).field("_index");

            SumAggregationBuilder imageUrlCountAgg = AggregationBuilders.sum(LIKE_COUNT).field(LIKE_COUNT);
            SumAggregationBuilder videoUrlCountAgg = AggregationBuilders.sum(COMMENT_COUNT).field(COMMENT_COUNT);
            SumAggregationBuilder linkCountAgg = AggregationBuilders.sum(SHARE_COUNT).field(SHARE_COUNT);

            TermsAggregationBuilder byPageIdAgg = AggregationBuilders.terms(BY_PAGE_ID)
                    .field("pageId.keyword")
                    .size(accountMaxLimit)
                    .subAggregation(postCountAgg)
                    .subAggregation(imageUrlCountAgg)
                    .subAggregation(videoUrlCountAgg)
                    .subAggregation(linkCountAgg);

            // Building the search source
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.aggregation(byPageIdAgg);
            searchSourceBuilder.size(0);

            // Setting the search source to the search request
            searchRequest.source(searchSourceBuilder);

            return esService.search(searchRequest);

        } catch (Exception ex) {
            LOG.info("Exception occurred while creating self page published post template for request ", ex);
            return null;
        }
    }

    public SearchResponse createSelfEsRequestForEngagement(Date startDate, Date endDate, List<String> pageIds) {
        try {
            LOG.info("Index: {}", InsightsConstants.POST_INSIGHT);
            SearchRequest searchRequest = new SearchRequest(InsightsConstants.POST_INSIGHT);
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            // Building the bool query
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termsQuery("page_id", pageIds))
                    .filter(QueryBuilders.rangeQuery("posted_date")
                            .from(DateTimeUtils.localToESFormat(startDate))
                            .to(DateTimeUtils.localToESFormat(endDate))
                            .includeLower(true)
                            .includeUpper(true)
                            .format(YYYY_MM_DD_HH_MM_SS));

            // Building the aggregations
            ValueCountAggregationBuilder postCountAgg = AggregationBuilders.count(POST_COUNT).field("_index");

            SumAggregationBuilder imageUrlCountAgg = AggregationBuilders.sum(LIKE_COUNT).field("like_count");
            SumAggregationBuilder videoUrlCountAgg = AggregationBuilders.sum(COMMENT_COUNT).field("comment_count");
            SumAggregationBuilder linkCountAgg = AggregationBuilders.sum(SHARE_COUNT).field("share_count");

            TermsAggregationBuilder byPageIdAgg = AggregationBuilders.terms(BY_PAGE_ID)
                    .field("page_id")
                    .size(MAX_SELF_PAGE_LIMIT)
                    .subAggregation(postCountAgg)
                    .subAggregation(imageUrlCountAgg)
                    .subAggregation(videoUrlCountAgg)
                    .subAggregation(linkCountAgg);

            // Building the search source
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.aggregation(byPageIdAgg);
            searchSourceBuilder.size(0);

            // Setting the search source to the search request
            searchRequest.source(searchSourceBuilder);

            return esService.search(searchRequest);

        } catch (Exception ex) {
            LOG.info("Exception occurred while creating self page published post template for request ", ex);
            return null;
        }
    }

    public CompetitorEngagementReportESData createEsReportDataForEngagement(SearchResponse searchResponse, Map<String, CompetitorPageDetails> pageIdVsName) {
        Map<String, CompetitorEngagementESData> data = new HashMap<>();
        try {
            Aggregations aggregations = searchResponse.getAggregations();
            if(Objects.isNull(aggregations)){
                LOG.info(EMPTY_AGGREGATIONS_LOG);
                return null;
            }

            ParsedStringTerms dataByPageId = aggregations.get(BY_PAGE_ID);

            List<? extends Terms.Bucket> buckets = dataByPageId.getBuckets();
            if(Objects.isNull(buckets)) {
                return null;
            }

            for (Terms.Bucket bucket : buckets) {
                try {
                    // pageId
                    String pageId = bucket.getKeyAsString();

                    ParsedValueCount postCountSum = bucket.getAggregations().get(POST_COUNT);
                    ParsedSum commentCountSum = bucket.getAggregations().get(COMMENT_COUNT);
                    ParsedSum reactionCountSum = bucket.getAggregations().get(LIKE_COUNT);
                    ParsedSum shareCountSum = bucket.getAggregations().get(SHARE_COUNT);


                    postCountSum.getValue();
                    int postCount = (int) postCountSum.getValue();
                    double commentCount = Objects.nonNull(commentCountSum) ? commentCountSum.getValue() : 0;
                    double reactionCount = Objects.nonNull(reactionCountSum) ? reactionCountSum.getValue() : 0;
                    double shareCount = Objects.nonNull(shareCountSum) ? shareCountSum.getValue() : 0;
                    double totalEngagement =  commentCount + reactionCount + shareCount;
                    double engagementRate =  postCount == 0 ?
                            0 : (totalEngagement / postCount);
                    LOG.info("pageId : {}, postCount : {}, commentCount : {}, reactionCount : {}, shareCount : {}, totalEngagement : {}, engagementRate : {}",
                            pageId, postCount, commentCount, reactionCount, shareCount, totalEngagement, engagementRate);

                    String profilePictureUrl = Objects.isNull(pageIdVsName.get(pageId)) ? null : pageIdVsName.get(pageId).getProfilePictureUrl();
                    String pageLink = Objects.isNull(pageIdVsName.get(pageId)) ? null : pageIdVsName.get(pageId).getPageLink();
                    String channel = Objects.isNull(pageIdVsName.get(pageId)) ? null : pageIdVsName.get(pageId).getChannel();


                    CompetitorEngagementESData engagementESData = CompetitorEngagementESData.builder()
                            .reactions(roundToTwoDecimalPlaces(reactionCount))
                            .shares(SocialChannel.INSTAGRAM.getName().equals(channel) ? null : roundToTwoDecimalPlaces(shareCount))
                            .comments(roundToTwoDecimalPlaces(commentCount))
                            .totalEngagement(roundToTwoDecimalPlaces(totalEngagement))
                            .publicEngagementPerPost(roundToTwoDecimalPlaces(engagementRate))
                            .profilePictureUrl(profilePictureUrl)
                            .pageLink(pageLink)
                            .build();

                    data.put(pageId, engagementESData);

                } catch (Exception e) {
                    LOG.info("getTotalAudience CompetitorEsChannelWise Parse exception occurred : {}", e.getMessage());
                }
            }

        } catch (Exception ex) {
            LOG.info("getTotalAudience CompetitorEsChannelWise exception occurred : {}", ex.getMessage());
        }
        return CompetitorEngagementReportESData.builder().data(data).build();
    }

    public Optional<SearchResponse> createEsRequestForSelfPubBehaviourAudienceEngSummary(Date startDate, Date endDate, List<String> pageIds) {
        try {
            LOG.info("indexes: {}", ElasticConstants.POST_INSIGHTS.getName());
            SearchRequest searchRequest = new SearchRequest(ElasticConstants.POST_INSIGHTS.getName());
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            // Building the bool query
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termsQuery("page_id", pageIds))
                    .filter(QueryBuilders.rangeQuery("posted_date")
                            .from(DateTimeUtils.localToESFormat(startDate))
                            .to(DateTimeUtils.localToESFormat(endDate))
                            .includeLower(true)
                            .includeUpper(true)
                            .format(YYYY_MM_DD_HH_MM_SS));

            // Building the aggregations
            SumAggregationBuilder likeCountAgg = AggregationBuilders.sum(LIKE_COUNT).field("like_count");
            SumAggregationBuilder commentCountAgg = AggregationBuilders.sum(COMMENT_COUNT).field("comment_count");
            SumAggregationBuilder shareCountAgg = AggregationBuilders.sum(SHARE_COUNT).field("share_count");
            ValueCountAggregationBuilder postCountAgg = AggregationBuilders.count(POST_COUNT).field("post_id");

            // Adding aggregations to the search request
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.aggregation(likeCountAgg);
            searchSourceBuilder.aggregation(commentCountAgg);
            searchSourceBuilder.aggregation(shareCountAgg);
            searchSourceBuilder.aggregation(postCountAgg);
            searchSourceBuilder.size(0); // Setting size to 0 because we only need aggregations

            // Setting the search source to the search request
            searchRequest.source(searchSourceBuilder);

            return Optional.ofNullable(esService.search(searchRequest));

        } catch (Exception ex) {
            LOG.info("Exception occurred while creating engagement summary request: {}", ex.getMessage());
            return Optional.empty();
        }
    }
    public CompetitorSummaryResponse getCompetitorPubAndAudSummary(CompetitorProfileDetailsRequestDTO requestDTO, List<String> pageIds){
        Optional<SearchResponse> searchResponse;
        CompetitorSummaryResponse reportESData = CompetitorSummaryResponse.builder()
                .audienceGrowth(0.0)
                .audienceEngagements(0.0)
                .publishingBehaviour(0.0)
                .build();
        searchResponse = createEsRequestForPubBehaviourAudienceEngSummary(requestDTO.getStartDate(), requestDTO.getEndDate(), pageIds);
        if (searchResponse.isPresent()) {
            reportESData = createEsReportDataForCompetitorSummary(searchResponse.get());
        }
        return reportESData;
    }
    public CompetitorSummaryResponse getCompetitorProfileSummary(CompetitorProfileDetailsRequestDTO requestDTO, List<String> pageIds){
        Optional<SearchResponse> profileSearchResponse;
        CompetitorSummaryResponse profileReportESData = CompetitorSummaryResponse.builder()
                .audienceGrowth(0.0)
                .audienceEngagements(0.0)
                .publishingBehaviour(0.0)
                .build();
        profileSearchResponse = createEsRequestForCompetitorProfileSummary(requestDTO.getStartDate(), requestDTO.getEndDate(), pageIds);
        if (profileSearchResponse.isPresent()) {
            profileReportESData = createEsReportDataForCompetitorProfileSummary(profileSearchResponse.get());
        }
        return profileReportESData;
    }
    public CompetitorSummaryResponse getSelfPubAndAudSummary(CompetitorProfileDetailsRequestDTO requestDTO, List<String> pageIds){
        Optional<SearchResponse> searchResponse;
        CompetitorSummaryResponse reportESData = CompetitorSummaryResponse.builder()
                .audienceGrowth(0.0)
                .audienceEngagements(0.0)
                .publishingBehaviour(0.0)
                .build();
        searchResponse = createEsRequestForSelfPubBehaviourAudienceEngSummary(requestDTO.getStartDate(), requestDTO.getEndDate(), pageIds);
        if (searchResponse.isPresent()) {
            reportESData = createEsReportDataForSelfSummary(searchResponse.get());
        }
        return reportESData;
    }
    public CompetitorSummaryResponse getSelfProfileSummary(CompetitorProfileDetailsRequestDTO requestDTO, List<String> pageIds){
        Optional<SearchResponse> profileSearchResponse;
        CompetitorSummaryResponse profileReportESData = CompetitorSummaryResponse.builder()
                .audienceGrowth(0.0)
                .audienceEngagements(0.0)
                .publishingBehaviour(0.0)
                .build();
        profileSearchResponse = createEsRequestForSelfProfileSummary(requestDTO.getStartDate(), requestDTO.getEndDate(), pageIds);
        if (profileSearchResponse.isPresent()) {
            profileReportESData = createEsReportDataForSelfProfileSummary(profileSearchResponse.get());
        }
        return profileReportESData;
    }

    public Optional<SearchResponse> createEsRequestForPubBehaviourAudienceEngSummary(Date startDate, Date endDate, List<String> pageIds) {
        try {
            LOG.info("indexes: {}", ElasticConstants.COMPETITOR_POSTS.getName());
            SearchRequest searchRequest = new SearchRequest(ElasticConstants.COMPETITOR_POSTS.getName());
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            // Building the bool query
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termsQuery("pageId", pageIds))
                    .filter(QueryBuilders.rangeQuery("publishedDate")
                            .from(DateTimeUtils.localToESFormat(startDate))
                            .to(DateTimeUtils.localToESFormat(endDate))
                            .includeLower(true)
                            .includeUpper(true)
                            .format(YYYY_MM_DD_HH_MM_SS));

            // Building the aggregations
            SumAggregationBuilder likeCountAgg = AggregationBuilders.sum(LIKE_COUNT).field(LIKE_COUNT);
            SumAggregationBuilder shareCountAgg = AggregationBuilders.sum(SHARE_COUNT).field(SHARE_COUNT);
            SumAggregationBuilder commentCountAgg = AggregationBuilders.sum(COMMENT_COUNT).field(COMMENT_COUNT);
            ValueCountAggregationBuilder postCountAgg = AggregationBuilders.count(POST_COUNT).field("_index");

            // Adding aggregations to the search request
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.aggregation(likeCountAgg);
            searchSourceBuilder.aggregation(shareCountAgg);
            searchSourceBuilder.aggregation(commentCountAgg);
            searchSourceBuilder.aggregation(postCountAgg);
            searchSourceBuilder.size(0); // Setting size to 0 because we only need aggregations

            // Setting the search source to the search request
            searchRequest.source(searchSourceBuilder);

            return Optional.of(esService.search(searchRequest));

        } catch (Exception ex) {
            LOG.info("Exception occurred while creating competitor posts request: {}", ex.getMessage());
            return Optional.empty();
        }
    }
    public CompetitorSummaryResponse createEsReportDataForSelfSummary(SearchResponse searchResponse) {
        try {
            Aggregations aggregations = searchResponse.getAggregations();
            if (Objects.isNull(aggregations)) {
                LOG.info(EMPTY_AGGREGATIONS_LOG);
                throw new BirdeyeSocialException(EMPTY_AGGREGATIONS_LOG);
            }

            ParsedSum likeCount = aggregations.get(LIKE_COUNT);
            ParsedSum commentCount = aggregations.get(COMMENT_COUNT);
            ParsedSum shareCount = aggregations.get(SHARE_COUNT);
            ParsedValueCount postCount = aggregations.get(POST_COUNT);

            double audienceEngagements = (Objects.nonNull(likeCount)? likeCount.getValue(): 0.0)
                    + (Objects.nonNull(commentCount)? commentCount.getValue(): 0.0)
                    + (Objects.nonNull(shareCount)? shareCount.getValue(): 0.0);
            LOG.info("audienceEngagements: {} and postCount : {}", audienceEngagements, postCount.value());
            return CompetitorSummaryResponse.builder()
                    .audienceEngagements(roundToTwoDecimalPlaces(audienceEngagements))
                    .publishingBehaviour(roundToTwoDecimalPlaces(postCount.value()))
                    .build();

        } catch (Exception ex) {
            LOG.info("Exception occurred while creating engagement summary report data: {}", ex.getMessage());
        }
        return CompetitorSummaryResponse.builder()
                .audienceEngagements(0.0)
                .publishingBehaviour(0.0)
                .audienceGrowth(0.0)
                .build();
    }
    public CompetitorSummaryResponse createEsReportDataForCompetitorSummary(SearchResponse searchResponse) {
        try {
            Aggregations aggregations = searchResponse.getAggregations();
            if (Objects.isNull(aggregations)) {
                LOG.info(EMPTY_AGGREGATIONS_LOG);
                throw new BirdeyeSocialException(EMPTY_AGGREGATIONS_LOG);
            }

            ParsedSum likeCount = aggregations.get(LIKE_COUNT);
            ParsedSum shareCount = aggregations.get(SHARE_COUNT);
            ParsedSum commentCount = aggregations.get(COMMENT_COUNT);
            ParsedValueCount postCount = aggregations.get(POST_COUNT);

            double audienceEngagements = (Objects.nonNull(likeCount)? likeCount.getValue(): 0.0)
                    + (Objects.nonNull(shareCount)? shareCount.getValue(): 0.0)
                    + (Objects.nonNull(commentCount)? commentCount.getValue(): 0.0);
            LOG.info("audienceEngagements: {} and postCount : {}", audienceEngagements, postCount.value());
            return CompetitorSummaryResponse.builder()
                    .audienceEngagements(roundToTwoDecimalPlaces(audienceEngagements))
                    .publishingBehaviour(roundToTwoDecimalPlaces(postCount.value()))
                    .build();
        } catch (Exception ex) {
            LOG.info("Exception occurred while creating competitor posts report data: {}", ex.getMessage());
        }
        return CompetitorSummaryResponse.builder()
                .audienceEngagements(0.0)
                .publishingBehaviour(0.0)
                .audienceGrowth(0.0)
                .build();
    }

    public Optional<SearchResponse> createEsRequestForCompetitorProfileSummary(Date startDate, Date endDate, List<String> pageIds) {
        try {
            LOG.info("indexes: {} {} {}", ElasticConstants.COMPETITOR_FB_PROFILE_INSIGHTS.getName(),
                    ElasticConstants.COMPETITOR_INSTA_PROFILE_INSIGHTS.getName(),
                    ElasticConstants.COMPETITOR_TWITTER_PROFILE_INSIGHTS.getName());

            SearchRequest searchRequest = new SearchRequest(
                    ElasticConstants.COMPETITOR_FB_PROFILE_INSIGHTS.getName(),
                    ElasticConstants.COMPETITOR_INSTA_PROFILE_INSIGHTS.getName(),
                    ElasticConstants.COMPETITOR_TWITTER_PROFILE_INSIGHTS.getName()
            );
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            // Build the query
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termsQuery("pageId", pageIds))
                    .filter(QueryBuilders.rangeQuery("reportDate")
                            .from(DateTimeUtils.localToESFormat(startDate))
                            .to(DateTimeUtils.localToESFormat(endDate))
                            .includeLower(true)
                            .includeUpper(true)
                            .format(YYYY_MM_DD_HH_MM_SS));

            // Build the aggregation
            TermsAggregationBuilder groupByPageId = AggregationBuilders.terms("group_by_page_id")
                    .field("pageId")
                    .size(10000)
                    .order(BucketOrder.key(true)) // Ordering by key (pageId)
                    .subAggregation(AggregationBuilders.topHits("latest_entry")
                            .sort(SortBuilders.fieldSort("reportDate").order(SortOrder.DESC))
                            .size(1)
                            .fetchSource(new String[] {FOLLOWER_COUNT}, null));

            // Build the search source
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.aggregation(groupByPageId);
            searchSourceBuilder.size(0); // No hits needed, only aggregations

            // Set the search source to the search request
            searchRequest.source(searchSourceBuilder);

            // Execute the search request and return the response
            return Optional.of(esService.search(searchRequest));
        }catch (Exception ex) {
            LOG.info("Exception occurred while creating competitor profile summary request: {}", ex.getMessage());
            return Optional.empty();
        }
    }

    public Optional<SearchResponse> createEsRequestForSelfProfileSummary(Date startDate, Date endDate, List<String> pageIds) {
        try {
            LOG.info("indexes: {} {} {}", ElasticConstants.FACEBOOK_PAGE_INSIGHTS.getName(),
                    ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName(),
                    ElasticConstants.TWITTER_PAGE_INSIGHTS.getName());

            // Create a search request for the specified indexes
            SearchRequest searchRequest = new SearchRequest(ElasticConstants.FACEBOOK_PAGE_INSIGHTS.getName(),
                    ElasticConstants.INSTAGRAM_PAGE_INSIGHTS.getName(),
                    ElasticConstants.TWITTER_PAGE_INSIGHTS.getName());
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            // Build the query
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termsQuery("page_id", pageIds))
                    .filter(QueryBuilders.rangeQuery("day")
                            .from(DateTimeUtils.localToESFormat(startDate))
                            .to(DateTimeUtils.localToESFormat(endDate))
                            .includeLower(true)
                            .includeUpper(true)
                            .format(YYYY_MM_DD_HH_MM_SS));

            // Build the aggregation
            TermsAggregationBuilder groupByPageIdAggregation = AggregationBuilders
                    .terms("group_by_page_id")
                    .field("page_id")
                    .size(10000)
                    .subAggregation(AggregationBuilders.topHits("latest_entry")
                            .sort(SortBuilders.fieldSort("day").order(SortOrder.DESC))
                            .fetchSource(new String[] {TOTAL_FOLLOWER_COUNT}, null)
                            .size(1));

            // Build the search source
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.aggregation(groupByPageIdAggregation);
            searchSourceBuilder.size(0); // No hits needed, only aggregations

            // Set the search source to the search request
            searchRequest.source(searchSourceBuilder);

            // Execute the search request and return the response
            return Optional.of(esService.search(searchRequest));
        } catch (Exception ex) {
            LOG.info("Exception occurred while creating Facebook page insights request: {}", ex.getMessage());
            return Optional.empty();
        }
    }

    public CompetitorSummaryResponse createEsReportDataForCompetitorProfileSummary(SearchResponse searchResponse) {
        try {
            Aggregations aggregations = searchResponse.getAggregations();
            if (Objects.isNull(aggregations)) {
                LOG.info(EMPTY_AGGREGATIONS_LOG);
                throw new BirdeyeSocialException(EMPTY_AGGREGATIONS_LOG);
            }

            ParsedTerms groupByPageId = aggregations.get("group_by_page_id");
            double totalFollowerCount = 0.0;

            for (Terms.Bucket bucket : groupByPageId.getBuckets()) {
                ParsedTopHits latestEntry = bucket.getAggregations().get("latest_entry");
                SearchHit latestHit = latestEntry.getHits().getAt(0);
                Map<String, Object> sourceAsMap = latestHit.getSourceAsMap();
                totalFollowerCount += Double.parseDouble(sourceAsMap.get(FOLLOWER_COUNT).toString());
            }

            LOG.info("totalFollowerCount: {}", totalFollowerCount);
            return CompetitorSummaryResponse.builder()
                    .audienceGrowth(roundToTwoDecimalPlaces(totalFollowerCount))
                    .build();

        } catch (Exception ex) {
            LOG.info("Exception occurred while creating follower summary report data: {}", ex.getMessage());
        }
        return CompetitorSummaryResponse.builder()
                .audienceEngagements(0.0)
                .publishingBehaviour(0.0)
                .audienceGrowth(0.0)
                .build();
    }

    public CompetitorSummaryResponse createEsReportDataForSelfProfileSummary(SearchResponse searchResponse) {
        try {
            Aggregations aggregations = searchResponse.getAggregations();
            if (Objects.isNull(aggregations)) {
                LOG.info(EMPTY_AGGREGATIONS_LOG);
                throw new BirdeyeSocialException(EMPTY_AGGREGATIONS_LOG);
            }

            ParsedTerms groupByPageId = aggregations.get("group_by_page_id");
            double totalFollowerCountSum = 0;

            if (Objects.nonNull(groupByPageId)) {
                for (Terms.Bucket bucket : groupByPageId.getBuckets()) {
                    TopHits latestEntryHits = bucket.getAggregations().get("latest_entry");
                    for (SearchHit hit : latestEntryHits.getHits()) {
                        Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                        Object followerCountObj = sourceAsMap.get(TOTAL_FOLLOWER_COUNT);
                        if (Objects.nonNull(followerCountObj)) {
                            totalFollowerCountSum += Double.parseDouble(followerCountObj.toString());
                        }
                    }
                }
            }

            LOG.info("totalFollowerCountSum: {}", totalFollowerCountSum);
            return CompetitorSummaryResponse.builder()
                    .audienceGrowth(roundToTwoDecimalPlaces(totalFollowerCountSum))
                    .build();
        } catch (Exception ex) {
            LOG.info("Exception occurred while creating Facebook page insights report data: {}", ex.getMessage());
        }
        return CompetitorSummaryResponse.builder()
                .audienceEngagements(0.0)
                .publishingBehaviour(0.0)
                .audienceGrowth(0.0)
                .build();
    }

    public Double roundToTwoDecimalPlaces(Double value) {
        if(value == null) return null;
        return Math.round(value * 10.0) / 10.0;
    }
    public CompetitorSummaryResponse getCompetitorSummary(CompetitorSummaryResponse reportESData, CompetitorSummaryResponse profileReportESData, List<String> pageIds){
        Double averageAudienceGrowth = roundToTwoDecimalPlaces(profileReportESData.getAudienceGrowth() / pageIds.size());
        Double averageAudienceEngagements = roundToTwoDecimalPlaces(reportESData.getAudienceEngagements() / pageIds.size());
        Double averagePublishingBehaviour = roundToTwoDecimalPlaces(reportESData.getPublishingBehaviour() / pageIds.size());
        return CompetitorSummaryResponse.builder()
                .audienceGrowth(averageAudienceGrowth)
                .audienceEngagements(averageAudienceEngagements)
                .publishingBehaviour(averagePublishingBehaviour)
                .build();
    }
    public Optional<SearchResponse> createEsRequestForTopCompetitorPosts(Date startDate, Date endDate, List<String> pageIds, Integer size) {
        try {
            LOG.info("index: {}", ElasticConstants.COMPETITOR_POSTS.getName());
            SearchRequest searchRequest = new SearchRequest(ElasticConstants.COMPETITOR_POSTS.getName());
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            // Building the bool query
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termsQuery("pageId", pageIds))
                    .must(QueryBuilders.rangeQuery("publishedDate")
                            .from(DateTimeUtils.localToESFormat(startDate))
                            .to(DateTimeUtils.localToESFormat(endDate))
                            .includeLower(true)
                            .includeUpper(true)
                            .format(YYYY_MM_DD_HH_MM_SS));

            // Building the search source builder
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.sort("engagement", SortOrder.DESC); // Sorting by engagement in descending order
            searchSourceBuilder.size(size); // Limit to size results

            // Setting the search source to the search request
            searchRequest.source(searchSourceBuilder);

            return Optional.of(esService.search(searchRequest));

        } catch (Exception ex) {
            LOG.info("Exception occurred while creating competitor posts request: {}", ex.getMessage());
            return Optional.empty();
        }
    }
    public Optional<SearchResponse> createEsRequestForTopPostInsight(Date startDate, Date endDate, List<String> pageIds, Integer size) {
        try {
            LOG.info("index: {}", ElasticConstants.POST_INSIGHTS.getName());
            SearchRequest searchRequest = new SearchRequest(ElasticConstants.POST_INSIGHTS.getName());
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            // Building the bool query
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termsQuery("page_id", pageIds))
                    .must(QueryBuilders.rangeQuery("day")
                            .from(DateTimeUtils.localToESFormat(startDate))
                            .to(DateTimeUtils.localToESFormat(endDate))
                            .includeLower(true)
                            .includeUpper(true)
                            .format(YYYY_MM_DD_HH_MM_SS));

            // Building the search source builder
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.sort("engagement", SortOrder.DESC); // Sorting by engagement in descending order
            searchSourceBuilder.size(size); // Limit to size results

            // Setting the search source to the search request
            searchRequest.source(searchSourceBuilder);

            return Optional.of(esService.search(searchRequest));

        } catch (Exception ex) {
            LOG.info("Exception occurred while creating post insight request: {}", ex.getMessage());
            return Optional.empty();
        }
    }

    public CompetitorPostResponse createEsResponseForTopCompetitorPosts(SearchResponse searchResponse, Map<String, CompetitorPageDetails> pageDetailsMap) {
        CompetitorPostResponse response = new CompetitorPostResponse();

        try {
            SearchHits searchHits = searchResponse.getHits();
            List<CompetitorPostData> postDataList = new ArrayList<>();

            for (SearchHit hit : searchHits) {
                CompetitorPostData postData = new CompetitorPostData();
                Map<String, Object> sourceAsMap = hit.getSourceAsMap();

                if(pageDetailsMap.containsKey((String) sourceAsMap.get("pageId"))) {
                    CompetitorPageDetails pageDetails = pageDetailsMap.get((sourceAsMap.get("pageId")));
                    postData.setChannel(pageDetails.getChannel());
                    postData.setName(pageDetails.getPageName());
                    postData.setProfilePictureUrl(pageDetails.getProfilePictureUrl());
                }

                postData.setPageId((String) sourceAsMap.get("pageId"));
                postData.setPostId((String) sourceAsMap.get("postId"));
                postData.setPostUrl((String) sourceAsMap.get("postUrl"));
                postData.setPostText((String) sourceAsMap.get("postText"));
                postData.setImageUrls((List<String>) sourceAsMap.get("imageUrls"));
                postData.setVideoUrls((List<String>) sourceAsMap.get("videoUrls"));
                postData.setThumbnailUrls((List<String>) sourceAsMap.get("thumbnailUrls"));
                postData.setLikeCount(Objects.isNull(sourceAsMap.get("likeCount")) ? 0 : (Integer) sourceAsMap.get("likeCount"));
                postData.setCommentCount(Objects.isNull(sourceAsMap.get("commentCount")) ? 0 :  (Integer) sourceAsMap.get("commentCount"));
                if(SocialChannel.INSTAGRAM.getName().equals(postData.getChannel())) {
                    postData.setShareCount(0);
                } else {
                    postData.setShareCount(Objects.isNull(sourceAsMap.get("shareCount")) ? 0 : (Integer) sourceAsMap.get("shareCount"));
                }
                postData.setEngagement(Objects.isNull(sourceAsMap.get("engagement")) ? 0 : (Integer) sourceAsMap.get("engagement"));

                String publishedDateStr = (String) sourceAsMap.get("publishedDate");
                SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
                Date publishedDate = sdf.parse(publishedDateStr);
                postData.setPublishedDate(publishedDate.getTime());

                postData.setUserName((String) sourceAsMap.get("userName"));

                // Assuming imageMetaData, videoMetaData, thumbnailMetaData are not available in the provided document
                postData.setImageMetaData(null);
                postData.setVideoMetaData(null);
                postData.setThumbnailMetaData(null);

                postData.setIsVerified(null); // Verified status not available in the document
                postData.setIsCompetitor(true);
                postDataList.add(postData);
            }

            response.setPostData(postDataList);
            response.setTotalCount(searchHits.getTotalHits().value);
            response.setPostsFetchedForAll(true); // Set based on your logic
            response.setPostsFetchedForAtLeastOne(true); // Set based on your logic
            LOG.info("Competitor postData size: {}", postDataList.size());
        } catch (Exception ex) {
            LOG.info("Exception occurred while processing competitor posts response: {}", ex.getMessage());
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Error occurred while processing competitor posts response");
        }
        return response;
    }
    public CompetitorPostResponse createEsResponseForTopPostInsight(SearchResponse searchResponse, Map<String, CompetitorPageDetails> pageDetailsMap) {
        CompetitorPostResponse response = new CompetitorPostResponse();

        try {
            SearchHits searchHits = searchResponse.getHits();
            List<CompetitorPostData> postDataList = new ArrayList<>();

            for (SearchHit hit : searchHits) {
                CompetitorPostData postData = new CompetitorPostData();
                Map<String, Object> sourceAsMap = hit.getSourceAsMap();


                if(pageDetailsMap.containsKey((String) sourceAsMap.get("page_id"))) {
                    CompetitorPageDetails pageDetails = pageDetailsMap.get((sourceAsMap.get("page_id")));
                    postData.setChannel(pageDetails.getChannel());
                    postData.setName(pageDetails.getPageName());
                    postData.setProfilePictureUrl(pageDetails.getProfilePictureUrl());
                }

                postData.setPageId((String) sourceAsMap.get("page_id"));
                postData.setPostId((String) sourceAsMap.get("post_id"));
                postData.setPostUrl((String) sourceAsMap.get("post_url"));
                postData.setPostText((String) sourceAsMap.get("post_content"));
                postData.setImageUrls((List<String>) sourceAsMap.get("image_urls"));
                postData.setVideoUrls((List<String>) sourceAsMap.get("video_urls"));
                postData.setThumbnailUrls((List<String>) sourceAsMap.get("thumbnail_urls"));
                postData.setLikeCount(Objects.isNull(sourceAsMap.get("like_count")) ? 0 : (Integer) sourceAsMap.get("like_count"));
                postData.setCommentCount(Objects.isNull(sourceAsMap.get("comment_count")) ? 0 :  (Integer) sourceAsMap.get("comment_count"));
                if(SocialChannel.INSTAGRAM.getName().equals(postData.getChannel())) {
                    postData.setShareCount(0);
                } else {
                    postData.setShareCount(Objects.isNull(sourceAsMap.get("share_count")) ? 0 : (Integer) sourceAsMap.get("share_count"));
                }
                postData.setEngagement(Objects.isNull(sourceAsMap.get("engagement")) ? 0 : (Integer) sourceAsMap.get("engagement"));

                String publishedDateStr = (String) sourceAsMap.get("day");
                SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
                Date publishedDate = sdf.parse(publishedDateStr);
                postData.setPublishedDate(publishedDate.getTime());

                postData.setUserName((String) sourceAsMap.get("user_name"));

                // Assuming imageMetaData, videoMetaData, thumbnailMetaData are not available in the provided document
                postData.setImageMetaData(null);
                postData.setVideoMetaData(null);
                postData.setThumbnailMetaData(null);

                postData.setIsVerified((Boolean) sourceAsMap.get("is_verified"));
                postData.setIsCompetitor(false);

                postDataList.add(postData);
            }

            response.setPostData(postDataList);
            response.setTotalCount(searchHits.getTotalHits().value);
            response.setPostsFetchedForAll(true); // Set based on your logic
            response.setPostsFetchedForAtLeastOne(true); // Set based on your logic
            LOG.info("PostData size: {}", postDataList.size());
        } catch (Exception ex) {
            LOG.info("Exception occurred while processing post insight response: {}", ex.getMessage());
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Error occurred while processing post insight response");
        }
        return response;
    }

    public CompetitorPostResponse combineTopPostsByEngagement(CompetitorPostResponse competitorResponse, CompetitorPostResponse selfResponse, Integer size) {
        CompetitorPostResponse response = new CompetitorPostResponse();
        try {
            List<CompetitorPostData> competitorPosts = Optional.ofNullable(competitorResponse.getPostData()).orElse(Collections.emptyList());
            List<CompetitorPostData> selfPosts = Optional.ofNullable(selfResponse.getPostData()).orElse(Collections.emptyList());

            // Combine the lists of posts
            List<CompetitorPostData> combinedPosts = new ArrayList<>(competitorPosts);
            combinedPosts.addAll(selfPosts);

            // Sort the combined list by engagement in descending order
            List<CompetitorPostData> topPosts = combinedPosts.stream()
                    .filter(Objects::nonNull) // Remove any null entries
                    .sorted(Comparator.comparingInt(CompetitorPostData::getEngagement).reversed())
                    .limit(size) // Limit the result to the top 'size' posts
                    .collect(Collectors.toList());

            // Set the response fields
            response.setPostData(topPosts);
            response.setTotalCount((long) topPosts.size()); // Total count is the size of the top posts list
            LOG.info("Top posts by engagement size: {}", topPosts.size());
        } catch (Exception ex) {
            LOG.info("Exception occurred while combining top posts by engagement: {}", ex.getMessage());
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Error occurred while combining top posts by engagement");
        }
        return response;
    }
    public CompetitorPostResponse getCompetitorTopPosts(CompetitorAllProfilesDetailsRequestDTO request, Integer size) {
        List<String> channels =  getCompetitorProfileSourceName(request.getCompetitorChannelWisePage());
        if(CollectionUtils.isEmpty(channels)) {
            LOG.info("No valid channels in request {}", request);
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, "No valid channels in request");
        }

        Map<String, CompetitorPageDetails> pageDetailsMap = processPageIdVsNameTopPost(channels, request, true);


        List<String> pageIds =new ArrayList<>();
        for(String channel: channels){
            pageIds.addAll(request.getCompetitorChannelWisePage().getPageIdsByChannel(channel));
        }
        pageIds = pageIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(pageIds)) {
            LOG.info("No valid pageIds in request {}", request);
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, "No valid pageIds in request");
        }
        Optional<SearchResponse> competitorSearchResponse;

        competitorSearchResponse = createEsRequestForTopCompetitorPosts(request.getStartDate(), request.getEndDate(), pageIds, size);
        if (competitorSearchResponse.isPresent()) {
            return createEsResponseForTopCompetitorPosts(competitorSearchResponse.get(), pageDetailsMap);
        }
        LOG.info("No valid response from ES for competitor top posts");
        return new CompetitorPostResponse();
    }

    public CompetitorPostResponse getSelfProfileTopPosts(CompetitorAllProfilesDetailsRequestDTO request, Integer size) {
        List<String> channels =  getCompetitorProfileSourceName(request.getSelfChannelWisePage());
        if(CollectionUtils.isEmpty(channels)) {
            LOG.info("No valid channels in request {}", request);
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, "No valid channels in request");
        }
        List<String> pageIds =new ArrayList<>();
        for(String channel: channels){
            pageIds.addAll(request.getSelfChannelWisePage().getPageIdsByChannel(channel));
        }

        Map<String, CompetitorPageDetails> pageDetailsMap = processPageIdVsNameTopPost(channels, request, false);


        pageIds = pageIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(pageIds)) {
            LOG.info("No valid pageIds in request {}", request);
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, "No valid pageIds in request");
        }
        Optional<SearchResponse> selfSearchResponse;
        selfSearchResponse = createEsRequestForTopPostInsight(request.getStartDate(), request.getEndDate(), pageIds, size);
        if(selfSearchResponse.isPresent()){
            return createEsResponseForTopPostInsight(selfSearchResponse.get(), pageDetailsMap);
        }
        LOG.info("No valid response from ES for self top posts");
        return new CompetitorPostResponse();
    }


    public List<CompetitorExcelESData> getCompetitorExcelEsData(CompetitorAllProfilesDetailsRequestDTO request, Integer pageSize, Integer startIndex) {
        List<String> channels =  getCompetitorProfileSourceName(request.getCompetitorChannelWisePage());
        if(CollectionUtils.isEmpty(channels)) {
            LOG.info("No valid channels in request {}", request);
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, "No valid channels in request");
        }
        List<String> competitorPageIds = new ArrayList<>();

        for(String channel: channels) {
            competitorPageIds.addAll(request.getCompetitorChannelWisePage().getPageIdsByChannel(channel));
        }

        // competitor Data.
        List<String> indicesName = getCompetitorProfileEsIndex(channels);

        // fetch post data via ES at once.
        List<CompetitorExcelESData> profileInsightESList = getCompetitorsProfileDataES(request.getStartDate(), request.getEndDate(), competitorPageIds, indicesName, pageSize, startIndex);

        // fetch profile data via ES at once.
        SearchResponse postInsightsResponse = getCompetitorPostDataES(request.getStartDate(), request.getEndDate(), competitorPageIds, indicesName);
        List<CompetitorExcelESData> esDataList = createEsReportDataForExcel(postInsightsResponse);

        mergeCompetitorPostAndProfileData(esDataList,  profileInsightESList);

        return profileInsightESList;
    }

    public List<CompetitorExcelESData> createEsReportDataForExcel(SearchResponse searchResponse) {
        List<CompetitorExcelESData> response = new ArrayList<>();
        try {
            Aggregations aggregations = searchResponse.getAggregations();
            if(Objects.isNull(aggregations)){
                LOG.info(EMPTY_AGGREGATIONS_LOG);
                return null;
            }

            ParsedStringTerms dataByPageId = aggregations.get(BY_PAGE_ID);

            List<? extends Terms.Bucket> buckets = dataByPageId.getBuckets();
            if(Objects.isNull(buckets)) {
                return null;
            }

            for (Terms.Bucket bucket : buckets) {
                try {
                    // pageId
                    String pageId = bucket.getKeyAsString();

                    ParsedDateHistogram dateHistogram = bucket.getAggregations().get("d_histogram");

                    List<? extends Histogram.Bucket> histogramBuckets = dateHistogram.getBuckets();

                    for(Histogram.Bucket hBucket : histogramBuckets) {

                        String dateAsStr = hBucket.getKeyAsString();
                        ParsedSum imageUrlCount = hBucket.getAggregations().get(IMAGE_URL_COUNT);
                        ParsedSum videoUrlCount = hBucket.getAggregations().get(VIDEO_URL_COUNT);
                        ParsedValueCount postCount = hBucket.getAggregations().get(POST_COUNT);
                        ParsedSum linkCount = hBucket.getAggregations().get(LINK_COUNT);

                        // engagement Count
                        ParsedSum likeCount = hBucket.getAggregations().get(LIKE_COUNT);
                        ParsedSum commentCount = hBucket.getAggregations().get(COMMENT_COUNT);
                        ParsedSum shareCount = hBucket.getAggregations().get(SHARE_COUNT);
                        ParsedSum textPostCount = hBucket.getAggregations().get(TEXT_POST_COUNT);

                        ParsedMax sourceId = hBucket.getAggregations().get("sourceId");

                        int engagement = (int) commentCount.value() + (int) shareCount.getValue() + (int) likeCount.getValue();
                        double engagementPerPost = postCount.getValue() == 0 ?  0 : (engagement)/ postCount.getValue();

                        CompetitorExcelESData excelESData = CompetitorExcelESData.builder()
                                .publishedPhotos(Objects.nonNull(imageUrlCount) && Objects.nonNull(imageUrlCount.getValue()) ? (int) imageUrlCount.value() : 0)
                                .publishedVideos(Objects.nonNull(videoUrlCount) && Objects.nonNull(videoUrlCount.getValue()) ? (int) videoUrlCount.value() : 0)
                                .publishedPost(Objects.nonNull(postCount.getValue()) ? (int) postCount.value() : 0)
                                .publishedLinks(Objects.nonNull(linkCount.getValue()) ? (int) linkCount.value() : 0)
                                .reactions(Objects.nonNull(likeCount.getValue()) ? (int) likeCount.value() : 0)
                                .shares(Objects.nonNull(shareCount.getValue()) ? (int) shareCount.value() : 0)
                                .comments(Objects.nonNull(commentCount.getValue()) ? (int) commentCount.value() : 0)
                                .publishedText(Objects.nonNull(textPostCount.getValue()) ? (int) textPostCount.value() : 0)
                                .date(dateAsStr)
                                .profile(pageId)
                                .network(SocialChannel.getSocialChannelNameById((int) sourceId.getValue()))
                                .publicEngagement(engagement)
                                .publicEngagementsPerPost(engagementPerPost)
                                .build();

                        response.add(excelESData);
                    }

                } catch (Exception e) {
                    LOG.info("getTotalAudience CompetitorEsChannelWise Parse exception occurred : {}", e.getMessage());
                }
            }

        } catch (Exception ex) {
            LOG.info("getTotalAudience CompetitorEsChannelWise exception occurred : {}", ex.getMessage());
        }
        return response;
    }

    private void mergeCompetitorPostAndProfileData(List<CompetitorExcelESData> competitorExcelESData, List<CompetitorExcelESData> profileInsightsES) {


            for(CompetitorExcelESData profileEsDocument :  profileInsightsES) {
                for (CompetitorExcelESData postData : competitorExcelESData) {
                    if (DateTimeUtils.isSameDay(DateTimeUtils.convertStringDateToDate(postData.getDate()),
                            DateTimeUtils.convertStringDateToDate(profileEsDocument.getDate()))
                            && (postData.getProfile().equals(profileEsDocument.getProfile()))) {


                        profileEsDocument.setPublishedPost(postData.getPublishedPost());
                        profileEsDocument.setPublishedVideos(postData.getPublishedVideos());
                        profileEsDocument.setPublishedPhotos(postData.getPublishedPhotos());
                        profileEsDocument.setPublishedLinks(postData.getPublishedLinks());
                        profileEsDocument.setReactions(postData.getReactions());
                        profileEsDocument.setComments(postData.getComments());
                        profileEsDocument.setShares(postData.getShares());
                        profileEsDocument.setPublicEngagement(postData.getPublicEngagement());
                        profileEsDocument.setPublicEngagementsPerPost(postData.getPublicEngagementsPerPost());
                        profileEsDocument.setPublishedText(postData.getPublishedText());

//                        profileEsDocument.setNetwork(postData.getNetwork());
//                        profileEsDocument.setProfile(postData.getProfile());

                    }
                }
            }

    }

    private void mergeSelfPostAndProfileData(List<CompetitorExcelESData> competitorExcelESData, List<CompetitorExcelESData> profileInsightsES) {

        for(CompetitorExcelESData postData:  competitorExcelESData) {

            for(CompetitorExcelESData profileEsData :  profileInsightsES) {
                if(DateTimeUtils.isSameDay(DateTimeUtils.convertStringDateToDate(postData.getDate()),
                        DateTimeUtils.convertStringDateToDate(profileEsData.getDate()))
                        && (postData.getProfile().equals(profileEsData.getProfile()))) {

                    profileEsData.setPublishedPost(postData.getPublishedPost());
                    profileEsData.setPublishedVideos(postData.getPublishedVideos());
                    profileEsData.setPublishedPhotos(postData.getPublishedPhotos());
                    profileEsData.setPublishedLinks(postData.getPublishedLinks());
                    profileEsData.setReactions(postData.getReactions());
                    profileEsData.setComments(postData.getComments());
                    profileEsData.setShares(postData.getShares());
                    profileEsData.setPublicEngagement(postData.getPublicEngagement());
                    profileEsData.setPublicEngagementsPerPost(postData.getPublicEngagementsPerPost());
                    profileEsData.setPublishedText(postData.getPublishedText());

                    profileEsData.setNetwork(postData.getNetwork());
                }
            }
        }

    }

    private static double getNetAudienceGrowth(ESPageRequest postsEsDocument, int followerCount) {
        int followerGain = Objects.isNull(postsEsDocument.getFollower_gain()) ? 0 : postsEsDocument.getFollower_gain();
        int followerLost = Objects.isNull(postsEsDocument.getFollower_lost()) ? 0 : postsEsDocument.getFollower_lost();
        int netGrowth = followerGain - followerLost;
        // TODO: simply this formula
        return followerCount == 0 ? 0.0 : ((double) netGrowth /followerCount) * 100;

    }

    public List<CompetitorExcelESData> getSelfExcelEsData(CompetitorAllProfilesDetailsRequestDTO request, Integer pageSize, Integer startIndex) {

        List<String> channels =  getCompetitorProfileSourceName(request.getSelfChannelWisePage());
        if(CollectionUtils.isEmpty(channels)) {
            LOG.info("No valid channels in request {}", request);
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, "No valid channels in request");
        }
        List<String> selfPageIds =new ArrayList<>();

        for(String channel: channels) {
            selfPageIds.addAll(request.getSelfChannelWisePage().getPageIdsByChannel(channel));
        }

        // competitor Data.
        List<String> indicesName = getSelfProfileEsIndices(channels);

        // fetch post data via ES at once.
        List<CompetitorExcelESData> profileInsightESList = getSelfProfileDataES(request.getStartDate(), request.getEndDate(), selfPageIds, indicesName, pageSize, startIndex);


        // fetch profile data via ES at once.
        SearchResponse postInsightsResponse = getSelfPostInsightDataES(request.getStartDate(), request.getEndDate(), selfPageIds);
        List<CompetitorExcelESData> esDataList = createEsReportDataForExcel(postInsightsResponse);

        mergeSelfPostAndProfileData(esDataList, profileInsightESList);

        return profileInsightESList;
    }

    public void formatExcelEsData(List<CompetitorExcelESData> esDataList, Map<String, CompetitorPageDetails> pageIdVsName) {

        if(CollectionUtils.isEmpty(esDataList) || MapUtils.isEmpty(pageIdVsName)) {
            LOG.info("No valid data found for page");
            return;
        }

        for(CompetitorExcelESData profileEsDocument: esDataList) {

            profileEsDocument.setNetwork(Objects.nonNull(pageIdVsName.get(profileEsDocument.getProfile())) ?  pageIdVsName.get(profileEsDocument.getProfile()).getChannel() : "");
            profileEsDocument.setProfile(Objects.nonNull(pageIdVsName.get(profileEsDocument.getProfile())) ?  pageIdVsName.get(profileEsDocument.getProfile()).getPageName() : "");
            profileEsDocument.setDate(DateTimeUtils.parseDateOnlyFormat(profileEsDocument.getDate()));


        }

    }

}

