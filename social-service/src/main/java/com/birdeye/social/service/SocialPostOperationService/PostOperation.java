package com.birdeye.social.service.SocialPostOperationService;


import com.birdeye.social.entities.ProcessingPost;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.model.PageInfoDto;

import java.util.List;
import java.util.Map;

import java.util.List;

public interface PostOperation {

    String channelName();
    void editPublishedPost(SocialPostPublishInfo publishInfo) throws Exception;

    void deletePost(SocialPostPublishInfo publishedPost) throws Exception;

    List<String> getAllPageIds(int page, int size, Long enterpriseId);

    void processPendingPosts(ProcessingPost processingPost, PageInfoDto pageInfoDto, SocialPostPublishInfo publishInfo);

    Map<String,PageInfoDto> getPageInfoDto(List<String> pageIds);
}
