/**
 * 
 */
package com.birdeye.social.service;

import com.birdeye.social.constant.MappingStatus;
import com.birdeye.social.constant.PageSortDirection;
import com.birdeye.social.constant.ResellerSearchType;
import com.birdeye.social.constant.ResellerSortType;
import com.birdeye.social.dto.SocialEnabledStatusDto;
import com.birdeye.social.entities.*;
import com.birdeye.social.instagram.InstagramConversationResponse;
import com.birdeye.social.model.*;
import com.birdeye.social.model.instagram.InstagramAuthRequest;
import com.birdeye.social.model.instagram.TokenUpdateRequest;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.sro.*;

import java.net.URISyntaxException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
public interface IInstragramSetupService {



	List<Feed> getInstagramComments(Business business, BusinessInstagramAccount instagramAccount, String postId);

	String postComment(SocialPostComment postComment, Business business, BusinessInstagramAccount instagramAccount);

	boolean deleteComment(String postId, String commentId, Business business, BusinessInstagramAccount instagramAccount);

	boolean unlikePost(String postId, Business business, BusinessInstagramAccount instagramAccount);

	BusinessInstagramAccount updateNegativeValidity(BusinessInstagramAccount account, String errorCode,
													String errorMessage, Integer isManaged, Integer isValid);

	boolean likePost(String postId, Business business, BusinessInstagramAccount instagramAccount);

	InstagramConversationResponse fetchInstagramConversationData(String pageId, String accesToken);

	InstagramConversationResponse fetchInstagramConversationDataWrapper(String pageId);

	void fetchPages(BusinessGetPageRequest request, String extendedToken, FbUserProfileInfo user,
					Business business, Integer birdeyeUserId,String type);

	FetchPageResponse getIntegrationPage(Long enterpriseId);

	ConnectedPages getPages(Integer userId, Long enterpriseId, String type);

	ConnectedPages getPagesForPostReconnect(Long enterpriseId, String type, SocialPostPageConnectRequest request);

	Map<String, String> getInstagramIntegrationStatus(Integer businessId);

	String getInstagramIdByBusinessId(Integer businessId);

	Map<String, Object> getByBusinessId(String instagramId, List<String> fields);

	ChannelPageInfo connectInstagramAccountV1(InstagramConnectAccountRequest request, Integer accountId);

	CheckStatusResponse getIntegrationRequestStatus(Long businessId, Boolean reconnectFlag);

	InboxStatusResponse checkMessengerStatus(Long enterpriseId);

	List<SocialPageListInfo> getUnmappedInstagramAccountsByEnterpriseId(Long enterpriseId);

	void cancelRequest(Long enterpriseId, Boolean forceCancel);

	void saveInstagramLocationMapping(Integer locationId , String pageId,Integer userId, String type, Long resellerId);


	void removeInstagramLocationAccountMapping(List<LocationPageMappingRequest> input,String type, boolean unlink);

	void removeInstagramAccounts(List<LocationPageMappingRequest> input, Long enterpriseId);

	void removeInstagramPages(List<BusinessInstagramAccount> existingPages, String type);

	LocationPageMapping getLocationMappingPages(Long enterpriseId, Integer userId, List <Integer> businessIds, Set<String> status,Integer page,Integer size,String search, List<String> includeModules) throws Exception;

	ChannelSetupStatus.PageSetupStatus getPageSetupStatus(Long enterpriseId);

	public ChannelSetupStatus.PageSetupStatus getPageConnectionSetupStatus(Long enterpriseId);
	void fetchDisconnectedAndStore();

	void updateInstagramAccountIsValidStatus(String pageId, Integer isValid);

	void removeInactiveIntegration(String channel, Long enterpriseId);

	void processDeletePageEvent(String channel, List<BusinessInstagramAccount> existingPages);

	void backupInstagramPages(SocialPagesAudit socialPage);

	boolean checkIfAccountExistsByAccountId(Long accountId);

	void reconnectInstagramAccounts(InstagramAuthRequest authRequest);

	void updateAccessToken(TokenUpdateRequest request);

	void updateInvalidPage(SocialAudit audit);

	OpenUrlFetchPageResponse submitFetchPageRequestForOpenURL(Long businessId,  ChannelAuthOpenUrlRequest authRequest) throws Exception;

	OpenUrlPagesInfo getPagesFetchedByOpenUrl(Long enterpriseId);

	OpenUrlPagesInfo connectPagesFetchedByOpenUrl(Long enterpriseId, OpenUrlConnectRequest connectRequest, Integer userId);

	BusinessInstagramAccount getInstagramPageByBusinessId(Integer businessId);

	void removeBusinessInactiveIntegration(String name, Integer businessId);

//	void syncSocialEnabledStatus();

	Boolean subscribeSocial(BusinessInstagramAccount businessInstagramAccount) throws Exception;

	void unsubscribeSocial(String pageId, String pageAccessToken) throws Exception;

	void updateSubscription(SocialEnabledStatusDto socialEnabledStatusDto);

	void initiateDPSync();

	void syncIGDP(DpSyncRequest igDpSyncRequest);

    void removeUnmappedByEnterprise(Long sourceBusinessAccountNumber, Integer businessId);

    void removePageMap(Integer businessId);

    void moveInstagramAccountLocation(Long sourceBusinessAccountNumber, Long targetBusinessAccountNumber, Integer sourceBusinessId, boolean b,Integer accountId);

    void removeInstagramAccountByPagesIds(List<String> pagesIds);
	String checkPageIntegrationStatusByBusinessId(Integer businessId);

	void submitFetchPageRequest(Long parentId, Integer birdeyeUserId, String authCode, String redirectUri, String tempAccessToken, String type);

    void removeIGPageForReseller(List<String> pageIds, Integer limit);

	PaginatedConnectedPages getResellerPages(Long resellerId, PageConnectionStatus pageConnectionStatus, Integer page, Integer size, String search,
											 ResellerSearchType searchType, PageSortDirection sortDirection,
											 ResellerSortType sortType, List<Integer> locationIds, MappingStatus mappingStatus, List<Integer> userIds,
											 Boolean locationFilterSelected);

	Map<String, Object> getPaginatedPages(BusinessGetPageRequest businessGetPageRequest, Integer pageNumber, Integer pageSize,String search);

	List<Integer> getMappedResellerLeafLocations(List<Integer> resellerLeafLocationIds);
	
	void updateIGValidityType(BusinessInstagramAccount businessInstagramAccount);

	void validityCheckForIG(Collection<String> igAccountIds);
  
	List<String> getMappedRequestIds(Set<String> requestIds);
  
	String getAuthorizationUrl(String origin) throws URISyntaxException;

	boolean checkPermission(List<BusinessInstagramAccount> instagramAccounts, List<String> modules);

	Validity fetchValidityAndErrorMessage(BusinessInstagramAccount instagramAccount, String igPermissions);

	List<ChannelAccountInfo> getInstagramAccounts(BusinessGetPageRequest req, Long enterpriseId);
}
