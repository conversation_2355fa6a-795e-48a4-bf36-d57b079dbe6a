package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.businessgetpage.IBusinessGetPageService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessGetPageOpenUrlReqRepo;
import com.birdeye.social.dao.BusinessGetPageReqRepo;
import com.birdeye.social.dao.BusinessLinkedinPageRepository;
import com.birdeye.social.dao.SocialPagesAuditRepo;
import com.birdeye.social.dao.SocialSetupAuditRepository;
import com.birdeye.social.dto.*;
import com.birdeye.social.elasticdto.SocialElasticDto;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.request.business.BusinessLiteRequest;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.linkedin.*;
import com.birdeye.social.linkedin.organization.LinkedInOrganizationElement;
import com.birdeye.social.linkedin.organization.LinkedInOrganizationResponse;
import com.birdeye.social.linkedin.organization.Role;
import com.birdeye.social.linkedin.organization.State;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.engageV2.EngageWebhookSubscriptionRequest;
import com.birdeye.social.model.linkedin.LinkedinAuthRequest;
import com.birdeye.social.platform.dao.BusinessRepository;
import com.birdeye.social.specification.LinkedinSpecification;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.*;

import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.SocialProxyHandler;

import com.birdeye.social.utils.SocialElasticUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.domain.Specifications;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.*;
import static com.birdeye.social.utils.ConversionUtils.prepareListOfOrganizationData;
import static com.birdeye.social.utils.ConversionUtils.createResponseWithLogoUrl;
import static java.util.Comparator.nullsFirst;
import static java.util.stream.Collectors.groupingBy;
import org.springframework.data.domain.PageRequest;
@Service
public class SocialLinkedinServiceImpl extends SocialAccountSetupCommonService implements SocialLinkedinService{

    @Autowired
    private BusinessRepository businessRepo;

    @Autowired
    private ISocialAppService socialAppService;

    @Autowired
    private ILinkedinConnectService linkedinConnectService;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private BusinessUtilsService businessUtilService;

    @Autowired
    private IBusinessGetPageService businessGetPageService;

    @Autowired
    private IPermissionMappingService permissionMappingService;

    @Autowired
    private IRedisLockService redisService;

    @Autowired
    private BusinessLinkedinPageRepository linkedInRepo;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private ISocialLinkedinRefreshTokenService socialLinkedinRefreshTokenService;

    @Autowired
    private SocialPagesAuditRepo socialPagesAuditRepo;

    @Autowired
    private SocialEngagementService socialEngagementService;

    @Autowired
    private BusinessGetPageOpenUrlReqRepo businessGetPageOpenUrlReqRepo;

    @Autowired
    private SocialProxyHandler socialProxyHandler;

    @Autowired
    private SocialSetupAuditRepository setupAuditRepo;

    @Autowired
    private IBrokenIntegrationService brokenIntegrationService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ISocialModulePermissionService socialModulePermissionService;

    @Autowired
    private SocialBusinessService socialBusinessService;

    @Autowired
    private KafkaProducerService kafkaProducer;
    
    @Autowired
    private BusinessLinkedinPageRepository businessLinkedinPageRepository;
    
    @Autowired
    private LinkedinSpecification linkedinSpecification;
    
    @Autowired
	private BusinessGetPageReqRepo businessGetPageReqRepo;
    
    @Autowired
   	private IBusinessCoreService iBusinessCoreService;

    @Autowired
    private BusinessService coreBusinessService;

    private static final Logger LOGGER = LoggerFactory.getLogger(SocialLinkedinServiceImpl.class);
    private static final String CONNECT = "connect";
    private static final String RECONNECT = "reconnect";
    public static final String LINKEDIN_COMPANY_URL = "https://www.linkedin.com/company/";
    public static final String LINKEDIN_PROFILE_URL = "https://www.linkedin.com/in/";
    
    private static final String APPROVED = "APPROVED";

    private static final String ADMINISTRATOR = "ADMINISTRATOR";

    private static final String CONTENT_ADMINISTRATOR = "CONTENT_ADMINISTRATOR";

    public static  final String LINKEDIN_SOCIAL_ENABLED_UPDATE = "linkedin-social-enabled-update";

    private static final String LINKEDIN_DP_SYNC_TOPIC= "linkedin-dp-sync";

    private final List<Integer> SOCIAL_LISTEN_ENABLED = Arrays.asList(4,5,6,13,14,22,29,30);
    private final List<Integer> SOCIAL_ENGAGE_ENABLED = Arrays.asList(17,22,26,29,30);

    @Override
    public String getAuthorizationUrl(Integer businessId, String redirectUri, String origin) throws Exception {
        LOGGER.info("Received call for linkedin to get authorization url for business Id {}",businessId);
        SocialAppCredsInfo domainInfo = socialAppService.getLinkedinAppSettings();
        LinkedinCreds linkedinCreds =  new LinkedinCreds(domainInfo.getChannelClientId(),domainInfo.getChannelClientSecret());
        return linkedinConnectService.getAuthorizationUrl(linkedinCreds, redirectUri, origin);
    }

    @Override
    public Map<String, Object> getLinkedinTokenDetails(String token) {
        return linkedinConnectService.getLinkedinTokenDetails(token);
    }

    @Override
    public LinkedinUserProfileInfo getLinkedInUserProfile(String token) {
        return linkedinConnectService.getlinkedinUserProfile(token);
    }

    @Override
    public LinkedinOrganizationPagesInfo linkedinOrganizationInfo(String token) {
        LinkedinOrganizationPagesInfo linkedinOrganizationPagesInfo = new LinkedinOrganizationPagesInfo();
        List<LinkedinPageInfoDetails> elements = new ArrayList<>();
        LinkedinOrgElements linkedinOrgElements;
        int count = Integer.parseInt(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("linkedin.pagination.limit"));
        int start = 0;
        while(true) {
            linkedinOrgElements = linkedinConnectService.linkedinOrganizationInfo(token,count,start);
            if(Objects.isNull(linkedinOrgElements) || CollectionUtils.isEmpty(linkedinOrgElements.getElements())){
                LOGGER.info("No more data found");
                break;
            }
            start += count;
            List<Integer> profileIds = new ArrayList<>();
            List<String> imageIds = new ArrayList<>();
            linkedinOrgElements.getElements().forEach(element ->
                    profileIds.add(Integer.valueOf(element.getOrganization().split(LINKEDIN_ORG_PREFIX)[1])));
            if(CollectionUtils.isEmpty(profileIds)){
                LOGGER.info("No data found to get info from profile ids");
                return linkedinOrganizationPagesInfo;
            }
            LinkedinOrgPageInfoWrapper linkedinOrganizationInfoForIds =  linkedinConnectService.linkedinOrganizationInfoForIds(profileIds,token);
            if(Objects.isNull(linkedinOrganizationInfoForIds) || MapUtils.isEmpty(linkedinOrganizationInfoForIds.getResults())){
                LOGGER.info("Profile ids data not found : {}",profileIds);
                continue;
            }
            elements.addAll(prepareListOfOrganizationData(linkedinOrganizationInfoForIds,linkedinOrgElements,imageIds));
            prepareLogoImageUrl(elements,imageIds,token);
        }
        linkedinOrganizationPagesInfo.setElements(elements);
        return linkedinOrganizationPagesInfo;
    }

    private void prepareLogoImageUrl(List<LinkedinPageInfoDetails> elements,List<String> imageIds,String token) {
        Map<String,LinkedinImageData> results = new HashMap<>();
        // use single call to get images from linkedin
        imageIds.forEach(image -> {
            LinkedinImageData imageData = linkedinConnectService.linkedinImageFromId(image,token);
            results.put(image,imageData);
        });
        LinkedinImagesWrapper linkedinImagesWrapper = new LinkedinImagesWrapper();
        linkedinImagesWrapper.setResults(results);
        createResponseWithLogoUrl(linkedinImagesWrapper,elements);
    }

    @Override
    public void cancelRequest(Long businessId, Boolean forceCancel) {
        LOGGER.info("[Linkedin Setup] Request received to Cancel Twitter Request for business {}", businessId);
        BusinessGetPageRequest req =  businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(businessId, SocialChannel.LINKEDIN.getName(),CONNECT);
        if(Objects.isNull(req)) {
            LOGGER.error("No record found in business get page request for businessId: {}", businessId);
            return;
        }
        if(!forceCancel && Status.INITIAL.getName().equals(req.getStatus())) {
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, Constants.INIT_TO_CANCEL_ERROR_REQUEST_MESSAGE);
        }
        req.setStatus(Status.CANCEL.getName());
        req.setUpdated(new Date());
        businessGetPageService.saveAndFlush(req);
        pushCheckStatusInFirebase(SocialChannel.LINKEDIN.getName(),req.getRequestType(),Status.COMPLETE.getName(), req.getEnterpriseId());
        releaseLock(req.getChannel(), req.getEnterpriseId());
    }

    @Override
    @Deprecated
    public ChannelPageInfo getLinkedinIntegrationRequestInfo(Long businessId, Boolean reconnectFlag) throws Exception {
        LOGGER.info("Request received to checkStatus for business {}", businessId);
        String requestType = null;
        if(reconnectFlag) {
            requestType = "reconnect";
        } else {
            requestType = "connect";
        }
        ChannelPageInfo response = new ChannelPageInfo();
        BusinessGetPageRequest linkedinData = businessGetPageService.findLastRequestByEnterpriseIdAndChannel(businessId, SocialChannel.LINKEDIN.getName());
        if (linkedinData!=null){
            if(linkedinData.getStatus().equalsIgnoreCase(Status.INITIAL.getName()) || linkedinData.getStatus().equalsIgnoreCase(Status.NO_PAGES_FOUND.getName())) {
                response.setStatus(linkedinData.getStatus());
                response.setStatusType(linkedinData.getRequestType());
            } else if(CONNECT.equalsIgnoreCase(linkedinData.getRequestType()) && linkedinData.getStatus().equalsIgnoreCase(Status.FETCHED.getName())) {
                LOGGER.info("isFetched is true for linkedin businessId , requestId :  {} , {}",businessId,linkedinData.getId());
                response.setStatus(Status.FETCHED.getName());
                Map<String, List<ChannelAccountInfo>> pageType = getPages(businessId, linkedinData);
                response.setPageTypes(pageType);
                response.setStatusType(linkedinData.getRequestType());
            } else {
                response.setStatus(Status.COMPLETE.getName());
                response.setStatusType(linkedinData.getRequestType());
            }
        } else {
            response.setStatus(Status.COMPLETE.getName());
            response.setStatusType(linkedinData!=null?linkedinData.getRequestType():requestType);
        }
        return response;
    }

    @Override
    public CheckStatusResponse getIntegrationRequestStatus(Long businessId, Boolean reconnectFlag) {
        LOGGER.info("Request received to check status for channel {} and business {}", SocialChannel.LINKEDIN.getName(), businessId);
        return getIntegrationStatus(SocialChannel.LINKEDIN.getName(), businessId, reconnectFlag);
    }

    @Override
    public FetchPageResponse getIntegrationPage(Long businessId) {
        LOGGER.info("Request received to fetch integrated pages for business and channel {} {}",businessId,SocialChannel.LINKEDIN.getName());
        FetchPageResponse fetchPageResponse = new FetchPageResponse();
        Map<String, List<ChannelAccountInfo>> data = new HashMap<>();
        BusinessGetPageRequest businessGetPageRequest = businessGetPageService.findLastRequestByEnterpriseIdAndChannel(businessId, SocialChannel.getSocialChannelByName("linkedin").getName());
        if(Objects.isNull(businessGetPageRequest)){
            LOGGER.info("No request exists for business and channel {} {}",businessId,SocialChannel.LINKEDIN.getName());
            throw new BirdeyeSocialException(ErrorCodes.NO_BUSINESS_GET_PAGE_REQUEST_FOUND,"No request exists for business");
        }

        data = getPages(businessId,businessGetPageRequest);
        if(Objects.nonNull(data) && data.containsKey(SocialChannel.LINKEDIN.getName())) {
            Boolean allPagesMapped = data.get(SocialChannel.LINKEDIN.getName()).stream()
                    .allMatch(ChannelAccountInfo::getIsMapped);
            fetchPageResponse.setAllPagesMapped(allPagesMapped);
        }
        fetchPageResponse.setPageTypes(data);
        return fetchPageResponse;
    }

    private boolean isBusinessNotMappedToLinkedInAccount(BusinessLiteDTO business) {
        return linkedInRepo.findByBusinessId(business.getBusinessId()).isEmpty();
    }

    private void checkForUnMappedSmbPages(BusinessLiteDTO business) {
        if (checkBusinessSMB(business)) {
            List<BusinessLinkedinPage> existingPages = linkedInRepo.findByAccountId(business.getBusinessId());
            List<String> unMappedProfileIds = new ArrayList<>();
            List<String> unMappedCompanyIds = new ArrayList<>();
            existingPages.forEach(page->{
                if(page.getPageType().equalsIgnoreCase(LinkedinPageTypeEnum.COMPANY.getName())){
                    unMappedCompanyIds.add(page.getProfileId());
                } else {
                    unMappedProfileIds.add(page.getProfileId());
                }
            });
            if(CollectionUtils.isNotEmpty(unMappedCompanyIds)) {
                linkedInRepo.deleteByPageTypeAndProfileIdIn(LinkedinPageTypeEnum.COMPANY.getName(),unMappedCompanyIds);
            }
            if(CollectionUtils.isNotEmpty(unMappedProfileIds)) {
                linkedInRepo.deleteByPageTypeAndProfileIdIn(LinkedinPageTypeEnum.PROFILE.getName(),unMappedProfileIds);
            }
            if(CollectionUtils.isEmpty(unMappedCompanyIds) && CollectionUtils.isEmpty(unMappedProfileIds)) {
                linkedInRepo.deleteByAccountId(business.getBusinessId());
            }
        }
    }

    @Override
    public ChannelPageInfo connectLinkedinPagesV1(ConnectPageRequest input, Integer accountId)  {
        LOGGER.info("connectPages linkedin: BusinessId : {} , {}", input.getBusinessId(), JSONUtils.toJSON(input.getInput()));
        ChannelPageInfo channelAccountInfo = new ChannelPageInfo();
        List<BusinessGetPageRequest> request = Constants.RESELLER.equals(input.getType())?businessGetPageReqRepo.findByResellerIdAndStatusAndChannelAndRequestType(input.getBusinessId(), Status.FETCHED.getName(),SocialChannel.LINKEDIN.name(),CONNECT):
        	businessGetPageService.getRequestForBusiness(input.getBusinessId(), Status.FETCHED.getName(),SocialChannel.LINKEDIN.name(),CONNECT);
        if (CollectionUtils.isEmpty(request)) {
            throw new BirdeyeSocialException(ErrorCodes.STATUS_CHANGED, "seems linkedin request status has already changed");
        } else if (request.size() > 1) {
            throw new BirdeyeSocialException(ErrorCodes.MULTI_FETCHED_ROWS, "multiple rows with fetched status for linkedin request");
        } else {
            BusinessLiteDTO business = businessCoreService.getBusinessLiteByNumber(input.getBusinessId());
            checkForUnMappedSmbPages(business);
            BusinessGetPageRequest req = request.get(0);
            req.setStatus(Status.COMPLETE.getName());
            req.setUpdated(new Date());
            businessGetPageService.saveAndFlush(req);
            pushCheckStatusInFirebase(SocialChannel.LINKEDIN.getName(),req.getRequestType(),Status.COMPLETE.getName(), input.getBusinessId());
            List<String> profileIds =null;
            if(Constants.RESELLER.equals(input.getType()) && BooleanUtils.isTrue(input.getSelectAll()) && StringUtils.isNotEmpty(input.getSearchStr())) {
            	Specification<BusinessLinkedinPage> linkedInSpec = Specifications.where((linkedinSpecification.hasPageName(input.getSearchStr()))).
        				and(linkedinSpecification.hasRequestId(req.getId().toString()));
            	List<BusinessLinkedinPage> list = businessLinkedinPageRepository.findAll(linkedInSpec);
            	profileIds=list.stream().map(page->page.getProfileId()).collect(Collectors.toList());
            }
			else if(Constants.RESELLER.equals(input.getType()) && BooleanUtils.isTrue(input.getSelectAll())){
				profileIds = linkedInRepo.findAllByRequestId(req.getId().toString());
			}else{
				profileIds = input.getInput().stream().map(LinkedinPageRequest::getId).collect(Collectors.toList());
			}
            List<BusinessLinkedinPage> businessLinkedinCompanyPages = linkedInRepo.findByProfileIdIn(profileIds);
            // We need to modify the request for Reseller Business because the current code requires the page type, which is not included in the incoming request.
            if(Constants.RESELLER.equals(input.getType())) {
            	List<LinkedinPageRequest> linkedinPageRequest = businessLinkedinCompanyPages.stream().map(page -> new LinkedinPageRequest(page.getProfileId(),page.getPageType()))
        				.collect(Collectors.toList());
            	input.setInput(linkedinPageRequest);
            }
            businessLinkedinCompanyPages.forEach(page -> {
            	if(Constants.RESELLER.equals(input.getType())) {
            		page.setResellerId(input.getBusinessId());
            	}else {
            		page.setEnterpriseId(input.getBusinessId());
            	}
                page.setAccountId(accountId);
                page.setIsSelected(1);
                linkedInRepo.saveAndFlush(page);
            });
            releaseLock(req.getChannel(),input.getBusinessId());
            BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest();
            businessLiteRequest.setKey("businessNumber");
            businessLiteRequest.setValue(input.getBusinessId());
            if (checkBusinessSMB(business) && isBusinessNotMappedToLinkedInAccount(business)) {
                saveLinkedinLocationMapping(business.getBusinessId(), profileIds.get(0), input.getInput().get(0).getType(),Constants.ENTERPRISE, null, null);
            }
            Map<String, List<ChannelAccountInfo>> accountMap = new HashMap<>();
            List<ChannelAccountInfo> channelAccountInfos=prepareChannelAccountInfo(Constants.RESELLER.equals(input.getType())?linkedInRepo.findByResellerIdAndProfileIdIn(input.getBusinessId(),profileIds) :linkedInRepo.findByEnterpriseIdAndProfileIdIn(input.getBusinessId(),profileIds), null);
			if(Constants.RESELLER.equals(input.getType())) {
				channelAccountInfos.forEach(info->info.setUserId(req.getEmail()));
			}
            accountMap.put(SocialChannel.LINKEDIN.getName(),channelAccountInfos);
            if(CollectionUtils.isNotEmpty(accountMap.get(SocialChannel.LINKEDIN.getName()))) {
                accountMap.get(SocialChannel.LINKEDIN.getName()).forEach(s->s.setDisabled(null));
            }
            channelAccountInfo.setPageTypes(accountMap);
            publishConnectPageRequest(input);
        }
        return channelAccountInfo;
    }

    private void publishConnectPageRequest(List<BusinessLinkedinPage> businessLinkedinPages) {
        List<LinkedinConnectRequest> linkedinConnectRequests = new ArrayList<>();
        businessLinkedinPages.stream().forEach(linkedinPage -> {
            LinkedinConnectRequest linkedinConnectRequest = new LinkedinConnectRequest();
            linkedinConnectRequest.setId(linkedinPage.getId().toString());
            linkedinConnectRequest.setType(linkedinPage.getPageType());
            linkedinConnectRequests.add(linkedinConnectRequest);
        });

        SocialConnectPageRequest socialConnectPageRequest = new SocialConnectPageRequest();
        socialConnectPageRequest.setLinkedinConnectRequests(linkedinConnectRequests);
        socialConnectPageRequest.setChannel(SocialChannel.LINKEDIN.getName());
        kafkaProducerService.sendObject(SOCIAL_PAGE_CONNECT,socialConnectPageRequest);
    }

    private void publishConnectPageRequest(ConnectPageRequest input) {
        List<LinkedinConnectRequest> linkedinConnectRequests = new ArrayList<>();
        input.getInput().stream().forEach(linkedinPageRequest -> {
            LinkedinConnectRequest linkedinConnectRequest = new LinkedinConnectRequest();
            linkedinConnectRequest.setId(linkedinPageRequest.getId());
            linkedinConnectRequest.setType(linkedinPageRequest.getType());
            linkedinConnectRequests.add(linkedinConnectRequest);
        });

        SocialConnectPageRequest socialConnectPageRequest = new SocialConnectPageRequest();
        socialConnectPageRequest.setLinkedinConnectRequests(linkedinConnectRequests);
        socialConnectPageRequest.setChannel(SocialChannel.LINKEDIN.getName());
        kafkaProducerService.sendObject(SOCIAL_PAGE_CONNECT,socialConnectPageRequest);
    }

    @Override
    public void removeLinkedinLocationAccountMapping(List<LocationPageMappingRequest> locationPageMappingRequests,String type, boolean unlink) {
        if ( CollectionUtils.isEmpty(locationPageMappingRequests)) {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_PAGE_OR_LOCATION_ID, "page/location id is null");
        }
        final Integer maxRequests = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getMaxDeleteMappingsCount();
        if ( locationPageMappingRequests.size() > maxRequests ) {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Maximum remove mappings request can be " + maxRequests);
        }
        LOGGER.info("[Linkedin Setup] Linkedin company Id {} remove mapping with location Id for size {}" , locationPageMappingRequests.size());

        List<String> profileIds = locationPageMappingRequests.stream().map(LocationPageMappingRequest::getPageId).collect(Collectors.toList());
        List<BusinessLinkedinPage> existingPages = linkedInRepo.findByProfileIdIn(profileIds);
        if(Objects.nonNull(locationPageMappingRequests.get(0)) && Objects.nonNull(locationPageMappingRequests.get(0).getResellerId())){
            List<Long> storedResellerIds = existingPages.stream().map(BusinessLinkedinPage::getResellerId).collect(Collectors.toList());
            commonService.checkRequestFromAuthorizedSourceUsingResellerIdsList(storedResellerIds, locationPageMappingRequests.get(0).getResellerId());
        }
        existingPages.forEach(businessLinkedinPage -> {
//            sendEventToSubscribeLinkedin(businessLinkedinPage.getProfileId(), false);
            commonService.sendLinkedinSetupAuditEvent(SocialSetupAuditEnum.REMOVE_MAPPING.name(), Arrays.asList(businessLinkedinPage), null, businessLinkedinPage.getBusinessId(),Constants.ENTERPRISE.equals(type)?businessLinkedinPage.getEnterpriseId():businessLinkedinPage.getResellerId());
            businessLinkedinPage.setBusinessId(null);
            if(Objects.nonNull(businessLinkedinPage.getResellerId())){
                businessLinkedinPage.setEnterpriseId(null);
                businessLinkedinPage.setAccountId(null);
            }
            if(unlink) {
                businessLinkedinPage.setIsSelected(0);
                businessLinkedinPage.setEnterpriseId(null);
                businessLinkedinPage.setResellerId(null);
            }
        });
        linkedInRepo.save(existingPages);
        LOGGER.info("[Linkedin Setup] Linkedin company Id {} removed mapping with location Id for size {}" , locationPageMappingRequests.size());
        kafkaProducerService.sendObject(Constants.LINKEDIN_PAGE_MAPPING_REMOVED, locationPageMappingRequests);

        this.removeEngageStreams(existingPages);
    }

    private void sendEventToSubscribeLinkedin(String pageId,boolean subscribe) {
        LOGGER.info("Request received for fb engage subcrioption for pageId {}", pageId);
        EngageWebhookSubscriptionRequest payload = new EngageWebhookSubscriptionRequest();
        payload.setSubscription(subscribe);
        payload.setChannel(SocialChannel.LINKEDIN.getName());
        payload.setPageId(pageId);
        // subscribe to engage webhook
        kafkaProducerService.sendObject(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_WEBHOOK_SUBSCRIPTION_TOPIC.getName(), payload);
    }

    // TODO - Fix for BIRDEYE-73936
    private void removeEngageStreams(List<BusinessLinkedinPage> existingPages) {
        if ( CollectionUtils.isNotEmpty(existingPages) ) {
            final List<Integer> existingPageIds = existingPages.stream().map(BusinessLinkedinPage::getId).collect(Collectors.toList());
            socialEngagementService.deleteStreams(109, existingPageIds);
        }
    }

    @Override
    public void removeLinkedinPages(List<LocationPageMappingRequest> input, String type) {
        List<String> companyProfileId = input.stream().filter(l->LinkedinPageTypeEnum.COMPANY.getName().equalsIgnoreCase(l.getType())).map(LocationPageMappingRequest::getPageId).collect(Collectors.toList());
        List<String> profileIds = input.stream().filter(l->LinkedinPageTypeEnum.PROFILE.getName().equalsIgnoreCase(l.getType())).map(LocationPageMappingRequest::getPageId).collect(Collectors.toList());
        List<BusinessLinkedinPage> linkedInProfiles = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(companyProfileId)) {
        	linkedInProfiles.addAll(linkedInRepo.findByProfileIdInAndPageType(companyProfileId,LinkedinPageTypeEnum.COMPANY.getName()));
            linkedInRepo.deleteByPageTypeAndProfileIdIn(LinkedinPageTypeEnum.COMPANY.getName(),companyProfileId);
        }
        if(CollectionUtils.isNotEmpty(profileIds)) {
         	linkedInProfiles.addAll(linkedInRepo.findByProfileIdInAndPageType(profileIds,LinkedinPageTypeEnum.PROFILE.getName()));
            linkedInRepo.deleteByPageTypeAndProfileIdIn(LinkedinPageTypeEnum.PROFILE.getName(),profileIds);
        }
        removeLinkedinPagesForPages(linkedInProfiles,type);
    }

    private void removeLinkedinPagesForPages(List<BusinessLinkedinPage> linkedInProfiles, String type) {
        this.removeEngageStreams(linkedInProfiles);
        //aduit check -pranjali
        linkedInProfiles.forEach(rawPage ->
                commonService.sendLinkedinSetupAuditEvent(SocialSetupAuditEnum.REMOVE_PAGE.name(),
                Collections.singletonList(rawPage), null, rawPage.getBusinessId(),
                        RESELLER.equals(type)?rawPage.getResellerId():rawPage.getEnterpriseId()));
        LOGGER.info("[Linkedin Setup] Linkedin profiles removed  for size {}" , linkedInProfiles.size());
        if(CollectionUtils.isNotEmpty(linkedInProfiles)) {
            unsubscribeWebhookForLinkedin(linkedInProfiles);
        }
        List<ChannelPageRemoved> channelPageRemoveds = linkedInProfiles.stream().map(s -> new ChannelPageRemoved(
                SocialChannel.LINKEDIN.getName(),
                s.getProfileId(), null, s.getBusinessId(), null, null, null, s.getId()
        )).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(channelPageRemoveds)) {
            kafkaProducerService.sendObject(Constants.SOCIAL_PAGE_REMOVED, channelPageRemoveds);
        }
    }

    @Override
    public LocationPageMapping getLocationMappingPages(Long enterpriseId, Integer userId, List <Integer> businessIds, Set<String> status,Integer page,Integer size,String search, List<String> includeModules) throws Exception {
        LOGGER.info("[Linkedin Setup] Fetching Linkedin pages mapped with locations for enterpriseId: {} and userId: {}", enterpriseId, userId);
        LocationPageMapping response = new LocationPageMapping();


        List<Integer> totalEnterpriseBusinessIds = new ArrayList<>();
        if(CollectionUtils.isEmpty(businessIds)) {
            BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest();
            businessLiteRequest.setKey("businessNumber");
            businessLiteRequest.setValue(enterpriseId);
            BusinessLiteDTO business = businessCoreService.getBusinessLite(businessLiteRequest);
            totalEnterpriseBusinessIds = businessUtilService.getBusinessLocationsForEnterpriseUsingLite(business, null);
        }
        // Filter out the business based on User have access to business
        if (CollectionUtils.isNotEmpty(totalEnterpriseBusinessIds) || CollectionUtils.isNotEmpty(businessIds)) {
            response.setTotalLocations(businessIds != null ? businessIds.size() : 0);
            if(CollectionUtils.isEmpty(status)) {
                LOGGER.info("blank status received hence returning for business {}",enterpriseId);
                response.setLocationList(new ArrayList<>());
                response.setDisconnectedCount(0);
                response.setUnmapped(0);
                response.setAllPagesMapped(false);
                response.setPermissionIssuePageCount(0);
                return response;
            }
            Boolean toSearch = Objects.nonNull(search) && !search.isEmpty();
            if (CollectionUtils.isNotEmpty(businessIds)) {
                List<BusinessLinkedinPage> pages = linkedInRepo.findAllByBusinessIdIn(businessIds);
                response.setUnmapped(businessIds.size() - pages.size());
                if(status.size() > 1)
                    prepareLocationLinkedinPageMapping(enterpriseId,businessIds, response, status,page,size,search,toSearch,pages, includeModules);
                else if(status.size() == 1)
                    prepareMappedAndUnMappedData(enterpriseId,businessIds,response,status,page,size,search,toSearch,pages, includeModules);
            }else {
                LOGGER.error("No business ids found for userId: {} and enterpriseIds: {}", userId, totalEnterpriseBusinessIds);
                throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, "Business not found.");
            }
        }
        LOGGER.info("[Linkedin Setup] Exiting com.birdeye.social.service.TwitterSocialAccountService#getLocationMappingPages().");
        return response;
    }

    private void prepareMappedAndUnMappedData(Long enterpriseId, List<Integer> businessIds, LocationPageMapping response, Set<String> status, Integer page, Integer size,String search,Boolean toSearch,List<BusinessLinkedinPage> linkedinPages, List<String> includeModules) {
        LOGGER.info("Mapped locations for linkedIn pages size : {} and enterpriseId :{}",linkedinPages.size(),enterpriseId);
        Map<Integer, BusinessLocationLiteEntity> businessLocationsMap;
        List<Integer> filterBusinessIds = new LinkedList<>();
        linkedinPages.stream().filter(Objects::nonNull).forEach(pages -> filterBusinessIds.add(pages.getBusinessId()));
        businessIds.removeAll(filterBusinessIds);
        if(status.contains(LocationStatusEnum.UNMAPPED.getName())){
            if(toSearch){
                businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(businessIds,page,size,search,response);
            }else{
                businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginated(businessIds,page,size);
            }
        }else {
            if(toSearch){
                businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(filterBusinessIds,page,size,search,response);
            }else{
                businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginated(filterBusinessIds,page,size);
            }
        }
        Map<Integer,BusinessLinkedinPage> businessLinkedinPageMap = linkedinPages.stream().collect(Collectors.toMap(BusinessLinkedinPage::getBusinessId, Function.identity()));
        List<ChannelLocationInfo> locationList = getChannelLocationInfoList(businessLocationsMap, businessLinkedinPageMap,response,status, includeModules);
        locationList.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));
        response.setLocationList(locationList);
        response.setAllPagesMapped(businessIds.size() <= 0);
    }

    @Override
    public void submitFetchPageRequest(LinkedinAuthRequest linkedinAuthRequest,String type) {
        Long businessId = linkedinAuthRequest.getBusinessId();
        Integer birdeyeUserId = linkedinAuthRequest.getBirdeyeUserId();
        String authCode = linkedinAuthRequest.getAuthCode();
        String redirectUri = linkedinAuthRequest.getRedirectUri();
        String key = SocialChannel.LINKEDIN.getName().concat(String.valueOf(businessId));
        boolean lock = redisService.tryToAcquireLock(key);
        LOGGER.info("[Redis Lock] Lock status : {}",lock);
        
        BusinessGetPageRequest request = Constants.ENTERPRISE.equals(type)? businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(businessId, SocialChannel.LINKEDIN.getName(),CONNECT)
        		:businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(businessId, SocialChannel.LINKEDIN.getName(),CONNECT);
        if(lock){
            try{
                LinkedinCreds linkedinCreds = getLinkedinCreds(businessId);
                AccessTokenInfo accessTokenInfo = linkedinConnectService.getAccessToken(linkedinCreds,redirectUri,authCode);
                List<String> statusList = Arrays.asList(Status.INITIAL.getName(), Status.FETCHED.getName());
                if (Objects.isNull(request) || !statusList.contains(request.getStatus())) {
                    pushCheckStatusInFirebase(SocialChannel.LINKEDIN.getName(),CONNECT,Status.INITIAL.getName(), businessId);
                    BusinessGetPageRequest businessGetPageRequest = submitLinkedinGetPageRequest(businessId, birdeyeUserId, accessTokenInfo,CONNECT,type);
                    socialProxyHandler.runInAsync(() -> {
                        fetchLinkedinPages(businessGetPageRequest,accessTokenInfo,type);
                        return true;
                    });
                }else{
                    LOGGER.info("Linkedin BusinessGetPageRequest found with status INITIAL and FETCHED , hence returning for businessId and userId : {} , {}",businessId,birdeyeUserId);
                    redisService.release(key);
                    pushCheckStatusInFirebase(SocialChannel.LINKEDIN.getName(),request.getRequestType(),request.getStatus(),businessId);
                }
            }catch (Exception ex){
                redisService.release(key);
                LOGGER.error("[Redis] (Linkedin) Lock released for business {}, error ", businessId, ex);
                BusinessGetPageRequest reqLinkedin = Constants.ENTERPRISE.equals(type)?businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(businessId,SocialChannel.LINKEDIN.getName(),CONNECT):businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(businessId,SocialChannel.LINKEDIN.getName(),CONNECT);
                if (reqLinkedin != null && reqLinkedin.getStatus().equalsIgnoreCase(Status.INITIAL.getName())) {
                    reqLinkedin.setStatus(Status.CANCEL.getName());
                    reqLinkedin.setErrorLog(ex.getMessage()!=null?ex.getMessage().substring(0,Math.min(ex.getMessage().length(), 4000)):null);
                    businessGetPageService.saveAndFlush(reqLinkedin);
                }
                pushCheckStatusInFirebase(SocialChannel.LINKEDIN.getName(),CONNECT,Status.COMPLETE.getName(),businessId,true);
            }
        }else{
            LOGGER.info("Could not acquire redis lock for key {} ", key);
            handleFailureLock(request,SocialChannel.LINKEDIN.getName(),key,businessId,CONNECT);
        }
    }

    private LinkedinCreds getLinkedinCreds(Long businessId) throws Exception {
        SocialAppCredsInfo domainInfo = socialAppService.getLinkedinAppSettings();
        LOGGER.info("The domain info for the call is:{}",domainInfo);
        if(Objects.isNull(domainInfo)) {
            throw new Exception("No credentials found for linkedin");
        }
        return new LinkedinCreds(domainInfo.getChannelClientId(), domainInfo.getChannelClientSecret());
    }

    public BusinessGetPageRequest submitLinkedinGetPageRequest(Long businessId, Integer birdeyeUserId, AccessTokenInfo accessTokenInfo,String connectType,String type) {
        BusinessGetPageRequest businessGetPageRequest = new BusinessGetPageRequest();
        businessGetPageRequest.setBirdeyeUserId(birdeyeUserId);
        businessGetPageRequest.setChannel(SocialChannel.LINKEDIN.getName());
        if(Constants.ENTERPRISE.equals(type)) {
        	businessGetPageRequest.setEnterpriseId(businessId);
        }else {
        	businessGetPageRequest.setResellerId(businessId);
        }
        businessGetPageRequest.setStatus(Status.INITIAL.getName());
        businessGetPageRequest.setPageCount(0);
        businessGetPageRequest.setRequestType(connectType);
        businessGetPageRequest.setUserAccessToken(accessTokenInfo.getAccessToken());
        return businessGetPageService.save(businessGetPageRequest);
    }


    @Override
    public void fetchLinkedinPages(BusinessGetPageRequest linkedinRequest, AccessTokenInfo accessTokenInfo,String type) {
        Integer pageCount = 0;
        Integer totalCount = 0;
        Set<String> oldRequestIds = new HashSet<>();
        Long parentId=Constants.ENTERPRISE.equals(type)?linkedinRequest.getEnterpriseId():linkedinRequest.getResellerId();
        String key = SocialChannel.LINKEDIN.getName().concat(String.valueOf(parentId));
        try{
            try {
                LOGGER.info("delaying request for business Id in setup for {} {}",parentId,CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("linkedin.delay.millis"));
                Thread.sleep(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("linkedin.delay.millis"));
            } catch (InterruptedException e) {
                LOGGER.error("exception occured while sleep after fetching access token {}",e);
            }
            populateEmailInRequest(linkedinRequest); // no rollback
            String requestId = linkedinRequest.getId().toString();
            List<BusinessLinkedinPage> businessLinkedinPages = fetchAllPagesForAccount(accessTokenInfo, requestId,linkedinRequest.getEmail());
            linkedinRequest.setSocialUserId(getProfileIdFromLinkedinPagesOfSameAccount(businessLinkedinPages));

            LOGGER.info("List of linkedin Pages fetched: {} for business: {}",businessLinkedinPages,parentId);
            LinkedinRefreshToken linkedinRefreshToken = socialLinkedinRefreshTokenService.createOrUpdateLinkedinRefreshToken(linkedinRequest.getBirdeyeUserId(), linkedinRequest.getSocialUserId(), accessTokenInfo);

            Map<String,List<BusinessLinkedinPage>> listMap = businessLinkedinPages.stream().collect(groupingBy(BusinessLinkedinPage :: getPageType));
            LOGGER.info("List map for fetched pages: {} for business: {}",listMap,parentId);
            if(MapUtils.isNotEmpty(listMap)){
                for(Map.Entry m : listMap.entrySet()){
                    String pageType = (String)m.getKey();
                    List<BusinessLinkedinPage> newPages = (List<BusinessLinkedinPage>)m.getValue();
                    Map<String,BusinessLinkedinPage> newBusinessLinkedinPagesMap = newPages.stream().collect(Collectors.toMap(BusinessLinkedinPage::getProfileId,Function.identity(), (first,second)-> second));
                    List<String> profileIds = newPages.stream().map(BusinessLinkedinPage::getProfileId).collect(Collectors.toList());
                    List<BusinessLinkedinPage> existingLinkedinPages = linkedInRepo.findByProfileIdInAndPageType(profileIds,pageType);
                    Map<String,BusinessLinkedinPage> existingLinkedinPagesMap = existingLinkedinPages.stream().collect(Collectors.toMap(BusinessLinkedinPage :: getProfileId, Function.identity()));
                    List<String> updatedProfileIds = new ArrayList<>();
                    if(CollectionUtils.isNotEmpty(existingLinkedinPages)){
                        existingLinkedinPages.forEach(businessLinkedinPage -> {
                            oldRequestIds.add(businessLinkedinPage.getBusinessGetPageId());
                            businessLinkedinPage.setBusinessGetPageId(linkedinRequest.getId().toString());
                            businessLinkedinPage.setUpdatedBy(linkedinRequest.getBirdeyeUserId());
                            if(businessLinkedinPage.getEnterpriseId() == null && businessLinkedinPage.getResellerId()==null){
                                BusinessLinkedinPage page = newBusinessLinkedinPagesMap.get(businessLinkedinPage.getProfileId());
                                updateExisitngPage(businessLinkedinPage, page,linkedinRefreshToken);
                            }
                            updatedProfileIds.add(businessLinkedinPage.getProfileId());
                            commonService.sendLinkedinSetupAuditEvent(SocialSetupAuditEnum.UPDATE_PAGE.name(), Arrays.asList(businessLinkedinPage),
                                    linkedinRequest.getBirdeyeUserId().toString(), businessLinkedinPage.getBusinessId(), parentId);
                        });
                        pushToKafkaForValidity(LINKEDIN, updatedProfileIds);
                        linkedInRepo.save(existingLinkedinPages);
                        existingLinkedinPages.forEach(page->commonService.uploadPageImageToCDN(page));
                        removeExistingPages(businessLinkedinPages, existingLinkedinPagesMap);
                        pageCount = businessLinkedinPages.size();
                    }else{
                        pageCount = businessLinkedinPages.size();
                    }

                    if(CollectionUtils.isNotEmpty(existingLinkedinPages)) {
                        totalCount = existingLinkedinPages.size() + pageCount;
                    } else {
                        totalCount = pageCount;
                    }
                }

                if(CollectionUtils.isNotEmpty(oldRequestIds)) {// send event to check invalid get page requests and mark them as cancelled
                    kafkaProducer.sendObjectV1(VALIDATE_PAGE_REQUEST, new CheckInvalidGetPageState(LINKEDIN, oldRequestIds));
                }

                LOGGER.info("totalCount of Linkedin pages: {} for enterprise Id: {} and reseller Id {}",totalCount,linkedinRequest.getEnterpriseId(),linkedinRequest.getResellerId());

                if(businessLinkedinPages.size()>0){
                    List<String> updatedProfileIds = new ArrayList<>();
                    businessLinkedinPages.forEach(businessLinkedinPage -> {
                        businessLinkedinPage.setCreatedBy(linkedinRequest.getBirdeyeUserId());
                        businessLinkedinPage.setUpdatedBy(linkedinRequest.getBirdeyeUserId());
                        businessLinkedinPage.setRefreshToken(linkedinRefreshToken.getId());
                        updatedProfileIds.add(businessLinkedinPage.getProfileId());
                    });
                    pushToKafkaForValidity(LINKEDIN, updatedProfileIds);
                    linkedInRepo.save(businessLinkedinPages);
                    businessLinkedinPages.forEach(page -> {
                        SocialTokenValidationDTO socialTokenValidationDTO = new SocialTokenValidationDTO();
                        socialTokenValidationDTO.setId(page.getId());
                        socialTokenValidationDTO.setChannel(SocialChannel.LINKEDIN.getName());
                        kafkaProducerService.sendObjectV1(KafkaTopicEnum.LINKEDIN_PERMISSION_CHECK.getName(), socialTokenValidationDTO);
                        commonService.uploadPageImageToCDN(page);
                    });
                    commonService.sendLinkedinSetupAuditEvent(SocialSetupAuditEnum.ADD_PAGES.name(), businessLinkedinPages,
                            linkedinRequest.getBirdeyeUserId().toString(), null, parentId);
                }
            }

            linkedinRequest.setPageCount(pageCount);
            linkedinRequest.setTotalPages(totalCount);
            if(Objects.isNull(linkedinRequest.getTotalPages()) || linkedinRequest.getTotalPages() == 0){
                linkedinRequest.setStatus(Status.NO_PAGES_FOUND.getName());
                redisService.release(key);
            }else{
                linkedinRequest.setStatus(Status.FETCHED.getName());
            }
            pushCheckStatusInFirebase(SocialChannel.LINKEDIN.getName(),linkedinRequest.getRequestType(),linkedinRequest.getStatus(),parentId);
            businessGetPageService.saveAndFlush(linkedinRequest);
        }
        catch (Exception ex){
            redisService.release(key);
            LOGGER.error("[Redis Lock] (Linkedin) Lock released for business {}, error", parentId, ex);
            //Cleanup business get pages request table
            if (linkedinRequest != null) {
                linkedinRequest.setStatus(Status.CANCEL.getName());
                linkedinRequest.setErrorLog(ex.getMessage()!=null?ex.getMessage().substring(0,Math.min(ex.getMessage().length(), 4000)):null);
                businessGetPageService.saveAndFlush(linkedinRequest);
            }
            pushCheckStatusInFirebase(SocialChannel.LINKEDIN.getName(),CONNECT,Status.COMPLETE.getName(),parentId,true);
        }
    }

    @Override
    public void pushToKafkaForValidity(String channel, Collection<String> profileIds) {
        if(CollectionUtils.isEmpty(profileIds)) {
            return;
        }
        ValidityRequestDTO validityRequestDTO = new ValidityRequestDTO();
        validityRequestDTO.setChannel(channel);
        validityRequestDTO.setLinkedinProfileIds(profileIds);
        kafkaProducer.sendObject(Constants.CHECK_VALIDITY,validityRequestDTO);
    }

    @Override
    public String getProfileIdFromLinkedinPagesOfSameAccount(List<BusinessLinkedinPage> businessLinkedinPages) {
        for (BusinessLinkedinPage businessLinkedinPage : businessLinkedinPages) {
            if (LinkedinPageTypeEnum.PROFILE.getName().equalsIgnoreCase(businessLinkedinPage.getPageType())) {
                return businessLinkedinPage.getProfileId();
            }
        }
        return null;
    }

    private void populateEmailInRequest(BusinessGetPageRequest linkedinRequest) {
        try{
            LinkedinContactInfo linkedinContactInfo = linkedinContactDetails(linkedinRequest.getUserAccessToken());
            if(Objects.nonNull(linkedinContactInfo) && CollectionUtils.isNotEmpty(linkedinContactInfo.getElements())){
                Map<String,Object> map = linkedinContactInfo.getElements().get(0);
                Map<String,Object> map1 = (Map<String, Object>) map.get("handle~");
                if(Objects.nonNull(map1) && Objects.nonNull(map1.get("emailAddress"))){
                    String email = (String) map1.get("emailAddress");
                    linkedinRequest.setEmail(email);
                }
            }
        }catch (Exception ex){
            LOGGER.error("error occurred while setting email id in business get page request for business {} {}",linkedinRequest.getEnterpriseId(),ex);
        }
    }

    @Override
    public void reconnectLinkedinPagesFlow(LinkedinAuthRequest linkedinAuthRequest,String type) throws Exception {
        Long businessId = linkedinAuthRequest.getBusinessId();
        Integer userId = linkedinAuthRequest.getBirdeyeUserId();
        String key = SocialChannel.LINKEDIN.getName().concat(String.valueOf(businessId));
        boolean lock = redisService.tryToAcquireLock(key);
        LOGGER.info("[Redis Lock for reconnect linkedin] Lock status for business : {} : {}",businessId,lock);
        if(lock){
            try{
                LinkedinCreds linkedinCreds = getLinkedinCreds(businessId);
                AccessTokenInfo accessTokenInfo = linkedinConnectService.getAccessToken(linkedinCreds,linkedinAuthRequest.getRedirectUri(),linkedinAuthRequest.getAuthCode());
                List<String> statusList = Arrays.asList(Status.INITIAL.getName(), Status.FETCHED.getName());
                String channel = SocialChannel.LINKEDIN.getName();
                List<BusinessGetPageRequest> requests =Constants.RESELLER.equals(type)?businessGetPageService.findByResellerIdAndStatusInAndChannel(businessId,
						statusList, SocialChannel.LINKEDIN.getName()):businessGetPageService.findByEnterpriseIdAndStatusInAndChannel(businessId, statusList, channel);
                if(CollectionUtils.isEmpty(requests)){
                    BusinessGetPageRequest businessGetPageRequest = submitLinkedinGetPageRequest(businessId, userId, accessTokenInfo,RECONNECT,type);
                    pushCheckStatusInFirebase(SocialChannel.LINKEDIN.getName(),RECONNECT,Status.INITIAL.getName(), businessId);
                    socialProxyHandler.runInAsync(() -> {
                        reconnectPages(businessGetPageRequest,linkedinAuthRequest,accessTokenInfo,type);
                        return true;
                    });
                }else{
                    LOGGER.info("Linkedin Reconnect BusinessGetPageRequest found with status INITIAL and FETCHED , hence returning for businessId and userId : {} , {}",businessId,userId);
//                    pushCheckStatusInFirebase(SocialChannel.LINKEDIN.getName(),requests.get(0).getRequestType(),requests.get(0).getStatus(),businessId);
                    throw new BirdeyeSocialException(ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS, ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.name());

                }
            }catch (Exception ex){
                redisService.release(key);
                LOGGER.error("exception occurred while reconnect for businessId {} , {}",businessId,ex);
                if (ex instanceof BirdeyeSocialException) {
                    BirdeyeSocialException beExp = (BirdeyeSocialException) ex;
                    if (beExp.getCode() == ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.value())
                        throw beExp;
                }
                BusinessGetPageRequest reqLinkedin =Constants.RESELLER.equals(type)? businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(businessId,SocialChannel.LINKEDIN.getName(),RECONNECT):businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(businessId,SocialChannel.LINKEDIN.getName(),RECONNECT);
                if (reqLinkedin != null && reqLinkedin.getStatus().equalsIgnoreCase(Status.INITIAL.getName())) {
                    reqLinkedin.setStatus(Status.CANCEL.getName());
                    reqLinkedin.setErrorLog(ex.getMessage()!=null?ex.getMessage().substring(0,Math.min(ex.getMessage().length(), 4000)):null);
                    businessGetPageService.saveAndFlush(reqLinkedin);
                    pushCheckStatusInFirebase(SocialChannel.LINKEDIN.getName(),reqLinkedin.getRequestType(),Status.COMPLETE.getName(),businessId,true);
                }else{
                    pushCheckStatusInFirebase(SocialChannel.LINKEDIN.getName(),CONNECT,Status.COMPLETE.getName(),businessId,true);
                }
            }
        } else {
            LOGGER.info("Linkedin Reconnect: Lock is already acquired for business {}", businessId);
            throw new BirdeyeSocialException(ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS, ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.name());
        }
    }

    @Override
    public void reconnectPages(BusinessGetPageRequest businessGetPageRequest, LinkedinAuthRequest linkedinAuthRequest,AccessTokenInfo accessTokenInfo, String type) {
    	Long parentId=Constants.RESELLER.equals(type)?businessGetPageRequest.getResellerId():businessGetPageRequest.getEnterpriseId();
        try{
            LOGGER.info("delaying request for enterprise Id in setup reconnect for {} {}",parentId,CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("linkedin.delay.millis"));
            Thread.sleep(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("linkedin.delay.millis"));
            List<BusinessLinkedinPage> businessLinkedinPagesAll = fetchAllPagesForAccount(accessTokenInfo,businessGetPageRequest.getId().toString(),businessGetPageRequest.getEmail());
            businessGetPageRequest.setSocialUserId(getProfileIdFromLinkedinPagesOfSameAccount(businessLinkedinPagesAll));
           //We need to add the pagesInfo field to the request for Reseller business. This field is required in the current thread, but it is currently missing from the request. 
            if(Constants.RESELLER.equals(type)) {
        	   updateRequestForReseller(businessLinkedinPagesAll,linkedinAuthRequest);
           }
            Map<String,List<LocationPageMappingRequest>> listMap = linkedinAuthRequest.getPagesInfo().stream().collect(groupingBy(LocationPageMappingRequest ::getType));
            AtomicReference<Integer> reconnectedCount = new AtomicReference<>(0);
            if(MapUtils.isNotEmpty(listMap) && CollectionUtils.isNotEmpty(businessLinkedinPagesAll)){
                List<BusinessLinkedinPage> updatePage = new ArrayList<>();
                LinkedinRefreshToken linkedinRefreshToken =socialLinkedinRefreshTokenService.createOrUpdateLinkedinRefreshToken(businessGetPageRequest.getBirdeyeUserId(),
                        businessGetPageRequest.getSocialUserId(), accessTokenInfo);

                for(Map.Entry<String,List<LocationPageMappingRequest>> m : listMap.entrySet()){
                    String pageType = m.getKey();
                    List<LocationPageMappingRequest> locationPageMappingRequests = m.getValue();
                    List<String> profileIds = locationPageMappingRequests.stream().map(LocationPageMappingRequest :: getPageId).collect(Collectors.toList());
                    List<BusinessLinkedinPage> existingBusinessLinkedinPages = linkedInRepo.findByProfileIdInAndPageType(profileIds,pageType);
                    Map<String, BusinessLinkedinPage> pageMappingCounts = existingBusinessLinkedinPages.stream().collect(Collectors.toMap(BusinessLinkedinPage :: getProfileId, page -> page));
                    businessLinkedinPagesAll.forEach(businessLinkedinPage -> {
                        if(pageMappingCounts.get(businessLinkedinPage.getProfileId()) != null){
                            reconnectedCount.getAndSet(reconnectedCount.get() + 1);
                            BusinessLinkedinPage existingPage = pageMappingCounts.get(businessLinkedinPage.getProfileId());
                            existingPage.setIsValid(1);
                            brokenIntegrationService.pushValidIntegrationStatus(existingPage.getEnterpriseId(), SocialChannel.LINKEDIN.getName(),existingPage.getId(),1,null);
                            existingPage.setAccessToken(accessTokenInfo.getAccessToken());
                            existingPage.setExpiresOn(accessTokenInfo.getExpireTime());
                            existingPage.setCreatedBy(businessGetPageRequest.getBirdeyeUserId());
                            existingPage.setUpdatedBy(businessGetPageRequest.getBirdeyeUserId());
                            existingPage.setScope(businessLinkedinPage.getScope());
                            existingPage.setErrorLog(null);
                            existingPage.setLinkedinErrorCode(null);
                            existingPage.setBusinessGetPageId(businessGetPageRequest.getId().toString());
                            existingPage.setRefreshToken(linkedinRefreshToken.getId());
                            updatePage.add(existingPage);
                        }
                    });
                }
                if(CollectionUtils.isNotEmpty(updatePage)){
                    linkedInRepo.save(updatePage);
                    updatePage.forEach(page->commonService.uploadPageImageToCDN(page));
                    linkedInRepo.flush();

                    updatePage.forEach(page -> {
                        SocialTokenValidationDTO socialTokenValidationDTO = new SocialTokenValidationDTO();
                        socialTokenValidationDTO.setId(page.getId());
                        socialTokenValidationDTO.setChannel(SocialChannel.LINKEDIN.getName());
                        kafkaProducerService.sendObjectV1(KafkaTopicEnum.LINKEDIN_PERMISSION_CHECK.getName(), socialTokenValidationDTO);
                        commonService.sendLinkedinSetupAuditEvent(SocialSetupAuditEnum.RECONNECT_PAGE.name(), Collections.singletonList(page),
                                null, page.getBusinessId(), page.getEnterpriseId());
                        engageDisconnectDataEvent(page);
                    });
                }
            }

            //Marking Request as COMPLETE
            businessGetPageRequest.setPageCount(reconnectedCount.get());
            businessGetPageRequest.setTotalPages(businessLinkedinPagesAll.size());
           // if(businessLinkedinPagesAll.size() == 0) {
             //   businessGetPageRequest.setStatus(Status.NO_PAGES_FOUND.getName());
            //} else {
                businessGetPageRequest.setStatus(Status.COMPLETE.getName());
            //}
            populateEmailInRequest(businessGetPageRequest);
            businessGetPageService.saveAndFlush(businessGetPageRequest);
            pushCheckStatusInFirebase(SocialChannel.LINKEDIN.getName(),RECONNECT,businessGetPageRequest.getStatus(),parentId);
            releaseLock(businessGetPageRequest.getChannel(),parentId);
        }catch (Exception ex){
            releaseLock(businessGetPageRequest.getChannel(),parentId);
            LOGGER.error("[Redis Lock] (Linkedin Reconnect) Lock released for business {}, error", parentId, ex);
            //Cleanup business get pages request table
            if (businessGetPageRequest != null) {
                businessGetPageRequest.setStatus(Status.CANCEL.getName());
                businessGetPageRequest.setErrorLog(ex.getMessage()!=null?ex.getMessage().substring(0,Math.min(ex.getMessage().length(), 4000)):null);
                businessGetPageService.saveAndFlush(businessGetPageRequest);
                pushCheckStatusInFirebase(SocialChannel.LINKEDIN.getName(),RECONNECT,Status.COMPLETE.getName(),parentId,true);
            }
        }
    }

    private void updateRequestForReseller(List<BusinessLinkedinPage> businessLinkedinPagesAll,
			LinkedinAuthRequest linkedinAuthRequest) {
		if(CollectionUtils.isNotEmpty(businessLinkedinPagesAll)) {
			List<String> profileIds=businessLinkedinPagesAll.stream().map(page->page.getProfileId()).collect(Collectors.toList());
			List<BusinessLinkedinPage> businessLinkedinPages=linkedInRepo.findByResellerIdAndProfileIdInAndIsValid(linkedinAuthRequest.getBusinessId(),
                    profileIds,0);
			if(!businessLinkedinPages.isEmpty()) {
				List<LocationPageMappingRequest> pageInfos=businessLinkedinPages.stream().map(page->new
                        LocationPageMappingRequest(page.getProfileId(),page.getPageType())).collect(Collectors.toList());
				linkedinAuthRequest.setPagesInfo(pageInfos);
			}
		}
	}

	private void engageDisconnectDataEvent(BusinessLinkedinPage page) {
        LOGGER.info("Inside engageDisconnectDataEvent for profileId :{} ",page.getProfileId());
        if(Objects.nonNull(page.getBusinessId())) {
            kafkaProducer.sendObject(Constants.LINKEDIN_PAGE_UPDATE, new LocationPageMappingRequest(page.getBusinessId(), page.getProfileId()));
        }
    }

    private List<BusinessLinkedinPage> fetchAllPagesForAccount(AccessTokenInfo accessTokenInfo, String requestId, String userEmail) {
        int count = 0;
        int maxTries = 2;
        while(true){
            try{
                LOGGER.info("calling fetch page for {} {}",requestId,count);
                Map<String, Object> tokenIntrospection = getLinkedinTokenDetails(accessTokenInfo.getAccessToken());
                String scope = (String)tokenIntrospection.get("scope");
                List<BusinessLinkedinPage> businessLinkedinPages = new ArrayList<>();
                LOGGER.info("Linkedin : call sent to linkedin for fetch company pages for requestId and token {} {} {} {} {}",requestId,accessTokenInfo.getAccessToken(),accessTokenInfo.getExpireTime(),tokenIntrospection,accessTokenInfo.getRefreshToken());
                LinkedinOrganizationPagesInfo linkedinOrganizationPagesInfo = linkedinOrganizationInfo(accessTokenInfo.getAccessToken());
                LOGGER.info("Linkedin : call sent to linkedin for fetch user profile info for requestId and token {} {} {}",requestId,accessTokenInfo.getAccessToken(),accessTokenInfo.getExpireTime());
                LinkedinUserProfileInfo linkedinUserProfileInfo = getLinkedInUserProfile(accessTokenInfo.getAccessToken());
                if(Objects.nonNull(linkedinOrganizationPagesInfo) && !CollectionUtils.isEmpty(linkedinOrganizationPagesInfo.getElements())){
                    linkedinOrganizationPagesInfo.getElements().forEach(element -> {
                    	if((ADMINISTRATOR.equalsIgnoreCase(element.getRole()) ||
                                (CONTENT_ADMINISTRATOR.equalsIgnoreCase(element.getRole()))) &&
                                (APPROVED).equalsIgnoreCase(element.getState())) {
                    		BusinessLinkedinPage businessLinkedinPage = new BusinessLinkedinPage();
                            String pageName = element.getLocalizedName();
                            String urn = element.getOrganization();
                            businessLinkedinPage.setPageType(LinkedinPageTypeEnum.COMPANY.getName());
                            businessLinkedinPage.setAccessToken(accessTokenInfo.getAccessToken());
                            businessLinkedinPage.setUrn(urn);
                            businessLinkedinPage.setBusinessGetPageId(requestId);
                            businessLinkedinPage.setCompanyName(pageName);
                            businessLinkedinPage.setProfileId(urn.split(LINKEDIN_ORG_PREFIX)[1]);
                            businessLinkedinPage.setPageUrl(LINKEDIN_COMPANY_URL +businessLinkedinPage.getProfileId());
                            businessLinkedinPage.setScope(scope);
                            businessLinkedinPage.setIsValid(1);
                            businessLinkedinPage.setIsSelected(0);
                            businessLinkedinPage.setUserEmailId(userEmail);
                            businessLinkedinPage.setExpiresOn(accessTokenInfo.getExpireTime());
                            businessLinkedinPage.setLogoUrl(element.getLogoUrl());
                            businessLinkedinPage.setVanityName(element.getVanityName());
                            if(Objects.nonNull(linkedinUserProfileInfo)) {
                                businessLinkedinPage.setPersonUrn(linkedinUserProfileInfo.getId());
                            }
                            LOGGER.info("Linkedin page added in list from org: {}",businessLinkedinPage);
                            businessLinkedinPages.add(businessLinkedinPage);
                    	}
                    });
                }
                if(Objects.nonNull(linkedinUserProfileInfo)){
                    BusinessLinkedinPage businessLinkedinPage = new BusinessLinkedinPage();
                    businessLinkedinPage.setPageType("profile");
                    businessLinkedinPage.setAccessToken(accessTokenInfo.getAccessToken());
                    businessLinkedinPage.setUrn("urn:li:person:"+linkedinUserProfileInfo.getId());
                    businessLinkedinPage.setFirstName(linkedinUserProfileInfo.getLocalizedFirstName());
                    businessLinkedinPage.setLastName(linkedinUserProfileInfo.getLocalizedLastName());
                    businessLinkedinPage.setBusinessGetPageId(requestId);
                    businessLinkedinPage.setProfileId(linkedinUserProfileInfo.getId());
                    businessLinkedinPage.setPageUrl(LINKEDIN_PROFILE_URL +linkedinUserProfileInfo.getVanityName());
                    businessLinkedinPage.setScope(scope);
                    businessLinkedinPage.setIsValid(1);
                    businessLinkedinPage.setIsSelected(0);
                    businessLinkedinPage.setUserEmailId(userEmail);
                    businessLinkedinPage.setExpiresOn(accessTokenInfo.getExpireTime());
                    LOGGER.info("Linkedin profile page added in list: {}",businessLinkedinPage);
                    businessLinkedinPages.add(businessLinkedinPage);
                }
                return businessLinkedinPages;
            }catch (Exception ex){
                try {
                    Thread.sleep(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("linkedin.delay.millis"));
                } catch (InterruptedException e) {
                    LOGGER.error("exception occurred while sleep in retry {}",ex);
                }
                LOGGER.error("exception occurred {}",ex);
                if (++count == maxTries) throw ex;
            }
        }
    }

    public void updateExisitngPage(BusinessLinkedinPage existingBusinessLinkedinPage, BusinessLinkedinPage page, LinkedinRefreshToken linkedinRefreshToken) {
        existingBusinessLinkedinPage.setIsValid(1);
        existingBusinessLinkedinPage.setLinkedinErrorCode(null);
        existingBusinessLinkedinPage.setErrorLog(null);
        existingBusinessLinkedinPage.setCompanyName(page.getCompanyName());
        existingBusinessLinkedinPage.setUrn(page.getUrn());
        existingBusinessLinkedinPage.setPageUrl(page.getPageUrl());
        existingBusinessLinkedinPage.setScope(page.getScope());
        existingBusinessLinkedinPage.setRefreshToken(linkedinRefreshToken.getId());
    }

    private void removeExistingPages(List<BusinessLinkedinPage> businessLinkedinPages,
                                     Map<String, BusinessLinkedinPage> existingLinkedinPagesMap) {
        Iterator<BusinessLinkedinPage> itr = businessLinkedinPages.iterator();
        while (itr.hasNext()){
            BusinessLinkedinPage businessLinkedinPage = itr.next();
            if(existingLinkedinPagesMap.get(businessLinkedinPage.getProfileId()) !=null){
                itr.remove();
            }
        }
    }

    private void prepareLocationLinkedinPageMapping(Long enterpriseId , List<Integer> businessIds, LocationPageMapping response, Set<String> status, Integer page, Integer size, String search, Boolean toSearch,List<BusinessLinkedinPage> pages, List<String> includeModules) {
        Map<Integer, BusinessLocationLiteEntity> businessLocationsMap;
        if(toSearch){
            businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(businessIds,page,size,search,response);
        }else{
            businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginated(businessIds,page,size);
        }
        List<BusinessLinkedinPage> linkedinPages = pages.stream().filter(p -> businessLocationsMap.containsKey(p.getBusinessId())).collect(Collectors.toList());

        if (Objects.nonNull(businessLocationsMap)) {
            Map<Integer, BusinessLinkedinPage> businessLinkedinPageMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(linkedinPages)) {
                businessLinkedinPageMap = linkedinPages.stream().collect(Collectors.toMap(BusinessLinkedinPage::getBusinessId, Function.identity()));
            }
            List<ChannelLocationInfo> locationList = getChannelLocationInfoList(businessLocationsMap, businessLinkedinPageMap, response, status, includeModules);

            List<ChannelLocationInfo> locationListWithoutMapping = new LinkedList<>();
            List<ChannelLocationInfo> locationListWithMapping = new LinkedList<>();

            for (ChannelLocationInfo ll : locationList) {
                if (ll.getPageData() != null) {
                    locationListWithMapping.add(ll);
                } else {
                    locationListWithoutMapping.add(ll);
                }
            }

            locationListWithoutMapping.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));
            locationListWithMapping.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));

            List<ChannelLocationInfo> finalList = new LinkedList<>();
            if(status.contains(LocationStatusEnum.UNMAPPED.getName())) {
                finalList.addAll(locationListWithoutMapping);
            }
            if(status.contains(LocationStatusEnum.MAPPED.getName())) {
                finalList.addAll(locationListWithMapping);
            }
            response.setLocationList(finalList);
        }
    }

    private List<BusinessLinkedinPage> getBusinessLinkedinPages(List<BusinessLinkedinPage> linkedinPages, Map<Integer, BusinessLocationLiteEntity> businessLocationsMap) {
        return linkedinPages.stream().filter(businessLinkedinPage -> businessLocationsMap.keySet().contains(businessLinkedinPage.getBusinessId())).collect(Collectors.toList());
    }

    private List<ChannelLocationInfo> getChannelLocationInfoList(Map<Integer, BusinessLocationLiteEntity> businessLocationsMap, Map<Integer, BusinessLinkedinPage> businessLinkedinPageMap, LocationPageMapping response, Set<String> status, List<String> includeModules) {
        LOGGER.info("[Linkedin Setup] com.birdeye.social.linkedin.LinkedinServiceImpl#getChannelLocationInfoList().");
        Integer disconnectedCount = 0;
        List<ChannelLocationInfo> locationList = new ArrayList<>();
        for (Map.Entry<Integer, BusinessLocationLiteEntity> entry : businessLocationsMap.entrySet()) {
            ChannelLocationInfo locInfo = new ChannelLocationInfo();
            locInfo.setLocationId(entry.getKey());
            locInfo.setLocationName(entry.getValue().getAlias1() != null ? entry.getValue().getAlias1() : entry.getValue().getName());
            locInfo.setAddress(prepareBusinessAddress(entry.getValue()));
            if (Objects.nonNull(businessLinkedinPageMap) && businessLinkedinPageMap.get(entry.getKey()) != null) {
                Map<String, LocationPageListInfo> pageInfoMap = new HashMap<>();
                LocationPageListInfo locationPageListInfo = preparePageData(businessLinkedinPageMap.get(entry.getKey()), includeModules);
                pageInfoMap.put(SocialChannel.LINKEDIN.getName(), locationPageListInfo);
                if(locationPageListInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.INVALID.getName()) ||
                        locationPageListInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.PARTIAL_VALID.getName())
                ) {
                    disconnectedCount++;
                }
                locInfo.setPageData(pageInfoMap);
            }
            locationList.add(locInfo);
        }
        if(!(status.size() <= 1 && status.contains(LocationStatusEnum.UNMAPPED.getName())) ) {
            response.setDisconnectedCount(disconnectedCount);
        }

        LOGGER.info("[Linkedin Setup] Exiting com.birdeye.social.linkedin.LinkedinServiceImpl#getChannelLocationInfoList().");
        return locationList;
    }

    private String prepareBusinessAddress(BusinessLocationLiteEntity location) {
        StringBuilder address = new StringBuilder();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(location.getAddress1())) {
            address.append(location.getAddress1()).append(", ");

        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(location.getAddress2())) {
            address.append(location.getAddress2()).append(", ");

        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(location.getCity())) {
            address.append(location.getCity()).append(", ");

        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(location.getState())) {
            address.append(location.getState()).append(" ");

        }
        // Zipcode will be always there
        if (StringUtils.isNotEmpty(location.getZip())) {
            address.append(location.getZip());

        }
        return address.toString();
    }

    private LocationPageListInfo preparePageData(BusinessLinkedinPage businessLinkedinPage, List<String> includeModules) {
        LocationPageListInfo pageInfo = new LocationPageListInfo();
        pageInfo.setType(businessLinkedinPage.getPageType());
        pageInfo.setId(businessLinkedinPage.getProfileId());
        if ( businessLinkedinPage.getPageType().equalsIgnoreCase("company") ) {
            pageInfo.setPageName(businessLinkedinPage.getCompanyName());
        } else if ( businessLinkedinPage.getPageType().equalsIgnoreCase("profile") ) {
            String pageName = StringUtils.isNotEmpty(businessLinkedinPage.getFirstName()) ? businessLinkedinPage.getFirstName() : "";
            if ( StringUtils.isNotEmpty(businessLinkedinPage.getLastName()) ) {
                pageName += (pageName.length() > 0 ? " " : "") + businessLinkedinPage.getLastName();
            }
            pageInfo.setPageName(pageName);
        }
        pageInfo.setLink(businessLinkedinPage.getPageUrl());
        pageInfo.setConnectedInReseller(Objects.isNull(businessLinkedinPage.getResellerId())?false:true);
        Validity validity = fetchValidityAndErrorMessage(businessLinkedinPage);
        pageInfo.setValidType(validity.getValidType());
        pageInfo.setErrorCode(validity.getErrorCode());
        pageInfo.setErrorMessage(validity.getErrorMessage());

        if (CollectionUtils.isNotEmpty(includeModules)) {
            LOGGER.info("Linkedin page: {}",businessLinkedinPage.getProfileId());
            Map<String, PermissionDTO> permissionsMap = new HashMap<>();
            for (String module : includeModules) {
                if(StringUtils.isNotEmpty(businessLinkedinPage.getModuleImpacted())){
                    LOGGER.info("Linkedin page: {} module impacted : {}",businessLinkedinPage.getProfileId(),businessLinkedinPage.getModuleImpacted());
                    if(includeModules.contains(PUBLISH_MODULE) && businessLinkedinPage.getModuleImpacted().contains(EnabledTasks.POSTS.name())){
                        permissionsMap.put(module, new PermissionDTO(false));
                    }else {
                        permissionsMap.put(module,new PermissionDTO(true));
                    }
                }
            }
            pageInfo.setModulePermission(permissionsMap);
        }
        return pageInfo;
    }

    private Map<String, List<ChannelAccountInfo>> getPages(Long businessId, BusinessGetPageRequest request){
        LOGGER.info("getPages request for linkedin for business and requestId {} {} ",businessId,request.getId());
        Map<String, List<ChannelAccountInfo>> pageTypes = new HashMap<>();
        List<ChannelAccountInfo> linkedinPages = getLinkedinPages(request, businessId);
        if(CollectionUtils.isNotEmpty(linkedinPages)){
            pageTypes.put(SocialChannel.LINKEDIN.getName(), linkedinPages);
        }
        return pageTypes;
    }
@Override
    public List<ChannelAccountInfo> getLinkedinPages(BusinessGetPageRequest req, Long enterpriseId) {
        List<BusinessLinkedinPage> businessLinkedinPages = linkedInRepo.findByBusinessGetPageId(req.getId().toString());
        if(!CollectionUtils.isEmpty(businessLinkedinPages)){
            return prepareChannelAccountInfo(businessLinkedinPages, enterpriseId);
        }
        return new ArrayList<>();
    }

    private List<ChannelAccountInfo> prepareChannelAccountInfo(List<BusinessLinkedinPage> businessLinkedinPages, Long enterpriseId) {
        List<ChannelAccountInfo> channelAccountInfos = new ArrayList<>();
        if(CollectionUtils.isEmpty(businessLinkedinPages)){
            return channelAccountInfos;
        }
        businessLinkedinPages.forEach(businessLinkedinPage -> {
            ChannelAccountInfo channelAccountInfo = new ChannelAccountInfo();
            channelAccountInfo.setType(businessLinkedinPage.getPageType());
            if(businessLinkedinPage.getPageType().equalsIgnoreCase(LinkedinPageTypeEnum.COMPANY.getName())){
                channelAccountInfo.setPageName(businessLinkedinPage.getCompanyName());
            }else{
                String fullName = null;
                if(StringUtils.isNotBlank(businessLinkedinPage.getFirstName()) && StringUtils.isNotBlank(businessLinkedinPage.getLastName())){
                    fullName = businessLinkedinPage.getFirstName()+ " "+businessLinkedinPage.getLastName();
                }else{
                    fullName = businessLinkedinPage.getFirstName();
                }
                channelAccountInfo.setPageName(fullName);
            }
            channelAccountInfo.setId(businessLinkedinPage.getProfileId());
            channelAccountInfo.setLink(businessLinkedinPage.getPageUrl());
            Validity validity = fetchValidityAndErrorMessage(businessLinkedinPage);
            channelAccountInfo.setValidType(validity.getValidType());
            channelAccountInfo.setErrorCode(validity.getErrorCode());
            channelAccountInfo.setErrorMessage(validity.getErrorMessage());
            channelAccountInfo.setDisabled((businessLinkedinPage.getIsSelected() != null && businessLinkedinPage.getIsSelected() == 1) ? Boolean.TRUE : Boolean.FALSE);
            if(Objects.nonNull(businessLinkedinPage.getEnterpriseId()) && Objects.nonNull(businessLinkedinPage.getIsSelected())
                    && businessLinkedinPage.getIsSelected() == 1
                    && Objects.nonNull(enterpriseId)) {
                commonService.setCommonChannelAccountInfo(channelAccountInfo, businessLinkedinPage.getBusinessId(),
                        businessLinkedinPage.getIsSelected(), enterpriseId, businessLinkedinPage.getEnterpriseId() );
            }
            if (Objects.nonNull(businessLinkedinPage.getEnterpriseId()) && Objects.nonNull(enterpriseId)) {
                channelAccountInfo.setSameAccountConnections(businessLinkedinPage.getEnterpriseId().equals(enterpriseId));
                channelAccountInfo.setDiffAccountConnections(!businessLinkedinPage.getEnterpriseId().equals(enterpriseId));
            }
            channelAccountInfos.add(channelAccountInfo);
        });
        return channelAccountInfos;
    }

    private void releaseLock(String channel, Long enterpriseId) {
        redisService.release(channel.concat(String.valueOf(enterpriseId)));
    }

    private Integer getBusinessId(Long businessNumber){
        BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest();
        businessLiteRequest.setKey("businessNumber");
        businessLiteRequest.setValue(businessNumber);
        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(businessLiteRequest);
        if(Objects.nonNull(businessLiteDTO)){
            return businessLiteDTO.getBusinessId();
        }
        return null;
    }

    @Override
    public OpenUrlPagesInfo getPagesFetchedByOpenUrl(Long enterpriseId) throws Exception {
        LOGGER.info("getPagesFetchedByOpenUrl linkedin: enterpriseId {}", enterpriseId);
        OpenUrlPagesInfo response = new OpenUrlPagesInfo();
        List<BusinessGetPageOpenUrlRequest> liRequests = businessGetPageOpenUrlReqRepo.findFirstByEnterpriseIdAndChannelAndRequestTypeOrderByCreatedDesc(enterpriseId, SocialChannel.LINKEDIN.getName(), CONNECT);
        LOGGER.info("getPagesFetchedByOpenUrl: List<BusinessGetPageOpenUrlRequest> got result {}", liRequests);

        if ( liRequests != null && !liRequests.isEmpty() ) {
            // only get the pages if status is fetched
            BusinessGetPageOpenUrlRequest liRequest = liRequests.get(0);
            if ( liRequest.getStatus().equalsIgnoreCase(Status.FETCHED.getName()) ) {
                LOGGER.info("getPagesFetchedByOpenUrl: linkedinRequest found with fetched status");
                List<BusinessLinkedinPage> linkedinPages = linkedInRepo.findByBusinessGetPageId(liRequest.getId());
                List<ChannelAccountInfo> info = new ArrayList<>();
                for(BusinessLinkedinPage linkedinPage: linkedinPages) {
                    info.add(getChannelAccountInfo(linkedinPage));
                }
                response.setPageTypes(info);
            } else {
                LOGGER.info("getPagesFetchedByOpenUrl: linkedinRequest found with {} status", liRequest.getStatus());
            }
            response.setStatus(liRequest.getStatus());
            response.setStatusType(liRequest.getRequestType());
        } else {
            LOGGER.info("getPagesFetchedByOpenUrl: No BusinessGetPageOpenUrlRequest found with given input");
            response.setStatus(Status.COMPLETE.getName());
            response.setStatusType("connect");
        }

        LOGGER.info("getPagesFetchedByOpenUrl: Returning response {}", response);
        return response;
    }

    private ChannelAccountInfo getChannelAccountInfo(BusinessLinkedinPage linkedinPage) {
        ChannelAccountInfo accInfo = new ChannelAccountInfo();
        accInfo.setId(String.valueOf(linkedinPage.getProfileId()));
        accInfo.setLink(linkedinPage.getPageUrl());
        Validity validity = fetchValidityAndErrorMessage(linkedinPage);
        accInfo.setValidType(validity.getValidType());
        accInfo.setErrorCode(validity.getErrorCode());
        accInfo.setErrorMessage(validity.getErrorMessage());
        accInfo.setDisabled((linkedinPage.getIsSelected() != null && linkedinPage.getIsSelected() == 1) ? Boolean.TRUE : Boolean.FALSE);
        accInfo.setType(linkedinPage.getPageType());
        if(linkedinPage.getPageType().equalsIgnoreCase("company")){
            accInfo.setPageName(linkedinPage.getCompanyName());
        }else{
            String fullName = null;
            if(StringUtils.isNotBlank(linkedinPage.getFirstName()) && StringUtils.isNotBlank(linkedinPage.getLastName())){
                fullName = linkedinPage.getFirstName() + " "+ linkedinPage.getLastName() ;
            }else{
                fullName =linkedinPage.getFirstName();
            }
            accInfo.setPageName(fullName);
        }
        return accInfo;
    }

    @Override
    public OpenUrlPagesInfo connectPagesFetchedByOpenUrl(Long enterpriseId, OpenUrlConnectRequest connectRequest, Integer userId) {
        LOGGER.info("connectPagesFetchedByOpenUrl : enterpriseId {} connectRequest {} userId {}", enterpriseId, connectRequest, userId);
        if ( enterpriseId == null || connectRequest == null || StringUtils.isEmpty(connectRequest.getFirebaseKey()) || CollectionUtils.isEmpty(connectRequest.getPageRequests()) ) {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid value for enterpriseId/connectRequest/userId");
        }
        final String firebaseKey = connectRequest.getFirebaseKey();
        Map<String,List<com.birdeye.social.sro.PageRequest>> pageRequestMapByType = connectRequest.getPageRequests().stream().collect(groupingBy(com.birdeye.social.sro.PageRequest::getType));
        OpenUrlPagesInfo response = new OpenUrlPagesInfo();
        List<BusinessGetPageOpenUrlRequest> liRequests = getRequestForOpenUrlBusiness(enterpriseId, Status.FETCHED.getName(), SocialChannel.LINKEDIN.getName(), "connect", firebaseKey);
        if ( CollectionUtils.isEmpty(liRequests) ) {
            // case of no request found
            LOGGER.error("connectPagesFetchedByOpenUrl : No rows found with fetched status for pageRequest {} enterpriseId {} firebaseKey {}", pageRequestMapByType, enterpriseId, firebaseKey);
            throw new BirdeyeSocialException(ErrorCodes.STATUS_CHANGED, "Seems request status has already changed");
        } else if ( liRequests.size() > 1 ) {
            // case of multiple requests present
            LOGGER.error("connectPagesFetchedByOpenUrl : Multiple rows found with fetched status for pageRequest {} and enterpriseId {} firebaseKey {}", pageRequestMapByType, enterpriseId, firebaseKey);
            throw new BirdeyeSocialException(ErrorCodes.MULTI_FETCHED_ROWS, "Multiple rows with fetched status found in BusinessGetPageOpenUrlRequest");
        } else {
            // Get details of business using core API
            BusinessLiteDTO business = businessCoreService.getBusinessLiteByNumber(enterpriseId);
            boolean isBusinessNotMapped = false;
            List<ChannelAccountInfo> channelAccountInfos = new ArrayList<>();
            if ( Objects.isNull(business) ) {
                throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, "Business not found using Business Lite API");
            }
            List<BusinessLinkedinPage> newPages = new ArrayList<>();
            for(Map.Entry<String,List<com.birdeye.social.sro.PageRequest>> entry : pageRequestMapByType.entrySet()){
                String pageType = entry.getKey();
                List<com.birdeye.social.sro.PageRequest> pageRequests = entry.getValue();
                List<String> pageIds = pageRequests.stream().map(com.birdeye.social.sro.PageRequest::getId).collect(Collectors.toList());
                List<BusinessLinkedinPage> liPages = linkedInRepo.findByProfileIdInAndPageType(pageIds,pageType);
                if ( CollectionUtils.isEmpty(liPages) ) {
                    LOGGER.error("connectPagesFetchedByOpenUrl : No LinkedIn pages found with pageIds {}", pageIds);
                    throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_PAGES_NOT_FOUND, "LinkedIn Pages not found with given pageIds");
                }
                isBusinessNotMapped = isBusinessNotMappedToLinkedinPage(business);
                boolean isSmbAndMapped = checkBusinessSMB(business) && !isBusinessNotMapped;
                LOGGER.info("connectPagesFetchedByOpenUrl: checkBusinessSMB {} isBusinessNotMapped {} invalidSmbCase {}", checkBusinessSMB(business), isBusinessNotMapped, isSmbAndMapped);
                for ( BusinessLinkedinPage liPage : liPages) {
                    if ( liPage.getEnterpriseId() == null && !isSmbAndMapped ) { // case of newly found page
                        liPage.setIsSelected(1);
                        liPage.setEnterpriseId(enterpriseId);
                        liPage.setAccountId(business.getBusinessId());
                        linkedInRepo.saveAndFlush(liPage);
                        newPages.add(liPage);
                    } else if ( liPage.getEnterpriseId() != null && liPage.getEnterpriseId().equals(enterpriseId) ) {
                        liPage.setIsValid(1);
                        linkedInRepo.saveAndFlush(liPage);
                        brokenIntegrationService.pushValidIntegrationStatus(liPage.getEnterpriseId(),SocialChannel.LINKEDIN.getName(),liPage.getId(),liPage.getIsValid(),null);
                    } else if ( isSmbAndMapped ) {
                        setupAuditRepo.saveAndFlush(new SocialSetupAudit("OPEN_URL_CONNECT", business.getBusinessId(),
                                liPage.getProfileId(), String.valueOf(userId), liPage.toString(), SocialChannel.LINKEDIN.getName(),
                                "SMB account is already mapped"));
                    } else {
                        setupAuditRepo.saveAndFlush(new SocialSetupAudit("OPEN_URL_CONNECT", business.getBusinessId(),
                                liPage.getProfileId(), String.valueOf(userId), liPage.toString(), SocialChannel.LINKEDIN.getName(),
                                "This page is already connected with some other enterpriseId"));
                    }
                    channelAccountInfos.add(getChannelAccountInfo(liPage));
                }
            }
            // prepare response
            response.setPageTypes(channelAccountInfos);
            response.setStatus(Status.COMPLETE.getName());
            response.setStatusType("connect");
            LOGGER.info("connectPagesFetchedByOpenUrl : Connected LinkedIn Pages {}", pageRequestMapByType);

            // update request in db
            liRequests.get(0).setStatus(Status.COMPLETE.getName());
            liRequests.get(0).setUpdated(new Date());
            businessGetPageOpenUrlReqRepo.saveAndFlush(liRequests.get(0));
            LOGGER.info("connectPagesFetchedByOpenUrl : Request status updated to complete");

            // try to map business & page if it is valid SMB case
            if (checkBusinessSMB(business) && isBusinessNotMapped) {
                try {
                    LOGGER.info("connectPagesFetchedByOpenUrl : Trying to map business {} to pageId {}", business.getBusinessId(), connectRequest.getPageRequests().get(0).getId());
                  saveLinkedinLocationMapping(business.getBusinessId(), connectRequest.getPageRequests().get(0).getId(),connectRequest.getPageRequests().get(0).getType(),Constants.ENTERPRISE, userId, null);
                } catch (Exception saveMappingException) {
                    // we need to return 200 OK response even if mapping fails
                    LOGGER.error("connectPagesFetchedByOpenUrl : Failed to map business with page with error {}", saveMappingException.getMessage());
                    setupAuditRepo.saveAndFlush(new SocialSetupAudit("OPEN_URL_CONNECT", business.getBusinessId(),
                            connectRequest.getPageRequests().get(0).getId(), String.valueOf(userId), null, SocialChannel.LINKEDIN.getName(),
                            "SMB account is already mapped"));
                }
            }else{
                if(CollectionUtils.isNotEmpty(newPages)){
                    LOGGER.info("publishing SOCIAL_PAGE_CONNECT event for linkedin open url for enterpriseID {}",enterpriseId);
                    publishConnectPageRequest(newPages);
                }
            }
        }

        LOGGER.info("connectPagesFetchedByOpenUrl : Response {}", response);
        return response;
    }

    private boolean isBusinessNotMappedToLinkedinPage(BusinessLiteDTO business) {
        return linkedInRepo.findByBusinessId(business.getBusinessId()).isEmpty();
    }

    @Override
    public void refreshAccessToken(BusinessLinkedinPage linkedinPage) {
        LinkedinRefreshToken linkedinRefreshToken = socialLinkedinRefreshTokenService.findById(linkedinPage.getRefreshToken());
        AccessTokenInfo accessTokenInfo = generateAccessTokenFromRefreshToken(linkedinRefreshToken,linkedinPage.getEnterpriseId());
        if(Objects.nonNull(accessTokenInfo)){
            linkedInRepo.updateAccessTokenByRefreshTokenId(linkedinPage.getRefreshToken(),accessTokenInfo.getAccessToken(),accessTokenInfo.getExpireTime(),new Date());

            linkedinRefreshToken.setRefreshToken(accessTokenInfo.getRefreshToken());
            linkedinRefreshToken.setExpiresOn(accessTokenInfo.getRefreshTokenExpiresIn());
            socialLinkedinRefreshTokenService.updateLinkedinRefreshToken(linkedinRefreshToken);
            LOGGER.info("access token updeted for linkedin page: {} on: {}, which expires in: {}", linkedinPage.getProfileId(), new Date(), accessTokenInfo.getRefreshTokenExpiresIn());
        }
    }

    @Override
    public AccessTokenInfo generateAccessTokenFromRefreshToken(LinkedinRefreshToken linkedinRefreshToken,Long enterpriseId) {
        LinkedinCreds linkedinCreds = null;
        try {
            linkedinCreds = getLinkedinCreds(enterpriseId);
        } catch (Exception e) {
            LOGGER.info("No creds found for the linkedin");
           return null;
        }
        return linkedinConnectService.refreshAccessToken(linkedinCreds,linkedinRefreshToken);
    }

    @Override
    public void saveLinkedinLocationMapping(Integer locationId, String pageId, String pageType,String type, Integer userId, Long resellerId) {
        LOGGER.info("[Linkedin Setup] Linkedin profile Id {} mapping with location Id {} pageType {} userId {}", pageId, locationId, pageType, userId);
        BusinessLinkedinPage businessLinkedinPage = linkedInRepo.findByProfileIdAndPageType(pageId, pageType);
        if(Objects.nonNull(businessLinkedinPage)) {
            if(Objects.nonNull(businessLinkedinPage.getBusinessId())){
                LOGGER.error("[Linkedin Setup] For Linkedin profile id {} already mapped ", pageId);
                throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_PAGE_ALREADY_MAPPED, "linkedin page already mapped");
            }
            if(linkedInRepo.existsByBusinessId(locationId)){
                throw new BirdeyeSocialException(ErrorCodes.MAPPING_ALREADY_EXISTS,MAPPING_ALREADY_EXIST_ERROR);
            }
            if(Objects.nonNull(resellerId)){
                commonService.checkRequestFromAuthorizedSourceUsingLongResellerID(businessLinkedinPage.getResellerId(), resellerId);
            }
            businessLinkedinPage.setBusinessId(locationId);
            businessLinkedinPage.setUpdatedBy(userId);
            updateLinkedInPageForReseller(businessLinkedinPage, type, locationId);
            linkedInRepo.saveAndFlush(businessLinkedinPage);

            commonService.sendLinkedinSetupAuditEvent(SocialSetupAuditEnum.ADD_MAPPING.name(), Arrays.asList(businessLinkedinPage), String.valueOf(userId), locationId,Constants.ENTERPRISE.equals(type)?businessLinkedinPage.getEnterpriseId():businessLinkedinPage.getResellerId());

            LOGGER.info("[Linkedin Setup] Linkedin profile Id {} mapped with location Id {}", pageId, locationId);
            kafkaProducerService.sendObject(Constants.LINKEDIN_PAGE_MAPPING_ADDED, new LocationPageMappingRequest(locationId, pageId,pageType));
            //subscribeWebhookForLinkedin(businessLinkedinPage);

        } else {
            LOGGER.error("[Linkedin Setup] For Linkedin page id {} no data found ", pageId);
            throw new BirdeyeSocialException(ErrorCodes.LINKEDIN_PAGE_NOT_FOUND, "linkedin page data not found");
        }
    }



    @Override
    public void updateSubscription(SocialEnabledStatusDto socialEnabledStatusDto) {
        LOGGER.info("Request received to update subscription for linkedin payload: {}", socialEnabledStatusDto);
            Set<Integer> socialCode = new HashSet<>(SOCIAL_ENGAGE_ENABLED);
            socialCode.addAll(SOCIAL_LISTEN_ENABLED);
            List<BusinessLinkedinPage> linkedinPage = linkedInRepo.
                    findAllValidAccounts(Collections.singletonList(socialEnabledStatusDto.getEnterpriseId()),"company");
            if (CollectionUtils.isNotEmpty(linkedinPage)) {
                linkedinPage.forEach(page -> {
                    try {
                        if (Objects.nonNull(page)) {
                            if (socialCode.contains(socialEnabledStatusDto.getOldSocialEnabledValue()) && !socialCode.contains(socialEnabledStatusDto.getNewSocialEnabledValue())) {
                                LOGGER.info("UnSubscribing mention for Linkedin account: {}, pushing to kafka", page.getProfileId());
                                unsubscribeSocial(page);
                            } else if (!socialCode.contains(socialEnabledStatusDto.getOldSocialEnabledValue()) && socialCode.contains(socialEnabledStatusDto.getNewSocialEnabledValue())) {
                                LOGGER.info("Subscribing mention for Linkedin account: {}, pushing to kafka", page.getProfileId());
                                subscribeSocial(page);
                            }
                        }
                    } catch (Exception e) {
                        LOGGER.info("Error updating subscription for Linkedin account: {}", page.getProfileId(), e);
                    }
                });
            }
        }

    @Override
    public void updatePermission() {
        LOGGER.info("Started JOB for linkedin to update permissions");
        int page = 0;
        int size = CacheManager.getInstance().getCache(SystemPropertiesCache.class).pageSizeForDailyLinkedInPageScan();
        Page<BusinessLinkedinPage> businessLinkedinPages;

        LOGGER.info("Update permission for page number : {} and size : {}",page,size);
        businessLinkedinPages = linkedInRepo.
                findByLastScannedOnLessThanAndIsValid(DateUtils.addDays(new Date(), -1),1, new org.springframework.data.domain.PageRequest(page, size));
        if(CollectionUtils.isEmpty(businessLinkedinPages.getContent())){
            LOGGER.info("No pages left to check validations");
            return;
        }
        List<Integer> ids = businessLinkedinPages.getContent().stream().map(BusinessLinkedinPage::getId).collect(Collectors.toList());
        linkedInRepo.updateLastScannedOnByIdIn(new Date(), ids);
        businessLinkedinPages.getContent().forEach(linkedinPage -> {
            SocialTokenValidationDTO socialTokenValidationDTO = new SocialTokenValidationDTO();
            socialTokenValidationDTO.setChannel(SocialChannel.LINKEDIN.getName());
            socialTokenValidationDTO.setId(linkedinPage.getId());
            kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_MARK_PAGE_INVALID.getName(),socialTokenValidationDTO);
        });
    }

    @Async
    @Override
    public void updatePermissionForValidPage(SocialTokenValidationDTO socialTokenValidationDTO) {
        LOGGER.info("Update Permission of linkedin for request : {}",socialTokenValidationDTO);
        BusinessLinkedinPage businessLinkedinPage = linkedInRepo.findById(socialTokenValidationDTO.getId());
        if(Objects.isNull(businessLinkedinPage)){
            LOGGER.info("No page found for request : {}",socialTokenValidationDTO);
            return;
        }
        boolean isProfile = Objects.equals(businessLinkedinPage.getPageType(),"profile");
        LinkedInOrganizationResponse response = isProfile ? null
                : linkedinConnectService.linkedInOrganizationResponse(businessLinkedinPage.getAccessToken());
//        if(Objects.isNull(response) || CollectionUtils.isEmpty(response.getElements())){
//            LOGGER.info("Unable to get data from Linkedin API for request : {}",socialTokenValidationDTO.getId());
//            return;
//        }
        List<LinkedInOrganizationElement> elements = new ArrayList<>();
        Set<Role> roles = new HashSet<>();
        if(Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getElements())) {
            for (LinkedInOrganizationElement element : response.getElements()) {
                if (Objects.equals(element.getOrganization(), businessLinkedinPage.getUrn()) && Objects.equals(element.getState(), State.APPROVED)) {
                    elements.add(element);
                    Role role = Role.getRole(element.getRole());
                    if(Objects.nonNull(role)) {
                        roles.add(role);
                    }
                }
            }
        }

        boolean getPosts = linkedinConnectService.checkPermissions(roles,businessLinkedinPage.getScope(),EnabledTasks.POSTS,isProfile);
        boolean getPostInsights = linkedinConnectService.checkPermissions(roles,businessLinkedinPage.getScope(),EnabledTasks.INSIGHTS,isProfile);
        String permissions = null;
        if(!getPosts){
            permissions = EnabledTasks.POSTS.name();
        }
        if(!getPostInsights){
            permissions = StringUtils.isEmpty(permissions) ? EnabledTasks.INSIGHTS.name()
                    : permissions.concat(","+EnabledTasks.INSIGHTS.name());
        }
        if(roles.contains(Role.ADMINISTRATOR)) {
            businessLinkedinPage.setRole(Role.ADMINISTRATOR.name());
        } else if(roles.contains(Role.CONTENT_ADMINISTRATOR)) {
            businessLinkedinPage.setRole(Role.CONTENT_ADMINISTRATOR.name());
        } else if(CollectionUtils.isNotEmpty(elements)) {
            if(Objects.nonNull(elements.get(0).getRole())){
                businessLinkedinPage.setRole(elements.get(0).getRole());
            }
        }
        pushToKafkaForValidity(LINKEDIN, Collections.singletonList(businessLinkedinPage.getProfileId()));
        businessLinkedinPage.setModuleImpacted(permissions);
        linkedInRepo.save(businessLinkedinPage);
    }

    private void subscribeSocial(BusinessLinkedinPage businessLinkedinPage) throws Exception {

        String developerId=   CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getLinkedinAppDeveloperId();
        Map<String,Object> response = linkedinConnectService.linkedinEventUnSubscriptionInfo(businessLinkedinPage.getAccessToken(),
                businessLinkedinPage.getUrn(), businessLinkedinPage.getPersonUrn(), developerId,false);
        if (Objects.nonNull(response)) {
            LOGGER.info("[Linkedin]:  subscription already present for businessId: {} and accountId: {}", businessLinkedinPage.getBusinessId(),
                    businessLinkedinPage.getProfileId());
        } else {
            String webhookUrl=   CacheManager.getInstance()
                    .getCache(SystemPropertiesCache.class)
                    .getLinkedinWebhookUrl();

            boolean result = linkedinConnectService.linkedinEventSubscriptionInfo(businessLinkedinPage.getAccessToken(), businessLinkedinPage.getUrn(), businessLinkedinPage.getPersonUrn(),developerId,webhookUrl);

            if (result) {
                Map<String,Object> data = linkedinConnectService.linkedinEventUnSubscriptionInfo(businessLinkedinPage.getAccessToken(),
                        businessLinkedinPage.getUrn(), businessLinkedinPage.getPersonUrn(), developerId,false);
                new Timestamp((Long) data.get("expiresAt")).toLocalDateTime();
                Date expiresAt = new Date((Long) data.get("expiresAt"));
                linkedInRepo.updateEventRecord(expiresAt, businessLinkedinPage.getId());
                LOGGER.info("[Linkedin]: subscribed for businessId: {} and accountId: {}", businessLinkedinPage.getBusinessId(),
                        businessLinkedinPage.getProfileId());
            } else {
                LOGGER.info("[Linkedin]: No event response received for subscription for businessId: {} and accountId: {}", businessLinkedinPage.getBusinessId(),
                        businessLinkedinPage.getProfileId());

            }
        }
    }

    private void unsubscribeSocial(BusinessLinkedinPage businessLinkedinPage) throws Exception {
        String developerId=   CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getLinkedinAppDeveloperId();
        linkedinConnectService.linkedinEventUnSubscriptionInfo(businessLinkedinPage.getAccessToken(), businessLinkedinPage.getUrn(), businessLinkedinPage.getPersonUrn(),developerId,true);

            LOGGER.info("[Linkedin]:  response received for unsubscription for businessId: {} and accountId: {}", businessLinkedinPage.getBusinessId(),
                    businessLinkedinPage.getProfileId());
    }

    @Override
    public List<SocialPageListInfo> getUnmappedLinkedinPagesByEnterpriseId(Long enterpriseId) {
        List<BusinessLinkedinPage> connectedLinkedinPages = linkedInRepo.findByEnterpriseIdAndIsSelected(enterpriseId, 1);
        if (CollectionUtils.isEmpty(connectedLinkedinPages)) {
            return Collections.emptyList();
        }
        List<SocialPageListInfo> unmappedPagesFinal = connectedLinkedinPages.stream().map(businessLinkedinPage -> convertBusinessLinkedinPageToLocationPageListInfo(businessLinkedinPage))
                .collect(Collectors.toList());
        unmappedPagesFinal.sort(Comparator.comparing(SocialPageListInfo::getPageName, nullsFirst(Comparator.naturalOrder())));
        return unmappedPagesFinal;
    }

    private String fetchInvalidTypeForPage(BusinessLinkedinPage connectedLinkedinPages) {
        String invalidType = null;
        if(Objects.isNull(connectedLinkedinPages.getLinkedinErrorCode())){
            invalidType = "integration";
        }else if(connectedLinkedinPages.getLinkedinErrorCode() == 1){
            invalidType ="tokenExpired";
        }else if(connectedLinkedinPages.getLinkedinErrorCode() == 2){
            invalidType ="revokedPermission";
        }
        return invalidType;
    }

    private SocialPageListInfo convertBusinessLinkedinPageToLocationPageListInfo(BusinessLinkedinPage connectedLinkedinPages) {
        SocialPageListInfo locationPageListInfo = new SocialPageListInfo();
        locationPageListInfo.setId(connectedLinkedinPages.getProfileId());
        if ( connectedLinkedinPages.getPageType().equalsIgnoreCase("company") ) {
            locationPageListInfo.setPageName(connectedLinkedinPages.getCompanyName());
        } else if ( connectedLinkedinPages.getPageType().equalsIgnoreCase("profile") ) {
            String pageName = StringUtils.isNotEmpty(connectedLinkedinPages.getFirstName()) ? connectedLinkedinPages.getFirstName() : "";
            if ( StringUtils.isNotEmpty(connectedLinkedinPages.getLastName()) ) {
                pageName += (pageName.length() > 0 ? " " : "") + connectedLinkedinPages.getLastName();
            }
            locationPageListInfo.setPageName(pageName);
        }
        locationPageListInfo.setMapped(connectedLinkedinPages.getBusinessId() != null ? Boolean.TRUE : Boolean.FALSE);
        locationPageListInfo.setLink(connectedLinkedinPages.getPageUrl());

        Validity validity = fetchValidityAndErrorMessage(connectedLinkedinPages);
        locationPageListInfo.setValidType(validity.getValidType());
        locationPageListInfo.setErrorCode(validity.getErrorCode());
        locationPageListInfo.setErrorMessage(validity.getErrorMessage());
        locationPageListInfo.setType(connectedLinkedinPages.getPageType());
        locationPageListInfo.setConnectedInReseller(Objects.isNull(connectedLinkedinPages.getResellerId())?false:true);
        return locationPageListInfo;
    }

    @Override
    public void updateLinkedinAccountIsValidStatus(String profileId, Integer isValid) {
        LOGGER.info("Marking linkedin page invalid for profileId {}", profileId);
        BusinessLinkedinPage businessLinkedinPage = linkedInRepo.findByProfileId(profileId);
        if(Objects.nonNull(businessLinkedinPage)){
            businessLinkedinPage.setIsValid(isValid);
            linkedInRepo.saveAndFlush(businessLinkedinPage);
            LOGGER.info("Marked linkedin page invalid for profileId {}", profileId);
            commonService.sendLinkedinSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(businessLinkedinPage),
                    null, businessLinkedinPage.getBusinessId(), businessLinkedinPage.getEnterpriseId());
        }else{
            LOGGER.info("No linkedin page found for profileId {}", profileId);
        }
    }

    @Override
    public void removeInactiveIntegration(String channel, Long enterpriseId) {
        List<BusinessLinkedinPage> existingPages = linkedInRepo.findByEnterpriseId(enterpriseId);
        LOGGER.info("Count of existing pages found for channel and enterpriseId {} , {} : {}",channel, enterpriseId, existingPages.size());
        if(CollectionUtils.isEmpty(existingPages)){
            return;
        }
        List<SocialPagesAudit> socialPagesAudits = getLinkedinAuditSocialObject(existingPages);
        socialPagesAuditRepo.save(socialPagesAudits);
        socialPagesAuditRepo.flush();
        existingPages.forEach(page -> {
            LOGGER.info("Remove Page for id :{}",page.getProfileId());
            DeleteEventRequest request = DeleteEventRequest.builder()
                    .channel(SocialChannel.LINKEDIN.getName())
                    .pagesIds(Collections.singletonList(page.getProfileId()))
                    .build();
            kafkaProducer.sendObjectV1(KafkaTopicEnum.SOCIAL_DELETE_PAGE.getName(),request);
        });
    }

    private List<SocialPagesAudit> getLinkedinAuditSocialObject(List<BusinessLinkedinPage> existingPages) {
        List<SocialPagesAudit> socialPagesAudits = new ArrayList<>();
        existingPages.forEach(page -> {
            SocialPagesAudit auditPage = new SocialPagesAudit();
            auditPage.setChannel(SocialChannel.LINKEDIN.name());
            auditPage.setRemovedOnDate(new Date());
            auditPage.setEnterpriseId(page.getEnterpriseId());
            auditPage.setPageId(page.getProfileId());
            auditPage.setPageName(page.getCompanyName());
            auditPage.setPageUrl(page.getPageUrl());
            auditPage.setAccessToken(page.getAccessToken());
            auditPage.setIsSelected(page.getIsSelected());
            auditPage.setIsValid(page.getIsValid());
            auditPage.setRequestId(page.getBusinessGetPageId());
            auditPage.setCreatedBy(page.getCreatedBy());
            auditPage.setUpdatedBy(page.getUpdatedBy());
            auditPage.setBusinessId(page.getBusinessId());
            auditPage.setUrn(page.getUrn());
            auditPage.setExpiresOn(page.getExpiresOn());
            auditPage.setPageType(page.getPageType());
            auditPage.setFirstName(page.getFirstName());
            auditPage.setLastName(page.getLastName());
            socialPagesAudits.add(auditPage);
        });
        return socialPagesAudits;
    }



    @Override
    public ConnectedPages getPages(Integer userId,Long enterpriseId,String type) {
        if ( !PageConnectionStatus.contains(type) ) {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid value for type");
        }
        PageConnectionStatus pageConnectionStatus = PageConnectionStatus.valueOf(type.toUpperCase());
        LOGGER.info("Inside Linkedin get pages for enterprise and pageconnectionstatus : {} , {}",enterpriseId,pageConnectionStatus);
        ConnectedPages connectedPage = new ConnectedPages();
        Map<String, ChannelPageDetails> pageTypes = new HashMap<>();
        List<BusinessLinkedinPage> connectedPages = linkedInRepo.findByEnterpriseIdAndIsSelected(enterpriseId, 1);
        ChannelPageDetails accountInfo = getPageInfo(connectedPages, pageConnectionStatus);
        pageTypes.put(SocialChannel.LINKEDIN.getName(), accountInfo);
        connectedPage.setPageTypes(pageTypes);
        return connectedPage;
    }

    @Override
    public ConnectedPages getPagesForPostReconnect(Long enterpriseId,String type, SocialPostPageConnectRequest request) {
        if ( !PageConnectionStatus.contains(type) ) {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid value for type");
        }
        PageConnectionStatus pageConnectionStatus = PageConnectionStatus.valueOf(type.toUpperCase());
        LOGGER.info("Inside Linkedin get pages for enterprise and page connection status : {} , {}",enterpriseId,pageConnectionStatus);
        ConnectedPages connectedPage = new ConnectedPages();
        Map<String, ChannelPageDetails> pageTypes = new HashMap<>();
        List<BusinessLinkedinPage> connectedPages = linkedInRepo.findByProfileIdInAndIsSelected(request.getPageIds(), 1);
        ChannelPageDetails accountInfo = getPageInfo(connectedPages, pageConnectionStatus);
        pageTypes.put(SocialChannel.LINKEDIN.getName(), accountInfo);
        connectedPage.setPageTypes(pageTypes);
        return connectedPage;
    }

    private ChannelPageDetails getPageInfo(List<BusinessLinkedinPage> linkedinPages, PageConnectionStatus pageConnectionStatus){
        List<ChannelPages> pageInfo = new ArrayList<>();
        linkedinPages.forEach(businessLinkedinPage -> {
            ChannelPages completePageInfo = getPageInfo(businessLinkedinPage);
            if ( pageConnectionStatus == PageConnectionStatus.CONNECTED &&
                    completePageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.VALID.getName()) ) {
                pageInfo.add(completePageInfo);
            } else if ( pageConnectionStatus == PageConnectionStatus.DISCONNECTED &&
                    !completePageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.VALID.getName())) {
                pageInfo.add(completePageInfo);
            } else if ( pageConnectionStatus == PageConnectionStatus.ALL ) {
                pageInfo.add(completePageInfo);
            }
        });

        return sortInvalidAndValidPage(pageInfo);
    }

    private ChannelPageDetails sortInvalidAndValidPage(List<ChannelPages> pageInfo) {
        ChannelPageDetails channelPageDetails = new ChannelPageDetails();
        List<ChannelPages> invalidPages = new ArrayList<>();
        List<ChannelPages> validPages = new ArrayList<>();
        for(ChannelPages page: pageInfo) {
            if(page.getValidType().equalsIgnoreCase(ValidTypeEnum.VALID.getName())) {
                validPages.add(page);
            } else {
                invalidPages.add(page);
            }
        }
        channelPageDetails.setDisconnected(invalidPages.size());
        invalidPages.sort(Comparator.comparing(ChannelPages::getPageName, nullsFirst(Comparator.naturalOrder())));
        validPages.sort(Comparator.comparing(ChannelPages::getPageName, nullsFirst(Comparator.naturalOrder())));
        invalidPages.addAll(validPages);
        channelPageDetails.setPages(invalidPages);
        return channelPageDetails;
    }

    @Override
    public Validity fetchValidityAndErrorMessage(BusinessLinkedinPage page) {
        Validity validity = new Validity();
        if(Objects.isNull(page)){
            return validity;
        }
        if(page.getIsValid() == 1) {
            String errorMessage = DEFAULT_ERROR_INSTAGRAM_MESSAGE_START;
            int numberOfErrors = 0;
            List<PermissionMapping> permissionMappingList =
                    permissionMappingService.getDataByChannelAndModuleAndPermissionNameNotNull(SocialChannel.LINKEDIN.getName(),SocialMessageModule.INTEGRATION.name());

//            List<PermissionMapping> permissionMappings = permissionMappingService.
//                    getDataByChannelAndPermissionNameNotNull(SocialChannel.LINKEDIN.getName()).
//                    stream().filter(permissionMapping -> Objects.nonNull(permissionMapping.getPermissionName())).collect(Collectors.toList());
//
//            String errorMessage = DEFAULT_ERROR_INSTAGRAM_MESSAGE_START;
//            int numberOfErrors = 0;
//            for(PermissionMapping permission : permissionMappings){
//                if(Objects.nonNull(page.getScope()) && !page.getScope().contains(permission.getPermissionName())){
//                    if(numberOfErrors == 0) {
//                        errorMessage = errorMessage.concat(permission.getErrorMessage());
//                    } else if(numberOfErrors > 0) {
//                        errorMessage = errorMessage.concat(", ").concat(permission.getErrorMessage());
//                    }
//                    numberOfErrors++;
//                }
//            }

            LOGGER.info("Page contain all the required scopes. Checking for administrator permissions");
            if(CollectionUtils.isNotEmpty(permissionMappingList) && StringUtils.isNotEmpty(page.getModuleImpacted())) {
                for(PermissionMapping permissionMapping : permissionMappingList) {
                    if(page.getModuleImpacted().contains(permissionMapping.getPermissionName())) {
                        if (numberOfErrors == 0) {
                            errorMessage = errorMessage.concat(permissionMapping.getErrorMessage());
                        } else if (numberOfErrors > 0) {
                            errorMessage = errorMessage.concat(", ").concat(permissionMapping.getErrorMessage());
                        }
                        numberOfErrors++;
                    }
                }
            }
            if(numberOfErrors > 0) {
                validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
                validity.setErrorCode(PERMISSION_MISSING);
                validity.setErrorMessage(errorMessage);
            }
//            validity.setValidType(ValidTypeEnum.VALID.getName());
        } else {
            validity.setValidType(ValidTypeEnum.INVALID.getName());
            PermissionMapping permissionMapping = new PermissionMapping();
            if(Objects.nonNull(page.getLinkedinErrorCode())){
                permissionMapping = permissionMappingService.getDataByChannelAndPermissionCode(
                        SocialChannel.LINKEDIN.getName(),page.getLinkedinErrorCode());
            } else {
                permissionMapping = permissionMappingService.getDataByChannelAndErrorCode(
                        SocialChannel.LINKEDIN.getName(),Constants.INTEGRATION);
            }
            if("RESTRICTED_MEMBER".equalsIgnoreCase(page.getInvalidType())) {
                permissionMapping = permissionMappingService.getDataByChannelAndErrorCode(
                        SocialChannel.LINKEDIN.getName(),Constants.MEMBER_RESTRICTED);
            }
            // @ashu - added check to skip NPE
            if (Objects.nonNull(permissionMapping)) {
                validity.setErrorCode(permissionMapping.getErrorCode());
                validity.setErrorMessage(permissionMapping.getErrorMessage());
            }
        }
        if(validity.getValidType() == null ) {
            if (page.getIsValid() == 1) {
                validity.setValidType(ValidTypeEnum.VALID.getName());
            }
        }
        return validity;
    }


    private Validity createValidityOnValidType(BusinessLinkedinPage page) {
        Validity validity = new Validity();
        if(ValidTypeEnum.VALID.getId().equals(page.getValidType())) {
            validity.setValidType(ValidTypeEnum.VALID.getName());
        } else if(ValidTypeEnum.INVALID.getId().equals(page.getValidType())) {
            validity.setValidType(ValidTypeEnum.INVALID.getName());
            PermissionMapping permissionMapping = new PermissionMapping();
            if(Objects.nonNull(page.getLinkedinErrorCode())){
                permissionMapping = permissionMappingService.getDataByChannelAndPermissionCode(
                        SocialChannel.LINKEDIN.getName(),page.getLinkedinErrorCode());
            } else {
                permissionMapping = permissionMappingService.getDataByChannelAndErrorCode(
                        SocialChannel.LINKEDIN.getName(),Constants.INTEGRATION);
            }
            if("RESTRICTED_MEMBER".equalsIgnoreCase(page.getInvalidType())) {
                permissionMapping = permissionMappingService.getDataByChannelAndErrorCode(
                        SocialChannel.LINKEDIN.getName(),Constants.MEMBER_RESTRICTED);
            }
            // @ashu - added check to skip NPE
            if (Objects.nonNull(permissionMapping)) {
                validity.setErrorCode(permissionMapping.getErrorCode());
                validity.setErrorMessage(permissionMapping.getErrorMessage());
            }
        } else {
            validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
            String errorMessage = DEFAULT_ERROR_INSTAGRAM_MESSAGE_START;
            int numberOfErrors = 0;
            List<PermissionMapping> permissionMappingList =
                    permissionMappingService.getDataByChannelAndModuleAndPermissionNameNotNull(SocialChannel.LINKEDIN.getName(),SocialMessageModule.INTEGRATION.name());

            LOGGER.info("Page contain all the required scopes. Checking for administrator permissions");
            if(CollectionUtils.isNotEmpty(permissionMappingList) && StringUtils.isNotEmpty(page.getModuleImpacted())) {
                for(PermissionMapping permissionMapping : permissionMappingList) {
                    if(page.getModuleImpacted().contains(permissionMapping.getPermissionName())) {
                        if (numberOfErrors == 0) {
                            errorMessage = errorMessage.concat(permissionMapping.getErrorMessage());
                        } else if (numberOfErrors > 0) {
                            errorMessage = errorMessage.concat(", ").concat(permissionMapping.getErrorMessage());
                        }
                        numberOfErrors++;
                    }
                }
            }
            if(numberOfErrors == 0) {
                errorMessage = GENERIC_PERMISSION_MISSING_MESSAGE;
            }
            validity.setErrorCode(PERMISSION_MISSING);
            validity.setErrorMessage(errorMessage);
        }
        return validity;
    }

    private ChannelPages getPageInfo(BusinessLinkedinPage linkedinPages){
        ChannelPages pageInfo = new ChannelPages();
        pageInfo.setId(linkedinPages.getProfileId());
        if(linkedinPages.getPageType().equalsIgnoreCase("company")){
            pageInfo.setPageName(linkedinPages.getCompanyName());
        }else{
            String fullName = null;
            if(StringUtils.isNotBlank(linkedinPages.getFirstName()) && StringUtils.isNotBlank(linkedinPages.getLastName())){
                fullName = linkedinPages.getFirstName()+ " "+linkedinPages.getLastName();
            }else{
                fullName = linkedinPages.getFirstName();
            }
            pageInfo.setPageName(fullName);
        }
        pageInfo.setLink(linkedinPages.getPageUrl());
        Validity validity = fetchValidityAndErrorMessage(linkedinPages);
        pageInfo.setValidType(validity.getValidType());
        pageInfo.setErrorCode(validity.getErrorCode());
        pageInfo.setErrorMessage(validity.getErrorMessage());

        pageInfo.setType(linkedinPages.getPageType());
        return pageInfo;
    }

    @Override
    public boolean checkIfAccountExistsByAccountId(Long accountId) {
        return linkedInRepo.existsByEnterpriseIdAndIsSelected(accountId,1);
    }

    @Override
    public ChannelSetupStatus.PageSetupStatus getPageSetupStatus(Long enterpriseId) {
        List<BusinessLinkedinPage> linkedinPages = linkedInRepo.findByEnterpriseId(enterpriseId);
        if ( linkedinPages.isEmpty() ) {
            return ChannelSetupStatus.PageSetupStatus.NO_PAGES_FOUND;
        } else {
            // We only need to check if there are pages which have broken mapping
            // We don't need to check for unmapped pages here
            for ( BusinessLinkedinPage linkedinPage : linkedinPages ) {
                if ( linkedinPage.getBusinessId() != null && linkedinPage.getIsValid() != 1 ) {
                    return ChannelSetupStatus.PageSetupStatus.DISCONNECTED_PAGES_FOUND;
                }
            }
        }
        return ChannelSetupStatus.PageSetupStatus.OK;
    }

    @Override
    public ChannelSetupStatus.PageSetupStatus getPageConnectionSetupStatus(Long enterpriseId) {
        List<BusinessLinkedinPage> linkedinPages = linkedInRepo.findByEnterpriseId(enterpriseId);
        if ( linkedinPages.isEmpty() ) {
            return ChannelSetupStatus.PageSetupStatus.INVALID_INTEGRATION;
        } else {
            // We only need to check if there are pages which have broken mapping
            // We don't need to check for unmapped pages here
            for ( BusinessLinkedinPage linkedinPage : linkedinPages ) {
                if (  Objects.nonNull(linkedinPage.getBusinessId()) && linkedinPage.getIsValid() == 1 ) {
                    return ChannelSetupStatus.PageSetupStatus.VALID_INTEGRATION;
                }
            }
            return ChannelSetupStatus.PageSetupStatus.INVALID_INTEGRATION;
        }
    }

    @Override
    public LinkedinContactInfo linkedinContactDetails(String token) {
        return linkedinConnectService.linkedinContactDetails(token);
    }

    @Override
    public void backupLinkedinPages(SocialPagesAudit socialPagesAudit) {
        LOGGER.info("Start restoring Linkedin PageId {} : for enterpriseId {} : ", socialPagesAudit.getPageId(),socialPagesAudit.getEnterpriseId());
        BusinessLinkedinPage existingPage = linkedInRepo.findByProfileIdAndPageType(socialPagesAudit.getPageId(),socialPagesAudit.getPageType());
        if(Objects.isNull(existingPage)){
            BusinessLinkedinPage businessLinkedinPage = new BusinessLinkedinPage();
            businessLinkedinPage.setAccessToken(socialPagesAudit.getAccessToken());
            businessLinkedinPage.setUrn(socialPagesAudit.getUrn());
            businessLinkedinPage.setBusinessGetPageId(socialPagesAudit.getRequestId());
            businessLinkedinPage.setCompanyName(socialPagesAudit.getPageName());
            businessLinkedinPage.setProfileId(socialPagesAudit.getPageId());
            businessLinkedinPage.setPageUrl(socialPagesAudit.getPageUrl());
            businessLinkedinPage.setScope(socialPagesAudit.getScope());
            businessLinkedinPage.setIsValid(socialPagesAudit.getIsValid());
            businessLinkedinPage.setIsSelected(socialPagesAudit.getIsSelected());
            businessLinkedinPage.setBusinessId(socialPagesAudit.getBusinessId());
            businessLinkedinPage.setExpiresOn(socialPagesAudit.getExpiresOn());
            businessLinkedinPage.setCreatedBy(socialPagesAudit.getCreatedBy());
            businessLinkedinPage.setUpdatedBy(socialPagesAudit.getUpdatedBy());
            businessLinkedinPage.setPageType(socialPagesAudit.getPageType());
            businessLinkedinPage.setFirstName(socialPagesAudit.getFirstName());
            businessLinkedinPage.setLastName(socialPagesAudit.getLastName());
            linkedInRepo.saveAndFlush(businessLinkedinPage);
            if(businessLinkedinPage.getIsValid().equals(0)) {
                commonService.sendLinkedinSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(businessLinkedinPage),
                        null, businessLinkedinPage.getBusinessId(), businessLinkedinPage.getEnterpriseId());
            }

            socialPagesAudit.setSocialEnterpriseRestorePageId(businessLinkedinPage.getId().toString());
            LOGGER.info("Restoring Linkedin PageId {} : in BusinessLinkedinPage for enterpriseId {} : is completed ", socialPagesAudit.getPageId(),socialPagesAudit.getEnterpriseId());
        }else{
            socialPagesAudit.setSocialEnterpriseRestorePageId("Exists :: "+existingPage.getId());
            LOGGER.info("Already exists Linkedin pageId {} : in BusinessLinkedinPage for enterpriseId {} : ", socialPagesAudit.getPageId(),socialPagesAudit.getEnterpriseId());
        }
    }

    @Override
    public List<? extends Object> fetchRawPages(List<String> integrationIds,String type) {
        return linkedInRepo.findByProfileIdInAndPageType(integrationIds,type);
    }

    @Override
    public List<BusinessLinkedinPage> findByProfileIdInAndPageType(List<String> pageIds, String pageType) {
        return linkedInRepo.findByProfileIdInAndPageType(pageIds,pageType);
    }

    @Override
    public List<Number> fetchRawPagesId(List<String> pages, String type) {
        return linkedInRepo.findIdByProfileIdInAndPageType(pages,type);
    }

    @Override
    public List<SocialElasticDto> fetchRawPagesSocialEsDto(List<LinkedinConnectRequest> linkedinConnectRequests) {
        List<BusinessLinkedinPage> rawPages = new ArrayList<>();
        // Filter out LinkedinConnectRequest objects with non-null type and group valid request by type.
        Map<String, List<LinkedinConnectRequest>> pageRequestMapByType = linkedinConnectRequests.stream()
                .filter(req -> Objects.nonNull(req.getType()))
                .collect(Collectors.groupingBy(LinkedinConnectRequest::getType));
        for(Map.Entry<String,List<LinkedinConnectRequest>> entry : pageRequestMapByType.entrySet()){
            String pageType = entry.getKey();
            List<LinkedinConnectRequest> pageRequests = entry.getValue();
            List<String> pageIds = pageRequests.stream().map(LinkedinConnectRequest::getId).collect(Collectors.toList());
            List<BusinessLinkedinPage> businessLinkedinPages = findByProfileIdInAndPageType(pageIds,pageType);
            if(CollectionUtils.isNotEmpty(businessLinkedinPages)){
                businessLinkedinPages.forEach(businessLinkedinPage -> rawPages.add(businessLinkedinPage));
            }
        }
        return SocialElasticUtil.getSocialEsDtoFromObjects(rawPages,SocialChannel.LINKEDIN.getName());
    }

    @Override
    public List<SocialElasticDto> fetchPagesEsDto() {
        List<BusinessLinkedinPage> businessLinkedinPages = linkedInRepo.findByIsSelected(1);
        return SocialElasticUtil.getSocialEsDtoFromObjects(businessLinkedinPages,SocialChannel.LINKEDIN.getName());
    }

    @Override
    public List<SocialElasticDto> fetchPagesEsDto(Integer id) {
        BusinessLinkedinPage businessLinkedinPages = linkedInRepo.findById(id);
        if(Objects.nonNull(businessLinkedinPages)) {
            return SocialElasticUtil.getSocialEsDtoFromObjects(Collections.singletonList(businessLinkedinPages), SocialChannel.LINKEDIN.getName());
        }
        return Collections.emptyList();
    }

    @Override
    public boolean getLinkedinPermission(Integer accountId, List<String> modules) {
        LOGGER.info("Check LinkedIn post permissions for account id : {}",accountId);
        List<BusinessLinkedinPage> businessLinkedinPages = linkedInRepo.findByAccountIdAndBusinessIdNotNull(accountId);
        return checkPermission(businessLinkedinPages,modules);
    }

    @Override
    public boolean getLinkedinPostPermission(List<BusinessLinkedinPage> linkedinPages, List<String> modules) {
        return checkPermission(linkedinPages,modules);
    }

    @Override
    public void markLinkedinPageAsInvalid(SocialTokenValidationDTO request) {
        LOGGER.info("Request to check token validity : {}",request);
        try {
            BusinessLinkedinPage businessLinkedinPage= linkedInRepo.findById(request.getId());
            if(Objects.isNull(businessLinkedinPage) || businessLinkedinPage.getIsValid() == 0) {
                LOGGER.info("No linkedin page found or is already marked as invalid for request : {}",request);
                return;
            }
            Boolean isTokenValid = true;
            Map<String, Object> tokenDetails = new HashMap<>();
            try {
                tokenDetails = linkedinConnectService.getLinkedinTokenDetails(businessLinkedinPage.getAccessToken());
                if(MapUtils.isNotEmpty(tokenDetails) && Objects.nonNull(tokenDetails.get("active"))) {
                    isTokenValid = (Boolean)tokenDetails.get("active");
                }
                LOGGER.info("token details :{}",tokenDetails);
            } catch (BirdeyeSocialException ex) {
                if( ex.getCode() == ErrorCodes.UNABLE_TO_RUN_TOKEN_INTROSPECTION.value() && Objects.nonNull(ex.getData()) && Objects.nonNull(ex.getData().get("error"))) {
                    LOGGER.info("Token validation failed with error {}", ex.getData());
                    String errorMessage = (String) ex.getData().get("error");
                    if( errorMessage.equalsIgnoreCase("restricted_token")) {
                        // user is restricted to perform action.
                        isTokenValid = false;
                        tokenDetails.put("invalidType", "RESTRICTED_MEMBER");
                    }
                }
            } catch (Exception e) {
                LOGGER.info("Error while fetching token details for linkedin page with id: {} as invalid", request.getId());
            }
            if(isTokenValid) {
                LOGGER.info("Token for linkedin  page : {} is valid from api", request.getId());
                if(StringUtils.isEmpty(businessLinkedinPage.getScope()) && StringUtils.isNotEmpty(String.valueOf(tokenDetails.get("scope")))){
                    businessLinkedinPage.setScope(String.valueOf(tokenDetails.get("scope")));
                    linkedInRepo.saveAndFlush(businessLinkedinPage);
                }
                kafkaProducerService.sendObjectV1(KafkaTopicEnum.LINKEDIN_PERMISSION_CHECK.getName(),request);
            } else {
                LOGGER.info("Marking linkedin  page with id: {} as invalid", request.getId());
                businessLinkedinPage.setIsValid(0);
                businessLinkedinPage.setValidType(ValidTypeEnum.INVALID.getId());
                if(StringUtils.isNotEmpty(request.getInvalidMessage())) {
                    businessLinkedinPage.setErrorLog(request.getInvalidMessage());
                }
                if(Objects.nonNull(tokenDetails.get("invalidType"))) {
                    businessLinkedinPage.setInvalidType((String) tokenDetails.get("invalidType"));
                }
                linkedInRepo.saveAndFlush(businessLinkedinPage);
                commonService.sendLinkedinSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Collections.singletonList(businessLinkedinPage), null, businessLinkedinPage.getBusinessId(), businessLinkedinPage.getEnterpriseId());
            }
        } catch (Exception e) {
            LOGGER.error("got exception while marking linkedin page with id: {} as invalid, the error: {}", request.getId(), e.getMessage());
        }
    }

    private boolean checkPermission(List<BusinessLinkedinPage> businessLinkedinPages, List<String> modules) {
        if(CollectionUtils.isEmpty(businessLinkedinPages)){
            LOGGER.info("businessLinkedinPages list is empty");
            return true;
        }
        if (CollectionUtils.isNotEmpty(modules)) {
            LOGGER.info("Modules impacted {}",modules);
            for(BusinessLinkedinPage linkedinPage : businessLinkedinPages) {
                if (linkedinPage.getIsValid() == 0){
                    return false;
                }
                if(StringUtils.isNotEmpty(linkedinPage.getModuleImpacted())){
                    if(modules.contains("PUBLISH") && linkedinPage.getModuleImpacted().contains(EnabledTasks.POSTS.name())){
                        return false;
                    }
                }
            }
        }
        return true;
    }

    @Override
    public Map<String, Boolean> getLinkedinPostPermissionPageWise(List<BusinessLinkedinPage> businessLinkedinPages, List<String> modules) {
        Map<String, Boolean> permissionMap = new HashMap<>();
        List<String> linkedinPermissions = new ArrayList<>();
        if(CollectionUtils.isEmpty(businessLinkedinPages)){
            LOGGER.info("businessLinkedinPages list is empty");
            return permissionMap;
        }
        if (CollectionUtils.isNotEmpty(modules)) {
            LOGGER.info("Modules impacted {}",modules);
            for(BusinessLinkedinPage linkedinPage : businessLinkedinPages) {
                boolean flag = true;
                if (linkedinPage.getIsValid() == 0){
                    flag = false;
                }
                if(flag && StringUtils.isNotEmpty(linkedinPage.getModuleImpacted())){
                    if(modules.contains("PUBLISH") && linkedinPage.getModuleImpacted().contains(EnabledTasks.POSTS.name())){
                        flag = false;
                    }
                }
                permissionMap.put(linkedinPage.getProfileId(), flag);
            }
        }
        return permissionMap;
    }

    @Override
    public void removeBusinessInactiveIntegration(String name, Integer businessId) {
        List<BusinessLinkedinPage>  existingPage = linkedInRepo.findByBusinessId(businessId);
        if(CollectionUtils.isNotEmpty(existingPage)) {
            //save into social audit/backup table
            List<SocialPagesAudit> socialPagesAudits = getLinkedinAuditSocialObject(existingPage);
            socialPagesAuditRepo.save(socialPagesAudits);
            socialPagesAuditRepo.flush();
            existingPage.forEach(page ->{
                LOGGER.info("Remove Page for id :{}",page.getProfileId());
                DeleteEventRequest request = DeleteEventRequest.builder()
                        .channel(SocialChannel.LINKEDIN.getName())
                        .pagesIds(Collections.singletonList(page.getProfileId()))
                        .build();
                kafkaProducer.sendObjectV1(KafkaTopicEnum.SOCIAL_DELETE_PAGE.getName(),request);
            });
        }
    }

    @Async
    private void unsubscribeWebhookForLinkedin(List<BusinessLinkedinPage> existingPages) {
        existingPages.forEach(linkedinPage -> {
        if("company".equalsIgnoreCase(linkedinPage.getPageType())) {
            LOGGER.info("Request received for linkedin engage unsubscription for pageId {}", linkedinPage.getProfileId());

            EngageWebhookSubscriptionRequest payload = new EngageWebhookSubscriptionRequest();
            payload.setSubscription(true);
            payload.setChannel(SocialChannel.LINKEDIN.getName());
            payload.setPageId(linkedinPage.getProfileId());
            // subscribe to engage webhook
            kafkaProducerService.sendObject(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_WEBHOOK_SUBSCRIPTION_TOPIC.getName(), payload);
        }
        else{
            LOGGER.info("No company page found for linkedin subscription event : {}",linkedinPage.getProfileId());
        }
        });
       /* String developerId=   CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getLinkedinAppDeveloperId();
        existingPages.forEach(linkedinPage -> {
                         try {

                             linkedinConnectService.linkedinEventUnSubscriptionInfo(linkedinPage.getAccessToken(), linkedinPage.getUrn(), linkedinPage.getPersonUrn(),developerId
                             ,true);
                }catch (Exception e){
                    LOGGER.info("Exception occured while getting subscribeApps for page id : {}",linkedinPage.getProfileId());
                }
        });*/

    }

   @Async
    private void subscribeWebhookForLinkedin(BusinessLinkedinPage businessLinkedinPage) {
     /*  try {
           if("company".equalsIgnoreCase(businessLinkedinPage.getPageType()) && socialBusinessService.isMentionEnabled(businessLinkedinPage.getEnterpriseId())) {

               subscribeSocial(businessLinkedinPage);
           }
           else{
               LOGGER.info("No company page found for linkedin subscription event : {}",businessLinkedinPage.getProfileId());
           }
       }catch (Exception e){
           LOGGER.info("Exception occured while getting subscription for linkedin  page id : {}",businessLinkedinPage.getProfileId());

       }*/
       if("company".equalsIgnoreCase(businessLinkedinPage.getPageType())) {
           LOGGER.info("Request received for linkedin engage subcrioption for pageId {}", businessLinkedinPage.getProfileId());

           EngageWebhookSubscriptionRequest payload = new EngageWebhookSubscriptionRequest();
           payload.setSubscription(true);
           payload.setChannel(SocialChannel.LINKEDIN.getName());
           payload.setPageId(businessLinkedinPage.getProfileId());
           // subscribe to engage webhook
           kafkaProducerService.sendObject(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_WEBHOOK_SUBSCRIPTION_TOPIC.getName(), payload);
       }
       else{
           LOGGER.info("No company page found for linkedin subscription event : {}",businessLinkedinPage.getProfileId());
       }

    }

    @Override
    public void initiateDPSync() {
        LOGGER.info("Request received to initiate profile pic sync for linkedin accounts");
        try {
            List<BusinessLinkedinPage> linkedinPages = linkedInRepo.findByIsValidAndPageTypeAndEnterpriseIdNotNull(1, "company");
            linkedinPages.forEach(linkedinPage -> {
                DpSyncRequest dpSyncRequest = new DpSyncRequest();
                dpSyncRequest.setId(linkedinPage.getId());
                kafkaProducer.sendObjectV1(LINKEDIN_DP_SYNC_TOPIC,dpSyncRequest);
            });

        } catch (Exception e) {
            LOGGER.info("Exception while initiating linkedin Dp sync request: ",e);
        }
    }

    @Override
    public void syncLinkedinDP(DpSyncRequest linkedinDpSyncRequest) {
        LOGGER.info("Request received to sync dp for linkedin account with id: {}",linkedinDpSyncRequest.getId());
        try {
            BusinessLinkedinPage linkedinPage = linkedInRepo.findById(linkedinDpSyncRequest.getId());


            if(Objects.isNull(linkedinPage)) {
                LOGGER.info("no page found");
                return ;
            }
            LinkedinOrganizationPagesInfo pagesInfo = linkedinOrganizationInfo(linkedinPage.getAccessToken());
            if(Objects.isNull(pagesInfo) || CollectionUtils.isEmpty(pagesInfo.getElements()))  {
                LOGGER.info("image info not found");
                return ;
            }
            if (Objects.nonNull(pagesInfo.getElements().get(0))) {
                linkedinPage.setLogoUrl(pagesInfo.getElements().get(0).getLogoUrl());
                linkedInRepo.save(linkedinPage);
                commonService.uploadPageImageToCDN(linkedinPage);
            }
        } catch (Exception e) {
            LOGGER.info("Exception while syncing dp for linkedin account with id: {}",linkedinDpSyncRequest.getId(),e);
        }
    }

    @Override
    public void removeUnmappedPages(Long enterpriseId, Integer businessId) {
        List<BusinessLinkedinPage> businessLinkedinPages = linkedInRepo.findByEnterpriseId(enterpriseId);
        if (CollectionUtils.isEmpty(businessLinkedinPages)) {
            LOGGER.info("No Linkedin page found for enterprise id:{}", businessId);
            return;
        }
        businessLinkedinPages.forEach(page -> {
            if (!Objects.equals(page.getBusinessId(), businessId)) {
                LOGGER.info("Delete page with profile id : {}", page.getProfileId());
                commonService.deletePage(SocialChannel.LINKEDIN.getName(), page.getProfileId());
            }
        });
    }

    @Override
    public LinkedInOrganizationResponse linkedInOrganizationResponse(String token) {
        return linkedinConnectService.linkedInOrganizationResponse(token);
    }

    @Override
    public void removePageMap(Integer businessId) {
        List<BusinessLinkedinPage> businessLinkedinPages = linkedInRepo.findByBusinessId(businessId);
        if(CollectionUtils.isEmpty(businessLinkedinPages)){
            LOGGER.info("No page found for business : {}",businessId);
            return;
        }
        businessLinkedinPages.forEach(page ->{
            LOGGER.info("Remove page mapping for page id : {}",page.getProfileId());
            commonService.removeMapping(page.getBusinessId(),page.getProfileId(),SocialChannel.LINKEDIN.getName(), page.getPageType());
        });
    }

    @Override
    public void moveLinkedinAccountLocation(Long sourceEnterpriseNumber, Long targetEnterpriseNumber, Integer businessId, boolean isMultiLocationSource,Integer accountId) {
        List<BusinessLinkedinPage> gmbData = isMultiLocationSource
                ? linkedInRepo.findByBusinessId(businessId)
                : linkedInRepo.findByEnterpriseId(sourceEnterpriseNumber);
        if(CollectionUtils.isEmpty(gmbData)){
            LOGGER.info("Unable to get page for business id :{} or enterprise id  :{}",businessId,sourceEnterpriseNumber);
            return;
        }
        updateMappingForGMBPages(sourceEnterpriseNumber,targetEnterpriseNumber,gmbData);
    }

    @Override
    public void removeLinkedinPagesByPagesIds(List<String> pagesIds) {
        List<BusinessLinkedinPage> businessLinkedinPages = linkedInRepo.findByProfileIdIn(pagesIds);
        if(CollectionUtils.isEmpty(businessLinkedinPages)){
            LOGGER.info("No pages found for page ids : {}",pagesIds);
            return;
        }
        removeLinkedinPagesForPages(businessLinkedinPages,ENTERPRISE);
    }

    @Override
    public void removeLinkedinPagesByPagesIdsWithLimit(List<String> pageIds, Integer limit) {
        List<BusinessLinkedinPage> businessLinkedinPages = linkedInRepo.findByProfileIdInWithLimit(pageIds,new org.springframework.data.domain.PageRequest(0,limit));
        if(CollectionUtils.isEmpty(businessLinkedinPages)){
            LOGGER.info("No pages found for page ids : {}",pageIds);
            return;
        }
        List<LocationPageMappingRequest> input= createLinkedinRemovePageRequest(businessLinkedinPages);
        removeLinkedinPages(input, RESELLER);
    }

    private List<LocationPageMappingRequest> createLinkedinRemovePageRequest(List<BusinessLinkedinPage> businessLinkedinPages) {
       return businessLinkedinPages.stream()
                .map(businessPage -> new LocationPageMappingRequest(businessPage.getProfileId(), businessPage.getPageType()))
                .collect(Collectors.toList());
    }

    public Page<BusinessLinkedinPage> search(String search, org.springframework.data.domain.PageRequest page, Long resellerId) {
        Specification<BusinessLinkedinPage> linkedinPage = Specifications.where((linkedinSpecification.hasPageName(search))).
                and(linkedinSpecification.isSelected(1)).
                and(linkedinSpecification.hasResellerId(resellerId));

        return linkedInRepo.findAll(linkedinPage,page);

    }

    public Page<BusinessLinkedinPage> searchSortAndPaginate(String search, Long resellerId, List<Integer> businessIds, PageConnectionStatus pageConnectionStatus,
                                                               List<Integer> createdByIds, Integer isSelected, MappingStatus mappingStatus, Integer page, Integer size,
                                                               PageSortDirection sortDirection, ResellerSortType sortType, Boolean locationFilterSelected) {
        Specification<BusinessLinkedinPage> spec = Specifications.where((linkedinSpecification.hasResellerId(resellerId)));
        if(Objects.nonNull(search)) {
            spec = Specifications.where(spec).and(linkedinSpecification.hasPageName(search));
        }
        if(CollectionUtils.isNotEmpty(businessIds)) {
            if(locationFilterSelected) {
                if(MappingStatus.UNMAPPED.equals(mappingStatus)) return new PageImpl<>(new ArrayList<>());
                else spec = Specifications.where(spec).and(linkedinSpecification.inBusinessIds(businessIds));
            } else {
                if(MappingStatus.MAPPED.equals(mappingStatus)) {
                    spec = Specifications.where(spec).and(linkedinSpecification.inBusinessIds(businessIds));
                } else if(MappingStatus.UNMAPPED.equals(mappingStatus)) {
                    spec = Specifications.where(spec).and(linkedinSpecification.hasBusinessIdNullOrNotNull(true));
                } else {
                    Specification<BusinessLinkedinPage> orSpec = Specifications.where(linkedinSpecification.inBusinessIds(businessIds));
                    orSpec = Specifications.where(orSpec).or(linkedinSpecification.hasBusinessIdNullOrNotNull(true));
                    spec = Specifications.where(spec).and(orSpec);
                }
            }
        } else {
            if(MappingStatus.MAPPED.equals(mappingStatus)) {
                return new PageImpl<>(new ArrayList<>());
            } else {
                spec = Specifications.where(spec).and(linkedinSpecification.hasBusinessIdNullOrNotNull(true));
            }
        }
        if(PageConnectionStatus.CONNECTED.equals(pageConnectionStatus)) {
            spec = Specifications.where(spec).and(linkedinSpecification.inValidityTypes(Arrays.asList(ValidTypeEnum.VALID.getId())));
            if(Objects.nonNull(isSelected)) {
                spec = Specifications.where(spec).and(linkedinSpecification.isSelected(isSelected));
            }
        } else if(PageConnectionStatus.DISCONNECTED.equals(pageConnectionStatus)) {
            spec = Specifications.where(spec).and(linkedinSpecification.inValidityTypes(Arrays.asList(ValidTypeEnum.INVALID.getId(),
                    ValidTypeEnum.PARTIAL_VALID.getId())));
        } else {
            spec = Specifications.where(spec).and(linkedinSpecification.isSelected(isSelected));
        }
        if(CollectionUtils.isNotEmpty(createdByIds)) {
            spec = Specifications.where(spec).and(linkedinSpecification.inCreatedByIds(createdByIds));
        }
        PageRequest pageRequest = null;
        if(ResellerSortType.PAGE_NAME.equals(sortType) && Objects.nonNull(sortDirection)) {
            spec = Specifications.where(spec).and(linkedinSpecification.sortOnPageName(sortDirection));
            pageRequest = new PageRequest(page, size);
        } else if(ResellerSortType.STATUS.equals(sortType) && Objects.nonNull(sortDirection)) {
            spec = Specifications.where(spec).and(linkedinSpecification.sortValidTypesInGroup(sortDirection));
            pageRequest = new PageRequest(page, size);
        } else {
            spec = Specifications.where(spec).and(linkedinSpecification.sortBusinessIdNullsFirst());
            pageRequest = new PageRequest(page, size);
        }

        return linkedInRepo.findAll(spec, pageRequest);
    }

    @Override
    public PaginatedConnectedPages getResellerPages(Long resellerId, PageConnectionStatus pageConnectionStatus, Integer page, Integer size, String search,
                                                    ResellerSearchType searchType, PageSortDirection sortDirection,
                                                    ResellerSortType sortParam, List<Integer> locationIds, MappingStatus mappingStatus, List<Integer> userIds,
                                                    Boolean locationFilterSelected) {
        PaginatedConnectedPages connectedPage = new PaginatedConnectedPages();
        Map<String, ChannelPageDetails> pageTypes = new HashMap<>();

        Long getInvalidPageIds = linkedInRepo.findCountByResellerIdAndValidityType(resellerId,
                Arrays.asList(ValidTypeEnum.PARTIAL_VALID.getId(), ValidTypeEnum.INVALID.getId()));
        Page<BusinessLinkedinPage> connectedPages  ;
        connectedPages = searchSortAndPaginate(search, resellerId, locationIds, pageConnectionStatus, userIds, 1, mappingStatus,
                page, size, sortDirection, sortParam, locationFilterSelected);
        LOGGER.info("getpages : Found {} pages", CollectionUtils.size(connectedPages));
        if (CollectionUtils.isNotEmpty(connectedPages.getContent())) {
            connectedPages.getContent().stream().forEach(fb -> {
                if (fb.getProfileId() == null) {
                    LOGGER.error("Linkedin page Id is null for resellerId: {}, Data cleanup is required.",
                            fb.getResellerId());
                }
            });
        }
        try {
            ChannelPageDetails accountInfo = getResellerPageInfo(connectedPages.getContent());
            accountInfo.setDisconnected(Math.toIntExact(getInvalidPageIds));
            pageTypes.put(SocialChannel.LINKEDIN.getName(), accountInfo);

            connectedPage.setPageTypes(pageTypes);
            connectedPage.setPageCount(connectedPages.getTotalPages());
            connectedPage.setTotalCount(connectedPages.getTotalElements());
            return connectedPage;
        } catch (Exception e) {
            LOGGER.info("exception occred while setting page details error: {}",e.getMessage());
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }
    }

    public ChannelPageDetails getResellerPageInfo( List<BusinessLinkedinPage> linkedinPages) throws Exception {
        List<ChannelPages> pageInfo = new ArrayList<>();
        // Process for non empty connected Instagram pages.
        if (!linkedinPages.isEmpty()) {
            List<Integer> businessIds = new ArrayList<>();
            List<Integer> userIds = new ArrayList<>();
            linkedinPages.forEach(x->{
                if(Objects.nonNull(x.getBusinessId())) businessIds.add(x.getBusinessId());
                if(Objects.nonNull(x.getCreatedBy())) userIds.add(x.getCreatedBy());
            });
            Map<String, Object> businessLocations = null;
            CompletableFuture<Map<String, Object>> businessLocationsFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return businessCoreService.getBusinessesInBulkByBusinessIds(businessIds,true);
                } catch (Exception e) {
                    LOGGER.info("exception while executing business location future, error: {}", e.getMessage());
                    return new HashMap<>();
                }
            });
            CompletableFuture<Map<Integer, BusinessCoreUser>> userDetailsFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return coreBusinessService.getBusinessUserForUserId(userIds);
                } catch (Exception e) {
                    LOGGER.info("exception while executing user details future, error: {}", e.getMessage());
                    return new HashMap<>();
                }
            });
            CompletableFuture<Void> allCompletableFuture = CompletableFuture.allOf(businessLocationsFuture, userDetailsFuture);
            allCompletableFuture.get(10, TimeUnit.SECONDS);
            businessLocations = businessLocationsFuture.get();
            Map<Integer, BusinessCoreUser> userIdVsInfoMap= userDetailsFuture.get();
            Map<String, Object> finalBusinessLocations = businessLocations;
            linkedinPages.stream().forEach(page -> {
                BusinessLocationLiteEntity locationLite = null;
                if(Objects.nonNull(finalBusinessLocations) && Objects.nonNull(page.getBusinessId())){
                    LOGGER.info("Prepare data for mapped page :{}",page);
                    Map<String ,Object> locationData = (Map<String, Object>) finalBusinessLocations.get(page.getBusinessId().toString());
                    locationLite = commonService.getMappedLocationInfo(locationData, page.getBusinessId(), page.getProfileId());
                }

                BusinessCoreUser userDetail = null;
                if(Objects.nonNull(page.getCreatedBy()) && MapUtils.isNotEmpty(userIdVsInfoMap) &&
                        userIdVsInfoMap.containsKey(page.getCreatedBy())) {
                    userDetail = userIdVsInfoMap.get(page.getCreatedBy());
                }

                ChannelPages completePageInfo = getResellerPageInfo(page, locationLite, userDetail);
                pageInfo.add(completePageInfo);
            });
        }
        return commonService.sortInvalidAndValidPage(pageInfo);
    }




    private ChannelPages getResellerPageInfo(BusinessLinkedinPage linkedinPages,BusinessLocationLiteEntity locationDetails, BusinessCoreUser userDetail) {
        ChannelPages pageInfo = new ChannelPages();

        pageInfo.setId(linkedinPages.getProfileId());
        if(linkedinPages.getPageType().equalsIgnoreCase("company")){
            pageInfo.setPageName(linkedinPages.getCompanyName());
        }else{
            String fullName = null;
            if(StringUtils.isNotBlank(linkedinPages.getFirstName()) && StringUtils.isNotBlank(linkedinPages.getLastName())){
                fullName = linkedinPages.getFirstName()+ " "+linkedinPages.getLastName();
            }else{
                fullName = linkedinPages.getFirstName();
            }
            pageInfo.setPageName(fullName);
        }
        pageInfo.setLink(linkedinPages.getPageUrl());
        Validity validity = createValidityOnValidType(linkedinPages);
        pageInfo.setValidType(validity.getValidType());
        pageInfo.setErrorCode(validity.getErrorCode());
        pageInfo.setErrorMessage(validity.getErrorMessage());
        pageInfo.setType(linkedinPages.getPageType());
        pageInfo.setUserId(linkedinPages.getUserEmailId());
        pageInfo.setAddedBy(Objects.nonNull(userDetail)?businessCoreService.getFullUsername(userDetail):null);
        if( Objects.nonNull(locationDetails)){
            pageInfo.setLocationId(locationDetails.getId());
            pageInfo.setLocationName(locationDetails.getAlias1() != null ? locationDetails.getAlias1() : locationDetails.getName());
            pageInfo.setLocationAddress(commonService.prepareBusinessAddress(locationDetails));
            pageInfo.setParentName(locationDetails.getAccountName());
        }
        return pageInfo;

    }

    @Override
    public BusinessLinkedinPage findByProfileId(String pageId) {
        return linkedInRepo.findByProfileId(pageId);
    }

    @Override
    public void updateTable(Integer id, Integer isValid, String scope, String moduleImpacted) {
        BusinessLinkedinPage businessLinkedinPage = businessLinkedinPageRepository.findById(id);
        if(Objects.nonNull(isValid)) {
            businessLinkedinPage.setIsValid(isValid);
        }
        if(Objects.nonNull(scope)) {
            businessLinkedinPage.setScope(scope);
        }
        if(Objects.nonNull(moduleImpacted)) {
            businessLinkedinPage.setModuleImpacted(moduleImpacted);
        }
        businessLinkedinPageRepository.save(businessLinkedinPage);
    }

    private void updateMappingForGMBPages(Long sourceEnterpriseNumber, Long targetEnterpriseNumber,List<BusinessLinkedinPage> mappingData) {
        mappingData.forEach(page-> {
            LOGGER.info("Updated Linkedin enterpriseId from {} to {}  for location Ids {}", sourceEnterpriseNumber, targetEnterpriseNumber, page.getProfileId());
            commonService.sendLinkedinSetupAuditEvent(SocialSetupAuditEnum.UPDATE_MAPPING.name(),
                    Collections.singletonList(page), null, page.getBusinessId(),page.getEnterpriseId());
            page.setEnterpriseId(targetEnterpriseNumber);
            linkedInRepo.save(page);
        });
    }

	@Override
	public Map<String, Object> getPaginatedPages(BusinessGetPageRequest businessGetPageRequest, Integer pageNumber,
			Integer pageSize, String search) {
		Map<String, Object> pageData = new HashMap<>();
		Map<String, List<ChannelAccountInfoLite>> pageType = new HashMap<>();
		List<ChannelAccountInfoLite> managed = new ArrayList<>();

		// fetchedPages =  defined pageNumber and pageSize to get pagableResult
		LinkedinLiteDto fetchedPages = StringUtils.isNotEmpty(search) ?
				convertToLinkedinObject(search,new PageRequest(pageNumber, pageSize,Sort.Direction.ASC, IS_SELECTED),businessGetPageRequest.getId().toString()):
					findByRequestIdOrderByCreatedAt(businessGetPageRequest.getId().toString(),new PageRequest(pageNumber, pageSize,Sort.Direction.ASC, IS_SELECTED));
		if (CollectionUtils.isNotEmpty(fetchedPages.getPageLites())) {
			fetchedPages.getPageLites().forEach(page -> managed.add(getResellerAccInfo(page,businessGetPageRequest.getEmail())));
			if (CollectionUtils.isNotEmpty(managed))
				pageType.put(SocialChannel.LINKEDIN.getName(), managed);
		}
		pageData.put("pageType", pageType);
		pageData.put("totalCount", fetchedPages.getTotalElements());
		pageData.put("pageCount", fetchedPages.getTotalPages());
		return pageData;
	}

	private ChannelAccountInfoLite getResellerAccInfo(BusinessLinkedinPage businessLinkedinPage, String userEmail) {
		ChannelAccountInfoLite channelAccountInfo = new ChannelAccountInfoLite();
		 channelAccountInfo.setType(businessLinkedinPage.getPageType());
         if(businessLinkedinPage.getPageType().equalsIgnoreCase(LinkedinPageTypeEnum.COMPANY.getName())){
             channelAccountInfo.setPageName(businessLinkedinPage.getCompanyName());
         }else{
             String fullName = null;
             if(StringUtils.isNotBlank(businessLinkedinPage.getFirstName()) && StringUtils.isNotBlank(businessLinkedinPage.getLastName())){
                 fullName = businessLinkedinPage.getFirstName()+ " "+businessLinkedinPage.getLastName();
             }else{
                 fullName = businessLinkedinPage.getFirstName();
             }
             channelAccountInfo.setPageName(fullName);
         }
         channelAccountInfo.setId(businessLinkedinPage.getProfileId());
         channelAccountInfo.setLink(businessLinkedinPage.getPageUrl());
         Validity validity = fetchValidityAndErrorMessage(businessLinkedinPage);
         channelAccountInfo.setValidType(validity.getValidType());
         channelAccountInfo.setErrorCode(validity.getErrorCode());
         channelAccountInfo.setErrorMessage(validity.getErrorMessage());
         channelAccountInfo.setUserId(userEmail);
         channelAccountInfo.setDisabled((businessLinkedinPage.getIsSelected() != null && businessLinkedinPage.getIsSelected() == 1) ? Boolean.TRUE : Boolean.FALSE);
		return channelAccountInfo;
	}

	private LinkedinLiteDto convertToLinkedinObject(String search, PageRequest pageRequest, String requestId) {
		Page<BusinessLinkedinPage> page = searchWithRequestId(search,pageRequest,requestId);
		return getinkedinLiteDto(page);
	}
	public LinkedinLiteDto findByRequestIdOrderByCreatedAt(String requestId, PageRequest pageRequest) {
		Page<BusinessLinkedinPage> page = businessLinkedinPageRepository.findByBusinessGetPageId(requestId,pageRequest);
		return getinkedinLiteDto(page);
	}

	private LinkedinLiteDto getinkedinLiteDto(Page<BusinessLinkedinPage> page) {
		LinkedinLiteDto linkedinLiteDto = new LinkedinLiteDto();
		if (Objects.nonNull(page)) {
			linkedinLiteDto.setPageLites(page.getContent());
			linkedinLiteDto.setTotalElements(page.getTotalElements());
			linkedinLiteDto.setTotalPages(page.getTotalPages());
		}
		return linkedinLiteDto;
	}

	private Page<BusinessLinkedinPage> searchWithRequestId(String search, PageRequest pageRequest,
			String requestId) {
		Specification<BusinessLinkedinPage> igSpec = Specifications.where((linkedinSpecification.hasPageName(search))).
				and(linkedinSpecification.hasRequestId(requestId));

		return businessLinkedinPageRepository.findAll(igSpec,pageRequest);
	}

    @Override
    public void validityCheckForLinkedin(Collection<String> linkedinProfileIds) {
        if(CollectionUtils.isEmpty(linkedinProfileIds)){
            LOGGER.error("linkedin account ids can not be null");
            return;
        }
        LOGGER.info("Request received to update validity column for linedkin accountIds: {}", linkedinProfileIds);
        List<String> linkedinAccountIds = (List<String>) linkedinProfileIds;

        List<BusinessLinkedinPage> businessLinkedinPages = linkedInRepo.findByProfileIdIn(linkedinAccountIds);
        if(CollectionUtils.isEmpty(businessLinkedinPages)){
            LOGGER.info("No pages found for account ids : {}", businessLinkedinPages);
            return;
        }
        businessLinkedinPages.forEach(account -> {
            account.setCanPost(getLinkedinPostPermission(Collections.singletonList(account),
                    Collections.singletonList(SocialMessageModule.PUBLISH.name())) ? 1: 0);
            updateLinkedinValidityType(account);
        });
        linkedInRepo.save(businessLinkedinPages);
        LOGGER.info("Successfully saved to db");
    }

    @Override
    public void updateLinkedinValidityType(BusinessLinkedinPage page) {
        Validity validity = fetchValidityAndErrorMessage(page);
        if(Objects.nonNull(validity.getValidType()) ) {
            if(validity.getValidType().equalsIgnoreCase(ValidTypeEnum.PARTIAL_VALID.getName())){
                page.setValidType(ValidTypeEnum.PARTIAL_VALID.getId());
            } else if(validity.getValidType().equalsIgnoreCase(ValidTypeEnum.VALID.getName())){
                page.setValidType(ValidTypeEnum.VALID.getId());
            } else {
                page.setValidType(ValidTypeEnum.INVALID.getId());
            }
        }
    }
	
	private void updateLinkedInPageForReseller(BusinessLinkedinPage businessLinkedinPage, String type, Integer locationId) {
	    if (Constants.RESELLER.equals(type)) {
	        BusinessLiteDTO businessLiteDTO = iBusinessCoreService.getBusinessLite(locationId, false);
	        businessLinkedinPage.setAccountId(businessLiteDTO.getAccountId());

	        if (Objects.isNull(businessLiteDTO.getEnterpriseId())) {
	            businessLinkedinPage.setEnterpriseId(businessLiteDTO.getBusinessNumber());
	        } else {
	            businessLinkedinPage.setEnterpriseId(businessLiteDTO.getEnterpriseNumber());
	        }
	    }
	}

    @Override
    public List<Integer> getMappedResellerLeafLocations(List<Integer> resellerLeafLocationIds) {
        if(CollectionUtils.isNotEmpty(resellerLeafLocationIds)) {
            return linkedInRepo.findAllIdByBusinessIdIn(resellerLeafLocationIds);
        }
        return Collections.emptyList();
    }

    /**
     * @param requestIds
     */
    @Override
    public List<String> getMappedRequestIds(Set<String> requestIds) {
        return linkedInRepo.findDistinctBusinessGetPageIdByBusinessGetPageIdIn(requestIds);
    }
}
