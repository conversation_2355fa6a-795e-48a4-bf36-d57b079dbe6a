package com.birdeye.social.service.Channels;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.dao.SocialStreamsRepository;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.EngageFeedDetails;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.exception.SocialException;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.facebook.*;
import com.birdeye.social.facebook.FacebookPost.FacebookPostDetails;
import com.birdeye.social.model.*;
import com.birdeye.social.model.FbNotification.*;
import com.birdeye.social.model.engageV2.EngageBusinessDetails;
import com.birdeye.social.model.engageV2.EngageCommentRequest;
import com.birdeye.social.model.engageV2.PageDetailsData;
import com.birdeye.social.model.engageV2.message.ExternalServiceEvent;
import com.birdeye.social.model.engageV2.EngageWebhookSubscriptionRequest;
import com.birdeye.social.model.engageV2.message.InboxMessageRequest;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.FacebookPageService;
import com.birdeye.social.service.IFbNotificationService;
import com.birdeye.social.service.IRedisExternalService;
import com.birdeye.social.service.SocialEngageService.SocialEngageV2;
import com.birdeye.social.service.SocialEngageService.converter.EngageConverterService;
import com.birdeye.social.utils.DateTimeUtils;
import com.birdeye.social.utils.SocialProxyHandler;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.EngageV2FeedTypeEnum.AD_COMMENT;
import static com.birdeye.social.constant.EngageV2FeedTypeEnum.COMMENT;

@Service
public class FacebookEngagementServiceV2 implements SocialEngageV2 {

    @Autowired
    private FacebookService facebookService;

    @Autowired
    private SocialFBPageRepository fbPageRepository;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private EngageConverterService engageConverterService;

    @Autowired
    private IFbNotificationService fbNotificationService;

    @Autowired
    private IRedisExternalService redisExternalService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private SocialProxyHandler socialProxyHandler;

    private static final Logger log = LoggerFactory.getLogger(Facebook.class);

    public static final String DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss+SSSS";

    public static final String FB_URL = "https://www.facebook.com/";
    public static final String PAGE_NOT_FOUND_LOG_MSG = "No valid page found for pageId {}";
    public static final String INVALID_PAGE_MSG = "Invalid page";
    public static final String INTERNAL_ERROR_LOG_MSG = "Something went wrong while call fb API with error {} for request {}";
    public static final String PHOTO = "photo";
    public static final String VIDEO = "video";
    public static final String MULTI_SHARE_NO_CARD = "multi_share_no_end_card";
    public static final String SHARE = "share";
    public static final String ALBUM = "album";
    public static final String VIDEO_AUTOPLAY = "video_autoplay";
    public static final String VIDEO_INLINE = "video_inline";
    public static final String ANIMATED_IMAGE_SHARE = "animated_image_share";
    private static final String SINGULAR_API_EXCEPTION_STRING = "singular statuses API is deprecated for versions v2.4 and higher";
    private static final String ERROR_MESSAGE_KEY = "errorMessage";

    @Autowired
    private SocialStreamsRepository socialStreamsRepository;

    @Autowired
    private FacebookPageService fbPageService;


    @Override
    public String channelName() {
        return SocialChannel.FACEBOOK.getName();
    }

    @Override
    public SocialTimeline getFeedData(String pageId, EngageFeedDetails feedDetails, String type) {
        try {
            List<BusinessFBPage> businessFBPages = fbPageRepository.findByFacebookPageIdAndIsValid(pageId, 1);
            if(CollectionUtils.isEmpty(businessFBPages)) {
                log.info(PAGE_NOT_FOUND_LOG_MSG, pageId);
                return null;
            }
            BusinessFBPage businessFBPage = businessFBPages.get(0);
            FacebookPageAccessInfo creds = new FacebookPageAccessInfo(businessFBPage.getFacebookPageId(), businessFBPage.getPageAccessToken(), getFacebookGraphApiBaseUrl());
            FacebookFeedData feedData = null;

            /* removing backfill data and just fetching 10 post if(Objects.nonNull(feedDetails)) {
                return getAllFeedData(businessFBPage, feedDetails.getFeedId());
            } else {*/
                feedData = facebookService.getFacebookMyPost(creds, null);
            //}

            return convertFBPostEngageFeed(feedData, businessFBPage, null);

        } catch (Exception ex) {
            log.info("Something went wrong while fetching post data for facebook for page id {} with error {}", pageId, ex);
            return null;
        }
    }

    @Override
    public List<EngageNotificationDetails> getFeedEngagement(String pageId) {
        return null;
    }

    private SocialTimeline getAllFeedData(BusinessFBPage businessFBPage, String feedId) {
        try {
            Boolean matchFound = false;
            FacebookFeedData filteredPosts = new FacebookFeedData();
            List<FacebookFeed> data = new ArrayList<>();

            FacebookPageAccessInfo creds = new FacebookPageAccessInfo(businessFBPage.getFacebookPageId(), businessFBPage.getPageAccessToken(), getFacebookGraphApiBaseUrl());

            FacebookFeedData feedData = facebookService.getFacebookMyPost(creds, null);
            if(Objects.nonNull(feedData) && CollectionUtils.isNotEmpty(feedData.getData())) {
                matchFound = addFeedData(feedData, feedId, data);
            }

            String nextPageToken = Objects.nonNull(feedData) && Objects.nonNull(feedData.getPaging().getNext()) ? feedData.getPaging().getCursors().getAfter() : null;

            while(!matchFound) {
                if(Objects.isNull(nextPageToken)) {
                    break;
                }

                FacebookFeedData feedData1  = facebookService.getFacebookMyPost(creds, nextPageToken);
                if(Objects.nonNull(feedData1) && CollectionUtils.isNotEmpty(feedData1.getData())) {
                   matchFound = addFeedData(feedData1, feedId, data);
                } else {
                    break;
                }

                nextPageToken = Objects.nonNull(feedData1.getPaging().getNext()) ? feedData1.getPaging().getCursors().getAfter() : null;
            }

            filteredPosts.setData(data);

            return convertFBPostEngageFeed(filteredPosts, businessFBPage, null);

        } catch (Exception ex) {
            log.info("Something went wrong while fetching post details for fbpage {}", businessFBPage);
            return null;
        }
    }

    private boolean addFeedData(FacebookFeedData feedData, String feedId, List<FacebookFeed> data) {
        for(FacebookFeed feed : feedData.getData()) {
            if(feed.getId().equalsIgnoreCase(feedId)) {
               return true;
            }
            data.add(feed);
        }
        return false;
    }


    @Override
    public List<EngageNotificationDetails> getCommentData(EngageCommentRequest request) {
        try {
            List<Integer> restrictedAccountIds =
                    CacheManager.getInstance().getCache(SystemPropertiesCache.class).getEngageCommentFetchedRestrictions();
            Boolean getAll = true;
            if(CollectionUtils.isNotEmpty(restrictedAccountIds) && restrictedAccountIds.get(0).equals(1)) {
                log.info("fetching comments, all comments not allowed for facebook limit to 5");
                getAll = false;
            }
            int counter = 15;
            List<BusinessFBPage> businessFBPages = fbPageRepository.findByFacebookPageIdAndIsValid(request.getPageId(), 1);
            if(CollectionUtils.isEmpty(businessFBPages)) {
                log.info(PAGE_NOT_FOUND_LOG_MSG, request.getPageId());
                return Arrays.asList();
            }
            BusinessFBPage businessFBPage = businessFBPages.get(0);
            Integer accountId = businessFBPage.getAccountId();
            if(Objects.nonNull(accountId)) {
                if( CollectionUtils.isNotEmpty(restrictedAccountIds) && restrictedAccountIds.contains(accountId)) {
                    log.info("restricted account id found: {}, fetching 5 comment", accountId);
                    getAll = false;
                }
            }
            FbComments feedData = facebookService.getPostComments(request.getObjectId(), businessFBPage.getPageAccessToken(), null, getAll);
            List<EngageNotificationDetails> comments = convertFBPostCommentToEngage(feedData, businessFBPage, request.getObjectId());
            return comments;

        } catch (BirdeyeSocialException e) {
            if (isSingularStatusException(e)) {
                log.warn("FacebookEngagementServiceV2#getCommentData() Something went wrong while fetching comment for request {} with error {}", request, e.getData().get(ERROR_MESSAGE_KEY));
            } else {
                log.warn("Something went wrong while fetching comment for request {} with error ", request, e);
            }
            return Arrays.asList();
        } catch (Exception ex) {
                log.info("Something went wrong while fetching comment for request {} with error ", request, ex);
            return Arrays.asList();
        }
    }

    @Override
    public  EngageNotificationDetails prepareAdditionalParamentToEs(EngageNotificationDetails request) {

        try {
            List<BusinessFBPage> pages = fbPageRepository.findByFacebookPageId(request.getPageId());

            if(CollectionUtils.isEmpty(pages)) {
                log.info("No page found for pageId {}", request.getPageId());
                return null;
            }

            if(StringUtils.isEmpty(request.getPageName())) {
                request.setPageName(pages.get(0).getFacebookPageName());
            }

            if(Objects.isNull(request.getAuthorProfileImage()) && StringUtils.isNotEmpty(request.getAuthorId())) {
                FbUserProfileInfo authorProfileInfo = getUserDetails(request.getAuthorId(), pages.get(0).getPageAccessToken());
                if(Objects.isNull(authorProfileInfo)) {
                    log.info("User profile info could not be fetched {}", request.getAuthorId());
                } else {
                    request.setAuthorProfileImage(authorProfileInfo.getPicture().getData().getUrl());
                }
            } else {
                log.info("User profile info could not be fetched as user id is null");
            }
        } catch (Exception ex) {
            log.info("Something went wrong while fetching details to request {} with error {}", request, ex);
        }

        return request;
    }

    private FbUserProfileInfo getUserDetails(String userId, String accessToken) {
        try {
            return facebookService.getUserDetails(null, userId, accessToken);
        } catch (Exception e) {
            if(e instanceof SocialBirdeyeException) {
                try {
                    return facebookService.getUserDetails(null, userId, fbPageService.getPPCASystemUserToken());
                } catch (Exception e2) {
                    return null;
                }
            }
            return null;
        }
    }

    @Override
    public Feed getPostDetails(FreshPostNotificationRequest request) {
        try {
            Feed feed = new Feed();
            List<BusinessFBPage> businessFBPages = fbPageRepository.findByFacebookPageIdAndIsValid(request.getPageId(), 1);
            if(CollectionUtils.isEmpty(businessFBPages)) {
                log.info(PAGE_NOT_FOUND_LOG_MSG, request.getPageId());
                return null;
            }
            BusinessFBPage businessFBPage = businessFBPages.get(0);
            FacebookPostDetails postDetails =
                    socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(() ->
                        facebookService.getPostDataForEngage(businessFBPage.getPageAccessToken(), request.getPostId())
                    );
            if(Objects.isNull(postDetails)) {
                log.info("Cannot fetch post details for postId {}", request.getPostId());
                return null;
            }
            String adminProfilePicture =
                    facebookService.getUserProfilePicture(getFacebookGraphApiBaseUrl(),
                            businessFBPage.getPageAccessToken(), businessFBPage.getFacebookPageId());
            feed.setPublisherName(businessFBPage.getFacebookPageName());
            feed.setPublisherHandle(businessFBPage.getHandle());
            feed.setProfileImage(adminProfilePicture);
            feed.setDatePublished(postDetails.getCreated_time());
            feed.setFeedId(request.getPostId());
            feed.setPublisherId(businessFBPage.getFacebookPageId());
            feed.setProfileURL(FB_URL + feed.getPublisherId());
            if(Objects.nonNull(postDetails.getAttachments())) {
                attachPostMediaDetails(postDetails, feed);
            }
            feed.setCan_like(checkAdminPostLike(postDetails, businessFBPage.getFacebookPageId()));
            feed.setCanDelete(true);
            feed.setFeedText(postDetails.getMessage());
            feed.setFeedUrl("https://fb.com/"+ request.getPostId());
            feed.setPageName(businessFBPage.getFacebookPageName());
            feed.setMessageTags(postDetails.getMessage_tags());
            if(postDetails.getReactions()!=null && postDetails.getReactions().getSummary()!=null ) {
                feed.setLikes(postDetails.getReactions().getSummary().getTotal_count());
            }
            if(postDetails.getComments()!=null && postDetails.getComments().getSummary()!=null) {
                feed.setComments(postDetails.getComments().getSummary().getTotal_count());
            }
            feed.setAccountId(businessFBPage.getAccountId());
            feed.setBusinessId(businessFBPage.getBusinessId());
            return feed;
        } catch (Exception ex) {
            log.info("Something went wrong while fetching comment for request {} with error : {}", request, ex.getMessage());
            return null;
        }
    }

    private boolean checkAdminPostLike(FacebookPostDetails postDetails, String pageId) {
        boolean canLike = true;

        try {
            if (postDetails.getLikes() != null) {
                for (Like like : postDetails.getLikes().getData()) {
                    if (like.getId().trim().equalsIgnoreCase(pageId)) {
                        canLike = false;
                        break;
                    }
                }
            }
        } catch (Exception ex) {
            log.info("Error occured while checking post reaction check with error ", ex);
        }
        return canLike;
    }

    @Override
    public void startBackFill(GenericScriptRequest scriptRequest) {
        EngageWebhookSubscriptionRequest engageWebhookSubscriptionRequest = new EngageWebhookSubscriptionRequest();
        engageWebhookSubscriptionRequest.setChannel(Constants.FACEBOOK);
        engageWebhookSubscriptionRequest.setSubscription(true);

        List<Integer> socialStreamsList = socialStreamsRepository.findSocialIdByChannelId(SocialChannel.FACEBOOK.getId());
        Set<String> streamPageIds = new HashSet<>();
        if(CollectionUtils.isNotEmpty(socialStreamsList)) {
            streamPageIds = fbPageRepository.findPageIdById(socialStreamsList);
        }
        Set<String> finalStreamPageIds = streamPageIds;
        if(CollectionUtils.isNotEmpty(scriptRequest.getBusinessIds())){
            List<String> businessFBPageIds = fbPageRepository.findDistinctFacebookPageIdByBusinessIdIn(scriptRequest.getBusinessIds());
            if(CollectionUtils.isEmpty(businessFBPageIds)){
                log.info("No data left for back filling engage");
                return;
            }
            businessFBPageIds.forEach(pageId -> {
                if(finalStreamPageIds.contains(pageId) && scriptRequest.getBackFill()) {
                    engageWebhookSubscriptionRequest.setBackFill(true);
                }
                engageWebhookSubscriptionRequest.setPageId(pageId);
                kafkaProducerService.sendObjectV1(Constants.SOCIAL_FACEBOOK_SUBSCRIBE, engageWebhookSubscriptionRequest);
            });
            return;
        }
        int page = scriptRequest.getPage();
        int size = scriptRequest.getSize();
        while (true) {
            List<String> businessFBPageIds = fbPageRepository.findValidBusinessPageIds(new PageRequest(page, size));
            if(CollectionUtils.isEmpty(businessFBPageIds)){
                log.info("No data left for back filling engage");
                break;
            }
            businessFBPageIds.forEach(pageId -> {
                if(finalStreamPageIds.contains(pageId) && scriptRequest.getBackFill()) {
                    engageWebhookSubscriptionRequest.setBackFill(true);
                }
                engageWebhookSubscriptionRequest.setPageId(pageId);
                kafkaProducerService.sendObjectV1(Constants.SOCIAL_FACEBOOK_SUBSCRIBE, engageWebhookSubscriptionRequest);
            });
            page++;
        }

    }

    @Override
    public void saveMessages(InboxMessageRequest inboxMessageRequest) {
        List<BusinessFBPage> businessFBPageList = fbPageRepository.findByFacebookPageIdAndIsValid(inboxMessageRequest.getPageId(),1);
        if(CollectionUtils.isEmpty(businessFBPageList)){
            log.info("No facebook pages found for page id : {} and message id : {}",inboxMessageRequest.getPageId(),inboxMessageRequest.getMessageId());
            return;
        }
        BusinessFBPage businessFBPage = businessFBPageList.get(0);
        inboxMessageRequest.setPageName(businessFBPage.getFacebookPageName());
        EngageNotificationDetails engageNotificationDetails = engageConverterService.convertInboxObjToEngageObj(inboxMessageRequest,SocialChannel.FACEBOOK);
        engageNotificationDetails.setReviewerUrl(FB_URL+inboxMessageRequest.getAuthorId());
        if(Objects.equals(inboxMessageRequest.getCommunicationDirection(), "SEND")){
            engageConverterService.setRepliedOnIdAndUpdateBrandReplyId(engageNotificationDetails);
        }
        log.info("setting reviewerUrl:{}",engageNotificationDetails.getReviewerUrl());
        engageNotificationDetails.setRawFeedId(engageConverterService.validateAndSaveEngageDetails(engageNotificationDetails));
        log.info("Create or update raw feed id :{}",engageNotificationDetails.getFeedId());
        kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC.getName(), engageNotificationDetails);
    }

    public EngageBusinessDetails getChannelEnterpriseIdByPageId(String pageId) {
        EngageBusinessDetails engageBusinessDetails = new EngageBusinessDetails();
        List<BusinessFBPage> pages = fbPageRepository.findByFacebookPageId(pageId);

        if(CollectionUtils.isEmpty(pages)) {
            log.info("No page found for pageId {}", pageId);
            return null;
        }

        engageBusinessDetails.setEnterpriseId(pages.get(0).getEnterpriseId());
        engageBusinessDetails.setBusinessIds(Objects.nonNull(pages.get(0).getBusinessId()) ? Arrays.asList(pages.get(0).getBusinessId()) : null);

        return engageBusinessDetails;

    }

    @Override
    public Feed getFeedDetails(String pageId, Feed feed) {
        return null;
    }

    @Override
    public void hideWallPost(SocialEngageObjectRequest request) {
        try {
            List<BusinessFBPage> businessFBPages = fbPageRepository.findByFacebookPageIdAndIsValid(request.getPageId(), 1);
            if(CollectionUtils.isEmpty(businessFBPages)) {
                throw new BirdeyeSocialException(INVALID_PAGE_MSG);
            }
            BusinessFBPage page= businessFBPages.get(0);
            facebookService.hideContent(request.getFeedId(), page.getPageAccessToken(), request.getHide());
        } catch (Exception ex) {
            log.info(INTERNAL_ERROR_LOG_MSG, ex, request);
        }
    }

    @Override
    public void hidePostComment(SocialEngageObjectRequest request) {
        try {
            List<BusinessFBPage> businessFBPages = fbPageRepository.findByFacebookPageIdAndIsValid(request.getPageId(), 1);
            if(CollectionUtils.isEmpty(businessFBPages)) {
                throw new BirdeyeSocialException(INVALID_PAGE_MSG);
            }
            BusinessFBPage page= businessFBPages.get(0);
            facebookService.hideContent(request.getFeedId(), page.getPageAccessToken(), request.getHide());
        } catch (Exception ex) {
            log.info(INTERNAL_ERROR_LOG_MSG, ex, request);
        }
    }

    @Override
    public void blockUserFromPage(SocialEngageObjectRequest request, ExternalServiceEvent blockUserEvent) {

        try {
            List<BusinessFBPage> businessFBPages = Objects.isNull(request.getPageId())
                    ? fbPageRepository.findByBusinessId(request.getBusinessId())
                    :fbPageRepository.findByFacebookPageIdAndIsValid(request.getPageId(), 1);
            if(CollectionUtils.isEmpty(businessFBPages)) {
                throw new BirdeyeSocialException(INVALID_PAGE_MSG);
            }
            BusinessFBPage page= businessFBPages.get(0);
            blockUserEvent.setBusinessId(page.getBusinessId());
            request.setPageId(page.getFacebookPageId());
            facebookService.blockUser(request.getAuthorId(), page.getFacebookPageId(), page.getPageAccessToken());
        } catch (BirdeyeSocialException ex) {
            throw new SocialException(ex.getMessage(), ex.getCode());
        } catch (Exception e) {
            throw new SocialException(e.getMessage(), ErrorCodes.INTERNAL_SERVER_ERROR.value());
        }
    }

    @Override
    public void unBlockUserFromPage(SocialEngageObjectRequest request, ExternalServiceEvent blockUserEvent) {
        List<BusinessFBPage> businessFBPages = Objects.isNull(request.getPageId())
                ? fbPageRepository.findByBusinessId(request.getBusinessId())
                : fbPageRepository.findByFacebookPageIdAndIsValid(request.getPageId(), 1);
        if(CollectionUtils.isEmpty(businessFBPages)) {
            throw new BirdeyeSocialException(INVALID_PAGE_MSG);
        }
        BusinessFBPage page= businessFBPages.get(0);
        request.setPageId(page.getFacebookPageId());
        blockUserEvent.setBusinessId(page.getBusinessId());
        facebookService.unblockUser(request.getAuthorId(), page.getFacebookPageId(), page.getPageAccessToken());
    }

    @Override
    public void likePageContent(SocialEngageObjectRequest request,ExternalServiceEvent externalServiceEvent) {
        try {
            List<BusinessFBPage> businessFBPages = fbPageRepository.findByFacebookPageIdAndIsValid(request.getPageId(), 1);
            if(CollectionUtils.isEmpty(businessFBPages)) {
                throw new BirdeyeSocialException(INVALID_PAGE_MSG);
            }
            BusinessFBPage page= businessFBPages.get(0);

            FacebookPageAccessInfo creds = new FacebookPageAccessInfo();
            creds.setAccessToken(page.getPageAccessToken());
            creds.setObjectId(request.getFeedId());
            externalServiceEvent.setBusinessId(externalServiceEvent.getBusinessId());
            facebookService.likeObject(creds);
        } catch (Exception ex) {
            log.info(INTERNAL_ERROR_LOG_MSG, ex, request);
        }
    }

    @Override
    public void unLikePageContent(SocialEngageObjectRequest request,ExternalServiceEvent externalServiceEvent) {
        try {
            List<BusinessFBPage> businessFBPages = fbPageRepository.findByFacebookPageIdAndIsValid(request.getPageId(), 1);
            if(CollectionUtils.isEmpty(businessFBPages)) {
                throw new BirdeyeSocialException(INVALID_PAGE_MSG);
            }
            BusinessFBPage page= businessFBPages.get(0);

            FacebookPageAccessInfo creds = new FacebookPageAccessInfo();
            creds.setAccessToken(page.getPageAccessToken());
            creds.setBaseUrl(getFacebookGraphApiBaseUrl() + request.getFeedId() + "/");

            facebookService.unlikeObject(creds);
        } catch (Exception ex) {
            log.info(INTERNAL_ERROR_LOG_MSG, ex, request);
        }
    }

    @Override
    public void commentPageContent(SocialEngageObjectRequest request) throws Exception {
        try {
            List<BusinessFBPage> businessFBPages = fbPageRepository.findByFacebookPageIdAndIsValid(request.getPageId(), 1);
            if(CollectionUtils.isEmpty(businessFBPages)) {
                throw new BirdeyeSocialException(INVALID_PAGE_MSG);
            }
            BusinessFBPage page= businessFBPages.get(0);
            if(Objects.nonNull(page)) {
                commonService.checkRequestFromAuthorizedSourceUsingBusinessNumber(page.getEnterpriseId(), request.getBusinessNumber());
            }

            FacebookPageAccessInfo creds =
                    new FacebookPageAccessInfo(page.getFacebookPageId(), page.getPageAccessToken(),
                            getFacebookGraphApiBaseUrl() + request.getFeedId() + "/");
            creds.setObjectId(request.getFeedId());

            FacebookData facebookData = new FacebookData();
            FbUser publisher = null;
            if (StringUtils.isNotEmpty(request.getPublishedBy())) {
                log.info("publisher id is not empty --> {}", request.getPublishedBy());
                publisher = new FbUser();
                publisher.setId(request.getPublisherId());
                publisher.setName(request.getPublishedBy());

            }
            facebookData.setText(request.getCommentMsg());
            if (CollectionUtils.isNotEmpty(request.getCommentImages())) {
                if (request.getCommentImages().size() > 1) {
                    throw new BirdeyeSocialException("multiple images not allowed");
                } else {
                    facebookData.setMediaUrl(request.getCommentImages().get(0));
                }
            } else if (CollectionUtils.isNotEmpty(request.getCommentVideos())) {
                if (request.getCommentVideos().size() > 1) {
                    throw new BirdeyeSocialException("multiple images not allowed");
                } else {
                    facebookData.setMediaUrl(request.getCommentVideos().get(0));
                }
            }
            String commentId = facebookService.postComment(creds, facebookData, publisher);
            request.setCommentId(commentId);
            request.setAccountId(page.getAccountId());
            request.setBusinessId(page.getBusinessId());
            String adminProfilePicture =
                    facebookService.getUserProfilePicture(getFacebookGraphApiBaseUrl(), page.getPageAccessToken(), page.getFacebookPageId());
            request.setAuthorProfileImage(adminProfilePicture);
            saveDataInDbAndEs(request, page);
        } catch (Exception ex) {
            log.info(INTERNAL_ERROR_LOG_MSG, ex, request);
            throw ex;
        }
    }

    private String makeTextInvert(String value) {
        value = "@["+value+"]";
        return value;
    }

    private EngageNotificationDetails fetchEsDocByFeedIdAndPageId(String feedId, String pageId) throws IOException {
        return engageConverterService.fetchEsDocByFeedIdAndPageId(feedId, pageId);
    }

    private void saveDataInDbAndEs(SocialEngageObjectRequest request, BusinessFBPage fbPage) {

        try {
            log.info("Starting flow to save data: {}", request);

            String text = request.getCommentMsg();
            if (CollectionUtils.isNotEmpty(request.getMentionsData())) {

                for (EngageCommentMentionData mentionData : request.getMentionsData()) {
                    if (text.contains(makeTextInvert(mentionData.getId()))
                            && SocialChannel.FACEBOOK.getName().equalsIgnoreCase(request.getChannel())) {
                        text = text.replace("@[" + mentionData.getId() + "]", mentionData.getName());
                    }
                }
            }

            EngageNotificationDetails notificationDetails = new EngageNotificationDetails();
            Comment comment = facebookService.getCommentById(request.getCommentId(), fbPage.getPageAccessToken());
            boolean isParentComment = request.getFeedId().equals(request.getPostId());
            notificationDetails.setPageId(request.getPageId());
            notificationDetails.setPostId(request.getPostId());
            EngageNotificationDetails engageNotificationDetails = new EngageNotificationDetails();
            engageNotificationDetails = fetchEsDocByFeedIdAndPageId(request.getPostId(), request.getPageId());
            if (Objects.isNull(engageNotificationDetails)) {
                log.info("Cannot find feed for postId {}", request.getPostId());
            }
            notificationDetails.setFeedId(request.getCommentId());
            notificationDetails.setEventParentId(request.getFeedId());
            notificationDetails.setAuthorId(request.getPageId());
            notificationDetails.setText(text);
            notificationDetails.setPostUrl(FB_URL + request.getPostId());
            notificationDetails.setReviewerUrl(FB_URL + request.getPageId());
            notificationDetails.setAuthorName(fbPage.getFacebookPageName());
            notificationDetails.setAuthorUsername(fbPage.getFacebookPageName());
            notificationDetails.setAddedFromDashboard(true);
            notificationDetails.setAuthorProfileImage(null);
            if (Objects.nonNull(request.getCommentImages())) {
                notificationDetails.setImageUrls(request.getCommentImages());
            }
            notificationDetails.setEventAction("add");
            notificationDetails.setIsLikedByAdmin(false);
            notificationDetails.setIsCompleted(false);
            notificationDetails.setIsAdminComment(true);
            notificationDetails.setRepliedOnId(request.getRepliedOnId());
            notificationDetails.setIsParentComment(isParentComment);
            notificationDetails.setEngageFeedId(request.getFeedId());
            notificationDetails.setSourceId(SocialChannel.FACEBOOK.getId());
            notificationDetails.setChannel(SocialChannel.FACEBOOK.getName());
            notificationDetails.setType(EngageV2FeedTypeEnum.COMMENT.name());

            if (Objects.nonNull(engageNotificationDetails) && Objects.nonNull(engageNotificationDetails.getSubType())
                    && engageNotificationDetails.getSubType().equalsIgnoreCase(EngageV2FeedSubTypeEnum.MENTION.name())) {
                notificationDetails.setCanReplyPrivately(true);
            } else {
                notificationDetails.setCanReplyPrivately(false);
                if (Objects.nonNull(comment)) {

                    if (Objects.isNull(notificationDetails.getImageUrls()) && Objects.nonNull(comment.getAttachment()) && Objects.nonNull(comment.getAttachment().getMedia()) &&
                            Objects.nonNull(comment.getAttachment().getMedia().getImage()) &&
                            Objects.nonNull(comment.getAttachment().getMedia().getImage().getSrc())) {
                        notificationDetails.setImageUrls(Collections.singletonList(comment.getAttachment().getMedia().getImage().getSrc()));
                    }
                }
            }

            if (!notificationDetails.getIsParentComment()) {
                notificationDetails.setSubType(EngageV2FeedSubTypeEnum.REPLY.name());
            }
            notificationDetails.setIsBrandReply(request.getIsBrandReply()); // Replied by brand from BirdEye dashboard
            notificationDetails.setBirdeyeUserId(request.getUserId());
            notificationDetails.setAccountId(request.getAccountId());
            notificationDetails.setLocationId(request.getBusinessId());
            notificationDetails.setHideOnThread(notificationDetails.getIsAdminComment());
            notificationDetails.setIsEdited(false);
            Date date = new Date();
            notificationDetails.setFeedDate(DateTimeUtils.localToUTCSqlFormat(date));
            //For Response Time calculation
            request.setFeedDate(notificationDetails.getFeedDate());

            Integer acknowledgementId = fbNotificationService.validateFBContentDetails(notificationDetails, request.getPageId(), false);

            log.info("Acknowledgement id: {}", acknowledgementId);

            if (Objects.isNull(acknowledgementId)) {
                log.info("This comment is already processed, must be duplicate. exiting the flow");
                return;
            }
            request.setRawFeedId(acknowledgementId);
            notificationDetails.setRawFeedId(acknowledgementId);
            notificationDetails.setTopParentFeedId(isParentComment ? request.getCommentId() : request.getTopParentFeedId());
            notificationDetails.setMessageTags(Objects.isNull(comment) ? null : comment.getMessage_tags());
            kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC.getName(), notificationDetails);
        } catch (Exception e) {
            log.info("Exception while saving comment in db: {}", request.getCommentId(),e);
        }
    }

    @Override
    public void deletePageContent(SocialEngageObjectRequest request) throws Exception{
        try {
            List<BusinessFBPage> businessFBPages = fbPageRepository.findByFacebookPageIdAndIsValid(request.getPageId(), 1);
            if(CollectionUtils.isEmpty(businessFBPages)) {
                throw new BirdeyeSocialException(INVALID_PAGE_MSG);
            }
            BusinessFBPage page= businessFBPages.get(0);
            if(Objects.nonNull(page)) {
                commonService.checkRequestFromAuthorizedSourceUsingBusinessNumber(page.getEnterpriseId(), request.getBusinessNumber());
            }
            FacebookPageAccessInfo creds = new FacebookPageAccessInfo();
            creds.setAccessToken(page.getPageAccessToken());
            creds.setBaseUrl(getFacebookGraphApiBaseUrl() + request.getFeedId());
            boolean isDeleted = true;
            if( Objects.equals(request.getType(), COMMENT.name())|| Objects.equals(request.getType(), AD_COMMENT.name())){
                boolean isCommentDeleted = facebookService.getCommentExistById(request.getFeedId(),page.getPageAccessToken());
                if(!isCommentDeleted)
                     isDeleted = facebookService.deleteObject(creds);
            }
            else
            {
                isDeleted = facebookService.deleteObject(creds);
            }
            
            if(!isDeleted) {
                throw new BirdeyeSocialException(ErrorCodes.DELETE_FAILED,"Failed to post comment");
            }
        } catch (Exception ex) {
            log.info(INTERNAL_ERROR_LOG_MSG, ex, request);
            throw ex;
        }
    }

    @Override
    public Boolean subscribeNotificationWebhook(String pageId) {
        try {
            List<BusinessFBPage> businessFBPages = fbPageRepository.findByFacebookPageIdAndIsValid(pageId, 1);
            if(CollectionUtils.isEmpty(businessFBPages)) {
                throw new BirdeyeSocialException(INVALID_PAGE_MSG);
           }

            BusinessFBPage page= businessFBPages.get(0);
            SubscribeToWebhookRequest request = new SubscribeToWebhookRequest();
            request.setBusinessId(page.getBusinessId());

            return fbNotificationService.subscribeToWebhookV2(request);
        } catch (Exception ex){
            if(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBooleanProperty(Constants.SET_FAILURE_REDIS)) {
                redisExternalService.setKeyAndValue(Constants.FB_FAILURE_REDIS_KEY, pageId);
                log.info("Get key FB_FAILURE_REDIS_KEY: {}", redisExternalService.get(Constants.FB_FAILURE_REDIS_KEY));
            }
            log.info("unable to subscribe for pageId {} with error ", pageId, ex);
        }
        return false;
    }


    @Override
    public void unSubscribeNotificationWebhook(String pageId) {
        try {
            SubscribeToWebhookRequest request = new SubscribeToWebhookRequest();
            request.setPageId(pageId);

            fbNotificationService.unSubscribeToWebhook(request);
        } catch (Exception ex) {
            log.info("Something went wrong while unsubscribing for pageId {}",pageId, ex);
        }
    }

    @Override
    public void followUser(SocialEngageObjectRequest request) {
        // not used in FB
    }


    private void attachPostMediaDetails(FacebookPostDetails postDetails, Feed feed) {

        if (postDetails.getAttachments()!=null && (PHOTO.equalsIgnoreCase(postDetails.getAttachments().getData().get(0).getType())
                || SHARE.equalsIgnoreCase(postDetails.getAttachments().getData().get(0).getType())
                || MULTI_SHARE_NO_CARD.equalsIgnoreCase(postDetails.getAttachments().getData().get(0).getType())
                || ANIMATED_IMAGE_SHARE.equalsIgnoreCase(postDetails.getAttachments().getData().get(0).getType())
                ||ALBUM.equalsIgnoreCase(postDetails.getAttachments().getData().get(0).getType()) )) {
            List<String> images = new ArrayList<>();
            List<String> videos = new ArrayList<>();
            addAttachmentData(postDetails, images, videos);
            feed.setImages(images);
            feed.setVideos(videos);
        }  else if (postDetails.getAttachments()!=null && (VIDEO.equalsIgnoreCase(postDetails.getAttachments().getData().get(0).getType())
                || VIDEO_INLINE.equalsIgnoreCase(postDetails.getAttachments().getData().get(0).getType())
                || VIDEO_AUTOPLAY.equalsIgnoreCase(postDetails.getAttachments().getData().get(0).getType()))) {
            List<String> videos = new ArrayList<>();
            videos.add(postDetails.getAttachments().getData().get(0).getMedia().getSource());
            feed.setVideos(videos);
        }

        if(postDetails.getAttachments() != null){
            log.info("LOg to check attach type for testing : {}", postDetails.getAttachments().getData().get(0).getType());
        }
    }

    private void addAttachmentData(FacebookPostDetails postDetails, List<String> images, List<String> videos ) {
        for (Attachement data : postDetails.getAttachments().getData()) {
            if (ALBUM.equalsIgnoreCase(data.getType())
                    || ANIMATED_IMAGE_SHARE.equalsIgnoreCase(data.getType())
                    || MULTI_SHARE_NO_CARD.equalsIgnoreCase(data.getType())
                    || PHOTO.equalsIgnoreCase(data.getType())
                    || ANIMATED_IMAGE_SHARE.equalsIgnoreCase(data.getType())
                    || SHARE.equalsIgnoreCase(data.getType())) {
                log.info("Getting data for FB attachment : {}", postDetails);
                FbAttachement subAttachment = data.getSubattachments();
                if (null != subAttachment) {
                    addSubAttachmentData(subAttachment, images, videos);
                } else {
                    if (data.getMedia() != null && data.getMedia().getImage() != null && data.getMedia().getImage().getSrc() != null) {
                        images.add(data.getMedia().getImage().getSrc());
                    }
                }
            }
        }
    }

    private void addSubAttachmentData(FbAttachement subAttachment, List<String> images, List<String> videos) {
            for (Attachement data1 : subAttachment.getData()) {
                if(VIDEO.equalsIgnoreCase(data1.getType()) || VIDEO_AUTOPLAY.equalsIgnoreCase(data1.getType())) {
                    videos.add(data1.getMedia().getSource());
                } else {
                    images.add(data1.getMedia().getImage().getSrc());
                }
            }
        }


    private SocialTimeline convertFBPostEngageFeed(FacebookFeedData feeds, BusinessFBPage fbPage, Date maxDate)  {
        SocialTimeline socialTimeline = new SocialTimeline();
        try {
            List<Feed> feedsList = new ArrayList<>();
            String adminProfilePicture = facebookService.getUserProfilePicture(null, fbPage.getPageAccessToken(), fbPage.getFacebookPageId());
            Feed feed;
            if (feeds != null) {
                for (FacebookFeed fbFeed : feeds.getData()) {
                    SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT);
                    if (null != feeds.getPaging()) {
                        socialTimeline.setNextUrl(feeds.getPaging().getCursors().getAfter());
                    }
                    if(Objects.nonNull(maxDate) && dateFormat.parse(fbFeed.getCreated_time()).after(maxDate)) {
                        socialTimeline.setNextUrl(null);
                        break;
                    }
                    feed = new Feed();
                    feed.setFeedId(fbFeed.getId());
                    feed.setFeedText(fbFeed.getMessage());
                    feed.setDatePublished(fbFeed.getCreated_time());

                    feed.setPublisherId(fbFeed.getFrom().getId());
                    feed.setPublisherName(fbFeed.getFrom().getName());
                    setFeedDetails(feed, fbPage, adminProfilePicture, fbFeed);
                    feed.setFeedUrl(FB_URL + feed.getFeedId());
                    FacebookPostDetails postEngageDetails = facebookService.getPostData(fbPage.getPageAccessToken(), feed.getFeedId());
                    if(Objects.nonNull(postEngageDetails)) {
                        if(postEngageDetails.getReactions()!=null && postEngageDetails.getReactions().getSummary() !=null) {
                            feed.setLikes(postEngageDetails.getReactions().getSummary().getTotal_count());
                        }
                        if(postEngageDetails.getComments()!=null && postEngageDetails.getComments().getSummary() !=null) {
                            feed.setComments(postEngageDetails.getComments().getSummary().getTotal_count());
                        }
                    }else{
                        log.info("Unable to fetch like/comment count for postId {}", feed.getFeedId());
                    }
                    feed.setBusinessId(fbPage.getBusinessId());
                    feed.setAccountId(fbPage.getAccountId());

                    convertFeedToEngageFeed(fbFeed, fbPage, adminProfilePicture, feed);
                    // following block is for shared post

                    feedsList.add(feed);
                }
                socialTimeline.setFeeds(feedsList);
            }
            socialTimeline.setChannel("facebook");
            socialTimeline.setStreamName(fbPage.getFacebookPageName());
            socialTimeline.setStreamImage(adminProfilePicture);
            socialTimeline.setBusinessId(fbPage.getBusinessId());
            socialTimeline.setPageId(fbPage.getId());
            socialTimeline.setPageName(fbPage.getFacebookPageName());
            return socialTimeline;
        } catch (Exception ex) {
            log.info("Error occured", ex);
        }
        return socialTimeline;
    }

    private void convertFeedToEngageFeed(FacebookFeed fbFeed, BusinessFBPage fbPage, String adminProfilePicture, Feed feed) {
        // following block is for shared post
        if (StringUtils.isNotEmpty(fbFeed.getStory()) && StringUtils.isNotEmpty(fbFeed.getParent_id())) {
            // it is a shared post
            Feed quotedFeed = new Feed();
            //changed while upgrading graph api version to v6.0
            quotedFeed.setFeedText(fbFeed.getAttachments().getData().get(0).getDescription());
            quotedFeed.setFeedId(fbFeed.getParent_id());
            quotedFeed.setFeedUrl(FB_URL + fbFeed.getParent_id());
            FacebookPageAccessInfo info = new FacebookPageAccessInfo(null, fbPage.getPageAccessToken(), null);
            info.setObjectId(fbFeed.getParent_id());
            FbUser user = null;
            try {
                user = facebookService.getUserForPost(info);
                quotedFeed.setPublisherId(user.getId());
                quotedFeed.setPublisherName(user.getName());
            } catch (Exception e) {
                log.error("Error while fetching facebook user for post info {}", info);
            }
            if (quotedFeed.getPublisherId() != null) {
                addPictureDetails(quotedFeed, fbPage, user, adminProfilePicture, info);

            }
            if (fbFeed.getAttachments() != null
                    && !CollectionUtils.isEmpty(fbFeed.getAttachments().getData())) {
                addMediaAttachmentDetails(fbFeed, quotedFeed);
            }
            feed.setQuotedFeed(quotedFeed);
        }
    }

    private void addMediaAttachmentDetails(FacebookFeed fbFeed, Feed quotedFeed) {
        if (fbFeed.getAttachments().getData().get(0).getMedia_type().equalsIgnoreCase(PHOTO)) {
            List<String> images = new ArrayList<>();
            List<String> videos = new ArrayList<>();
            addFeedAttachmentData(fbFeed, images, videos);
            quotedFeed.setImages(images);
        } else if (fbFeed.getAttachments().getData().get(0).getMedia_type().equalsIgnoreCase(VIDEO)) {
            List<String> videos = new ArrayList<>();
            // changed for graph api version upgrated to v6.0
            videos.add(fbFeed.getAttachments().getData().get(0).getMedia().getSource());
            quotedFeed.setVideos(videos);
        }
    }

    private void addPictureDetails(Feed quotedFeed, BusinessFBPage fbPage, FbUser user, String adminProfilePicture, FacebookPageAccessInfo info ) {
        if (quotedFeed.getPublisherId().equalsIgnoreCase(fbPage.getFacebookPageId())) {
            quotedFeed.setProfileImage(adminProfilePicture);
            quotedFeed.setProfileURL(fbPage.getLink());
        } else {
            quotedFeed.setProfileURL(FB_URL + quotedFeed.getPublisherId());
            if (user != null && user.getId() != null) {
                try {
                    String profilePicture = facebookService.getUserProfilePicture(null, fbPage.getPageAccessToken(), user.getId());
                    quotedFeed.setProfileImage(profilePicture);
                } catch (Exception e) {
                    log.error("Error while fetching facebook getUserProfilePicture for post info {}", info);
                }
            }
        }
    }

    private void setFeedDetails(Feed feed, BusinessFBPage fbPage, String adminProfilePicture, FacebookFeed fbFeed) throws IOException {

        if (fbFeed.getLikes() != null) {
            feed.setLikes(fbFeed.getLikes().getData().size());
        }
        if (null != fbFeed.getShares()) {
            feed.setShares(Integer.parseInt(fbFeed.getShares().getCount()));
        }

        if (feed.getPublisherId().equalsIgnoreCase(fbPage.getFacebookPageId())) {
            feed.setProfileImage(adminProfilePicture);
            feed.setProfileURL(fbPage.getLink());
            feed.setEditable(true);
        } else {
            feed.setProfileURL(FB_URL + feed.getPublisherId());
            feed.setProfileImage(facebookService.getUserProfilePicture(null, fbPage.getPageAccessToken(), feed.getPublisherId()));
        }
        if (StringUtils.isEmpty(fbFeed.getParent_id()) && fbFeed.getAttachments() != null) {
            addAttachmentDetails(fbFeed, feed);
        }


        Boolean canLike = true;
        if (fbFeed.getLikes() != null) {
            for (Like like : fbFeed.getLikes().getData()) {
                if (like.getId().trim().equalsIgnoreCase(fbPage.getFacebookPageId().trim())) {
                    canLike = false;
                    break;
                }
            }
        }
        feed.setCan_like(canLike);
    }

    private void addAttachmentDetails(FacebookFeed fbFeed, Feed feed) {
        if (PHOTO.equalsIgnoreCase(fbFeed.getAttachments().getData().get(0).getMedia_type())
                || SHARE.equalsIgnoreCase(fbFeed.getAttachments().getData().get(0).getMedia_type())
                || MULTI_SHARE_NO_CARD.equalsIgnoreCase(fbFeed.getAttachments().getData().get(0).getMedia_type())
                ||ALBUM.equalsIgnoreCase(fbFeed.getAttachments().getData().get(0).getMedia_type()) ) {
            List<String> images = new ArrayList<>();
            List<String> videos = new ArrayList<>();
            addFeedAttachmentData(fbFeed, images, videos);
            feed.setVideos(videos);
            feed.setImages(images);
        }  else if (VIDEO.equalsIgnoreCase(fbFeed.getAttachments().getData().get(0).getMedia_type())
                || "video_inline".equalsIgnoreCase(fbFeed.getAttachments().getData().get(0).getMedia_type())
                ||  VIDEO_AUTOPLAY.equalsIgnoreCase(fbFeed.getAttachments().getData().get(0).getMedia_type())) {
            List<String> videos = new ArrayList<>();
            videos.add(fbFeed.getAttachments().getData().get(0).getMedia().getSource());
            feed.setVideos(videos);
        }
    }

    private void addFeedAttachmentData(FacebookFeed fbFeed, List<String> images, List<String> videos ) {
        for (Attachement data : fbFeed.getAttachments().getData()) {
            if (ALBUM.equalsIgnoreCase(data.getMedia_type())
                    || MULTI_SHARE_NO_CARD.equalsIgnoreCase(data.getMedia_type())) {
                FbAttachement subAttachment = data.getSubattachments();
                if (null != subAttachment) {
                    addSubAttachmentData(subAttachment, images, videos);
                }
            } else {
                if (data.getMedia() != null && data.getMedia().getImage() != null && data.getMedia().getImage().getSrc() != null) {
                    images.add(data.getMedia().getImage().getSrc());
                }
            }
        }
    }

    private List<EngageNotificationDetails> convertFBPostCommentToEngage(FbComments comments, BusinessFBPage fbPage, String postId) throws BirdeyeSocialException, Exception {
        List<EngageNotificationDetails> conversations = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(comments.getData()) &&  null != comments.getData()) {
            String adminProfilePicture = facebookService.getUserProfilePicture(null, fbPage.getPageAccessToken(), fbPage.getFacebookPageId());

            for (Comment comment : comments.getData()) {
                EngageNotificationDetails conversation = new EngageNotificationDetails();

                conversation.setFeedDate(DateTimeUtils.formatGraphApiStringToDate(comment.getCreated_time()));
                conversation.setText(comment.getMessage());
                commentPrimaryDetails(comment, fbPage,conversation, adminProfilePicture);
                conversation.setCanReplyPrivately(comment.getCan_reply_privately());
                conversation.setEventParentId(Objects.nonNull(comment.getParent()) ? comment.getParent().getId() : postId);
                conversation.setIsParentComment(Objects.isNull(comment.getParent()));
                conversation.setFeedId(comment.getId());
                conversation.setEngageFeedId(postId);
                conversation.setAccountId(fbPage.getAccountId());
                conversation.setLocationId(fbPage.getBusinessId());
                if(Objects.nonNull(comment.getLike_count())) {
                    conversation.setLikeCount(Integer.parseInt(comment.getLike_count()));
                }
                if(Objects.nonNull(comment.getComment_count())) {
                    conversation.setCommentCount(Integer.parseInt(comment.getComment_count()));
                }

                conversations.add(conversation);
            }
        }
        return conversations;
    }

    private void commentPrimaryDetails(Comment comment, BusinessFBPage fbPage, EngageNotificationDetails conversation, String adminProfilePicture) throws Exception {
        Boolean canLike = true;

        if (Objects.nonNull(comment.getFrom())) {
            String authorId = comment.getFrom().getId();
            String authorName = comment.getFrom().getName();

            conversation.setIsAdminComment(Objects.equals(authorId, fbPage.getFacebookPageId()));
            conversation.setAuthorId(authorId);
            conversation.setAuthorName(authorName);
            conversation.setAuthorUsername(authorName);
            conversation.setReviewerUrl(FB_URL + authorId);
        }
        if (Objects.nonNull(conversation.getAuthorId()) && conversation.getAuthorId().equalsIgnoreCase(fbPage.getFacebookPageId())) {
            conversation.setAuthorProfileImage(adminProfilePicture);
        } else {
            conversation.setAuthorProfileImage(facebookService.getUserProfilePicture(getFacebookGraphApiBaseUrl(), fbPage.getPageAccessToken(), conversation.getAuthorId()));
        }
        if (null != comment.getAttachment()) {
            commentDetails(comment, fbPage, conversation);
        }
        canLike = true;
        if (comment.getLikes() != null) {
            canLike = canLike(comment, fbPage);
        }
        conversation.setIsLikedByAdmin(!canLike);
    }

    private void commentDetails(Comment comment, BusinessFBPage fbPage, EngageNotificationDetails conversation) throws Exception {
        if (comment.getAttachment().getType().equalsIgnoreCase(PHOTO) || comment.getAttachment().getType().equalsIgnoreCase("sticker")
                || comment.getAttachment().getType().equalsIgnoreCase("animated_image_share") || comment.getAttachment().getType().equalsIgnoreCase("native_templates")) {
            List<String> images = new ArrayList<>();
            images.add(comment.getAttachment().getMedia().getImage().getSrc());
            conversation.setImageUrls(images);
        } else {
            addMediaDetails(comment, fbPage, conversation);
        }
    }

    private boolean canLike(Comment comment, BusinessFBPage fbPage) {
        for (Like like : comment.getLikes().getData()) {
            if (like.getId().trim().equalsIgnoreCase(fbPage.getFacebookPageId().trim())) {
                return false;
            }
        }
        return true;
    }

    private void addMediaDetails(Comment comment, BusinessFBPage fbPage, EngageNotificationDetails conversation) throws Exception {
        if (comment.getAttachment().getTarget() != null) {
            String targetId = comment.getAttachment().getTarget().getId();
            if (targetId != null && !targetId.equalsIgnoreCase(fbPage.getFacebookPageId())) {
                FacebookPageAccessInfo info = new FacebookPageAccessInfo(null, fbPage.getPageAccessToken(), getFacebookGraphApiBaseUrl() + targetId);
                MediaTarget target = facebookService.getVideoSource(info);
                if (target != null) {
                    List<String> videos = new ArrayList<>();
                    videos.add(target.getSource());
                    conversation.setVideoUrls(videos);
                }
            } else {
                List<String> images = new ArrayList<>();
                if(Objects.nonNull(comment.getAttachment().getMedia())) {
                    images.add(comment.getAttachment().getMedia().getImage().getSrc());
                }
                conversation.setImageUrls(images);
            }
        }
    }


    public String getFacebookGraphApiBaseUrl() {
        String baseUrl = FacebookApis.GRAPH_API_BASE_WITH_VERSION;
        if (com.birdeye.social.utils.StringUtils.isEmpty(baseUrl)) {
            baseUrl = FacebookApis.GRAPH_API_BASE_VERSION_DEFAULT_V21;
        }
        return baseUrl;
    }

    @Override
    public void unfollowUser(SocialEngageObjectRequest request) {
        // only used in twitter
    }

    @Override
    public void shareComment(SocialEngageObjectRequest request) {
        // not used for fb

    }

    @Override
    public void unShareComment(SocialEngageObjectRequest request) {
        // not used for fb
    }

    @Override
    public void saveCommentInDbAndInES(SocialEngageObjectRequest request, EngageNotificationDetails documentFromEs) {
        // saved in commentPageContent method
    }

    @Override
    public void likeMessage(SocialEngageObjectRequest request, ExternalServiceEvent externalServiceEvent) {

    }

    @Override
    public void unLikeMessage(SocialEngageObjectRequest request, ExternalServiceEvent externalServiceEvent) {

    }

    @Override
    public boolean isPageValid(String pageId) {
        List<BusinessFBPage> fbPage = fbPageRepository.findByFacebookPageIdAndIsValid(pageId, 1);
        if(CollectionUtils.isEmpty(fbPage)) return false;
        return true;
    }

    @Override
    public List<String> getChannelPageIdsByEnterpriseId(Long enterpriseId) {
        return fbPageRepository.findByEnterpriseIdAndIsValidAndMapped(enterpriseId);
    }

    @Override
    public PageDetailsData getChannelPageIdsByEnterpriseIds(List<Long> enterpriseId) {

        List<BusinessFBPage> pageDetails= fbPageRepository.findByEnterpriseIdInAndIsValidAndBusinessIdNotNull(enterpriseId);
        List<PageDetailsData.PageDetails> pageDetailsList = pageDetails.stream()
                .map(page -> {
                    PageDetailsData.PageDetails pageDetailsObj = new PageDetailsData.PageDetails();
                    pageDetailsObj.setPageId(page.getFacebookPageId()); // Assuming getPageId() exists
                    pageDetailsObj.setBusinessId(page.getBusinessId()); // Assuming getBusinessId() exists
                    pageDetailsObj.setAccountId(page.getAccountId()); // Assuming getAccountId() exists
                    return pageDetailsObj;
                })
                .collect(Collectors.toList());
        PageDetailsData data = new PageDetailsData();
        data.setPageDetailsList(pageDetailsList);
        return data;

    }

    @Override
    public void subscribeEngageNotificationWebhook(String pageId) {
        try {
            List<BusinessFBPage> businessFBPages = fbPageRepository.findByFacebookPageIdAndBusinessIdIsNotNull(pageId);
            if(CollectionUtils.isEmpty(businessFBPages)) {
                throw new BirdeyeSocialException(INVALID_PAGE_MSG);
            }

            BusinessFBPage page= businessFBPages.get(0);

            if(page.getIsValid() == 0) {
                log.info("page is not valid, existing the subscribing process");
            }
            SubscribeToWebhookRequest request = new SubscribeToWebhookRequest();
            request.setBusinessId(page.getBusinessId());

            fbNotificationService.subscribeEngageToWebhook(request);
        } catch (Exception ex) {
            log.info("unable to subscribe for pageId {} with error {}", pageId, ex);
        }
    }

    @Override
    public void unSubscribeEngageNotificationWebhook(String pageId) {
        try {

            SubscribeToWebhookRequest request = new SubscribeToWebhookRequest();
            request.setPageId(pageId);

            fbNotificationService.unSubscribeEngageToWebhook(request);
        } catch (Exception ex) {
            log.info("unable to subscribe for pageId {} with error {}", pageId, ex);
        }

    }

    @Override
    public  void removePageNotificationWebhook(EngageWebhookSubscriptionRequest request) {
        try {
            fbNotificationService.removePageEngageToWebhook(request);
        } catch (Exception ex) {
            log.info("unable to subscribe for pageId {} with error {}", request, ex);
        }
    }

    @Override
    public String getReviewerUrlByPageId(String pageId) {
        List<BusinessFBPage> pages=fbPageRepository.findByFacebookPageId(pageId);
        if(CollectionUtils.isNotEmpty(pages))
        {
            return pages.get(0).getLink();
        }
        return "";
    }

    @Override
    public EngagePageDetails getRawPageDetails(String pageId) {
        List<BusinessFBPage> pages=fbPageRepository.findByFacebookPageId(pageId);
        if(CollectionUtils.isNotEmpty(pages))
        {
            return EngagePageDetails.builder()
                    .pageName(pages.get(0).getFacebookPageName())
                    .username(pages.get(0).getHandle())
                    .profilePicUrl(pages.get(0).getFacebookPagePictureUrl())
                    .pageId(pages.get(0).getFacebookPageId())
                    .build();
        }
        return EngagePageDetails.builder().build();
    }

    @Override
    public boolean validateImageUrls(List<String> imageUrls) {
        return true;
    }

    @Override
    public boolean validateCommentDetails(List<EngageNotificationDetails> comments, String commentId) {
        return true;
    }

    private boolean isSingularStatusException(BirdeyeSocialException ex) {
        if (Objects.nonNull(ex) && Objects.nonNull(ex.getData())) {
            String error = String.valueOf(ex.getData().get(ERROR_MESSAGE_KEY));
            return StringUtils.isNotBlank(error) ? error.contains(SINGULAR_API_EXCEPTION_STRING) : false;
        }
        return false;
    }
}
