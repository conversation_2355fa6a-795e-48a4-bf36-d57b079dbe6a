package com.birdeye.social.service;

import com.birdeye.social.entities.PermissionMapping;
import com.birdeye.social.model.PermissionMappingRequest;
import com.birdeye.social.model.SocialMessageModule;

import java.util.Collection;
import java.util.List;

public interface IPermissionMappingService {

    PermissionMapping getDataByChannelAndPermissionCode(String channel, Integer permissionCode);
    PermissionMapping getDataByChannelAndPermissionCodeAndModule(String channel, Integer permissionCode, String module);

    List<PermissionMapping> getDataByChannelAndPermissionNameNotNull(String channel);

    PermissionMapping getDataByChannelAndErrorCode(String channel, String errorCode);

    List<PermissionMapping> getListOfDataByChannelAndErrorCode(String channel, String errorCode);

    PermissionMapping getDataByChannelAndModuleAndErrorCode(String channel, String module, String errorCode);

    List<PermissionMapping> getDataByChannelandHttpResponseAndErrorCodeAndErrorSubCode(String channel, Integer httpResponse, Integer errorCode, Integer errorSubCode);

    List<PermissionMapping> getDataByChannelandHttpResponseAndErrorCode(String channel, Integer httpResponse, Integer errorCode);

    List<PermissionMapping> getDataByChannelandHttpResponse(String channel, Integer httpResponse);

    List<PermissionMapping> getDataByChannelAndParentErrorCodeAndPermissionCode(String channel, Integer errorCode, Integer errorSubCode);
    List<PermissionMapping> getDataByChannelAndParentErrorCodeAndPermissionCodeNull(String channel, Integer errorCode, Integer errorSubCode);
    List<PermissionMapping> getListOfDataByChannelAndPermissionCode(String channel, Integer errorSubCode);

    List<PermissionMapping> getDataByChannelAndHttpErrorResponse(String channel, Integer errorResponse);

    PermissionMapping getDataByChannelAndActualErrorText(String channel, String message);

    PermissionMapping getDataByChannelAndParentCodeAndErrorCode(String channel, Integer parentCode, String message);

    void manualInsertUpdateDelete(PermissionMappingRequest request, String operation);

    String getDataByChannelAndModuleAndPermissionName(String name, SocialMessageModule gmbMessaging, String verificationError);

    String getDataByChannelAndModuleAndPermissionCode(String name, SocialMessageModule gmbMessaging, Integer permissionCode);
    List<PermissionMapping> getDataByChannelAndParentErrorCodeAndPermissionCodeAndModule(String channel, Integer errorCode, Integer errorSubCode, SocialMessageModule module);


    List<PermissionMapping> getDataByChannelAndModuleAndPermissionNameNotNull(String name, String name1);

    List<PermissionMapping> getDataByChannelAndPermissionCodeInAndModule(String channel, Collection<Integer> permissionCode, String module);
	List<PermissionMapping> getDataByChannel(String name);
    PermissionMapping getErrorMessageAndCode(String permissionName,Integer value, String name);

    PermissionMapping getPostingErrorByChannelAndModuleAndErrorCode(String channel, String module, String errorCode);

    PermissionMapping getPostingErrorByChannelAndModuleAndPermissionCode(String channel, String module, Integer permissionCode);
}
