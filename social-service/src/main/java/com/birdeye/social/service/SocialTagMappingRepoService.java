package com.birdeye.social.service;

import com.birdeye.social.constant.SocialTagEntityType;
import com.birdeye.social.dao.SocialTagMappingRepository;
import com.birdeye.social.dto.SocialTagMappingInfo;
import com.birdeye.social.entities.SocialTagMapping;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> on 21/12/23
 */
@Service
public class SocialTagMappingRepoService {

    @Autowired
    private SocialTagMappingRepository socialTagMappingRepository;

    public List<SocialTagMappingInfo> findTagMappingInfoByEntityIdsAndEntityType(Collection<Long> entityIds, SocialTagEntityType entityType) {
        if (CollectionUtils.isNotEmpty(entityIds)) {
            return socialTagMappingRepository.findTagMappingInfoByEntityIdsAndEntityType(entityIds, entityType);
        }
        return Collections.emptyList();
    }
    public List<SocialTagMappingInfo> findTagMappingInfoByEntityIdsAndEntityType(Collection<Long> entityIds, List<SocialTagEntityType> entityType) {
        if (CollectionUtils.isNotEmpty(entityIds)) {
            return socialTagMappingRepository.findTagMappingInfoByEntityIdsAndEntityType(entityIds, entityType);
        }
        return Collections.emptyList();
    }

    public List<SocialTagMappingInfo> findDistinctEntityIdAndEntityTypeMappedToTagIds(Collection<Long> tagIds) {
        if (CollectionUtils.isNotEmpty(tagIds)) {
            return socialTagMappingRepository.findDistinctEntityIdAndEntityTypeMappedToTagIdIn(tagIds);
        }
        return Collections.emptyList();
    }

    public List<Long> findEntityIdByEntityTypeAndTagIdIn(Collection<Long> tagIds, SocialTagEntityType entityType) {
        if (CollectionUtils.isNotEmpty(tagIds)) {
            return socialTagMappingRepository.findEntityIdByEntityTypeAndTagIdIn(tagIds, entityType);
        }
        return Collections.emptyList();
    }

    public List<Long> findEntityIdByEntityTypeAndAccountId(Integer accountId, SocialTagEntityType entityType) {
        return socialTagMappingRepository.findEntityIdByEntityTypeAndAccountId(accountId, entityType);
    }

    public void deleteByTagIds(Collection<Long> tagIds) {
        if (CollectionUtils.isNotEmpty(tagIds)) {
            socialTagMappingRepository.deleteByTagIdIn(tagIds);
        }
    }

    public void deleteByEntityIdsEntityTypeAndTagIds(Collection<Long> entityIds, SocialTagEntityType entityType, Collection<Long> tagIds) {
        if (CollectionUtils.isNotEmpty(entityIds) && CollectionUtils.isNotEmpty(tagIds)) {
            socialTagMappingRepository.deleteByEntityIdsEntityTypeAndTagIds(entityIds, entityType, tagIds);
        }
    }

    public void deleteByEntityIdsEntityTypeAndAccountId(Collection<Long> entityIds, SocialTagEntityType entityType, Integer accountId) {
        if (CollectionUtils.isNotEmpty(entityIds)) {
            socialTagMappingRepository.deleteByEntityIdsEntityTypeAndAccountId(entityIds, entityType, accountId);
        }
    }

    public List<SocialTagMapping> saveAllAndFlush(List<SocialTagMapping> socialTagMappings) {
        if (CollectionUtils.isNotEmpty(socialTagMappings)) {
            socialTagMappings = socialTagMappingRepository.save(socialTagMappings);
            socialTagMappingRepository.flush();
        }
        return socialTagMappings;
    }
}