package com.birdeye.social.service.SocialTrendsReportService;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.TrendsLocationReportSortOption;
import com.birdeye.social.constant.TrendsReportSortOption;
import com.birdeye.social.dao.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.insights.ES.PostSortingOrder;
import com.birdeye.social.trends.*;
import com.birdeye.social.insights.constants.SLAReportConstants;
import com.birdeye.social.service.SocialEngageService.EngageFactory;
import com.birdeye.social.service.SocialReportService.Converter.ReportDataConverter;
import com.birdeye.social.service.SocialReportService.InsightsFactory;
import com.birdeye.social.service.SocialReportService.SocialInsights;
import com.birdeye.social.trends.SocialTrendsReportResponse;
import com.birdeye.social.trends.TrendsOverviewResponseDTO;
import com.birdeye.social.trends.TrendsLocationLeaderboardResponse;
import com.birdeye.social.trends.TrendsReportRequest;
import com.birdeye.social.trends.TrendsReportSummaryResponse;
import com.birdeye.social.utils.DateTimeUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import javax.ws.rs.BadRequestException;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class SocialTrendsReportServiceImpl implements SocialTrendsReportService {

    @Autowired
    private SocialTrendsEsService trendsEsService;

    @Autowired
    SocialPropertyRepository socialPropertyRepository;
    @Autowired
    private ReportDataConverter reportDataConverter;
    @Autowired
    private InsightsFactory socialInsights;
    @Autowired
    private EngageFactory engageFactory;

    private static final String DATE_FORMATTER_STRING = "MM/dd/yyyy";
    private static final String DATE_FOR_STRING = "MM/dd/yyyy";
    private static final String INVALID_CHANNEL="Invalid channel: {}";

    private static final Logger log = LoggerFactory.getLogger(SocialTrendsReportServiceImpl.class);

    @Override
    public TrendsReportSummaryResponse getSLAReportSummary(TrendsReportRequest request) {
        log.info("Generating response summary for request: {}", request);
        verifyStartDateAndEndDate(request);
        updateTrendsRequest(request);
        List<String> pageIds = getPageIdsFromBidsForGivenChannels(request.getBusinessIds(), request.getSocialChannels());
        if(CollectionUtils.isEmpty(pageIds) || request.getEndDate().before(request.getTrendsReportStartDate())){
            return new TrendsReportSummaryResponse();
        }
        SearchResponse searchResponse = trendsEsService.getTrendsResponseSummaryEsResponse(pageIds, request);
        if(Objects.isNull(searchResponse) || Objects.isNull(searchResponse.getAggregations()) || searchResponse.status().getStatus() != 200){
            log.error("Error while fetching data for trends summary report.");
            throw new BirdeyeSocialException("Error while fetching summary for trends report");
        }
        return trendsEsService.convertTrendsEsSummaryResponse(searchResponse, request.getMessages());
    }

    @Override
    public SocialTrendsReportResponse getResponseRateByChannel(TrendsReportRequest request) {
        log.info("Generating response rate by channel report for request: {}", request);
        verifyStartDateAndEndDate(request);
        updateTrendsRequest(request);
        List<String> pageIds = getPageIdsFromBidsForGivenChannels(request.getBusinessIds(), request.getSocialChannels());
        if(CollectionUtils.isEmpty(pageIds)){
            return new SocialTrendsReportResponse();
        }
        SearchResponse searchResponse = trendsEsService.getResponseRateByChannelEsResponse(pageIds, request);
        if (Objects.isNull(searchResponse) || Objects.isNull(searchResponse.getAggregations()) || searchResponse.status().getStatus() != 200){
            log.error("Error while fetching data for trends channel-wise report");
            throw new BirdeyeSocialException("Error while fetching data for trends report");
        }
        return trendsEsService.convertResponseRateByChannelEsResponse(searchResponse, request);
    }

    private List<String> getAllSocialChannels() {
        List<String> sourceIds = new ArrayList<>();
        sourceIds.add(SocialChannel.FACEBOOK.getName());
        sourceIds.add(SocialChannel.TWITTER.getName());
        sourceIds.add(SocialChannel.INSTAGRAM.getName());
        sourceIds.add(SocialChannel.LINKEDIN.getName());
        sourceIds.add(SocialChannel.YOUTUBE.getName());
        return sourceIds;
    }

    public boolean channelSupportedInTrends(String channel){
        return (SocialChannel.FACEBOOK.getName().equals(channel)
                || SocialChannel.TWITTER.getName().equals(channel)
                || SocialChannel.INSTAGRAM.getName().equals(channel)
                || SocialChannel.LINKEDIN.getName().equals(channel)
                || SocialChannel.YOUTUBE.getName().equals(channel));
    }

    @Override
    public SocialTrendsReportResponse getResponseTimeByUser(TrendsReportRequest request) throws IOException {
        if(StringUtils.isEmpty(request.getSortParam())) {
            request.setSortParam(TrendsReportSortOption.RESPONSE_TIME.getName());
            request.setSortOrder(PostSortingOrder.ASC.getName());
        }

        log.info("Generating response time by user for request: {}", request);
        verifyStartDateAndEndDate(request);
        updateTrendsRequest(request);


        TrendsReportSortOption sortingCriteria = TrendsReportSortOption.sortingCriteria(request.getSortParam());
        SortOrder sortingOrder = SortOrder.valueOf(request.getSortOrder().toUpperCase());
        if(sortingCriteria == null || sortingOrder == null) {
            throw new BadRequestException("Invalid sorting attributes");
        }
        List<String> pageIds = getPageIdsFromBidsForGivenChannels(request.getBusinessIds(), request.getSocialChannels());
        if(CollectionUtils.isEmpty(pageIds)){
            return new SocialTrendsReportResponse();
        }
        SearchResponse searchResponse = trendsEsService.getResponseTimeByUserEsResponse(pageIds, request,sortingOrder);
        if(Objects.isNull(searchResponse) || searchResponse.status().getStatus() != 200){
            log.error("Error while fetching data for user trends report");
            throw new BirdeyeSocialException("Error while fetching data for user trends report");
        }
        return trendsEsService.convertTrendsEsByUserResponse(searchResponse,request);
    }

    private void updateTrendsRequest(TrendsReportRequest request) {
        if(CollectionUtils.isEmpty(request.getSocialChannels()))
        {
            request.setSocialChannels(getAllSocialChannels());
        }else{
            // remove invalid channels
            request.getSocialChannels().removeIf(channel -> !channelSupportedInTrends(channel));
        }
        if(CollectionUtils.isEmpty(request.getMessages())){
            request.setMessages(Arrays.asList("COMMENTS", "DM"));
        }
        long diff = request.getEndDate().getTime() - request.getStartDate().getTime();
        long noOfDaysBetween = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        reportDataConverter.setGroupByType(request,noOfDaysBetween);
    }

    @Override
    public SocialTrendsReportResponse getResponseTimeByChannel(TrendsReportRequest request) {
        log.info("Generating response time by channel report for request: {}", request);
        verifyStartDateAndEndDate(request);
        updateTrendsRequest(request);

        List<String> pageIds = getPageIdsFromBidsForGivenChannels(request.getBusinessIds(), request.getSocialChannels());
        if(CollectionUtils.isEmpty(pageIds)){
            return new SocialTrendsReportResponse();
        }
        SearchResponse searchResponse = trendsEsService.getResponseTimeByChannelEsResponse(pageIds, request);
        if(Objects.isNull(searchResponse) || searchResponse.status().getStatus() != 200){
            log.error("Error while fetching data for trends response time report");
            throw new BirdeyeSocialException("Error while fetching response time data for trends report");
        }
        return trendsEsService.convertTrendsEsByChannelResponse(searchResponse,request);
    }

    @Override
    public List<Object> getSLAExcelReport(TrendsReportRequest request) throws IOException {
        log.info("Generating excel report for request: {}", request);

        SLAReportConstants reportType = SLAReportConstants.valueOf(request.getReportType().toUpperCase());
        switch (reportType) {
            case REPORT_SLA_SUMMARY :
                TrendsReportSummaryResponse summaryResponse = getSLAReportSummary(request);
                return convertSummarySLAResponse(summaryResponse);
            case REPORT_SLA_RR_CHANNEL:
                SocialTrendsReportResponse responseRateByChannel = getResponseRateByChannel(request);
                return convertResponseRateChannelData(responseRateByChannel);
            case REPORT_SLA_RT_USER:
                SocialTrendsReportResponse responseTimeByUser = getResponseTimeByUser(request);
                return convertResponseTimeUserData(responseTimeByUser);
            case REPORT_SLA_RT_CHANNEL:
                SocialTrendsReportResponse responseTimeByChannel = getResponseTimeByChannel(request);
                return convertResponseTimeChannelData(responseTimeByChannel);
            case REPORT_SLA_LOCATION_LEADERBOARD:
                setSortingParameters(request, false);
                TrendsLocationLeaderboardResponse response = getLocationLeaderboardData(request);
                return convertLocationLeaderBoardResponse(response);
            case REPORT_RESPONSE_OVERVIEW:
                setSortingParameters(request, true);
                TrendsOverviewResponseDTO responseOverview = getTrendsResponseOverview(request);
                return convertResponseOverviewExcel(responseOverview);
            default:
                log.warn("Invalid report type: {}", request.getReportType());
        }
        return Collections.emptyList();
    }

    private List<Object> convertResponseTimeChannelData(SocialTrendsReportResponse trendsReportResponse) {
        List<Object> slaResponses = new ArrayList<>();
        Map<String, TrendsReportMetrics> channelWiseData=trendsReportResponse.getChannelWiseData();
        for (ReportTrendsDataPoint dataPoint : trendsReportResponse.getDataPoints()) {
            for (String channel : channelWiseData.keySet()) {
                SLAExcelResponse.SLAExcelResponseTime response = SLAExcelResponse.SLAExcelResponseTime.builder()
                        .responded(getMessageCount(dataPoint,channel,true))
                        .responseTime(getResponseTime(dataPoint,channel))
                        .channel(getChannelData(channel))
                        .date(setStartDateAndEndDateInResponse(dataPoint.getStartDate()))
                        .build();
                slaResponses.add(response);
            }
        }
        return slaResponses;
                }

    private String getResponseTime(ReportTrendsDataPoint dataPoint, String channel) {
        Double responseTime = null;
        SocialChannel socialChannel = SocialChannel.getSocialChannelByName(channel);
        switch (socialChannel) {
            case FACEBOOK:
                responseTime= dataPoint.getFbResponseTime();
                break;
            case INSTAGRAM:
                responseTime= dataPoint.getIgResponseTime();
                break;
            case TWITTER:
                responseTime= dataPoint.getTwResponseTime();
                break;
            case LINKEDIN:
                responseTime= dataPoint.getLnResponseTime();
                break;
            case YOUTUBE:
                responseTime= dataPoint.getYtResponseTime();
                break;
            default:
                log.error(INVALID_CHANNEL, channel);
        }
        return formatTimeToUnits(responseTime);

    }


    private String setStartDateAndEndDateInResponse(String date) {
        try {
            SimpleDateFormat inputFormat = new SimpleDateFormat(DATE_FORMATTER_STRING);
            SimpleDateFormat outputFormat = new SimpleDateFormat(DATE_FOR_STRING);

            Date startDate = inputFormat.parse(date.trim()); // Parse with correct format
            return outputFormat.format(startDate);
        } catch (ParseException e) {
            log.error("Error while parsing date: {} {}", date, e.getMessage());
        }
        return null;
    }

    public List<Object> convertResponseRateChannelData(SocialTrendsReportResponse trendsReportResponse)
    {
        List<Object> slaResponses = new ArrayList<>();
        Map<String, TrendsReportMetrics> channelWiseData=trendsReportResponse.getChannelWiseData();
        for (ReportTrendsDataPoint dataPoint : trendsReportResponse.getDataPoints()) {
            for (String channel : channelWiseData.keySet()) {
                SLAExcelResponse.SLAExcelResponseRate response = SLAExcelResponse.SLAExcelResponseRate.builder()
                        .msgReceived(getMessageCount(dataPoint,channel,false))
                        .responded(getMessageCount(dataPoint,channel,true))
                        .responseRate(getResponseRate(dataPoint,channel))
                         .channel(getChannelData(channel))
                        .date(setStartDateAndEndDateInResponse(dataPoint.getStartDate()))
                        .build();
                slaResponses.add(response);
            }
        }
        return slaResponses;
    }

    private String getResponseRate(ReportTrendsDataPoint dataPoint, String channel) {
        Double responseRate = null;
        SocialChannel socialChannel = SocialChannel.getSocialChannelByName(channel);
        switch (socialChannel) {
            case FACEBOOK:
                responseRate= dataPoint.getFbResponseRate();
                break;
            case INSTAGRAM:
                responseRate= dataPoint.getIgResponseRate();
                break;
            case TWITTER:
                responseRate= dataPoint.getTwResponseRate();
                break;
            case LINKEDIN:
                responseRate= dataPoint.getLnResponseRate();
                break;
            case YOUTUBE:
                responseRate= dataPoint.getYtResponseRate();
                break;
            default:
                log.error(INVALID_CHANNEL, channel);
        }
        return formatToTwoDecimalString(responseRate);
    }


    private Long getMessageCount(ReportTrendsDataPoint dataPoint, String channel, boolean isReplied) {
        SocialChannel socialChannel = SocialChannel.getSocialChannelByName(channel);
        Long messageCount = null;

        switch (socialChannel) {
            case FACEBOOK:
                messageCount = isReplied ? dataPoint.getFbMessagesReplied() : dataPoint.getFbMessagesReceived();
                break;
            case INSTAGRAM:
                messageCount = isReplied ? dataPoint.getIgMessagesReplied() : dataPoint.getIgMessagesReceived();
                break;
            case TWITTER:
                messageCount = isReplied ? dataPoint.getTwMessagesReplied() : dataPoint.getTwMessagesReceived();
                break;
            case LINKEDIN:
                messageCount = isReplied ? dataPoint.getLnMessagesReplied() : dataPoint.getLnMessagesReceived();
                break;
            case YOUTUBE:
                messageCount = isReplied ? dataPoint.getYtMessagesReplied() : dataPoint.getYtMessagesReceived();
                break;
            default:
                log.error(INVALID_CHANNEL, channel);
                break;
        }

        return messageCount;
    }




    public String getChannelData(String channel) {

    SocialChannel socialChannel = SocialChannel.getSocialChannelByName(channel);
    switch (socialChannel) {
        case FACEBOOK:
           return SocialChannel.FACEBOOK.getLabel();
        case INSTAGRAM:
            return SocialChannel.INSTAGRAM.getLabel();
        case TWITTER:
            return SocialChannel.TWITTER.getLabel();
        case LINKEDIN:
            return SocialChannel.LINKEDIN.getLabel();
        case YOUTUBE:
            return SocialChannel.YOUTUBE.getLabel();
        default:
            log.error(INVALID_CHANNEL, channel);
    }
    return null;
}

    private void setSortingParameters(TrendsReportRequest request, boolean isOverview) {
        if(StringUtils.isEmpty(request.getSortParam())) {
            if(isOverview) {
                request.setSortParam(TrendsReportSortOption.RESPONSE_TIME.getName());
                request.setSortOrder(PostSortingOrder.ASC.getName());
            } else {
                request.setSortParam(TrendsLocationReportSortOption.RESPONSE_RATE.getName());
                request.setSortOrder(PostSortingOrder.DESC.getName());
            }
        }else if(StringUtils.isEmpty(request.getSortOrder())){
            if (isOverview) {
                request.setSortOrder(PostSortingOrder.ASC.getName());
            }else{
                request.setSortOrder(PostSortingOrder.DESC.getName());
            }
        }
    }

    private List<Object> convertLocationLeaderBoardResponse(TrendsLocationLeaderboardResponse response) {

        if (Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getDataPoints())) {
            return response.getDataPoints().stream()
                    .map(dataPoint -> SLAExcelResponse.SLAExcelLocationLeaderBoard.builder()
                            .msgReceived(dataPoint.getIncomingMsgCount())
                            .responded(dataPoint.getOutgoingMsgCount())
                            .responseRate(formatToTwoDecimalString(dataPoint.getResponseRate()))
                            .responseTime(formatTimeToUnits(dataPoint.getResponseTime()))
                            .location(dataPoint.getLabel())
                            .build())
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private List<Object> convertResponseOverviewExcel(TrendsOverviewResponseDTO response) {
        return response.getDataPoints().stream()
                .map(dataPoint -> SLAExcelResponse.SLAExcelResponseSummaryOverview.builder()
                        .pageName(dataPoint.getPageName())
                        .channel(dataPoint.getChannel())
                        .incomingMsg(dataPoint.getIncomingMsg().getContent())
                        .msgReceivedAt(DateTimeUtils.getDateByFormat(dataPoint.getIncomingMsg().getFeedDate()))
                        .commentedBy(dataPoint.getIncomingMsg().getUserName())
                        .response(dataPoint.getOutgoingMsg().getContent())
                        .respondSendAt(DateTimeUtils.getDateByFormat(dataPoint.getOutgoingMsg().getFeedDate()))
                        .respondedBy(dataPoint.getOutgoingMsg().getUserName())
                        .responseTime(formatTimeToUnits(dataPoint.getResponseTime()))
                        .locationName(dataPoint.getLocationName())
                        .messageType(dataPoint.getMessageType().equals("DM") ? "Direct message" : "Comment")
                        .incomingMsgImgUrls(convertListToString(dataPoint.getIncomingMsg().getImageUrls()))
                        .incomingMsgVidUrls(convertListToString(dataPoint.getIncomingMsg().getVideoUrls()))
                        .responseImgUrls(convertListToString(dataPoint.getOutgoingMsg().getImageUrls()))
                        .responseVidUrls(convertListToString(dataPoint.getOutgoingMsg().getVideoUrls()))
                        .build())
                .collect(Collectors.toList());
    }

    public String convertListToString(List<String> list) {
        if(CollectionUtils.isEmpty(list)) {
            return null;
        }
        return String.join(",", list);
    }

    private String formatTimeToUnits(Double value) {

        if (value == null) {
            return null;
        }
        long seconds = Math.round(value);
        Object[][] units = {
                {"d", 24L * 60 * 60},
                {"h", 60L * 60},
                {"m", 60L},
                {"s", 1L}
        };
        List<String> result = new ArrayList<>();

        for (Object[] unit : units) {
            String label = (String) unit[0];
            long unitValue = (long) unit[1];

            if (result.size() < 2 && seconds >= unitValue) {
                long amount = seconds / unitValue;
                seconds %= unitValue;
                result.add(amount + label);
            }
        }

        return result.isEmpty() ? "0s" : String.join(" ", result);
    }

    public void verifyStartDateAndEndDate(TrendsReportRequest request){
        if(Objects.isNull(request.getStartDate()) || Objects.isNull(request.getEndDate())){
            log.error("Start date and end date cannot be null");
            throw new BirdeyeSocialException("Start date and end date cannot be null");
        }
        if(CollectionUtils.isEmpty(request.getBusinessIds())){
            log.error("Business Ids cannot be null");
            throw new BirdeyeSocialException("Business Ids cannot be null");
        }
        String startDate = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getTrendsReportStartDate();
        try {
            Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(startDate);
            request.setTrendsReportStartDate(date);
        }catch (Exception e){
            request.setTrendsReportStartDate(Timestamp.valueOf("2025-02-03 00:00:00"));
            log.error("Error while parsing date: {}", startDate);
        }
    }

    @Override
    public List<String> getPageIdsFromBidsForGivenChannels(List<Integer> businessIds, List<String> channels) {
        List<String> pageIds = new ArrayList<>();
        for(String channel: channels){
            List<String> pageIdsList = findPageIdsUsingBusinessIds(businessIds, channel);
            if(CollectionUtils.isNotEmpty(pageIdsList)) {
                pageIds.addAll(pageIdsList);
            }
        }

        return pageIds;
    }

    @Override
    public TrendsOverviewResponseDTO getTrendsResponseOverview(TrendsReportRequest request) {
        log.info("Generating response overview report for request: {}", request);
        verifyStartDateAndEndDate(request);
        updateTrendsRequest(request);

        List<String> pageIds = getPageIdsFromBidsForGivenChannels(request.getBusinessIds(), request.getSocialChannels());
        if(CollectionUtils.isEmpty(pageIds)){
            return new TrendsOverviewResponseDTO();
        }
        SearchResponse searchResponse = trendsEsService.getIncomingMessagesListFromEs(pageIds, request);
        if(Objects.isNull(searchResponse) || searchResponse.status().getStatus() != 200){
            log.error("Error while fetching data for trends response overview report");
            throw new BirdeyeSocialException("Error while fetching response overview data for trends report");
        }
        return trendsEsService.getResponseOverviewResponse(pageIds, request, searchResponse);
    }

    public List<String> findPageIdsUsingBusinessIds(List<Integer> businessIds, String channel){
        log.info("businessIds: {}, channel {}", businessIds, channel);
        String ch = SocialChannel.getSocialChannelByName(channel).getName();
        SocialInsights execute = socialInsights
                .getSocialInsightsChannel(ch)
                .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
        return execute.getPageIdsFromBusinessIds(businessIds);
    }

    public List<Object> convertResponseTimeUserData(SocialTrendsReportResponse summaryResponse) {

        List<Object> slaExcelResponse = new ArrayList<>();

        if(Objects.isNull(summaryResponse) || CollectionUtils.isEmpty(summaryResponse.getDataPoints())) {
            return slaExcelResponse;
        }
        for(ReportTrendsDataPoint dataPoint : summaryResponse.getDataPoints()) {
            SLAExcelResponse.SLAExcelResponseByUser s =  SLAExcelResponse.SLAExcelResponseByUser.builder()
                    .user(dataPoint.getLabel())
                    .responded(dataPoint.getMessagesReplied())
                    .responseTime(formatTimeToUnits(dataPoint.getResponseTime()))
                    .build();
            slaExcelResponse.add(s);
        }

        return slaExcelResponse;
    }

    @Override
    public TrendsLocationLeaderboardResponse getLocationLeaderboardData(TrendsReportRequest request) {

        log.info("Request received for LocationLeaderboard report :{}",request);
        verifyStartDateAndEndDate(request);
        if(CollectionUtils.isEmpty(request.getSocialChannels()))
        {
            request.setSocialChannels(getAllSocialChannels());
        }
        TrendsLocationReportSortOption sortingCriteria = TrendsLocationReportSortOption.sortingCriteria(request.getSortParam());
        SortOrder sortingOrder = SortOrder.valueOf(request.getSortOrder().toUpperCase());
        if(sortingCriteria == null || sortingOrder == null) {
            throw new BadRequestException("Invalid sorting attributes");
        }
        List<String> pageIds = getPageIdsFromBidsForGivenChannels(request.getBusinessIds(), request.getSocialChannels());

        if(CollectionUtils.isEmpty(pageIds)){
            return new TrendsLocationLeaderboardResponse();
        }
        SearchResponse searchResponse = trendsEsService.getLeaderboardSummaryEsResponse(pageIds, request,sortingCriteria,sortingOrder);
        if(Objects.isNull(searchResponse) || Objects.isNull(searchResponse.getAggregations()) || searchResponse.status().getStatus() != 200){
            log.error("Error while fetching data for trends location leaderboard report.");
            throw new BirdeyeSocialException("Error while fetching location leaderboard for trends report");
        }
        return trendsEsService.convertLeaderboardEsSummaryResponse(searchResponse);
    }

    public List<Object> convertSummarySLAResponse(TrendsReportSummaryResponse summaryResponse) {
        SLAExcelResponse.SLAExcelSummaryResponse response =  SLAExcelResponse.SLAExcelSummaryResponse.builder()
                .msgReceived(summaryResponse.getIncomingMsgCount())
                .responded(summaryResponse.getOutgoingMsgCount())
                .responseRate(formatToTwoDecimalString(summaryResponse.getResponseRate()))
                .avgResponseTime(formatTimeToUnits(summaryResponse.getResponseTime()))
                .unresponded(summaryResponse.getUnRespondedMsgCount())
                .build();
        return Collections.singletonList(response);
    }

    public static String formatToTwoDecimalString(Double value) {

        if(value==null)
            return null;
        double doubleValue=value;
        if (value == (int) doubleValue) {
            return String.valueOf((int) doubleValue).concat("%");
        } else {
            return String.format("%.2f", doubleValue).concat("%");
        }
    }



}
