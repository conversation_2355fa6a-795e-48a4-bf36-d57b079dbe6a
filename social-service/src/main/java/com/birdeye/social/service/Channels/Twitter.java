package com.birdeye.social.service.Channels;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.SocialTwitterAccountRepository;
import com.birdeye.social.dto.PicturesqueMediaCallback;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessTwitterAccounts;
import com.birdeye.social.entities.ProcessingPost;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.entities.mediaupload.SocialAssetChunkInfo;
import com.birdeye.social.entities.mediaupload.SocialMediaUploadInfo;
import com.birdeye.social.entities.mediaupload.SocialMediaUploadRequest;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.request.mediaupload.MediaUploadRequest;
import com.birdeye.social.external.service.SocialRawPageDetail;
import com.birdeye.social.facebook.NewFbPostData;
import com.birdeye.social.insights.ES.LocationReportRequest;
import com.birdeye.social.insights.ES.PageInsightV2EsData;
import com.birdeye.social.insights.ES.ReportSortingCriteria;
import com.birdeye.social.insights.ES.SearchTemplate;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.model.*;
import com.birdeye.social.model.competitorProfile.CompetitorPageDetails;
import com.birdeye.social.model.competitorProfile.CompetitorPageDetailsDTO;
import com.birdeye.social.model.competitorProfile.CompetitorProfileReportESData;
import com.birdeye.social.service.SocialCompetitorService.SocialCompetitor;
import com.birdeye.social.service.SocialCompetitorService.Twitter.TwitterCompetitorService;
import com.birdeye.social.service.SocialMediaUploadService;
import com.birdeye.social.service.SocialPostOperationService.PostOperation;
import com.birdeye.social.service.SocialPostOperationService.Twitter.TwitterPostOperationService;
import com.birdeye.social.service.SocialPostTwitterService;
import com.birdeye.social.service.SocialReportService.SocialInsights;
import com.birdeye.social.service.SocialReportService.Twitter.TwitterInsights;
import com.birdeye.social.service.SocialReportService.Twitter.TwitterReportService;
import com.google.common.base.Functions;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.birdeye.social.service.EntityToDtoConverter.convertToSocialRawPageDetailResponse;

@Service
public class Twitter implements SocialInsights, SocialCompetitor, SocialMediaUploadService, PostOperation {

    @Autowired
    TwitterInsights twitterInsights;
    @Autowired
    TwitterReportService twitterReportService;

    @Autowired
    SocialTwitterAccountRepository twitterAccountRepository;

    @Autowired
    TwitterCompetitorService twitterCompetitorService;

    @Autowired
    TwitterPostOperationService postOperationService;

    @Autowired
    SocialPostTwitterService socialPostTwitterService;

    @Autowired
    SocialTwitterAccountRepository socialTwitterAccountRepository;

    private static final Logger log = LoggerFactory.getLogger(Twitter.class);

    @Override
    public String channelName() {
        return SocialChannel.TWITTER.getName();
    }

    @Override
    @Cacheable(value = "twitterPage", key = "#pageId+'_'+'twitter'", unless = "#result == null")
    public SocialRawPageDetail getPageDetails(String pageId) {
        List<BusinessTwitterAccounts> businessTwitterAccounts = socialTwitterAccountRepository.findByProfileId(Long.parseLong(pageId));
        if(CollectionUtils.isEmpty(businessTwitterAccounts)) {
            return null;
        }
        return convertToSocialRawPageDetailResponse(businessTwitterAccounts.get(0));
    }

    @Override
    public void registerMedia(Integer socialMediaUploadRequestId, SocialRawPageDetail socialRawPageDetail, MediaUploadRequest mediaInitiateRequest) throws Exception {
        socialPostTwitterService.registerMedia(socialMediaUploadRequestId, socialRawPageDetail, mediaInitiateRequest);
    }

    @Override
    public void uploadChunk(SocialAssetChunkInfo socialAssetChunkInfo, SocialMediaUploadInfo socialMediaUploadInfo,
                            SocialRawPageDetail socialRawPageDetail, SocialMediaUploadRequest socialMediaUploadRequest, boolean isV2request) throws Exception {
        socialPostTwitterService.uploadChunk(socialAssetChunkInfo, socialMediaUploadInfo, socialRawPageDetail, socialMediaUploadRequest, isV2request);
    }

    @Override
    public void uploadCaption(SocialRawPageDetail socialRawPageDetail, SocialMediaUploadRequest socialMediaUploadRequest) {

    }

    @Override
    public void finalizeVideoUpload(SocialRawPageDetail socialRawPageDetail, SocialMediaUploadRequest socialMediaUploadRequest, List<String> eTags) throws Exception {
        socialPostTwitterService.finalizeVideoUpload(socialMediaUploadRequest, socialRawPageDetail, null);
    }

    @Override
    public void postCaption(SocialMediaUploadRequest socialMediaUploadRequest, SocialRawPageDetail socialRawPageDetail) {

    }

    @Override
    public void checkStatus(SocialRawPageDetail socialRawPageDetail, MediaUploadRequest mediaUploadRequest) throws Exception {
        socialPostTwitterService.checkStatus(socialRawPageDetail, mediaUploadRequest);
    }

    @Override
    public void birdeyeExceptionHandler(BirdeyeSocialException bse, Integer publishInfoId, String pageId) {
        socialPostTwitterService.birdeyeExceptionHandler(bse,publishInfoId,pageId);
    }

    @Override
    public void generalExceptionHandler(String message, Integer publishInfoId) {
        socialPostTwitterService.generalExceptionHandler(message,publishInfoId);
    }

    @Override
    public void postContentWithMedia(MediaUploadRequest request, String pageId, SocialPostPublishInfo publishInfoId) throws Exception {
        socialPostTwitterService.postContentWithMedia(request, pageId, publishInfoId);
    }

    @Override
    @CacheEvict(value = "twitterPage", key = "#externalPageId+'_'+'twitter'")
    public void evictPageCache(String externalPageId) {
        log.info("Twitter Evict cache for page Id:{}",externalPageId);
    }

    @Override
    public Object getPageInsightsFromES(InsightsRequest insightsRequest) throws Exception {
        log.info("Request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        if(Objects.isNull(insightsRequest)){
            return null;
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);
        if(Objects.nonNull(searchTemplate)) {
            return twitterInsights.getTwitterInsightsForPage(insightsRequest);
        }
        return null;
    }

    @Override
    public Object getPageInsightsESData(InsightsRequest insightsRequest) throws Exception {
        log.info("getPageInsightsESData Request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        if(Objects.isNull(insightsRequest)){
            return null;
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);

        if (Objects.isNull(searchTemplate)) {
            return null;
        }

        if(SearchTemplate.PPR_PAGE_POST_MESSAGE_METRIC.equals(searchTemplate)){
            PageInsightV2EsData engageData = twitterInsights.getTwitterInsightsForMessageSent(insightsRequest);

            PageInsightV2EsData postData = twitterInsights.getTwitterInsightsForPublishPost(insightsRequest);
            if(Objects.nonNull(postData)) {
                engageData.setBuckets(postData.getBuckets());
                engageData.setCurrentData(postData.getCurrentData());
            }
            insightsRequest.setReportType(SearchTemplate.PPR_PAGE_POST_MESSAGE_METRIC.getName());
            return engageData;
        } else if (SearchTemplate.PPR_PAGE_POST_PUBLISHED_METRIC.equals(searchTemplate)) {
            return twitterInsights.getTwitterInsightsForPublishPost(insightsRequest);
        } else {
            return twitterInsights.getPageInsightsESData(insightsRequest);
        }
   }

    @Override
    public Object getPageInsightsFromESByLocation(InsightsRequest insightsRequest) throws Exception {
        return null;
    }

    @Override
    public void postPageInsightsToEs(PageInsights pageInsights) {
        if(Objects.isNull(pageInsights)){
            return;
        }
        twitterInsights.postTwitterPageInsightToES(pageInsights);
    }

    @Override
    public void getPostInsights(List<BusinessPosts> businessPosts, Boolean isFreshRequest) {
        if(CollectionUtils.isEmpty(businessPosts)){
            return;
        }
        twitterInsights.getPostInsights(businessPosts,isFreshRequest);
    }

    @Override
    public void getPageInsightsFromSocialChannel(SocialScanEventDTO socialScanEventDTO) {
        twitterReportService.getPageInsightsFromTwitter(socialScanEventDTO);
    }

    @Override
    public void postPostDataAndInsightsToEs(PostData postData) {
        twitterInsights.postTwitterPostInsightsToEs(postData);
    }

    @Override
    public Object getPostDataAndInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        return twitterInsights.getTwitterInsightsForPost(insightsRequest,startIndex,pageSize,sortParam,sortOrder, excelDownload);
    }

    @Override
    public void updateToPostAndPageIndexEs(NewFbPostData newFbPostData) {

    }

    @Override
    public void updatePageInsightsDb(String pageId,Integer businessId, Long enterpriseId) {

    }

    public void updatePageInsightsPostCount(PageInsights pageInsights) {

    }

    @Override
    public String getPageInsightIndexName() {
        return ElasticConstants.TWITTER_PAGE_INSIGHTS.getName();
    }

    @Override
    public void startScanForPosts(String pageId) {

    }

	@Override
	public void getGMBPageAnalytics(String pageId, Integer businessId) {
		// TODO Auto-generated method stub

	}

    @Override
    public void saveCDNPostToES(BusinessPosts businessPosts) {
        if(Objects.isNull(businessPosts)){
            return;
        }
        twitterInsights.saveCDNPostToES(businessPosts);
    }

    @Override
    public void getGMBKeywordAnalytics(Integer businessId) throws Exception {
        // TODO Auto-generated method stub

    }

    @Override
    public PerformanceSummaryResponse getPerformanceData(InsightsRequest insightsRequest) {
        return twitterInsights.getTwitterPerformanceData(insightsRequest);
    }

    @Override
    public boolean backfillProfilePagesToEs(PageInsightsRequest pageInsights) {
        return twitterInsights.backfillProfilePagesToEs(pageInsights);
    }
    public Object getPageReportESData(InsightsRequest insightsRequest) throws Exception {
        log.info("Request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        if(Objects.isNull(insightsRequest)){
            return null;
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);
        if (Objects.isNull(searchTemplate)) {
            return null;
        }

        if(SearchTemplate.REPORT_MESSAGE_VOLUME.equals(searchTemplate)){
            return twitterInsights.getMessageVolumeInsightsReportData(insightsRequest);
        } else {
            return twitterInsights.getTwitterInsightsReportData(insightsRequest);
        }
    }

    @Override
    public Map<String, Integer> getBusinessIdPageIdMapping(List<Integer> businessIds) {
        List<BusinessTwitterAccounts> twitterAccounts = twitterAccountRepository.findAllByBusinessIdIn(businessIds);
        if(CollectionUtils.isNotEmpty(twitterAccounts)){
            Map<String, Integer> businessIdPageIdMap = new HashMap<>();
            twitterAccounts.forEach(account -> {
                businessIdPageIdMap.put(String.valueOf(account.getProfileId()), account.getBusinessId());
            });
            return businessIdPageIdMap;
        }
        return null;
    }

    @Override
    public void fetchCompetitorPosts(CompetitorRequestDTO competitorRequestDTO) throws SocialBirdeyeException{
        if(Objects.nonNull(competitorRequestDTO.getPageId())) {
            twitterCompetitorService.fetchCompetitorPosts(competitorRequestDTO);
        } else {
            log.info("[Twitter competitor posts] pageId cannot be null in request, exiting the flow!!");
        }
    }

    @Override
    public void fetchCompetitorAccounts(List<String> accountIdentifier, Long enterpriseId) throws SocialBirdeyeException {
        if(CollectionUtils.isNotEmpty(accountIdentifier)) {
            twitterCompetitorService.fetchCompetitorAccounts(accountIdentifier, enterpriseId);
        } else {
            log.info("Empty list received in input");
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, ErrorCodes.INVALID_REQUEST.name());
        }
    }

    @Override
    public void updateCompCache(SocialChannel channel, Long businessNumber) {
        twitterCompetitorService.updateCompCache(channel, businessNumber);
    }

    @Override
    public CompetitorListResponse getCompetitorList(Long businessNumber) {
        return twitterCompetitorService.getCompetitorList(businessNumber);
    }

    @Override
    public void deleteCompetitor(DeleteCompetitorDetailRequest deleteRequest, Long enterpriseId) throws SocialBirdeyeException {
        twitterCompetitorService.deleteCompetitor(deleteRequest, enterpriseId);
    }
    @Override
    public String getPageIdOnCompId(Integer rawCompId) {
        return twitterCompetitorService.getPageIdOnCompId(rawCompId);
    }

    @Override
    public void scanPages() {
        twitterCompetitorService.scanPages();
    }

    @Override
    public List<CompetitorBasicDetail> getCompetitorsBasicDetails(List<String> pageIds) {
        return twitterCompetitorService.getCompetitorsBasicDetails(pageIds);
    }

    @Override
    public void updateProfilePictureUrl(PicturesqueMediaCallback picturesqueMediaCallback, Integer rawCompId) {
        twitterCompetitorService.updateProfilePictureUrl(picturesqueMediaCallback, rawCompId);
    }

    @Override
    public void callPicturesQueForPage(PicturesqueCompRequest request) {
        twitterCompetitorService.callPicturesQueForPage(request);
    }

    @Override
    public CompetitorPageDetailResponse getPageSummary(String pageId, Long businessNumber) {
        return twitterCompetitorService.getPageSummary(pageId, businessNumber);
    }

    @Override
    public void proceedToUnmapPage(Long businessNumber) {
        twitterCompetitorService.proceedToUnmapPage(businessNumber);
    }

    @Override
    public Optional<Integer> fetchCompetitorProfileFollowerCount(CompetitorRequestDTO competitorRequestDTO) throws SocialBirdeyeException {
        return twitterCompetitorService.fetchCompetitorProfileFollowerCount(competitorRequestDTO);
    }

    @Override
    public CompetitorProfileReportESData getCompetitorProfileDataES(SearchRequest searchRequest, String channel) {
        return null;
    }

    @Override
    public Map<String, CompetitorPageDetails> getPageIdVsPageName(List<String> pageIds) throws SocialBirdeyeException {
        return twitterCompetitorService.getPageNameByPageId(pageIds);
    }

    @Override
    public Map<String, CompetitorPageDetails> getLocationIdVsPageId(List<String> pageIds) throws SocialBirdeyeException {
        Map<String, CompetitorPageDetails> locationIdVsPageId = new HashMap<>();
        try {
            List<Long> twitterProfileIds = new ArrayList<>();
            pageIds.forEach(pageId -> twitterProfileIds.add(Long.parseLong(pageId)));
            List<BusinessTwitterAccounts> businessTwitterAccountsList = twitterAccountRepository.findByProfileIdIn(twitterProfileIds);

            businessTwitterAccountsList.forEach(businessTwitterAccounts -> locationIdVsPageId.put(businessTwitterAccounts.getProfileId().toString(),
                    CompetitorPageDetails.builder()
                            .profilePictureUrl(businessTwitterAccounts.getProfilePicUrl())
                            .pageName(businessTwitterAccounts.getName())
                            .pageLink(businessTwitterAccounts.getProfileUrl())
                            .channel(SocialChannel.TWITTER.getName())
                            .pageId(businessTwitterAccounts.getProfileId().toString())
                            .build()));
        }catch (Exception e){
            log.error("Error in fetching locationIdVsPageId for twitter pages {}", e.getMessage());
        }
        return locationIdVsPageId;
    }

    @Override
    public List<CompetitorPageDetailsDTO> getPageDetails(List<String> pageIds) {
        return twitterCompetitorService.getPageDetails(pageIds);
    }

    @Override
    public List<CompetitorPageDetailsDTO> getSelfPageDetails(List<String> pageIds) {
        List<CompetitorPageDetailsDTO> response = new ArrayList<>();

        List<Long> twitterProfileIds = new ArrayList<>();
        pageIds.forEach(pageId -> twitterProfileIds.add(Long.parseLong(pageId)));

        List<BusinessTwitterAccounts> twitterAccounts = twitterAccountRepository.findByProfileIdIn(twitterProfileIds);
        if(CollectionUtils.isEmpty(twitterAccounts)) {
            return new ArrayList<>();
        }

        for(BusinessTwitterAccounts twitterAccount : twitterAccounts) {
            CompetitorPageDetailsDTO data =  CompetitorPageDetailsDTO.builder()
                    .pageName(twitterAccount.getName())
                    .pageId(Long.toString(twitterAccount.getProfileId()))
                    .pageProfileUrl(twitterAccount.getProfilePicUrl())
                    .build();
            response.add(data);
        }
        return response;
    }


    @Override
    public void editPublishedPost(SocialPostPublishInfo publishInfo) {

    }

    @Override
    public void deletePost(SocialPostPublishInfo publishedPost) throws Exception {
        postOperationService.deletePost(publishedPost);
    }

    @Override
    public void processPendingPosts(ProcessingPost processingPost, PageInfoDto pageInfoDto, SocialPostPublishInfo publishInfo) {

    }

    @Override
    public Map<String, PageInfoDto> getPageInfoDto(List<String> pageIds) {
        return Collections.emptyMap();
    }

    @Override
    public Integer getCompetitorAccounts(Long enterpriseId) throws SocialBirdeyeException {
        return twitterCompetitorService.getCompetitorAccounts(enterpriseId);

    }

    public PostData createPostData(BusinessPosts businessPosts) throws Exception {
        return twitterInsights.createPostData(Collections.singletonList(businessPosts), new HashMap<>(), new HashMap<>(), new HashMap<>()).get(0);
    }

    @Override
    public void backfillEngagementBreakdown(BackfillInsightReq backfillInsightReq) {

    }

    @Override
    public void backfillEngagementBreakdownPageWise(BackfillRequest backfillRequest) {

    }

    public List<String> getPageIds(List<Integer> businessIds) {
        return Lists.transform(twitterAccountRepository.findProfileIdsByBusinessIdIn(businessIds),
                Functions.toStringFunction());
    }

    @Override
    public LeadershipByPostsDataPoints getPostLeadershipReport(LocationReportRequest insightsRequest,
                                                               ReportSortingCriteria postSortingCriteria, SortOrder order,
                                                               Integer startIndex, Integer pageSize) {
        return twitterInsights.getPostLeadershipReport(insightsRequest, postSortingCriteria, order, startIndex, pageSize);
    }

    @Override
    public List<String> getPageIdsFromBusinessIds(List<Integer> businessIds) {
        return twitterAccountRepository.findProfileIdsByBusinessIdIn(businessIds).stream().map(Functions.toStringFunction()::apply).collect(Collectors.toList());
    }

    @Override
    public Object getTopPostsInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        return twitterInsights.getTwitterInsightsForTopPost(insightsRequest,startIndex,pageSize,sortParam,sortOrder, excelDownload);
    }

    @Override
    public List<String> getAllPageIds(int page, int size, Long enterpriseId) {

        List<Long> profileIds = Objects.nonNull(enterpriseId)? twitterAccountRepository.findProfileIdsByEnterpriseId(enterpriseId):
                twitterAccountRepository.findProfileIdsWithPagination(new PageRequest(page,size));

        return profileIds.stream()
                .map(String::valueOf)
                .collect(Collectors.toList());
    }
}
