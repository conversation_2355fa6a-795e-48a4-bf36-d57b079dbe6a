package com.birdeye.social.service;

import com.birdeye.social.constant.GMBLocationJobStatus;
import com.birdeye.social.dto.BusinessGMBNotificationDTO;
import com.birdeye.social.dto.GMBAccountDTO;
import com.birdeye.social.dto.GoogleMyBusinessPagesDTO;
import com.birdeye.social.dto.SocialPageDTO;
import com.birdeye.social.entities.BusinessGetPageOpenUrlRequest;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import com.birdeye.social.model.FilterPageRequest;
import com.birdeye.social.model.PaginatedGMBResponse;
import com.birdeye.social.model.Validity;
import com.birdeye.social.sro.GoogleAuthToken;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface GoogleMyBusinessPageService 
{

	Map<String, BusinessGoogleMyBusinessLocation> getExistingGMBPages(List<String> locationIds);

	List<BusinessGoogleMyBusinessLocation> connectedGmbPagesForAnEnterprise(Long enterpriseId);

	Page<BusinessGoogleMyBusinessLocation> connectedGmbPagesForAnReseller(Long resellerId,Integer isSelected, Pageable page);

	void saveNewGoogleMyBusinessPages(List<GoogleMyBusinessPagesDTO> myBusinessPages);

	List<BusinessGoogleMyBusinessLocation> getGMBPagesByLocationIds(Collection<String> locationIds);

	void updateGMBPagesByLocationIds(List<String> locationIds, Long enterpriseId, Integer accountId);

	void updateGMBPagesByLocationIdsForReseller(List<String> locationIds, Long enterpriseId);

	List<BusinessGoogleMyBusinessLocation> getGMBPagesByEnterpriseAndLocationIds(List<String> locationIds, Long enterpriseId);
	
	void deleteGMBLocation(List<String> locationIds);

	List<BusinessGoogleMyBusinessLocation> findByEnterpriseIdAndIsSelected(Long enterpriseId, int isSelected);

	Map<String, BusinessGoogleMyBusinessLocation> getGMBPagesMap(Long enterpriseId, int isSelected);

	int updateGMBPagesForRefreshToken(List<Integer> refreshTokenIds);

	List<BusinessGoogleMyBusinessLocation> getGMBPagesByLocationId(List<String> locationIds);

	List<BusinessGoogleMyBusinessLocation> getGMBPagesByLocationIdWithLimit(List<String> locationIds, Pageable page);

	void saveGMBRowPage(BusinessGoogleMyBusinessLocation businessPage);

	void updateGMBLocationIsValidStatus(String locationId, Integer isValid);

	void saveOrUpdateGMBRowPage(BusinessGoogleMyBusinessLocation businessLocation);

	void saveOrUpdateGMBRowPage(List<BusinessGoogleMyBusinessLocation> businessLocation);

	void fetchGmbLocations(BusinessGetPageRequest request, GoogleAuthToken token);
	
	void fetchGmbLocations(BusinessGetPageOpenUrlRequest request, GoogleAuthToken token);

	List<BusinessGoogleMyBusinessLocation> getGMBPagesByRequestId(String requestId);

	List<BusinessGoogleMyBusinessLocation> getGMBPagesByRequestIdAndGmbAccountId(String requestId,Integer accountId);

	List<BusinessGoogleMyBusinessLocation> getGMBPagesByRequestIdAndAccountId(String requestId, String accountId);

	PaginatedGMBResponse getGMBResellerPagesByRequestIdAndGmbAccountId(String requestId, Integer accountId, PageRequest pageRequest);

	@Deprecated
	Map<String, GoogleMyBusinessPagesDTO> getGoogleMyBusinessPagesDTO(GoogleAuthToken gmbAuth, Integer requestId);

	void submitConnectFetchPageRequest(GoogleAuthToken gmbAuth, BusinessGetPageRequest requestId);

	List<BusinessGMBNotificationDTO> findDistinctAccountIdsByEnterpriseId(Long businessId,int isValid);

	List<BusinessGMBNotificationDTO> getAllDistinctAccountIds();

	Boolean isAccountIdAvailableonOtherEnterprise(String accountId,Long businessId);

	List<Integer> fetchRefreshTokens(int count);
	
    Map<String, GoogleMyBusinessPagesDTO> processAllGMBLocationsForAccount(GMBAccountDTO gmbAccount, GoogleAuthToken gmbAuth) ;

	void updateSyncStatusForLocation(Integer refreshTokenId, String locationId, GMBLocationJobStatus locationCheckStatus);

	public void updateGMBLocationBatches(Integer refreshTokenId, List<String> locationIds, GMBLocationJobStatus gmbLocationJobStatus);

	void updateGMBAccountLocationBatches(Integer refreshTokenId, String accountId,
			GMBLocationJobStatus gmbLocationJobStatus);

	List<BusinessGoogleMyBusinessLocation> getGMBPagesByRefreshTokenId(Integer refreshToken);

	void postFetchPageProcess(Map<String, GoogleMyBusinessPagesDTO> pagesMap,String businessGetPageRequestId,String accountId);

	void saveToRedisByBusinessGetPageRequestId(List<GMBAccountDTO> gmbAccountList, String requestId);
	
	void postFetchPageProcessForOpenUrl(Map<String, GoogleMyBusinessPagesDTO> pagesMap,String businessGetPageRequestId,String accountId);

	void postFetchPageProcessForOpenUrlV1(Map<String, GoogleMyBusinessPagesDTO> pagesMap,String businessGetPageRequestId,String accountId);

	public boolean isPageValid(BusinessGoogleMyBusinessLocation gmbPage);

	void updateMessagingEnabledForEnterpriseId(Long enterpriseId, Integer isMessengerEnabled);

	List<BusinessGoogleMyBusinessLocation> findLastUpdatedWithDistinctRefreshTokenId(Long enterpriseId, Integer limit);

	List<BusinessGoogleMyBusinessLocation> findLastUpdatedWithDistinctRefreshTokenIdWithAgentId(Integer agentId, Integer limit);

	List<BusinessGoogleMyBusinessLocation> findByEnterpriseIdAndIsValidAndIsSelected(Long enterpriseId);
    
	BusinessGMBNotificationDTO getGMBPagesByLocationId(String locationId);

	Boolean isAccountIdUsedForOtherLocation(String accountId, String locationId, int isValid);

    void fetchGmbAccounts(BusinessGetPageRequest businessGetPageRequest, GoogleAuthToken googleAuthToken,String type,Long parentAccountId);

	void fetchGmbAccountsForOpenUrl(BusinessGetPageOpenUrlRequest businessGetPageOpenUrlRequest, GoogleAuthToken googleAuthToken,String type,Long parentAccountId, String firebaseKey);

	List<GMBAccountDTO> fetchGmbAccountForFreemium(BusinessGetPageRequest businessGetPageRequest, GoogleAuthToken googleAuthToken);

	BusinessGoogleMyBusinessLocation findFirstByLocationId(String pageId);

    BusinessGoogleMyBusinessLocation findByBusinessId(Integer businessId);

	List<BusinessGoogleMyBusinessLocation> findByAgentIds(Collection<Integer> agentId);

	List<BusinessGoogleMyBusinessLocation> findByAgentId(Integer agentID);

	void updateAgentIds(Integer agentId);

	void updateAgentIdsByBusinessIds(Integer agentId,List<Integer> businessIds);

	List<BusinessGoogleMyBusinessLocation> findByBusinessIds(Collection<Integer> businessId, Integer isValid, Integer isSelected);

	List<BusinessGoogleMyBusinessLocation> findByBusinessIdIn(List<Integer> businessIds);

	PaginatedGMBResponse findByRequestId(String requestId, PageRequest pageRequest);

	void postFetchPageProcessForReseller(Map<String, GoogleMyBusinessPagesDTO> data, String businessGetPageRequestId, String accountId);

	List<BusinessGoogleMyBusinessLocation> getGMBPagesByResellerAndLocationIds(List<String> pageIds, Long enterpriseId);

	List<Long> getEnterpriseIdByResellerId(Long parentId);

	Set<Integer> getDistinctAgentIdByResellerId(Long parentId);

	List<String> getGMBLocationIdsByRequestId(Integer id);

	public List<BusinessGoogleMyBusinessLocation> getPagesByResellerId(Long resellerId);

	PaginatedGMBResponse getGMBConnectPages(String toString, Long resellerId, PageRequest pageRequest);

	SocialPageDTO getGMBPageDTO(FilterPageRequest filterPage);

	void clearGMBPageByFilter(FilterPageRequest filterPage);

	void pushToKafkaForValidity(String channel, String locationIds);

	Page<BusinessGoogleMyBusinessLocation> getDisconnectedPages(Long resellerId,List<Integer> validType,Pageable pageable,Integer isSelected);

	PaginatedGMBResponse findByRequestIdAndSearchStr(String toString, PageRequest pageRequest, String search);

	List<String> getGMBLocationIdsByLocationName(String searchStr, Integer requestId);

	void getVoiceOfMerchant(String accessToken, BusinessGoogleMyBusinessLocation page,boolean isViaNotification);

	List<BusinessGoogleMyBusinessLocation> findByEnterpriseIdAndIsValidAndIsSelectedAndAgentIdAndBusinessIdIsNotNull(Long enterpriseId,Integer agentId);

	List<BusinessGoogleMyBusinessLocation> findByEnterpriseIdAndIsValidAndIsSelectedAndBusinessIdIsNotNull(Long enterpriseId);

	int addAgentIdToLocations(Integer agentId, List<Integer> businessIds);

	Set<Integer> getDistinctAgentIdByEnterpriseId(Long parentId);

	void updateGoogleMyBusiness(Integer id, Long enterpriseId);

	List<BusinessGoogleMyBusinessLocation> findByAgentIdAndIsValidAndIsSelected(Integer agentId, int i, int i1);

	List<BusinessGoogleMyBusinessLocation> findByBusinessIds(Integer accountId, Collection<Integer> businessIds,
			String gmsgLocationStatus);
	List<BusinessGoogleMyBusinessLocation> getGMBPagesByLocationIdsAndIsSelected(Collection<String> locationIds, Integer isSelected);

}
