package com.birdeye.social.service;
/*
 * <AUTHOR> yadav
 */

import com.birdeye.social.bam.BAMAggregationService;
import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.businessgetpage.IBusinessGetPageService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.*;
import com.birdeye.social.dto.*;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.elasticdto.SocialElasticDto;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.exception.ExternalAPIException;
import com.birdeye.social.external.request.business.BusinessLiteRequest;
import com.birdeye.social.external.service.FbMessengerExternalService;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.facebook.*;
import com.birdeye.social.facebook.FacebookPost.FacebookMentionsData;
import com.birdeye.social.facebook.FacebookPost.FacebookPageSearch;
import com.birdeye.social.facebook.response.FacebookBaseResponse;
import com.birdeye.social.instagram.InstagramExternalService;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.competitorProfile.CompetitorPageDetails;
import com.birdeye.social.model.competitorProfile.CompetitorPageDetailsDTO;
import com.birdeye.social.model.engageV2.EngageWebhookSubscriptionRequest;
import com.birdeye.social.model.instagram.TokenUpdateRequest;
import com.birdeye.social.model.notification.MessageTypeEnum;
import com.birdeye.social.model.notification.SocialNotificationAudit;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.platform.dao.BusinessFacebookPageRepository;
import com.birdeye.social.platform.dao.BusinessRepository;
import com.birdeye.social.platform.dao.BusinessUserRepository;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.platform.entities.BusinessAggregation;
import com.birdeye.social.platform.entities.BusinessFacebookPageNew;
import com.birdeye.social.service.resellerservice.ResellerHelperService;
import com.birdeye.social.specification.FBSpecification;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.text.StrBuilder;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.http.client.utils.URIBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.domain.Specifications;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.birdeye.social.cache.SystemPropertiesCache.FB_AUTH_SCOPES;
import static com.birdeye.social.cache.SystemPropertiesCache.FB_REDIRECT_DOMAIN;
import static com.birdeye.social.constant.Constants.*;
import static java.util.Comparator.nullsFirst;
import static java.util.stream.Collectors.groupingBy;


@Service("fbSocialAccountService")
public class FacebookSocialAccountServiceImpl extends SocialAccountSetupCommonService implements FacebookSocialAccountService{

	private static final String PAGES_MESSAGING = "pages_messaging";

	private static final String READ_INSIGHTS = "read_insights";

	private static final String MESSENGER = "messenger/";

	private static final String AUTOMAPPING = "automapping/";

	private static final String REVOKED_PERMISSION = "revokedPermission";

	private static final String USER_BLOCKED = "userBlocked";

	private static final String TOKEN_EXPIRED = "tokenExpired";

	private static final String INSUFFICIENT_PERMISSION = "insufficientPermission";

	private static final String PSWD_CHANGED = "passwordChanged";

	private static final String INTEGRATION = "integration";

	private static final String CONNECT = "connect";

	private static final String RECONNECT = "reconnect";

	private static final String EXTENDED_TOKEN = "Extended token {}:";

	private static final String FOR_BUSINESS_AND_EXTENDED_TOKEN_USER_PROFILE_ID = "For business {}, and extended-token {} user profile Id {} ";

	private static final String SOCIAL_SETUP_GETTING_USERS_FOR_BUSINESS_EXTENDED_TOKEN = "[Social Setup] Getting users for business: {}, extended token: {}";

	private static final String SOCIAL_RECONNECT_GETTING_USERS_FOR_BUSINESS_EXTENDED_TOKEN = "[Social Reconenct] Getting users for business: {}, extended token: {}";

	private static final String FOR_BUSINESS_AND_EXTENDED_TOKEN_USER_PROFILE_ID_EMAIL_ID = "For business {}, and extended-token {} user profile Id {} emailId {}";

	private static final String FOR_RESELLER_AND_EXTENDED_TOKEN_USER_PROFILE_ID_EMAIL_ID = "For reseller {}, and extended-token {} user profile Id {} emailId {}";

	private static final String MESSENGER_NOT_ENABLED = "messengerNotEnabled";

	private static final String	TOPIC_INIT	= "auto-mapping-init";

	private static final String	FACEBOOK_PAGE_MATCH_RULE_TYPE = "FACEBOOK-PAGE-MATCH";

	private static final String	TOPIC_MATCHED	= "auto-mapping-matched";

	private static final String AUHTORIZATION_URI = "https://www.facebook.com/v21.0/dialog/oauth";

	public final String REDIRECT_URI_GENERIC = "https://%s/dashboard/social/callback?active=facebook";

	@Autowired
	private SocialFBPageRepository			socialFbRepo;

	@Autowired
	private SocialPostFacebookService		socialFacebookService;

	@Autowired
	private IBusinessCachedService businessService;

	@Autowired
	private BusinessUtilsService			businessUtilService;

	@Autowired
	private BusinessFacebookPageRepository	businessFbPageRepo;

	@Autowired
	private IRoleMappingService roleMappingService;

	@Autowired
	private IPermissionMappingService permissionMappingService;

	@Autowired
	private KafkaProducerService	producer;

	@Autowired
	private AutoMappingService autoMappingService;

	@Autowired
	private IBusinessGetPageService businessGetPageService;

	@Autowired
	private BusinessRepository				businessRepo;

	@Autowired
	private CommonService					commonService;

	@Autowired
	private AutoMappingRepo autoMappingRepo;

	@Autowired
	private InstagramExternalService instagramExtService;

	@Autowired
	private FacebookService					fbService;

	@Autowired
	private FacebookPageService				fbPageService;
	@Autowired
	private BusinessUserRepository			businessUserRepository;

	@Autowired
	private BusinessGetPageReqRepo			businessGetPageReqRepo;

	@Autowired
	private IRedisLockService				redisService;

	@Autowired
	private IRedisExternalService redisExternalService;

	@Autowired
	private SocialEngagementService	socialEngageService;

	@Autowired
	private FacebookSocialService fbSocialService;

	@Autowired
	private SocialPagesAuditRepo socialPagesAuditRepo;

	private static Logger			logger	= LoggerFactory.getLogger(FacebookSocialAccountServiceImpl.class);

	@Autowired
	private BAMAggregationService	bamAggregationService;

	@Autowired
	private IBusinessCoreService businessCoreService;

	@Autowired
	private NexusService nexusService;

	@Autowired
	private InvalidSocialIntegrationRepo socialInvalidRepo;

	@Autowired
	private KafkaProducerService	kafkaProducer;

	@Autowired
	private ISocialAppService socialAppService;

	@Autowired
	private FbMessengerExternalService fbMsgService;

	@Autowired
	private BusinessGetPageOpenUrlReqRepo		businessGetPageOpenUrlReqRepo;

	@Autowired
	private SocialSetupAuditRepository setupAuditRepo;

	@Autowired
	private IBrokenIntegrationService brokenIntegrationService;

	@Autowired
	private FBSpecification fbSpecification;

	@Autowired
	private SocialErrorMessagePageService socialErrorMessageService;

	@Autowired
	private BusinessInstagramAccountRepository instagramAccountRepository;

	@Autowired
	private ISocialModulePermissionService socialModulePermissionService;

	@Autowired
	private ResellerHelperService resellerHelperService;

	@Autowired
	private BusinessService coreBusinessService;

	@Autowired
	private IBusinessCoreService iBusinessCoreService;

	@Override
	public void saveMapping(BusinessFBPage fbPage,Integer locationId, Integer userId) {
		BusinessFacebookPageNew fbPageNew = getFbPageObject(fbPage);
		if(fbPageNew!=null) {
			fbPageNew.setBusinessId(locationId);
			fbPageNew.setEnabled(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getSocialAutoPostFlag());
			fbPageNew.setCreatedBy(userId);
			fbPageNew.setUpdatedBy(userId);
			businessFbPageRepo.saveAndFlush(fbPageNew);
		}
	}

	@Override
	public void saveLocationPageMapping(Integer locationId, String fbPageId, Integer userId, String type, Long resellerId)
			throws Exception {
		logger.info("FB page Id {} mapping with location Id {}", fbPageId, locationId);

		List<BusinessFBPage> tempFbPage = socialFbRepo.findByFacebookPageId(fbPageId);
		if (CollectionUtils.isEmpty(tempFbPage)) {
			logger.error("For Fb page id {} no data found ", fbPageId);
			throw new BirdeyeSocialException(ErrorCodes.FB_PAGE_NOT_FOUND, "facebook page data not found");
		}
		if(socialFbRepo.existsByBusinessId(locationId)){
			throw new BirdeyeSocialException(ErrorCodes.MAPPING_ALREADY_EXISTS,MAPPING_ALREADY_EXIST_ERROR);
		}
		BusinessFBPage fbPage = tempFbPage.get(0);
		if(Objects.nonNull(fbPage) && Objects.nonNull(resellerId)){
			commonService.checkRequestFromAuthorizedSourceUsingLongResellerID(fbPage.getResellerId(), resellerId);
		}
		fbPage.setBusinessId(locationId);
		fbPage.setEnabled(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getSocialAutoPostFlag());
		//Adding accountId and enterpriseId to the FB page when the page is mapped from the reseller dashboard.
		updateFbPageForReseller(fbPage, type, locationId);
		socialFbRepo.save(tempFbPage);
		fbPageService.performPostMappingAction(fbPage);
		kafkaProducer.sendObject(Constants.FB_PAGE_MAPPING_ADDED, new LocationPageMappingRequest(locationId, fbPageId));
		commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.ADD_MAPPING.name(), Arrays.asList(fbPage),
				userId.toString(), locationId,
				Constants.ENTERPRISE.equals(type) ? fbPage.getEnterpriseId() : fbPage.getResellerId());
	}
	@Async
	void subscribeFbNotificationWebhook(String pageId) {
		logger.info("Request received for fb engage subcrioption for pageId {}", pageId);

		EngageWebhookSubscriptionRequest payload = new EngageWebhookSubscriptionRequest();
		payload.setSubscription(true);
		payload.setChannel(SocialChannel.FACEBOOK.getName());
		payload.setPageId(pageId);


		// subscribe to engage webhook
		kafkaProducer.sendObject(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_WEBHOOK_SUBSCRIPTION_TOPIC.getName(), payload);
	}




	@Override
	public boolean isPageBusinessPlaceIdSame(Integer locationId, String pageId, Integer userId) throws Exception {
		List<BusinessFBPage> fbPages = socialFbRepo.findByFacebookPageId(pageId);
		if ( CollectionUtils.isEmpty(fbPages) ) {
			throw new BirdeyeSocialException(ErrorCodes.FB_PAGE_NOT_FOUND, "FB Page with provided pageId not found");
		}
		// In case of FB, locationId (or business_id) will be mapped to 1 or more profile_id in business_aggregation table
		Map<String,List<String>> businessProfileIdsMap = bamAggregationService.getBusinessPlaceIds(SocialChannel.FACEBOOK.getName(), Collections.singletonList(locationId));
		if ( businessProfileIdsMap != null ) {
			logger.info("Checking if facebookPageId {} exists in map {}", fbPages.get(0).getFacebookPageId(), businessProfileIdsMap);
			return businessProfileIdsMap.getOrDefault(String.valueOf(locationId), new ArrayList<>()).contains(fbPages.get(0).getFacebookPageId());
		}
		return false;
	}

	private BusinessFacebookPageNew getFbPageObject(BusinessFBPage fbPageInput) {
		BusinessFacebookPageNew fbPage = new BusinessFacebookPageNew();
		fbPage.setAccessToken(fbPageInput.getPageAccessToken());
		fbPage.setFacebookPageId(fbPageInput.getFacebookPageId());
		fbPage.setFacebookPageName(fbPageInput.getFacebookPageName());
		fbPage.setFacebookUserId(fbPageInput.getUserId());
		fbPage.setLink(fbPageInput.getLink());
		fbPage.setIsValid(fbPageInput.getIsValid());
		fbPage.setSingleLineAddress(fbPageInput.getSingleLineAddress());
		fbPage.setProfilePictureUrl(fbPageInput.getFacebookPagePictureUrl());
		fbPage.setMessengerOpted(fbPageInput.getMessengerOpted());
		fbPage.setRatingsOpted(fbPageInput.getRatingsOpted());
		return fbPage;
	}

	@Override
	public void cleanupPageMappings(List<FacebookRemovePageMappingCleanupRequest> fbRemovePageMappingCleanupReqs) throws Exception {
		logger.info("Cleanup request received for FB page mappings with input {}", fbRemovePageMappingCleanupReqs);
		for (FacebookRemovePageMappingCleanupRequest cleanupRequest : fbRemovePageMappingCleanupReqs) {
			Business business = null;
			try {
				business = businessRepo.findById(cleanupRequest.getLocationId());
				// Uninstall tab
				//socialFacebookService.uninstallTabOnPage(cleanupRequest.getProfileId(), business, cleanupRequest.getAccessToken());
				// Unsubscribe Page for Messenger
				if (cleanupRequest.getMessengerOpted() == 1) {
					FacebookBaseResponse response = fbMsgService.fbPageUnsubscribeApps(cleanupRequest.getPageId(), cleanupRequest.getAccessToken());
					if (response.isSuccess()) {
						logger.info("Remove Mapping, Messenger unsubscribed for businessId: {} and pageId: {}", cleanupRequest.getLocationId(), cleanupRequest.getPageId());
					}
				}
			} catch (Exception ex) {
				logger.error("Error occurred while tab removal on page {} for business {} is : {}", cleanupRequest.getPageId(), cleanupRequest.getLocationId(), ex.getMessage());
			}

			if ( business != null ) {
				Integer enterpriseId = business.getId();
				if ( business.getEnterprise() != null ) {
					enterpriseId = business.getEnterprise().getId();
				}
				if ( CacheManager.getInstance().getCache(SystemPropertiesCache.class).getPushMappingStatusToFirebase() ) {
					// Push Social status to Messenger for Remove Mapping
					// This is needed to let a running FB conversation know that mapping is now broken
					// This code will be removed when BIRDEYE-71968 is done
					pushFbStatusInFirebase(enterpriseId, cleanupRequest.getLocationId(), FacebookIntegrationStatus.CONNECT.getStatus());
				}
			}
		}
		logger.info("Cleanup request completed for FB page mappings with input {}", fbRemovePageMappingCleanupReqs);
	}

	@Override
	public void removeFbLocationPageMapping(List<LocationPageMappingRequest> locationPageMappings, String type, boolean unlink) throws Exception {
		logger.info("remove FB page mappings with input {} and type {}", locationPageMappings,type);
		Set<String> pageIds = locationPageMappings.stream().map(LocationPageMappingRequest::getPageId).collect(Collectors.toSet());
		List<BusinessFBPage> businessFBPages = socialFbRepo.findByFacebookPageIdIn(pageIds);

		if(CollectionUtils.isNotEmpty(businessFBPages)) {
			if(Objects.nonNull(locationPageMappings.get(0)) && Objects.nonNull(locationPageMappings.get(0).getResellerId())){
				List<Long> storedResellerIds = businessFBPages.stream().map(BusinessFBPage::getResellerId).collect(Collectors.toList());
				commonService.checkRequestFromAuthorizedSourceUsingResellerIdsList(storedResellerIds, locationPageMappings.get(0).getResellerId());
			}
			businessFBPages.forEach(businessFBPage -> {
				commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.REMOVE_MAPPING.name(), Arrays.asList(businessFBPage), businessFBPage.getUserId(),
						businessFBPage.getBusinessId(), Constants.ENTERPRISE.equals(type)?businessFBPage.getEnterpriseId():businessFBPage.getResellerId());
//				sendEventToSubscribeFB(businessFBPage.getFacebookPageId(), false);
				businessFBPage.setBusinessId(null);
				if(Objects.nonNull(businessFBPage.getResellerId())){
					businessFBPage.setEnterpriseId(null);
					businessFBPage.setAccountId(null);
				}
				if(unlink) {
					businessFBPage.setIsSelected(0);
					businessFBPage.setEnterpriseId(null);
					businessFBPage.setResellerId(null);
				}
			});
//			unSubscribeFromFacebook(businessFBPages);
			socialFbRepo.save(businessFBPages);
			logger.info("FB page mappings removed with input {}", locationPageMappings);
			this.removeEngageStreams(businessFBPages);
			//Send Kafka Message that will be consumed by NiFi to perform cleanup activities
			kafkaProducer.sendObject(Constants.FB_PAGE_MAPPING_REMOVED, businessFBPages.stream()
					.map(page -> new FacebookRemovePageMappingCleanupRequest(page.getBusinessId(), page.getFacebookPageId(), page.getPageAccessToken(), page.getMessengerOpted()))
					.collect(Collectors.toList()));
			// replace delete with remove
		}
	}

	private void sendEventToSubscribeFB(String pageId,boolean subscribe) {
		logger.info("Request received for fb engage subscription for pageId {}", pageId);
		EngageWebhookSubscriptionRequest payload = new EngageWebhookSubscriptionRequest();
		payload.setSubscription(subscribe);
		payload.setChannel(SocialChannel.FACEBOOK.getName());
		payload.setPageId(pageId);
		// subscribe to engage webhook
		kafkaProducer.sendObject(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_WEBHOOK_SUBSCRIPTION_TOPIC.getName(), payload);
	}

	// TODO - Fix for BIRDEYE-73936
	private void removeEngageStreams(List<BusinessFBPage> businessFbPages) {
		if ( CollectionUtils.isNotEmpty(businessFbPages) ) {
			final List<Integer> businessFbPageIds = businessFbPages.stream().map(BusinessFBPage::getId).collect(Collectors.toList());
			socialEngageService.deleteStreams(110, businessFbPageIds);
		}
	}

	/**
	 * @param enterpriseId
	 */
	private void pushFbStatusInFirebase(Integer enterpriseId, Integer locationId, String status) {
		String topicSocial = MESSENGER+enterpriseId+"/"+locationId;
		nexusService.insertMapInFirebase(topicSocial,"socialIntegrationStatus", status);
	}

	private void pushFbAutoMappingInFirebase(Long enterpriseId, String status) {
		String topicSocial = AUTOMAPPING+SocialChannel.FACEBOOK.getName()+"/"+enterpriseId;
		nexusService.insertMapInFirebase(topicSocial,"socialAutoMappingStatus", status);
	}

	/**
	 *
	 * @param business
	 * @return connected --> isSelected true in BusinessFBPage disconnected --> isSelected false in BusinessFBPage mapped --> selected present in
	 *         BusinessFacebookPage unmapped --> selected but not in BusinessFacebookPage enabled --> in BusinessFacebookPage with enabled 1 disabled -->
	 *         in BusinessFacebookPage with enabled 0
	 */

	@Override
	public ChannelPageCount getCountObj(Business business) {
		ChannelPageCount channelCount = null;
		List<BusinessFBPage> businessFBPages = socialFbRepo.findByEnterpriseId(business.getBusinessId());
		if(CollectionUtils.isNotEmpty(businessFBPages)){
			Long connected = businessFBPages.stream().filter(fbPage -> Objects.equals(fbPage.getIsSelected(),1)).count();
			Long disconnected = businessFBPages.stream().filter(fbPage -> Objects.equals(fbPage.getIsSelected(),0)).count();

			List<Integer> businessIds = businessUtilService.getBusinessLocationsForEnterprise(business, null);
			Long mapped = businessFBPages.stream().filter(fbPage -> Objects.nonNull(fbPage.getBusinessId())).count();
			Long unmapped = connected - mapped;

			Long enabled = businessFBPages.stream().filter(fbPage -> {
				if(businessIds.contains(fbPage.getBusinessId()) && fbPage.getEnabled()==1){
					return true;
				}
				return false;
			}).count();
			Long disabled = mapped - enabled;

			channelCount = new ChannelPageCount(connected.intValue(), disconnected.intValue(), mapped.intValue(), unmapped.intValue(), enabled.intValue(), disabled.intValue());
		}else{
			channelCount = new ChannelPageCount();
		}
		return channelCount;
	}

	private List<BusinessFBPage> getFBPages(List<String> pageIds) {
		return socialFbRepo.findByFacebookPageIdIn(pageIds);
	}

	private List<BusinessFBPage> getFBPages(List<String> pageIds, long enterpriseId) {
		return socialFbRepo.findByEnterpriseIdAndFacebookPageIdIn(enterpriseId, pageIds);
	}

	private List<BusinessFBPage> getResellerFBPages(List<String> pageIds, long resellerId) {
		return socialFbRepo.findByResellerIdAndFacebookPageIdIn(resellerId, pageIds);
	}

	public List<ChannelAccountInfo> getAccountInfo(List<BusinessFBPage> fbPages, Long enterpriseId) {
		List<ChannelAccountInfo> accountInfo = new ArrayList<>();
		boolean granularFlag = commonService.checkGranular(enterpriseId);
		fbPages.forEach(page -> {
			String fbPermissions = (granularFlag && StringUtils.isNotEmpty(page.getGranularPagePermissions()))?
					page.getGranularPagePermissions() : page.getPagePermissions();
			accountInfo.add(getAccountInfo(page,webChatEnabled(enterpriseId), fbPermissions));
		});
		return accountInfo;
	}

	private Boolean webChatEnabled(Long enterpriseId, boolean... traverse ){
		Boolean isWebChatEnabled = false;
		try {
			if (enterpriseId != null) {
				isWebChatEnabled = businessCoreService.isWebChatEnabledByNumber(enterpriseId);
			}
		} catch (Exception e) {
			logger.info("Something went while fetching details from business core service for enterpriseId: {}", enterpriseId);
		}
		return isWebChatEnabled;
	}

	public List<ChannelAccountInfo> getResellerAccountInfo(List<BusinessFBPage> fbPages, String userId, Long parentId) {
		List<ChannelAccountInfo> accountInfo = new ArrayList<>();
		Boolean isWebChatEnabled = false;
		if(CollectionUtils.isNotEmpty(fbPages) && Objects.nonNull(fbPages.get(0).getEnterpriseId())) {
			isWebChatEnabled = webChatEnabled(fbPages.get(0).getEnterpriseId());
		}
		Boolean finalIsWebChatEnabled = isWebChatEnabled;
		boolean granularFlag = commonService.checkGranular(parentId);
		fbPages.stream().forEach(page -> {
			String permission = (granularFlag && StringUtils.isNotEmpty(page.getGranularPagePermissions()))? page.getGranularPagePermissions() :
					page.getPagePermissions();
			accountInfo.add(getAccountInfoLite(page, finalIsWebChatEnabled, userId, permission));
		});
		return accountInfo;
	}

	private ChannelAccountInfoLite getAccountInfoLite(BusinessFBPage page,Boolean isWebChatEnabled, String userId, String fbPermissions) {
		logger.info("getAccountInfo : pageId : {}", page.getFacebookPageId());
		ChannelAccountInfoLite accountPage = new ChannelAccountInfoLite();
		accountPage.setAddress(page.getSingleLineAddress());
		accountPage.setId(page.getFacebookPageId());
		accountPage.setUserId(userId);
		accountPage.setPageName(page.getFacebookPageName());
		accountPage.setLink(page.getLink());
		//BIRD-57670
		accountPage.setDisabled(null);
		Validity validity = fetchValidityAndErrorMessage(page,isWebChatEnabled, fbPermissions);
		accountPage.setValidType(validity.getValidType());
		accountPage.setErrorCode(validity.getErrorCode());
		accountPage.setErrorMessage(validity.getErrorMessage());
		return accountPage;
	}

	private ChannelAccountInfo getAccountInfo(BusinessFBPage page,Boolean isWebChatEnabled, String fbPermissions) {
		logger.info("getAccountInfo : pageId : {}", page.getFacebookPageId());
		ChannelAccountInfo accountPage = new ChannelAccountInfo();
		accountPage.setAddress(page.getSingleLineAddress());
		accountPage.setId(page.getFacebookPageId());
		accountPage.setImage(page.getFacebookPagePictureUrl());
		accountPage.setPageName(page.getFacebookPageName());
		accountPage.setLink(page.getLink());
		accountPage.setHandle(page.getHandle());
		Validity validity = fetchValidityAndErrorMessage(page,isWebChatEnabled, fbPermissions);
		accountPage.setValidType(validity.getValidType());
		accountPage.setErrorCode(validity.getErrorCode());
		accountPage.setErrorMessage(validity.getErrorMessage());
		accountPage.setDisabled((page.getIsSelected() != null && page.getIsSelected() == 1) ? Boolean.TRUE : Boolean.FALSE);
		return accountPage;
	}


	@Deprecated //TODO "Remove if /{channel}/all is not required"
	@Override
	public ConnectedPages getConnectedPages(Map<Integer, BusinessEntity> idToBusinessMap, Long enterpriseId) {
		ConnectedPages connectedPage = new ConnectedPages();
		List<BusinessFBPage> connectedPages = socialFbRepo.findByEnterpriseIdAndIsSelected(enterpriseId, 1);
		List<String> pageIds = new ArrayList<>();
		Map<String, BusinessEntity> fbPageToBusinessMap = new HashMap<>();
		Set<Integer> businessIds= idToBusinessMap.keySet();
		if (CollectionUtils.isNotEmpty(connectedPages)) {
			//connectedPages.stream().forEach(page -> pageIds.add(page.getFacebookPageId()));
			//List<BusinessFacebookPageNew> existingPages = businessFbPageRepo.findByFacebookPageIdIn(pageIds);
			List<BusinessFBPage> filteredFBPages = connectedPages.stream().filter(page -> {
				return Objects.nonNull(page.getBusinessId())  && businessIds.contains(page.getBusinessId());
			}).collect(Collectors.toList());

			filteredFBPages.forEach(page -> {
				fbPageToBusinessMap.put(page.getFacebookPageId(), idToBusinessMap.get(page.getBusinessId()));
			});
			ChannelPageDetails accountInfo = getPageInfo(filteredFBPages, fbPageToBusinessMap);
			Map<String, ChannelPageDetails> pageTypes = new HashMap<>();
			if (CollectionUtils.isNotEmpty(accountInfo.getPages())) {
				pageTypes.put(SocialChannel.FACEBOOK.getName(), accountInfo);
			}
			connectedPage.setPageTypes(pageTypes);
		}


		return connectedPage;
	}

	@Deprecated //TODO "Remove if /{channel}/all is not required"
	public ChannelPageDetails getPageInfo(List<BusinessFBPage> fbPages, Map<String, BusinessEntity> fbPageToBusinessMap) {
		List<ChannelPages> pageInfo = new ArrayList<>();
		fbPages.stream().forEach(page -> pageInfo.add(getPageInfoOld(page, fbPageToBusinessMap.get(page.getFacebookPageId()))));
		return sortInvalidAndValidPage(pageInfo);
	}
	@Override
	public ChannelSetupStatus.PageSetupStatus getPageSetupStatus(Long enterpriseId) {
		List<BusinessFBPage> fbPages = socialFbRepo.findByEnterpriseId(enterpriseId);
		if ( fbPages.isEmpty() ) {
			return ChannelSetupStatus.PageSetupStatus.NO_PAGES_FOUND;
		} else {
			List<BusinessFBPage> mappedPages = fbPages.stream().filter(fbPage -> Objects.nonNull(fbPage.getBusinessId())).collect(Collectors.toList());

			// We only need to check if there are pages which have broken mapping
			// We don't need to check for unmapped pages here
			for ( BusinessFBPage fbPageNew : mappedPages ) {
				if ( fbPageNew.getIsValid() != 1 ) {
					return ChannelSetupStatus.PageSetupStatus.DISCONNECTED_PAGES_FOUND;
				}
			}
		}
		return ChannelSetupStatus.PageSetupStatus.OK;
	}

	@Override
	public ChannelSetupStatus.PageSetupStatus getPageConnectionSetupStatus(Long enterpriseId) {
		List<BusinessFBPage> fbPages = socialFbRepo.findByEnterpriseId(enterpriseId);
		if ( fbPages.isEmpty() ) {
			return ChannelSetupStatus.PageSetupStatus.INVALID_INTEGRATION;
		} else {
			List<BusinessFBPage> mappedPages = fbPages.stream().filter(fbPage -> Objects.nonNull(fbPage.getBusinessId())).collect(Collectors.toList());

			// We only need to check if there are pages which have broken mapping
			// We don't need to check for unmapped pages here
			for ( BusinessFBPage fbPageNew : mappedPages ) {
				if ( Objects.nonNull(fbPageNew.getBusinessId()) && fbPageNew.getIsValid() == 1 ) {
					return ChannelSetupStatus.PageSetupStatus.VALID_INTEGRATION;
				}
			}
			return ChannelSetupStatus.PageSetupStatus.INVALID_INTEGRATION;
		}
	}

	@Override
	public BusinessIntegrationStatus.ChannelIntegrationInfo getPageIntegrationStatus(Integer businessId) {
		logger.info("getPageIntegrationStatus {}", businessId);
		if (businessId != null) {
			// Get FB mapping
			final List<BusinessFBPage> fbLocList = socialFbRepo.findByBusinessId(businessId);
			if (CollectionUtils.isEmpty(fbLocList)) {
				return null;
			} else if (fbLocList.size() > 1) {
				throw new BirdeyeSocialException(ErrorCodes.FB_MULTIPLE_MAPPING_FOUND, "Multiple mapping found for businessId");
			}

			BusinessFBPage fbLocRaw = fbLocList.get(0);
			// Prepare response
			BusinessIntegrationStatus.ChannelIntegrationInfo channelInfo = new BusinessIntegrationStatus.ChannelIntegrationInfo();
			channelInfo.setPageId(fbLocRaw.getFacebookPageId());
			channelInfo.setValid(fbLocRaw.getIsValid());
			channelInfo.setPageName(fbLocRaw.getFacebookPageName());

			// Get emailId from get page request table
			if (StringUtils.isNotEmpty(fbLocRaw.getRequestId())) {
				final String requestId = fbLocRaw.getRequestId();
				logger.info("getPageIntegrationStatus: requestId {}", requestId);
				if (requestId.startsWith(OPEN_URL_REQUEST_PREFIX)) {
					final BusinessGetPageOpenUrlRequest openUrlRequest = businessGetPageOpenUrlReqRepo.findOne(requestId);
					if (openUrlRequest != null) {
						channelInfo.setEmail(openUrlRequest.getEmail());
					}
				} else {
					final BusinessGetPageRequest getPageRequest = businessGetPageReqRepo.findOne(Integer.valueOf(requestId));
					if (getPageRequest != null) {
						channelInfo.setEmail(getPageRequest.getEmail());
					}
				}
			}

			// Get firstName and lastName
			channelInfo.setUserName(String.join(" ",
					StringUtils.isNotEmpty(fbLocRaw.getFirstName()) ? fbLocRaw.getFirstName() : "",
					StringUtils.isNotEmpty(fbLocRaw.getLastName()) ? fbLocRaw.getLastName() : "").trim()
			);

			channelInfo.setPageUrl(fbLocRaw.getLink());
			return channelInfo;
		}
		return null;
	}

	@Override
	public ConnectedPages getPages(Long enterpriseId, PageConnectionStatus pageConnectionStatus) {
		ConnectedPages connectedPage = new ConnectedPages();
		Map<String, ChannelPageDetails> pageTypes = new HashMap<>();

		List<BusinessFBPage> connectedPages = socialFbRepo.findByEnterpriseIdAndIsSelected(enterpriseId, 1);
		logger.info("getpages : Found {} pages", CollectionUtils.size(connectedPages));
		if (CollectionUtils.isNotEmpty(connectedPages)) {
			connectedPages.stream().forEach(fb->{
				if(fb.getFacebookPageId() == null) {
					logger.error("FB page Id is null for enterpriseId: {}, Data cleanup is required.",fb.getEnterpriseId());
				}
			});
		}
		ChannelPageDetails accountInfo = getPageInfo(enterpriseId, connectedPages, pageConnectionStatus);
		pageTypes.put(SocialChannel.FACEBOOK.getName(), accountInfo);

		connectedPage.setPageTypes(pageTypes);
		return connectedPage;
	}

	@Override
	public ConnectedPages getPagesForPostReconnect(Long enterpriseId, PageConnectionStatus pageConnectionStatus, SocialPostPageConnectRequest request) {
		ConnectedPages connectedPage = new ConnectedPages();
		Map<String, ChannelPageDetails> pageTypes = new HashMap<>();

		List<BusinessFBPage> connectedPages = socialFbRepo.findByFacebookPageIdInAndIsSelected(request.getPageIds(), 1);
		logger.info("getpages : Found {} pages", CollectionUtils.size(connectedPages));
		if (CollectionUtils.isNotEmpty(connectedPages)) {
			connectedPages.stream().forEach(fb->{
				if(fb.getFacebookPageId() == null) {
					logger.error("FB page Id is null for enterpriseId: {}, Data cleanup is required.",fb.getEnterpriseId());
				}
			});
		}
		ChannelPageDetails accountInfo = getPageInfo(enterpriseId, connectedPages, pageConnectionStatus);
		pageTypes.put(SocialChannel.FACEBOOK.getName(), accountInfo);

		connectedPage.setPageTypes(pageTypes);
		return connectedPage;
	}

	@Override
	public void updateAddress(SocialScanEventDTO socialScanEventDTO) {
		if(Objects.isNull(socialScanEventDTO) || StringUtils.isEmpty(socialScanEventDTO.getExternalId())) {
			logger.info("null event received to update address");
			return;
		}

		BusinessFBPage fbPage = socialFbRepo.findFirstByFacebookPageId(socialScanEventDTO.getExternalId());

		if(Objects.isNull(fbPage)) {
			logger.info("no page found for page id: {}", socialScanEventDTO.getExternalId());
			return;
		}

		if(StringUtils.isNotEmpty(fbPage.getSingleLineAddress())) {
			logger.info("single line address is already there for page id: {}", socialScanEventDTO.getExternalId());
			return;
		}

		try {
			FacebookPageInfo fbPageInfo = fbService.getPageDetailsByPageId(fbPage.getFacebookPageId(), fbPage.getPageAccessToken());
			String singleLineAddress = fbPageService.extractSingleLineAddressFromLocation(fbPageInfo);

			if(StringUtils.isNotEmpty(singleLineAddress)) {
				fbPage.setSingleLineAddress(singleLineAddress);
				socialFbRepo.save(fbPage);
			}
		} catch (Exception e) {
			logger.info("Exception occurred while updating facebook address for page id: {}, error: {}", fbPage.getFacebookPageId(), e.getMessage());
		}
	}

	@Override
	public PaginatedConnectedPages getResellerPages(Long resellerId, PageConnectionStatus pageConnectionStatus, Integer page, Integer size, String search,
													ResellerSearchType searchType, PageSortDirection sortDirection, ResellerSortType sortType,
													List<Integer> locationIds, MappingStatus mappingStatus, List<Integer> userIds, Boolean locationFilterSelected) {
		PaginatedConnectedPages connectedPage = new PaginatedConnectedPages();
		Map<String, ChannelPageDetails> pageTypes = new HashMap<>();

		Long getInvalidPageIds = socialFbRepo.findCountByResellerIdAndValidityType(resellerId,Arrays.asList(ValidTypeEnum.INVALID.getId(),ValidTypeEnum.PARTIAL_VALID.getId()));
		Page<BusinessFBPage> connectedPages  ;
		connectedPages = searchSortAndPaginate(search, resellerId, locationIds, pageConnectionStatus, userIds, 1, mappingStatus,
				page, size, sortDirection, sortType, locationFilterSelected);
		logger.info("getpages : Found {} pages", CollectionUtils.size(connectedPages));
		if (Objects.nonNull(connectedPages) && CollectionUtils.isNotEmpty(connectedPages.getContent())) {
			connectedPages.getContent().stream().forEach(fb->{
				if(fb.getFacebookPageId() == null) {
					logger.error("FB page Id is null for resellerId: {}, Data cleanup is required.",fb.getResellerId());
				}
			});
		}
		try {
			ChannelPageDetails accountInfo = getResellerPageInfo(resellerId, connectedPages.getContent());
			Long totalPagesCount = connectedPages.getTotalElements();
			//accountInfo.setPages(resellerHelperService.sortAndPaginatedPages(accountInfo.getPages(), sortType, sortDirection, page, size));
			Integer currentPagesCount = connectedPages.getTotalPages();
			accountInfo.setDisconnected(Math.toIntExact(getInvalidPageIds));
			pageTypes.put(SocialChannel.FACEBOOK.getName(), accountInfo);

			connectedPage.setPageTypes(pageTypes);
			connectedPage.setPageCount(currentPagesCount);
			connectedPage.setTotalCount(totalPagesCount);
			return connectedPage;
		} catch (Exception e) {
			logger.info("exception occred while setting page details error: {}",e.getMessage());
			throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Internal Server Error");
		}
	}

	public ChannelPageDetails getPageInfo(Long enterpriseId, List<BusinessFBPage> fbPages,
										  PageConnectionStatus pageConnectionStatus) {
		List<ChannelPages> pageInfo = new ArrayList<>();
		// Process for non empty connected FB pages.
		if (!fbPages.isEmpty()) {
			Boolean isWebChatEnabled = businessCoreService.isWebChatEnabledByNumber(enterpriseId);
			boolean granularFlag = commonService.checkGranular(enterpriseId);
			fbPages.forEach(page -> {
				String fbPermission = (granularFlag && StringUtils.isNotEmpty(page.getGranularPagePermissions()))? page.getGranularPagePermissions() :
						page.getPagePermissions();
				ChannelPages completePageInfo = getPageInfo(page, isWebChatEnabled, fbPermission);
				if (pageConnectionStatus == PageConnectionStatus.CONNECTED
						&& completePageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.VALID.getName())) {
					pageInfo.add(completePageInfo);
				} else if (pageConnectionStatus == PageConnectionStatus.DISCONNECTED) {
					if (completePageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.PARTIAL_VALID.getName())
							|| completePageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.INVALID.getName())) {
						pageInfo.add(completePageInfo);
					} else {
						if (((completePageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.PARTIAL_VALID.getName())
								|| completePageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.INVALID.getName()))
								&& (fbPermission != null && !fbPermission.contains(PAGES_MESSAGING)))
								|| (completePageInfo.getReadInsightsPermission() != null
								&& !completePageInfo.getReadInsightsPermission())
								|| fbPermission == null) {
							pageInfo.add(completePageInfo);
						}
					}

				} else if (pageConnectionStatus == PageConnectionStatus.ALL) {
					pageInfo.add(completePageInfo);
				}
			});
		}
		return sortInvalidAndValidPage(pageInfo);
	}

	public ChannelPageDetails getResellerPageInfo(Long parentId, List<BusinessFBPage> fbPages) throws Exception {
		List<ChannelPages> pageInfo = new ArrayList<>();
		// Process for non empty connected FB pages.
		if (!fbPages.isEmpty()) {
			boolean granularFlag = commonService.checkGranular(parentId);
			List<Integer> businessIds = new ArrayList<>();
			List<Integer> userIds = new ArrayList<>();
			fbPages.forEach(x->{
				if(Objects.nonNull(x.getBusinessId())) businessIds.add(x.getBusinessId());
				if(Objects.nonNull(x.getCreatedBy())) userIds.add(x.getCreatedBy());
			});
			Map<String, Object> businessLocations = null;
			CompletableFuture<Map<String, Object>> businessLocationsFuture = CompletableFuture.supplyAsync(() -> {
				try {
					return businessCoreService.getBusinessesInBulkByBusinessIds(businessIds,true);
				} catch (Exception e) {
					logger.info("exception while executing business location future, error: {}", e.getMessage());
					return new HashMap<>();
				}
			});
			CompletableFuture<Map<Integer, BusinessCoreUser>> userDetailsFuture = CompletableFuture.supplyAsync(() -> {
				try {
					return coreBusinessService.getBusinessUserForUserId(userIds);
				} catch (Exception e) {
					logger.info("exception while executing user details future, error: {}", e.getMessage());
					return new HashMap<>();
				}
			});
			CompletableFuture<Void> allCompletableFuture = CompletableFuture.allOf(businessLocationsFuture, userDetailsFuture);
			allCompletableFuture.get(10,TimeUnit.SECONDS);
			businessLocations = businessLocationsFuture.get();
			Map<Integer, BusinessCoreUser> userIdVsInfoMap= userDetailsFuture.get();
			Map<String, Object> finalBusinessLocations = businessLocations;
			fbPages.stream().forEach(page -> {
				String permission = (granularFlag && StringUtils.isNotEmpty(page.getGranularPagePermissions()))? page.getGranularPagePermissions() :
						page.getPagePermissions();
				Boolean isWebChatEnabled = false;
				try {
					if(CollectionUtils.isNotEmpty(fbPages) && Objects.nonNull(page.getEnterpriseId())) {
						isWebChatEnabled = webChatEnabled(page.getEnterpriseId());
					}
				}catch (ExternalAPIException e){
					logger.info("Something went wrong while getting isGoogleMessagingEnabled for enterprise id: {}",page.getEnterpriseId());
				}
				//BusinessLocationLiteEntity locationLite = null;

				BusinessLocationLiteEntity locationLite = null;
				if(Objects.nonNull(finalBusinessLocations) && Objects.nonNull(page.getBusinessId())){
					logger.info("Prepare data for mapped page :{}",page);
					Map<String ,Object> locationData = (Map<String, Object>) finalBusinessLocations.get(page.getBusinessId().toString());
					locationLite = commonService.getMappedLocationInfo(locationData, page.getBusinessId(), page.getFacebookPageName());
				}

				BusinessCoreUser userDetail = null;
				if(Objects.nonNull(page.getCreatedBy()) && MapUtils.isNotEmpty(userIdVsInfoMap) &&
						userIdVsInfoMap.containsKey(page.getCreatedBy())) {
					userDetail = userIdVsInfoMap.get(page.getCreatedBy());
				}
				ChannelPages completePageInfo = getResellerPageInfo(page, isWebChatEnabled, locationLite, permission, userDetail);
				pageInfo.add(completePageInfo);
			});
		}
		return sortInvalidAndValidPageForReseller(pageInfo,parentId);
	}


	// filter and sorting
	private ChannelPageDetails sortInvalidAndValidPage(List<ChannelPages> pageInfo) {
		ChannelPageDetails channelPageDetails = new ChannelPageDetails();
		List<ChannelPages> invalidPages = new ArrayList<>();
		List<ChannelPages> messengerNotEnabledPage = new ArrayList<>();
		List<ChannelPages>  partitionInsights = new ArrayList<>();
		List<ChannelPages> validPages = new ArrayList<>();
		for(ChannelPages page: pageInfo) {
			if(page.getValidType().equalsIgnoreCase(ValidTypeEnum.VALID.getName())) {
				validPages.add(page);
			} else {
				invalidPages.add(page);
				if(MESSENGER_NOT_ENABLED.equalsIgnoreCase(page.getErrorCode())) {
					messengerNotEnabledPage.add(page);
				}
				if(page.getReadInsightsPermission() != null && !page.getReadInsightsPermission()) {
					partitionInsights.add(page);
				}
			}
		}
		channelPageDetails.setDisconnected(invalidPages.size());
		channelPageDetails.setMessenger_disabled(messengerNotEnabledPage.size());
		channelPageDetails.setReadInsightsPermissionMissing(partitionInsights.size());
		invalidPages.sort(Comparator.comparing(ChannelPages::getPageName, nullsFirst(Comparator.naturalOrder())));
		validPages.sort(Comparator.comparing(ChannelPages::getPageName, nullsFirst(Comparator.naturalOrder())));
		invalidPages.addAll(validPages);
		channelPageDetails.setPages(invalidPages);
		return channelPageDetails;
	}

	private ChannelPageDetails sortInvalidAndValidPageForReseller(List<ChannelPages> pageInfo,Long resellerId) {
		ChannelPageDetails channelPageDetails = new ChannelPageDetails();
		List<ChannelPages> messengerNotEnabledPage = new ArrayList<>();
		List<ChannelPages>  partitionInsights = new ArrayList<>();
		for(ChannelPages page: pageInfo) {
			if(Objects.nonNull(page.getValidType()) && !page.getValidType().equalsIgnoreCase(ValidTypeEnum.VALID.getName())) {
				if(MESSENGER_NOT_ENABLED.equalsIgnoreCase(page.getErrorCode())) {
					messengerNotEnabledPage.add(page);
				}
				if(page.getReadInsightsPermission() != null && !page.getReadInsightsPermission()) {
					partitionInsights.add(page);
				}
			}
		}
//		List<String> getInvalidPageIds = socialFbRepo.findByResellerIdAndValidityType(resellerId,Arrays.asList(ValidTypeEnum.INVALID.getId(),ValidTypeEnum.PARTIAL_VALID.getId()));
//		channelPageDetails.setDisconnected(getInvalidPageIds.size());
		channelPageDetails.setMessenger_disabled(messengerNotEnabledPage.size());
		channelPageDetails.setReadInsightsPermissionMissing(partitionInsights.size());
		channelPageDetails.setPages(pageInfo);
		return channelPageDetails;
	}

	private Validity fetchValidityAndErrorMessageByGroup(BusinessFBPage page, Boolean isWebChatEnabled, Map<String, RoleMapping> fbPagesScopes, List<PermissionMapping> permissionMappings, PermissionMapping permissionMappingIntegration, String fbPermissions) {
		Validity validity = new Validity();
		if(page.getIsManaged() != null && page.getIsManaged() == 0) {
			validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
			RoleMapping roleMapping = fbPagesScopes.get(page.getFacebookPageId());
			if(roleMapping != null) {
				validity.setErrorCode(roleMapping.getErrorCode());
				if(isWebChatEnabled == false) {
					validity.setErrorMessage(roleMapping.getErrorMessage().split(" and enable messaging")[0]);
				} else {
					validity.setErrorMessage(roleMapping.getErrorMessage());
				}
			} else {
				validity.setErrorCode(Constants.DEFAULT);
				validity.setErrorMessage(Constants.DEFAULT_MESSAGE);
			}
		} else if(page.getIsValid() == 0) {
			validity.setValidType(ValidTypeEnum.INVALID.getName());
			if(page.getFbErrorSubcode() != null && !page.getFbErrorSubcode().equals(0)) {
				PermissionMapping permissionMapping = permissionMappingService.getDataByChannelAndPermissionCodeAndModule(
						SocialChannel.FACEBOOK.getName(),page.getFbErrorSubcode(), INTEGRATION_MODULE);
				validity.setErrorCode(permissionMapping.getErrorCode());
				validity.setErrorMessage(permissionMapping.getErrorMessage());
			} else {
				validity.setErrorCode(permissionMappingIntegration.getErrorCode());
				validity.setErrorMessage(permissionMappingIntegration.getErrorMessage());
			}
		} else if(StringUtils.isNotEmpty(fbPermissions)) {
			String finalErrorMessage = DEFAULT_ERROR_MESSAGE_START;
			Integer numberOfErrors = 0;
			for(PermissionMapping permissionMapping: permissionMappings) {
				if(!fbPermissions.contains(permissionMapping.getPermissionName()) && finalErrorMessage.indexOf(permissionMapping.getErrorMessage()) == -1) {
					if(isWebChatEnabled == false && permissionMapping.getPermissionName().equalsIgnoreCase("pages_messaging")) {
						continue;
					}
					if(numberOfErrors == 0) {
						finalErrorMessage = finalErrorMessage.concat(permissionMapping.getErrorMessage());
					} else if(numberOfErrors > 0) {
						finalErrorMessage = finalErrorMessage.concat(", ").concat(permissionMapping.getErrorMessage());
					}
					numberOfErrors++;
				}
			}
			if(numberOfErrors>0) {
				validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
				validity.setErrorCode(PERMISSION_MISSING);
				validity.setErrorMessage(finalErrorMessage);
			} else {
				validity.setValidType(ValidTypeEnum.VALID.getName());
			}
		} else {
			validity.setValidType(ValidTypeEnum.VALID.getName());
		}
		return validity;
	}

	private Validity fetchValidityAndErrorMessageByGroupV2(BusinessFBPage page, Boolean isWebChatEnabled, Map<String, RoleMapping> fbPagesScopes, List<PermissionMapping> permissionMappings, PermissionMapping permissionMappingIntegration, Map<Integer, PermissionMapping> errorPermissionMapping,
														   String permissions) {
		Validity validity = new Validity();
		if(page.getIsManaged() != null && page.getIsManaged() == 0) {
			validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
			RoleMapping roleMapping = fbPagesScopes.get(page.getFacebookPageId());
			if(roleMapping != null) {
				validity.setErrorCode(roleMapping.getErrorCode());
				if(isWebChatEnabled == false) {
					validity.setErrorMessage(roleMapping.getErrorMessage().split(" and enable messaging")[0]);
				} else {
					validity.setErrorMessage(roleMapping.getErrorMessage());
				}
			} else {
				validity.setErrorCode(Constants.DEFAULT);
				validity.setErrorMessage(Constants.DEFAULT_MESSAGE);
			}
		} else if(page.getIsValid() == 0) {
			validity.setValidType(ValidTypeEnum.INVALID.getName());
			if(page.getFbErrorSubcode() != null && !page.getFbErrorSubcode().equals(0)) {
				PermissionMapping permissionMapping = errorPermissionMapping.get(page.getFbErrorSubcode());
				if(Objects.nonNull(permissionMapping)) {
					validity.setErrorCode(permissionMapping.getErrorCode());
					validity.setErrorMessage(permissionMapping.getErrorMessage());
				}

			} else {
				validity.setErrorCode(permissionMappingIntegration.getErrorCode());
				validity.setErrorMessage(permissionMappingIntegration.getErrorMessage());
			}
		} else if(StringUtils.isNotEmpty(permissions)) { // convery string to list and iterate according.
			String finalErrorMessage = DEFAULT_ERROR_MESSAGE_START;
			Integer numberOfErrors = 0;
			for(PermissionMapping permissionMapping: permissionMappings) {
				if(!permissions.contains(permissionMapping.getPermissionName()) && finalErrorMessage.indexOf(permissionMapping.getErrorMessage()) == -1) {
					if(isWebChatEnabled == false && permissionMapping.getPermissionName().equalsIgnoreCase("pages_messaging")) {
						continue;
					}
					if(numberOfErrors == 0) {
						finalErrorMessage = finalErrorMessage.concat(permissionMapping.getErrorMessage());
					} else if(numberOfErrors > 0) {
						finalErrorMessage = finalErrorMessage.concat(", ").concat(permissionMapping.getErrorMessage());
					}
					numberOfErrors++;
				}
			}
			if(numberOfErrors>0) {
				validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
				validity.setErrorCode(PERMISSION_MISSING);
				validity.setErrorMessage(finalErrorMessage);
			} else {
				validity.setValidType(ValidTypeEnum.VALID.getName());
			}
		} else {
			validity.setValidType(ValidTypeEnum.VALID.getName());
		}
		return validity;
	}


	@Override
	public Validity fetchValidityAndErrorMessage(BusinessFBPage page, Boolean isWebChatEnabled, String fbPermissions) {
		Validity validity = new Validity();
		/*if(page.getIsManaged() == null) {
			page = fbPageService.updateFacebookRole(page);
			if(page.getIsManaged()!=null) {
				socialFbRepo.saveAndFlush(page);
			}
		}*/
		if(StringUtils.isEmpty(fbPermissions)) {
			fbPermissions = page.getPagePermissions();
		}
		if(page.getIsManaged() != null && page.getIsManaged() == 0) {
			validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
			String task = page.getScope()!=null?page.getScope().split(" -")[0]:null;
			RoleMapping roleMapping = roleMappingService.getDataByChannelAndRole(
					SocialChannel.FACEBOOK.getName(),task);
			if(roleMapping != null) {
				validity.setErrorCode(roleMapping.getErrorCode());
				if(isWebChatEnabled == false) {
					validity.setErrorMessage(roleMapping.getErrorMessage().split(" and enable messaging")[0]);
				} else {
					validity.setErrorMessage(roleMapping.getErrorMessage());
				}
			} else {
				validity.setErrorCode(Constants.DEFAULT);
				validity.setErrorMessage(Constants.DEFAULT_MESSAGE);
			}
		} else if(page.getIsValid() == 0) {
			validity.setValidType(ValidTypeEnum.INVALID.getName());
			if(page.getFbErrorSubcode() != null && !page.getFbErrorSubcode().equals(0)) {
				PermissionMapping permissionMapping = permissionMappingService.getDataByChannelAndPermissionCodeAndModule(
						SocialChannel.FACEBOOK.getName(),page.getFbErrorSubcode(), INTEGRATION_MODULE);
				validity.setErrorCode(permissionMapping.getErrorCode());
				validity.setErrorMessage(permissionMapping.getErrorMessage());
			} else {
				PermissionMapping permissionMapping = permissionMappingService.getDataByChannelAndErrorCode(
						SocialChannel.FACEBOOK.getName(),INTEGRATION);
				validity.setErrorCode(permissionMapping.getErrorCode());
				validity.setErrorMessage(permissionMapping.getErrorMessage());
			}
		} else if(StringUtils.isNotEmpty(fbPermissions)) {
			List<PermissionMapping> permissionMappings = permissionMappingService.getDataByChannelAndPermissionNameNotNull(
					SocialChannel.FACEBOOK.getName());
			String finalErrorMessage = DEFAULT_ERROR_MESSAGE_START;
			Integer numberOfErrors = 0;
			for(PermissionMapping permissionMapping: permissionMappings) {
				if(!fbPermissions.contains(permissionMapping.getPermissionName()) && finalErrorMessage.indexOf(permissionMapping.getErrorMessage()) == -1) {
					if(isWebChatEnabled == false && permissionMapping.getPermissionName().equalsIgnoreCase("pages_messaging")) {
						continue;
					}
					if(numberOfErrors == 0) {
						finalErrorMessage = finalErrorMessage.concat(permissionMapping.getErrorMessage());
					} else if(numberOfErrors > 0) {
						finalErrorMessage = finalErrorMessage.concat(", ").concat(permissionMapping.getErrorMessage());
					}
					numberOfErrors++;
				}
			}
			if(numberOfErrors>0) {
				validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
				validity.setErrorCode(PERMISSION_MISSING);
				validity.setErrorMessage(finalErrorMessage);
			} else {
				validity.setValidType(ValidTypeEnum.VALID.getName());
			}
		} else {
			validity.setValidType(ValidTypeEnum.VALID.getName());
		}
		return validity;
	}

	private Validity createValidityOnValidType(BusinessFBPage page, Boolean isWebChatEnabled, String fbPermissions) {
		if(Objects.isNull(page)) return new Validity();

		Validity validity = new Validity();
		if(ValidTypeEnum.VALID.getId().equals(page.getValidType())) {
			validity.setValidType(ValidTypeEnum.VALID.getName());
		} else if(ValidTypeEnum.INVALID.getId().equals(page.getValidType())) {
			validity.setValidType(ValidTypeEnum.INVALID.getName());
			if(page.getFbErrorSubcode() != null && !page.getFbErrorSubcode().equals(0)) {
				PermissionMapping permissionMapping = permissionMappingService.getDataByChannelAndPermissionCodeAndModule(
						SocialChannel.FACEBOOK.getName(),page.getFbErrorSubcode(), INTEGRATION_MODULE);
				validity.setErrorCode(permissionMapping.getErrorCode());
				validity.setErrorMessage(permissionMapping.getErrorMessage());
			} else {
				PermissionMapping permissionMapping = permissionMappingService.getDataByChannelAndErrorCode(
						SocialChannel.FACEBOOK.getName(),INTEGRATION);
				validity.setErrorCode(permissionMapping.getErrorCode());
				validity.setErrorMessage(permissionMapping.getErrorMessage());
			}
		} else if(ValidTypeEnum.PARTIAL_VALID.getId().equals(page.getValidType())) {
			validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
			if(page.getIsManaged() != null && page.getIsManaged() == 0) {
				String task = page.getScope()!=null?page.getScope().split(" -")[0]:null;
				RoleMapping roleMapping = roleMappingService.getDataByChannelAndRole(
						SocialChannel.FACEBOOK.getName(),task);
				if(roleMapping != null) {
					validity.setErrorCode(roleMapping.getErrorCode());
					if(isWebChatEnabled == false) {
						validity.setErrorMessage(roleMapping.getErrorMessage().split(" and enable messaging")[0]);
					} else {
						validity.setErrorMessage(roleMapping.getErrorMessage());
					}
				} else {
					validity.setErrorCode(Constants.DEFAULT);
					validity.setErrorMessage(Constants.DEFAULT_MESSAGE);
				}
			} else {
				List<PermissionMapping> permissionMappings = permissionMappingService.getDataByChannelAndPermissionNameNotNull(
						SocialChannel.FACEBOOK.getName());
				String finalErrorMessage = DEFAULT_ERROR_MESSAGE_START;
				Integer numberOfErrors = 0;
				for(PermissionMapping permissionMapping: permissionMappings) {
					if(!fbPermissions.contains(permissionMapping.getPermissionName()) && finalErrorMessage.indexOf(permissionMapping.getErrorMessage()) == -1) {
						if(isWebChatEnabled == false && permissionMapping.getPermissionName().equalsIgnoreCase("pages_messaging")) {
							continue;
						}
						if(numberOfErrors == 0) {
							finalErrorMessage = finalErrorMessage.concat(permissionMapping.getErrorMessage());
						} else if(numberOfErrors > 0) {
							finalErrorMessage = finalErrorMessage.concat(", ").concat(permissionMapping.getErrorMessage());
						}
						numberOfErrors++;
					}
				}
				if(numberOfErrors.equals(0)) {
					finalErrorMessage = GENERIC_PERMISSION_MISSING_MESSAGE;
				}
				validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
				validity.setErrorCode(PERMISSION_MISSING);
				validity.setErrorMessage(finalErrorMessage);
			}
		}
		return validity;
	}

	private ChannelPages getPageInfoOld(BusinessFBPage page, BusinessEntity mappedBusiness) {
		ChannelPages pageInfo = new ChannelPages();
		pageInfo.setAddress(page.getSingleLineAddress());
		pageInfo.setId(page.getFacebookPageId());
		pageInfo.setImage(page.getFacebookPagePictureUrl());
		pageInfo.setPageName(page.getFacebookPageName());
		pageInfo.setLink(page.getLink());
		if (org.apache.commons.lang3.StringUtils.isNotBlank(page.getHandle()) && !org.apache.commons.lang3.StringUtils.startsWith(page.getHandle(), "@")) {
			pageInfo.setHandle(org.apache.commons.lang3.StringUtils.join("@", page.getHandle()));
		}
		pageInfo.setValidType(page.getIsValid() == 1 ? ValidTypeEnum.VALID.toString() : ValidTypeEnum.INVALID.toString());
		if (mappedBusiness != null) {
			pageInfo.setLocationId(mappedBusiness.getId());
			pageInfo.setLocationName(mappedBusiness.getAlias1() != null ? mappedBusiness.getAlias1() : mappedBusiness.getName());
		}

		if (StringUtils.isEmpty(page.getPagePermissions()) || !page.getPagePermissions().contains(READ_INSIGHTS))
			pageInfo.setReadInsightsPermission(false);

		return pageInfo;
	}
	private ChannelPages getPageInfo(BusinessFBPage page, Boolean isWebChatEnabled, String fbPermissions) {
		ChannelPages pageInfo = new ChannelPages();
		pageInfo.setAddress(page.getSingleLineAddress());
		pageInfo.setId(page.getFacebookPageId());
		pageInfo.setImage(page.getFacebookPagePictureUrl());
		pageInfo.setPageName(page.getFacebookPageName());
		pageInfo.setLink(page.getLink());
		if (org.apache.commons.lang3.StringUtils.isNotBlank(page.getHandle()) && !org.apache.commons.lang3.StringUtils.startsWith(page.getHandle(), "@")) {
			pageInfo.setHandle(org.apache.commons.lang3.StringUtils.join("@", page.getHandle()));
		}
		Validity validity = fetchValidityAndErrorMessage(page,isWebChatEnabled, fbPermissions);
		pageInfo.setValidType(validity.getValidType());
		pageInfo.setErrorCode(validity.getErrorCode());
		pageInfo.setErrorMessage(validity.getErrorMessage());
		return pageInfo;
	}

	private ChannelPages getResellerPageInfo(BusinessFBPage page, Boolean isWebChatEnabled, BusinessLocationLiteEntity locationDetails,
											 String fbPermissions, BusinessCoreUser userDetail) {
		ChannelPages pageInfo = new ChannelPages();
		pageInfo.setAddress(page.getSingleLineAddress());
		pageInfo.setId(page.getFacebookPageId());
		pageInfo.setUserId(page.getUserEmailId());
		pageInfo.setImage(page.getFacebookPagePictureUrl());
		pageInfo.setPageName(page.getFacebookPageName());
		pageInfo.setLink(page.getLink());
		pageInfo.setAddedBy(Objects.nonNull(userDetail)?businessCoreService.getFullUsername(userDetail):null);

		if( Objects.nonNull(locationDetails)){
			pageInfo.setLocationId(locationDetails.getId());
			pageInfo.setLocationAddress(commonService.prepareBusinessAddress(locationDetails));
			pageInfo.setLocationName(locationDetails.getAlias1() != null ? locationDetails.getAlias1() : locationDetails.getName());
			pageInfo.setParentName(locationDetails.getAccountName());

		}

		if (org.apache.commons.lang3.StringUtils.isNotBlank(page.getHandle()) && !org.apache.commons.lang3.StringUtils.startsWith(page.getHandle(), "@")) {
			pageInfo.setHandle(org.apache.commons.lang3.StringUtils.join("@", page.getHandle()));
		}
		Validity validity = createValidityOnValidType(page,isWebChatEnabled, fbPermissions);
		pageInfo.setValidType(validity.getValidType());
		pageInfo.setErrorCode(validity.getErrorCode());
		pageInfo.setErrorMessage(validity.getErrorMessage());
		return pageInfo;
	}

	/**
	 * Method to get invalid type
	 * @param mappedPage
	 */
	private String getInvalidTypeForAllPages(BusinessFBPage mappedPage) {
		String invalidType = null;
		if (mappedPage.getFbErrorSubcode() == FbErrorSubCodeEnum.PASSWORD_CHANGED.value()) {
			invalidType = PSWD_CHANGED;
		} else if (mappedPage.getFbErrorSubcode() == FbErrorSubCodeEnum.INSUFFICIENT_PERMISSION.value()) {
			invalidType = INSUFFICIENT_PERMISSION;
		} else if (mappedPage.getFbErrorSubcode() == FbErrorSubCodeEnum.TOKEN_EXPIRED.value()) {
			invalidType = TOKEN_EXPIRED;
		}  else if (mappedPage.getFbErrorSubcode() == FbErrorSubCodeEnum.USER_BLOCKED.value()) {
			invalidType = USER_BLOCKED;
		} else if (mappedPage.getFbErrorSubcode() == FbErrorSubCodeEnum.REVOKED_PERMISSION.value()) {
			invalidType = REVOKED_PERMISSION;
		} else if (mappedPage.getFbErrorSubcode() == FbErrorSubCodeEnum.INVALID_ACCESS_TOKEN.value()) {
			invalidType = TOKEN_EXPIRED;
		}
		return  invalidType;
	}

	@Override
	public ReviewOptionsInfo getReviewShareOptions(Long enterpriseId, Map<Integer, BusinessEntity> idToBusinessMap, Map<Integer, String> locationToAddressMap, Integer startIndex, Integer count) {
		ReviewOptionsInfo reviewShareOptions = new ReviewOptionsInfo();
		List<Integer> busienssIds = new ArrayList<>(idToBusinessMap.keySet());
		SortedSet<ReviewOptionInfo> reviewOptions = new TreeSet<>();

		if (CollectionUtils.isNotEmpty(busienssIds)) {
			List<BusinessFBPage> existingPages = socialFbRepo.findAllByBusinessIdInWithLimit(busienssIds, new PageRequest((startIndex / count), count));
			existingPages.stream().forEach(page -> reviewOptions.add(getReviewShareOptionObj(page, idToBusinessMap, locationToAddressMap)));
			reviewShareOptions.setCount(socialFbRepo.countByBusinessIdIn(busienssIds).size());
		}
		Integer totalPage = fbPageService.getBusinessSelectedPagesCount(enterpriseId);
		reviewShareOptions.setOptions(reviewOptions);
		reviewShareOptions.setTotalPage(totalPage);
		return reviewShareOptions;
	}

	private ReviewOptionInfo getReviewShareOptionObj(BusinessFBPage page, Map<Integer, BusinessEntity> idToBusinessMap, Map<Integer, String> locationToAddressMap) {
		ReviewOptionInfo reviewOption = new ReviewOptionInfo();
		LocationDetail location = new LocationDetail();
		BusinessEntity business = idToBusinessMap.get(page.getBusinessId());
		location.setId(business.getId());
		location.setName(business.getAlias1() != null ? business.getAlias1() : business.getName());
		location.setAddress(locationToAddressMap.get(business.getLocationId()));
		reviewOption.setLocation(location);
		reviewOption.setPageId(page.getFacebookPageId());
		reviewOption.setAutoPostingEnabled(page.getEnabled() == 1 ? Boolean.TRUE : Boolean.FALSE);
		reviewOption.setPostVal(page.getMaxPostAllowed());
		reviewOption.setStarVal(page.getMinRating());
		return reviewOption;
	}

	@Override
	public ReviewOptionDto updateReviewSharingOptions(Integer businessId, String pageId, Boolean autoPostingEnabled, Integer starVal, Integer postVal,  Integer userId) throws Exception {
		logger.info("Request Received to Update Review Sharing Options for Facebook : BusinessID {} PageID {} and AutoPost Flag {}",businessId, pageId, autoPostingEnabled);
		List<BusinessFBPage> pages = socialFbRepo.findByBusinessIdAndFacebookPageId(businessId, pageId);
		ReviewOptionDto dto = null;
		if (CollectionUtils.isNotEmpty(pages)) {
			if (pages.size() > 1) {
				throw new BirdeyeSocialException(ErrorCodes.FB_MULTIPLE_MAPPING_FOUND, "Facebook page is mapped with multiple locations");
			}
			BusinessFBPage page = pages.get(0);
			if (autoPostingEnabled != null) {
				page.setEnabled(autoPostingEnabled ? 1 : 0);
			} else if (starVal != null) {
				page.setMinRating(starVal);
			} else if (postVal != null) {
				page.setMaxPostAllowed(postVal);
			}
			page.setUpdatedBy(userId);
			page.setUpdatedAt(new Date());
			socialFbRepo.saveAndFlush(page);
			dto = new ReviewOptionDto();
			dto.setAutoPostingEnabled(page.getEnabled() == 1 ? Boolean.TRUE : Boolean.FALSE);
			dto.setLocationId(page.getBusinessId());
			dto.setPageId(page.getFacebookPageId());
			dto.setPostVal(page.getMaxPostAllowed());
			dto.setStarVal(page.getMinRating());
			logger.info("Request Completed to Update Review Sharing Options for Facebook : BusinessID {} PageID {} and AutoPost Flag {}",page.getBusinessId(), page.getFacebookPageId(), page.getEnabled());
		}
		return dto;
	}

	@Override
	public LocationPageMapping getLocationMappingPages(Long enterpriseId, Integer userId,List<Integer> businessIds, Set<String> status,Integer page,Integer size,String search, List<String> includeModules, Integer accountId) throws Exception {
		logger.info("getLocationMappingPages: enterpriseId {} userId {} status {}", enterpriseId, userId, status);
		if ( enterpriseId == null || userId == null || CollectionUtils.isEmpty(businessIds) ) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Invalid value for enterpriseId/userId/businessIds");
		}
		LocationPageMapping response = new LocationPageMapping();
		response.setTotalLocations(businessIds.size());

		List<Integer> totalEnterpriseBusinessIds = new ArrayList<>();
		if(CollectionUtils.isEmpty(businessIds)) {
			BusinessLiteDTO businessLiteDTO = new BusinessLiteDTO();
			businessLiteDTO.setBusinessId(accountId);

			totalEnterpriseBusinessIds = businessUtilService.getBusinessLocationsForEnterpriseUsingLite(businessLiteDTO, null);
			businessIds.addAll(totalEnterpriseBusinessIds);
		}

		if (CollectionUtils.isNotEmpty(totalEnterpriseBusinessIds) || CollectionUtils.isNotEmpty(businessIds)) {
			response.setTotalLocations(businessIds != null ? businessIds.size() : 0);
			if(CollectionUtils.isEmpty(status)) {
				logger.info("[Facebook] blank status received hence returning for business {}",enterpriseId);
				response.setLocationList(new ArrayList<>());
				response.setDisconnectedCount(0);
				response.setUnmapped(0);
				response.setAllPagesMapped(false);
				response.setPermissionIssuePageCount(0);
				return response;
			}
			Boolean toSearch = Objects.nonNull(search) && !search.isEmpty();
			if (CollectionUtils.isNotEmpty(businessIds)){
				List<BusinessFBPage> businessFBPages = socialFbRepo.findByBusinessIdIn(businessIds);
				response.setUnmapped(businessIds.size() - businessFBPages.size());
				if(status.size() > 1) {
					prepareLocationFBPageMappingV2(businessIds, response, status, enterpriseId,page,size,search,toSearch,businessFBPages, includeModules);
				} else{
					prepareMappedOrUnmappedData(businessIds,status,response,enterpriseId,page,size,search,toSearch,businessFBPages, includeModules);
				}

			}else {
				logger.error("[Facebook] No business ids found for userId: {} and enterpriseIds: {}", userId, totalEnterpriseBusinessIds);
				throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, "Business not found.");
			}
		}
		return response;
	}

	private void prepareMappedOrUnmappedData(List<Integer> businessIds, Set<String> status, LocationPageMapping response, Long enterpriseId, Integer page, Integer size,String search,Boolean toSearch,List<BusinessFBPage> businessFBPages, List<String> includeModules) {
		Map<Integer,BusinessFBPage> mappedFbPagesMap = businessFBPages.stream().collect(Collectors.toMap(BusinessFBPage::getBusinessId, Function.identity()));
		List<Integer> fbBusinessIds = new LinkedList<>();
		if(Objects.nonNull(mappedFbPagesMap)){
			fbBusinessIds.addAll(mappedFbPagesMap.keySet());
		}
		Map<Integer, BusinessLocationLiteEntity> businessIdLocationMap;
		businessIds.removeAll(fbBusinessIds);
		if(status.contains(LocationStatusEnum.UNMAPPED.getName())){
			if(toSearch){
				businessIdLocationMap = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(businessIds,page,size,search,response);
			}else{
				businessIdLocationMap = businessUtilService.getBusinessLocationsLiteMapPaginated(businessIds,page,size);
			}
		}else {
			if(toSearch){
				businessIdLocationMap = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(fbBusinessIds,page,size,search,response);
			}else{
				businessIdLocationMap = businessUtilService.getBusinessLocationsLiteMapPaginated(fbBusinessIds,page,size);
			}
		}
		List<ChannelLocationInfo> locationList = new ArrayList<>();
		if(Objects.nonNull(businessIdLocationMap)){
			locationList = getChannelLocationInfoList(businessIdLocationMap,mappedFbPagesMap,response,status,enterpriseId, includeModules);
		}
		locationList.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));
		response.setLocationList(locationList);
		response.setAllPagesMapped(businessIds.size() <= 0);
	}


	@Override
	public Integer getUnmappedLocationCount(UnmappedLocationMappingReq request) {
		Map<Integer, BusinessLocationLiteEntity> businessIdLocationMap = businessUtilService.getBusinessLocationsLiteMap(request.getBusinessIds());
		if (MapUtils.isNotEmpty(businessIdLocationMap)) {
			Map<Integer, BusinessFBPage> businessIdFbPageMap = getBusinessFBPageMap(request.getBusinessIds());
			List<ChannelLocationInfo> unmappedLocList = getUnmappedLocations(businessIdLocationMap, businessIdFbPageMap);
			return unmappedLocList.size();
		}else{
			throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, "No business found for given businessIds");
		}
	}



	private ChannelLocationInfo getChannelLocInfo(BusinessLocationLiteEntity businessLoc) {
		final ChannelLocationInfo locInfo = new ChannelLocationInfo();
		locInfo.setLocationId(businessLoc.getId());
		locInfo.setLocationName(businessLoc.getAlias1() != null ? businessLoc.getAlias1() :businessLoc.getName());
		locInfo.setAddress(commonService.prepareBusinessAddress(businessLoc));
		return locInfo;
	}

	private List<ChannelLocationInfo> getUnmappedLocations(Map<Integer, BusinessLocationLiteEntity> businessIdLocationMap, Map<Integer, BusinessFBPage> businessIdFbPageMap) {
		final List<ChannelLocationInfo> response = new ArrayList<>();
		for ( Map.Entry<Integer, BusinessLocationLiteEntity> entry : businessIdLocationMap.entrySet() ) {
			// Only take this business-location if it does not have any mapping
			if ( businessIdFbPageMap == null || businessIdFbPageMap.isEmpty() || !businessIdFbPageMap.containsKey(entry.getKey()) ) {
				response.add(getChannelLocInfo(entry.getValue()));
			}
		}
		response.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));
		return response;
	}

	private Map<Integer, BusinessFBPage> getBusinessFBPageMap(List<Integer> businessIds) {
		if (CollectionUtils.isNotEmpty(businessIds)) {
			List<BusinessFBPage> fbLocations = socialFbRepo.findByBusinessIdIn(businessIds);
			if (CollectionUtils.isNotEmpty(fbLocations)) {
				Map<Integer, BusinessFBPage> fbLocMap = new HashMap<>();
				for (BusinessFBPage bfb : fbLocations) {
					if (fbLocMap.get(bfb.getBusinessId()) != null) {
						if (fbLocMap.get(bfb.getBusinessId()).getIsValid() == 0 && bfb.getIsValid() == 1) {
							fbLocMap.put(bfb.getBusinessId(), bfb);
						}
					} else {
						fbLocMap.put(bfb.getBusinessId(), bfb);
					}
				}
				return fbLocMap;
			}
		}
		return null;
	}

	private List<ChannelLocationInfo> getChannelLocationInfoList(Map<Integer, BusinessLocationLiteEntity> businessLocationsMap, Map<Integer, BusinessFBPage> mappedFbPagesMap,
																 LocationPageMapping response, Set<String> status,Long enterpriseId, List<String> includeModules) {
		logger.info("Get channel location info for facebook enterprise id :{} ",enterpriseId);
		Integer disconnectedCount = 0;
		int permissionIssuePageCount = 0;
		List<ChannelLocationInfo> locationList = new ArrayList<>();
		Boolean isWebChatEnabled = businessCoreService.isWebChatEnabledByNumber(enterpriseId);

		List<SocialModulePermission> socialModulePermission = new ArrayList<>();
		Map<Integer, PermissionMapping> errorPermissionMapping = new HashMap<>();
		if(CollectionUtils.isNotEmpty(includeModules)) {
			socialModulePermission = socialModulePermissionService
					.getPermissionsForChannelAndModuleIn(SocialChannel.FACEBOOK.getId(), includeModules);
		}

		List<PermissionMapping> permissionMappings = permissionMappingService.getDataByChannelAndPermissionNameNotNull(
				SocialChannel.FACEBOOK.getName());

		PermissionMapping permissionMappingIntegration = permissionMappingService.getDataByChannelAndErrorCode(
				SocialChannel.FACEBOOK.getName(),INTEGRATION);
		List<String> tasks = mappedFbPagesMap.values().stream()
				.map(page -> page.getScope() !=null ? page.getScope().split(" -")[0] : null)
				.filter(v -> Objects.nonNull(v))
				.collect(Collectors.toList());

		List<RoleMapping> roleMapping = roleMappingService.getAllDataByChannelAndRole(
				SocialChannel.FACEBOOK.getName(), tasks);

		Map<String, RoleMapping> fbPagesScopes  = new HashMap<>();

		for(BusinessFBPage page : mappedFbPagesMap.values()) {
			String key  = page.getFacebookPageId();
			String role = page.getScope()!=null ? page.getScope().split(" -")[0] : null;
			RoleMapping rMapping = null;

			Optional<RoleMapping> roleM = roleMapping.stream().filter(r -> r.getRole().equalsIgnoreCase(role)).findFirst();
			if(roleM.isPresent()) {
				rMapping = roleM.get();
			}

			fbPagesScopes.put(key, rMapping);
		}


		List<Integer> errorList = mappedFbPagesMap.values().stream()
				.map(page -> page.getFbErrorSubcode() !=null ? page.getFbErrorSubcode() : null)
				.filter(v -> Objects.nonNull(v))
				.collect(Collectors.toList());
		if(CollectionUtils.isNotEmpty(errorList)) {
			errorPermissionMapping = permissionMappingService.getDataByChannelAndPermissionCodeInAndModule(
							SocialChannel.FACEBOOK.getName(), errorList, INTEGRATION_MODULE).stream()
					.collect(Collectors.toMap(PermissionMapping::getPermissionCode, Function.identity()));
		}

		boolean granularFlag = commonService.checkGranular(enterpriseId);

		for (Map.Entry<Integer, BusinessLocationLiteEntity> entry : businessLocationsMap.entrySet()) {
			ChannelLocationInfo locInfo = getChannelLocInfo(entry.getValue());
			if (MapUtils.isNotEmpty(mappedFbPagesMap) && mappedFbPagesMap.get(entry.getKey()) != null) {
				Map<String, LocationPageListInfo> pageInfoMap = new HashMap<>();
				LocationPageListInfo locationPageListInfo = prepareFBPageData(mappedFbPagesMap.get(entry.getKey()),isWebChatEnabled, socialModulePermission, permissionMappings, permissionMappingIntegration, fbPagesScopes, errorPermissionMapping, granularFlag);
				if(locationPageListInfo.getValidType().equals(ValidTypeEnum.INVALID.getName()) ||
						locationPageListInfo.getValidType().equals(ValidTypeEnum.PARTIAL_VALID.getName()) ) {
					disconnectedCount++;
					if(MESSENGER_NOT_ENABLED.equalsIgnoreCase(locationPageListInfo.getErrorCode()) ||
							(locationPageListInfo.getReadInsightsPermission() != null && !locationPageListInfo.getReadInsightsPermission()))  {
						permissionIssuePageCount++;
					}
				}
				pageInfoMap.put(SocialChannel.FACEBOOK.getName(), locationPageListInfo);
				locInfo.setPageData(pageInfoMap);
			}
			locationList.add(locInfo);
		}
		if(!(status.size() <= 1 && status.contains(LocationStatusEnum.UNMAPPED.getName()))) {
			response.setDisconnectedCount(disconnectedCount);
		}
		response.setPermissionIssuePageCount(permissionIssuePageCount);

		logger.info("Completed channel location info for facebook enterprise id :{} ",enterpriseId);
		return locationList;
	}

	public void prepareLocationFBPageMappingV2(List<Integer> businessIds, LocationPageMapping response, Set<String> status, Long enterpriseId,
											   Integer page,Integer size,String search,Boolean toSearch,List<BusinessFBPage> pages, List<String> includeModules) {
		Map<Integer, BusinessLocationLiteEntity> mapPaginated;
		if(toSearch){
			mapPaginated = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(businessIds,page,size,search,response);
		}else{
			mapPaginated = businessUtilService.getBusinessLocationsLiteMapPaginated(businessIds,page,size);
		}

		logger.info("prepareLocationFBPageMapping: Total businessLocations fetched {}", mapPaginated.size());
		// move to MAP -
		List<BusinessFBPage> businessFBPages = pages.stream().filter(p -> mapPaginated.containsKey(p.getBusinessId())).collect(Collectors.toList());
		Map<Integer, BusinessFBPage> mappedFbPagesMap = new HashMap<>();
		if(Objects.nonNull(businessFBPages)) {
			mappedFbPagesMap = businessFBPages.stream().collect(Collectors.toMap(BusinessFBPage::getBusinessId, Function.identity()));
		}
		List<ChannelLocationInfo> locationList = getChannelLocationInfoList(mapPaginated, mappedFbPagesMap, response, status, enterpriseId, includeModules);

		List<ChannelLocationInfo> locationListWithoutMapping = new LinkedList<>();
		List<ChannelLocationInfo> locationListWithMapping = new LinkedList<>();
		for (ChannelLocationInfo ll : locationList) {
			if (ll.getPageData() != null) {
				locationListWithMapping.add(ll);
			} else {
				locationListWithoutMapping.add(ll);
			}
		}

		locationListWithoutMapping.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));
		locationListWithMapping.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));

		List<ChannelLocationInfo> finalList = new LinkedList<>();
		if(status.contains(LocationStatusEnum.UNMAPPED.getName())) {
			finalList.addAll(locationListWithoutMapping);
		}
		if(status.contains(LocationStatusEnum.MAPPED.getName())) {
			finalList.addAll(locationListWithMapping);
		}
		response.setLocationList(finalList);
	}



	private String prepareBusinessAddress(BusinessLocationEntity location) {
		StringBuilder address = new StringBuilder();
		if (StringUtils.isNotEmpty(location.getAddress1())) {
			address.append(location.getAddress1()).append(", ");

		}
		if (StringUtils.isNotEmpty(location.getAddress2())) {
			address.append(location.getAddress2()).append(", ");

		}
		if (StringUtils.isNotEmpty(location.getCity())) {
			address.append(location.getCity()).append(", ");

		}
		if (StringUtils.isNotEmpty(location.getState())) {
			address.append(location.getState()).append(" ");

		}
		// Zipcode will be always there
		if (StringUtils.isNotEmpty(location.getZip())) {
			address.append(location.getZip());

		}
		return address.toString();
	}

	private LocationPageListInfo preparePageData(BusinessFBPage fbPage, Boolean isWebChatEnabled) {
		if (fbPage == null) {
			return null;
		}
		FacebookPageMetadataDTO facebookPageMetadataDTO = fbSocialService.getFacebookPageDetailsByPageId(fbPage.getFacebookPageId());
		return getLocationPageListInfo(fbPage, facebookPageMetadataDTO, isWebChatEnabled, null);
	}

	private LocationPageListInfo prepareFBPageData(BusinessFBPage fbPage, Boolean isWebChatEnabled, List<SocialModulePermission> socialModulePermission, List<PermissionMapping> permissionMappings, PermissionMapping permissionMappingIntegration, Map<String, RoleMapping> fbPagesScopes, Map<Integer, PermissionMapping> errorPermissionMapping, boolean granularFlag) {
		if (fbPage == null) {
			return null;
		}
		FacebookPageMetadataDTO facebookPageMetadataDTO = fbSocialService.getFacebookPageDetails(fbPage);
		return getLocationPageListInfoV2(fbPage, facebookPageMetadataDTO, isWebChatEnabled, socialModulePermission, permissionMappings, permissionMappingIntegration, fbPagesScopes, errorPermissionMapping, granularFlag);
	}

	private LocationPageListInfo getLocationPageListInfoV2(BusinessFBPage fbPage, FacebookPageMetadataDTO fbPageMetaDTO, Boolean isWebChatEnabled, List<SocialModulePermission> socialModulePermission,  List<PermissionMapping> permissionMappings, PermissionMapping permissionMappingIntegration, Map<String, RoleMapping> fbPagesScopes, Map<Integer, PermissionMapping> errorPermissionMapping, boolean granularFlag) {
		if (fbPage == null || fbPageMetaDTO == null) {
			return null;
		}
		String permission = (granularFlag && StringUtils.isNotEmpty(fbPage.getGranularPagePermissions()))? fbPage.getGranularPagePermissions() :
				fbPage.getPagePermissions();
		LocationPageListInfo pageInfo = new LocationPageListInfo();
		pageInfo.setId(fbPageMetaDTO.getFacebookPageId());
		pageInfo.setImage(fbPageMetaDTO.getFacebookPagePictureUrl());
		pageInfo.setLink(fbPageMetaDTO.getLink());
		pageInfo.setPageName(fbPageMetaDTO.getFacebookPageName());
		pageInfo.setAddress(fbPageMetaDTO.getSingleLineAddress());
		Validity validity = fetchValidityAndErrorMessageByGroupV2(fbPage,isWebChatEnabled, fbPagesScopes, permissionMappings, permissionMappingIntegration, errorPermissionMapping, permission);
		pageInfo.setValidType(validity.getValidType());
		pageInfo.setErrorCode(validity.getErrorCode());
		pageInfo.setErrorMessage(validity.getErrorMessage());
		pageInfo.setConnectedInReseller(Objects.isNull(fbPage.getResellerId())?false:true);

		if (CollectionUtils.isNotEmpty(socialModulePermission)) {
			List<String> permissionList = Objects.isNull(permission)?null:Arrays.asList(permission.split(","));
			logger.info("Fb page: {}, contains permissions: {}",fbPage.getFacebookPageId(),permissionList);
			Map<String, PermissionDTO> permissionsMap = new HashMap<>();
			for (SocialModulePermission module : socialModulePermission) {

				List<String> modulePermissions = new ArrayList<>();
				if(Objects.nonNull(module) && Objects.nonNull(module.getPermissionsNeeded())) {
					modulePermissions = Arrays.asList(module.getPermissionsNeeded().split(","));
				}
				logger.info("For fb page: {}, required permissions for module: {} are: {}",fbPage.getFacebookPageId(),module.getModule(),modulePermissions);
				if(CollectionUtils.isNotEmpty(permissionList) && new HashSet<>(permissionList).containsAll(modulePermissions)) {
					permissionsMap.put(module.getModule(),new PermissionDTO(true));
				} else {
					permissionsMap.put(module.getModule(),new PermissionDTO(false));
				}
			}
			pageInfo.setModulePermission(permissionsMap);
		}
		return pageInfo;
	}


	private LocationPageListInfo getLocationPageListInfo(BusinessFBPage fbPage, FacebookPageMetadataDTO fbPageMetaDTO, Boolean isWebChatEnabled, List<String> includeModules) {
		if (fbPage == null || fbPageMetaDTO == null) {
			return null;
		}
		LocationPageListInfo pageInfo = new LocationPageListInfo();
		pageInfo.setId(fbPageMetaDTO.getFacebookPageId());
		pageInfo.setImage(fbPageMetaDTO.getFacebookPagePictureUrl());
		pageInfo.setLink(fbPageMetaDTO.getLink());
		pageInfo.setPageName(fbPageMetaDTO.getFacebookPageName());
		pageInfo.setAddress(fbPageMetaDTO.getSingleLineAddress());
		Validity validity = fetchValidityAndErrorMessage(fbPage,isWebChatEnabled, null);
		pageInfo.setValidType(validity.getValidType());
		pageInfo.setErrorCode(validity.getErrorCode());
		pageInfo.setErrorMessage(validity.getErrorMessage());
		pageInfo.setConnectedInReseller(Objects.isNull(fbPage.getResellerId())?false:true);

		if (CollectionUtils.isNotEmpty(includeModules)) {
			List<String> permissionList = Objects.isNull(fbPageMetaDTO.getPagePermissions())?null:Arrays.asList(fbPageMetaDTO.getPagePermissions().split(","));
			logger.info("Fb page: {}, contains permissions: {}",fbPage.getFacebookPageId(),permissionList);
			Map<String, PermissionDTO> permissionsMap = new HashMap<>();
			for (String module : includeModules) {

				SocialModulePermission socialModulePermission = socialModulePermissionService
						.getPermissionsForChannelAndModule(SocialChannel.FACEBOOK.getId(), module);
				List<String> modulePermissions = new ArrayList<>();
				if(Objects.nonNull(socialModulePermission) && Objects.nonNull(socialModulePermission.getPermissionsNeeded())) {
					modulePermissions = Arrays.asList(socialModulePermission.getPermissionsNeeded().split(","));
				}
				logger.info("For fb page: {}, required permissions for module: {} are: {}",fbPage.getFacebookPageId(),module,modulePermissions);
				if(CollectionUtils.isNotEmpty(permissionList) && new HashSet<>(permissionList).containsAll(modulePermissions)) {
					permissionsMap.put(module,new PermissionDTO(true));
				} else {
					permissionsMap.put(module,new PermissionDTO(false));
				}
			}
			pageInfo.setModulePermission(permissionsMap);
		}
		return pageInfo;
	}

	@Override
	public void removeFbPage(Map<String, LocationPageMappingRequest> pageToMappingMap) {
		logger.info("Removing facebook page from social, Ids {} ", pageToMappingMap);
		List<BusinessFBPage> fbPages = socialFbRepo.findByFacebookPageIdIn(new ArrayList<>(pageToMappingMap.keySet()));
		logger.info("Count of facebook pages to be removed {} ", fbPages.size());
		removeFacebookPages(fbPages);
	}

	public void removeFacebookPages(List<BusinessFBPage> fbPages) {
		unSubscribeFromFacebook(fbPages);
		socialFbRepo.delete(fbPages);
		socialFbRepo.flush();
		producer.sendObject(Constants.SOCIAL_PAGE_REMOVED, fbPages.stream().map(rawPage -> new ChannelPageRemoved(
				SocialChannel.FACEBOOK.getName(),
				rawPage.getFacebookPageId(),
				rawPage.getFacebookPageName(),
				rawPage.getBusinessId(),
				rawPage.getEnterpriseId(),
				null, null,rawPage.getId()
		)).collect(Collectors.toList()));
		fbPages.forEach(page ->
				commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.REMOVE_PAGE.name(),
						Collections.singletonList(page), page.getUserId(), page.getBusinessId(),page.getEnterpriseId()));
	}

	@Override
	public void removeResellerFbPage(List<String> pageIds,Integer limit) {
		logger.info("Removing facebook page from social, Ids {} ", pageIds);
		List<BusinessFBPage> fbPages = socialFbRepo.findByFacebookPageIdInWithLimit(pageIds,new PageRequest(0,limit));
		logger.info("Count of facebook pages to be removed {} ", fbPages.size());
//		unSubscribeFromFacebook(fbPages);
		socialFbRepo.delete(fbPages);
		socialFbRepo.flush();

		fbPages.forEach(page -> {
//			sendEventToSubscribeFB(page.getFacebookPageId(), false);

			commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.REMOVE_PAGE.name(), Arrays.asList(page), page.getUserId(), page.getBusinessId(), page.getEnterpriseId());
		});

		producer.sendObject(Constants.SOCIAL_PAGE_REMOVED, fbPages.stream().map(rawPage -> new ChannelPageRemoved(
				SocialChannel.FACEBOOK.getName(),
				rawPage.getFacebookPageId(),
				rawPage.getFacebookPageName(),
				rawPage.getBusinessId(),
				rawPage.getResellerId(),
				null, null,rawPage.getId()
		)).collect(Collectors.toList()));
	}

	@Async
	void unSubscribeFromFacebook(List<BusinessFBPage> fbPages) {
		fbPages.forEach(fbPage -> {
			try{
				socialEngageService.deleteStreams(110, new ArrayList<>(Collections.singletonList(fbPage.getId())));
				if(Objects.nonNull(fbPage.getBusinessId())){
					//uninstallTabOnPage(fbPage.getFacebookPageId(), fbPage.getBusinessId(), fbPage.getPageAccessToken());
					Business business = businessRepo.findById(fbPage.getBusinessId());
					Integer enterpriseId = null;
					if(business.getEnterprise() != null) {
						enterpriseId = business.getEnterprise().getId();
					} else {
						enterpriseId = business.getId();
					}

					pushFbStatusInFirebase(enterpriseId, fbPage.getBusinessId(), FacebookIntegrationStatus.CONNECT.getStatus());
				}
				if (fbPage.getMessengerOpted() == 1) {
					List<BusinessInstagramAccount> account = instagramAccountRepository.findByFacebookPageIdAndBusinessIdIsNotNull(fbPage.getFacebookPageId());
					if(CollectionUtils.isEmpty(account)) {
						FacebookBaseResponse response = fbMsgService.fbPageUnsubscribeApps(fbPage.getFacebookPageId(), fbPage.getPageAccessToken());
						if (response.isSuccess()) {
							logger.info("Remove Page, Messenger unsubscribed for pageId: {}", fbPage.getFacebookPageId());
						}
					}
				}

			}catch (Exception ex){
				logger.error("exception occurred while unsubscribing fb events while removeing page {} {}",fbPage,ex);
			}
		});
	}

	private void uninstallTabOnPage(String pageId, Integer locationId, String accessToken) {
		Business business = businessRepo.findById(locationId);
		try {
			socialFacebookService.uninstallTabOnPage(pageId, business, accessToken);
		} catch (Exception ex) {
			logger.error("error occured while tab removal on page {} is : {}", pageId, ex.getMessage());
		}
	}

	/**
	 * Get list of unmapped fb pages
	 *
	 * @param enterpriseId
	 * @return
	 */
	@Override
	public List<SocialPageListInfo> getUnmappedFbPagesByEnterpriseId(Long enterpriseId) {
		// Get connected fb pages
		List<BusinessFBPage> connectedFbPages = socialFbRepo.findByEnterpriseIdAndIsSelected(enterpriseId, 1);
		if (CollectionUtils.isEmpty(connectedFbPages)) {
			logger.info("No pages found for enterpriseId {}", enterpriseId);
			return Collections.emptyList();
		}
		logger.info("Size of pages fetched {} for enterpriseId {}", connectedFbPages.size(), enterpriseId);

		Boolean isWebChatEnabled = businessCoreService.isWebChatEnabledByNumber(enterpriseId);
		logger.info("Status of isWebChatEnabled: {}", isWebChatEnabled);

		List<String> tasks = connectedFbPages.stream()
				.map(page -> page.getScope() !=null ? page.getScope().split(" -")[0] : null)
				.filter(v -> Objects.nonNull(v))
				.collect(Collectors.toList());

		List<RoleMapping> roleMapping = roleMappingService.getAllDataByChannelAndRole(
				SocialChannel.FACEBOOK.getName(), tasks);

		Map<String, RoleMapping> fbPagesScopes  = new HashMap<>();

		for(BusinessFBPage page : connectedFbPages) {
			String key  = page.getFacebookPageId();
			String role = page.getScope()!=null ? page.getScope().split(" -")[0] : null;
			RoleMapping rMapping = null;

			Optional<RoleMapping> roleM = roleMapping.stream().filter(r -> r.getRole().equalsIgnoreCase(role)).findFirst();
			if(roleM.isPresent()) {
				rMapping = roleM.get();
			}

			fbPagesScopes.put(key, rMapping);
		}

		List<PermissionMapping> permissionMappings = permissionMappingService.getDataByChannelAndPermissionNameNotNull(
				SocialChannel.FACEBOOK.getName());

		PermissionMapping permissionMappingIntegration = permissionMappingService.getDataByChannelAndErrorCode(
				SocialChannel.FACEBOOK.getName(),INTEGRATION);

		boolean granularFlag = commonService.checkGranular(enterpriseId);
		List<SocialPageListInfo> unmappedFbPagesFinal = connectedFbPages.stream().map(connectedFbPage -> convertBusinessFBPageToLocationPageListInfo(connectedFbPage,isWebChatEnabled, fbPagesScopes, permissionMappings, permissionMappingIntegration, granularFlag))
				.collect(Collectors.toList());
		logger.info("Size of unmapped pages: {}", unmappedFbPagesFinal.size());
		// sorting
		unmappedFbPagesFinal.sort(Comparator.comparing(SocialPageListInfo::getPageName, nullsFirst(Comparator.naturalOrder())));
		return unmappedFbPagesFinal;
	}

	/**
	 * Comverter utility to convert BusinessFBPage into LocationPageListInfo
	 *
	 * @param fbPage
	 * @return
	 */
	private SocialPageListInfo convertBusinessFBPageToLocationPageListInfo(BusinessFBPage fbPage,Boolean isWebChatEnabled, Map<String, RoleMapping> fbPagesScopes, List<PermissionMapping> permissionMappings, PermissionMapping permissionMappingIntegration, boolean granularFlag) {
		if (fbPage == null) {
			return null;
		}
		SocialPageListInfo locationPageListInfo = new SocialPageListInfo();
		locationPageListInfo.setId(fbPage.getFacebookPageId());
		locationPageListInfo.setMapped(fbPage.getBusinessId() != null ? Boolean.TRUE : Boolean.FALSE);
		locationPageListInfo.setPageName(fbPage.getFacebookPageName());
		locationPageListInfo.setLink(fbPage.getLink());
		locationPageListInfo.setAddress(!StringUtils.isBlank(fbPage.getSingleLineAddress()) ? fbPage.getSingleLineAddress() : fbPage.getPrimaryPhone());
		locationPageListInfo.setImage(fbPage.getFacebookPagePictureUrl());
		String permission = (granularFlag && StringUtils.isNotEmpty(fbPage.getGranularPagePermissions()))? fbPage.getGranularPagePermissions() :
				fbPage.getPagePermissions();
		Validity validity = fetchValidityAndErrorMessageByGroup(fbPage, isWebChatEnabled, fbPagesScopes, permissionMappings, permissionMappingIntegration, permission);

		locationPageListInfo.setValidType(validity.getValidType());
		locationPageListInfo.setErrorCode(validity.getErrorCode());
		locationPageListInfo.setErrorMessage(validity.getErrorMessage());
		locationPageListInfo.setConnectedInReseller(Objects.isNull(fbPage.getResellerId())?false:true);
		return locationPageListInfo;
	}

	@Override
	public void copyPageToEnterprise() {
		List<String> existingRawPages = socialFbRepo.findAllSelectedPageIds();
		List<String> existingUnmappedRawPages = socialFbRepo.findAllUnmappedPageIds();
		Map<Integer, Long> businessToEnterpriseMap = new HashMap<>();
		List<BusinessFacebookPageNew> pagesToCopy = null;
		if (CollectionUtils.isNotEmpty(existingRawPages)) {
			pagesToCopy = businessFbPageRepo.findByFacebookPageIdNotIn(existingRawPages);
		} else {
			pagesToCopy = businessFbPageRepo.findAll();
		}

		logger.info("pagesToCopy size is :: {}", CollectionUtils.size(pagesToCopy));
		if (CollectionUtils.isNotEmpty(pagesToCopy)) {
			Map<String, List<BusinessFacebookPageNew>> userIdToPage = pagesToCopy.stream().filter(page -> page.getFacebookUserId() != null)
					.collect(Collectors.groupingBy(BusinessFacebookPageNew::getFacebookUserId));
			userIdToPage.keySet().stream().forEach(userId -> {
				try {
					savePage(userId, userIdToPage.get(userId), businessToEnterpriseMap, existingUnmappedRawPages);
				} catch (Exception e) {
					logger.error("Exception thrown while copying data to raw table : {}", e.getMessage());
				}
			});
		}

	}

	private void savePage(String userId, List<BusinessFacebookPageNew> pages, Map<Integer, Long> businessToEnterpriseMap, List<String> existingUnmappedRawPages)
			throws BirdeyeSocialException, Exception {
		String userAccess = null;
		FbUserProfileInfo userInfo = null;
		logger.info("existingUnmappedRawPages size for user id {} is :: {}", userId, CollectionUtils.size(pages));
		if (CollectionUtils.isNotEmpty(pages)) {
			userAccess = pages.get(0).getAccessToken();
			try {
				userInfo = fbService.getUserDetails(getFacebookGraphApiBaseUrl(), userId, userAccess);
			} catch (Exception ex) {
				logger.warn("Exception while fetching user details for user {} :", userId);
				userInfo = new FbUserProfileInfo();
				userInfo.setId(userId);
			}
			FbUserProfileInfo userInfoNew = new FbUserProfileInfo(userInfo);
			Integer count = 0;
			pages.stream().forEach(page -> {
				Long enterpriseId = null;
				if (businessToEnterpriseMap.get(page.getBusinessId()) != null) {
					enterpriseId = businessToEnterpriseMap.get(page.getBusinessId());
				} else {
					Business business = businessRepo.findById(page.getBusinessId());
					if (business.getEnterpriseId() == null) {
						enterpriseId = business.getBusinessId();
					} else {
						enterpriseId = business.getEnterprise().getBusinessId();
					}
					businessToEnterpriseMap.put(page.getBusinessId(), enterpriseId);
				}
				try {
					boolean alreadyPresent = existingUnmappedRawPages.contains(page.getFacebookPageId());
					savePage(userInfoNew, page, enterpriseId, alreadyPresent);
					incrementCount(count);
				} catch (Exception e) {
					logger.error("Exception thrown while copying data for user {} to raw table : {}", userId, e.getMessage());
				}
			});
			logger.info("no of migrated pages for user id {} is :: {}", userId, CollectionUtils.size(pages));
		}
	}

	private void incrementCount(Integer count) {
		count++;
	}

	private void savePage(FbUserProfileInfo userInfo, BusinessFacebookPageNew page, long enterpriseId, Boolean alreadyPresent) throws Exception {
		logger.info("savePage :{}  for location id {}, enterprise id {}", page.getFacebookPageId(), page.getBusinessId(), enterpriseId);
		BusinessFBPage fbPage = null;
		// If the page is already present in raw table and unmapped then map it with the current enterprise
		if (alreadyPresent) {
			logger.info("page {}, is already present in social table", page.getFacebookPageId());
			fbPage = socialFbRepo.findByFacebookPageId(page.getFacebookPageId()).get(0);
			fbPage.setIsSelected(1);
			fbPage.setEnterpriseId(enterpriseId);
			fbPage.setIsValid(page.getIsValid());
			if(fbPage.getIsValid().equals(0)) {
				brokenIntegrationService.pushValidIntegrationStatus(fbPage.getEnterpriseId(),SocialChannel.FACEBOOK.getName(),fbPage.getId(),0,fbPage.getFacebookPageId());
				commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(fbPage), fbPage.getUserId(),fbPage.getBusinessId(), fbPage.getEnterpriseId());
			}
		} else {
			// If the page is not present in raw table then create new entry
			logger.info("page {}, is not present in social table", page.getFacebookPageId());
			fbPage = new BusinessFBPage(page.getFacebookPageId(), page.getLink(), page.getAccessToken(), page.getFacebookPageName(), page.getProfilePictureUrl(), null, userInfo.getId(), null,
					userInfo.getName(), null, enterpriseId, 1, page.getIsValid(), null);
		}
		socialFbRepo.saveAndFlush(fbPage);
		pushToKafkaForValidity(Constants.FACEBOOK,Arrays.asList(fbPage.getFacebookPageId()) );
	}

	@Override
	public void removeDuplicatePages() {
		List<BusinessFacebookPageNew> pagesToBeDeleted = new ArrayList<>();
		List<DuplicatePageDTO> duplicatePages = businessFbPageRepo.findDuplicateEntries();
		if (CollectionUtils.isNotEmpty(duplicatePages)) {
			duplicatePages.stream().forEach(page -> pagesToBeDeleted.addAll(getDuplicatePagesToRemove(page.getPageId(), page.getLocationId())));
		}
		if (CollectionUtils.isNotEmpty(pagesToBeDeleted)) {
			businessFbPageRepo.delete(pagesToBeDeleted);
		}

		List<BusinessFBPage> rawPagesToBeDeleted = new ArrayList<>();
		List<DuplicatePageDTO> duplicateRawPages = socialFbRepo.findDuplicateEntries();
		if (CollectionUtils.isNotEmpty(duplicateRawPages)) {
			duplicateRawPages.stream().forEach(page -> rawPagesToBeDeleted.addAll(getDuplicatePagesToRemove(page.getPageId(), page.getEnterpriseId())));
		}
		if (CollectionUtils.isNotEmpty(rawPagesToBeDeleted)) {
			socialFbRepo.delete(rawPagesToBeDeleted);
		}
	}

	private List<BusinessFacebookPageNew> getDuplicatePagesToRemove(String pageId, Integer locationId) {
		// TODO : Log
		List<BusinessFacebookPageNew> pages = businessFbPageRepo.findByBusinessIdAndFacebookPageIdOrderByUpdatedAtDesc(locationId, pageId);
		pages.remove(0);
		return pages;
	}

	private List<BusinessFBPage> getDuplicatePagesToRemove(String pageId, Long enterpriseId) {
		// TODO : Log
		List<BusinessFBPage> pages = socialFbRepo.findByEnterpriseIdAndFacebookPageIdOrderByUpdatedAtDesc(enterpriseId, pageId);
		pages.remove(0);
		return pages;
	}

	@Override
	public void removeMultiPage() {
		List<BusinessFacebookPageNew> pagesToBeDeleted = new ArrayList<>();
		List<DuplicatePageDTO> multiPageBusiness = businessFbPageRepo.findMultiPageEntries();
		List<Integer> businessIds = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(multiPageBusiness)) {
			multiPageBusiness.stream().forEach(x -> businessIds.add(x.getLocationId()));
			List<BusinessFacebookPageNew> pages = businessFbPageRepo.findAllByBusinessIdIn(businessIds);
			List<BusinessAggregation> aggregation = businessUtilService.getAggregationForBusinessAndSource(businessIds, 110);
			Map<Integer, List<BusinessFacebookPageNew>> businessToPage = pages.stream().collect(Collectors.groupingBy(BusinessFacebookPageNew::getBusinessId));
			Map<Integer, List<BusinessAggregation>> buinessToAggrMap = aggregation.stream().collect(Collectors.groupingBy(BusinessAggregation::getBusinessId));
			businessToPage.keySet().stream().forEach(businessId -> {
				List<BusinessFacebookPageNew> pagesForBusiness = businessToPage.get(businessId);
				Collections.sort(pagesForBusiness);
				pagesToBeDeleted.addAll(getListOfPagesToBeDeleted(pagesForBusiness, buinessToAggrMap.get(businessId)));
			});

			if (CollectionUtils.isNotEmpty(pagesToBeDeleted)) {
				businessFbPageRepo.delete(pagesToBeDeleted);
			}
		}
	}

	private List<BusinessFacebookPageNew> getListOfPagesToBeDeleted(List<BusinessFacebookPageNew> pagesForBusiness, List<BusinessAggregation> aggregations) {
		List<BusinessFacebookPageNew> matchedPages = new ArrayList<>();
		if (CollectionUtils.isEmpty(aggregations)) {
			pagesForBusiness.remove(0);
			return pagesForBusiness;
		}
		pagesForBusiness.stream().forEach(page -> {
			aggregations.forEach(aggregation -> {
				if (ControllerUtils.isMatched(page, aggregation)) {
					matchedPages.add(page);
				}
			});
		});

		if (CollectionUtils.isEmpty(matchedPages)) {
			pagesForBusiness.remove(0);
		} else {
			pagesForBusiness.remove(matchedPages.get(0));
		}
		return pagesForBusiness;
	}

	@Override
	public void copyPageToEnterpriseWithNullUser() {
		List<String> existingRawPages = socialFbRepo.findAllSelectedPageIds();
		List<String> existingUnmappedRawPages = socialFbRepo.findAllUnmappedPageIds();
		Map<String, FbUserProfileInfo> userIdsMap = new HashMap<>();
		Map<Integer, Long> businessToEnterpriseMap = new HashMap<>();
		List<BusinessFacebookPageNew> pagesToCopy = null;
		if (CollectionUtils.isNotEmpty(existingRawPages)) {
			pagesToCopy = businessFbPageRepo.findByFacebookPageIdNotIn(existingRawPages);
		} else {
			pagesToCopy = businessFbPageRepo.findAll();
		}

		logger.info("pagesToCopy size is :: {}", CollectionUtils.size(pagesToCopy));
		if (CollectionUtils.isNotEmpty(pagesToCopy)) {
			pagesToCopy.stream().forEach(page -> {
				try {
					savePage(userIdsMap, page, businessToEnterpriseMap, existingUnmappedRawPages);
				} catch (Exception e) {
					logger.error("Exception thrown while copying data for page {} to raw table : {}", page.getFacebookPageId(), e.getMessage());
				}
			});

		}

	}

	private void savePage(Map<String, FbUserProfileInfo> userIdsMap, BusinessFacebookPageNew page, Map<Integer, Long> businessToEnterpriseMap, List<String> existingUnmappedRawPages)
			throws BirdeyeSocialException, Exception {
		logger.info("existingUnmappedRawPages size :: {}", CollectionUtils.size(existingUnmappedRawPages));
		FbUserProfileInfo userInfo = null;
		if (page.getFacebookUserId() != null) {
			if (userIdsMap.containsKey(page.getFacebookUserId())) {
				userInfo = userIdsMap.get(page.getFacebookUserId());
			} else {
				try {
					userInfo = fbService.getUserDetails(getFacebookGraphApiBaseUrl(), page.getFacebookUserId(), page.getAccessToken());
					userIdsMap.put(page.getFacebookUserId(), userInfo);
				} catch (Exception ex) {
					userIdsMap.put(page.getFacebookUserId(), null);
					userInfo = new FbUserProfileInfo();
					userInfo.setId(page.getFacebookUserId());
				}
			}
		}
		savePage(businessToEnterpriseMap, page, userInfo, existingUnmappedRawPages);
	}

	private void savePage(Map<Integer, Long> businessToEnterpriseMap, BusinessFacebookPageNew page, FbUserProfileInfo userInfo, List<String> existingUnmappedRawPages) throws Exception {
		Long enterpriseId;
		if (businessToEnterpriseMap.get(page.getBusinessId()) != null) {
			enterpriseId = businessToEnterpriseMap.get(page.getBusinessId());
		} else {
			Business business = businessRepo.findById(page.getBusinessId());
			if (business.getEnterpriseId() == null) {
				enterpriseId = business.getBusinessId();
			} else {
				enterpriseId = business.getEnterprise().getBusinessId();
			}
			businessToEnterpriseMap.put(page.getBusinessId(), enterpriseId);
		}
		if (userInfo == null) {
			userInfo = new FbUserProfileInfo();
		}
		savePage(userInfo, page, enterpriseId, existingUnmappedRawPages.contains(page.getFacebookPageId()));
	}

	@Override
	public void copyPageToEnterprise(Long enterpriseId) {
		// Existing mapped pages in raw table.
		List<String> existingRawPages = socialFbRepo.findAllByEnterpriseIdAndSelectedPageIds(enterpriseId);
		logger.info("Mapped Social Pages for enterprise {} is :: {}", enterpriseId, existingRawPages.size());
		// Existing unmapped pages in raw table.
		List<String> existingUnmappedRawPages = socialFbRepo.findAllUnmappedPageIds();
		logger.info("UnMapped Social Pages for enterprise {} is :: {}", enterpriseId, existingUnmappedRawPages.size());
		Map<Integer, Long> businessToEnterpriseMap = new HashMap<>();
		List<BusinessFacebookPageNew> pagesToCopy = null;
		Business enterprise = businessRepo.findByBusinessId(enterpriseId);
		List<Integer> businessIds = businessUtilService.getBusinessLocationsForEnterprise(enterprise, null);
		if (CollectionUtils.isNotEmpty(existingRawPages)) {
			pagesToCopy = businessFbPageRepo.findByFacebookPageIdNotInAndBusinessIdIn(existingRawPages, businessIds);
		} else {
			pagesToCopy = businessFbPageRepo.findAllByBusinessIdIn(businessIds);
		}

		logger.info("pagesToCopy size for enterprise {} is :: {}", enterpriseId, CollectionUtils.size(pagesToCopy));
		if (CollectionUtils.isNotEmpty(pagesToCopy)) {
			Map<String, List<BusinessFacebookPageNew>> userIdToPage = pagesToCopy.stream().filter(page -> page.getFacebookUserId() != null)
					.collect(Collectors.groupingBy(BusinessFacebookPageNew::getFacebookUserId));
			userIdToPage.keySet().stream().forEach(userId -> {
				try {
					savePage(userId, userIdToPage.get(userId), businessToEnterpriseMap, existingUnmappedRawPages);
				} catch (Exception e) {
					logger.error("Exception thrown while copying data to raw table : {}", e.getMessage());
				}
			});
		}

	}

	@Override
	public void syncBusinessFacebookPageWithRawTableForEnterprise(Long enterpriseId) {
		// Existing mapped pages in raw table.
		List<BusinessFBPage> existingRawPages = null;
		List<BusinessFacebookPageNew> pagesToSync = null;
		if (enterpriseId != null) {
			Business enterprise = businessRepo.findByBusinessId(enterpriseId);
			List<Integer> businessIds = businessUtilService.getBusinessLocationsForEnterprise(enterprise, null);
			logger.info("Total Business for enterprise {} is :: {}", enterpriseId, businessIds != null ? businessIds.size() : 0);
			existingRawPages = socialFbRepo.findByEnterpriseIdAndIsSelected(enterpriseId, 1);
			logger.info("Total selected FB Pages for enterprise {} is :: {}", enterpriseId, existingRawPages.size());
			pagesToSync = businessFbPageRepo.findAllByBusinessIdIn(businessIds);
		} else {
			existingRawPages = socialFbRepo.findByIsSelected(1);
			pagesToSync = businessFbPageRepo.findByIsValid(1);
			logger.info("Total selected FB Pages is :: {}", existingRawPages.size());
		}

		if (CollectionUtils.isEmpty(existingRawPages)) {
			logger.error("No FB Pages are found in system");
			return;
		}
		Map<String, BusinessFBPage> fbrowPageMap = existingRawPages.stream().collect(Collectors.toMap(fb -> fb.getFacebookPageId(), fb -> fb, (x, y) -> x));
		if (CollectionUtils.isNotEmpty(pagesToSync)) {
			for (BusinessFacebookPageNew fbpg : pagesToSync) {
				if (fbrowPageMap.get(fbpg.getFacebookPageId()) != null) {
					logger.info("FB Page {} found in raw table", fbpg.getFacebookPageId());
					/*
					 * access_token
					 * link
					 * profile_picture_url
					 * single_line_address
					 * facebook_page_name
					 */
					fbpg.setAccessToken(fbrowPageMap.get(fbpg.getFacebookPageId()).getPageAccessToken());
					fbpg.setLink(fbrowPageMap.get(fbpg.getFacebookPageId()).getLink());
					fbpg.setProfilePictureUrl(fbrowPageMap.get(fbpg.getFacebookPageId()).getFacebookPagePictureUrl());
					fbpg.setSingleLineAddress(fbrowPageMap.get(fbpg.getFacebookPageId()).getSingleLineAddress());
					fbpg.setFacebookPageName(fbrowPageMap.get(fbpg.getFacebookPageId()).getFacebookPageName());
					fbpg.setIsValid(fbrowPageMap.get(fbpg.getFacebookPageId()).getIsValid());
					// Save Part
					businessFbPageRepo.saveAndFlush(fbpg);
				} else {
					logger.info("FB Page {} not found in raw table", fbpg.getFacebookPageId());
				}
			}
		}
	}

	@Override
	public void updateUser() {
		List<String> fbPageIds = socialFbRepo.findAllByUserIdNull();
		List<BusinessFacebookPageNew> addedData = businessFbPageRepo.findByFacebookPageIdIn(fbPageIds);
		Map<String, List<BusinessFacebookPageNew>> pageIdToPage = addedData.stream().filter(page -> page.getFacebookUserId() != null)
				.collect(Collectors.groupingBy(BusinessFacebookPageNew::getFacebookPageId));

		List<BusinessFBPage> pages = socialFbRepo.findByFacebookPageIdIn(fbPageIds);
		Map<String, List<BusinessFBPage>> rawPageIdToPage = pages.stream().collect(Collectors.groupingBy(BusinessFBPage::getFacebookPageId));
		fbPageIds.stream().forEach(pageId -> {
			if (pageIdToPage.containsKey(pageId)) {
				saveUser(rawPageIdToPage.get(pageId).get(0), pageIdToPage.get(pageId).get(0));
			}
		});

	}

	private void saveUser(BusinessFBPage rawPage, BusinessFacebookPageNew fbPage) {
		rawPage.setUserId(fbPage.getFacebookUserId());
		socialFbRepo.saveAndFlush(rawPage);
	}

	private List<BusinessGetPageRequest> getRequestForBusiness(Long businessId, String status, String requestType) {
		return businessGetPageReqRepo.findByEnterpriseIdAndStatusAndChannelAndRequestType(businessId, status, SocialChannel.FACEBOOK.getName(), requestType);
	}

	private List<BusinessGetPageRequest> getRequestForReseller(Long resellerId, String status, String requestType) {
		return businessGetPageReqRepo.findByResellerIdAndStatusAndChannelAndRequestType(resellerId, status, SocialChannel.FACEBOOK.getName(), requestType);
	}

	private List<BusinessGetPageOpenUrlRequest> getRequestForBusinessOpenUrl(Long businessId, String status, String requestType) {
		return businessGetPageOpenUrlReqRepo.findByEnterpriseIdAndStatusAndChannelAndRequestType(businessId, status, SocialChannel.FACEBOOK.getName(), requestType);
	}

	private Boolean isUnderProcess(Long businessId, String requestType) {
		List<BusinessGetPageRequest> underProcessRequests = getRequestForBusiness(businessId, Status.INITIAL.getName(), requestType);
		return CollectionUtils.isNotEmpty(underProcessRequests);
	}

	private Boolean isFetched(Long businessId, String requestType) {
		if (CONNECT.equalsIgnoreCase(requestType)) {
			List<BusinessGetPageRequest> fetchedRequests = getRequestForBusiness(businessId, Status.FETCHED.getName(), requestType);
			return CollectionUtils.isNotEmpty(fetchedRequests);
		}
		return false;
	}

	@Deprecated
	public Map<String, List<ChannelAccountInfo>> getPages(Long businessId, String requestType) {
		Map<String, List<ChannelAccountInfo>> pageType = new HashMap<>();
		List<ChannelAccountInfo> managed = new ArrayList<>();
		List<BusinessFBPage> fetchedPages = getListOfPages(businessId, requestType);
		if (CollectionUtils.isNotEmpty(fetchedPages)) {
			getAcntsInfo(fetchedPages, managed, null);
			if (CollectionUtils.isNotEmpty(managed))
				pageType.put(SocialChannel.FACEBOOK.getName(), managed);
		}
		return pageType;
	}

	public Map<String, List<ChannelAccountInfo>> getPages(BusinessGetPageRequest businessGetPageRequest, Long enterpriseId){
		Map<String, List<ChannelAccountInfo>> pageType = new HashMap<>();
		List<ChannelAccountInfo> managed = new ArrayList<>();
		List<BusinessFBPage> fetchedPages = socialFbRepo.findByRequestId(businessGetPageRequest.getId().toString());
		if (CollectionUtils.isNotEmpty(fetchedPages)) {
			getAcntsInfo(fetchedPages, managed, enterpriseId);
			if (CollectionUtils.isNotEmpty(managed)) {
				pageType.put(SocialChannel.FACEBOOK.getName(), managed);
			}
		}
		return pageType;
	}

	@Override
	public Map<String, Object> getPaginatedPages(BusinessGetPageRequest businessGetPageRequest, Integer pageNumber, Integer pageSize,String search){
		Map<String, Object> pageData = new HashMap<>();
		Map<String, List<ChannelAccountInfoLite>> pageType = new HashMap<>();
		List<ChannelAccountInfoLite> managed = new ArrayList<>();

		// fetchedPages =  defined pageNumber and pageSize to get pagableResult
		FacebookLiteDto fetchedPages = Objects.nonNull(search) && !search.isEmpty() ?
				socialFacebookService.convertToFBObject(search,new PageRequest(pageNumber, pageSize,Sort.Direction.ASC, IS_SELECTED),businessGetPageRequest.getId().toString()):
				socialFacebookService.findByRequestIdOrderByCreatedAt(businessGetPageRequest.getId().toString(), new PageRequest(pageNumber, pageSize, Sort.Direction.ASC, IS_SELECTED));
		if (CollectionUtils.isNotEmpty(fetchedPages.getPageLites())) {
			fetchedPages.getPageLites().forEach(page -> managed.add(getResellerAccInfo(page)));
			if (CollectionUtils.isNotEmpty(managed))
				pageType.put(SocialChannel.FACEBOOK.getName(), managed);
		}
		pageData.put("pageType", pageType);
		pageData.put("totalCount", fetchedPages.getTotalElements());
		pageData.put("pageCount", fetchedPages.getTotalPages());
		return pageData;
	}



	@Deprecated
	private List<BusinessFBPage> getListOfPages(Long businessId, String requestType) {
		List<BusinessFBPage> pages = null;
		List<BusinessGetPageRequest> fetchedRequests = getRequestForBusiness(businessId, Status.FETCHED.getName(), requestType);
		if (CollectionUtils.isNotEmpty(fetchedRequests)) {
			if (fetchedRequests.size() > 1) {
				logger.error("multiple fetched rows are not possible for any business");
				throw new BirdeyeSocialException("fetched accounts for multiple social users");
			}
			BusinessGetPageRequest getRequest = fetchedRequests.get(0);
			pages = socialFbRepo.findByRequestId(getRequest.getId().toString());
			if (CollectionUtils.isEmpty(pages)) {
				getRequest.setStatus(Status.COMPLETE.getName());
				getRequest.setUpdated(new Date());
				businessGetPageReqRepo.saveAndFlush(getRequest);
				releaseLock(getRequest.getChannel(), getRequest.getEnterpriseId());
			}
		}
		return pages;
	}

	@Override
	public void getAcntsInfo(List<BusinessFBPage> fetchedPages, List<ChannelAccountInfo> managed, Long enterpriseId) {
		fetchedPages.stream().forEach(page -> {
			managed.add(getAcntInfo(page,enterpriseId ));
		});
	}

	private ChannelAccountInfo getAcntInfo(BusinessFBPage fetchedPage, Long enterpriseId) {
		ChannelAccountInfo accountInfo = new ChannelAccountInfo();
		accountInfo.setId(fetchedPage.getFacebookPageId());
		accountInfo.setPageName(fetchedPage.getFacebookPageName());
		accountInfo.setLink(fetchedPage.getLink());
		accountInfo.setAddress(fetchedPage.getSingleLineAddress());
		accountInfo.setImage(fetchedPage.getFacebookPagePictureUrl());
		accountInfo.setHandle(fetchedPage.getHandle());
		accountInfo.setDisabled((fetchedPage.getIsSelected() != null && fetchedPage.getIsSelected() == 1) ? Boolean.TRUE : Boolean.FALSE);
		if(Objects.nonNull(fetchedPage.getEnterpriseId()) && Objects.nonNull(fetchedPage.getIsSelected())
				&& fetchedPage.getIsSelected() == 1
				&& Objects.nonNull(enterpriseId)) {
			commonService.setCommonChannelAccountInfo(accountInfo, fetchedPage.getBusinessId(), fetchedPage.getIsSelected(),
					enterpriseId, fetchedPage.getEnterpriseId());
		}
		if (Objects.nonNull(fetchedPage.getEnterpriseId()) && Objects.nonNull(enterpriseId)) {
			accountInfo.setSameAccountConnections(fetchedPage.getEnterpriseId().equals(enterpriseId));
			accountInfo.setDiffAccountConnections(!fetchedPage.getEnterpriseId().equals(enterpriseId));
		}
		return accountInfo;
	}
	//private ChannelAccountInfoLite getResellerAccInfo(BusinessFBPageLite fetchedPage) {
	private ChannelAccountInfoLite getResellerAccInfo(BusinessFBPage fetchedPage) {
		ChannelAccountInfoLite accountInfo = new ChannelAccountInfoLite();
		accountInfo.setId(fetchedPage.getFacebookPageId());
		accountInfo.setPageName(fetchedPage.getFacebookPageName());
		accountInfo.setLink(fetchedPage.getLink());
		accountInfo.setAddress(fetchedPage.getSingleLineAddress());
		accountInfo.setDisabled((fetchedPage.getIsSelected() != null && fetchedPage.getIsSelected() == 1) ? Boolean.TRUE : Boolean.FALSE);
		accountInfo.setParentId(Objects.nonNull(fetchedPage.getResellerId())?fetchedPage.getResellerId():fetchedPage.getEnterpriseId());
		return accountInfo;
	}

	@Override
	public SSOResponse getFbSSOAuthDetails(FacebookSSOAuthRequest fbSSOAuthRequest) {
		SocialAppCredsInfo socialAppFbCreds = getFacebookCreds();
		FacebookCreds creds = new FacebookCreds(socialAppFbCreds.getChannelClientId(), socialAppFbCreds.getChannelClientSecret(),
				fbSSOAuthRequest.getToken(), fbSSOAuthRequest.getRedirectUri(), getFacebookGraphApiBaseUrl());
		try {
			String extendedToken = fbService.getExtendedFacebookToken(creds);
			if(StringUtils.isEmpty(extendedToken)) {
				logger.info("Unable to generate extended token!!");
				throw new BirdeyeSocialException("Unable to generate extended token");
			}
			return getFacebookUserDetailsByAccessToken(extendedToken);
		} catch (BirdeyeSocialException e) {
			logger.info("BirdeyeException while getting fb info: {}", e.getMessage());
			throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN);
		} catch (Exception e) {
			logger.info("Exception while getting fb info: {}", e.getMessage());
			throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR);
		}
	}

	@Override
	public SSOResponse getFacebookUserDetailsByAccessToken(String extendedToken) {
		SSOResponse fbSSOResponse = new SSOResponse();
		try {
			FbUserProfileInfo user = fbService.getUserDetails(extendedToken);
			if(Objects.isNull(user)) {
				logger.info("Unable to get user info!!");
				throw new BirdeyeSocialException("Unable to get user info");
			}
			fbSSOResponse.setEmail(user.getEmail());
			fbSSOResponse.setAccessToken(extendedToken);
			fbSSOResponse.setName(user.getName());
			if(user.getPicture() != null && user.getPicture().getUrl() != null) {
				fbSSOResponse.setPictureUrl(user.getPicture().getUrl());
			}
		} catch (BirdeyeSocialException e) {
			logger.info("BirdEyeException while getting fb user info: {}", e.getMessage());
			throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN);
		} catch (Exception e) {
			logger.info("Exception while getting fb user info: {}", e.getMessage());
			throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR);
		}
		return fbSSOResponse;
	}
	@Override
	public void submitFetchPageRequest(ChannelAuthRequest channelAuthRequest, String type) {
		Long parentId = channelAuthRequest.getBusinessId();
		Integer birdeyeUser = channelAuthRequest.getBirdeyeUserId();
		String fbGraphApiBaseUrl = getFacebookGraphApiBaseUrl();
		FacebookCreds creds;
		String key = SocialChannel.FACEBOOK.getName().concat(String.valueOf(parentId));
		boolean lock = redisService.tryToAcquireLock(key);
		logger.info("[Redis Lock] Lock status {} for key {}", lock, key);
		BusinessGetPageRequest request = (type.equalsIgnoreCase(ENTERPRISE)) ? businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(parentId, SocialChannel.FACEBOOK.getName(),CONNECT)
				: businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(parentId, SocialChannel.FACEBOOK.getName(),CONNECT);
		if (lock) {
			try {
				List<String> statusList = Arrays.asList(Status.INITIAL.getName(), Status.FETCHED.getName());
				if (Objects.isNull(request) || !statusList.contains(request.getStatus())) {
					pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),CONNECT,Status.INITIAL.getName(),parentId);
					Business business = businessRepo.findByBusinessId(parentId);
					SocialAppCredsInfo socialAppFbCreds = getFacebookCreds();

					if(StringUtils.isNotEmpty(channelAuthRequest.getTempAccessToken())) {
						creds = new FacebookCreds(socialAppFbCreds.getChannelClientId(), socialAppFbCreds.getChannelClientSecret(),
								channelAuthRequest.getTempAccessToken(), fbGraphApiBaseUrl);
					} else {
						creds = new FacebookCreds(socialAppFbCreds.getChannelClientId(), socialAppFbCreds.getChannelClientSecret(),
								channelAuthRequest.getAuthCode(), channelAuthRequest.getRedirectUri(), fbGraphApiBaseUrl);
					}

					String extendedToken = fbService.getExtendedFacebookToken(creds);
					if(StringUtils.isEmpty(extendedToken))  {
						logger.info("Extended token could not be retrieved, cancelling the fetch request");
						throw new Exception("Invalid extended token");
					}
					logger.info(EXTENDED_TOKEN, extendedToken);

					logger.info(SOCIAL_SETUP_GETTING_USERS_FOR_BUSINESS_EXTENDED_TOKEN, business.getId(), extendedToken);
//					String baseUrl = getFacebookGraphApiBaseUrl();
					FbUserProfileInfo user = fbService.getUserDetails(extendedToken);
					logger.info(FOR_BUSINESS_AND_EXTENDED_TOKEN_USER_PROFILE_ID, parentId, extendedToken, (user != null ? user.getId() : null));
					if(Objects.isNull(user)) {
						throw new Exception("Failed to get user info!!");
					}
					String userTokenPermissions = createUserTokenPermissions(user);
					request = createBusinessGetPageRequest(birdeyeUser,user,parentId,extendedToken,userTokenPermissions,type);

					businessGetPageReqRepo.saveAndFlush(request);
					Long enterpriseId = type.equals(ENTERPRISE) ? request.getEnterpriseId() : request.getResellerId();
					fbPageService.fetchPages(request, extendedToken, user, business, ENTERPRISE, enterpriseId, false);
				}else{
					logger.info("[Facebook] BusinessGetPageRequest found with status init or fetched for parent id {}",parentId);
					pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),request.getRequestType(),request.getStatus(),parentId);
					redisService.release(key);
				}
			} catch (Exception e) {
				// Cleanup redis cache for error cases.
				redisService.release(key);
				logger.error("[Redis Lock] (Facebook) Lock released for business {} exception {}", parentId, e);
				request = (type == ENTERPRISE) ? businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(parentId, SocialChannel.FACEBOOK.getName(),CONNECT)
						: businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(parentId, SocialChannel.FACEBOOK.getName(),CONNECT);
				if (request != null) {
					pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),request.getRequestType(),Status.COMPLETE.getName(),parentId,true);
					request.setErrorLog(e.getMessage()!=null?e.getMessage().substring(0, Math.min(e.getMessage().length(), 4000)):null);
					request.setStatus(Status.CANCEL.getName());
					businessGetPageReqRepo.saveAndFlush(request);
				}else{
					pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),CONNECT,Status.COMPLETE.getName(),parentId,true);
				}
			}
		} else {
			logger.info("[Redis Lock] (Facebook) Lock is already acquired for business {}", parentId);
			handleFailureLock(request,SocialChannel.FACEBOOK.getName(), key,parentId,CONNECT);
		}
	}


	@Override
	public void submitSystemUserFetchPageRequest(ChannelAuthRequest authRequest, String type) throws Exception {
		Long parentId = authRequest.getBusinessId();
		Integer birdeyeUser = Objects.nonNull(authRequest.getBirdeyeUserId())?authRequest.getBirdeyeUserId():0;
		String birdeyeUserName=authRequest.getBirdeyeUser();
		String systemUserAccessToken = authRequest.getSystemUserAccessToken();
		String key = SocialChannel.FACEBOOK.getName().concat(String.valueOf(parentId));
		boolean lock = redisService.tryToAcquireLock(key);
		logger.info("[Redis Lock] Lock status : {}",lock);
		BusinessGetPageRequest request = (type.equalsIgnoreCase(ENTERPRISE)) ? businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(parentId, SocialChannel.FACEBOOK.getName(),CONNECT)
				: businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(parentId, SocialChannel.FACEBOOK.getName(),CONNECT);
		if (lock) {
			try {
				List<String> statusList = Arrays.asList(Status.INITIAL.getName(), Status.FETCHED.getName());
				if (Objects.isNull(request) || !statusList.contains(request.getStatus())) {
					pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),CONNECT,Status.INITIAL.getName(),parentId);
					Business business = businessRepo.findByBusinessId(parentId);

					logger.info(SOCIAL_SETUP_GETTING_USERS_FOR_BUSINESS_EXTENDED_TOKEN, business.getId(), systemUserAccessToken);
//					String baseUrl = getFacebookGraphApiBaseUrl();
					FbUserProfileInfo user = fbService.getUserDetails(systemUserAccessToken);
					if(Objects.isNull(user)) {
						throw new Exception("Failed to get user info!!");
					}
					//user.setLast_name(getLastNameFromName(user.getName()));
					user.setLast_name(birdeyeUserName);
					user.setFirst_name("FACEBOOK_SYSTEM_USER");
					logger.info(FOR_BUSINESS_AND_EXTENDED_TOKEN_USER_PROFILE_ID, parentId, systemUserAccessToken, (user != null ? user.getId() : null));
					String userTokenPermissions = createUserTokenPermissions(user);
					request = createBusinessGetPageRequest(birdeyeUser,user,parentId,systemUserAccessToken,userTokenPermissions,type);
					request.setEmail("System User");
					request.setType("FACEBOOK_SYSTEM_USER");

					businessGetPageReqRepo.saveAndFlush(request);
					Long enterpriseId = type.equals(ENTERPRISE) ? request.getEnterpriseId() : request.getResellerId();
					fbPageService.fetchPages(request, systemUserAccessToken, user, business, ENTERPRISE, enterpriseId, true);
				}else{
					logger.info("[Facebook] BusinessSystemUserGetPageRequest found with status init or fetched for parent id {}",parentId);
					pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),request.getRequestType(),request.getStatus(),parentId);
					redisService.release(key);
				}
			} catch (Exception e) {
				// Cleanup redis cache for error cases.
				redisService.release(key);
				logger.error("[Redis Lock] (Facebook) Lock released for business {} exception ", parentId, e);
				request = (type == ENTERPRISE) ? businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(parentId, SocialChannel.FACEBOOK.getName(),CONNECT)
						: businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(parentId, SocialChannel.FACEBOOK.getName(),CONNECT);
				if (request != null) {
					pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),request.getRequestType(),Status.COMPLETE.getName(),parentId,true);
					request.setErrorLog(e.getMessage()!=null?e.getMessage().substring(0, Math.min(e.getMessage().length(), 4000)):null);
					request.setStatus(Status.CANCEL.getName());
					businessGetPageReqRepo.saveAndFlush(request);
				}else{
					pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),CONNECT,Status.COMPLETE.getName(),parentId,true);
				}
			}
		} else {
			logger.info("[Redis Lock] (Facebook) Lock is already acquired for business {}", parentId);
			handleFailureLock(request,SocialChannel.FACEBOOK.getName(), key,parentId,CONNECT);
		}
	}

	private String getLastNameFromName(String name) {
		String[] splitString = name.split(" ");
		if(splitString.length<=1) return null;

		StringBuilder lastName = new StringBuilder(splitString[1]);
		for(int i=2;i<splitString.length;i++) {
			lastName.append(" ");
			lastName.append(splitString[i]);
		}
		return lastName.toString();
	}

	private BusinessGetPageRequest createBusinessGetPageRequest(Integer birdeyeUser, FbUserProfileInfo user,Long parentId,String extendedToken,String userTokenPermissions, String type) {
		BusinessGetPageRequest request = new BusinessGetPageRequest();
		request.setBirdeyeUserId(birdeyeUser);
		request.setSocialUserId(user !=null ? user.getId() : "");
		request.setChannel(SocialChannel.FACEBOOK.getName());
		if (type == ENTERPRISE) {
			request.setEnterpriseId(parentId);
		} else {
			request.setResellerId(parentId);
		}
		request.setPageCount(0);
		request.setStatus(Status.INITIAL.getName());
		request.setRequestType(CONNECT);
		request.setEmail(user !=null ? user.getEmail() : "");
		request.setUserAccessToken(extendedToken);
		request.setUserFbPermissions(userTokenPermissions);
		return request;
	}

	private BusinessGetPageOpenUrlRequest createBusinessGetPageRequestOpenUrl(FbUserProfileInfo user,Long businessId,String extendedToken,
																			  String userTokenPermissions,String firebaseKey) {
		BusinessGetPageOpenUrlRequest request = new BusinessGetPageOpenUrlRequest();
		request.setSocialUserId(user !=null ? user.getId() : "");
		request.setChannel(SocialChannel.FACEBOOK.getName());
		request.setEnterpriseId(businessId);
		request.setPageCount(0);
		request.setStatus(Status.INITIAL.getName());
		request.setRequestType(CONNECT);
		request.setFirebaseKey(firebaseKey);
		request.setEmail(user !=null ? user.getEmail() : "");
		request.setUserAccessToken(extendedToken);
		request.setUserFbPermissions(userTokenPermissions);
		return request;
	}

	@Override
	public OpenUrlFetchPageResponse submitFetchPageRequestForOpenURL(Long businessId, ChannelAuthOpenUrlRequest authRequest) throws Exception {
		String key = SocialChannel.FACEBOOK.getName().concat(String.valueOf(authRequest.getFirebaseKey()));
		boolean lock = redisService.tryToAcquireLock(key);
		OpenUrlFetchPageResponse openUrlFetchPageResponse = new OpenUrlFetchPageResponse();
		logger.info("[Redis Lock] Lock status : {}",lock);
		if(lock) {
			try {
				Business business = businessRepo.findByBusinessId(businessId);

				SocialAppCredsInfo socialAppFbCreds = getFacebookCreds();
				FacebookCreds creds = new FacebookCreds(socialAppFbCreds.getChannelClientId(), socialAppFbCreds.getChannelClientSecret(), authRequest.getTempAccessToken(), getFacebookGraphApiBaseUrl());

				String extendedToken = fbService.getExtendedFacebookToken(creds);
				logger.info(EXTENDED_TOKEN, extendedToken);

				logger.info(SOCIAL_SETUP_GETTING_USERS_FOR_BUSINESS_EXTENDED_TOKEN, business.getId(), extendedToken);
				String baseUrl = getFacebookGraphApiBaseUrl();
				FbUserProfileInfo user = fbService.getUserDetails(extendedToken);
				if(Objects.isNull(user)) {
					throw new Exception("[Openurl] Failed to get user info!!");
				}
				logger.info(FOR_BUSINESS_AND_EXTENDED_TOKEN_USER_PROFILE_ID, businessId, extendedToken, (user != null ? user.getId() : null));
				String userTokenPermissions = createUserTokenPermissions(user);

				BusinessGetPageOpenUrlRequest request = createBusinessGetPageRequestOpenUrl(user,businessId,extendedToken,userTokenPermissions,authRequest.getFirebaseKey());
				businessGetPageOpenUrlReqRepo.saveAndFlush(request);
				nexusService.insertMapInFirebase("socialOpenUrl/"+ authRequest.getFirebaseKey(),authRequest.getFirebaseKey() ,Status.INITIAL.getName());
				fbPageService.fetchPagesForOpenUrl(request, extendedToken, user, business,authRequest.getFirebaseKey());
			} catch (Exception e) {
				handleCleanupRedisForOpenurl(key, authRequest, businessId);
			}
		} else {
			logger.info("[Redis Lock] (Facebook) Lock is already acquired for business {}", businessId);
		}
		openUrlFetchPageResponse.setFirebaseKey(authRequest.getFirebaseKey());
		logger.info("API response: {}",openUrlFetchPageResponse.toString());
		return openUrlFetchPageResponse;
	}

	@Override
	public void cancelRequest(String channel, Long businessId, Boolean forceCancel) {
		BusinessGetPageRequest req =  businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(businessId, SocialChannel.FACEBOOK.getName(),CONNECT);
		if(Objects.isNull(req)) {
			logger.error("No record found in business get page request for businessId: {}", businessId);
			return;
		}
		if(!forceCancel && Status.INITIAL.getName().equals(req.getStatus())) {
			throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, Constants.INIT_TO_CANCEL_ERROR_REQUEST_MESSAGE);
		}
		req.setStatus(Status.CANCEL.getName());
		req.setUpdated(new Date());
		businessGetPageReqRepo.saveAndFlush(req);
		pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),req.getRequestType(),Status.COMPLETE.getName(), req.getEnterpriseId());
		releaseLock(req.getChannel(), req.getEnterpriseId());
	}

	private void releaseLock(String channel, Long enterpriseId) {
		redisService.release(channel.concat(String.valueOf(enterpriseId)));
	}

	private boolean isBusinessNotMappedToFbAccount(BusinessLiteDTO business) {
		return socialFbRepo.findByBusinessId(business.getBusinessId()).isEmpty();
	}

	private void checkForUnMappedSmbPages(BusinessLiteDTO business) {
		if (checkBusinessSMB(business)) {
			List<BusinessFBPage> existingPages = socialFbRepo.findByAccountId(business.getBusinessId());
			List<BusinessFBPage> unMappedPage = existingPages.stream().filter(fbPage -> Objects.isNull(fbPage.getBusinessId())).collect(Collectors.toList());
			if(CollectionUtils.isNotEmpty(unMappedPage)) {
				socialFbRepo.delete(unMappedPage);
			} else {
				socialFbRepo.deleteByAccountId(business.getBusinessId());
			}
		}
	}

	private void checkForUnMappedResellerPages(BusinessLiteDTO business) {
		if (checkBusinessSMB(business)) {
			List<BusinessFBPage> existingPages = socialFbRepo.findByEnterpriseId(business.getBusinessNumber());
			List<BusinessFBPage> unMappedPage = existingPages.stream().filter(fbPage -> Objects.isNull(fbPage.getBusinessId())).collect(Collectors.toList());
			if(CollectionUtils.isNotEmpty(unMappedPage)) {
				socialFbRepo.delete(unMappedPage);
			} else {
				socialFbRepo.deleteByEnterpriseId(business.getBusinessNumber());
			}
		}
	}

	@Override
	public ChannelPageInfo connectPagesV1(List<String> pageIds, Long enterpriseId, Integer accountId) throws Exception {
		logger.info("connectPages : pageIds : {}", pageIds);
		ChannelPageInfo channelAccountInfo = new ChannelPageInfo();
		List<BusinessGetPageRequest> request = getRequestForBusiness(enterpriseId, Status.FETCHED.getName(),CONNECT);
		if (CollectionUtils.isEmpty(request)) {
			throw new BirdeyeSocialException(ErrorCodes.STATUS_CHANGED, "seems status has already changed");
		} else if (request.size() > 1) {
			throw new BirdeyeSocialException(ErrorCodes.MULTI_FETCHED_ROWS, "multiple rows with fetched status");
		} else {
			BusinessLiteDTO business = businessCoreService.getBusinessLiteByNumber(enterpriseId);
			checkForUnMappedSmbPages(business);
			BusinessGetPageRequest req = request.get(0);
			req.setStatus(Status.COMPLETE.getName());
			req.setUpdated(new Date());
			businessGetPageReqRepo.saveAndFlush(req);
			pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),req.getRequestType(),req.getStatus(), req.getEnterpriseId());
			Boolean autoMappingRequired = checkForAutoMapping(pageIds,enterpriseId);
			channelAccountInfo.setAutoMapping(autoMappingRequired);
			List<BusinessFBPage> fbPages = getFBPages(pageIds);
			fbPages.stream().forEach(page -> {
				page.setEnterpriseId(enterpriseId);
				page.setAccountId(accountId);
				page.setIsSelected(1);
				socialFbRepo.saveAndFlush(page);
			});
			releaseLock(req.getChannel(),req.getEnterpriseId());
			Map<String, List<ChannelAccountInfo>> accountMap = new HashMap<>();
			accountMap.put(SocialChannel.FACEBOOK.getName(), getAccountInfo(getFBPages(pageIds, enterpriseId),enterpriseId));
			channelAccountInfo.setPageTypes(accountMap);
			if(autoMappingRequired.equals(true)) {
				triggerAutoMapping(enterpriseId);
			}
			BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest();
			businessLiteRequest.setKey("businessNumber");
			businessLiteRequest.setValue(enterpriseId);

			if (checkBusinessSMB(business) && isBusinessNotMappedToFbAccount(business)) {
				saveLocationPageMapping(business.getBusinessId(), fbPages.get(0).getFacebookPageId(), req.getBirdeyeUserId(),Constants.ENTERPRISE, null);
			}
			else{
				SocialConnectPageRequest socialConnectPageRequest = new SocialConnectPageRequest(pageIds,SocialChannel.FACEBOOK.getName());
				kafkaProducer.sendObject(SOCIAL_PAGE_CONNECT,socialConnectPageRequest);
			}
		}
		return channelAccountInfo;
	}

	@Override
	public ChannelPageInfo connectResellerPages(List<String> pageIds, Long resellerId, Boolean selectAll, String searchStr) {
		logger.info("connectPages : pageIds : {}", pageIds);
		ChannelPageInfo channelAccountInfo = new ChannelPageInfo();
		List<BusinessGetPageRequest> request = getRequestForReseller(resellerId, Status.FETCHED.getName(),CONNECT);
		if (CollectionUtils.isEmpty(request)) {
			throw new BirdeyeSocialException(ErrorCodes.STATUS_CHANGED, "seems status has already changed");
		} else if (request.size() > 1) {
			throw new BirdeyeSocialException(ErrorCodes.MULTI_FETCHED_ROWS, "multiple rows with fetched status");
		} else {

			BusinessGetPageRequest req = request.get(0);
			req.setStatus(Status.COMPLETE.getName());
			req.setUpdated(new Date());
			businessGetPageReqRepo.saveAndFlush(req);
			pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),req.getRequestType(),req.getStatus(), req.getResellerId());
			List<String> pageId;
			if(selectAll && !searchStr.isEmpty()) {
				pageId = socialFbRepo.findAllByFBPageName(searchStr, searchStr, req.getId().toString());
			}
			else if(selectAll){
				pageId = socialFbRepo.findAllByRequestId(req.getId().toString());
			}else{
				pageId = pageIds;
			}
			List<BusinessFBPage> fbPages = getFBPages(pageId);
			fbPages.stream().forEach(page -> {
				page.setResellerId(resellerId);
				page.setIsSelected(1);
				socialFbRepo.saveAndFlush(page);
			});

			// release lock for reseller id
			redisService.release(req.getChannel().concat(String.valueOf(req.getResellerId())));

			Map<String, List<ChannelAccountInfo>> accountMap = new HashMap<>();
			accountMap.put(SocialChannel.FACEBOOK.getName(), getResellerAccountInfo(getResellerFBPages(pageIds, resellerId), req.getEmail(), resellerId));
			channelAccountInfo.setPageTypes(accountMap);

			SocialConnectPageRequest socialConnectPageRequest = new SocialConnectPageRequest(pageIds,SocialChannel.FACEBOOK.getName());
			kafkaProducer.sendObject(SOCIAL_PAGE_CONNECT,socialConnectPageRequest);

		}
		return channelAccountInfo;
	}


	@Override
	public Boolean checkForAutoMapping(List<String> pageIds, Long enterpriseId) {
		logger.info("checkForAutoMapping : pageIds : {} and enterprise: {}", pageIds,enterpriseId);
		List<BusinessFBPage> existingPages = socialFbRepo.findByEnterpriseId(enterpriseId);
		Business business = businessRepo.findByBusinessId(enterpriseId);
		List<Integer> businessIds = businessRepo.findEnterpriseLocations(business.getId());
		if (CollectionUtils.isEmpty(existingPages) && !CollectionUtils.isEmpty(businessIds)) {
			return true;
		}
		return false;
	}

	@Override
	public void triggerAutoMapping(Long enterpriseId) {
		logger.info("triggering auto mapping for enterprise: {}", enterpriseId);
		pushFbAutoMappingInFirebase(enterpriseId,Status.INITIAL.getName());

		autoMappingService.createAutoMappingEntry(enterpriseId,SocialChannel.FACEBOOK.getName());
		AutoMappingRequest autoMappingRequest  = new AutoMappingRequest();
		autoMappingRequest.setEnterpriseId(enterpriseId);
		autoMappingRequest.setChannel(SocialChannel.FACEBOOK.getName());
		producer.sendObject(TOPIC_INIT,autoMappingRequest);
	}

	/**
	 * Method to check in progress request for a business in case an exception occurred
	 * @param businessId
	 * @param requestType
	 * @return
	 */
	private BusinessGetPageRequest checkInProgressRequests(Long businessId, String requestType) {
		List<BusinessGetPageRequest> underProcessRequests = getRequestForBusiness(businessId, Status.INITIAL.getName(), requestType);
		if (CollectionUtils.isNotEmpty(underProcessRequests)) {
			return underProcessRequests.get(0);
		}
		return null;
	}

	/**
	 * Method to check in progress request for a reseller in case an exception occurred
	 * @param resellerId
	 * @param requestType
	 * @return
	 */
	private BusinessGetPageRequest checkInProgressRequestsForReseller(Long resellerId, String requestType) {
		List<BusinessGetPageRequest> underProcessRequests = getRequestForReseller(resellerId, Status.INITIAL.getName(), requestType);
		if (CollectionUtils.isNotEmpty(underProcessRequests)) {
			return underProcessRequests.get(0);
		}
		return null;
	}

	private BusinessGetPageOpenUrlRequest checkInProgressRequestsOpenUrl(Long businessId, String requestType) {
		List<BusinessGetPageOpenUrlRequest> underProcessRequests = getRequestForBusinessOpenUrl(businessId, Status.INITIAL.getName(), requestType);
		if (CollectionUtils.isNotEmpty(underProcessRequests)) {
			return underProcessRequests.get(0);
		}
		return null;
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.FacebookSocialAccountService#removeInactiveIntegrations(java.lang.Long)
	 */
	@Async
	@Override
	public void removeInactiveIntegration(String channel, Long enterpriseId) {
		List<BusinessFBPage> existingPages = socialFbRepo.findByEnterpriseId(enterpriseId);
		logger.info("Count of existing pages found for channel {} and enterpriseId {} : {}",channel, enterpriseId, existingPages.size());
		if (CollectionUtils.isEmpty(existingPages)) {
			logger.info("No page found for enterprise id :{}",enterpriseId);
		}
		processDeletePageEvent(channel, existingPages);
	}

	public void processDeletePageEvent(String channel, List<BusinessFBPage> existingPages) {
		List<SocialPagesAudit> auditPages = getFBAuditSocialObject(channel, existingPages);
		socialPagesAuditRepo.save(auditPages);
		socialPagesAuditRepo.flush();
		existingPages.forEach(page -> {
			logger.info("Delete FB page for page id : {}",page.getFacebookPageId());
			//save into social audit/backup table
			DeleteEventRequest request = DeleteEventRequest.builder()
					.channel(SocialChannel.FACEBOOK.getName())
					.pagesIds(Collections.singletonList(page.getFacebookPageId()))
					.build();
			//delete from BusinessFBPage
			kafkaProducer.sendObjectV1(KafkaTopicEnum.SOCIAL_DELETE_PAGE.getName(),request);
		});
	}

	@Override
	public void removeBusinessInactiveIntegration(String channel, Integer businessId) {
		List<BusinessFBPage> existingPages = socialFbRepo.findByBusinessId(businessId);
		if(CollectionUtils.isEmpty(existingPages)) {
			logger.info("No pages found for business id :{}",businessId);
			return;
		}
		List<SocialPagesAudit> auditPage = getFBAuditSocialObject(channel, Collections.singletonList(existingPages.get(0)));
		socialPagesAuditRepo.save(auditPage);
		socialPagesAuditRepo.flush();
		existingPages.forEach(page ->{
			logger.info("Remove Page for id :{}",page.getFacebookPageId());
			DeleteEventRequest request = DeleteEventRequest.builder()
					.channel(SocialChannel.FACEBOOK.getName())
					.pagesIds(Collections.singletonList(page.getFacebookPageId()))
					.build();
			kafkaProducer.sendObjectV1(KafkaTopicEnum.SOCIAL_DELETE_PAGE.getName(),request);
		});
	}

	@Override
	public void moveFBAccountLocation(Long sourceEnterpriseNumber, Long targetEnterpriseNumber, Integer businessId,
									  boolean isMultiLocationSource,Integer accountId) {
		List<BusinessFBPage> pageData = isMultiLocationSource
				? socialFbRepo.findByBusinessId(businessId)
				: socialFbRepo.findByEnterpriseId(sourceEnterpriseNumber);
		if(CollectionUtils.isEmpty(pageData)) {
			logger.info("Unable to get page for business id:{} or enterprise id  :{}", businessId, sourceEnterpriseNumber);
			return;
		}
		if (isMultiLocationSource) {
			// fetch mapping for changing location data in social
			List<BusinessFBPage> mappingData = socialFbRepo.findByBusinessId(businessId);
			if (CollectionUtils.isNotEmpty(mappingData)) {
				List<String> pagesIdList = mappingData.stream().map(BusinessFBPage::getFacebookPageId).collect(Collectors.toList());
				logger.info("updated Facebook enterpriseId from {} to {} for page Ids {} ", sourceEnterpriseNumber, targetEnterpriseNumber, pagesIdList);
				socialFbRepo.updateEnterpriseIdAndAccountIdByPageIdIn(sourceEnterpriseNumber, targetEnterpriseNumber, pagesIdList,accountId);
			}

		} else {
			logger.info("updated Facebook enterpriseId from {} to {}  ", sourceEnterpriseNumber,
					targetEnterpriseNumber);
			socialFbRepo.updateEnterpriseIdAndAccountId(sourceEnterpriseNumber, targetEnterpriseNumber, accountId);
		}
		updateMappingForFBPages(sourceEnterpriseNumber,targetEnterpriseNumber,pageData);
	}

	private void updateMappingForFBPages(Long sourceEnterpriseNumber, Long targetEnterpriseNumber, List<BusinessFBPage> mappingData) {
		mappingData.forEach(page-> {
			logger.info("Update FB enterpriseId from {} to {}  for page Id {}", sourceEnterpriseNumber, targetEnterpriseNumber, page.getFacebookPageId());
			commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.UPDATE_MAPPING.name(), Collections.singletonList(page), null, page.getBusinessId(),page.getEnterpriseId());
			page.setEnterpriseId(targetEnterpriseNumber);
			socialFbRepo.save(page);
		});
	}

	@Override
	public void removeUnmappedByEnterprise(Long enterpriseNumber, Integer businessId) {

		// fetch mapping for changing location data in social
		List<BusinessFBPage> businessFBPages = socialFbRepo.findByEnterpriseId(enterpriseNumber);
		if(CollectionUtils.isEmpty(businessFBPages)){
			logger.info("No page found with enterprise id : {}",enterpriseNumber);
			return;
		}
		businessFBPages.forEach(page -> {
			if(!Objects.equals(businessId, page.getBusinessId())){
				logger.info("Remove FB page for page id : {}",page.getFacebookPageId());
				commonService.deletePage(SocialChannel.FACEBOOK.getName(),page.getFacebookPageId());
			}
		});
	}

	private List<ChannelPageRemoved> prepareList(BusinessFBPage rawPage) {
		ChannelPageRemoved channelPageRemoved =  new ChannelPageRemoved(SocialChannel.FACEBOOK.getName(), rawPage.getFacebookPageId(),
				rawPage.getFacebookPageName(), rawPage.getBusinessId(), rawPage.getEnterpriseId(), null, null,rawPage.getId()
		);
		return Collections.singletonList(channelPageRemoved);
	}


	/**
	 * @param channel
	 * @param pages
	 */
	private List<SocialPagesAudit> getFBAuditSocialObject(String channel, List<BusinessFBPage> pages) {
		List<SocialPagesAudit> socialPagesAudits = new ArrayList<>();
		pages.forEach(page -> {
			SocialPagesAudit auditPage = new SocialPagesAudit();
			auditPage.setChannel(channel);
			auditPage.setRemovedOnDate(new Date());
			auditPage.setEnterpriseId(page.getEnterpriseId());
			auditPage.setPageId(page.getFacebookPageId());
			auditPage.setPageName(page.getFacebookPageName());
			auditPage.setPageUrl(page.getLink());
			auditPage.setAccessToken(page.getPageAccessToken());
			auditPage.setHandle(page.getHandle());
			auditPage.setProfileImageUrl(page.getFacebookPagePictureUrl());
			auditPage.setPictureUrl(page.getPictureUrl());
			auditPage.setFirstName(page.getFirstName());
			auditPage.setLastName(page.getLastName());
			auditPage.setIsSelected(page.getIsSelected());
			auditPage.setIsValid(page.getIsValid());
			auditPage.setIsManaged(page.getIsManaged());
			auditPage.setScope(page.getScope());
			auditPage.setSingleLineAddress(page.getSingleLineAddress());
			auditPage.setUserId(page.getUserId());
			auditPage.setRequestId(page.getRequestId());
			auditPage.setCreatedBy(page.getCreatedBy());
			auditPage.setUpdatedBy(page.getUpdatedBy());
			auditPage.setPagePermissions(page.getPagePermissions());
			auditPage.setBusinessId(page.getBusinessId());
			socialPagesAudits.add(auditPage);
		});
		return socialPagesAudits;
	}

	/**
	 * @param mappedPage
	 * @param auditPage
	 */
	private SocialPagesAudit getFBAuditMappingObject(BusinessFacebookPageNew mappedPage, SocialPagesAudit auditPage) {
		auditPage.setBusinessId(mappedPage.getBusinessId());
		auditPage.setFbErrorSubcode(mappedPage.getFbErrorSubcode());

		return auditPage;
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.FacebookSocialAccountService#updateInvalidPageCounter(java.lang.String)
	 */
	@Override
	public void updateInvalidPageCounter(String pageId, Object details) {
		try {
			BusinessFBPage bFBPage = socialFbRepo.findFirstByFacebookPageId(pageId);
			if(Objects.isNull(bFBPage) || Objects.isNull(bFBPage.getBusinessId())) {
				logger.info("Exiting updateInvalidPageCounter. No mapped pages found.");
				return;
			}
//			String accessToken = bFBPage.getPageAccessToken();
//			socialInvalidRepo.insertOrUpdateCounter("facebook", pageId, accessToken);

			//For Auditing
			Boolean isStatusUpdated = socialFacebookService.updateFacebookPageStatusAudit(bFBPage, details);

//			This part is for immediate mark Invalid
//			Boolean isStatusUpdated = socialFacebookService.updateFacebookPageStatus(bFBPage);
//			InvalidSocialIntegration invalidPage = socialInvalidRepo.findByProfileId(bFBPage.getFacebookPageId());
//			if(Objects.isNull(invalidPage)) {
//				invalidPage = new InvalidSocialIntegration();
//				invalidPage.setChannel("facebook");
//				invalidPage.setCreatedAt(new Date());
//				invalidPage.setProfileId(bFBPage.getFacebookPageId());
//			}
//			invalidPage.setCounter(-1);
//			invalidPage.setAccessToken(bFBPage.getPageAccessToken());
//			invalidPage.setMarkedAt(new Date());

			if (isStatusUpdated) {
//				invalidPage.setMarkedInvalid(1);
//				socialInvalidRepo.saveAndFlush(invalidPage);
				logger.info("markFBIntegrationInvalid : Job execution is complete. Page whose is_valid status is changed from 1 to 0 is : {} : {}", bFBPage.getBusinessId(),bFBPage.getFacebookPageId());

				//push social status for messenger after marking invalid
				Business bus = businessRepo.findById(bFBPage.getBusinessId());
				Integer enterpriseId = null;
				if (bus.getEnterprise() != null) {
					enterpriseId = bus.getEnterprise().getId();
				} else {
					enterpriseId = bus.getId();
				}
				//Changing Data in Firebase to update only timestamp(this would reduce firebase usage), Revert this when required.
				pushFbStatusInFirebase(enterpriseId, bFBPage.getBusinessId(), FacebookIntegrationStatus.RECONNECT.getStatus());
			}
			else {
//				invalidPage.setMarkedInvalid(0);
//				socialInvalidRepo.saveAndFlush(invalidPage);
				logger.info("markFBIntegrationInvalid : Job execution is complete. Page whose is_valid status is not changed from 1 to 0 is : {} : {}", bFBPage.getBusinessId(),bFBPage.getFacebookPageId());
			}

		} catch (Exception ex) {
			logger.error("Exception while updateInvalidPageCounter, pageId : {} :: {}", pageId, ex.getMessage());
		}
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.FacebookSocialAccountService#markFBIntegrationInvalid()
	 */
	@Override
	public void markFBIntegrationInvalid() {

		Date yesterday = DateUtils.addDays(new Date(), -1);

		Integer fbInvalidLimit = Integer.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("fb.invalid.limit"));

		logger.info("Starting job :: markFBIntegrationInvalid with parameters date {}, counter {}",yesterday,fbInvalidLimit);

		List<InvalidSocialIntegration> pagesToMarkInvalid = socialInvalidRepo.findInvalidCandidate(yesterday, fbInvalidLimit);

		List<String> pageIds =  pagesToMarkInvalid.stream().map(page -> page.getPageId()).collect(Collectors.toList());

		ExecutorService taskExecutor = Executors.newFixedThreadPool(5);
		CompletionService<Boolean> ecs = new ExecutorCompletionService<>(taskExecutor);

		//Get page from mapped table, Verify debug access token and  mark invalid
		List<BusinessFBPage> fbPages = socialFbRepo.findByFacebookPageIdIn(pageIds);

		List<BusinessFBPage> mappedPages = fbPages.stream().filter(fbPage -> Objects.nonNull(fbPage.getBusinessId())).collect(Collectors.toList());

		if(CollectionUtils.isEmpty(mappedPages)) {
			logger.info("Exiting markFBIntegrationInvalid. No mapped pages found.");
			return;
		}

		logger.info("markFBIntegrationInvalid :: Job execution started for {} pages",mappedPages.size());
		logger.info("markFBIntegrationInvalid :: Job execution started for pages {}",mappedPages);

		Map<Integer,String> fBmappedPages = mappedPages.stream().collect(Collectors.toMap(val->val.getBusinessId(),val->val.getFacebookPageId(),
				(x,y)-> x));

		//TODO: Khyati - Need to submit events to Kafka for better parallism.
		Map<Integer, List<String>> map = new ConcurrentHashMap<>();
		for (BusinessFBPage mappedPage : mappedPages) {
			ecs.submit(validateIntegration(mappedPage, map));
		}

		for (BusinessFBPage mappedPage : mappedPages) {
			try {
				Boolean result = ecs.take().get(5l, TimeUnit.SECONDS);
			} catch (InterruptedException | ExecutionException | TimeoutException e) {
				logger.error("markFBIntegrationInvalid : Thread interrupted", e);
				// Restore interrupted state...
				Thread.currentThread().interrupt();
			}
		}
		taskExecutor.shutdown();
		boolean done;
		try {
			done = taskExecutor.awaitTermination(30, TimeUnit.SECONDS);
			logger.info("markFBIntegrationInvalid : Job completed : {}", done);
		} catch (InterruptedException e) {
			logger.error("markFBIntegrationInvalid : Executor service termination interrupted ", e);
			// Restore interrupted state...
			Thread.currentThread().interrupt();
		}

		logger.info("markFBIntegrationInvalid : Job execution is complete. Pages whose is_valid status is changed from 1 to 0 are : {}", map);

		// Reset page counter and Submit RECONNECT status for messenger
		//Set<Integer> allBusiness = map.keySet();

		Set<Integer> allBusiness = fBmappedPages.keySet();

		for (Integer locationId : allBusiness) {
			List<String> allPages = map.get(locationId);
			logger.info("Working for businessId: {}",locationId);
			InvalidSocialIntegration markedPage = socialInvalidRepo.findByPageId(fBmappedPages.get(locationId));
			markedPage.setCounter(0);
			markedPage.setMarkedAt(new Date());
			if(CollectionUtils.isEmpty(allPages)) {
				markedPage.setMarkedInvalid(0);
				socialInvalidRepo.saveAndFlush(markedPage);
			}
			else {
				markedPage.setMarkedInvalid(1);
				socialInvalidRepo.saveAndFlush(markedPage);

				//push social status for messenger after marking invalid
				Business bus = businessRepo.findById(locationId);
				Integer enterpriseId = null;
				if (bus.getEnterprise() != null) {
					enterpriseId = bus.getEnterprise().getId();
				} else {
					enterpriseId = bus.getId();
				}

				//Changing Data in Firebase to update only timestamp(this would reduce firebase usage), Revert this when required.
				pushFbStatusInFirebase(enterpriseId, locationId, FacebookIntegrationStatus.RECONNECT.getStatus());
			}
		}

	}

	private Callable<Boolean> validateIntegration(BusinessFBPage page, Map<Integer, List<String>> map)
	{
		return () -> {
			Boolean output = socialFacebookService.updateFacebookPageStatus(page);
			if (BooleanUtils.isTrue(output)) {
				if (map.containsKey(page.getBusinessId())) {
					map.get(page.getBusinessId()).add(page.getFacebookPageId());
				} else {
					List<String> list = new CopyOnWriteArrayList<>();
					list.add(page.getFacebookPageId());
					map.put(page.getBusinessId(), list);
				}
			}
			logger.info("markFBIntegrationInvalid :: Execution completed for BusinessFacebookPageNew ID : {}, Output : {}", page.getId(), output);
			return output;
		};
	}
	private BusinessGetPageRequest getRequestForBusinessWithoutStatus(Long businessId, String channel, String requestType) {

		List <BusinessGetPageRequest> gmbData = businessGetPageReqRepo.findLastRequestByEnterpriseIdAndChannelAndRequestType(businessId, channel, requestType);

		if (CollectionUtils.isNotEmpty(gmbData)) {
			return gmbData.get(0);
		} else {
			return null;
		}
	}

	@Override
	public OpenUrlPagesInfo getPagesFetchedByOpenUrl(Long enterpriseId) throws Exception {
		logger.info("getPagesFetchedByOpenUrl: enterpriseId {}", enterpriseId);
		OpenUrlPagesInfo response = new OpenUrlPagesInfo();
		List<BusinessGetPageOpenUrlRequest> fbRequests = businessGetPageOpenUrlReqRepo.findFirstByEnterpriseIdAndChannelAndRequestTypeOrderByCreatedDesc(enterpriseId, SocialChannel.FACEBOOK.getName(), CONNECT);
		logger.info("getPagesFetchedByOpenUrl: List<BusinessGetPageOpenUrlRequest> got result {}", fbRequests);

		if ( fbRequests != null && !fbRequests.isEmpty() ) {
			// only get the pages if status is fetched
			BusinessGetPageOpenUrlRequest fbRequest = fbRequests.get(0);
			if ( fbRequest.getStatus().equalsIgnoreCase(Status.FETCHED.getName()) ) {
				logger.info("getPagesFetchedByOpenUrl: fbRequest found with fetched status");
				List<BusinessFBPage> fbPages = socialFbRepo.findByRequestId(fbRequest.getId());
				response.setPageTypes(getAccountInfo(fbPages,enterpriseId));
			} else {
				logger.info("getPagesFetchedByOpenUrl: fbRequest found with {} status", fbRequest.getStatus());
			}
			response.setStatus(fbRequest.getStatus());
			response.setStatusType(fbRequest.getRequestType());
		} else {
			logger.info("getPagesFetchedByOpenUrl: No fbRequest found with given input");
			response.setStatus(Status.COMPLETE.getName());
			response.setStatusType(CONNECT);
		}

		logger.info("getPagesFetchedByOpenUrl: response {}", response);
		return response;
	}

	@Override
	public OpenUrlPagesInfo connectPagesFetchedByOpenUrl(Long enterpriseId, OpenUrlConnectRequest connectRequest, Integer userId) throws Exception {
		logger.info("connectPagesFetchedByOpenUrl : enterpriseId {} connectRequest {} userId {}", enterpriseId, connectRequest, userId);
		if ( enterpriseId == null || connectRequest == null || StringUtils.isEmpty(connectRequest.getFirebaseKey()) || CollectionUtils.isEmpty(connectRequest.getPageRequests()) ) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid value for enterpriseId/connectRequest/userId");
		}
		final String firebaseKey = connectRequest.getFirebaseKey();
		final List<String> pageIds = connectRequest.getPageRequests().stream().map(com.birdeye.social.sro.PageRequest::getId).collect(Collectors.toList());
		OpenUrlPagesInfo response = new OpenUrlPagesInfo();
		List<BusinessGetPageOpenUrlRequest> fbRequests = getRequestForOpenUrlBusiness(enterpriseId, Status.FETCHED.getName(), SocialChannel.FACEBOOK.getName(), CONNECT, firebaseKey);
		if ( CollectionUtils.isEmpty(fbRequests) ) {
			// case of no request found
			logger.error("connectPagesFetchedByOpenUrl : No rows found with fetched status for pageIds {} enterpriseId {} firebaseKey {}", pageIds, enterpriseId, firebaseKey);
			throw new BirdeyeSocialException(ErrorCodes.STATUS_CHANGED, "Seems request status has already changed");
		} else if ( fbRequests.size() > 1 ) {
			// case of multiple requests present
			logger.error("connectPagesFetchedByOpenUrl : Multiple rows found with fetched status for pageIds {} and enterpriseId {} firebaseKey {}", pageIds, enterpriseId, firebaseKey);
			throw new BirdeyeSocialException(ErrorCodes.MULTI_FETCHED_ROWS, "Multiple rows with fetched status found in BusinessGetPageOpenUrlRequest");
		} else {
			List<BusinessFBPage> fbPages = socialFbRepo.findByFacebookPageIdIn(pageIds);
			if ( CollectionUtils.isEmpty(fbPages) ) {
				logger.error("connectPagesFetchedByOpenUrl : No FB pages found with pageIds {}", pageIds);
				throw new BirdeyeSocialException(ErrorCodes.FB_PAGE_NOT_FOUND, "FB Pages not found with given pageIds");
			}

			// Get details of business using core API
			BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest();
			businessLiteRequest.setKey("businessNumber");
			businessLiteRequest.setValue(enterpriseId);
			BusinessLiteDTO business = businessCoreService.getBusinessLite(businessLiteRequest);
			if (Objects.isNull(business)) {
				throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, "Business not found using Business Lite API");
			}

			final boolean isBusinessNotMapped = isBusinessNotMappedToFbPage(business);
			final boolean isSmbAndMapped = checkBusinessSMB(business) && !isBusinessNotMapped;
			logger.info("connectPagesFetchedByOpenUrl: checkBusinessSMB {} isBusinessNotMapped {} invalidSmbCase {}", checkBusinessSMB(business), isBusinessNotMapped, isSmbAndMapped);
			List<BusinessFBPage> existingPages = new ArrayList<>();
			List<String> newPages = new ArrayList<>();
			for ( BusinessFBPage fbPage : fbPages ) {
				if ( fbPage.getEnterpriseId() == null && !isSmbAndMapped ) { // case of newly found page
					fbPage.setIsSelected(1);
					fbPage.setEnterpriseId(enterpriseId);
					fbPage.setAccountId(business.getBusinessId());
					socialFbRepo.saveAndFlush(fbPage);
					newPages.add(fbPage.getFacebookPageId());
				} else if ( fbPage.getEnterpriseId() != null && fbPage.getEnterpriseId().equals(enterpriseId) ) { // case of existing page
					existingPages.add(fbPage);
				} else if ( isSmbAndMapped ) { // case of smb account already mapped
					setupAuditRepo.saveAndFlush(new SocialSetupAudit("OPEN_URL_CONNECT", business.getBusinessId(),
							fbPage.getFacebookPageId(), String.valueOf(userId), fbPage.toString(), SocialChannel.FACEBOOK.getName(),
							"SMB account is already mapped"));
				} else { // case of trying to connect page belonging to different enterpriseId
					setupAuditRepo.saveAndFlush(new SocialSetupAudit("OPEN_URL_CONNECT", business.getBusinessId(),
							fbPage.getFacebookPageId(), String.valueOf(userId), fbPage.toString(), SocialChannel.FACEBOOK.getName(),
							"This page is already connected with some other enterpriseId"));
				}
			}

			logger.info("connectPagesFetchedByOpenUrl : Existing mappings to be updated for pages {}", existingPages);
			updateValidStatusOfFBMappings(existingPages);

			// prepare response
			response.setStatus(Status.COMPLETE.getName());
			response.setPageTypes(getAccountInfo(fbPages,enterpriseId));
			response.setStatusType(CONNECT);
			logger.info("connectPagesFetchedByOpenUrl : Processed FB Pages {}", fbPages);

			// update request in db
			fbRequests.get(0).setStatus(Status.COMPLETE.getName());
			fbRequests.get(0).setUpdated(new Date());
			businessGetPageOpenUrlReqRepo.saveAndFlush(fbRequests.get(0));
			logger.info("connectPagesFetchedByOpenUrl : Request status updated to complete");

			// try to map business & page if it is valid SMB case
			if (checkBusinessSMB(business) && isBusinessNotMapped) {
				try {
					logger.info("connectPagesFetchedByOpenUrl : Trying to map business {} to pageId {}", business.getBusinessId(), pageIds.get(0));
					saveLocationPageMapping(business.getBusinessId(), pageIds.get(0), userId,Constants.ENTERPRISE, null);
				} catch (Exception saveMappingException) {
					// we need to return 200 OK response even if mapping fails
					logger.error("connectPagesFetchedByOpenUrl : Failed to map business with page with error {}", saveMappingException.getMessage());
					setupAuditRepo.saveAndFlush(new SocialSetupAudit("OPEN_URL_CONNECT", business.getBusinessId(),
							pageIds.get(0), String.valueOf(userId), null, SocialChannel.FACEBOOK.getName(),
							"SMB account is already mapped"));
				}
			}else{
				if(CollectionUtils.isNotEmpty(newPages)){
					logger.info("publishing SOCIAL_PAGE_CONNECT event for facebook open url for enterpriseID {}",enterpriseId);
					SocialConnectPageRequest socialConnectPageRequest = new SocialConnectPageRequest(newPages,SocialChannel.FACEBOOK.getName());
					kafkaProducer.sendObject(SOCIAL_PAGE_CONNECT,socialConnectPageRequest);
				}
			}
		}

		logger.info("connectPagesFetchedByOpenUrl : Response {}", response);
		return response;
	}

	private boolean isBusinessNotMappedToFbPage(BusinessLiteDTO business) {
		return socialFbRepo.findByBusinessId(business.getBusinessId()).isEmpty();
	}

	/**
	 * This method updates is_valid status of a BusinessFacebookPageNew using the is_valid status of provided BusinessFBPage
	 * For all the pages that are being reconnected, we push business to kafka to be consumed by BAM and listing
	 * @param fbPages : List<BusinessGoogleMyBusinessLocation>
	 */
	@Async
	void updateValidStatusOfFBMappings(List<BusinessFBPage> fbPages) {
		logger.info("markFBLocationMappingAsInvalid : Marking BusinessFacebookPageNew as invalid for pageIds {}", fbPages);
		if ( fbPages != null && !fbPages.isEmpty() ) {
			fbPages.stream().filter(fbPage -> Objects.nonNull(fbPage.getBusinessId()) && fbPage.getIsValid()==1).forEach(fbPage -> {
				updateBusinessAggregationFB(fbPage.getFacebookPageId(), fbPage.getLink(), fbPage.getBusinessId());
				brokenIntegrationService.pushValidIntegrationStatus(fbPage.getEnterpriseId(),SocialChannel.FACEBOOK.getName(),fbPage.getId(),fbPage.getIsValid(),fbPage.getFacebookPageId());
			});
			logger.info("markFBLocationMappingAsInvalid : BusinessFacebookPageNew {} have been marked as invalid", fbPages);
		}
	}

	private void updateBusinessAggregationFB(String fbPageId, String link, Integer locationId) {
		logger.info("updateBusinessAggregationFB: Request Received to update Review Scanned for FB through BAM for fbPageId {} link {} locationId {}", fbPageId, link, locationId);
		if ( fbPageId != null && !fbPageId.isEmpty() && locationId != null ) {
			// Pushing Business into kafka to be consumed by Listing and BAM
			BAMUpdateRequest payload = new BAMUpdateRequest(SocialChannel.FACEBOOK.getName(), locationId, link, fbPageId);
			producer.sendWithKey(Constants.INTEGRATION_TOPIC, String.valueOf(locationId), payload);
		} else {
			logger.info("updateBusinessAggregationFB: Invalid parameters ");
		}
	}

	@Override
	@Deprecated
	public ChannelPageInfo getFBIntegrationRequestStatus(Long businessId, String requestType) throws Exception {

		ChannelPageInfo response = new ChannelPageInfo();
		BusinessGetPageRequest fbData = businessGetPageService.findLastRequestByEnterpriseIdAndChannel(businessId, SocialChannel.FACEBOOK.getName());
		if (fbData!=null){
			if(fbData.getStatus().equalsIgnoreCase(Status.INITIAL.getName()) || fbData.getStatus().equalsIgnoreCase(Status.NO_PAGES_FOUND.getName())) {
				response.setStatus(fbData.getStatus());
				response.setStatusType(fbData.getRequestType());
			} else if(CONNECT.equalsIgnoreCase(fbData.getRequestType()) && fbData.getStatus().equalsIgnoreCase(Status.FETCHED.getName())) {
				logger.info("[Facebook] isFetched is true for businessId and request Type {} {}",businessId,requestType);
				response.setStatus(Status.FETCHED.getName());
				Map<String, List<ChannelAccountInfo>> pageType = getPages(businessId, requestType);
				response.setPageTypes(pageType);
				response.setStatusType(fbData.getRequestType());
			} else {
				response.setStatus(Status.COMPLETE.getName());
				response.setStatusType(fbData.getRequestType());
			}
		} else {
			response.setStatus(Status.COMPLETE.getName());
			response.setStatusType(fbData!=null?fbData.getRequestType():requestType);
		}
		return response;
	}

	/**
	 * @param businessId
	 */
	private String isCompleteProcessType(Long businessId) {
		List<BusinessGetPageRequest> completeRequest = getMaxRequestForBusiness(businessId, Status.COMPLETE.getName());
		if (CollectionUtils.isNotEmpty(completeRequest)){
			return completeRequest.get(0).getRequestType();
		}
		return CONNECT;
	}

	private List<BusinessGetPageRequest> getMaxRequestForBusiness(Long businessId, String status) {
		return businessGetPageReqRepo.findLastRequestByEnterpriseIdAndStatusAndChannel(businessId, status, SocialChannel.FACEBOOK.getName());
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.FacebookSocialAccountService#reconnectPagesEnhancedFlow(java.lang.Long, java.util.List, java.lang.String, java.lang.Integer)
	 */
	@Override
	public void reconnectFBPagesEnhancedFlow(Long businessId, ChannelAllPageReconnectRequest pageRequest, Integer userId) {
		String fbGraphApiBaseUrl = getFacebookGraphApiBaseUrl();
		FacebookCreds creds;
		String key = SocialChannel.FACEBOOK.getName().concat(String.valueOf(businessId));
		boolean lock = redisService.tryToAcquireLock(key);
		logger.info("[Redis Lock] Lock status : {}",lock);
		if (lock) {
			try {
				// Check if another connect request is present in INIT or FETCHED state
				BusinessGetPageRequest existingInProgressReq = businessGetPageReqRepo.findFirstByEnterpriseIdAndChannelAndStatusIn(
						businessId, SocialChannel.FACEBOOK.getName(), Arrays.asList(Status.FETCHED.name(), Status.INITIAL.name()));
				if (existingInProgressReq != null) {
					logger.info("reconnectFBPagesEnhancedFlow: Existing BusinessGetPageRequest found with status INITIAL/FETCHED {}",
							existingInProgressReq);
					throw new BirdeyeSocialException(ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS, ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.name());
				}
				Business business = businessRepo.findByBusinessId(businessId);
				pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),RECONNECT,Status.INITIAL.getName(),businessId);
				SocialAppCredsInfo socialAppFbCreds = getFacebookCreds();

				if(StringUtils.isNotEmpty(pageRequest.getAccessToken())) {
					creds = new FacebookCreds(socialAppFbCreds.getChannelClientId(), socialAppFbCreds.getChannelClientSecret(),
							pageRequest.getAccessToken(), fbGraphApiBaseUrl);
				} else {
					creds = new FacebookCreds(socialAppFbCreds.getChannelClientId(), socialAppFbCreds.getChannelClientSecret(),
							pageRequest.getAuthCode(), pageRequest.getRedirectUri(), fbGraphApiBaseUrl);
				}

				String extendedToken = fbService.getExtendedFacebookToken(creds);
				logger.info(EXTENDED_TOKEN, extendedToken);
				if(StringUtils.isEmpty(extendedToken))  {
					logger.info("Extended token could not be retrieved, cancelling the fetch request");
					throw new Exception("Invalid extended token");
				}

				logger.info(SOCIAL_RECONNECT_GETTING_USERS_FOR_BUSINESS_EXTENDED_TOKEN, business.getId(), extendedToken);

				String baseUrl = getFacebookGraphApiBaseUrl();
				FbUserProfileInfo user = fbService.getUserDetails(extendedToken);
				logger.info(FOR_BUSINESS_AND_EXTENDED_TOKEN_USER_PROFILE_ID_EMAIL_ID, businessId, extendedToken, (user != null ? user.getId() : null), user.getEmail());

				String userTokenPermissions = null;
				if (user!=null && user.getPermissions() != null) {
					UserPermissions userPermissions = user.getPermissions();
					List<UserPermissionsData> permissionList = userPermissions.getData();
					List<String> permissions = new ArrayList<>();
					for (UserPermissionsData permission : permissionList) {
						permissions.add(permission.getPermission()+":"+permission.getStatus());
					}
					userTokenPermissions = String.join(",", permissions);
				}
				BusinessGetPageRequest request = new BusinessGetPageRequest();
				request.setBirdeyeUserId(userId);
				request.setSocialUserId(user !=null ? user.getId() : "");
				request.setChannel(SocialChannel.FACEBOOK.getName());
				request.setEnterpriseId(businessId);
				request.setPageCount(0);
				request.setStatus(Status.INITIAL.getName());
				request.setRequestType(RECONNECT);
				request.setEmail(user.getEmail());
				request.setUserAccessToken(extendedToken);
				request.setUserFbPermissions(userTokenPermissions);
				businessGetPageReqRepo.saveAndFlush(request);

				fbPageService.getFbPagesAndReconnect(business, pageRequest.getPageId(), userId, extendedToken, baseUrl, user, request);

			} catch (Exception e) {
				logger.info("Exception occurred in connect facebook,Redis lock released,Checking for in progress request for business id {} , exception {}", businessId,e);
				redisService.release(key);
				if (e instanceof BirdeyeSocialException) {
					BirdeyeSocialException beExp = (BirdeyeSocialException) e;
					if (beExp.getCode() == ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.value())
						throw beExp;
				}
				BusinessGetPageRequest req = checkInProgressRequests(businessId, RECONNECT);
				if (req != null) {
					req.setStatus(Status.CANCEL.getName());
					businessGetPageReqRepo.saveAndFlush(req);
					pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),req.getRequestType(),Status.COMPLETE.getName(),businessId,true);
				}else{
					pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),RECONNECT,Status.COMPLETE.getName(),businessId,true);
				}
			}
		} else {
			logger.info("[Redis Lock] (Facebook Reconnect) Lock is already acquired for business {}", businessId);
			throw new BirdeyeSocialException(ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS, ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.name());
		}
	}

	@Override
	public void reconnectResellerFBPages(Long resellerId, ChannelAllPageReconnectRequest pageRequest, Integer userId, Integer limit) {
		String fbGraphApiBaseUrl = getFacebookGraphApiBaseUrl();
		FacebookCreds creds;
		String key = SocialChannel.FACEBOOK.getName().concat(String.valueOf(resellerId));
		boolean lock = redisService.tryToAcquireLock(key);
		logger.info("[Redis Lock] Lock status : {}",lock);
		List<String> pageId = socialFbRepo.findByResellerIdAndValidityType(resellerId, Arrays.asList(ValidTypeEnum.INVALID.getId(),ValidTypeEnum.PARTIAL_VALID.getId()));
		if (lock) {
			try {
				// Check if another connect request is present in INIT or FETCHED state
				BusinessGetPageRequest existingInProgressReq = businessGetPageReqRepo.findFirstByResellerIdAndChannelAndStatusIn(
						resellerId, SocialChannel.FACEBOOK.getName(), Arrays.asList(Status.FETCHED.name(), Status.INITIAL.name()));
				if (existingInProgressReq != null) {
					logger.info("reconnectResellerFBPages: Existing BusinessGetPageRequest found with status INITIAL/FETCHED {}",
							existingInProgressReq);
					throw new BirdeyeSocialException(ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS, ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.name());
				}
				Business business = businessRepo.findByBusinessId(resellerId);
				pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(),RECONNECT,Status.INITIAL.getName(), resellerId);
				SocialAppCredsInfo socialAppFbCreds = getFacebookCreds();

				if(StringUtils.isNotEmpty(pageRequest.getAccessToken())) {
					creds = new FacebookCreds(socialAppFbCreds.getChannelClientId(), socialAppFbCreds.getChannelClientSecret(),
							pageRequest.getAccessToken(), fbGraphApiBaseUrl);
				} else {
					creds = new FacebookCreds(socialAppFbCreds.getChannelClientId(), socialAppFbCreds.getChannelClientSecret(),
							pageRequest.getAuthCode(), pageRequest.getRedirectUri(), fbGraphApiBaseUrl);
				}

				String extendedToken = fbService.getExtendedFacebookToken(creds);
				logger.info(EXTENDED_TOKEN, extendedToken);
				if(StringUtils.isEmpty(extendedToken))  {
					logger.info("Extended token could not be retrieved, cancelling the fetch request");
					throw new Exception("Invalid extended token");
				}

				logger.info(SOCIAL_RECONNECT_GETTING_USERS_FOR_BUSINESS_EXTENDED_TOKEN, business.getId(), extendedToken);

				String baseUrl = getFacebookGraphApiBaseUrl();
				FbUserProfileInfo user = fbService.getUserDetails(extendedToken);
				logger.info(FOR_RESELLER_AND_EXTENDED_TOKEN_USER_PROFILE_ID_EMAIL_ID, resellerId, extendedToken, (user != null ? user.getId() : null), user.getEmail());

				String userTokenPermissions = null;
				if (user!=null && user.getPermissions() != null) {
					UserPermissions userPermissions = user.getPermissions();
					List<UserPermissionsData> permissionList = userPermissions.getData();
					List<String> permissions = new ArrayList<>();
					for (UserPermissionsData permission : permissionList) {
						permissions.add(permission.getPermission()+":"+permission.getStatus());
					}
					userTokenPermissions = String.join(",", permissions);
				}
				BusinessGetPageRequest request = new BusinessGetPageRequest();
				request.setBirdeyeUserId(userId);
				request.setSocialUserId(user !=null ? user.getId() : "");
				request.setChannel(SocialChannel.FACEBOOK.getName());
				request.setResellerId(resellerId);
				request.setPageCount(0);
				request.setStatus(Status.INITIAL.getName());
				request.setRequestType(RECONNECT);
				request.setEmail(user.getEmail());
				request.setUserAccessToken(extendedToken);
				request.setUserFbPermissions(userTokenPermissions);
				businessGetPageReqRepo.saveAndFlush(request);

				fbPageService.getFbPagesAndReconnectForReseller(business, pageId, userId, extendedToken, baseUrl, user, request);

			} catch (Exception e) {
				logger.info("Exception occurred in connect facebook, Redis lock released,Checking for in progress request for reseller id {} , exception {}", resellerId, e);
				redisService.release(key);
				if (e instanceof BirdeyeSocialException) {
					BirdeyeSocialException beExp = (BirdeyeSocialException) e;
					if (beExp.getCode() == ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.value())
						throw beExp;
				}
				BusinessGetPageRequest req = checkInProgressRequestsForReseller(resellerId , RECONNECT);
				if (req != null) {
					req.setStatus(Status.CANCEL.getName());
					businessGetPageReqRepo.saveAndFlush(req);
					pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(), req.getRequestType(), Status.COMPLETE.getName(), resellerId,true);
				}else{
					pushCheckStatusInFirebase(SocialChannel.FACEBOOK.getName(), RECONNECT,Status.COMPLETE.getName(), resellerId,true);
				}
			}
		} else {
			logger.info("[Redis Lock] (Facebook Reconnect) Lock is already acquired for reseller {}", resellerId);
			throw new BirdeyeSocialException(ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS, ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.name());
		}
		pushToKafkaForValidity(Constants.FACEBOOK, pageId );
	}

	private List<BusinessFBPage> convertLiteObject(Long resellerId) {
		List<BusinessFBPage> pages = new ArrayList<>() ;
		List<SocialFBPageRepository.BFL> pagesLite = socialFbRepo.getFBLiteInfo(resellerId);
		if(CollectionUtils.isNotEmpty(pagesLite)){
			for (SocialFBPageRepository.BFL page : pagesLite){
				BusinessFBPage info = new BusinessFBPage();
				info.setPagePermissions(page.getPagePermissions());
				info.setScope(page.getScope());
				info.setIsValid(page.getIsValid());
				info.setIsManaged(page.getIsManged());
				info.setFbErrorSubcode(page.getFbErrorSubCode());
				pages.add(info);
			}
		}
		return pages;
	}


	@Override
	public ChannelLocationInfo getSingleLocationMappingPages(Integer businessId) {
		ChannelLocationInfo locInfo = new ChannelLocationInfo();
		List<BusinessLocationEntity> businessLocations = businessRepo.getAllBusinessEnterpriseByBid(Collections.singletonList(businessId));
		if (CollectionUtils.isNotEmpty(businessLocations)) {
			BusinessLocationEntity businessLocationEntity = businessLocations.get(0);
			locInfo.setLocationId(businessLocationEntity.getId());
			locInfo.setLocationName(businessLocationEntity.getAlias1() != null ? businessLocationEntity.getAlias1() : businessLocationEntity.getName());
			locInfo.setAddress(prepareBusinessAddress(businessLocationEntity));
			List<BusinessFBPage> businessFbPages = socialFbRepo.findByBusinessId(businessId);
			// get facebook Page Mapping
			if(CollectionUtils.isNotEmpty(businessFbPages)){
				BusinessFBPage businessFBPage = businessFbPages.get(0);
				Map<String, LocationPageListInfo> pageInfoMap = new HashMap<>();
				Boolean isWebChatEnabled = businessCoreService.isWebChatEnabledByNumber(businessFBPage.getEnterpriseId());
				pageInfoMap.put(SocialChannel.FACEBOOK.getName(), preparePageData(businessFBPage,isWebChatEnabled));
				locInfo.setPageData(pageInfoMap);
			}
		}
		return locInfo;
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.FacebookSocialAccountService#updateFBTokenPermissionsForAllPages()
	 */
	@Override
	public void updateFBTokenPermissionsForAllPages() {
		List<BusinessFBPage> fbPages = socialFbRepo.findByPagePermissionsIsNull();

		fbPages.stream().forEach(page -> {
			Business business = businessRepo.findByBusinessId(page.getEnterpriseId());
			String tokenPermissions = null;
			try {
				tokenPermissions = getPageTokenPermissions(business, page.getPageAccessToken(), Collections.singletonList(page.getFacebookPageId()));
			} catch (Exception e) {
				logger.error("For page id {} Error while getPageTokenPermissions : {}", page.getFacebookPageId(), e.getMessage());
			}
			page.setPagePermissions(tokenPermissions);
		});
		socialFbRepo.save(fbPages);
		socialFbRepo.flush();
		List fbPageIds = fbPages.stream().map(BusinessFBPage::getFacebookPageId).collect(Collectors.toList());
		pushToKafkaForValidity(Constants.FACEBOOK, fbPageIds);
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.FacebookSocialAccountService#fetchPagePermissionSet(java.lang.String)
	 */
	@Override
	public String fetchPagePermissionSet(String pageId) {
		logger.info("fetchPagePermissionSet for pageId {}", pageId);
		BusinessFBPage rawPage = socialFbRepo.findFirstByFacebookPageId(pageId);
		return rawPage != null ? rawPage.getPagePermissions() : "";
	}

	@Async
	@Override
	public void restoreBusinessInactiveIntegration(String name, Integer businessId) {
		SocialPagesAudit socialPagesAudit = socialPagesAuditRepo.findByBusinessIdAndChannel(businessId,name);
		if(Objects.nonNull(socialPagesAudit)){
			backupFBPages(socialPagesAudit);
		}else{
			logger.info("no backup found in social pages audit for channel facebook and business Id {}",businessId);
		}
	}

	@Override
	public void backupFBPages(SocialPagesAudit socialPagesAudit) {

		logger.info("Start restoring FB PageId {} : for enterpriseId {} : ", socialPagesAudit.getPageId(),socialPagesAudit.getEnterpriseId());

		List<BusinessFBPage> existingLocations=socialFbRepo.findByFacebookPageId(socialPagesAudit.getPageId());
		if(CollectionUtils.isEmpty(existingLocations)) {
			getBusinessFBPageFromSocialAudit(socialPagesAudit);
			if(socialPagesAudit.getBusinessId()!=null) {
				kafkaProducer.sendObject(Constants.FB_PAGE_MAPPING_ADDED, new LocationPageMappingRequest(socialPagesAudit.getBusinessId(), socialPagesAudit.getPageId()));
			}
			logger.info("Restoring FB PageId {} : in BusinessFBPage for enterpriseId {} : is completed ", socialPagesAudit.getPageId(),socialPagesAudit.getEnterpriseId());
		} else {
			socialPagesAudit.setSocialEnterpriseRestorePageId("Exists :: "+existingLocations.get(0).getId());
			logger.info("Already exists FB pageId {} : in BusinessFBPage for enterpriseId {} : ", socialPagesAudit.getPageId(),socialPagesAudit.getEnterpriseId());
		}
		socialPagesAuditRepo.saveAndFlush(socialPagesAudit);
		pushToKafkaForValidity(Constants.FACEBOOK, Arrays.asList(socialPagesAudit.getPageId()) );
	}


	private BusinessFBPage getBusinessFBPageFromSocialAudit(SocialPagesAudit socialPagesAudit) {
		BusinessFBPage businessFBPage=new BusinessFBPage();
		businessFBPage.setEnterpriseId(socialPagesAudit.getEnterpriseId());
		businessFBPage.setFacebookPageId(socialPagesAudit.getPageId());
		businessFBPage.setFacebookPageName(socialPagesAudit.getPageName());
		businessFBPage.setLink(socialPagesAudit.getPageUrl());
		businessFBPage.setPageAccessToken(socialPagesAudit.getAccessToken());
		businessFBPage.setHandle(socialPagesAudit.getHandle());
		businessFBPage.setFacebookPagePictureUrl(socialPagesAudit.getProfileImageUrl());
		businessFBPage.setPictureUrl(socialPagesAudit.getPictureUrl());
		businessFBPage.setFirstName(socialPagesAudit.getFirstName());
		businessFBPage.setLastName(socialPagesAudit.getLastName());
		businessFBPage.setIsSelected(socialPagesAudit.getIsSelected());
		businessFBPage.setIsValid(socialPagesAudit.getIsValid());
		businessFBPage.setIsManaged(socialPagesAudit.getIsManaged());
		businessFBPage.setScope(socialPagesAudit.getScope());
		businessFBPage.setSingleLineAddress(socialPagesAudit.getSingleLineAddress());
		businessFBPage.setUserId(socialPagesAudit.getUserId());
		businessFBPage.setRequestId(socialPagesAudit.getRequestId());
		businessFBPage.setPagePermissions(socialPagesAudit.getPagePermissions());
		businessFBPage.setCreatedBy(socialPagesAudit.getCreatedBy());
		businessFBPage.setUpdatedBy(socialPagesAudit.getUpdatedBy());
		businessFBPage.setBusinessId(socialPagesAudit.getBusinessId());
		socialFbRepo.saveAndFlush(businessFBPage);
		socialPagesAudit.setSocialEnterpriseRestorePageId(businessFBPage.getId().toString());
		return businessFBPage;
	}


	/* (non-Javadoc)
	 * @see com.birdeye.social.service.FacebookSocialAccountService#fetchPagePermissionSet(java.lang.String)
	 */
	@Override
	public String getFacebookIntegrationStatus(Integer businessId) {
		logger.info("getFacebookIntegrationStatus for businessId {}", businessId);
		String facebookIntegrationStatus = "";
		List<BusinessFBPage> businessFacebookPage = socialFbRepo.findByBusinessId(businessId);
		if(CollectionUtils.isNotEmpty(businessFacebookPage)) {
			facebookIntegrationStatus =  FacebookIntegrationStatus.getFbIntegrationStatus(businessFacebookPage.get(0).getIsValid()).getStatus();
			if(facebookIntegrationStatus.equalsIgnoreCase(FacebookIntegrationStatus.RECONNECT.getStatus())) {
				return facebookIntegrationStatus;
			}
			//get permission set from raw fb page
			BusinessFBPage rawPage = businessFacebookPage.get(0);
			String pagePermissons = rawPage != null ? rawPage.getPagePermissions() : "";
			if ((StringUtils.isNotBlank(pagePermissons) && !pagePermissons.contains(PAGES_MESSAGING)) || pagePermissons == null){
				facebookIntegrationStatus = FacebookIntegrationStatus.ENABLE_MESSENGER.getStatus();
			} else if(StringUtils.isNotBlank(pagePermissons) && pagePermissons.contains(PAGES_MESSAGING) && businessFacebookPage.get(0).getMessengerOpted() == 0) {
				facebookIntegrationStatus = FacebookIntegrationStatus.OPTED_OUT.getStatus();
			}
		}
		else {
			facebookIntegrationStatus = FacebookIntegrationStatus.getFbIntegrationStatus(null).getStatus();
		}
		return facebookIntegrationStatus;
	}

	@Override
	public boolean checkIfPageExistsByAccountId(Long accountId) {
		return socialFbRepo.existsByEnterpriseIdAndIsSelected(accountId,1);
	}

	@Override
	public void processAutoMappingInitFbRequest(Long enterpriseId) {
		Business business = businessRepo.findByBusinessId(enterpriseId);
		logger.info("Initiating auto mapping for enterprise: {}",enterpriseId);
		List<Integer> businessIds = businessRepo.findEnterpriseLocations(business.getId());
		if(CollectionUtils.isEmpty(businessIds)) {
			businessIds.add(business.getId());
		}
		Map<String,List<String>> placeIdList = bamAggregationService.getBusinessPlaceIds(SocialChannel.FACEBOOK.getName(),businessIds);
		Set<String> existingPages = socialFbRepo.findAllByEnterpriseIdAndSelectedPageIdsInSet(enterpriseId);
		Map<Integer,String> finalMatchedList = new HashMap<>();
		if(placeIdList != null && placeIdList.size()>0) {
			logger.info("profileId List length received for enterprise: {} is {}", enterpriseId,placeIdList.size());
			List<String> bamPlaceIds;
			Set<String> matchedPlaceIds = new HashSet<>();
			for (Map.Entry<String,List<String>> entry : placeIdList.entrySet()) {
				bamPlaceIds = entry.getValue();
				bamPlaceIds.stream().forEach(bamPlaceId -> {
					if(existingPages.contains(bamPlaceId) && !matchedPlaceIds.contains(bamPlaceId)) {
						finalMatchedList.put(Integer.valueOf(entry.getKey()),bamPlaceId);
						matchedPlaceIds.add(bamPlaceId);
					} else if(matchedPlaceIds.contains(bamPlaceId)) {
						logger.info("Place id {} already mapped, can not map with business ID: {}",bamPlaceId,entry.getKey());
					}
				});
			}
		}
		if(finalMatchedList.size()>0) {
			logger.info("placeIdList auto mapped length for enterprise: {} is {}", enterpriseId,finalMatchedList.size());
			List<Integer> keyList = new ArrayList<Integer>(finalMatchedList.keySet());
			String key = TOPIC_MATCHED + "/" +  SocialChannel.FACEBOOK.getName() + "/" + enterpriseId;
			redisExternalService.delete(key);
			redisExternalService.set(key,keyList);
			for (Map.Entry<Integer,String> entry : finalMatchedList.entrySet()) {
				AutoMappingMatchedRequest autoMappingMatchedRequest = new AutoMappingMatchedRequest();
				autoMappingMatchedRequest.setChannel(SocialChannel.FACEBOOK.getName());
				autoMappingMatchedRequest.setBusinessId(Integer.parseInt(String.valueOf(entry.getKey())));
				autoMappingMatchedRequest.setProfileId(entry.getValue());
				autoMappingMatchedRequest.setEnterpriseId(enterpriseId);
				String topicKey = String.valueOf(enterpriseId);
				producer.sendWithKey(TOPIC_MATCHED, topicKey, autoMappingMatchedRequest);
			}
		} else {
			logger.info("No id matched in auto mapping for  enterprise: {}", enterpriseId);
			pushFbAutoMappingInFirebase(enterpriseId,Status.COMPLETE.getName());
			autoMappingService.updateAutoMappingEntryStatus(enterpriseId,Status.COMPLETE.getName(),SocialChannel.FACEBOOK.getName());
		}

	}
	@Override
	public void processAutoMappingMatchedRequest(AutoMappingMatchedRequest request) {
		String key = TOPIC_MATCHED + "/" +  SocialChannel.FACEBOOK.getName() + "/" + request.getEnterpriseId();
		Optional<Object> redisValue = redisExternalService.get(key);
		if(redisValue.isPresent()) {
			List<Integer> businessIds = (List<Integer>) redisValue.get();
			List<Integer> updatedBusinessIds = new ArrayList<>();
			for (int i = 0; i < businessIds.size(); i++) {
				if(Integer.parseInt(String.valueOf(businessIds.get(i))) != Integer.parseInt(String.valueOf(request.getBusinessId()))) {
					updatedBusinessIds.add(businessIds.get(i));
				} else {
					List<BusinessFBPage> fbPages = socialFbRepo.findByFacebookPageId(request.getProfileId());
					List<BusinessFBPage> mappedPages = fbPages.stream().filter(fbPage -> Objects.nonNull(fbPage.getBusinessId())).collect(Collectors.toList());
					if (CollectionUtils.isNotEmpty(mappedPages)) {
						logger.error("Fb page id {} is already mapped ", request.getProfileId());
					} else {
						if(fbPages.size()>0) {
							BusinessFBPage rawFBPage = fbPages.get(0);
							rawFBPage.setBusinessId(request.getBusinessId());
							socialFbRepo.save(fbPages);
							// TODO: This should be optimized. We are using two save calls for raw table update.
							// Consider using FB_PAGE_MAPPING_ADDED event for async consumption.
							try {
								fbPageService.performPostMappingAction(rawFBPage);
							} catch (Exception e) {
								logger.error("Error occured in performPostMappingAction for Fb page id {}",
										rawFBPage.getFacebookPageId(),e);
							}
							autoMappingService.updateAutoMappingRawIds(request.getEnterpriseId(),rawFBPage.getId(),SocialChannel.FACEBOOK.getName());
							kafkaProducer.sendObjectV1(Constants.FB_PAGE_MAPPING_ADDED, new LocationPageMappingRequest(request.getBusinessId(), request.getProfileId()));
						}
					}
				}
			}
			if(updatedBusinessIds.size()>0) {
				redisExternalService.set(key,updatedBusinessIds);
			} else {
				logger.info("auto mapping complete for enterprise: {}", request.getEnterpriseId());
				redisExternalService.delete(key);
				pushFbAutoMappingInFirebase(request.getEnterpriseId(),Status.COMPLETE.getName());
				autoMappingService.updateAutoMappingEntryStatus(request.getEnterpriseId(),Status.COMPLETE.getName(),SocialChannel.FACEBOOK.getName());
			}
		}
	}

	@Override
	public void fetchDisconnectedAndStore() {

		try {
			long queueSize = redisExternalService.getPriorityQueueSize(Constants.PRIORITY_QUEUE_FOR_FB_DISCONNECTED_ENTERPRISE);

			if (queueSize != 0) {
				logger.warn("{} {} In fetchAndStore : Priority queue is not empty",Constants.CHANNEL_DISCONNECT_PREFIX,SocialChannel.FACEBOOK.getName());
				return;
			}

			List<Long> disconnectedEnterpriseList = socialFbRepo.findDistinctEnterpriseIdByIsValid(0);
			redisExternalService.fillPriorityQueue(disconnectedEnterpriseList,Constants.PRIORITY_QUEUE_FOR_FB_DISCONNECTED_ENTERPRISE);
		} catch (Exception e) {
			logger.error("{} {} Some error occured due to {}",Constants.CHANNEL_DISCONNECT_PREFIX,SocialChannel.FACEBOOK.getName(),e.getMessage());

		}

	}

	@Override
	public String getConnectCTA() {
		return "facebook";
	}

	@Override
	public SmbUnmappedDataResponse getUnmappedfbPagesForSmb() {

		SmbUnmappedDataResponse smbUnmappedFbDataResponse = new SmbUnmappedDataResponse();
		smbUnmappedFbDataResponse.setChannel(SocialChannel.FACEBOOK.getName());
		List <Integer> rawIds = new ArrayList<>();

		List<FbPagePartialDTO> fbPages = socialFbRepo.findPartialSelectedId(1);
		logger.info("Total fb pages fetched: {}",fbPages.size());
		if(fbPages != null && fbPages.size()>0) {
			List<Long> businessNumberList = fbPages.stream()
					.map(r->r.getEnterpriseId()).distinct().collect(Collectors.toList());

			List<BusinesIdNumberDTO> businesIdNumberDTOS = new ArrayList<>();
			for(int i = 0; i<businessNumberList.size(); i=i+100) {
				List<Long> nElementsArray = new ArrayList<>();
				if(i+99<businessNumberList.size()) {
					nElementsArray = businessNumberList.subList(i,i+99);
				} else {
					nElementsArray = businessNumberList.subList(i,businessNumberList.size());
				}
				businesIdNumberDTOS.addAll(businessRepo.getAllSMBByBusinessId(nElementsArray));
			}

			if(businesIdNumberDTOS != null && businesIdNumberDTOS.size()>0) {
				logger.info("Total SMB business fetched: {}",businesIdNumberDTOS.size());
				Set<Long> businessNumberSmbList = businesIdNumberDTOS.stream()
						.map(r->r.getBusinessId()).collect(Collectors.toSet());
				List<FbPagePartialDTO> smbRawFbPages = fbPages.stream().filter(r -> businessNumberSmbList.contains(r.getEnterpriseId())).collect(Collectors.toList());
				List<FbPagePartialDTO> unmappedFbPages = smbRawFbPages.stream().filter(fbPagePartialDTO -> Objects.isNull(fbPagePartialDTO.getBusinessId())).collect(Collectors.toList());
				rawIds = unmappedFbPages.stream().map(r->r.getId()).collect(Collectors.toList());
			}
		}
		smbUnmappedFbDataResponse.setRawIds(rawIds);
		return  smbUnmappedFbDataResponse;
	}

	@Deprecated
	@Override
	public List<Integer> getFbActiveLocations() {
		// insight api requires read_insights,
		// pages_read_engagement permissions is required by /insights api but still this can be neglected and data will still be coming
//		return socialFbRepo.findBusinessIdsByIsValid(1).stream().
//				filter(page -> Objects.nonNull(page.getBusinessId()) && Objects.nonNull(page.getPagePermissions()) &&
//						page.getPagePermissions().contains("read_insights"))
//				.map(SocialFBPageRepository.BusinessInsightsAndValidity::getBusinessId).collect(Collectors.toList());
		return new ArrayList<>();
	}

	@Override
	public FbLocationsWithPermissions getFbLocationsWithPermissions(Long entNumber) {
		logger.info("getFbLocationsWithPermissionStatus: entNumber {}", entNumber);
		FbLocationsWithPermissions response = new FbLocationsWithPermissions();
		List<BusinessFBPage> pageIdsMissingPermission = socialFbRepo.findAllByEnterpriseIdAndNoInsightsPermission(entNumber);
		if (CollectionUtils.isNotEmpty(pageIdsMissingPermission)) {
			response.setLocationsMissingPermission(pageIdsMissingPermission.stream().
					filter(fbPage -> Objects.nonNull(fbPage.getBusinessId())).
					map(fbPage -> fbPage.getBusinessId()).collect(Collectors.toList()));
		}
		List<BusinessFBPage> pageIdsHavingPermission = socialFbRepo.findAllByEnterpriseIdAndInsightsPermission(entNumber);
		if (CollectionUtils.isNotEmpty(pageIdsHavingPermission)) {
			response.setLocationsHavingPermission(pageIdsHavingPermission.stream().
					filter(fbPage -> Objects.nonNull(fbPage.getBusinessId())).
					map(fbPage -> fbPage.getBusinessId()).collect(Collectors.toList()));
		}
		response.setHavingPermissionCount(CollectionUtils.size(response.getLocationsHavingPermission()));
		response.setMissingPermissionCount(CollectionUtils.size(response.getLocationsMissingPermission()));
		response.setTotalLocations(response.getHavingPermissionCount() + response.getMissingPermissionCount());
		return response;
	}
	@Override
	public List<BusinessFBPage> fetchRawPages(List<String> integrationIds) {
		return socialFbRepo.findByFacebookPageIdIn(integrationIds);
	}

	@Override
	public List<Number> fetchRawPagesId(List<String> integrationIds) {
		return socialFbRepo.findIdByFacebookPageIdIn(integrationIds);
	}

	@Override
	public List<SocialElasticDto> fetchPagesEsDto() {
		List<BusinessFBPage> businessFBPages = socialFbRepo.findByIsSelected(1);
		return SocialElasticUtil.getSocialEsDtoFromObjects(businessFBPages,SocialChannel.FACEBOOK.getName());
	}

	@Override
	public List<SocialElasticDto> fetchPagesEsDto(Integer id) {
		BusinessFBPage businessFBPages = socialFbRepo.findById(id);
		if(Objects.nonNull(businessFBPages)) {
			return SocialElasticUtil.getSocialEsDtoFromObjects(Collections.singletonList(businessFBPages), SocialChannel.FACEBOOK.getName());
		}
		return Collections.emptyList();
	}

	@Override
	public List<BusinessFBPage> fetchRawPagesById(List<Integer> rawId) {
		return socialFbRepo.findByIdIn(rawId);
	}

	@Override
	public Map<Integer, List<BusinessFBPage>> fetchPagesByTokenValidity(List<BusinessFBPage> businessFBPages) {
		Map<Integer, List<BusinessFBPage>> map = new HashMap<>();
		List<BusinessFBPage> validPages = new ArrayList<>();
		List<BusinessFBPage> inValidPages = new ArrayList<>();
		businessFBPages.forEach(businessFBPage -> {
			logger.info("fetching fb token details for pageId and rawid {} {}", businessFBPage.getFacebookPageId(),businessFBPage.getId());
			try{
				DebugTokenResponse debugTokenResponse = fbService.getTokenDetails(businessFBPage.getPageAccessToken(), "113069365515461|aePk-WDY2ihQtv1WSLOiHqcUIG8");
				if(Objects.nonNull(debugTokenResponse) && Objects.nonNull(debugTokenResponse.getData()) && debugTokenResponse.getData().getIs_valid()){
					logger.info("fb token is valid for pageId and rawid {} {}", businessFBPage.getFacebookPageId(),businessFBPage.getId());
					validPages.add(businessFBPage);
				}else{
					logger.info("fb token is invalid for pageId and rawid , response {} {} {}", businessFBPage.getFacebookPageId(),businessFBPage.getId(),debugTokenResponse);
					inValidPages.add(businessFBPage);
				}
			}catch (Exception ex){
				logger.error("exception occurred while fetching token details for facebook page id {}",businessFBPage.getFacebookPageId());
			}

		});
		map.put(1,validPages);
		map.put(0,inValidPages);
		return map;
	}

	@Override
	public void checkFbTokenAndUpdate(List<Integer> rawId) {
		List<BusinessFBPage> businessFBPages = this.fetchRawPagesById(rawId);
		if(CollectionUtils.isNotEmpty(businessFBPages)){
			logger.info("Total raw pages fetched {}",businessFBPages.size());
			Map<Integer, List<BusinessFBPage>> map = this.fetchPagesByTokenValidity(businessFBPages);
			List<BusinessFBPage> inValidPages = map.get(0);

			List<BusinessFBPage> validPages = map.get(1);

			if(CollectionUtils.isNotEmpty(validPages)){
				logger.info("total valid pages found {}",validPages.size());
				validPages.stream().forEach(businessFBPage -> {
					List<BusinessFBPage> businessFacebookPageNews = socialFbRepo.findByFacebookPageId(businessFBPage.getFacebookPageId());
					if(CollectionUtils.isNotEmpty(businessFacebookPageNews)){
						BusinessFBPage businessFacebookPageNew = businessFacebookPageNews.get(0);
						logger.info("found location for facebook profile id marking valid in bot mapping and raw table {}",businessFBPage.getFacebookPageId());
						businessFacebookPageNew.setPageAccessToken(businessFBPage.getPageAccessToken());
						businessFacebookPageNew.setIsValid(1);
						socialFbRepo.save(businessFacebookPageNew);
						businessFBPage.setIsValid(1);
						socialFbRepo.save(businessFBPage);
						producer.sendObject(Constants.UPDATE_ACCESS_TOKEN,
								new TokenUpdateRequest(businessFBPage.getFacebookPageId(),
										businessFBPage.getPageAccessToken(),
										SocialChannel.INSTAGRAM.getName(),businessFBPage.getUserId()));
						syncPlatformTable(businessFBPage);
					}else{
						logger.info("no mapping found for fb page {}",businessFBPage.getFacebookPageId());
					}
				});
				List fbPagesIds = validPages.stream().map(BusinessFBPage::getFacebookPageId).collect(Collectors.toList());
				pushToKafkaForValidity(Constants.FACEBOOK, fbPagesIds);
			}

			if(CollectionUtils.isNotEmpty(inValidPages)){
				logger.info("total invalid pages found {} {}",inValidPages.size(),inValidPages);
				inValidPages.stream().forEach(businessFBPage -> {
					List<BusinessFBPage> businessFacebookPageNews = socialFbRepo.findByFacebookPageId(businessFBPage.getFacebookPageId());
					if(CollectionUtils.isNotEmpty(businessFacebookPageNews)){
						BusinessFBPage businessFacebookPageNew = businessFacebookPageNews.get(0);
						logger.info("found location for facebook profile id marking invalid both mapping and raw page {}",businessFBPage.getFacebookPageId());
						businessFacebookPageNew.setPageAccessToken(businessFBPage.getPageAccessToken());
						producer.sendObject(Constants.UPDATE_ACCESS_TOKEN,
								new TokenUpdateRequest(businessFBPage.getFacebookPageId(),businessFBPage.getPageAccessToken(),
										SocialChannel.INSTAGRAM.getName(),businessFBPage.getUserId()));
						businessFacebookPageNew.setIsValid(0);
						socialFbRepo.save(businessFacebookPageNew);
						businessFBPage.setIsValid(0);
						socialFbRepo.save(businessFBPage);
						syncPlatformTable(businessFBPage);
						commonService.sendFbSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(businessFBPage),
								businessFBPage.getUserId(), businessFBPage.getBusinessId(), businessFBPage.getEnterpriseId());
					}else{
						logger.info("no mapping found for fb page {}",businessFBPage.getFacebookPageId());
					}
				});
			}
			List fbinValidPagesIds = inValidPages.stream().map(BusinessFBPage::getFacebookPageId).collect(Collectors.toList());
			pushToKafkaForValidity(Constants.FACEBOOK, fbinValidPagesIds);
		}
	}

	private void syncPlatformTable(BusinessFBPage fbPage){
		SocialChannelSyncRequest socialChannelSyncRequest = new SocialChannelSyncRequest(SocialChannel.FACEBOOK.getName(),fbPage.getFacebookPageId());
		kafkaProducer.sendObject(Constants.SOCIAL_PAGE_SYNC,socialChannelSyncRequest);
	}

	@Override
	public void updateAccessToken(TokenUpdateRequest request) {
		logger.info("[Facebook]: Validating access token for fbPageId: {}", request.getFbPageId());
		List<BusinessFBPage> page = socialFbRepo.findByFacebookPageId(request.getFbPageId());
		if (CollectionUtils.isNotEmpty(page) && page.get(0).getUserId().equalsIgnoreCase(request.getUserId())) {
			BusinessFBPage page1 = page.get(0);
			page1.setPageAccessToken(request.getAccessToken());
			page1.setIsValid(1);
			page1 = fbPageService.updateFacebookRole(page1);
			socialFbRepo.saveAndFlush(page1);
			pushToKafkaForValidity(Constants.FACEBOOK, Arrays.asList(request.getFbPageId()));
			logger.info("[Facebook]: Updated access token for fbPageId: {}", request.getAccessToken());
		} else {
			logger.warn("[Facebook]: Either Facebook page does not exist or Updated access token is not updated from same User. fbPageId: {}, hence not updating"
					, request.getFbPageId());
		}
		//Manvi: ES Update
	}
	@Async
	@Override
	public void scheduleFbValidation() {
		List<BusinessFBPage> businessFBPages = socialFbRepo.findByIsValid(1);
		logger.info("Triggering Facebook debug token validation job for {} pages",CollectionUtils.isNotEmpty(businessFBPages)?businessFBPages.size():null);
		if(CollectionUtils.isNotEmpty(businessFBPages)){
			businessFBPages.parallelStream().filter(fbPage -> Objects.nonNull(fbPage.getBusinessId())).forEach(businessFBPage -> {
				FbDebugToken fbDebugToken = new FbDebugToken();
				fbDebugToken.setId(businessFBPage.getId());
				fbDebugToken.setAccessToken(businessFBPage.getPageAccessToken());
				fbDebugToken.setExternalId(businessFBPage.getFacebookPageId());
				kafkaProducer.sendObject(FB_DEBUG_TOKEN,fbDebugToken);
			});
		}
	}

	@Async
	@Override
	@Retryable( value = BirdeyeSocialException.class,maxAttempts=3, backoff = @Backoff(delay = 1000))
	public void validateAccessToken(FbDebugToken fbDebugToken) {
		logger.info("Request received to validate fb access token for page {} with accessToken {}",fbDebugToken.getExternalId(),fbDebugToken.getAccessToken());
		try{
			// we are getting error in the String format from FB for invalid token. mark it as invalid if this occurs.
			if(markPageInvalidWithUnexpectedErrorResponse(fbDebugToken)) {
				kafkaProducer.sendWithKey("fb-invalid-details", fbDebugToken.getExternalId(), fbDebugToken);
				throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GENERATE_TOKEN_ON_FACEBOOK);
			}

			SocialAppCredsInfo socialAppCredsInfo = socialAppService.getFacebookAppSettings();
			DebugTokenResponse tokenResponse = fbService.getTokenDetails(fbDebugToken.getAccessToken(), socialAppCredsInfo.getChannelAccessToken());
			logger.info("Facebook access token response for page {} {}",fbDebugToken.getExternalId(),tokenResponse);
			if (tokenResponse!=null && tokenResponse.getData()!=null && tokenResponse.getData().getError() != null && FacebookUtils.isUnAuthorisedErrorCode(tokenResponse.getData().getError().getCode(),tokenResponse.getData().getError().getSubcode())){
				kafkaProducer.sendWithKey("fb-invalid-details", fbDebugToken.getExternalId(), fbDebugToken);
				throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GENERATE_TOKEN_ON_FACEBOOK);
			}
		}catch (BirdeyeSocialException e){
			logger.info("Exception occurred while validating fb page access token for page {} with error {}",fbDebugToken.getExternalId(),e);
			throw e;
		}
	}

	private boolean markPageInvalidWithUnexpectedErrorResponse(FbDebugToken details) {
		boolean markPageInvalid = false;
		try {
			if(Objects.nonNull(details.getErrorResponse())
					&& details.getErrorResponse().equalsIgnoreCase(UnexpectedFacebookErrorResponse.SORRY_ERROR_RESPONSE.getName())) {
				markPageInvalid = true;
			}
//			fbService.getUserDetailsDebug(pageId, pageAccessToken);
		} catch (Exception ex) {
			logger.info("Something went wrong while checking audit error response for audit details {}", details);
		}
		return markPageInvalid;
	}

	@Override
	public void saveMapping(LocationPageMappingRequest locationPageMappingRequest) {
		logger.info("[Facebook] Request received to add Platform mapping locationPageMappings {}", locationPageMappingRequest);
		List<BusinessFBPage> businessFBPages = socialFbRepo.findByFacebookPageId(locationPageMappingRequest.getPageId());
		List<BusinessFacebookPageNew> businessFacebookPageNews = businessFbPageRepo.findByFacebookPageId(locationPageMappingRequest.getPageId());
		if(CollectionUtils.isNotEmpty(businessFBPages)){
			BusinessFBPage businessFBPage = businessFBPages.get(0);
			if(CollectionUtils.isEmpty(businessFacebookPageNews)){
				this.saveMapping(businessFBPage,locationPageMappingRequest.getLocationId(),null);
			}else{
				logger.info("[Facebook] Mapping already found in platform table , hence updating data for {}",locationPageMappingRequest);
				businessFacebookPageNews.forEach(facebookPageNew ->updateBusinessFacebookPages(businessFBPage,facebookPageNew));
			}
		}
	}

	@Override
	public void updateFbBusinessIsValid(SocialEsValidRequest socialEsValidRequest) {
		logger.info("[Facebook] Request received to update is valid in platform for gmb {}",socialEsValidRequest);
		List<BusinessFacebookPageNew> businessFacebookPages = businessFbPageRepo.findByFacebookPageId(socialEsValidRequest.getIntegrationId());
		if(CollectionUtils.isNotEmpty(businessFacebookPages)){
			businessFacebookPages.forEach(facebookPageNew -> facebookPageNew.setIsValid(socialEsValidRequest.getIsValid()));
			businessFbPageRepo.save(businessFacebookPages);
		}else{
			logger.info("[Facebook] No pages found to update is valid for paltform db for {}",socialEsValidRequest);
		}
	}

	@Override
	public void removePlatformMapping(List<LocationPageMappingRequest> locationPageMappings) {
		logger.info("[Facebook] remove Platform GMB page mappings with locationPageMappings {}", locationPageMappings);
		Set<String> pageIds = locationPageMappings.stream().map(LocationPageMappingRequest::getPageId).collect(Collectors.toSet());
		removeEntry(pageIds);
	}

	@Override
	public void removePlatformEntry(List<ChannelPageRemoved> input) {
		logger.info("[Facebook] remove Platform page for {}", input);
		Set<String> pageIds = input.stream().map(ChannelPageRemoved::getPageId).collect(Collectors.toSet());
		removeEntry(pageIds);
	}

	private void removeEntry(Set<String> pageIds) {
		List<BusinessFacebookPageNew> existingPages = businessFbPageRepo.findByFacebookPageIdIn(pageIds);
		if(CollectionUtils.isNotEmpty(existingPages)){
			businessFbPageRepo.delete(existingPages);
			businessFbPageRepo.flush();
		}else{
			logger.info("[Facebook] No pages found to remove from platform db for pages {}",pageIds);
		}
	}

	@Override
	public List<Integer> migrateForRatings(Integer limit) {
		List<Integer> failedList = new ArrayList<>();
		logger.info("fetching records to update subscriptions..");
		Page<BusinessFBPage> fbPages = socialFbRepo.findAll((root, criteriaQuery, criteriaBuilder) -> {
			List<Predicate> predicates = new ArrayList<>();
			predicates.add(criteriaBuilder.and(criteriaBuilder.equal(root.get("isValid"), 1)));
			predicates.add(criteriaBuilder.and(criteriaBuilder.equal(root.get("ratingsOpted"), 0)));
			return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
		},new PageRequest(0,limit,new Sort(Sort.Direction.DESC,"id")));

		if(fbPages!=null && fbPages.getTotalElements() > 0){
			logger.info("Total {} records found to update",fbPages.getTotalElements());
			fbPages.forEach(facebookPage -> {
				try {
					if(facebookPage.getMessengerOpted()!=0){
						fbMsgService.fbPageSubscribeApps(facebookPage.getFacebookPageId(),facebookPage.getPageAccessToken(), Constants.FB_MESSENGER_RATINGS_SUBSCRIPTION_FIELDS);
					}else{
						fbMsgService.fbPageSubscribeApps(facebookPage.getFacebookPageId(),facebookPage.getPageAccessToken(), Constants.FB_RATINGS_SUBSCRIPTION_FIELDS);
					}
					facebookPage.setRatingsOpted(1);
				} catch (Exception e) {
					facebookPage.setRatingsOpted(2);
					failedList.add(facebookPage.getId());
					logger.error("Reconnect, Messenger subscribe failed for businessId: {} and pageId: {} with exception {}", facebookPage.getBusinessId(), facebookPage.getFacebookPageId(),e);
				}
				socialFbRepo.save(facebookPage);
			});
		}else{
			logger.info("no record found to update..");
		}
		return failedList;
	}

	@Override
	public List<BusinessFBPage> findByBusinessId(Integer businessId) {
		return socialFbRepo.findByBusinessId(businessId);
	}
	@Override
	public List<Integer> findBusinessIdsByEnterpriseId(Long enterpriseId, Integer isValid) {
		return socialFbRepo.findBusinessIdsByEnterpriseIdAndIsValid(enterpriseId, isValid);
	}


	@Override
	public List<BusinessFBPage> findByFacebookPageIdAndIsValid(String facebookPageId, Integer valid) {
		return socialFbRepo.findByFacebookPageIdAndIsValid(facebookPageId,valid);
	}

	@Override
	public void updatePlatformMapping(SocialChannelSyncRequest socialChannelSyncRequest) {
		logger.info("[Facebook] Request received to update Platform mapping socialChannelSyncRequest {}", socialChannelSyncRequest);
		List<BusinessFBPage> businessFBPages = socialFbRepo.findByFacebookPageId(socialChannelSyncRequest.getIntegrationIdentifier());
		List<BusinessFacebookPageNew> businessFacebookPageNews = businessFbPageRepo.findByFacebookPageId(socialChannelSyncRequest.getIntegrationIdentifier());
		logger.info("businessFBPages: {}  \n businessFacebookPageNews: {}",businessFBPages,businessFacebookPageNews);
		if(CollectionUtils.isNotEmpty(businessFBPages) && CollectionUtils.isNotEmpty(businessFacebookPageNews)){
			if(businessFacebookPageNews.size()>1){
				logger.info("multiple mapping exists for fb page {}",socialChannelSyncRequest.getIntegrationIdentifier());
			}
			Optional<BusinessFBPage> bFbPage = businessFBPages.stream().filter(e->Objects.nonNull(e.getBusinessId())).findFirst();
			if(bFbPage.isPresent()){
				BusinessFBPage businessFBPage = bFbPage.get();
				businessFacebookPageNews.forEach(facebookPageNew ->updateBusinessFacebookPages(businessFBPage,facebookPageNew));
			} else  {
				logger.error("No business found with facebook page {}",socialChannelSyncRequest.getIntegrationIdentifier());
			}
		}
	}


	@Override
	public Integer getIntegrationStatus(Long accountId, List<Integer> businessIds) {
		List<SocialFBPageRepository.BFLMappingStatus> response = socialFbRepo.getValidIntegrations(accountId, businessIds);

		List<Integer> socialBusinessIds = response.stream().filter(e -> validListing(e)).map(SocialFBPageRepository.BFLMappingStatus::getBusinessId)
				.collect(Collectors.toList());
		Long unMapped = businessIds.stream().filter(l -> !socialBusinessIds.contains(l)).count();
		return unMapped.intValue();
	}

	public Page<BusinessFBPage> search(String search, PageRequest page,Long resellerId) {
		Specification<BusinessFBPage> gmb = Specifications.where((fbSpecification.hasEmail(search))).
				or(fbSpecification.hasPageName(search)).
				or(fbSpecification.hasAddress(search)).
				and(fbSpecification.isSelected(1)).
				and(fbSpecification.hasResellerId(resellerId));

		return socialFbRepo.findAll(gmb,page);

	}

	public List<BusinessFBPage> search(String search,Long resellerId) {
		Specification<BusinessFBPage> gmb = Specifications.where((fbSpecification.hasEmail(search))).
				or(fbSpecification.hasPageName(search)).
				or(fbSpecification.hasAddress(search)).
				and(fbSpecification.isSelected(1)).
				and(fbSpecification.hasResellerId(resellerId));

		return socialFbRepo.findAll(gmb);

	}

	public List<BusinessFBPage> search(String search,Long resellerId, List<Integer> businessIds) {
		Specification<BusinessFBPage> gmb = Specifications.where((fbSpecification.hasEmail(search))).
				or(fbSpecification.hasPageName(search)).
				or(fbSpecification.hasAddress(search)).
				and(fbSpecification.isSelected(1)).
				and(fbSpecification.hasResellerId(resellerId)).
				and(fbSpecification.inBusinessIds(businessIds));

		return socialFbRepo.findAll(gmb);

	}

	public Page<BusinessFBPage> searchSortAndPaginate(String search, Long resellerId, List<Integer> businessIds, PageConnectionStatus pageConnectionStatus,
													  List<Integer> createdByIds, Integer isSelected, MappingStatus mappingStatus, Integer page, Integer size,
													  PageSortDirection sortDirection, ResellerSortType sortType, Boolean locationFilterSelected) {
		Specification<BusinessFBPage> spec = Specifications.where((fbSpecification.hasResellerId(resellerId)));
		if(Objects.nonNull(search)) {
			spec = Specifications.where(spec).and(fbSpecification.hasPageName(search));
		}
		if(CollectionUtils.isNotEmpty(businessIds)) {
			if(Boolean.TRUE.equals(locationFilterSelected)) {
				if(MappingStatus.UNMAPPED.equals(mappingStatus)) return new PageImpl<>(new ArrayList<>());
				else spec = Specifications.where(spec).and(fbSpecification.inBusinessIds(businessIds));
			} else {
				if(MappingStatus.MAPPED.equals(mappingStatus)) {
					spec = Specifications.where(spec).and(fbSpecification.inBusinessIds(businessIds));
				} else if(MappingStatus.UNMAPPED.equals(mappingStatus)) {
					spec = Specifications.where(spec).and(fbSpecification.hasBusinessIdNullOrNotNull(true));
				} else {
					Specification<BusinessFBPage> orSpec = Specifications.where(fbSpecification.inBusinessIds(businessIds));
					orSpec = Specifications.where(orSpec).or(fbSpecification.hasBusinessIdNullOrNotNull(true));
					spec = Specifications.where(spec).and(orSpec);
				}
			}
		} else {
			if(MappingStatus.MAPPED.equals(mappingStatus)) {
				return new PageImpl<>(new ArrayList<>());
			} else {
				spec = Specifications.where(spec).and(fbSpecification.hasBusinessIdNullOrNotNull(true));
			}
		}
		if(PageConnectionStatus.CONNECTED.equals(pageConnectionStatus)) {
			spec = Specifications.where(spec).and(fbSpecification.inValidityTypes(Arrays.asList(ValidTypeEnum.VALID.getId())));
			if(Objects.nonNull(isSelected)) {
				spec = Specifications.where(spec).and(fbSpecification.isSelected(isSelected));
			}
		} else if(PageConnectionStatus.DISCONNECTED.equals(pageConnectionStatus)) {
			spec = Specifications.where(spec).and(fbSpecification.inValidityTypes(Arrays.asList(ValidTypeEnum.INVALID.getId(),
					ValidTypeEnum.PARTIAL_VALID.getId())));
		} else {
			spec = Specifications.where(spec).and(fbSpecification.isSelected(isSelected));
		}
		if(CollectionUtils.isNotEmpty(createdByIds)) {
			spec = Specifications.where(spec).and(fbSpecification.inCreatedByIds(createdByIds));
		}
		PageRequest pageRequest = null;
		if(ResellerSortType.PAGE_NAME.equals(sortType) && Objects.nonNull(sortDirection)) {
			pageRequest = new PageRequest(page, size,
					new Sort(PageSortDirection.ASC.equals(sortDirection)? Sort.Direction.ASC:Sort.Direction.DESC, "facebookPageName"));
		} else if(ResellerSortType.STATUS.equals(sortType) && Objects.nonNull(sortDirection)) {
			spec = Specifications.where(spec).and(fbSpecification.sortValidTypesInGroup(sortDirection));
			pageRequest = new PageRequest(page, size);
		} else {
			spec = Specifications.where(spec).and(fbSpecification.sortBusinessIdNullsFirst());
			pageRequest = new PageRequest(page, size);
		}

		return socialFbRepo.findAll(spec, pageRequest);
	}



	@Override
	public ConnectPagesResponse getPagesAfterConnect(Long resellerId, Integer size, Integer page) {
		return null;
	}

	@Override
	public void updatedFbPagesWithPhone() {

		List<SocialFBPageRepository.BFCreds> requestIds = socialFbRepo.getNumberOfPagesWithNoPhone();
		requestIds.forEach(r -> {
			try {
				FbUserProfileInfo fbUserProfileInfo = fbPageService.getPagePhoneNumber(r.getPageAccessToken());

				if(Objects.isNull(fbUserProfileInfo)) {
					logger.info("[Facebook] Cound not fetch page information for facebook page ID {}", r.getFacebookPageId());
				} else if (Objects.isNull(fbUserProfileInfo.getPhone())) {
					logger.info("[Facebook] Phone number is not available for facebook page ID {}", r.getFacebookPageId());
				} else {
					socialFbRepo.updatePagePhoneNumber(fbUserProfileInfo.getPhone(), r.getId());
				}

			} catch (Exception e) {
				logger.info("[Facebook] something went while updating the phone number for pageId {} . Error {}", r.getFacebookPageId(), e);
			}

		});

	}

	@Override
	public void removePageMap(Integer businessId) {
		logger.info("Remove business id from BusinessFBPage where businessId : {}",businessId);
		List<BusinessFBPage> businessFBPages = socialFbRepo.findByBusinessIdIn(Collections.singleton(businessId));
		if(CollectionUtils.isEmpty(businessFBPages)) {
			logger.info("Remove page map : {}",businessId);
			return;
		}
		businessFBPages.forEach(page ->{
			logger.info("Remove mapping for page id :{}",page.getFacebookPageId());
			commonService.removeMapping(page.getBusinessId(),page.getFacebookPageId(),SocialChannel.FACEBOOK.getName(), null);
		});
	}

	@Override
	public List<SocialNotificationAudit> auditNotifications(Object notificationObject) {
		List<SocialNotificationAudit> socialNotificationAuditList = new ArrayList<>();
		try{
			SocialNotificationAudit audit = new SocialNotificationAudit();
			ObjectMapper mapper = new ObjectMapper();
			JsonNode node = mapper.convertValue(notificationObject, JsonNode.class);
			for (Iterator<String> it = node.fieldNames(); it.hasNext(); ) {
				String s = it.next();
				if (s.equals("entry")) {
					JsonNode entry = node.get(s);
					if (Objects.nonNull(entry)) {
						JsonNode id = entry.get(0).get("id");
						audit.setEvent_id(id.asText());
						logger.info("Social Notification audit for facebook started for page id : {}",id.asText());
						JsonNode messaging = entry.get(0).get("messaging");
						if (Objects.nonNull(messaging)) {
							audit.setEvent_type(NotificationAuditTypes.FB_MESSAGE_NOTIFICATION.name());
							JsonNode messagingNode = messaging.get(0);
							if(Objects.nonNull(messagingNode)) {
								for (Iterator<String> field = messagingNode.fieldNames(); field.hasNext(); ) {
									String s1 = field.next();
									switch (s1) {
										case "read":
											if(Objects.nonNull(messagingNode.get(s1).get("watermark"))) {
												audit.setMessage_type(MessageTypeEnum.READ.name());
											}
											break;
										case "delivery":
											if(Objects.nonNull(messagingNode.get(s1).get("watermark"))) {
												audit.setMessage_type(MessageTypeEnum.DELIVERY.name());
											}
											break;
										case "message":
											if(Objects.nonNull(messagingNode.get(s1).get("mid"))) {
												audit.setMessage_type(MessageTypeEnum.UNKNOWN.name());
												if (Objects.nonNull(messagingNode.get(s1).get("is_echo"))) {
													String isEcho = messagingNode.get(s1).get("is_echo").asText();
													audit.setIs_echo_message(Boolean.valueOf(isEcho));
												}
											}
											break;
										case "sender":
											JsonNode senderNode = messagingNode.get(s1);
											if (Objects.nonNull(senderNode)) {
												String sender = senderNode.get("id").asText();
												audit.setSender(sender);
											}
											break;
										case "recipient":
											JsonNode recipientNode = messagingNode.get(s1);
											if (Objects.nonNull(recipientNode)) {
												String recipient = recipientNode.get("id").asText();
												audit.setRecipient(recipient);
											}
											break;

									}
								}
							}
						}
						JsonNode changesNode = entry.get(0).get("changes");
						if(Objects.nonNull(changesNode)){
							JsonNode changesNodeList = changesNode.get(0);
							if(Objects.nonNull(changesNodeList)){
								for (Iterator<String> jChangeNode = changesNodeList.fieldNames(); jChangeNode.hasNext();){
									String changeString = jChangeNode.next();
									switch (changeString){
										case "value":
											JsonNode changes = changesNodeList.get(changeString);
											if(Objects.nonNull(changes)){
												String openGraphId = changes.get("open_graph_story_id").asText();
												audit.setReview_id(openGraphId);
												String item = changes.get("item").asText();
												audit.setFb_item(item);
											}
											break;
										case "field":
											String field = changesNodeList.get("field").asText();
											if(Objects.equals(field,"ratings")) {
												audit.setEvent_type(NotificationAuditTypes.FB_REVIEW_NOTIFICATION.name());
											} else if(Objects.equals(field,"feed")) {
												audit.setEvent_type(NotificationAuditTypes.FB_COMMENT_NOTIFICATION.name());
											}
											audit.setReview_type(field);
											break;
									}
								}
							}
						}
					}
				}
			}
			audit.setChannel(Constants.FACEBOOK);
			audit.setEvent_payload(JSONUtils.toJSON(notificationObject));
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			audit.setCreated_at(simpleDateFormat.format(new Date().getTime()));
			socialNotificationAuditList.add(audit);
			logger.info("Object created for audit : {}",audit.getEvent_id());
		}catch (Exception e){
			logger.info("Something happened while auditing the data {} exception {}",notificationObject,e.getMessage());
		}
		return socialNotificationAuditList;
	}

	@Async
	@Override
	public void getFbPageMentionsData(FacebookPageDataFetchRequest request) {
		logger.info("Received request to get facebook data for requestid : {} and business {} and env: {}",
				request.getRequestIdentifier(), request.getBusinessDetails(), request.getEnvType());
		List<FacebookMentionsData> facebookData = getFacebookPageDataForBusinessDetails(request.getBusinessDetails(),
				request.getSearchQuery());

		FacebookPageDataResponse response = new FacebookPageDataResponse(request.getBusinessDetails(),
				request.getRequestIdentifier(), request.getAttempt(), request.getRequestType());

		if (CollectionUtils.isEmpty(facebookData)) {
			response.setStatus(GMBNotificationStatus.FAILED.name());
			response.setComment("No page data fetched for given search query");
		} else {
			response.setStatus(GMBNotificationStatus.SUCCESS.name());
		}
		response.setData(facebookData);
		if("free-prod".equalsIgnoreCase(request.getEnvType())) {
			kafkaProducer.sendObject(FREE_FB_PAGE_DATA_RESPONSE, response);
		} else {
			kafkaProducer.sendObject(FB_PAGE_DATA_RESPONSE, response);
		}
	}

	@Override
	public void getFbPageMentionsDataForListing(FacebookPageDataFetchRequest request) {
		Integer businessId = request.getBusinessDetails().getBusinessId();
		Integer requestIdentifier = request.getRequestIdentifier();
		String search = new StringBuilder("(")
				.append(request.getBusinessDetails().getName().replaceAll(" ", "|"))
				.append(")")
				.append(StringUtils.isNotEmpty(request.getBusinessDetails().getAddress()) ? " " + request.getBusinessDetails().getAddress().split(",")[0] : "")
				.append(StringUtils.isNotEmpty(request.getBusinessDetails().getZip()) ? " " + request.getBusinessDetails().getZip() : "")
				.toString();

		logger.info("Received request to fetch mentions for business id: {} for search: {}", businessId, search);
		FacebookPageDataResponse response = new FacebookPageDataResponse(request.getBusinessDetails(),
				requestIdentifier, request.getAttempt(), "Listing validator");

//		String accessToken = CacheManager.getInstance()
//				.getCache(SystemPropertiesCache.class)
//				.getBirdeyeAccessToken();

		String accessToken = fbPageService.getPPCASystemUserToken();
		try {
			FacebookPageSearch facebookPageSearch = fbService.getPageMentions(search, accessToken,
					"id,name,link,location", null);
			logger.info("fb pages fetched for business id: {}, pages {}", businessId, facebookPageSearch);

			FacebookMentionsData mentionsDataMatched = getMatchedMentionsData(facebookPageSearch,
					request.getBusinessDetails().getName(), request.getBusinessDetails().getZip());

			if (Objects.nonNull(mentionsDataMatched)) {
				response.setStatus(GMBNotificationStatus.SUCCESS.name());
				response.setData(Arrays.asList(mentionsDataMatched));
				kafkaProducer.sendObject(Constants.FB_LISTING_VALIDATOR_PAGE_DATA_RESPONSE, response);
				return;
			}
			response.setStatus(GMBNotificationStatus.FAILED.name());
			response.setComment("No page data fetched for given request");
			kafkaProducer.sendObject(FB_LISTING_VALIDATOR_PAGE_DATA_RESPONSE, response);
		} catch (Exception e) {
			publishToKafkaForFailedListingValidation("Error in fetching fb page data", response, request);
		}
	}

	private void publishToKafkaForFailedListingValidation(String comment, FacebookPageDataResponse response,
														  FacebookPageDataFetchRequest request) {
		Integer maxRetryAttempts = CacheManager.getInstance()
				.getCache(SystemPropertiesCache.class)
				.getRetryMaxAttempts();
		if (maxRetryAttempts.equals(request.getAttempt())) {
			response.setStatus(GMBNotificationStatus.FAILED.name());
			response.setComment(comment);
			kafkaProducer.sendObject(FB_LISTING_VALIDATOR_PAGE_DATA_RESPONSE, response);
			return;
		}

		request.setAttempt(request.getAttempt() + 1);
		kafkaProducer.sendObject(Constants.FETCH_LISTING_FB_PAGE_DATA, request);
	}

	private FacebookMentionsData getMatchedMentionsData(FacebookPageSearch facebookPageSearch, String name, String zip) {
		for (FacebookMentionsData data : facebookPageSearch.getData()) {
			if (name.equalsIgnoreCase(data.getName()) && Objects.nonNull(data.getLocation()) &&
					Objects.nonNull(zip) && zip.equalsIgnoreCase(data.getLocation().getZip())) {
				return data;
			}
		}
		return null;
	}


	private List<FacebookMentionsData> getFacebookPageDataForBusinessDetails(BusinessDetails businessDetails, String searchQuery) {
		logger.info("Received request to fetch mentions for business id: {} for search: {}", businessDetails.getBusinessId(), searchQuery);

//		String accessToken = CacheManager.getInstance()
//				.getCache(SystemPropertiesCache.class)
//				.getBirdeyeAccessToken();

		String accessToken = fbPageService.getPPCASystemUserToken();
		try {
			FacebookPageSearch facebookPageSearch = fbService.getPageMentions(searchQuery, accessToken,
					"id,name,link,location", null);
			logger.info("fb pages fetched for business id: {}, pages {}", businessDetails.getBusinessId(), facebookPageSearch);

			return CollectionUtils.isEmpty(facebookPageSearch.getData()) ? Collections.emptyList() : facebookPageSearch.getData();
			//getMatchedMentionsData(facebookPageSearch,businessDetails);
		} catch (Exception e) {
			throw e;
		}
	}

	// we are checking for read insight permissions.
	private boolean validListing(SocialFBPageRepository.BFLMappingStatus e) {
		String permissions = e.getPagePermissions();
		return (permissions!= null && permissions.contains("read_insights"));
	}

	@Override
	public void validityCheckForFB(Collection<String> locationIds) {
		if(CollectionUtils.isEmpty(locationIds)){
			logger.error("Location ids can not be null");
			return;
		}
		logger.info("Request received to update validity column for location ids: {}", locationIds);
		List<BusinessFBPage> pages = socialFbRepo.findByFacebookPageIdIn(locationIds);
		if(CollectionUtils.isEmpty(pages)){
			logger.info("No pages found for location ids : {}",locationIds);
			return;
		}
		List<BusinessFBPage> businessLocations = new ArrayList<>();
		Map<Long, List<BusinessFBPage>> mapPages = generateMapForFacebookMessageEnabled(pages);
		mapPages.entrySet().parallelStream().forEach(map -> {
			try {
				Boolean facebookMessage = false;
				Long enterpriseId = map.getKey();
				if (Objects.nonNull(enterpriseId)) {
					facebookMessage = webChatEnabled(enterpriseId, false);
				}
				Boolean finalFacebookMessage = facebookMessage;
				map.getValue().forEach(page -> {
					page.setCanPost(fbSocialService.getFacebookPostPermission(Collections.singletonList(page),
							Collections.singletonList(SocialMessageModule.PUBLISH.name())) ? 1 : 0);
					businessLocations.add(getValidity(page, finalFacebookMessage));
				});

			} catch (Exception e) {
				logger.info("Error from core {}", e.getMessage());
			}
		});
		socialFbRepo.save(businessLocations);
		logger.info("Successfully saved to db");
	}

	@Override
	public void updateEnterpriseWithNoReseller() {
		List<SocialFBPageRepository.MigrationBFL> enterpriseIds = socialFbRepo.findByEnterpriseIdIsNotNullAndResellerIdIsNull();
		enterpriseIds.forEach(value ->{
			logger.info("Reseller: Fb pages with enterprise Id: {}", value);
			try {
				BusinessLiteDTO businessLiteDTO= businessCoreService.getBusinessLiteByNumber(value.getEnterpriseId());
				if(Objects.nonNull(businessLiteDTO.getResellerId()) && !Constants.RESELLER_MIGRATION_AVOID_LIST.contains(businessLiteDTO.getResellerId())) {
					BusinessLiteDTO resellerDetails = businessCoreService.getBusinessLite(businessLiteDTO.getResellerId(), false);
					socialFbRepo.updateResellerId(resellerDetails.getBusinessNumber(), value.getEnterpriseId());
					final String userEmailById;
					if (value.getRequestId().startsWith(OPEN_URL_REQUEST_PREFIX)) {
						userEmailById = businessGetPageOpenUrlReqRepo.findUserEmailById(value.getRequestId());
					} else {
						userEmailById  = businessGetPageReqRepo.findUserEmailById(Integer.valueOf(value.getRequestId()));
					}

					socialFbRepo.updateUserEmailId(userEmailById, value.getId());

					String logs = "ResellerId " + resellerDetails.getBusinessNumber() + " is updated for enterpriseId: " + value;
					socialErrorMessageService.saveMessageToSocialMigration(SocialChannel.FACEBOOK.getName(), logs, false);
				} else {
					String logs = "ResellerId " + businessLiteDTO.getResellerId() + " cannot be updated for enterpriseId " + value + " as it is forbidden";
					socialErrorMessageService.saveMessageToSocialMigration(SocialChannel.FACEBOOK.getName(), logs, true);
				}
			} catch (Exception e) {
				String logs = "Cound not update resellerId for enterprise id " + value + " Error: " + e.getMessage();
				socialErrorMessageService.saveMessageToSocialMigration(SocialChannel.FACEBOOK.getName(), logs, true);
			}

		});
	}

	@Override
	public void validityCheckForAllFBPages() {
		List<BusinessFBPage> pages = socialFbRepo.findAll();
		List<BusinessFBPage> businessLocations = new ArrayList<>();
		Map<Long, List<BusinessFBPage>> mapPages = generateMapForFacebookMessageEnabled(pages);
		for (Map.Entry<Long, List<BusinessFBPage>> map : mapPages.entrySet()) {
			try {
				Boolean facebookMessage = false;
				Long enterpriseId = map.getKey();
				if (Objects.nonNull(enterpriseId)) {
					facebookMessage = webChatEnabled(enterpriseId, false);
				}
				Boolean finalFacebookMessage = facebookMessage;
				map.getValue().stream().forEach(page -> businessLocations.add(getValidity(page, finalFacebookMessage)));
			} catch (Exception e) {
				logger.info("Error from core {}",e.getMessage());
			}
		}
		socialFbRepo.save(businessLocations);
		logger.info("Successfully saved to db");
	}

	private BusinessFBPage getValidity(BusinessFBPage page, Boolean webChatEnabled) {
		Validity validity = fetchValidityAndErrorMessage(page, webChatEnabled, null);
		if(Objects.nonNull(validity.getValidType()) ) {
			if(validity.getValidType().equalsIgnoreCase(ValidTypeEnum.PARTIAL_VALID.getName())){
				page.setValidType(ValidTypeEnum.PARTIAL_VALID.getId());
			}else if(validity.getValidType().equalsIgnoreCase(ValidTypeEnum.VALID.getName())){
				page.setValidType(ValidTypeEnum.VALID.getId());
			}else {
				page.setValidType(ValidTypeEnum.INVALID.getId());
			}
		}
		return page;
	}




	public Map<Long ,List<BusinessFBPage>> generateMapForFacebookMessageEnabled(List<BusinessFBPage> pages){
		Map<Long ,List<BusinessFBPage>> mapPages = pages.stream().filter(page -> Objects.nonNull(page.getEnterpriseId())).collect(groupingBy(BusinessFBPage::getEnterpriseId));

		// for null enterpriseIds
		List<BusinessFBPage> fbPages = pages.stream().filter(page -> Objects.isNull(page.getEnterpriseId())).collect(Collectors.toList());
		mapPages.put(null,fbPages);
		return mapPages;
	}

	private void updateBusinessFacebookPages(BusinessFBPage rawPage, BusinessFacebookPageNew businessFacebookPageNew) {
		logger.info("[Facebook] updating platform mapping for page {}",rawPage.getFacebookPageId());
		businessFacebookPageNew.setAccessToken(rawPage.getPageAccessToken());
		businessFacebookPageNew.setIsValid(rawPage.getIsValid());
		businessFacebookPageNew.setFacebookPageName(rawPage.getFacebookPageName());
		businessFacebookPageNew.setProfilePictureUrl(rawPage.getPictureUrl());
		businessFacebookPageNew.setSingleLineAddress(rawPage.getSingleLineAddress());
		if (businessFacebookPageNew.getFacebookUserId()!= null && !businessFacebookPageNew.getFacebookUserId().equalsIgnoreCase(rawPage.getUserId())) {
			businessFacebookPageNew.setFacebookUserId(rawPage.getUserId());
		}
		businessFacebookPageNew.setErrorLog(rawPage.getErrorLog());
		businessFacebookPageNew.setFbErrorSubcode(rawPage.getFbErrorSubcode());
		businessFacebookPageNew.setUpdatedBy(businessFacebookPageNew.getUpdatedBy());
		businessFacebookPageNew.setUpdatedAt(new Date());
		businessFacebookPageNew.setMessengerOpted(rawPage.getMessengerOpted());
		businessFacebookPageNew.setRatingsOpted(rawPage.getRatingsOpted());
		businessFacebookPageNew.setBusinessId(rawPage.getBusinessId());
		businessFbPageRepo.saveAndFlush(businessFacebookPageNew);
	}

	private void pushToKafkaForValidity(String channel, Collection<String>locationIds) {
		ValidityRequestDTO validityRequestDTO = new ValidityRequestDTO();
		validityRequestDTO.setChannel(channel);
		validityRequestDTO.setFacebookPageId(locationIds);
		kafkaProducer.sendObject(Constants.CHECK_VALIDITY,validityRequestDTO);
	}

	@Override
	public void initiateDPSync() {

	}

	@Override
	public void syncFacebookDP(DpSyncRequest facebookDpSyncRequest) {
		logger.info("Request received to sync dp for facebook account with id: {}",facebookDpSyncRequest.getId());
		try {
			List<BusinessFBPage> businessFBPages = socialFbRepo.findByFacebookPageIdAndIsValid(facebookDpSyncRequest.getPageId(),1);
			if(CollectionUtils.isEmpty(businessFBPages)) {
				logger.info("no page found");
				return;
			}
			BusinessFBPage businessFBPage = businessFBPages.get(0);
			FacebookPageInfo pageInfo = fbService.getPageDetailsByPageId(null, businessFBPage.getPageAccessToken(), businessFBPage.getFacebookPageId());

			if(Objects.nonNull(pageInfo) && Objects.nonNull(pageInfo.getPicture()) && StringUtils.isNotEmpty(pageInfo.getPicture().getUrl())) {
				businessFBPage.setFacebookPagePictureUrl(pageInfo.getPicture().getUrl());
				socialFbRepo.saveAndFlush(businessFBPage);
				commonService.uploadPageImageToCDN(businessFBPage);
			}

		} catch (Exception e) {
			logger.info("Exception while syncing dp for facebool account with id: {}",facebookDpSyncRequest.getId(),e);
		}
	}

	@Override
	public void removeFacebookPagesByPageIds(List<String> pageIds) {
		List<BusinessFBPage> businessFBPages = socialFbRepo.findByFacebookPageIdIn(pageIds);
		if (CollectionUtils.isEmpty(businessFBPages)) {
			logger.info("No fb pages found with page ids :{}", pageIds);
			return;
		}
		removeFacebookPages(businessFBPages);
	}

	@Override
	public List<ApprovalPageInfo> findByFacebookPageId(String pageId) {
		return socialFbRepo.findFirstByFacebookPageIdLite(pageId);
	}

	@Override
	public List<SocialBusinessPageInfo> findByBusinessIds(List<Integer> businessIds){
		return socialFbRepo.findSocialBusinessPageInfoByBusinessIdIn(businessIds);
	}

	@Override
	public String checkPageIntegrationStatusByBusinessId(Integer businessId) {
		List<BusinessFBPage> mappedPages = socialFbRepo.findByBusinessId(businessId);
		String integrationStatus;
		if(CollectionUtils.isNotEmpty(mappedPages)) {
			integrationStatus = SocialIntegrationStatus.getIntegrationStatus(mappedPages.get(0).getIsValid()).getStatus();
		}else {
			integrationStatus = SocialIntegrationStatus.getIntegrationStatus(null).getStatus();
		}
		return integrationStatus;
	}

	@Override
	public List<String> findByBusinessIdIn(List<Integer> businessIds) {
		return socialFbRepo.findDistinctFacebookPageIdByBusinessIdsIn(businessIds);
	}

	@Override
	public Map<String, CompetitorPageDetails> findByFacebookPageIdsIn(List<String> pageIds) {
		Map<String, CompetitorPageDetails> data = new HashMap<>();
		try {
			List<BusinessFBPage> pageVsLocationId = socialFbRepo.findByFacebookPageIdIn(pageIds);

			if (CollectionUtils.isEmpty(pageVsLocationId))
				return Collections.emptyMap();

			for (BusinessFBPage bfp : pageVsLocationId) {
				data.put(bfp.getFacebookPageId(),
						CompetitorPageDetails.builder().pageName(bfp.getFacebookPageName())
								.profilePictureUrl(bfp.getFacebookPagePictureUrl())
								.channel(SocialChannel.FACEBOOK.getName()).build());
			}
		} catch (Exception e) {
			logger.error("Error while fetching business id for page ids: {}", pageIds, e);
		}
		return data;
	}

	@Override
	public List<CompetitorPageDetailsDTO> getPageDetails(List<String> pageIds) {
		List<CompetitorPageDetailsDTO> response = new ArrayList<>();


		List<BusinessFBPage> businessFbPage = socialFbRepo.findByFacebookPageIdIn(pageIds);
		if(CollectionUtils.isEmpty(businessFbPage)) {
			return new ArrayList<>();
		}

		for(BusinessFBPage bfp: businessFbPage) {
			CompetitorPageDetailsDTO data =  CompetitorPageDetailsDTO.builder()
					.pageName(bfp.getFacebookPageName())
					.pageId(bfp.getFacebookPageId())
					.pageProfileUrl(bfp.getFacebookPagePictureUrl())
					.build();
			response.add(data);
		}
		return response;
	}
	private void updateFbPageForReseller(BusinessFBPage fbPage, String type, Integer locationId) {
		if (Constants.RESELLER.equals(type)) {
			BusinessLiteDTO businessLiteDTO = iBusinessCoreService.getBusinessLite(locationId, false);
			fbPage.setAccountId(businessLiteDTO.getAccountId());

			if (Objects.isNull(businessLiteDTO.getEnterpriseId())) {
				fbPage.setEnterpriseId(businessLiteDTO.getBusinessNumber());
			} else {
				fbPage.setEnterpriseId(businessLiteDTO.getEnterpriseNumber());
			}
		}
	}

	@Override
	public List<Integer> getMappedResellerLeafLocations(List<Integer> resellerLeafLocationIds) {
		if(CollectionUtils.isNotEmpty(resellerLeafLocationIds)) {
			return socialFbRepo.findAllIdByBusinessIdIn(resellerLeafLocationIds);
		}
		return Collections.emptyList();
	}

	@Override
	public FacebookPageInfo getFbPageInfo(Integer businessId) throws Exception {
		logger.info("getFbPageInfo for businessId {}", businessId);
		if (businessId == null) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Business ID cannot be null.");
		}
		List<BusinessFBPage> businessFbPages = socialFbRepo.findByBusinessIdAndIsValid(businessId, 1);
		logger.info("Facebook Pages found: {}", businessFbPages);

		if (CollectionUtils.isEmpty(businessFbPages)) {
			logger.warn("No valid Facebook pages found for businessId: {}", businessId);
			return null;
		}
		BusinessFBPage fbPage = businessFbPages.get(0);
		FacebookPageInfo facebookPageInfo = new FacebookPageInfo();
		facebookPageInfo.setId(fbPage.getFacebookPageId());
		facebookPageInfo.setLink(fbPage.getLink());
		facebookPageInfo.setName(fbPage.getFacebookPageName());
		return facebookPageInfo;
	}

	@Override
	public String getAuthorizationUrl(String origin) throws URISyntaxException {
		logger.info("Calling facebook getAuthorizationUrl for origin: {}", origin);
		String scopes = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(FB_AUTH_SCOPES);
		String fbDomain = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(FB_REDIRECT_DOMAIN, "app.birdeye.com");
		String fbRedirectUri = String.valueOf(new StrBuilder(String.format(REDIRECT_URI_GENERIC, fbDomain)));

		URIBuilder authURL = new URIBuilder(AUHTORIZATION_URI);

		authURL.addParameter("client_id", getFacebookCreds().getChannelClientId());
		authURL.addParameter("redirect_uri", fbRedirectUri);
		authURL.addParameter("state", origin);
		authURL.addParameter("response_type", "code");
		authURL.addParameter("scope", scopes);
		return authURL.toString();
	}


	/**
	 * @param requestIds
	 */
	@Override
	public List<String> getMappedRequestIds(Set<String> requestIds) {
		return socialFbRepo.findDistinctRequestIdByRequestIdIn(requestIds);
	}

	public boolean isTokenValid(String accessToken) {
		logger.info("Check if fb token is valid: {}", accessToken);
		try {
			SocialAppCredsInfo socialAppCredsInfo = socialAppService.getFacebookAppSettings();
			DebugTokenResponse tokenResponse = fbService.getTokenDetails(accessToken, socialAppCredsInfo.getChannelAccessToken());
			logger.info("Facebook access token response for page {}", tokenResponse);
			return tokenResponse == null || tokenResponse.getData() == null || tokenResponse.getData().getError() == null ||
					!FacebookUtils.isUnAuthorisedErrorCode(tokenResponse.getData().getError().getCode(), tokenResponse.getData().getError().getSubcode());
		} catch (BirdeyeSocialException e) {
			logger.info("Exception occurred while validating fb page access token", e);
			throw e;
		}
	}

}