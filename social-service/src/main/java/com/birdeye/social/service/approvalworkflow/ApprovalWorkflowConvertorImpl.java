package com.birdeye.social.service.approvalworkflow;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.SocialPostsAssetsRepository;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.dto.SocialBusinessPageInfo;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.SocialPostScheduleInfo;
import com.birdeye.social.entities.SocialPostsAssets;
import com.birdeye.social.linkedin.LinkedinService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.approval_workflow.*;
import com.birdeye.social.model.approval_workflow.ApprovalEventType;
import com.birdeye.social.service.*;
import com.birdeye.social.service.tiktok.TiktokAccountService;
import com.birdeye.social.sro.SocialTagBasicDetail;
import com.birdeye.social.sro.socialReseller.SocialBusinessStatusResponse;
import com.birdeye.social.utils.ConversionUtils;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import com.birdeye.social.utils.TimeZoneUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.GOOGLE_OFFER;
import static com.birdeye.social.utils.ConversionUtils.convertToGMBOfferResponse;
import static com.birdeye.social.utils.ConversionUtils.convertToResponseForBusinessStatus;

@Service
public class ApprovalWorkflowConvertorImpl implements ApprovalWorkflowConvertorService {

    @Autowired
    private FacebookSocialAccountService facebookSocialAccountService;

    @Autowired
    private TwitterSocialAccountService twitterSocialAccountService;

    @Autowired
    private InstagramSocialService instagramSocialService;

    @Autowired
    private YouTubeAccountService youTubeAccountService;

    @Autowired
    private LinkedinService linkedinService;

    @Autowired
    private GoogleSocialAccountService googleSocialAccountService;

    @Autowired
    private SocialPostsAssetsRepository socialPostsAssetsRepository;

    @Autowired
    private AppleAccountService appleAccountService;

    @Autowired
    private SocialTagService socialTagService;
    @Autowired
    private ISocialPostsAssetService socialPostsAssetService;
    @Autowired
    private CommonService commonService;

    @Autowired
    private TiktokAccountService tiktokAccountService;

    private static final String PATTERN_DATE = "EEE, MMM dd, yyyy h:mm a";

    private static final Logger logger = LoggerFactory.getLogger(ApprovalWorkflowConvertorImpl.class);

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Override
    public ApprovalEventFromSocial prepareCreateEventForApprovalWorkflow(SocialPostInputMessageRequest socialPost, Integer socialPostId) {
        Integer creator = Objects.nonNull(socialPost.getCreatedBy())?socialPost.getCreatedBy():socialPost.getEditedBy();
        return ApprovalEventFromSocial.builder()
                .submitterId(creator)
                .updatedBy(getUserEmail(creator))
                .approvalWorkflowId(socialPost.getApprovalWorkflowId())
                .enterpriseId(socialPost.getBusinessId())
                .entityId(socialPostId)
                .entityName(ApprovalEnum.SOCIAL.name())
                .eventType(ApprovalEventType.CREATE.name())
                .build();

        // to add user email
    }

    @Override
    public ApprovalEventFromSocial prepareEventForDeleteApprovalWorkflow(SocialPost socialPost, Integer enterpriseId) {

        ApprovalMetadata approvalMetadata =  JSONUtils.fromJSON(socialPost.getApprovalMetadata(), ApprovalMetadata.class);
        return ApprovalEventFromSocial.builder()
                .submitterId(socialPost.getCreatedBy())
                .updatedBy(getUserEmail(socialPost.getCreatedBy()))
                .approvalWorkflowId(socialPost.getApprovalWorkflowId())
                .approvalRequestId(Objects.nonNull(approvalMetadata)?approvalMetadata.getApprovalRequestId():null)
                .approvalUUId(Objects.nonNull(approvalMetadata)?approvalMetadata.getApprovalUUId():null)
                .entityId(socialPost.getId())
                .enterpriseId(enterpriseId)
                .entityName(ApprovalEnum.SOCIAL.name())
                .eventType(ApprovalEventType.DELETE.name())
                .build();
    }

    @Override
    public ApprovalEventFromSocial prepareEventForEditApprovalWorkflow(SocialPost socialPost, Integer enterpriseId, Integer approvalWorkflowId) {
        ApprovalMetadata approvalMetadata =  JSONUtils.fromJSON(socialPost.getApprovalMetadata(), ApprovalMetadata.class);
        return ApprovalEventFromSocial.builder()
                .submitterId(socialPost.getCreatedBy())
                .updatedBy(getUserEmail(socialPost.getCreatedBy()))
                .approvalWorkflowId(approvalWorkflowId)
                .approvalRequestId(Objects.nonNull(approvalMetadata)?approvalMetadata.getApprovalRequestId():null)
                .approvalUUId(Objects.nonNull(approvalMetadata)?approvalMetadata.getApprovalUUId():null)
                .entityId(socialPost.getId())
                .enterpriseId(enterpriseId)
                .entityName(ApprovalEnum.SOCIAL.name())
                .eventType(ApprovalEventType.EDIT.name())
                .build();
    }

    @Override
    public List<Integer> getIdsFromListOfChannels(List<String> socialChannels) {
        List<Integer> sourceIds = new ArrayList<>();
        if(CollectionUtils.isEmpty(socialChannels)){
            sourceIds.add(SocialChannel.FACEBOOK.getId());
            sourceIds.add(SocialChannel.GOOGLE.getId());
            sourceIds.add(SocialChannel.TWITTER.getId());
            sourceIds.add(SocialChannel.INSTAGRAM.getId());
            sourceIds.add(SocialChannel.LINKEDIN.getId());
            sourceIds.add(SocialChannel.YOUTUBE.getId());
            sourceIds.add(SocialChannel.TIKTOK.getId());
            sourceIds.add(Constants.UNDEFINED);
            sourceIds.add(SocialChannel.APPLE_CONNECT.getId());
        } else {
            if (socialChannels.contains("facebook")) {
                sourceIds.add(SocialChannel.FACEBOOK.getId());
            }
            if (socialChannels.contains("google") || socialChannels.contains("gmb")) {
                sourceIds.add(SocialChannel.GOOGLE.getId());
            }
            if (socialChannels.contains("twitter")) {
                sourceIds.add(SocialChannel.TWITTER.getId());
            }
            if (socialChannels.contains("instagram")) {
                sourceIds.add(SocialChannel.INSTAGRAM.getId());
            }
            if (socialChannels.contains("linkedin")) {
                sourceIds.add(SocialChannel.LINKEDIN.getId());
            }
            if (socialChannels.contains("youtube")) {
                sourceIds.add(SocialChannel.YOUTUBE.getId());
            }
            if (socialChannels.contains(SocialChannel.APPLE_CONNECT.getName())) {
                sourceIds.add(SocialChannel.APPLE_CONNECT.getId());
            }
            if (socialChannels.contains(SocialChannel.TIKTOK.getName())) {
                sourceIds.add(SocialChannel.TIKTOK.getId());
            }
            if(socialChannels.contains("undefined")){
                sourceIds.add(Constants.UNDEFINED);
            }
        }
        return sourceIds;
    }

    @Override
    public ApprovalWorkflowResponse convertToApprovalResponse(List<SocialPostEsRequest> searchResponse,
                                                              Long businessNumber, Long totalCount, boolean isSearchText,
                                                              String searchText, Integer userId, String timeZone, Integer businessId, String businessName,
                                                              String requestSource) {
        logger.info("Prepare response for business id : {}", businessNumber);
        ApprovalWorkflowResponse approvalWorkflowResponse = new ApprovalWorkflowResponse();
        approvalWorkflowResponse.setTotalCount(totalCount);
        List<ApprovalWorkFlowData> posts = new ArrayList<>();
        if (CollectionUtils.isEmpty(searchResponse)) {
            logger.info("Search response is empty for business id : {}", businessNumber);
            return approvalWorkflowResponse;
        }

        Map<Long, SocialTagBasicDetail> basicDetailMap = filterTagsOnEnterpriseIdAndAddBasicDetailsToApprovalData(searchResponse, businessId);
        Map<Integer, ApprovalWorkFlowData> mapSocialPostIdWithApprovalData = new HashMap<>();
        Map<Integer, List<Integer>> mapSocialPostIdWithImagesAndVideo = new HashMap<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(PATTERN_DATE);
        searchResponse.forEach(response -> {
            Date scheduleDate = convertDateWithTimeZone(timeZone, response.getSchedule_time());
            ApprovalWorkFlowData approvalWorkFlowData = null;
            try {
                approvalWorkFlowData = convertApprovalData(response, userId, scheduleDate, businessNumber, simpleDateFormat, timeZone, businessName);
                if (Objects.nonNull(approvalWorkFlowData.getGmbOfferDetails()))
                    approvalWorkFlowData.setTopicType(GOOGLE_OFFER);
            } catch (IOException e) {
                logger.info("IO Exception occurred while converting approval response", e);
            }
            mapSocialPostIdWithApprovalData.put(response.getId(), approvalWorkFlowData);
            if (StringUtils.isNotEmpty(response.getVideo_ids()) || StringUtils.isNotEmpty(response.getImage_ids())) {
                prepareMapForImagesAndVideos(response.getVideo_ids(), response.getImage_ids(), response.getId(), mapSocialPostIdWithImagesAndVideo);
            }
            setTagsUsingTagMap(approvalWorkFlowData, response, basicDetailMap);
            setPageNameAndProfileImage(approvalWorkFlowData, response, isSearchText, searchText);
            if (restrictFbStoryAndReelPostForMobile(approvalWorkFlowData, requestSource)) {
                posts.add(approvalWorkFlowData);
            }
        });
        if (CollectionUtils.isNotEmpty(mapSocialPostIdWithImagesAndVideo.keySet())) {
            setImagesAndVideo(mapSocialPostIdWithImagesAndVideo, mapSocialPostIdWithApprovalData, businessNumber);
        }
        approvalWorkflowResponse.setPosts(posts);
        return approvalWorkflowResponse;
    }

    /**
     * Checks if the approval post should be restricted for mobile based on post type.
     * Filters out Facebook reels and stories from approval posts for mobile.
     *
     * @param approvalWorkFlowData The approval workflow data to check
     * @return true if the post should be included, false if it should be restricted
     */
    private boolean restrictFbStoryAndReelPostForMobile(ApprovalWorkFlowData approvalWorkFlowData, String requestSource) {
        if (!"mobile".equalsIgnoreCase(requestSource) || approvalWorkFlowData == null || StringUtils.isEmpty(approvalWorkFlowData.getPostMetaData())) {
            return true; // Include posts with no metadata
        }

        try {
            SocialPostSchedulerMetadata metadata = JSONUtils.fromJSON(approvalWorkFlowData.getPostMetaData(), SocialPostSchedulerMetadata.class);
            if (metadata == null) {
                return true; // Include posts with invalid metadata
            }

            // Check for Facebook reels and stories
            if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(approvalWorkFlowData.getChannel())
                    && StringUtils.isNotEmpty(metadata.getFbPostMetadata())) {
                FbPostMetadata fbPostMetadata = JSONUtils.fromJSON(metadata.getFbPostMetadata(), FbPostMetadata.class);
                if (fbPostMetadata != null &&
                        (FacebookPostType.REEL.getName().equalsIgnoreCase(fbPostMetadata.getType()) ||
                               FacebookPostType.STORY.getName().equalsIgnoreCase(fbPostMetadata.getType()))) {

                    logger.info("Post ID: {} is restricted on mobile as it's a Facebook {}",
                            approvalWorkFlowData.getId(), fbPostMetadata.getType());
                    return false; // Restrict Facebook reels and stories
                }
            }

            return true; // Include all other posts
        } catch (Exception e) {
            logger.error("Error checking if post should be restricted for mobile: {}", e.getMessage(), e);
            return true; // Include posts if there's an error in checking
        }
    }

    public ApprovalWorkflowResponse convertToApprovalResponseFromDB(List<SocialPostEsRequest> searchResponse,
                                                              Long businessNumber, Long totalCount, boolean isSearchText,
                                                              String searchText, Integer userId, String timeZone, Integer businessId) {
        logger.info("Prepare response for business id : {} ",businessNumber);
        ApprovalWorkflowResponse approvalWorkflowResponse = new ApprovalWorkflowResponse();
        approvalWorkflowResponse.setTotalCount(totalCount);
        List<ApprovalWorkFlowData> posts = new ArrayList<>();
        if(CollectionUtils.isEmpty(searchResponse)){
            logger.info("Search response is empty for business id : {}",businessNumber);
            return approvalWorkflowResponse;
        }

        Map<Long, SocialTagBasicDetail> basicDetailMap = filterTagsOnEnterpriseIdAndAddBasicDetailsToApprovalData(searchResponse, businessId);
        Map<Integer,ApprovalWorkFlowData> mapSocialPostIdWithApprovalData = new HashMap<>();
        Map<Integer,List<Integer>> mapSocialPostIdWithImagesAndVideo = new HashMap<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(PATTERN_DATE);
        searchResponse.forEach(response -> {
            Date scheduleDate = convertDateWithTimeZone(timeZone,response.getSchedule_time());
            ApprovalWorkFlowData approvalWorkFlowData = null;
            try {
                approvalWorkFlowData = convertApprovalData(response,userId,scheduleDate,businessNumber,simpleDateFormat,timeZone, null);
                if(Objects.nonNull(approvalWorkFlowData.getGmbOfferDetails()))
                    approvalWorkFlowData.setTopicType(GOOGLE_OFFER);
            } catch (IOException e) {
                logger.info("IO Exception occurred while converting approval response",e);
            }
            mapSocialPostIdWithApprovalData.put(response.getId(),approvalWorkFlowData);
            if(StringUtils.isNotEmpty(response.getVideo_ids()) || StringUtils.isNotEmpty(response.getImage_ids())) {
                prepareMapForImagesAndVideos(response.getVideo_ids(), response.getImage_ids(), response.getId(), mapSocialPostIdWithImagesAndVideo);
            }
            setTagsUsingTagMap(approvalWorkFlowData, response, basicDetailMap);
            setPageNameAndProfileImage(approvalWorkFlowData,response,isSearchText,searchText);
            posts.add(approvalWorkFlowData);
        });
        if(CollectionUtils.isNotEmpty(mapSocialPostIdWithImagesAndVideo.keySet())) {
            setImagesAndVideo(mapSocialPostIdWithImagesAndVideo, mapSocialPostIdWithApprovalData, businessNumber);
        }
        approvalWorkflowResponse.setPosts(posts);
        return approvalWorkflowResponse;
    }

    private Map<Long, SocialTagBasicDetail>  filterTagsOnEnterpriseIdAndAddBasicDetailsToApprovalData(List<SocialPostEsRequest> socialPostEsRequestList, Integer enterpriseId) {
        Set<Long> allTagIds = new HashSet<>();
        for(SocialPostEsRequest socialPostEsRequest: socialPostEsRequestList) {
            if(CollectionUtils.isNotEmpty(socialPostEsRequest.getTagIds()))
                allTagIds.addAll(socialPostEsRequest.getTagIds());

        }
        if(CollectionUtils.isEmpty(allTagIds)) {
            return new HashMap<>();
        }

        Map<Long, SocialTagBasicDetail> tagIdVsBasicTagDetailsMap = socialTagService.getTagIdsBasicDetailsFilteredOnEnterpriseId(allTagIds, enterpriseId);
        return tagIdVsBasicTagDetailsMap;
    }

    private void setTagsUsingTagMap(ApprovalWorkFlowData approvalWorkFlowData, SocialPostEsRequest socialPostEsResponse,Map<Long, SocialTagBasicDetail> basicDetailMap) {
        if(Objects.isNull(approvalWorkFlowData) || MapUtils.isEmpty(basicDetailMap) ||
        Objects.isNull(socialPostEsResponse) || CollectionUtils.isEmpty(socialPostEsResponse.getTagIds())) {
            return;
        }

        socialPostEsResponse.getTagIds().retainAll(basicDetailMap.keySet());
        List<SocialTagBasicDetail> socialTagBasicDetailList = socialPostEsResponse.getTagIds().stream()
                .map(s->basicDetailMap.get(s)).collect(Collectors.toList());


        if(CollectionUtils.isNotEmpty(socialTagBasicDetailList)) {
            approvalWorkFlowData.setTags(socialTagBasicDetailList);
            approvalWorkFlowData.setTagNameList(socialTagBasicDetailList.stream().map(SocialTagBasicDetail::getName).collect(Collectors.toList()));
        }
    }

    private ApprovalWorkFlowData convertApprovalData(SocialPostEsRequest response, Integer userId, Date scheduleDate,
                                                     Long businessNumber, SimpleDateFormat simpleDateFormat,
                                                     String timeZone, String businessName) throws IOException {
        return ApprovalWorkFlowData.builder()
                .id(response.getId())
                .text(response.getPost_text())
                .type(response.getApproval_status())
                .approvalStatus(response.getApproval_status())
                .scheduleDate(simpleDateFormat.format(scheduleDate))
                .locationCount(response.getPage_ids().size())
                .authorName(response.getCreated_by_name())
                .approvalWorkFlowId(String.valueOf(response.getApproval_workflow_id()))
                .approvalRequestId(response.getApproval_request_id())
                .approvalUserIds(response.getApproval_user_ids())
                .approvalUUId(response.getApproval_uuid())
                .referenceStepId(response.getReferenceStepId())
                .rejectedBy(response.getRejectedBy())
                .rejectedById(response.getRejectedById())
                .rejectedReason(response.getRejectedReason())
                .isQuoted(Objects.nonNull(response.getQuoted_post_id()))
                .quotedPostId(response.getQuoted_post_id())
                .conversationId(response.getConversation_id())
                .businessId(response.getEnterprise_id())
                .gmbOfferDetails((StringUtils.isNotEmpty(response.getPost_meta_data()) &&
                        SocialChannel.GMB.getId()== response.getChannel()) ?
                        convertToGMBOfferResponse(JSONUtils.fromJSON(response.getPost_meta_data(),
                                SocialPostSchedulerMetadata.class),simpleDateFormat,timeZone) : null)
                .linkPreviewUrl(response.getLink_preview_url())
                .isCreator(Objects.nonNull(userId) && Objects.nonNull(response.getCreated_by()) && userId.equals(response.getCreated_by()))
                .isApprover(Objects.nonNull(userId) && CollectionUtils.isNotEmpty(response.getApproval_user_ids())
                        && response.getApproval_user_ids().contains(String.valueOf(userId)))
                .mentions(StringUtils.isNotEmpty(response.getMentions()) ? JSONUtils.collectionFromJSON(response.getMentions(), MentionData.class):null)
                .businessNumber(businessNumber)
                .businessName(businessName)
                .createdBy(response.getCreated_by())
                .postMetaData(response.getPost_meta_data())
                .isEditedPost(Objects.nonNull(response.getIsEditedPost()) && response.getIsEditedPost())
                .aiPost(response.getAiPost())
                .postCategory(fetchPostCategory(response))
                .build();
    }

    private String fetchPostCategory(SocialPostEsRequest response) throws IOException {
        if (StringUtils.isNotEmpty(response.getPost_meta_data())) {
            SocialPostSchedulerMetadata metadata = JSONUtils.fromJSON(response.getPost_meta_data(), SocialPostSchedulerMetadata.class);
            if (Objects.nonNull(metadata)) {
                if (SocialChannel.GMB.getId() == response.getChannel()) {
                    if (Objects.nonNull(metadata.getGmbPostMetaData()) && Objects.nonNull(metadata.getGmbPostMetaDataObj())) {
                        return GOOGLE_OFFER;
                    }
                } else if (SocialChannel.FACEBOOK.getId() == response.getChannel()) {
                    if (Objects.nonNull(metadata.getFbPostMetadata()) && Objects.nonNull(metadata.getFbPostMetaDataObj())) {
                        return metadata.getFbPostMetaDataObj().getType();
                    }
                } else if (SocialChannel.INSTAGRAM.getId() == response.getChannel()) {
                    if (Objects.nonNull(metadata.getIgPostMetadata()) && Objects.nonNull(metadata.getIGPostMetaDataObj())) {
                        return metadata.getIGPostMetaDataObj().getType();
                    }
                }
            }
        }
        return "post"; // Return post if no category is found
    }

    private Date convertDateWithTimeZone(String timeZone, Date date) {
        logger.info("Converting date : {}, for timezone: {}", date, timeZone);
        Date scheduleDate;
        if(StringUtils.isEmpty(timeZone)) {
            scheduleDate = date;
        } else {
            try {
                scheduleDate = TimeZoneUtil.convertToSpecificTimeZone(date,timeZone).getTime();
            } catch (Exception e) {
                logger.info("error while converting date for timezone: {}", timeZone);
                scheduleDate = date;
            }
        }
        return scheduleDate;
    }

    private void setImagesAndVideo(Map<Integer, List<Integer>> mapSocialPostIdWithImagesAndVideo,
                                   Map<Integer, ApprovalWorkFlowData> mapSocialPostIdWithApprovalData,Long businessNumber) {
        List<SocialPostsAssets> socialPostAssets = socialPostsAssetsRepository.findByIds(mapSocialPostIdWithImagesAndVideo.keySet());
        if(CollectionUtils.isEmpty(socialPostAssets)){
            logger.info("SocialPostsAssets list is empty for ids {}",mapSocialPostIdWithImagesAndVideo.keySet());
            return;
        }
        Map<Integer,SocialPostsAssets> socialPostsAssetsMap = socialPostAssets.stream().collect(Collectors.toMap(SocialPostsAssets::getId, Function.identity()));
        for (Map.Entry<Integer, List<Integer>> entry : mapSocialPostIdWithImagesAndVideo.entrySet()){
            SocialPostsAssets socialPostsAssets = socialPostsAssetsMap.get(entry.getKey());
            logger.info("Social Post Assets is present for id : {}",entry.getKey());
            for(Integer postId : entry.getValue()){
                ApprovalWorkFlowData approvalWorkFlowData = mapSocialPostIdWithApprovalData.get(postId);
                PostAssetsData postAssetsData = getPostAssetsData(socialPostsAssets, businessNumber);
                List<String> attachments = getListOfAttachments(socialPostsAssets, businessNumber);
                if (StringUtils.isNotEmpty(socialPostsAssets.getImageUrl())) {
                    prepareImageData(approvalWorkFlowData, attachments, postAssetsData);
                } else if(StringUtils.isNotEmpty(socialPostsAssets.getVideoUrl())){
                    prepareVideoData(approvalWorkFlowData, attachments, postAssetsData);
                }
            }
        }
    }

    private void prepareVideoData(ApprovalWorkFlowData approvalWorkFlowData, List<String> attachments, PostAssetsData postAssetsData) {
        List<String> videoUrls = CollectionUtils.isEmpty(approvalWorkFlowData.getVideoUrls())
                ? new ArrayList<>() : approvalWorkFlowData.getVideoUrls();
        videoUrls.addAll(attachments);
        approvalWorkFlowData.setVideoUrls(videoUrls);
        List<MediaData> videoMediaData = CollectionUtils.isEmpty(approvalWorkFlowData.getVideoUrlsMetaData())
                ? new ArrayList<>() : approvalWorkFlowData.getVideoUrlsMetaData();
        if(CollectionUtils.isNotEmpty(postAssetsData.getVideos())) {
            videoMediaData.addAll(postAssetsData.getVideos());
        }
        approvalWorkFlowData.setVideoUrlsMetaData(videoMediaData);
    }

    private void prepareImageData(ApprovalWorkFlowData approvalWorkFlowData, List<String> attachments, PostAssetsData postAssetsData) {
        List<String> imageUrls = CollectionUtils.isEmpty(approvalWorkFlowData.getImageUrls())
                ? new ArrayList<>() : approvalWorkFlowData.getImageUrls();
        imageUrls.addAll(attachments);
        commonService.sortImageUrlFromSequenceId(imageUrls,approvalWorkFlowData.getPostMetaData());
        approvalWorkFlowData.setImageUrls(imageUrls);
        List<MediaData> imageMediaData = CollectionUtils.isEmpty(approvalWorkFlowData.getImageUrlsMetaData())
                ? new ArrayList<>() : approvalWorkFlowData.getImageUrlsMetaData();
        imageMediaData.addAll(postAssetsData.getImages());
        approvalWorkFlowData.setImageUrlsMetaData(imageMediaData);
    }


    private void prepareMapForImagesAndVideos(String videoIds, String imageIds, Integer postId,Map<Integer,List<Integer>> mapSocialPostIdWithImagesAndVideo) {
        List<Integer> postAssertsIds = new ArrayList<>();
        if(StringUtils.isNotEmpty(videoIds)){
            postAssertsIds.addAll(Arrays.stream(videoIds.split(",")).map(String::trim)
                        .map(Integer::parseInt).collect(Collectors.toList()));
        }
        if(StringUtils.isNotEmpty(imageIds)){
            postAssertsIds.addAll(Arrays.stream(imageIds.split(",")).map(String::trim)
                    .map(Integer::parseInt).collect(Collectors.toList()));
        }
        for(Integer assertId : postAssertsIds) {
            List<Integer> postIds = new ArrayList<>();
            if(CollectionUtils.isEmpty(mapSocialPostIdWithImagesAndVideo.get(assertId))){
                postIds.add(postId);
            }else {
                postIds = mapSocialPostIdWithImagesAndVideo.get(assertId);
                postIds.add(postId);
            }
            mapSocialPostIdWithImagesAndVideo.put(assertId, postIds);
        }
    }

    private void setPageNameAndProfileImage(ApprovalWorkFlowData approvalWorkFlowData, SocialPostEsRequest response,boolean isSearchText,String searchText) {
        SocialChannel socialChannel = SocialChannel.getSocialChannelById(response.getChannel());
        approvalWorkFlowData.setChannel(socialChannel.getName());
        if(CollectionUtils.isEmpty(response.getPage_ids())){
            logger.info("Post doesn't contains page ids");
            return;
        }
        String pageId = response.getPage_ids().get(0);
        String pageName = response.getPage_name().get(0);
        String profileImage = response.getProfileImages().get(0);
        if(!isSearchText && StringUtils.isNotEmpty(searchText)) {
            for (int i=0 ; i<response.getPage_name().size() ; i++) {
                if(response.getPage_name().get(i).contains(searchText)){
                    pageName = response.getPage_name().get(i);
                    pageId = response.getPage_ids().get(i);
                    profileImage = response.getProfileImages().get(i);
                    break;
                }
            }
        }
        logger.info("Set Profile image and profile name : {} and channel : {}",pageId,socialChannel.getName());
        approvalWorkFlowData.setPageName(pageName);
        approvalWorkFlowData.setPageProfileImage(profileImage);
    }

    private PostAssetsData getPostAssetsData(SocialPostsAssets socialPostsAsset,Long businessNumber){
        PostAssetsData postAssetsData = new PostAssetsData();
        String type = StringUtils.isEmpty(socialPostsAsset.getVideoUrl()) ? Constants.IMAGE : Constants.VIDEO;
        if (Constants.IMAGE.equals(type)) {
            List<MediaData> images = new ArrayList<>();
            String fullUrl = socialPostsAssetService.getCompleteCdnUrlFromBaseUrl(socialPostsAsset.getImageUrl(), String.valueOf(businessNumber));
            images.add(new MediaData(fullUrl, socialPostsAsset.getAssetMetaData()));
            postAssetsData.setImages(images);
        } else {
            List<MediaData> videos = new ArrayList<>();
            List<String> videoThumbnailUrls = new ArrayList<>();
            String fullUrl = socialPostsAssetService.getCompleteCdnUrlFromBaseUrl(socialPostsAsset.getVideoUrl(), String.valueOf(businessNumber));
            videos.add(new MediaData(fullUrl, socialPostsAsset.getAssetMetaData()));
            videoThumbnailUrls.add(socialPostsAsset.getVideoThumbnail());
            postAssetsData.setVideos(videos);
            postAssetsData.setVideoThumbnailUrls(videoThumbnailUrls);
        }
        return postAssetsData;
    }

    private List<String> getListOfAttachments(SocialPostsAssets socialPostsAssets, Long businessNumber) {
        List<String> attachments = new ArrayList<>();
        if(StringUtils.isEmpty(socialPostsAssets.getVideoUrl())){
            attachments.add(socialPostsAssetService.getCompleteCdnUrlFromBaseUrl(socialPostsAssets.getImageUrl(), String.valueOf(businessNumber)));
        }else{
            attachments.add(socialPostsAssetService.getCompleteCdnUrlFromBaseUrl(socialPostsAssets.getVideoUrl(), String.valueOf(businessNumber)));
        }
        return attachments;
    }

    @Override
    public ApprovalEventFromSocial prepareEventForExpiredApprovalWorkflow(SocialPost socialPost, Integer enterpriseId) {

        ApprovalMetadata approvalMetadata =  JSONUtils.fromJSON(socialPost.getApprovalMetadata(), ApprovalMetadata.class);
        return ApprovalEventFromSocial.builder()
                .submitterId(socialPost.getCreatedBy())
                .updatedBy(getUserEmail(socialPost.getCreatedBy()))
                .approvalWorkflowId(socialPost.getApprovalWorkflowId())
                .approvalRequestId(Objects.nonNull(approvalMetadata)?approvalMetadata.getApprovalRequestId():null)
                .approvalUUId(Objects.nonNull(approvalMetadata)?approvalMetadata.getApprovalUUId():null)
                .enterpriseId(enterpriseId)
                .entityId(socialPost.getId())
                .entityName(ApprovalEnum.SOCIAL.name())
                .eventType(ApprovalEventType.TERMINATED.name())
                .build();
    }

    @Override
    public BoolQueryBuilder prepareApprovalEsQuery(Integer userId, Integer businessId , List<String> approvalStatus) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery(ApprovalEnum.enterpriseId.name(),businessId));
        if(approvalStatus.size() == 1) {
            boolQueryBuilder.must(QueryBuilders.termQuery(ApprovalEnum.userId.name(), userId));
        }
        boolQueryBuilder.must(QueryBuilders.termsQuery(ApprovalEnum.approvalStatus.name(),approvalStatus));
        return boolQueryBuilder;
    }

    @Override
    public BoolQueryBuilder prepareResetQuery(ResetCountRequest resetCountRequest) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery(ApprovalEnum.enterpriseId.name(), resetCountRequest.getEnterpriseId()));
        boolQueryBuilder.must(QueryBuilders.termsQuery(ApprovalEnum.approvalStatus.name(), resetCountRequest.getStatus()));
        if (Objects.nonNull(resetCountRequest.getUserId())) {
            boolQueryBuilder.must(QueryBuilders.termQuery(ApprovalEnum.userId.name(), resetCountRequest.getUserId()));
        }
        return boolQueryBuilder;
    }

    @Override
    public ApprovalActivity prepareApprovalActivityRequest(ApprovalWorkflowEvent approvalWorkflowEvent, CurrentApprovalDetails currentApprovalDetails) {
        return ApprovalActivity.builder()
                .postId(approvalWorkflowEvent.getEntityId())
                .updatedAt(currentApprovalDetails.getUpdatedAt())
                .userId(currentApprovalDetails.getApproverId()) //TODO make submitter id as string
                .approvalWorkflowId(approvalWorkflowEvent.getApprovalWorkflowId())
                .activity(currentApprovalDetails.getStatus())
                .reason(currentApprovalDetails.getRejectionReason())
                .build();
    }

    @Override
    public ApprovalActivity prepareApprovalActivityRequest(ApprovalWorkflowEvent approvalWorkflowEvent, Long updatedAt) {
        return ApprovalActivity.builder()
                .postId(approvalWorkflowEvent.getEntityId())
                .updatedAt(updatedAt)
                .userId(null) //TODO make submitter id as string
                .approvalWorkflowId(approvalWorkflowEvent.getApprovalWorkflowId())
                .activity(approvalWorkflowEvent.getStatus())
                .reason(null)
                .build();
    }

    @Override
    public ApprovalWorkFlowData convertToApprovalPostResponse(SocialPost socialPost, List<PageDetail> pageDetails, SocialPostScheduleInfo socialPostScheduleInfo, Long businessNumber, String timezone, String businessName) throws IOException {
        PageDetail pageDetail = pageDetails.get(0);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(PATTERN_DATE);
        BusinessCoreUser businessCoreUser = Objects.nonNull(socialPost.getCreatedBy()) ? businessCoreService.getUserInfo(socialPost.getCreatedBy()) : null;
        ApprovalMetadata approvalMetadata = Objects.nonNull(socialPost.getApprovalMetadata()) ?
                JSONUtils.fromJSON(socialPost.getApprovalMetadata(), ApprovalMetadata.class) : null;
        Date scheduleDate = Objects.isNull(timezone) ? socialPostScheduleInfo.getPublishDate() : convertDateWithTimeZone(timezone,socialPostScheduleInfo.getPublishDate());
        List<SocialTagBasicDetail> tags = socialTagService.getBasicTagDetailForSingleEntityId(Long.valueOf(socialPost.getId()), SocialTagEntityType.POST);
        ApprovalWorkFlowData approvalWorkFlowData = ApprovalWorkFlowData.builder()
                .id(socialPost.getId())
                .text(socialPost.getPostText())
                .businessId(socialPostScheduleInfo.getEnterpriseId())
                .businessNumber(businessNumber)
                .businessName(businessName)
                .scheduleDate(simpleDateFormat.format(scheduleDate))
                .mentions(StringUtils.isNotEmpty(socialPost.getMentions()) ? JSONUtils.collectionFromJSON(socialPost.getMentions(), MentionData.class):null)
                .pageName(pageDetail.getPageName())
                .type(socialPost.getApprovalStatus())
                .pageProfileImage(pageDetail.getPageProfileDisplayImage())
                .locationCount(socialPostScheduleInfo.getPageIds().size())
                .approvalStatus(socialPost.getApprovalStatus())
                .authorName(Objects.nonNull(businessCoreUser) ? businessCoreUser.getName() : null)
                .quotedPostId(socialPost.getQuotedPostId())
                .isQuoted(Objects.nonNull(socialPost.getQuotedPostId()))
                .linkPreviewUrl(socialPost.getLinkPreviewUrl())
                .channel(SocialChannel.getSocialChannelNameById(socialPostScheduleInfo.getSourceId()))
                .createdBy(socialPost.getCreatedBy())
                .gmbOfferDetails((StringUtils.isNotEmpty(socialPost.getPostMetadata())
                        && SocialChannel.GMB.getId() == socialPostScheduleInfo.getSourceId()) ?
                        ConversionUtils.convertToGMBOfferResponse(JSONUtils.fromJSON(socialPost.getPostMetadata(),SocialPostSchedulerMetadata.class),
                                simpleDateFormat,timezone) : null)
                .approvalWorkFlowId(String.valueOf(socialPost.getApprovalWorkflowId()))
                .tags(CollectionUtils.isEmpty(tags)?null:tags)
                .tagNameList(CollectionUtils.isEmpty(tags)?null:tags.stream().map(SocialTagBasicDetail::getName).collect(Collectors.toList()))
                .approvalUserIds(Objects.isNull(socialPost.getApprovalUserIds()) ? new ArrayList<>()
                        : Arrays.stream(socialPost.getApprovalUserIds().split(",")).collect(Collectors.toList()))
                .postMetaData(socialPost.getPostMetadata())
                .isEditedPost(Objects.nonNull(socialPost.getParentPostId()))
                .aiPost((Objects.isNull(socialPost.getAiPost()) || socialPost.getAiPost() == 0)?false:true)
                .timeZone(timezone)
                .build();

        if(Objects.nonNull(approvalMetadata)){
            approvalWorkFlowData.setApprovalUUId(approvalMetadata.getApprovalUUId());
            approvalWorkFlowData.setConversationId(socialPost.getConversationId());
            approvalWorkFlowData.setRejectedBy(approvalMetadata.getRejectedBy());
            approvalWorkFlowData.setRejectedReason(approvalMetadata.getRejectedReason());
            approvalWorkFlowData.setReferenceStepId(approvalMetadata.getReferenceStepId());
            approvalWorkFlowData.setApprovalRequestId(approvalMetadata.getApprovalRequestId());
            approvalWorkFlowData.setIsSameContent(Boolean.parseBoolean(approvalMetadata.getIsSameContent()));
        }
        if(Objects.nonNull(approvalWorkFlowData.getGmbOfferDetails())){
            approvalWorkFlowData.setTopicType(GOOGLE_OFFER);
        }
        Map<Integer,List<Integer>> mapSocialPostIdWithImagesAndVideo = new LinkedHashMap<>();
        Map<Integer,ApprovalWorkFlowData> mapSocialPostIdWithApprovalData = new HashMap<>();
        mapSocialPostIdWithApprovalData.put(approvalWorkFlowData.getId(),approvalWorkFlowData);
        if(StringUtils.isNotEmpty(socialPost.getVideoIds()) || StringUtils.isNotEmpty(socialPost.getImageIds())) {
            prepareMapForImagesAndVideos(socialPost.getVideoIds(), socialPost.getImageIds(), socialPost.getId(), mapSocialPostIdWithImagesAndVideo);
        }
        if(CollectionUtils.isNotEmpty(mapSocialPostIdWithImagesAndVideo.keySet())) {
            setImagesAndVideo(mapSocialPostIdWithImagesAndVideo, mapSocialPostIdWithApprovalData, businessNumber);
        }
        return approvalWorkFlowData;
    }

    @Override
    public List<String> getPagesForBusinessIds(List<Integer> businessIds,List<Integer> socialChannels) {
        logger.info("Business ids size :{}",businessIds.size());
        List<String> pageIds = new ArrayList<>();
        CompletableFuture<List<String>> fbPageIds =  CompletableFuture.supplyAsync(() -> {
            if(socialChannels.contains(SocialChannel.FACEBOOK.getId()))
                return facebookSocialAccountService.findByBusinessIdIn(businessIds);
            else
                return new ArrayList<>();
        });
        CompletableFuture<List<String>> instagramPageIds = CompletableFuture.supplyAsync(() -> {
            if(socialChannels.contains(SocialChannel.INSTAGRAM.getId()))
                return instagramSocialService.findByBusinessIdIn(businessIds);
            else
                return new ArrayList<>();
        });
        CompletableFuture<List<String>> linkedInPageIds = CompletableFuture.supplyAsync(() -> {
            if(socialChannels.contains(SocialChannel.LINKEDIN.getId()))
                return linkedinService.findByBusinessIdIn(businessIds);
            else
                return new ArrayList<>();
        });
        CompletableFuture<List<String>> youtubeChannels = CompletableFuture.supplyAsync(() -> {
            if(socialChannels.contains(SocialChannel.YOUTUBE.getId()))
                return youTubeAccountService.findByBusinessIdIn(businessIds);
            else
                return new ArrayList<>();
        });
        CompletableFuture<List<String>> twitterPages = CompletableFuture.supplyAsync(() -> {
            if(socialChannels.contains(SocialChannel.TWITTER.getId())) {
                List<Long> twitterPageIds =  twitterSocialAccountService.findByBusinessIdIn(businessIds);
                return CollectionUtils.isNotEmpty(twitterPageIds) ?
                        twitterPageIds.parallelStream().map(String::valueOf).collect(Collectors.toList()) : new ArrayList<>();
            } else
                return new ArrayList<>();
        });
        CompletableFuture<List<String>> gmbPages = CompletableFuture.supplyAsync(() -> {
            if(socialChannels.contains(SocialChannel.GMB.getId()))
                return googleSocialAccountService.findByBusinessIdIn(businessIds);
            else
                return new ArrayList<>();
        });
        CompletableFuture<List<String>> applePages = CompletableFuture.supplyAsync(() -> {
            if(socialChannels.contains(SocialChannel.APPLE_CONNECT.getId()))
                return appleAccountService.findByBusinessIdIn(businessIds);
            else
                return new ArrayList<>();
        });
        CompletableFuture<List<String>> tiktokPages = CompletableFuture.supplyAsync(() -> {
            if(socialChannels.contains(SocialChannel.TIKTOK.getId()))
                return tiktokAccountService.findByBusinessIdIn(businessIds);
            else
                return new ArrayList<>();
        });
        try {
            CompletableFuture<Void> getPagesIds = CompletableFuture.allOf(fbPageIds,
                    instagramPageIds, linkedInPageIds, youtubeChannels, twitterPages, gmbPages,applePages, tiktokPages);
            getPagesIds.get(400, TimeUnit.SECONDS);
            logger.info("Business ids size :{}",businessIds.size());
            if(CollectionUtils.isNotEmpty(fbPageIds.get())){
                pageIds.addAll(fbPageIds.get());
            }
            if(CollectionUtils.isNotEmpty(instagramPageIds.get())){
                pageIds.addAll(instagramPageIds.get());
            }
            if(CollectionUtils.isNotEmpty(linkedInPageIds.get())){
                pageIds.addAll(linkedInPageIds.get());
            }
            if(CollectionUtils.isNotEmpty(youtubeChannels.get())){
                pageIds.addAll(youtubeChannels.get());
            }
            if(CollectionUtils.isNotEmpty(twitterPages.get())){
                pageIds.addAll(twitterPages.get());
            }if(CollectionUtils.isNotEmpty(applePages.get())){
                pageIds.addAll(applePages.get());
            }
            if(CollectionUtils.isNotEmpty(gmbPages.get())){
                pageIds.addAll(gmbPages.get());
            }
            if(CollectionUtils.isNotEmpty(applePages.get())){
                pageIds.addAll(applePages.get());
            }
            if(CollectionUtils.isNotEmpty(tiktokPages.get())) {
                pageIds.addAll(tiktokPages.get());
            }
        }catch (Exception e){
            logger.info("Exception occurred while fetching pages for business ids : {}",businessIds);
        }
        return pageIds;
    }

    @Override
    public ApprovalMetadata updateApprovalMetaData(String approvalMetadata, ApprovalWorkflowEvent approvalWorkflowEvent) {
        logger.info("Inside updateApprovalMetaData approvalWorkflowEvent: {}",approvalWorkflowEvent);
        ApprovalMetadata approval = null;
        if(Objects.nonNull(approvalMetadata)) {
            approval = JSONUtils.fromJSON(approvalMetadata, ApprovalMetadata.class);
        }
        if(Objects.isNull(approval)){
            approval = ApprovalMetadata.builder()
                    .approvalRequestId(approvalWorkflowEvent.getApprovalRequestId())
                    .approvalUUId(approvalWorkflowEvent.getApprovalUUId())
                    .referenceStepId(approvalWorkflowEvent.getReferenceStepId())
                    .build();
        }else{
            approval.setApprovalRequestId(approvalWorkflowEvent.getApprovalRequestId());
            approval.setApprovalUUId(approvalWorkflowEvent.getApprovalUUId());
            approval.setReferenceStepId(approvalWorkflowEvent.getReferenceStepId());
        }
        logger.info("returning ApprovalMetadata : {}",approval);
        return approval;
    }

    @Override
    public ApprovalActivity prepareApprovalRejectedRequest(ApprovalWorkflowEvent approvalWorkflowEvent, Long updatedAt) {
        return ApprovalActivity.builder()
                .postId(approvalWorkflowEvent.getEntityId())
                .updatedAt(updatedAt)
                .userId(approvalWorkflowEvent.getUpdatedBy())
                .approvalWorkflowId(approvalWorkflowEvent.getApprovalWorkflowId())
                .activity(ApprovalStatus.REJECTED.getName())
                .reason(approvalWorkflowEvent.getRemarks())
                .build();
    }

    @Override
    public SocialBusinessStatusResponse getAndConvertToBusinessIdResponse(List<Integer> socialChannels,
                                                                          List<Integer> businessIds) {
        SocialBusinessStatusResponse socialBusinessStatusResponse = new SocialBusinessStatusResponse();
        CompletableFuture<List<SocialBusinessPageInfo>> fbBusinessDetails =  CompletableFuture.supplyAsync(() -> {
            if(socialChannels.contains(SocialChannel.FACEBOOK.getId()))
                return facebookSocialAccountService.findByBusinessIds(businessIds);
            return new ArrayList<>();
        });
        CompletableFuture<List<SocialBusinessPageInfo>> igBusinessDetails = CompletableFuture.supplyAsync(() -> {
            if(socialChannels.contains(SocialChannel.INSTAGRAM.getId()))
                return instagramSocialService.findByBusinessIds(businessIds);
            return new ArrayList<>();
        });
        CompletableFuture<List<SocialBusinessPageInfo>> lnBusinessDetails = CompletableFuture.supplyAsync(() -> {
            if(socialChannels.contains(SocialChannel.LINKEDIN.getId()))
                return linkedinService.findByBusinessIds(businessIds);
            return new ArrayList<>();
        });
        CompletableFuture<List<SocialBusinessPageInfo>> twitterBusinessDetails = CompletableFuture.supplyAsync(() -> {
            if(socialChannels.contains(SocialChannel.TWITTER.getId()))
                return twitterSocialAccountService.findByBusinessIds(businessIds);
            return new ArrayList<>();
        });
        CompletableFuture<List<SocialBusinessPageInfo>> gmbBusinessDetails = CompletableFuture.supplyAsync(() -> {
            if(socialChannels.contains(SocialChannel.GMB.getId()))
                return googleSocialAccountService.findByBusinessIds(businessIds);
            return new ArrayList<>();
        });
        try {
            CompletableFuture<Void> getChannelBusinessIdDetails =
                    CompletableFuture.allOf(fbBusinessDetails, igBusinessDetails, lnBusinessDetails,
                            twitterBusinessDetails, gmbBusinessDetails);
            getChannelBusinessIdDetails.get(100, TimeUnit.SECONDS);
            socialBusinessStatusResponse = convertToResponseForBusinessStatus(fbBusinessDetails.get(),igBusinessDetails.get(),
                            lnBusinessDetails.get(),twitterBusinessDetails.get(),gmbBusinessDetails.get(),businessIds);

        }catch (Exception e){
            logger.info("Exception occurred while fetching pages for business ids : {} and exception ",businessIds,e);
        }
        return socialBusinessStatusResponse;
    }

    private String getUserEmail(Integer userId) {
        if(Objects.isNull(userId)) return null;
        BusinessCoreUser user = businessCoreService.getUserInfo(userId);
        if(Objects.nonNull(user)) {
            return user.getEmailId();
        }
        return null;
    }
}
