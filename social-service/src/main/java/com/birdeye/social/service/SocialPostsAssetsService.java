package com.birdeye.social.service;
/**
 * <AUTHOR>
 *
 */
import com.birdeye.social.entities.PostLib;
import com.birdeye.social.entities.PostLibMaster;
import com.birdeye.social.entities.SocialPostsAssets;

import java.util.List;
import java.util.Map;

public interface SocialPostsAssetsService {

    Map<Integer, SocialPostsAssets> getPostAssetsForPostLib(PostLibMaster postLibMaster, List<PostLib> postLibList);
}
