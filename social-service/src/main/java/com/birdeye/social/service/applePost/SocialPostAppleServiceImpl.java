package com.birdeye.social.service.applePost;

import com.birdeye.social.apple.AppleErrorResponse;
import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.*;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.SamayScheduleEventRequest;
import com.birdeye.social.entities.*;
import com.birdeye.social.model.*;
import com.birdeye.social.model.apple.AppleLiveCheckRequest;
import com.birdeye.social.model.approval_workflow.ApprovalEmailDTO;
import com.birdeye.social.model.approval_workflow.ApprovalEnum;
import com.birdeye.social.nexus.EmailDTO;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.service.*;
import com.birdeye.social.sro.applePost.*;
import com.birdeye.social.utils.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class SocialPostAppleServiceImpl implements SocialPostAppleService {

    private static final Logger LOG = LoggerFactory.getLogger(SocialPostAppleServiceImpl.class);

    @Autowired
    private AppleConnectService appleConnectService;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private SocialPostsAssetsRepository socialPostsAssetsRepository;

    @Autowired
    private SocialPostInfoRepository socialPostInfoRepository;

    @Autowired
    private BusinessAppleLocationRepo businessAppleLocationRepo;

    @Autowired
    private SocialPostAuditRepository socialPostAuditRepository;

    @Autowired
    private SocialPostRepository socialPostRepository;
    @Autowired
    private NexusService nexusService;

    @Autowired
    private ApplePublishInfoMetadataRepository applePublishInfoMetadataRepository;

    @Autowired
    private KafkaExternalService kafkaExternalService;

    @Autowired
    private IPermissionMappingService permissionMappingService;

    @Autowired
    private AppleCtaCategoryRepo appleCtaCategoryRepo;
    @Autowired
    private SamayService samayService;
    @Autowired
    private SocialRetryPostRepo socialRetryPostRepo;
    @Autowired
    private SocialPostActivityService socialPostActivityService;
    @Autowired
    private SocialPostScheduleInfoRepo socialPostScheduleInfoRepository;
    @Autowired
    private ISocialPostsAssetService socialPostsAssetService;

    @Autowired
    private CommonService commonService;

    private static final String EN_US = "en-US";

    private static final String LOCATION = "LOCATION";
    private static final String EMAIL_SUBJECT_PATTERN_DATE = "MMM dd";
    private static final String APPLE_FEED_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    private static final String APPLE_SHOWCASE_LIVE_CHECK = "apple_showcase_live_check";



    @Override
    public void postOnApple(SocialPostPublishInfo publishInfo) {
        try {
            ApplePublishInfoMetadata applePublishInfoMetadata = applePublishInfoMetadataRepository.findByPublishInfoId(publishInfo.getId());
            if(Objects.isNull(applePublishInfoMetadata)) {
                LOG.info("empty apple publish info for publish info {}", publishInfo);
                publishInfo.setIsPublished(SocialPostStatusEnum.FAILED_ACTION.getId());
                publishInfo.setFailureReason(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleInfoMetaDataNullMessage());
                socialPostInfoRepository.save(publishInfo);
                return;
            }
            String pubStatus = applePublishInfoMetadata.getPublishStatus();
            BusinessAppleLocation appleLocation =
                    businessAppleLocationRepo.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());

            if(Objects.isNull(appleLocation)) {
                LOG.info("No business mapping found the apple with details {}", publishInfo);
                return;
            }
            Map postMetadata =  JSONUtils.fromJSON(publishInfo.getSocialPost().getPostMetadata(), Map.class);
            String postMetaData = (String) postMetadata.get("appleMetaData");
            ApplePostMetadata metadata = JSONUtils.fromJSON(postMetaData, ApplePostMetadata.class);
            if(ApplePublishStateEnum.IN_PROCESS.getName().equals(pubStatus)) {
                if (uploadImageInApple(publishInfo, appleLocation.getAppleCompanyId(), applePublishInfoMetadata,appleLocation,metadata)) {
                    applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.IMAGE_REJECTED.getName());
                }
            } else if(ApplePublishStateEnum.IMAGE_CREATED.getName().equals(pubStatus) || ApplePublishStateEnum.IMAGE_APPROVED.getName().equals(pubStatus)) {
                if (createShowcaseCreativeInApple(publishInfo, metadata, appleLocation, applePublishInfoMetadata)) {
                    applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.SHOWCASE_CREATIVE_REJECTED.getName());
                }
            } else if(ApplePublishStateEnum.SHOWCASE_CREATIVE_APPROVED.getName().equals(pubStatus)
                    && (StringUtils.isEmpty(publishInfo.getSocialPost().getApprovalStatus())
                    || ApprovalStatus.APPROVED.getName().equalsIgnoreCase(publishInfo.getSocialPost().getApprovalStatus())) ) {
                if(createCreativeInApple(publishInfo, metadata, appleLocation.getAppleCompanyId(), appleLocation.getAppleBusinessId(),
                        applePublishInfoMetadata,appleLocation)) {
                    sendAppleStatusEmailEvent(ApplePublishStateEnum.SHOWCASE_CREATED.getName(),metadata,publishInfo.getSocialPost(),
                            appleLocation, null);
                }else {
                    applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.SHOWCASE_REJECTED.getName());
                }
            }

            socialPostRepository.saveAndFlush(publishInfo.getSocialPost());
            applePublishInfoMetadataRepository.saveAndFlush(applePublishInfoMetadata);

        } catch (Exception ex) {
            LOG.info("Something went wrong while publish data to apple post {} with error ", publishInfo, ex);
        }
    }
    @Override
    public void sendAppleStatusEmailEvent(String showCaseStatus, ApplePostMetadata metadata, SocialPost post, BusinessAppleLocation appleLocation, String errorMessage) {
        AppleShowcaseEmail showcaseEmail = new AppleShowcaseEmail(showCaseStatus,metadata,post.getPostText(),appleLocation.getAppleBusinessName(),
                post.getId(), post.getCreatedBy(), appleLocation.getEnterpriseId(),post.getImageIds(),null,null,
                appleLocation.getBusinessId(),errorMessage,appleLocation.getLogoUrl());
        kafkaExternalService.sendAppleStatusEmailEvent(showcaseEmail);
    }

    private boolean uploadImageInApple(SocialPostPublishInfo publishInfo, String appleCompanyId,
                                       ApplePublishInfoMetadata applePublishInfoMetadata,
                                       BusinessAppleLocation appleLocation,ApplePostMetadata metadata) {
        if(Objects.isNull(publishInfo.getSocialPost().getImageIds())) {
            return true;
        }
        AppleImageUploadRequest mediaRequests =
                getMediaRequest(publishInfo.getSocialPost().getImageIds(), publishInfo.getEnterpriseId());
        try {
            AppleImageUploadResponse mediaUploadResponse =
                    appleConnectService.mediaUpload(appleCompanyId, mediaRequests);

            if (Objects.isNull(mediaUploadResponse)) {
                return true;
            }

            applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.IMAGE_CREATED.getName());
            applePublishInfoMetadata.setPhotoId(mediaUploadResponse.getId());

            if (updateShowcaseStatus(publishInfo, mediaUploadResponse.getState(), 0, mediaUploadResponse.getId(), true, "Image uploaded")) {
                LOG.info("Image upload is complete, waiting for image to be approved");
            }
        }catch (HttpStatusCodeException e){
            PermissionMapping permissionMapping = getAppleErrorMessage(e);
            sendAppleStatusEmailEvent(AppleApprovalStatus.REJECTED.getName(),metadata,publishInfo.getSocialPost(),
                    appleLocation, permissionMapping.getErrorMessage());
            getShowcaseStatus(publishInfo, AppleShowcaseStatusEnum.FAILED.name(), 2, null, false, permissionMapping.getErrorMessage());
            updateSocialPostAudit(publishInfo.getSocialPostId(),"Posting failed on apple for image","POSTING_FAILED",
                    publishInfo.getSourceId(), publishInfo.getBusinessId(), publishInfo.getEnterpriseId());
            return true;
        }
        return false;
    }

    private boolean createShowcaseCreativeInApple(SocialPostPublishInfo publishInfo, ApplePostMetadata metadata,
                                                  BusinessAppleLocation businessAppleLocation,
                                                  ApplePublishInfoMetadata applePublishInfoMetadata) {

        String appleCompanyId = businessAppleLocation.getAppleCompanyId();
        String appleBusinessId = businessAppleLocation.getAppleBusinessId();

        AppleShowcaseCreativeRequest showcaseCreativeRequest = getShowcaseCreativeRequest(publishInfo, metadata, businessAppleLocation);
        try {
            AppleShowcaseCreativeResponse creativeResponse = appleConnectService.appleShowcaseCreativeGenerate(appleCompanyId, appleBusinessId, showcaseCreativeRequest);
            if(Objects.isNull(creativeResponse)) {
                return true;
            }

            applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.SHOWCASE_CREATIVE_CREATED.getName());
            applePublishInfoMetadata.setCreativeId(creativeResponse.getId());
            applePublishInfoMetadata.setCreativeEtag(creativeResponse.getEtag());

            if(updateShowcaseStatus(publishInfo, creativeResponse.getState(), 0,   creativeResponse.getId(), false, "Showcase creative uploaded")) {
                LOG.info("Showcase created upload is complete, waiting for image to be approved");
            }
        }catch (HttpStatusCodeException e){
            LOG.info("Apple post failed while creating Apple creative : ",e);
            PermissionMapping permissionMapping = getAppleErrorMessage(e);
            sendAppleStatusEmailEvent(AppleApprovalStatus.REJECTED.getName(),metadata,publishInfo.getSocialPost(),
                    businessAppleLocation, permissionMapping.getErrorMessage());
            getShowcaseStatus(publishInfo, AppleShowcaseStatusEnum.FAILED.name(), 2, null, false, permissionMapping.getErrorMessage());
            updateSocialPostAudit(publishInfo.getSocialPostId(),"Posting failed on apple for showcase_creative","POSTING_FAILED",
                    publishInfo.getSourceId(), publishInfo.getBusinessId(), publishInfo.getEnterpriseId());
            return true;
        }
        return false;
    }

    private boolean createCreativeInApple(SocialPostPublishInfo publishInfo, ApplePostMetadata metadata,
                                          String appleCompanyId, String appleBusinessId,
                                          ApplePublishInfoMetadata applePublishInfoMetadata,BusinessAppleLocation appleLocation) {
        AppleShowcaseDetails showcaseRequest = getShowcaseRequest(publishInfo, metadata,applePublishInfoMetadata);
        try {
            AppleShowcaseResponse showcaseResponse =
                    appleConnectService.appleShowcaseGenerate(appleCompanyId, appleBusinessId, showcaseRequest);

            if (Objects.isNull(showcaseResponse)) {
                return false;
            }

            applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.SHOWCASE_CREATED.getName());
            applePublishInfoMetadata.setShowcaseId(showcaseResponse.getId());
            applePublishInfoMetadata.setShowcaseEtag(showcaseResponse.getEtag());
            if (updateShowcaseStatus(publishInfo, showcaseResponse.getState(), 0, showcaseResponse.getId(), false, "Showcase uploaded")) {
                LOG.info("Showcase created upload is complete, waiting for Showcase to be approved");
            }
        }catch (HttpStatusCodeException e){
            LOG.info("Apple post failed while creating Apple creative : ",e);
            PermissionMapping permissionMapping = getAppleErrorMessage(e);
            sendAppleStatusEmailEvent(AppleApprovalStatus.REJECTED.getName(),metadata,publishInfo.getSocialPost(),
                    appleLocation, permissionMapping.getErrorMessage());
            getShowcaseStatus(publishInfo, AppleShowcaseStatusEnum.FAILED.name(), 2, null, false, permissionMapping.getErrorMessage());
            updateSocialPostAudit(publishInfo.getSocialPostId(),"Posting failed on apple for showcase","POSTING_FAILED",
                    publishInfo.getSourceId(), publishInfo.getBusinessId(), publishInfo.getEnterpriseId());
            return false;
        }
        return true;
    }

    private PermissionMapping getAppleErrorMessage(HttpStatusCodeException e) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            AppleErrorResponse[] errorResponses = mapper.readValue(e.getResponseBodyAsString(), AppleErrorResponse[].class);
            AppleErrorResponse appleErrorResponse = errorResponses[0];
            List<PermissionMapping> permissionMapping =
                    permissionMappingService.getListOfDataByChannelAndErrorCode(SocialChannel.APPLE_CONNECT.getName(),appleErrorResponse.getCode());
            for(PermissionMapping mapping : permissionMapping){
                if(mapping.geterrorActualMessage().contains(appleErrorResponse.getMessage())){
                    LOG.info("Mapping found :{}",mapping);
                    return mapping;
                }
            }
            LOG.info("No message found in permission mapping :{}",appleErrorResponse);
            return permissionMappingService.getDataByChannelAndPermissionCodeAndModule(SocialChannel.APPLE_CONNECT.getName(),
                    Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR,Constants.PUBLISH_MODULE);
        }catch (Exception ex){
            LOG.info("Not able to convert to apple error response ",ex);
            return permissionMappingService.getDataByChannelAndPermissionCodeAndModule(SocialChannel.APPLE_CONNECT.getName(),
                    Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR,Constants.PUBLISH_MODULE);
        }
    }

    @Override
    public boolean checkPostStatus(SocialPostPublishInfo publishInfo) {
        try {
            LOG.info("Inside checkPostStatus for {}",publishInfo.getId());
            ApplePublishInfoMetadata applePublishInfoMetadata =
                    applePublishInfoMetadataRepository.findByPublishInfoId(publishInfo.getId());
            if (Objects.isNull(applePublishInfoMetadata)) {
                LOG.info("empty apple publish info for publish info {}", publishInfo.getId());
                publishInfo.setIsPublished(SocialPostStatusEnum.FAILED_ACTION.getId());
                publishInfo.setFailureReason(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleInfoMetaDataNullMessage());
                socialPostInfoRepository.save(publishInfo);
                return false;
            }
            String pubStatus = applePublishInfoMetadata.getPublishStatus();

            if(ApplePublishStateEnum.LIVE.getName().equals(pubStatus)) {
                LOG.info("post is already live {}", publishInfo.getId());
                publishInfo.setIsPublished(SocialPostStatusEnum.PUBLISHED.getId());
                publishInfo.setFailureReason(null);
                socialPostInfoRepository.save(publishInfo);
                return true;
            }
            BusinessAppleLocation businessAppleLocation =
                    businessAppleLocationRepo.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
            if(Objects.isNull(businessAppleLocation)) {
                LOG.info("No business mapping found the apple with details {}", publishInfo.getId());
                return false;
            }
            String appleCompanyId = businessAppleLocation.getAppleCompanyId();
            String appleBusinessId = businessAppleLocation.getAppleBusinessId();

            String approvalState = publishInfo.getSocialPost().getApprovalStatus();
            boolean internalApprovalStatus = Objects.nonNull(approvalState) && ApprovalEnum.APPROVED.name().equalsIgnoreCase(approvalState);

            // internal approval pending state
            if(!internalApprovalStatus && Objects.nonNull(approvalState)) {
                LOG.info("Internal approval still pending for {} postid, exiting the process",publishInfo.getSocialPostId());
                return false;
            }

            // internal approval rejected state
            if(ApprovalEnum.REJECTED.name().equalsIgnoreCase(approvalState)) {
                LOG.info("Post is internally rejected, existing the process");
                publishInfo.setIsPublished(3);
                applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.INTERNAL_REJECTED.getName());
                return false;
            }


            if(ApplePublishStateEnum.IMAGE_CREATED.getName().equals(pubStatus) ||
                    ApplePublishStateEnum.IMAGE_APPROVED.getName().equals(pubStatus)) {
                // media upload status
                AppleImageUploadResponse mediaUploadResponse =
                        appleConnectService.getShowcaseImageStatus(appleCompanyId, applePublishInfoMetadata.getPhotoId());

                if(Objects.isNull(mediaUploadResponse)) {
                    return false;
                }else if(StringUtils.equalsIgnoreCase(mediaUploadResponse.getState(),"APPROVED")){
                    applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.IMAGE_APPROVED.getName());
                }else if(StringUtils.equalsIgnoreCase(mediaUploadResponse.getState(),"REJECTED")){
                    sendAppleStatusEmailEvent(ApplePublishStateEnum.IMAGE_REJECTED.name(),null,publishInfo.getSocialPost(),
                            businessAppleLocation, null);
                    applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.IMAGE_REJECTED.getName());
                }else if(StringUtils.equalsIgnoreCase(mediaUploadResponse.getState(),"DELETED")){
                    applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.DELETED.getName());
                    return false;
                }
                applePublishInfoMetadataRepository.saveAndFlush(applePublishInfoMetadata);
                if(getShowcaseStatus(publishInfo, mediaUploadResponse.getState(), 0,  null, true, null)) {
                    LOG.info("Image upload is complete, waiting for image to be approved");
                }
            } else if(ApplePublishStateEnum.SHOWCASE_CREATIVE_CREATED.getName().equals(pubStatus)
                ||ApplePublishStateEnum.SHOWCASE_CREATIVE_APPROVED.getName().equals(pubStatus)) {
                String errorMessage = null;

                Map postMetadata =  JSONUtils.fromJSON(publishInfo.getSocialPost().getPostMetadata(), Map.class);
                String postMetaData = (String) postMetadata.get("appleMetaData");
                ApplePostMetadata metadata = JSONUtils.fromJSON(postMetaData, ApplePostMetadata.class);
                Date postLiveDate = DateTimeUtils.convertStringDateToDate(metadata.getStartDate());

                // showcase creative status
                AppleShowcaseCreativeResponse creativeResponse = appleConnectService.getShowcaseCreativeStatus(appleCompanyId, appleBusinessId, applePublishInfoMetadata.getCreativeId());

                if(Objects.isNull(creativeResponse)) {
                    return false;
                }else if(StringUtils.equalsIgnoreCase(creativeResponse.getState(),"APPROVED")){
                    applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.SHOWCASE_CREATIVE_APPROVED.getName());
                }else if(StringUtils.equalsIgnoreCase(creativeResponse.getState(),"REJECTED")){
                    applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.SHOWCASE_CREATIVE_REJECTED.getName());
                    errorMessage = rejectionStatusHandler(appleCompanyId, "SHOWCASE_CREATIVE", applePublishInfoMetadata.getCreativeId());
                    sendAppleStatusEmailEvent(ApplePublishStateEnum.SHOWCASE_CREATIVE_REJECTED.name(),null,publishInfo.getSocialPost(),
                            businessAppleLocation,errorMessage);
                }else if(StringUtils.equalsIgnoreCase(creativeResponse.getState(),"DELETED")){
                    applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.DELETED.getName());
                    return false;
                } else if(StringUtils.equalsIgnoreCase(creativeResponse.getState(),"IN_REVIEW") && postLiveDate.before(new Date())) {
                    applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.SHOWCASE_CREATIVE_REJECTED.getName());
                    errorMessage = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleShowcaseStatusInReviewRejectedMessage();
                    sendAppleStatusEmailEvent(ApplePublishStateEnum.SHOWCASE_CREATIVE_REJECTED.name(),null,publishInfo.getSocialPost(),
                            businessAppleLocation,errorMessage);
                }
                applePublishInfoMetadataRepository.saveAndFlush(applePublishInfoMetadata);
                if(getShowcaseStatus(publishInfo, creativeResponse.getState(), 0, null, true, errorMessage)) {
                    LOG.info("Showcase created upload is complete, waiting for image to be approved");
                }
            } else if(ApplePublishStateEnum.SHOWCASE_CREATED.getName().equals(pubStatus)) {
                String errorMessage = null;

                Map postMetadata =  JSONUtils.fromJSON(publishInfo.getSocialPost().getPostMetadata(), Map.class);

                String postMetaData = (String) postMetadata.get("appleMetaData");

                ApplePostMetadata metadata = JSONUtils.fromJSON(postMetaData, ApplePostMetadata.class);

                Date postLiveDate = DateTimeUtils.convertStringDateToDate(metadata.getStartDate());
                Date currDate = new Date();
                // showcase status
                AppleShowcaseResponse showcaseResponse =
                        appleConnectService.getShowcaseStatus(appleCompanyId, appleBusinessId, applePublishInfoMetadata.getShowcaseId());
                if(Objects.isNull(showcaseResponse)) {
                    return false;
                }
                else if(StringUtils.equalsIgnoreCase(showcaseResponse.getState(),"APPROVED") ||
                        StringUtils.equalsIgnoreCase(showcaseResponse.getState(),"PUBLISHED")){
                    sendAppleStatusEmailEvent(ApplePublishStateEnum.SHOWCASE_APPROVED.name(),null,publishInfo.getSocialPost(),
                            businessAppleLocation, errorMessage);
                    applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.SHOWCASE_APPROVED.getName());
                    liveCheckRequestToSamay(publishInfo.getId(),postLiveDate,publishInfo.getSocialPost().getId(),
                            Long.valueOf(businessAppleLocation.getBusinessId()));
                    publishInfo.setAppleNextSyncDate(DateTimeUtils.addTimeInMinutes(postLiveDate,5));
                    getShowcaseStatus(publishInfo, showcaseResponse.getState(), 0, showcaseResponse.getId(), false, errorMessage);
                }else if(StringUtils.equalsIgnoreCase(showcaseResponse.getState(),"REJECTED")){
                    applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.SHOWCASE_REJECTED.getName());
                    errorMessage = rejectionStatusHandler(appleCompanyId, "SHOWCASE", applePublishInfoMetadata.getCreativeId());
                    sendAppleStatusEmailEvent(ApplePublishStateEnum.SHOWCASE_REJECTED.name(),null,publishInfo.getSocialPost(),
                            businessAppleLocation, errorMessage);
                    getShowcaseStatus(publishInfo, showcaseResponse.getState(), 2, showcaseResponse.getId(), false, errorMessage);
                }else if(StringUtils.equalsIgnoreCase(showcaseResponse.getState(),"DELETED")||
                        StringUtils.equalsIgnoreCase(showcaseResponse.getState(),"DEACTIVATED")){
                    applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.DELETED.getName());
                    getShowcaseStatus(publishInfo, ApplePublishStateEnum.DELETED.getName(), 3, null, false, null);
                }else if(currDate.after(postLiveDate) || currDate.after(getMaxWaitDate(showcaseResponse))) {
                    LOG.info("Post live date has passed, marking the post as expired");
                    applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.EXPIRED.getName());
                    applePublishInfoMetadataRepository.saveAndFlush(applePublishInfoMetadata);
                    deactivateShowcase(publishInfo);
                    getShowcaseStatus(publishInfo, ApplePublishStateEnum.EXPIRED.getName(), 2, null, false, null);
                }
                applePublishInfoMetadataRepository.saveAndFlush(applePublishInfoMetadata);
            }
        } catch (Exception ex) {
            LOG.info("Something went wrong while checking data to apple post {} with error ", publishInfo, ex);
        }
        return true;
    }

    @NotNull
    private Date getMaxWaitDate(AppleShowcaseResponse showcaseResponse) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(APPLE_FEED_DATE_FORMAT);
        Date updatedDate =simpleDateFormat.parse(showcaseResponse.getUpdatedDate());
        int waitDays = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleShowcaseWaitDays();
        return DateTimeUtils.addTimeInMinutes(updatedDate,waitDays*24*60);
    }

    private void liveCheckRequestToSamay(Integer id, Date postLiveDate, Integer postId, Long businessId) {
        AppleLiveCheckRequest request = new AppleLiveCheckRequest();
        request.setPublishInfoId(id);
        long scheduleDateToEpoch = postLiveDate.toInstant().toEpochMilli();
        SamayScheduleEventRequest samayRequest = SamayUtils.getSamayScheduleEventRequest(JSONUtils.toJSON(request),
                Long.valueOf(getSamaySchedulingInfoId(postId,request)),businessId, 0, 0, SamayRetryTypeEnum.SIMPLE.name(),
                scheduleDateToEpoch, SamayEventTypeEnum.MESSENGER.getType(),
                APPLE_SHOWCASE_LIVE_CHECK, false);
        samayService.pushMessageToSamayScheduler(samayRequest);
    }

    @Override
    public void deletePostFromApple(Integer socialPostId) {
        try {
            List<SocialPostPublishInfo> publishInfoList = socialPostInfoRepository.findBySocialPostIdAndIsPublishedAndSourceId(socialPostId, 0, SocialChannel.APPLE_CONNECT.getId());
            if(CollectionUtils.isEmpty(publishInfoList)) {
                LOG.info("No apple running apple post found for social post id {} , exiting the process", socialPostId);
            }
            for(SocialPostPublishInfo publishInfo: publishInfoList) {
                deletePostDataFromApple(publishInfo);
                socialPostInfoRepository.delete(publishInfo);
            }
        } catch (Exception ex) {
            LOG.info("Something went wrong while delete post from apple source ", ex);
        }
    }
    @Override
    public void deletePostDataFromApple(SocialPostPublishInfo publishInfo) {
        ApplePublishInfoMetadata applePublishInfoMetadata =
                applePublishInfoMetadataRepository.findByPublishInfoId(publishInfo.getId());
        String pubStatus = applePublishInfoMetadata.getPublishStatus();
        if(ApplePublishStateEnum.IMAGE_CREATED.getName().equals(pubStatus) ||
                ApplePublishStateEnum.IMAGE_REJECTED.getName().equals(pubStatus)||
                ApplePublishStateEnum.IMAGE_APPROVED.getName().equals(pubStatus)||
                ApplePublishStateEnum.SHOWCASE_CREATIVE_CREATED.getName().equals(pubStatus)||
                ApplePublishStateEnum.SHOWCASE_CREATIVE_REJECTED.getName().equals(pubStatus)||
                ApplePublishStateEnum.SHOWCASE_CREATIVE_APPROVED.getName().equals(pubStatus)) {
            applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.DELETED.getName());
            LOG.info("Image upload found. marking post as deleted");
        }
        BusinessAppleLocation businessAppleLocation =
                businessAppleLocationRepo.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());

        if(Objects.nonNull(businessAppleLocation)) {
            String appleCompanyId = businessAppleLocation.getAppleCompanyId();
            String appleBusinessId = businessAppleLocation.getAppleBusinessId();
            Boolean success = false;
            if (ApplePublishStateEnum.SHOWCASE_CREATED.getName().equals(pubStatus) ||
                    ApplePublishStateEnum.SHOWCASE_APPROVED.getName().equals(pubStatus)) {
                success = appleConnectService.deleteShowcase(appleCompanyId, appleBusinessId,
                        applePublishInfoMetadata.getShowcaseId(), applePublishInfoMetadata.getShowcaseEtag());
                removeSamayReminderForLiveCheck(publishInfo.getSocialPostId(), publishInfo.getId());
            } else if (ApplePublishStateEnum.LIVE.getName().equals(pubStatus)) {
                success = appleConnectService.deactivateShowcase(appleCompanyId, appleBusinessId,
                        applePublishInfoMetadata.getShowcaseId(), applePublishInfoMetadata.getShowcaseEtag());
            }

            if (success) {
                LOG.info("Showcase delete successfully");
                applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.DELETED.getName());
            } else {
                LOG.info("Could not delete showcase");
            }
        }else
            LOG.info("No business mapping found the apple with details {}", publishInfo);
        applePublishInfoMetadataRepository.saveAndFlush(applePublishInfoMetadata);
    }

    @Override
    public void updateSocialPostInfos(Integer id) {
        List<SocialPostPublishInfo> publishInfoList = socialPostInfoRepository.findBySocialPostId(id);
        publishInfoList.forEach(publishInfo -> {
            publishInfo.setIsPublished(ApplePublishStateEnum.INTERNAL_REJECTED.getId());
        });
        socialPostInfoRepository.save(publishInfoList);
    }

    private void updateSocialPostAudit(Integer postId, String status, String action, Integer sourceId, Integer businessId, Integer enterpriseId) {
        try {
            SocialPostAudit socialPostAudit = socialPostAuditRepository.findFirstByPostId(postId);

            if(Objects.nonNull(socialPostAudit)) {
                socialPostAudit.setAction(action);
                socialPostAudit.setSourceId(sourceId);
                socialPostAudit.setBusinessId(businessId);
                socialPostAudit.setEnterpriseId(enterpriseId); // need to check that
                socialPostAudit.setStatus(socialPostAudit.getStatus().concat(" | ").concat(status));
                socialPostAuditRepository.save(socialPostAudit);
            }
        } catch (Exception ex) {
            LOG.info("Something went wrong while updating the audit for social post Id {}", postId, ex);
        }

    }

    @Override
    public boolean updateShowcaseStatus(SocialPostPublishInfo publishInfo, String status,  Integer isPublished, String id, Boolean nextStep, String summary) {

        boolean processStatus = false;
        if(AppleShowcaseStatusEnum.APPROVED.name().equalsIgnoreCase(status)) {
            publishInfo.setIsPublished(isPublished);
            publishInfo.setPostId(id);

            processStatus = true;
            if(nextStep) {
                postOnApple(publishInfo);
            }
        } else if(AppleShowcaseStatusEnum.REJECTED.name().equalsIgnoreCase(status)) {
            publishInfo.setIsPublished(2);
            publishInfo.setPostId(null);
            publishInfo.setFailureReason("Post is rejected"); // add it with permission mapping flow

//            processStatus = true;
        }  else if(AppleShowcaseStatusEnum.IN_REVIEW.name().equalsIgnoreCase(status)) {
            publishInfo.setIsPublished(0);

            processStatus = true;
        } else if(AppleShowcaseStatusEnum.SUBMITTED.name().equalsIgnoreCase(status)) {
            publishInfo.setIsPublished(0);
            publishInfo.setPostId(id);

            processStatus = true;
            if(nextStep) {
                postOnApple(publishInfo);
            }
        }

        socialPostInfoRepository.saveAndFlush(publishInfo);

        updateSocialPostAudit(publishInfo.getSocialPostId(), summary, status,
                publishInfo.getSourceId(), publishInfo.getBusinessId(), publishInfo.getEnterpriseId());


        return !processStatus;
    }

    private String rejectionStatusHandler(String companyId, String resourceType, String id) {
        String rejectionReason = "";
        AppleFeedbackResponse response = appleConnectService.getAppleFeedBack(companyId, resourceType, id);

        if(Objects.isNull(response) || CollectionUtils.isEmpty(response.getData())) {
            LOG.info("No feedback details found for the resource {} with id {}", resourceType, id);
            return rejectionReason;
        }

      List<AppleFeedbackResponse.AppleFeedbackResponseDatum> validationFailure =
              response.getData().stream().filter(v -> v.getType().equalsIgnoreCase("VALIDATION_FAILURE"))
                      .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(validationFailure)) {
            LOG.info("No feedback details found for the resource {} with id {}", resourceType, id);
            return rejectionReason;
        }

        for(AppleFeedbackResponse.AppleFeedbackResponseDatum v : validationFailure) {
            rejectionReason += v.getValidationReports().stream().map(AppleFeedbackResponse.AppleFeedbackResponseDatum.ValidationReport::getMessage)
                    .collect(Collectors.joining(" | "));
        }

        return rejectionReason;
    }

    private PermissionMapping errorHandlerService(String channel, Integer failureCode, Integer errorSubCode, String message) {


        List<PermissionMapping> permissionMappings =permissionMappingService.getDataByChannelAndParentErrorCodeAndPermissionCode(channel,
                failureCode, errorSubCode);

        if(CollectionUtils.isEmpty(permissionMappings)) {
            return permissionMappingService.getDataByChannelAndPermissionCodeAndModule(channel,
                    Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR,Constants.PUBLISH_MODULE);
        }
        if(permissionMappings.size() == 1) return permissionMappings.get(0);

        for(PermissionMapping pm: permissionMappings) {
            if(com.birdeye.social.utils.StringUtils.isNotEmpty(message) && message.contains(pm.geterrorActualMessage())){
                return pm;
            }
        }
        return null;

    }

    private boolean getShowcaseStatus(SocialPostPublishInfo publishInfo, String status,  Integer isPublished, String id, Boolean nextStep, String failureReason) {

        boolean processStatus = false;
        if(AppleShowcaseStatusEnum.APPROVED.name().equalsIgnoreCase(status)) {
            publishInfo.setIsPublished(isPublished);
            publishInfo.setPostId(id);

            processStatus = true;

            // publish next step on apple post
            if(nextStep) {
                postOnApple(publishInfo);
            }

        } else if(AppleShowcaseStatusEnum.REJECTED.name().equalsIgnoreCase(status) || AppleShowcaseStatusEnum.FAILED.name().equalsIgnoreCase(status)
                || (AppleShowcaseStatusEnum.IN_REVIEW.name().equalsIgnoreCase(status) && Objects.nonNull(publishInfo.getPublishDate())
                && publishInfo.getPublishDate().before(new Date()))) {
            publishInfo.setIsPublished(2);
            publishInfo.setPostId(null);
            publishInfo.setFailureReason(failureReason); // add it with permission mapping flow. Add get feedback API integration as well

            processStatus = true;
        }  else if(AppleShowcaseStatusEnum.IN_REVIEW.name().equalsIgnoreCase(status)) {
            processStatus = true;
        } else if(AppleShowcaseStatusEnum.SUBMITTED.name().equalsIgnoreCase(status)) {
            processStatus = true;
        }

        socialPostInfoRepository.saveAndFlush(publishInfo);


        return !processStatus;
    }

    private AppleShowcaseDetails getShowcaseRequest(SocialPostPublishInfo publishInfo, ApplePostMetadata metadata, ApplePublishInfoMetadata applePublishInfoMetadata) {
        AppleShowcaseDetails showcaseDetails = new AppleShowcaseDetails();
        showcaseDetails.setShowcaseDetails(getAppleShowcaseRequest(publishInfo, metadata, applePublishInfoMetadata));
        return showcaseDetails;
    }

    @NotNull
    private AppleShowcaseRequest getAppleShowcaseRequest(SocialPostPublishInfo publishInfo, ApplePostMetadata metadata, ApplePublishInfoMetadata applePublishInfoMetadata) {
        AppleShowcaseRequest request = new AppleShowcaseRequest();
        request.setCreativeId(applePublishInfoMetadata.getCreativeId());
        try {
            request.setStartDate(TimeZoneUtil.getDateTimeWithTimezoneOffset(getDateStringForTimezone(metadata.getStartDate(),
                            metadata.getTimeZone(),false),metadata.getTimezoneOffSetValue()));
            request.setEndDate(TimeZoneUtil.getDateTimeWithTimezoneOffset(getDateStringForTimezone(metadata.getEndDate(),
                            metadata.getTimeZone(),true),metadata.getTimezoneOffSetValue()));
        }catch (ParseException e){
            request.setStartDate(metadata.getStartDate());
            request.setEndDate(metadata.getEndDate());
        }
        ShowcaseResourceDetails showcaseResourceDetails = new ShowcaseResourceDetails();
        showcaseResourceDetails.setResourceType(LOCATION);
        showcaseResourceDetails.setResourceId(publishInfo.getExternalPageId());

        request.setResourceDetails(showcaseResourceDetails);
        return request;
    }

    private AppleImageUploadRequest getMediaRequest(String imageIds, Integer businessId) {
        AppleImageUploadRequest request = new AppleImageUploadRequest();

        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(businessId, false);

        String[] imageIdsConversion = imageIds.split(",");
        List<Integer> ids = new ArrayList<>();
        for (String imageId : imageIdsConversion) {
            ids.add(Integer.parseInt(imageId));
        }
        List<String> imageUrls = socialPostsAssetsRepository.findImageUrlsByIds(ids);

        for (String imageUrl : imageUrls) {
            AppleUploadImageDetails requestDatum = new AppleUploadImageDetails();
            requestDatum.setUrl(socialPostsAssetService.getCompleteCdnUrlFromBaseUrl(imageUrl, String.valueOf(businessLiteDTO.getBusinessNumber())));
            request.setImageDetails(requestDatum);
        }

        return request;
    }


    private AppleShowcaseCreativeRequest getShowcaseCreativeRequest(SocialPostPublishInfo publishInfo, ApplePostMetadata metadata, BusinessAppleLocation businessAppleLocation) {
        AppleShowcaseCreativeRequest request = new AppleShowcaseCreativeRequest();
        request.setShowcaseCreativeDetails(getShowcaseCreativeDetailsObject(publishInfo,metadata, businessAppleLocation));
        return request;
    }

    @Override
    public void updateShowcase(SocialPostPublishInfo publishInfo, ApplePostMetadata metadata, ApplePublishInfoMetadata publishInfoMetadata) {
        BusinessAppleLocation appleLocation =
                businessAppleLocationRepo.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
        AppleShowcaseUpdateRequest updateRequest = getShowcaseUpdateRequest(publishInfo, metadata,publishInfoMetadata);
        AppleShowcaseResponse showcaseResponse =
                appleConnectService.updateShowcase(appleLocation.getAppleCompanyId(), appleLocation.getAppleBusinessId(), publishInfoMetadata.getShowcaseId(),
                        updateRequest,publishInfoMetadata.getShowcaseEtag());
        if(Objects.nonNull(showcaseResponse)) {
            publishInfoMetadata.setShowcaseEtag(showcaseResponse.getEtag());
            publishInfoMetadata.setPublishStatus(ApplePublishStateEnum.SHOWCASE_CREATED.getName());
        }
        applePublishInfoMetadataRepository.saveAndFlush(publishInfoMetadata);
        sendAppleStatusEmailEvent(ApplePublishStateEnum.SHOWCASE_UPDATED.getName(),metadata,publishInfo.getSocialPost(),
                appleLocation, null);
    }

    private AppleShowcaseUpdateRequest getShowcaseUpdateRequest(SocialPostPublishInfo publishInfo, ApplePostMetadata metadata, ApplePublishInfoMetadata applePublishInfoMetadata) {
        AppleShowcaseUpdateRequest request = new AppleShowcaseUpdateRequest();
        request.setId(publishInfo.getPostId());
        request.setShowcaseDetails(getAppleShowcaseRequest(publishInfo,metadata, applePublishInfoMetadata));
        return request;
    }

    @Override
    public void extendShowcase(SocialPostPublishInfo publishInfo, ApplePostMetadata metadata, ApplePublishInfoMetadata publishInfoMetadata) {
        BusinessAppleLocation appleLocation =
                businessAppleLocationRepo.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
        AppleShowcaseUpdateRequest showcaseRequest = getShowcaseExtendRequest(publishInfo, metadata);
        AppleShowcaseResponse showcaseResponse =
                appleConnectService.extendShowcase(appleLocation.getAppleCompanyId(), appleLocation.getAppleBusinessId(), publishInfo.getPostId(),
                        showcaseRequest,publishInfoMetadata.getShowcaseEtag());
        if(Objects.nonNull(showcaseResponse)) {
            publishInfoMetadata.setShowcaseEtag(showcaseResponse.getEtag());
            publishInfoMetadata.setPublishStatus(ApplePublishStateEnum.SHOWCASE_CREATED.getName());
        }
        applePublishInfoMetadataRepository.saveAndFlush(publishInfoMetadata);
        sendAppleStatusEmailEvent(ApplePublishStateEnum.SHOWCASE_UPDATED.getName(),metadata,publishInfo.getSocialPost(),
                appleLocation, null);
    }

    @Override
    public void updateShowcaseCreative(SocialPostPublishInfo publishInfo, ApplePostMetadata metadata, ApplePublishInfoMetadata publishInfoMetadata) {
        BusinessAppleLocation businessAppleLocation =
                businessAppleLocationRepo.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
        String appleCompanyId = businessAppleLocation.getAppleCompanyId();
        String appleBusinessId = businessAppleLocation.getAppleBusinessId();
        AppleUpdateShowcaseCreativeRequest showcaseCreativeRequest = getUpdateShowcaseCreativeRequest(publishInfo, metadata, businessAppleLocation);
        AppleShowcaseCreativeResponse creativeResponse = appleConnectService.appleShowcaseCreativeUpdate(appleCompanyId,
                appleBusinessId, showcaseCreativeRequest,publishInfoMetadata.getCreativeEtag());
        if(Objects.isNull(creativeResponse)) {
            return ;
        }
        publishInfoMetadata.setCreativeId(creativeResponse.getId());
        publishInfoMetadata.setCreativeEtag(creativeResponse.getEtag());
        publishInfoMetadata.setPublishStatus(ApplePublishStateEnum.SHOWCASE_CREATIVE_CREATED.getName());
        applePublishInfoMetadataRepository.saveAndFlush(publishInfoMetadata);
        if(updateShowcaseStatus(publishInfo, creativeResponse.getState(), 0,  creativeResponse.getId(), false, "Showcase creative updated")) {
            LOG.info("Showcase created upload is complete, waiting for image to be approved");
        }
    }

    @Override
    public boolean deactivateShowcase(SocialPostPublishInfo publishInfo) {
        BusinessAppleLocation businessAppleLocation =
                businessAppleLocationRepo.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
        String appleCompanyId = businessAppleLocation.getAppleCompanyId();
        String appleBusinessId = businessAppleLocation.getAppleBusinessId();
        ApplePublishInfoMetadata publishInfoMetadata = applePublishInfoMetadataRepository.findByPublishInfoId(publishInfo.getId());
        return appleConnectService.deactivateShowcase(appleCompanyId,appleBusinessId,publishInfo.getPostId(),publishInfoMetadata.getShowcaseEtag());
    }

    @Override
    public void addNewShowcaseForPage(List<SocialPostPublishInfo> publishInfos, SocialPostInputMessageRequest socialPostInput, String page) {
        if(!publishInfos.isEmpty()){
            SocialPostPublishInfo publishInfo = publishInfos.get(0);
            SocialPostPublishInfo publishInfoNew = SerializationUtils.clone(publishInfo);
            publishInfoNew.setExternalPageId(page);
            publishInfoNew.setId(null);
            publishInfoNew.setIsPublished(0);
            ApplePublishInfoMetadata publishInfoMetadata = applePublishInfoMetadataRepository.findByPublishInfoId(publishInfo.getId());
            ApplePublishInfoMetadata publishInfoMetadataNew = SerializationUtils.clone(publishInfoMetadata);
            BusinessAppleLocation businessAppleLocation =
                    businessAppleLocationRepo.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
            if(ApplePublishStateEnum.SHOWCASE_CREATED.getName().equals(publishInfoMetadata.getPublishStatus()) ||
                    ApplePublishStateEnum.SHOWCASE_APPROVED.getName().equals(publishInfoMetadata.getPublishStatus())){
                createCreativeInApple(publishInfoNew,socialPostInput.getPostingSites().get(SocialChannel.APPLE_CONNECT.getName()).getAppleMetaData(),
                        businessAppleLocation.getAppleCompanyId(),businessAppleLocation.getAppleBusinessId(),publishInfoMetadataNew,businessAppleLocation);
                publishInfoMetadataNew.setPublishStatus(ApplePublishStateEnum.SHOWCASE_CREATED.getName());
            }
            applePublishInfoMetadataRepository.saveAndFlush(publishInfoMetadataNew);
            socialPostInfoRepository.saveAndFlush(publishInfoNew);
            publishInfos.add(publishInfoNew);
        }

    }

    private AppleUpdateShowcaseCreativeRequest getUpdateShowcaseCreativeRequest(SocialPostPublishInfo publishInfo, ApplePostMetadata metadata, BusinessAppleLocation businessAppleLocation) {
        AppleUpdateShowcaseCreativeRequest updateRequest = new AppleUpdateShowcaseCreativeRequest();
        updateRequest.setId(publishInfo.getPostId());
        updateRequest.setShowcaseCreativeDetails(getShowcaseCreativeDetailsObject(publishInfo,metadata, businessAppleLocation));
        return updateRequest;
    }

    private AppleShowcaseUpdateRequest getShowcaseExtendRequest(SocialPostPublishInfo publishInfo, ApplePostMetadata metadata) {
        AppleShowcaseUpdateRequest request = new AppleShowcaseUpdateRequest();
        request.setId(publishInfo.getPostId());
        AppleShowcaseRequest req = new AppleShowcaseRequest();
        try {
            req.setEndDate(TimeZoneUtil.getDateTimeWithTimezoneOffset(getDateStringForTimezone(metadata.getEndDate(),
                    metadata.getTimeZone(), true),metadata.getTimezoneOffSetValue()));
        }catch(ParseException e) {
            req.setEndDate(metadata.getEndDate());
        }
        request.setShowcaseDetails(req);
        return request;
    }
    private ShowcaseCreativeDetails getShowcaseCreativeDetailsObject(SocialPostPublishInfo publishInfo, ApplePostMetadata metadata, BusinessAppleLocation businessAppleLocation){
        ShowcaseCreativeDetails showcaseCreativeDetails = new ShowcaseCreativeDetails();
        String headlineText = metadata.getTitle();
        String bodyText = publishInfo.getSocialPost().getPostText();
        String appId = null;

        if(Objects.nonNull(businessAppleLocation.getCta())) {
            AppleCTACategory category =  appleCtaCategoryRepo.findFirstByButtonText(metadata.getButtonType());
            Map<String,String> appleCTA = JSONUtils.fromJSON(businessAppleLocation.getCta(), Map.class);
            appId = appleCTA.get(metadata.getButtonType());

            // call to action details
            showcaseCreativeDetails.setCallToAction(category.getCategory());

            if(StringUtils.isNotEmpty(appId)) {
                showcaseCreativeDetails.setAppIds(Collections.singletonList(appId));
            }
        }

        // photo details
        ShowcaseCreativePhoto creativePhoto = new ShowcaseCreativePhoto();
        ShowcaseCaption photoCaption = new ShowcaseCaption();
        photoCaption.setLocale(EN_US);
        photoCaption.setAltText("Photo image"); // to be changes from UI
        creativePhoto.setCaptions(Collections.singletonList(photoCaption));
        creativePhoto.setId(publishInfo.getPostId());
        showcaseCreativeDetails.setPhoto(creativePhoto);
        // title
        ShowcaseCreativeContent titleCreativeContent = new ShowcaseCreativeContent();
        ShowcaseCreativeDescription titleCreativeDescription = new ShowcaseCreativeDescription();
        titleCreativeDescription.setLocale(EN_US);
        titleCreativeDescription.setText(headlineText);
        titleCreativeContent.setPlacement("HEADLINE");
        titleCreativeContent.setDescriptions(Collections.singletonList(titleCreativeDescription));
        // body text
        ShowcaseCreativeContent bodyCreativeContent = new ShowcaseCreativeContent();
        ShowcaseCreativeDescription bodyCreativeDescription = new ShowcaseCreativeDescription();
        bodyCreativeDescription.setLocale(EN_US);
        bodyCreativeDescription.setText(bodyText);
        bodyCreativeContent.setPlacement("BODY");
        bodyCreativeContent.setDescriptions(Collections.singletonList(bodyCreativeDescription));
        showcaseCreativeDetails.setContents(Arrays.asList(titleCreativeContent, bodyCreativeContent));
        return showcaseCreativeDetails;
    }
    @Override
    public void sendAppleShowcaseStatusEmail(AppleShowcaseEmail emailRequest) throws ParseException {
        LOG.info("Request received to send email for apple status mail for: {}", emailRequest);
        if(Objects.isNull(emailRequest) || StringUtils.isEmpty(emailRequest.getShowCaseStatus()))
            return;
        BusinessCoreUser businessCoreUser = businessCoreService.getUserInfo(emailRequest.getCreatedBy());
        if(Objects.isNull(emailRequest.getMetadata())) {
            SocialPost post = socialPostRepository.findById(emailRequest.getSocialPostId());
            SocialPostSchedulerMetadata postMetaData = JSONUtils.fromJSON(post.getPostMetadata(), SocialPostSchedulerMetadata.class);
            emailRequest.setMetadata(JSONUtils.fromJSON(postMetaData.getAppleMetaData(), ApplePostMetadata.class));
        }
        if(Objects.nonNull(emailRequest.getMetadata())) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(APPLE_FEED_DATE_FORMAT);
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
            String scheduledEndDateString = new SimpleDateFormat(EMAIL_SUBJECT_PATTERN_DATE).format(simpleDateFormat.parse(emailRequest.getMetadata().getEndDate()));
            LOG.info("Showcase status for postId: {}: {}", emailRequest.getSocialPostId(), emailRequest.getShowCaseStatus());
            Date scheduledDate = simpleDateFormat.parse(emailRequest.getMetadata().getStartDate());
            String scheduledDateString = new SimpleDateFormat(EMAIL_SUBJECT_PATTERN_DATE).format(scheduledDate);
            Pair<String,String> subjects = getSubjectsByStatus(emailRequest.getShowCaseStatus(),scheduledDateString,scheduledEndDateString);
            List<MediaData> mediaData = getMediaDataForCal(emailRequest.getImageIds(), emailRequest.getEnterpriseId());
            List<String> mediaUrls = mediaData.stream()
                    .map(MediaData::getMediaUrl)
                    .collect(Collectors.toList());
            String imageUrl = mediaUrls.get(0);
            List<ApprovalEmailDTO> emailIds = getCreatorEmailIdAsList(businessCoreUser.getEmailId());
            emailRequest.setSubSubject(subjects.getValue());
            emailRequest.setScheduleToken("Scheduled from " + scheduledDateString + " to " + scheduledEndDateString);
            sendAppleStatusEmail(emailRequest, emailIds, subjects.getKey(), imageUrl,businessCoreUser.getName());
        }
    }
    private Pair<String, String> getSubjectsByStatus(String approvalStatus, String scheduledDateString, String scheduledEndDateString) {
        String subject = String.format(Constants.APPLE_EMAIL_SUBJECT_SENT, scheduledDateString);
        String subSubject = String.format(Constants.APPLE_EMAIL_SUB_SUBJECT_SENT, scheduledDateString);
        if (StringUtils.containsIgnoreCase(approvalStatus,AppleApprovalStatus.REJECTED.getName())) {
            subject = String.format(Constants.APPLE_EMAIL_SUBJECT_REJECTED, scheduledDateString);
            subSubject = String.format(Constants.APPLE_EMAIL_SUB_SUBJECT_REJECTED, scheduledDateString);
        } else if (ApplePublishStateEnum.SHOWCASE_APPROVED.getName().equalsIgnoreCase(approvalStatus)) {
            subject = String.format(Constants.APPLE_EMAIL_SUBJECT_APPROVED, scheduledDateString);
            subSubject = String.format(Constants.APPLE_EMAIL_SUB_SUBJECT_APPROVED, scheduledDateString);
        } else if (AppleApprovalStatus.LIVE.getName().equalsIgnoreCase(approvalStatus)) {
            subject = Constants.APPLE_EMAIL_SUBJECT_LIVE;
            subSubject = String.format(Constants.APPLE_EMAIL_SUB_SUBJECT_LIVE, scheduledEndDateString);
        } else if (ApplePublishStateEnum.SHOWCASE_EXTENDED.getName().equalsIgnoreCase(approvalStatus)) {
            subject = String.format(Constants.APPLE_EMAIL_SUBJECT_EXTENSION_SENT, scheduledDateString);
            subSubject = String.format(Constants.APPLE_EMAIL_SUB_SUBJECT_EXTENSION_SENT, scheduledEndDateString);
        } else if (AppleApprovalStatus.EXTENSION_APPROVED.getName().equalsIgnoreCase(approvalStatus)) {
            subject = String.format(Constants.APPLE_EMAIL_SUBJECT_EXTENSION_APPROVED, scheduledDateString);
            subSubject = String.format(Constants.APPLE_EMAIL_SUB_SUBJECT_EXTENSION_APPROVED, scheduledEndDateString);
        }
        return new Pair<>(subject,subSubject);
    }
    private void sendAppleStatusEmail(AppleShowcaseEmail showcaseEmail, List<ApprovalEmailDTO> emailDTOS, String subject, String collageUrl, String name) {
        if(CollectionUtils.isEmpty(emailDTOS)){
            LOG.info("No email present in data :{}",showcaseEmail);
            return;
        }
        boolean isReseller = checkBusinessIsReseller(showcaseEmail.getEnterpriseId());
        emailDTOS.forEach(email -> {
            EmailDTO emailDTO = new EmailDTO();
            emailDTO.setBusinessId(showcaseEmail.getBusinessId());
            emailDTO.setTo(Collections.singletonList(email.getEmailId()));
            emailDTO.setSubject(subject);
            emailDTO.setRequestType(ApprovalEnum.social_approval_request_template.name());
            emailDTO.setExternalUuid(emailDTO.getRequestType().concat(String.valueOf(showcaseEmail.getSocialPostId())));
            String postToken = prepareAppleEmailToken(showcaseEmail,email.isPublic());
            nexusService.sendEmailV2(emailDTO, getEmailPayload(showcaseEmail, collageUrl, name, email, postToken, isReseller), isReseller);
        });
    }

    @NotNull
    private Map<String, Object> getEmailPayload(AppleShowcaseEmail showcaseEmail, String collageUrl, String name, ApprovalEmailDTO email, String postToken, boolean isReseller) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("pageName", showcaseEmail.getAppleBusinessName());
        payload.put("subSubject", showcaseEmail.getSubSubject());
        payload.put("pageProfileImage",getProfileLogoOfBusiness(showcaseEmail.getProfileLogo()));
        payload.put("authorName", name);
        payload.put("scheduleDate", showcaseEmail.getScheduleToken());
        payload.put("collageUrl", collageUrl);
        payload.put("postToken", postToken);
        payload.put("isReseller", isReseller);
        payload.put("text", showcaseEmail.getPostText());
        if (StringUtils.containsIgnoreCase(showcaseEmail.getShowCaseStatus(),AppleApprovalStatus.REJECTED.getName())) {
            payload.put("editPostToken", prepareAppleEmailToken(showcaseEmail, email.isPublic()));
            payload.put("rejectedReason", showcaseEmail.getRejectedReason());
        }
        else
            payload.put("viewDetailToken",prepareAppleEmailToken(showcaseEmail, email.isPublic()));
        return payload;
    }

    private String getProfileLogoOfBusiness(String logoUrl) {
        return (StringUtils.isNotBlank(logoUrl)) ? logoUrl : CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultLogoUrl();
    }

    private String prepareAppleEmailToken(AppleShowcaseEmail data, boolean isPublic) {
        String url = null;
        String commonUrl = isPublic ? CacheManager.getInstance().getCache(SystemPropertiesCache.class).getPublicUrl()
                : CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBirdeyeUrl();
      if (AppleApprovalStatus.REJECTED.getName().equalsIgnoreCase(data.getShowCaseStatus())) {
            url = commonUrl+"dashboard/social/publish/createpost?postId="+data.getSocialPostId()+"&action=edit&isQuotedPost=false";
      }else
            url = commonUrl+"dashboard/social/publish";
    LOG.info("Url for email tokens :{}",url);
    return url;
    }
    private boolean checkBusinessIsReseller(Long businessNumber) {
        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLiteByNumber(businessNumber);
        return businessLiteDTO.getResellerId() != null && !BusinessAccountTypeEnum.DIRECT.getName().equalsIgnoreCase(businessLiteDTO.getAccountType());
    }
    private List<ApprovalEmailDTO> getCreatorEmailIdAsList(String emailId) {
        List<ApprovalEmailDTO>  approvalEmailDTOS = new ArrayList<>();
        ApprovalEmailDTO approvalEmailDTO = new ApprovalEmailDTO();
        approvalEmailDTO.setEmailId(emailId);
        approvalEmailDTO.setPublic(false);
        approvalEmailDTOS.add(approvalEmailDTO);
        return approvalEmailDTOS;
    }
    @Override
    public void checkIfAppleShowcaseIsLive(AppleLiveCheckRequest request) {
        LOG.info("Request received to send live showcase email {}",request);
        SocialPostPublishInfo publishInfo = socialPostInfoRepository.findOne(request.getPublishInfoId());
        ApplePublishInfoMetadata applePublishInfoMetadata = applePublishInfoMetadataRepository.findByPublishInfoId(request.getPublishInfoId());
        BusinessAppleLocation businessAppleLocation = businessAppleLocationRepo.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
        AppleShowcaseResponse showcaseResponse = appleConnectService.getShowcaseStatus(businessAppleLocation.getAppleCompanyId(),
                                    businessAppleLocation.getAppleBusinessId(), applePublishInfoMetadata.getShowcaseId());
        if(StringUtils.equalsIgnoreCase(showcaseResponse.getState(),"PUBLISHED")) {
            applePublishInfoMetadata.setPublishStatus(ApplePublishStateEnum.LIVE.getName());
            applePublishInfoMetadataRepository.saveAndFlush(applePublishInfoMetadata);
            sendAppleStatusEmailEvent(ApplePublishStateEnum.LIVE.name(), null, publishInfo.getSocialPost(),
                    businessAppleLocation, null);
            publishInfo.setIsPublished(1);
            publishInfo.setFailureReason(null);
            LOG.info("Email sent for live status update {}",request);
            socialPostScheduleInfoRepository.updateisPublishedByWithId(SocialSchedulePostStatusEnum.POSTED.getId(), publishInfo.getSocialPostId());
            socialPostActivityService.sendEventToSaveActivity(publishInfo.getSocialPostId(), publishInfo.getSocialPost().getCreatedBy(), PostActivityType.PUBLISHED.getName(), null,null);
        }else{
            boolean retryLimit = commonService.retryQuotaCheck(publishInfo.getId());
            if(!retryLimit) {
                Integer timeDurationForRetry = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getTimeDurationDelayForApple();
                Calendar cal = Calendar.getInstance();
                cal.setTime(new Date());
                cal.add(Calendar.MINUTE, timeDurationForRetry);
                liveCheckRequestToSamay(publishInfo.getId(), cal.getTime(),publishInfo.getSocialPost().getId(),
                        Long.valueOf(businessAppleLocation.getBusinessId()));
                return;
            }

            publishInfo.setIsPublished(2);
            publishInfo.setFailureReason("Showcase is not PUBLISHED");
            LOG.warn("Email not sent for live status update {}",request);
            LOG.warn("Showcase is not PUBLISHED");
        }
        socialPostInfoRepository.saveAndFlush(publishInfo);
    }
    private Integer getSamaySchedulingInfoId(Integer postId, AppleLiveCheckRequest request) {
        List<SocialSamaySchedulingInfo> socialSamaySchedulingInfoList = socialRetryPostRepo.findBySocialPostIdAndIdentifier(postId,
                Constants.APPLE_SHOWCASE_LIVE_CHECK);

        Optional<SocialSamaySchedulingInfo> socialSamaySchedulingInfo = socialSamaySchedulingInfoList.stream()
                .filter(v -> v.getSamayPayload().contains(String.valueOf(request.getPublishInfoId()))).findFirst();

        if(!socialSamaySchedulingInfo.isPresent()) {
            SocialSamaySchedulingInfo req = new SocialSamaySchedulingInfo(Constants.APPLE_SHOWCASE_LIVE_CHECK,JSONUtils.toJSON(request), null, postId);
            return socialRetryPostRepo.saveAndFlush(req).getId();
        }
        return socialSamaySchedulingInfo.get().getId();
    }

    private void removeSamayReminderForLiveCheck(Integer socialPostId, Integer publishInfoId) {
        List<SocialSamaySchedulingInfo> socialSamaySchedulingInfo = socialRetryPostRepo.findBySocialPostIdAndIdentifier(socialPostId,
                Constants.APPLE_SHOWCASE_LIVE_CHECK);

        Optional<SocialSamaySchedulingInfo> samaySchedulingInfoOptional = socialSamaySchedulingInfo.stream()
                .filter(v -> v.getSamayPayload().contains(String.valueOf(publishInfoId))).findFirst();

        if(samaySchedulingInfoOptional.isPresent() && Objects.nonNull(samaySchedulingInfoOptional.get().getSchedulerAcknowledgementId()))
            removePostOnSamay(samaySchedulingInfoOptional.get().getSchedulerAcknowledgementId());
    }
    private boolean removePostOnSamay (String eventId){
        Map<String, String> payload = new HashMap<>();
        payload.put("application", "Social");
        payload.put("eventId", eventId);
        return samayService.cancelEventToSamayScheduler(payload);
    }
    private List<MediaData> getMediaDataForCal(String imageIds, Long enterpriseLongId){
        List<MediaData> listOfMediaData = new ArrayList<>();
        String[] idsConversion = imageIds.split(",");
        List<Integer> ids = new ArrayList<>();
        for (String id : idsConversion) {
            ids.add(Integer.parseInt(id));
        }
        List<SocialPostsAssets> postAssets = socialPostsAssetService.findByIds(new HashSet<>(ids));
        for (SocialPostsAssets media : postAssets) {
            String url = Objects.isNull(media.getImageUrl())
                    ? socialPostsAssetService.getCompleteVideoUrlFromPostAsset(media, enterpriseLongId.toString())
                    : socialPostsAssetService.getCompleteImageUrlFromPostAsset(media, enterpriseLongId.toString());
            listOfMediaData.add(new MediaData(url, media.getAssetMetaData()));
        }
        return listOfMediaData;
    }
    @NotNull
    private String getDateStringForTimezone(String dateString, String timezone, boolean isEndDate) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(APPLE_FEED_DATE_FORMAT);
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat(APPLE_FEED_DATE_FORMAT);
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone(ZoneId.of ( "UTC" )));
        simpleDateFormat2.setTimeZone(TimeZone.getTimeZone(ZoneId.of ( timezone )));
        if(isEndDate)
            return simpleDateFormat2.format(DateTimeUtils.addTimeInMinutes(simpleDateFormat.parse(dateString),24*60));
        else
            return simpleDateFormat2.format(simpleDateFormat.parse(dateString));
    }

}
