package com.birdeye.social.specification;

import com.birdeye.social.constant.PageSortDirection;
import com.birdeye.social.constant.ValidTypeEnum;
import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.entities.BusinessLinkedinPage;
import com.birdeye.social.entities.BusinessTwitterAccounts;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class XSpecification {

    public Specification<BusinessTwitterAccounts> hasResellerId(Long resellerId) {
        if(Objects.isNull(resellerId)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.equal(root.get("resellerId"),  resellerId);
        });
    }

    public Specification<BusinessTwitterAccounts> hasPageName(String pageName) {

        if(Objects.isNull(pageName)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.like(root.get("name"),  "%" + pageName + "%");
        });
    }

    public Specification<BusinessTwitterAccounts> isSelected(Integer i) {
        if(Objects.isNull(i)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.equal(root.get("isSelected"), i);
        });
    }
    public Specification<BusinessTwitterAccounts> hasRequestId(String requestId) {
        if(Objects.isNull(requestId)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.equal(root.get("requestId"), requestId);
        });
    }

    public Specification<BusinessTwitterAccounts> inBusinessIds(List<Integer> businessIds) {
        if(CollectionUtils.isEmpty(businessIds)){
            return null;
        }
        return ((root, query, cb) -> {
            return root.get("businessId").in(businessIds);
        });
    }

    public Specification<BusinessTwitterAccounts> inValidityTypes(List<Integer> validityTypes) {
        if(CollectionUtils.isEmpty(validityTypes)){
            return null;
        }
        return ((root, query, cb) -> {
            return root.get("validType").in(validityTypes);
        });
    }

    public Specification<BusinessTwitterAccounts> inCreatedByIds(List<Integer> createdByIds) {
        if(CollectionUtils.isEmpty(createdByIds)){
            return null;
        }
        return ((root, query, cb) -> {
            return root.get("createdBy").in(createdByIds);
        });
    }

    public Specification<BusinessTwitterAccounts> hasBusinessIdNullOrNotNull(boolean isNull) {
        return ((root, query, cb) -> {
            return isNull
                    ?cb.isNull(root.get("businessId"))
                    :cb.isNotNull(root.get("businessId"));
        });
    }

    public  Specification<BusinessTwitterAccounts> sortBusinessIdNullsFirst() {
        return (root, query, criteriaBuilder) -> {
            // Sort: NULL businessId first, then non-NULL
            query.orderBy(
                    criteriaBuilder.asc(
                            criteriaBuilder.selectCase()
                                    .when(criteriaBuilder.isNull(root.get("businessId")), 0)
                                    .otherwise(1)
                    ),
                    criteriaBuilder.asc(root.get("name"))
            );
            return criteriaBuilder.conjunction();
        };
    }

    public  Specification<BusinessTwitterAccounts> sortValidTypesInGroup(PageSortDirection sortDirection) {
        return (root, query, criteriaBuilder) -> {
            // Sort: NULL businessId first, then non-NULL
            query.orderBy(
                    PageSortDirection.ASC.equals(sortDirection)?criteriaBuilder.asc(
                            criteriaBuilder.selectCase()
                                    .when(criteriaBuilder.equal(root.get("validType"), ValidTypeEnum.VALID.getId()), 0)
                                    .when(criteriaBuilder.equal(root.get("validType"), ValidTypeEnum.PARTIAL_VALID.getId()),1)
                                    .when(criteriaBuilder.equal(root.get("validType"), ValidTypeEnum.INVALID.getId()),1)
                                    .otherwise(1)
                    )
                            : criteriaBuilder.desc(
                            criteriaBuilder.selectCase()
                                    .when(criteriaBuilder.equal(root.get("validType"), ValidTypeEnum.VALID.getId()), 0)
                                    .when(criteriaBuilder.equal(root.get("validType"), ValidTypeEnum.PARTIAL_VALID.getId()),1)
                                    .when(criteriaBuilder.equal(root.get("validType"), ValidTypeEnum.INVALID.getId()),1)
                                    .otherwise(1)
                    ),
                    criteriaBuilder.asc(root.get("name"))
            );
            return criteriaBuilder.conjunction();
        };
    }

    public Specification<BusinessTwitterAccounts> hasEnterpriseId(Long enterpriseId) {
        if(Objects.isNull(enterpriseId)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.equal(root.get("enterpriseId"),  enterpriseId);
        });
    }

    public Specification<BusinessTwitterAccounts> isValid(Integer i) {
        if(Objects.isNull(i)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.equal(root.get("isValid"), i);
        });
    }
}
