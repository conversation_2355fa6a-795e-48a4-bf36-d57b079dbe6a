package com.birdeye.social.model;

import com.birdeye.social.constant.FilterPostStatuses;
import com.birdeye.social.constant.FilterPostType;
import com.birdeye.social.constant.PageSortDirection;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonSerialize(include=JsonSerialize.Inclusion.NON_NULL)
public class GlobalFilterCriteriaSchedulePostMessage implements Serializable {

	 /**
	 * 
	 */
	private static final long serialVersionUID = 2792646741766042362L;
	private String startDate;
	private String endDate;
	private String postStatus;
	private List<String> socialChannels;
	private Integer businessId;
	private List<Integer> businessIds;
	private List<Integer> accessibleLocationIds;
	private Set<Long> tagIds;
	private List<Integer> approvals;
	private List<Integer> creators;

	private Integer pageNo;

	private Integer pageSize;

	private PageSortDirection order;
	private boolean businessIdsSelected;

	List<FilterPostType> postType;
	List<FilterPostType> postContent;

	private boolean showAiSuggestions;
	private boolean hasFullAccess = false;
	List<FilterPostStatuses> postStatuses;

	public List<FilterPostStatuses> getPostStatuses() {
		return postStatuses;
	}

	public void setPostStatuses(List<FilterPostStatuses> postStatuses) {
		this.postStatuses = postStatuses;
	}

	public List<Integer> getApprovals() {
		return approvals;
	}

	public void setApprovals(List<Integer> approvals) {
		this.approvals = approvals;
	}

	public List<Integer> getCreators() {
		return creators;
	}

	public void setCreators(List<Integer> creators) {
		this.creators = creators;
	}

	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public String getPostStatus() {
		return postStatus;
	}
	public void setPostStatus(String postStatus) {
		this.postStatus = postStatus;
	}
	public List<String> getSocialChannels() {
		return socialChannels;
	}
	public void setSocialChannels(List<String> socialChannels) {
		this.socialChannels = socialChannels;
	}

	public Integer getBusinessId() {
		return businessId;
	}

	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}

	public List<Integer> getBusinessIds() {
		return businessIds;
	}

	public void setBusinessIds(List<Integer> businessIds) {
		this.businessIds = businessIds;
	}

	public List<Integer> getAccessibleLocationIds() {
		return accessibleLocationIds;
	}

	public void setAccessibleLocationIds(List<Integer> accessibleLocationIds) {
		this.accessibleLocationIds = accessibleLocationIds;
	}

	public Integer getPageNo() {
		return pageNo;
	}

	public void setPageNo(Integer pageNo) {
		this.pageNo = pageNo;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public PageSortDirection getOrder() {
		return order;
	}

	public void setOrder(PageSortDirection order) {
		this.order = order;
	}
	public Set<Long> getTagIds() {
		return tagIds;
	}

	public void setTagIds(Set<Long> tagIds) {
		this.tagIds = tagIds;
	}

	public boolean isBusinessIdsSelected() {
		return businessIdsSelected;
	}

	public void setBusinessIdsSelected(boolean businessIdsSelected) {
		this.businessIdsSelected = businessIdsSelected;
	}

	public List<FilterPostType> getPostType() {
		return postType;
	}

	public void setPostType(List<FilterPostType> postType) {
		this.postType = postType;
	}

	public boolean getShowAiSuggestions() {
		return showAiSuggestions;
	}

	public void setShowAiSuggestions(boolean showAiSuggestions) {
		this.showAiSuggestions = showAiSuggestions;
	}

	public boolean getHasFullAccess() {
		return hasFullAccess;
	}

	public void setHasFullAccess(boolean hasFullAccess) {
		this.hasFullAccess = hasFullAccess;
	}

	public void setPostContent(List<FilterPostType> postContent) { this.postContent = postContent; }

	public List<FilterPostType> getPostContent() { return postContent; }
}
