package com.birdeye.social.model.approval_workflow;

import com.birdeye.social.model.GoogleOfferDetails;
import com.birdeye.social.model.MediaData;
import com.birdeye.social.model.MentionData;
import com.birdeye.social.model.TweetDetailsResponse;
import com.birdeye.social.sro.SocialTagBasicDetail;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApprovalWorkFlowData implements Serializable {

    private Integer id;
    private String postId;
    private String text;
    private String authorName;
    private String pageName;
    private String pageProfileImage;
    private List<MediaData> imageUrlsMetaData;
    private List<String> imageUrls;
    private List<MediaData> videoUrlsMetaData;
    private List<String> videoUrls;
    private String linkPreviewUrl;
    private String scheduleDate;
    private Integer scheduleInfoId;
    private List<String> mediaFiles;
    private List<MentionData> mentions;
    private String type;
    private Integer locationCount;
    private String channel;
    private String approvalWorkFlowId;
    private Integer approvalRequestId;
    private String approvalUUId;
    private Integer referenceStepId;
    private String conversationId;
    private List<String> approvalUserIds;
    private boolean isCreator;
    private boolean isApprover;
    private List<Attachments> attachments;
    private String collageUrl;
    private String businessName;
    private Long businessNumber;
    private Integer businessId;
    private Integer createdBy;
    private String rejectedBy;
    private Integer rejectedById;
    private String rejectedReason;
    private String approvalStatus;
    @JsonProperty("isReseller")
    private boolean isReseller;
    private boolean isQuoted;
    private String quotedPostId;
    private String subSubject;
    private TweetDetailsResponse tweetDetailsResponse;
    private String postMetaData;
    private boolean isCoBranded;
    private boolean isWhitelabel;
    private GoogleOfferDetails gmbOfferDetails;
    private List<SocialTagBasicDetail> tags;
    private List<String> tagNameList;
    private String topicType;
    private String pageInitials;
    private Boolean isEditedPost;
    private Boolean aiPost;
    private Boolean isSameContent;
    private String timeZone;
    private String postCategory;

    public boolean isReseller() {
        return isReseller;
    }

    @JsonProperty("isReseller")
    public void setReseller(boolean reseller) {
        isReseller = reseller;
    }
}
