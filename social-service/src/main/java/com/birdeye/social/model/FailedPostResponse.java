package com.birdeye.social.model;

import com.birdeye.social.sro.SocialTagBasicDetail;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class FailedPostResponse {
    private Integer id;
    private String postText;
    private List<MediaData> images;
    private List<String> compressedImages;
    private List<MediaData> videos;
    private List<String> mediaSequence;
    private String publishDate;
    private String publishedBy;
    private Date datePublish;
    private String endDate;
    private Date dateEnd;
    private List<String> postingSites;
    private Integer isPublished;
    private List<MentionData> mentions;
    private Boolean hasAccess = true;
    private List<String> incompleteChannel;
    private List<String> videoThumbnails;
    private String linkPreviewUrl;
    private Integer failedChannelCount;
    private Integer failedPageCount;
    private Boolean isQuotedTweet;
    private String type;
    private List<SocialTagBasicDetail> tags;
    private GoogleOfferDetails gmbOfferDetails;

    //New field addition for email and desktop notification
    private String postSubject;
    private Boolean postDeleted= false;
    private Boolean aiPost;
    private String postCategory;
}
