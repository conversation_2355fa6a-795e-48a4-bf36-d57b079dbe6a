package com.birdeye.social.model;

import com.birdeye.social.sro.AbstractSocialTagOperation;
import com.birdeye.social.sro.SocialTagMappingOperationRequest;
import com.birdeye.social.sro.SocialTagOperationRequest;
import lombok.*;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class SocialMasterPostInputMessages {
    private String scheduleDate;
    private Integer businessId;
    private Integer createdBy;
    private Integer editedBy;
    private String saveType;
    private Integer reviewId;
    private Boolean autoShare;
    private Boolean createPostLib = false;
    private Integer postLibId;
    private Integer aiPostId;
    private Integer masterPostId;
    @NotEmpty
    private List<SocialPostInputMessageRequest> socialPostList;
    private List<BusinessGroup> bizGroupMappings;
    private List<Integer> selectedLocationIds;
    private Set<SocialTagMappingOperationRequest> tagMappings;
    private Boolean aiPost;
}