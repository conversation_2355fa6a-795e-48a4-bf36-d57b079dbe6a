package com.birdeye.social.model;

import com.birdeye.social.dto.SocialTagMappingInfo;
import com.birdeye.social.entities.Mention;
import com.birdeye.social.insights.ES.CalendarViewPagePostInsightsData;
import com.birdeye.social.sro.SocialTagBasicDetail;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SocialSchedulePostMessage {

    private Integer id;
    private String postText;
    private String postHeader;
    private List<MediaData> images;
    private List<String> compressedImages;
    private List<MediaData> videos;
    private List<String> videoThumbnails;
    private String linkPreviewUrl;
    private String publishDate;
    private String publishedBy;
    private Date datePublish;
    private Integer scheduleInfoId;
    private List<String> postingSites;
    private Integer isPublished;
    private List<String> mediaSequence;
    private List<MentionData> mentions;
    private SocialPostPermissionStatusResponse permissionStatus;
    private List<CalendarViewPagePostInsightsData> postInsights;
    private Boolean hasAccess = true;
    private List<String> incompleteChannel;
    private Boolean hasPostFailed = false;
    private Integer failedChannelCount;
    private Integer failedPageCount;
    private List<String> failedSites;
    private String type;
    private GoogleOfferDetails gmbOfferDetails;
    private Boolean isExpired;
    private Integer duplicatedCount;
    private Boolean isQuotedTweet;
    private List<SocialTagBasicDetail> tags;
    private Boolean isCreator = false;
    private Boolean isApprover = false;
    private Integer approveWorkflowId;
    private String approvalStatus;
    private String approvalUUId;
    private Integer approvalRequestId;
    private String conversationId;
    private Integer referenceStepId;
    private String endDate;
    private String applePublishStatus;
    private Boolean isOperationAllowed = false;
    private String quotedTweetSource;
    private String createdByName;

    private Boolean aiPost;

    private Boolean aiSuggestion = false;
    private String postCategory;
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPostText() {
        return postText;
    }

    public List<String> getVideoThumbnails() {
        return videoThumbnails;
    }

    public void setVideoThumbnails(List<String> videoThumbnails) {
        this.videoThumbnails = videoThumbnails;
    }

    public void setPostText(String postText) {
        this.postText = postText;
    }

    public List<MediaData> getImages() {
        return images;
    }

    public Boolean getHasPostFailed() {
        return hasPostFailed;
    }

    public void setHasPostFailed(Boolean hasPostFailed) {
        this.hasPostFailed = hasPostFailed;
    }

    public void setImages(List<MediaData> images) {
        this.images = images;
    }

    public List<MediaData> getVideos() {
        return videos;
    }

    public void setVideos(List<MediaData> videos) {
        this.videos = videos;
    }

    public String getPublishDate() {
        return publishDate;
    }

    public void setPublishDate(String publishDate) {
        this.publishDate = publishDate;
    }

    public String getPublishedBy() {
        return publishedBy;
    }

    public void setPublishedBy(String publishedBy) {
        this.publishedBy = publishedBy;
    }

    public Date getDatePublish() {
        return datePublish;
    }

    public void setDatePublish(Date datePublish) {
        this.datePublish = datePublish;
    }

    public List<String> getPostingSites() {
        return postingSites;
    }

    public void setPostingSites(List<String> postingSites) {
        this.postingSites = postingSites;
    }

    public Integer getIsPublished() {
        return isPublished;
    }

    public void setIsPublished(Integer isPublished) {
        this.isPublished = isPublished;
    }

    public List<String> getMediaSequence() {
        return mediaSequence;
    }

    public void setMediaSequence(List<String> mediaSequence) {
        this.mediaSequence = mediaSequence;
    }

    public List<MentionData> getMentions() {
        return mentions;
    }

    public void setMentions(List<MentionData> mentions) {
        this.mentions = mentions;
    }

    public SocialPostPermissionStatusResponse getPermissionStatus() {
        return permissionStatus;
    }

    public void setPermissionStatus(SocialPostPermissionStatusResponse permissionStatus) {
        this.permissionStatus = permissionStatus;
    }

    public Boolean getHasAccess() {
        return hasAccess;
    }

    public void setHasAccess(Boolean hasAccess) {
        this.hasAccess = hasAccess;
    }

    public List<String> getIncompleteChannel() {
        return incompleteChannel;
    }

    public void setIncompleteChannel(List<String> incompleteChannel) {
        this.incompleteChannel = incompleteChannel;
    }
    public Integer getDuplicatedCount() {
        return duplicatedCount;
    }

    public void setDuplicatedCount(Integer duplicatedCount) {
        this.duplicatedCount = duplicatedCount;
    }

    public Boolean getIsQuotedTweet() {
        return isQuotedTweet;
    }

    public void setIsQuotedTweet(Boolean isQuotedTweet) {
        this.isQuotedTweet = isQuotedTweet;
    }
    public List<String> getCompressedImages() {
        return compressedImages;
    }

    public void setCompressedImages(List<String> compressedImages) {
        this.compressedImages = compressedImages;
    }

    public String getLinkPreviewUrl() {
        return linkPreviewUrl;
    }

    public void setLinkPreviewUrl(String linkPreviewUrl) {
        this.linkPreviewUrl = linkPreviewUrl;
    }

    public List<CalendarViewPagePostInsightsData> getPostInsights() {
        return postInsights;
    }

    public void setPostInsights(List<CalendarViewPagePostInsightsData> postInsights) {
        this.postInsights = postInsights;
    }

    public Integer getFailedChannelCount() {
        return failedChannelCount;
    }

    public void setFailedChannelCount(Integer failedChannelCount) {
        this.failedChannelCount = failedChannelCount;
    }

    public Integer getFailedPageCount() {
        return failedPageCount;
    }

    public void setFailedPageCount(Integer failedPageCount) {
        this.failedPageCount = failedPageCount;
    }

    public List<String> getFailedSites() {
        return failedSites;
    }

    public void setFailedSites(List<String> failedSites) {
        this.failedSites = failedSites;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getScheduleInfoId() {
        return scheduleInfoId;
    }

    public void setScheduleInfoId(Integer scheduleInfoId) {
        this.scheduleInfoId = scheduleInfoId;
    }

    public List<SocialTagBasicDetail> getTags() {
        return tags;
    }

    public void setTags(List<SocialTagBasicDetail> tags) {
        this.tags = tags;
    }

    public String getQuotedTweetSource() {
        return quotedTweetSource;
    }

    public void setQuotedTweetSource(String quotedTweetSource) {
        this.quotedTweetSource = quotedTweetSource;
    }

    public String getCreatedByName() {
        return createdByName;
    }

    public void setCreatedByName(String createdByName) {
        this.createdByName = createdByName;
    }

    public Boolean getAiPost() {
        return aiPost;
    }

    public void setAiPost(Boolean aiPost) {
        this.aiPost = aiPost;
    }

    public String getPostHeader() {
        return postHeader;
    }

    public void setPostHeader(String postHeader) {
        this.postHeader = postHeader;
    }

    public Boolean getAiSuggestion() {
        return aiSuggestion;
    }

    public void setAiSuggestion(Boolean aiSuggestion) {
        this.aiSuggestion = aiSuggestion;
    }
}
