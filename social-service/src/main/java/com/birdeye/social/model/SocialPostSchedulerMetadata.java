package com.birdeye.social.model;

import com.birdeye.social.linkedin.LinkedInPostMetadataRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SocialPostSchedulerMetadata implements Serializable {

    public enum PostType {
        REVIEW("review");

        private String name;

        PostType(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    private static final long serialVersionUID = -8876783736122139173L;

    private String postType;

    private String gmbPostMetaData;

    private String locationTagMetaData;

    private String linkedinPostMetaData;

    private String mediaSequence;
    private String templateData;
    private String youtubePostMetaData;
    private String linkInBioDetails;
    private String igPostMetadata;
    private String fbPostMetadata;

    private String tiktokPostMetaData;
    private String videoThumbnailMetadata;

    private String contentGenerated;

    private Boolean isValidPreview = true;

    private String appleMetaData;

    public String getPostType() {
        return postType;
    }

    public void setPostType(String postType) {
        this.postType = postType;
    }

    public String getGmbPostMetaData() {
        return gmbPostMetaData;
    }

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public GMBPostMetadataRequest getGmbPostMetaDataObj() throws IOException {
        if (StringUtils.isNotBlank(this.gmbPostMetaData)) {
            return OBJECT_MAPPER.readValue(this.gmbPostMetaData, GMBPostMetadataRequest.class);
        } else {
            return null;
        }
    }

    public FbPostMetadata getFbPostMetaDataObj() throws IOException {
        if(StringUtils.isNotBlank(this.fbPostMetadata)){
            return OBJECT_MAPPER.readValue(this.fbPostMetadata, FbPostMetadata.class);
        }else{
            return null;
        }
    }

    public IgPostMetadata getIGPostMetaDataObj() throws IOException {
        if(StringUtils.isNotBlank(this.igPostMetadata)){
            return OBJECT_MAPPER.readValue(this.igPostMetadata, IgPostMetadata.class);
        }else{
            return null;
        }
    }

    public ApplePostMetadata getApplePostMetaDataObj() throws IOException {
        if(StringUtils.isNotBlank(this.appleMetaData)){
            return OBJECT_MAPPER.readValue(this.appleMetaData, ApplePostMetadata.class);
        }else{
            return null;
        }
    }


    public String getApplePostMetaDataObjStartDate() throws IOException {
        if (StringUtils.isNotBlank(this.appleMetaData)) {
            ApplePostMetadata metadata = OBJECT_MAPPER.readValue(this.appleMetaData, ApplePostMetadata.class);
            return metadata.getStartDate();
        }
        return null;
    }

    public String getApplePostMetaDataObjEndDate() throws IOException {
        if (StringUtils.isNotBlank(this.appleMetaData)) {
            ApplePostMetadata metadata = OBJECT_MAPPER.readValue(this.appleMetaData, ApplePostMetadata.class);
            return metadata.getEndDate();
        }
        return null;
    }

    public void setGmbPostMetaData(String gmbPostMetaData) {
        this.gmbPostMetaData = gmbPostMetaData;
    }

    public void setGmbPostMetaData(GMBPostMetadataRequest gmbPostMetaData) throws JsonProcessingException {
        this.gmbPostMetaData = OBJECT_MAPPER.writeValueAsString(gmbPostMetaData);
    }

    public String getLocationTagMetaData() {
        return locationTagMetaData;
    }

    public void setLocationTagMetaData(String locationTagMetaData) {
        this.locationTagMetaData = locationTagMetaData;
    }

    public void setMediaSequence(String mediaSequence) {
        this.mediaSequence = mediaSequence;
    }

    public String getLinkedinPostMetaData() {
        return linkedinPostMetaData;
    }

    public LinkedInPostMetadataRequest getLinkedinPostMetaDataObj() throws IOException {
        if(StringUtils.isNotBlank(this.linkedinPostMetaData)){
            return OBJECT_MAPPER.readValue(this.linkedinPostMetaData, LinkedInPostMetadataRequest.class);
        }else{
            return null;
        }
    }

    public void setLinkedinPostMetaData(String linkedinPostMetaData) {
        this.linkedinPostMetaData = linkedinPostMetaData;
    }

    public void setLinkedinPostMetaData(LinkedInPostMetadataRequest linkedinPostMetaData) throws JsonProcessingException {
        this.linkedinPostMetaData = OBJECT_MAPPER.writeValueAsString(linkedinPostMetaData);
    }

    public String getMediaSequence() {
        return mediaSequence;
    }

    public void setMediaSequence(List<String> mediaSequence) throws JsonProcessingException {
        this.mediaSequence = OBJECT_MAPPER.writeValueAsString(mediaSequence);
    }

    public String getTemplateData() {
        return templateData;
    }
    public TemplateData getTemplateDataObj() throws IOException{
        if(StringUtils.isNotBlank(this.templateData)){
            return OBJECT_MAPPER.readValue(this.templateData, TemplateData.class);
        }else{
            return null;
        }
    }
    public void setTemplateData(String templateData) {
        this.templateData = templateData;
    }
    public void setTemplateData(TemplateData templateData) throws JsonProcessingException {
        this.templateData = OBJECT_MAPPER.writeValueAsString(templateData);
    }

    public String getYoutubePostMetaData() {
        return youtubePostMetaData;
    }

    public void setYoutubePostMetaData(String youtubePostMetaData) {
        this.youtubePostMetaData = youtubePostMetaData;
    }

    public String getLinkInBioDetails() {
        return linkInBioDetails;
    }

    public void setLinkInBioDetails(String linkInBioDetails) {
        this.linkInBioDetails = linkInBioDetails;
    }
    public String getIgPostMetadata() {
        return igPostMetadata;
    }

    public void setIgPostMetadata(String igPostMetadata) {
        this.igPostMetadata = igPostMetadata;
    }

    public String getVideoThumbnailMetadata() {
        return videoThumbnailMetadata;
    }

    public void setVideoThumbnailMetadata(String videoThumbnailMetadata) {
        this.videoThumbnailMetadata = videoThumbnailMetadata;
    }


    public String getContentGenerated() {
        return contentGenerated;
    }

    public void setContentGenerated(String contentGenerated) {
        this.contentGenerated = contentGenerated;
    }

    public Boolean getValidPreview() {
        return isValidPreview;
    }

    public void setValidPreview(Boolean validPreview) {
        isValidPreview = validPreview;
    }

    public String getAppleMetaData() {
        return appleMetaData;
    }

    public void setAppleMetaData(String appleMetaData) {
        this.appleMetaData = appleMetaData;
    }

    public String getTiktokPostMetaData() {
        return tiktokPostMetaData;
    }

    public void setTiktokPostMetaData(String tiktokPostMetaData) {
        this.tiktokPostMetaData = tiktokPostMetaData;
    }

    public String getFbPostMetadata() {
        return fbPostMetadata;
    }

    public void setFbPostMetadata(String fbPostMetadata) {
        this.fbPostMetadata = fbPostMetadata;
    }

    @Override
    public String toString() {
        return "SocialPostSchedulerMetadata{" +
                "postType='" + postType + '\'' +
                ", gmbPostMetaData='" + gmbPostMetaData + '\'' +
                ", locationTagMetaData='" + locationTagMetaData + '\'' +
                ", linkedinPostMetaData='" + linkedinPostMetaData + '\'' +
                ", mediaSequence='" + mediaSequence + '\'' +
                ", templateData='" + templateData + '\'' +
                ", youtubePostMetaData='" + youtubePostMetaData + '\'' +
                ", linkInBioDetails='" + linkInBioDetails + '\'' +
                ", igPostMetadata='" + igPostMetadata + '\'' +
                ", fbPostMetadata='" + fbPostMetadata + '\'' +
                ", tiktokPostMetaData='" + tiktokPostMetaData + '\'' +
                ", videoThumbnailMetadata='" + videoThumbnailMetadata + '\'' +
                ", contentGenerated='" + contentGenerated + '\'' +
                ", isValidPreview=" + isValidPreview +
                ", appleMetaData='" + appleMetaData + '\'' +
                '}';
    }
}
