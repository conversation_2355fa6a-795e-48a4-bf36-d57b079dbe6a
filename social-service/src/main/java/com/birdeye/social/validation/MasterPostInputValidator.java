package com.birdeye.social.validation;

import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.model.SocialMasterPostInputMessages;
import com.birdeye.social.model.SocialPostInputMessageRequest;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * Validator for SocialMasterPostInputMessages that validates schedule dates in the social post list.
 */
@Component
public class MasterPostInputValidator implements Validator {

    private static final String SCHEDULE_DATE_LIMIT_MESSAGE = "Schedule date cannot be more than 360 days from current date";
    
    // Date format used for schedule dates in the application
    private static final String SCHEDULE_DATE_FORMAT = "MM/dd/yyyy HH:mm:ss";
    
    // Maximum number of days allowed from current date
    private static final int MAX_SCHEDULE_DAYS = 360;

    @Override
    public boolean supports(Class<?> aClass) {
        return SocialMasterPostInputMessages.class.equals(aClass);
    }

    @Override
    public void validate(Object o, Errors errors) {
        SocialMasterPostInputMessages masterPostInputMessages = (SocialMasterPostInputMessages) o;

        // Validate schedule dates in the social post list
        if (CollectionUtils.isNotEmpty(masterPostInputMessages.getSocialPostList())) {
            for (SocialPostInputMessageRequest socialPost : masterPostInputMessages.getSocialPostList()) {
                validateScheduleDateLimit(socialPost.getScheduleDate());
            }
        }
        
        // Also validate the master schedule date if present
        validateScheduleDateLimit(masterPostInputMessages.getScheduleDate());
    }

    /**
     * Validates that the schedule date is not more than 360 days from current date.
     * @param scheduleDate the schedule date string to validate
     * @throws BirdeyeSocialException if the schedule date is more than 360 days from current date
     */
    private void validateScheduleDateLimit(String scheduleDate) {
        // If schedule date is null or empty, skip validation (let other validations handle null checks)
        if (StringUtils.isEmpty(scheduleDate)) {
            return;
        }
        
        try {
            // Parse the schedule date string
            SimpleDateFormat dateFormat = new SimpleDateFormat(SCHEDULE_DATE_FORMAT);
            Date scheduledDate = dateFormat.parse(scheduleDate);
            
            // Get current date
            Date currentDate = new Date();
            
            // Calculate the maximum allowed date (current date + MAX_SCHEDULE_DAYS)
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentDate);
            calendar.add(Calendar.DAY_OF_YEAR, MAX_SCHEDULE_DAYS);
            Date maxAllowedDate = calendar.getTime();
            
            // Check if scheduled date is within the allowed range
            if (scheduledDate.after(maxAllowedDate)) {
                throw new SocialBirdeyeException(ErrorCodes.INVALID_SCHEDULED_DATE, SCHEDULE_DATE_LIMIT_MESSAGE);
            }
            
        } catch (ParseException e) {
            // If we can't parse the date, let other validations handle the format issue
            // Don't throw an exception here to avoid masking other validation errors
        } catch (SocialBirdeyeException e) {
            // Re-throw our validation exception
            throw e;
        } catch (Exception e) {
            // In case of unexpected errors, log and continue
            // Don't fail the validation due to unexpected errors
        }
    }

    /**
     * Convenience method to validate if the class is supported and then validate.
     * @param request the request object to validate
     */
    public void checkIfValidateAndValidate(Object request) {
        if (supports(request.getClass())) {
            validate(request, null);
        }
    }
}
