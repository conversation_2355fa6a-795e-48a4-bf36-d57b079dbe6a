
package com.birdeye.social.service;

import com.birdeye.social.dao.EngageFeedDetailsRepo;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.model.FacebookEventRequest;
import com.birdeye.social.model.FbNotification.ReviewerDetails;
import com.birdeye.social.model.notification.Entry;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.util.*;

import static org.mockito.Mockito.*;

public class IFbNotificationServiceTest {

    @InjectMocks
    private FbNotificationServiceImpl fbNotificationService;

    @Mock
    private SocialFBPageRepository socialFbPageRepo;
    @Mock
    private EngageFeedDetailsRepo engageFeedDetailsRepo;
    @Mock
    private KafkaProducerService kafkaProducerService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private FacebookEventRequest createValidEventRequest() {
        com.birdeye.social.model.Value value = new com.birdeye.social.model.Value();
        value.setPost_id("post123");
        value.setItem("POST");
        value.setVerb("add");
        ReviewerDetails from = new ReviewerDetails();
        from.setId("fromUser123");
        value.setFrom(from);
        value.setParent_id("parent123");

        com.birdeye.social.model.Changes changes = new com.birdeye.social.model.Changes();
        changes.setField("feed");
        changes.setValue(value);

        Entry entry = new Entry();
        entry.setId(123L);
        entry.setTime(System.currentTimeMillis());
        entry.setChanges(Collections.singletonList(changes));
        entry.setMessaging(null);

        FacebookEventRequest request = new FacebookEventRequest();
        request.setObject("page");
        request.setEntry(Collections.singletonList(entry));
        request.setEvent("testEvent");
        request.setAddedFromDashboard(false);

        return request;
    }

    @Test
    public void testProcessFbEngageEvent_validEvent() {
        FacebookEventRequest request = createValidEventRequest();
        BusinessFBPage fbPage = new BusinessFBPage();
        fbPage.setFacebookPageId("123");
        fbPage.setBusinessId(1);
        fbPage.setAccountId(2);

        when(socialFbPageRepo.findByFacebookPageIdAndIsValid("123", 1))
                .thenReturn(Collections.singletonList(fbPage));
        when(engageFeedDetailsRepo.findFirstByEngageIdAndTypeAndPageId(anyString(), anyString(), anyString()))
                .thenReturn(null);

        fbNotificationService.processFbEngageEvent(request);

        verify(socialFbPageRepo, atLeastOnce()).findByFacebookPageIdAndIsValid("123", 1);
    }

    @Test
    public void testProcessFbEngageEvent_noFbPageFound() {
        FacebookEventRequest request = createValidEventRequest();
        when(socialFbPageRepo.findByFacebookPageIdAndIsValid("123", 1))
                .thenReturn(Collections.emptyList());

        fbNotificationService.processFbEngageEvent(request);

        verify(socialFbPageRepo).findByFacebookPageIdAndIsValid("123", 1);
    }

    @Test
    public void testProcessFbEngageEvent_exceptionHandling() {
        FacebookEventRequest request = createValidEventRequest();
        when(socialFbPageRepo.findByFacebookPageIdAndIsValid("123", 1))
                .thenThrow(new RuntimeException("DB error"));

        fbNotificationService.processFbEngageEvent(request);

        verify(socialFbPageRepo).findByFacebookPageIdAndIsValid("123", 1);
    }
}