package com.birdeye.social.service;

import org.junit.Before;
import org.junit.Test;
import static org.mockito.Mockito.*;

public class SocialAccountServiceTest {

    private SocialAccountService socialAccountService;

    @Before
    public void setUp() {
        socialAccountService = mock(SocialAccountService.class);
    }

    @Test
    public void testPageRegionSync_WithValidParams() {
        String channel = "facebook";
        Long enterpriseId = 123L;

        // Act
        socialAccountService.pageRegionSync(channel, enterpriseId);

        // Assert
        verify(socialAccountService, times(1)).pageRegionSync(channel, enterpriseId);
    }

    @Test
    public void testPageRegionSync_WithNullEnterpriseId() {
        String channel = "instagram";
        Long enterpriseId = null;

        // Act
        socialAccountService.pageRegionSync(channel, enterpriseId);

        // Assert
        verify(socialAccountService, times(1)).pageRegionSync(channel, enterpriseId);
    }
}