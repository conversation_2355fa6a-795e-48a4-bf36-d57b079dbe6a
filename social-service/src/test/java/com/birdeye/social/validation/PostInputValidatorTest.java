package com.birdeye.social.validation;

import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.model.PostingPageScheduler;
import com.birdeye.social.model.SocialPostInputMessageRequest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.fail;

@RunWith(PowerMockRunner.class)
public class PostInputValidatorTest {

    private PostInputValidator validator;
    private SocialPostInputMessageRequest request;
    private static final String SCHEDULE_DATE_FORMAT = "MM/dd/yyyy HH:mm:ss";

    @Before
    public void setUp() {
        validator = new PostInputValidator();
        request = new SocialPostInputMessageRequest();
        
        // Set up basic valid request
        request.setBusinessId(123);
        request.setPostText("Test post");
        
        // Add posting sites to avoid null validation error
        Map<String, PostingPageScheduler> postingSites = new HashMap<>();
        PostingPageScheduler scheduler = new PostingPageScheduler();
        postingSites.put("facebook", scheduler);
        request.setPostingSites(postingSites);
    }

    @Test
    public void testValidScheduleDate() {
        // Test with a valid schedule date (30 days from now)
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, 30);
        Date futureDate = calendar.getTime();
        
        SimpleDateFormat dateFormat = new SimpleDateFormat(SCHEDULE_DATE_FORMAT);
        String scheduleDateString = dateFormat.format(futureDate);
        request.setScheduleDate(scheduleDateString);
        
        // Should not throw exception
        validator.validate(request, null);
    }

    @Test
    public void testScheduleDateExactly360Days() {
        // Test with schedule date exactly 360 days from now (should be valid)
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, 360);
        Date futureDate = calendar.getTime();
        
        SimpleDateFormat dateFormat = new SimpleDateFormat(SCHEDULE_DATE_FORMAT);
        String scheduleDateString = dateFormat.format(futureDate);
        request.setScheduleDate(scheduleDateString);
        
        // Should not throw exception
        validator.validate(request, null);
    }

    @Test(expected = BirdeyeSocialException.class)
    public void testScheduleDateMoreThan360Days() {
        // Test with schedule date more than 360 days from now (should fail)
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, 361);
        Date futureDate = calendar.getTime();
        
        SimpleDateFormat dateFormat = new SimpleDateFormat(SCHEDULE_DATE_FORMAT);
        String scheduleDateString = dateFormat.format(futureDate);
        request.setScheduleDate(scheduleDateString);
        
        // Should throw BirdeyeSocialException
        validator.validate(request, null);
    }

    @Test
    public void testNullScheduleDate() {
        // Test with null schedule date (should be valid)
        request.setScheduleDate(null);
        
        // Should not throw exception
        validator.validate(request, null);
    }

    @Test
    public void testEmptyScheduleDate() {
        // Test with empty schedule date (should be valid)
        request.setScheduleDate("");
        
        // Should not throw exception
        validator.validate(request, null);
    }

    @Test
    public void testInvalidDateFormat() {
        // Test with invalid date format (should not throw exception for date limit validation)
        request.setScheduleDate("invalid-date-format");
        
        // Should not throw exception for schedule date validation
        // (other validations might handle format issues)
        validator.validate(request, null);
    }

    @Test
    public void testRescheduledActivity() {
        // Test that rescheduled posts skip validation
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, 400); // More than 360 days
        Date futureDate = calendar.getTime();
        
        SimpleDateFormat dateFormat = new SimpleDateFormat(SCHEDULE_DATE_FORMAT);
        String scheduleDateString = dateFormat.format(futureDate);
        request.setScheduleDate(scheduleDateString);
        request.setActivity("rescheduled");
        
        // Should not throw exception because rescheduled posts are skipped
        validator.validate(request, null);
    }
}
