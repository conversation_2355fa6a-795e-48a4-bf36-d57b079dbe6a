package com.birdeye.social.entities;

import com.birdeye.social.aspect.PageRegionSync;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;


@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@Table(name="business_youtube_channel")
@PageRegionSync
public class BusinessYoutubeChannel {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer				id;

    @Column(name = "channel_id")
    private String	channelId;

    @Column(name = "reseller_id")
    private Long resellerId;

    @Column(name = "user_email_id")
    private String emailId;

    @Column(name = "channel_name")
    private String	channelName;

    @Column(name = "channel_description")
    private String	channelDescription;

    @Column(name = "channel_status")
    private String	channelStatus;

    @Column(name = "permissions")
    private String	permissions;

    @Column(name = "profile_image_url")
    private String	pictureUrl;

    @Column(name = "cover_image_url")
    private String	coverImageUrl;

    @Column(name = "enterprise_id")
    private Long enterpriseId;

    @Column(name = "accountId")
    private Integer	 accountId;

    @Column(name = "business_id")
    private Integer	 businessId;

    @Column(name="error_log")
    private String errorLog;

    @Column(name = "validity_type")
    private Integer validType;

    @Column(name = "is_verified")
    private Integer	isVerified;

    // If is_selected == 1, this page in UI will be shown as "selected" in a checkbox
    // Refer - https://birdeye.atlassian.net/browse/BIRDEYE-22168
    @Column(name = "is_selected")
    private Integer	isSelected;

    @Column(name = "is_valid")
    private Integer	isValid;

    @Column(name = "can_post")
    private Integer canPost = 1;

    @Column(name = "refresh_token_id")
    private Integer	refreshTokenId;


    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at")
    private Date		createdAt;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at")
    private Date		updatedAt;

    @Column(name= "request_id")
    private String requestId;

    @Column(name="created_by")
    private Integer createdBy;

    @Column(name="updated_by")
    private Integer updatedBy;

    @Column(name="country_code")
    private String countryCode;

    @Column(name = "channel_link")
    private String channelLink;

    @Column(name = "refresh_data_sync_date")
    private Date refreshDataSyncDate;

    @Column(name = "engage_sync_date")
    private Date engageSyncDate;

    @Column(name = "next_sync_date") //report sync date
    private Date nextSyncDate;
}
