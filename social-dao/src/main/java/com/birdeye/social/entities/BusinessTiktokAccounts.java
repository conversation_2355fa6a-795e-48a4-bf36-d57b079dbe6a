package com.birdeye.social.entities;

import com.birdeye.social.aspect.PageRegionSync;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.Date;

@Entity
@Table(name = "business_tiktok_accounts")
@PageRegionSync
public class BusinessTiktokAccounts {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "profile_id")
    @Size(max = 20)
    private String profileId;

    @Column(name = "enterprise_id")
    private Long enterpriseId;

    @Column(name = "account_id")
    private Integer	accountId;

    @Column(name = "business_id")
    private Integer	businessId;

    @Column(name = "reseller_id")
    private Long resellerId;

    @Column(name = "profile_name")
    private String profileName;

    @Column(name = "profile_username")
    private String profileUsername;

    @Column(name = "profile_url")
    private String profileUrl;

    @Column(name = "is_verified")
    private Integer	isVerified;

    @Column(name = "is_valid")
    private Integer	isValid;

    @Column(name = "can_post")
    private Integer canPost;

    @Column(name = "validity_type")
    private Integer validType;

    @Column(name = "is_selected")
    private Integer	isSelected;

    @Column(name = "refresh_token")
    private String refreshToken;

    @Column(name = "profile_image_url")
    private String profileImageUrl;

    @Column(name = "request_id")
    private String requestId;

    @Column(name = "scopes")
    private String scope;

    @Column(name = "created_by")
    private Integer createdBy;

    @Column(name = "updated_by")
    private Integer updatedBy;

    @Column(name = "user_email_id")
    private String userEmailId;


    @Column(name = "expires_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date expiresOn;

    @Column(name = "last_scanned_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastScannedOn;

    @Column(name = "next_sync_date")
    private Date nextSyncDate;

    public Date getNextSyncDate() {
        return nextSyncDate;
    }

    public void setNextSyncDate(Date nextSyncDate) {
        this.nextSyncDate = nextSyncDate;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getProfileId() {
        return profileId;
    }

    public void setProfileId(String profileId) {
        this.profileId = profileId;
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public Long getResellerId() {
        return resellerId;
    }

    public void setResellerId(Long resellerId) {
        this.resellerId = resellerId;
    }

    public String getProfileName() {
        return profileName;
    }

    public void setProfileName(String profileName) {
        this.profileName = profileName;
    }

    public String getProfileUsername() {
        return profileUsername;
    }

    public void setProfileUsername(String profileUsername) {
        this.profileUsername = profileUsername;
    }

    public String getProfileUrl() {
        return profileUrl;
    }

    public void setProfileUrl(String profileUrl) {
        this.profileUrl = profileUrl;
    }

    public Integer getIsVerified() {
        return isVerified;
    }

    public void setIsVerified(Integer isVerified) {
        this.isVerified = isVerified;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Integer getValidType() {
        return validType;
    }

    public void setValidType(Integer validType) {
        this.validType = validType;
    }

    public Integer getIsSelected() {
        return isSelected;
    }

    public void setIsSelected(Integer isSelected) {
        this.isSelected = isSelected;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public String getProfileImageUrl() {
        return profileImageUrl;
    }

    public void setProfileImageUrl(String profileImageUrl) {
        this.profileImageUrl = profileImageUrl;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUserEmailId() {
        return userEmailId;
    }

    public void setUserEmailId(String userEmailId) {
        this.userEmailId = userEmailId;
    }

    public Date getExpiresOn() {
        return expiresOn;
    }

    public void setExpiresOn(Date expiresOn) {
        this.expiresOn = expiresOn;
    }

    public Date getLastScannedOn() {
        return lastScannedOn;
    }

    public void setLastScannedOn(Date lastScannedOn) {
        this.lastScannedOn = lastScannedOn;
    }

    public Integer getCanPost() {
        return canPost;
    }

    public void setCanPost(Integer canPost) {
        this.canPost = canPost;
    }

    @Override
    public String toString() {
        return "BusinessTiktokAccounts{" +
                "id=" + id +
                ", pageId='" + profileId + '\'' +
                ", enterpriseId=" + enterpriseId +
                ", accountId=" + accountId +
                ", businessId=" + businessId +
                ", resellerId=" + resellerId +
                ", pageName='" + profileName + '\'' +
                ", pageUsername='" + profileUsername + '\'' +
                ", pageUrl='" + profileUrl + '\'' +
                ", isVerified=" + isVerified +
                ", isValid=" + isValid +
                ", validType=" + validType +
                ", isSelected=" + isSelected +
                ", refreshToken='" + refreshToken + '\'' +
                ", pageImageUrl='" + profileImageUrl + '\'' +
                ", requestId='" + requestId + '\'' +
                ", scope='" + scope + '\'' +
                ", createdBy=" + createdBy +
                ", updatedBy=" + updatedBy +
                ", userEmailId='" + userEmailId + '\'' +
                ", expiresOn=" + expiresOn +
                ", lastScannedOn=" + lastScannedOn +
                '}';
    }
}
