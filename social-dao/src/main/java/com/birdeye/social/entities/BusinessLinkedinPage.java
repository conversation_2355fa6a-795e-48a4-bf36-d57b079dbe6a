package com.birdeye.social.entities;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.*;
import javax.validation.constraints.Size;

import com.birdeye.social.aspect.PageRegionSync;
import com.birdeye.social.scheduler.dto.ExternalIntegration;

@Entity
@Table(name = "business_linkedin_page")
@PageRegionSync
public class BusinessLinkedinPage implements Serializable, ExternalIntegration {
	
	private static final long serialVersionUID = -3577144936559185841L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(name = "page_type")
	private String pageType; // profile or company
	
	@Column(name = "enterprise_id")
	private Long enterpriseId;

	@Column(name = "accountId")
	private Integer	 accountId;

	@Column(name = "business_id")
	private Integer businessId;
	
	@Column(name = "access_token")
	@Size(max = 1000)
	private String accessToken;
	
	@Column(name = "is_valid")
	private Integer isValid = 1;

	@Column(name = "can_post")
	private Integer canPost = 1;

	@Column(name = "validity_type")
	private Integer validType;

	@Column(name = "is_selected")
	private Integer isSelected = 0;
	
	@Column(name = "error_log")
	private String errorLog;

	@Column(name="linkedin_error_code")
	private Integer linkedinErrorCode; // 1 = unauthorized access token expired , 2 = Not enough permissions
	
	@Column(name = "enabled")
	private Integer enabled = 0;
	
	@Column(name = "company_name")
	private String companyName;
	
	@Column(name = "urn")
	private String urn;

	@Column(name = "company_id")
	private Integer companyId;
	
	@Column(name = "expires_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date expiresOn;
	
	@Column(name = "first_name")
	private String firstName;
	
	@Column(name = "last_name")
	private String lastName;
	
	@Column(name="profile_id")
	private String profileId;
	
	@Column(name = "last_scanned_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date 	lastScannedOn;

	@Column(name = "scope")
	private String scope;

	@Column(name = "business_get_page_id")
	private String businessGetPageId;

	@Column(name="page_url")
	private String pageUrl;

	@Column(name="created_by")
	private Integer createdBy;

	@Column(name="updated_by")
	private Integer updatedBy;

	@Column(name = "refresh_token")
	private Integer refreshToken;

	@Column(name = "next_sync_date")
	private Date nextSyncDate;

	@Column(name = "logo_url")
	private String logoUrl;

	@Column(name = "vanity_name")
	private String vanityName;


	@Column(name = "event_subscription_expiry")
	@Temporal(TemporalType.TIMESTAMP)
	private Date eventExpiryDate;

	@Column(name = "person_urn")
	private String personUrn;

	@Column(name = "module_impacted")
	private String moduleImpacted;

	@Column(name = "role")
	private String role;

	@Column(name = "invalid_type")
	private String invalidType;

	@Column(name = "reseller_id")
	private Long resellerId;

	@Column(name = "user_email_id")
	private String userEmailId;

	public Integer getCanPost() {
		return canPost;
	}

	public void setCanPost(Integer canPost) {
		this.canPost = canPost;
	}

	public String getRole() {
		return role;
	}

	public void setRole(String role) {
		this.role = role;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getPageType() {
		return pageType;
	}

	public void setPageType(String pageType) {
		this.pageType = pageType;
	}

	public Long getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public Integer getBusinessId() {
		return businessId;
	}

	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Integer getIsSelected() {
		return isSelected;
	}

	public void setIsSelected(Integer isSelected) {
		this.isSelected = isSelected;
	}

	public String getErrorLog() {
		return errorLog;
	}

	public void setErrorLog(String errorLog) {
		this.errorLog = errorLog;
	}

	public Integer getEnabled() {
		return enabled;
	}

	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getUrn() {
		return urn;
	}

	public void setUrn(String urn) {
		this.urn = urn;
	}

	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

	public Date getExpiresOn() {
		return expiresOn;
	}

	public void setExpiresOn(Date expiresOn) {
		this.expiresOn = expiresOn;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getProfileId() {
		return profileId;
	}

	public void setProfileId(String profileId) {
		this.profileId = profileId;
	}

	public String getScope() {
		return scope;
	}

	public void setScope(String scope) {
		this.scope = scope;
	}

	public String getBusinessGetPageId() {
		return businessGetPageId;
	}

	public void setBusinessGetPageId(String businessGetPageId) {
		this.businessGetPageId = businessGetPageId;
	}

	public String getPageUrl() {
		return pageUrl;
	}

	public void setPageUrl(String pageUrl) {
		this.pageUrl = pageUrl;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Integer getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Integer getLinkedinErrorCode() {
		return linkedinErrorCode;
	}

	public void setLinkedinErrorCode(Integer linkedinErrorCode) {
		this.linkedinErrorCode = linkedinErrorCode;
	}

	public Integer getRefreshToken() {
		return refreshToken;
	}

	public void setRefreshToken(Integer refreshToken) {
		this.refreshToken = refreshToken;
	}


	public Date getNextSyncDate() {
		return nextSyncDate;
	}

	public void setNextSyncDate(Date nextSyncDate) {
		this.nextSyncDate = nextSyncDate;
	}

	public Date getEventExpiryDate() {
		return eventExpiryDate;
	}

	public void setEventExpiryDate(Date eventExpiryDate) {
		this.eventExpiryDate = eventExpiryDate;
	}

	public String getPersonUrn() {
		return personUrn;
	}

	public void setPersonUrn(String personUrn) {
		this.personUrn = personUrn;
	}


	public BusinessLinkedinPage(){
		
	}
	
	public String getLogoUrl() {
		return logoUrl;
	}

	public void setLogoUrl(String logoUrl) {
		this.logoUrl = logoUrl;
	}

	public String getVanityName() {
		return vanityName;
	}

	public void setVanityName(String vanityName) {
		this.vanityName = vanityName;
	}

	public Integer getAccountId() {
		return accountId;
	}

	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}

	public Long getResellerId() {
		return resellerId;
	}

	public void setResellerId(Long resellerId) {
		this.resellerId = resellerId;
	}

	public String getUserEmailId() {
		return userEmailId;
	}

	public void setUserEmailId(String userEmailId) {
		this.userEmailId = userEmailId;
	}

	public Integer getValidType() {
		return validType;
	}

	public void setValidType(Integer validType) {
		this.validType = validType;
	}

	public BusinessLinkedinPage(BusinessLinkedinPage linkedInPage){
		this.pageType = linkedInPage.getPageType();
		this.accessToken = linkedInPage.getAccessToken();
		this.enterpriseId = linkedInPage.getEnterpriseId();
		this.accountId = linkedInPage.accountId;
		this.businessId = linkedInPage.getBusinessId();
		this.expiresOn = linkedInPage.getExpiresOn();
		this.enabled = linkedInPage.getEnabled();
		this.isValid = linkedInPage.getIsValid();
		this.businessId = linkedInPage.getBusinessId();
		this.firstName = linkedInPage.getFirstName();
		this.lastName = linkedInPage.getLastName();
		this.profileId = linkedInPage.getProfileId();
		this.urn = linkedInPage.getUrn();
		this.companyName = linkedInPage.getCompanyName();
		this.scope = linkedInPage.getScope();
		this.pageUrl = linkedInPage.getPageUrl();
		this.createdBy = linkedInPage.getCreatedBy();
		this.updatedBy = linkedInPage.getUpdatedBy();
		this.linkedinErrorCode = linkedInPage.getLinkedinErrorCode();
		this.nextSyncDate=linkedInPage.getNextSyncDate();
		this.eventExpiryDate=linkedInPage.getEventExpiryDate();
		this.personUrn=linkedInPage.getPersonUrn();
		this.logoUrl=linkedInPage.getLogoUrl();
		this.vanityName=linkedInPage.getVanityName();
		this.resellerId=linkedInPage.getResellerId();
		this.userEmailId=linkedInPage.getUserEmailId();
	}
	
	public Date getLastScannedOn() {
		return lastScannedOn;
	}
	
	public void setLastScannedOn(Date lastScannedOn) {
		this.lastScannedOn = lastScannedOn;
	}

	public String getModuleImpacted() {
		return moduleImpacted;
	}

	public void setModuleImpacted(String moduleImpacted) {
		this.moduleImpacted = moduleImpacted;
	}

	public String getInvalidType() {
		return invalidType;
	}

	public void setInvalidType(String invalidType) {
		this.invalidType = invalidType;
	}

	@Override
	public String toString() {
		return "BusinessLinkedinPage{" +
				"id=" + id +
				", pageType='" + pageType + '\'' +
				", enterpriseId=" + enterpriseId +
				", accountId=" + accountId +
				", businessId=" + businessId +
				", accessToken='" + accessToken + '\'' +
				", isValid=" + isValid +
				", isSelected=" + isSelected +
				", errorLog='" + errorLog + '\'' +
				", linkedinErrorCode=" + linkedinErrorCode +
				", enabled=" + enabled +
				", companyName='" + companyName + '\'' +
				", urn='" + urn + '\'' +
				", companyId=" + companyId +
				", expiresOn=" + expiresOn +
				", firstName='" + firstName + '\'' +
				", lastName='" + lastName + '\'' +
				", profileId='" + profileId + '\'' +
				", lastScannedOn=" + lastScannedOn +
				", scope='" + scope + '\'' +
				", businessGetPageId='" + businessGetPageId + '\'' +
				", pageUrl='" + pageUrl + '\'' +
				", createdBy=" + createdBy +
				", updatedBy=" + updatedBy +
				", refreshToken=" + refreshToken +
				", nextSyncDate=" + nextSyncDate +
				", logoUrl='" + logoUrl + '\'' +
				", vanityName='" + vanityName + '\'' +
				", eventExpiryDate=" + eventExpiryDate +
				", personUrn='" + personUrn + '\'' +
				", moduleImpacted='" + moduleImpacted + '\'' +
				", role='" + role + '\'' +
				", invalidType='" + invalidType + '\'' +
				", resellerId='" + resellerId + '\'' +
				", userEmailId='" + userEmailId + '\'' +
				'}';
	}
}
