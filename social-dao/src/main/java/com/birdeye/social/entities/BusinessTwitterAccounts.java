package com.birdeye.social.entities;

import com.birdeye.social.aspect.PageRegionSync;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.elasticdto.SocialElasticDto;
import com.birdeye.social.elasticdto.builder.SocialElasticDtoBuilder;
import com.birdeye.social.scheduler.dto.ExternalIntegration;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "business_twitter_accounts")
@PageRegionSync
public class BusinessTwitterAccounts implements Serializable,ExternalIntegration {

	private static final long serialVersionUID = 6753017155628287006L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(name = "enterprise_id")
	private Long enterpriseId;

	@Column(name = "accountId")
	private Integer	 accountId;

	@Column(name = "business_id")
	private Integer	 businessId;

	@Column(name = "access_token")
	@Size(max = 255)
	private String accessToken;

	@Column(name = "access_secret")
	@Size(max = 255)
	private String accessSecret;

	@Column(name = "profile_id")
	private Long profileId;

	@Column(name = "profile_url")
	private String profileUrl;

	@Column(name = "profile_picture_url")
	private String profilePicUrl;

	@Column(name = "handle")
	private String handle;

	@Column(name = "name")
	private String name;

	@Column(name = "is_selected")
	private Integer isSelected;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_at")
	private Date createdAt;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_at")
	private Date updatedAt;

	@Column(name = "request_id")
	private String requestId;

	@Column(name = "is_valid")
	private Integer isValid = 1;

	@Column(name = "can_post")
	private Integer canPost = 1;

	@Column(name = "validity_type")
	private Integer validType;

	@Column(name = "invalid_type")
	private String invalidType;
	
	@Column(name = "location")
	private String location;
	
	@Column(name="created_by")
	private Integer createdBy;
	
	@Column(name="updated_by")
	private Integer updatedBy;

	@Column(name = "enabled")
	private Integer enabled = 0;

	@Column(name="account_permissions")
	private String accountPermissions;
	
	@Column(name = "max_post_allowed")
	private Integer maxPostAllowed = 3;
	
	@Column(name = "min_rating")
	private Integer minRating = 4;
	
	@Column(name = "last_scanned_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date				lastScannedOn;

	@Column(name = "next_sync_date")
	private Date nextSyncDate;

	@Column(name = "reseller_id")
	private Long resellerId;

	@Column(name = "user_email_id")
	private String userEmailId;


	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Long getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public String getAccessSecret() {
		return accessSecret;
	}

	public void setAccessSecret(String accessSecret) {
		this.accessSecret = accessSecret;
	}

	public Long getProfileId() {
		return profileId;
	}

	public void setProfileId(Long profileId) {
		this.profileId = profileId;
	}

	public String getProfileUrl() {
		return profileUrl;
	}

	public void setProfileUrl(String profileUrl) {
		this.profileUrl = profileUrl;
	}

	public String getProfilePicUrl() {
		return profilePicUrl;
	}

	public void setProfilePicUrl(String profilePicUrl) {
		this.profilePicUrl = profilePicUrl;
	}

	public String getHandle() {
		return handle;
	}

	public void setHandle(String handle) {
		this.handle = handle;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getAccountId() {
		return accountId;
	}

	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}

	public Integer getIsSelected() {
		return isSelected;
	}

	public void setIsSelected(Integer isSelected) {
		this.isSelected = isSelected;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public String getRequestId() {
		return requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public String getInvalidType() {
		return invalidType;
	}

	public void setInvalidType(String invalidType) {
		this.invalidType = invalidType;
	}

	/**
	 * @return the location
	 */
	public String getLocation() {
		return location;
	}

	/**
	 * @param location the location to set
	 */
	public void setLocation(String location) {
		this.location = location;
	}

	/**
	 * @return the createdBy
	 */
	public Integer getCreatedBy() {
		return createdBy;
	}

	/**
	 * @param createdBy the createdBy to set
	 */
	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	/**
	 * @return the updatedBy
	 */
	public Integer getUpdatedBy() {
		return updatedBy;
	}

	/**
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Integer getBusinessId() {
		return businessId;
	}

	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}

	public Integer getEnabled() {
		return enabled;
	}

	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}
	
	public Integer getMaxPostAllowed() {
		return maxPostAllowed;
	}

	public void setMaxPostAllowed(Integer maxPostAllowed) {
		this.maxPostAllowed = maxPostAllowed;
	}
	
	public Integer getMinRating() {
		return minRating;
	}

	public void setMinRating(Integer minRating) {
		this.minRating = minRating;
	}
	
	public Date getLastScannedOn() {
		return lastScannedOn;
	}
	
	public void setLastScannedOn(Date lastScannedOn) {
		this.lastScannedOn = lastScannedOn;
	}

	public Date getNextSyncDate() {
		return nextSyncDate;
	}

	public void setNextSyncDate(Date nextSyncDate) {
		this.nextSyncDate = nextSyncDate;
	}

	public String getAccountPermissions() {
		return accountPermissions;
	}

	public void setAccountPermissions(String accountPermissions) {
		this.accountPermissions = accountPermissions;
	}

	public Long getResellerId() {
		return resellerId;
	}

	public void setResellerId(Long resellerId) {
		this.resellerId = resellerId;
	}

	public String getUserEmailId() {
		return userEmailId;
	}

	public void setUserEmailId(String userEmailId) {
		this.userEmailId = userEmailId;
	}

	public Integer getValidType() {
		return validType;
	}

	public void setValidType(Integer validType) { this.validType = validType; }

	public Integer getCanPost() {
		return canPost;
	}

	public void setCanPost(Integer canPost) {
		this.canPost = canPost;
	}

	@Override
	public String toString() {
		return "BusinessTwitterAccounts [id=" + id + ", enterpriseId=" + enterpriseId + ", accountId=" + accountId
				+ ", businessId=" + businessId + ", accessToken=" + accessToken + ", accessSecret=" + accessSecret
				+ ", profileId=" + profileId + ", profileUrl=" + profileUrl + ", profilePicUrl=" + profilePicUrl
				+ ", handle=" + handle + ", name=" + name + ", isSelected=" + isSelected + ", createdAt=" + createdAt
				+ ", updatedAt=" + updatedAt + ", requestId=" + requestId + ", isValid=" + isValid + ", invalidType="
				+ invalidType + ", location=" + location + ", createdBy=" + createdBy + ", updatedBy=" + updatedBy
				+ ", enabled=" + enabled + ", accountPermissions=" + accountPermissions + ", maxPostAllowed="
				+ maxPostAllowed + ", minRating=" + minRating + ", lastScannedOn=" + lastScannedOn + ", nextSyncDate="
				+ nextSyncDate + ", resellerId=" + resellerId + ", userEmailId=" + userEmailId + "]";
	}

	public SocialElasticDto getElasticDto(){
		return new SocialElasticDtoBuilder()
				.with($ -> {
					$.channel = SocialChannel.TWITTER.getName();
					$.integration_id=this.profileId.toString();
					$.is_valid=this.isValid;
					$.enterprise_id=this.enterpriseId!=null?this.enterpriseId.toString():null;
					$.reseller_id=this.resellerId!=null?this.resellerId.toString():null;
					$.page_name=this.name;
					$.page_url= this.profileUrl;
					$.raw_id= this.id;
					$.handle= this.handle;
					$.is_selected= this.isSelected;
					$.fullName= this.name;
					$.business_id= this.getBusinessId()!=null?this.getBusinessId().toString():null;
				}).build();
	}

}
