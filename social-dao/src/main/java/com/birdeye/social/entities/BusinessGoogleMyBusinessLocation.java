/**
 * 
 */
package com.birdeye.social.entities;

import com.birdeye.social.aspect.PageRegionSync;
import com.birdeye.social.aspect.SocialEsSync;
import com.birdeye.social.constant.GMBLocationJobStatus;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name="business_google_mybusiness_location")
@PageRegionSync
public class BusinessGoogleMyBusinessLocation implements Serializable {

	private static final long serialVersionUID = -8753658016142218573L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer				id;
	
	@Column(name = "account_id")
	private String	accountId;

	@Column(name = "reseller_id")
	private Long resellerId;

	@Column(name = "user_email_id")
	private String emailId;

	@Column(name = "account_name")
	private String	accountName;
	
	@Column(name = "account_type")
	private String	accountType;
	
	@Column(name = "account_status")
	private String	accountStatus;
	
	@Column(name = "location_id")
	private String	locationId;
	
	@Column(name = "location_name")
	private String	locationName;
	
	@Column(name = "location_url")
	private String	locationUrl;
	
	@Column(name = "map_url")
	private String	locationMapUrl;
	
	@Column(name = "website_url")
	private String	websiteUrl;
	
	@Column(name = "primary_phone")
	private String	primaryPhone;
	
	@Column(name = "single_line_address")
	private String	singleLineAddress;
	
	@Column(name = "location_state")
	private String	locationState;
	
	@Column(name = "service_area")
	private String	serviceArea;
	
	@Column(name = "gplus_id")
	private String	googlePlusId;

	@Column(name = "place_id")
	private String	placeId;
	
	@Column(name = "is_verified")
	private Integer	isVerified;

	@Column(name = "is_messaging_enabled")
	private Integer	isMessagingEnabled;

	@Column(name = "permissions")
	private String	permissions;
	
	@Column(name = "profile_image_url")
	private String	pictureUrl;
	
	@Column(name = "cover_image_url")
	private String	coverImageUrl;
	
	@Column(name = "enterprise_id")
	private Long enterpriseId;

	@Column(name = "accountId")
	private Integer	 shortAccountId;

	@Column(name = "business_id")
	private Integer	 businessId;

	@Column(name="error_log")
	private String errorLog;
	
	@Column(name = "google_user_id")
	private String	userId;
	
	@Column(name = "google_user_name")
	private String	userName;

	@Column(name = "validity_type")
	private Integer validType;

	// If is_selected == 1, this page in UI will be shown as "selected" in a checkbox
	// Refer - https://birdeye.atlassian.net/browse/BIRDEYE-22168
	@Column(name = "is_selected")
	private Integer	isSelected;
	
	@Column(name = "is_valid")
	private Integer	isValid;

	@Column(name = "can_post")
	private Integer canPost = 1;
	
	@Column(name = "refresh_token_id")
	private Integer	refreshTokenId;
	
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_at")
	private Date		createdAt;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_at")
	private Date		updatedAt;
	
	@Column(name= "request_id")
	private String requestId;
	
	@Column(name="created_by")
	private Integer createdBy;
	
	@Column(name="updated_by")
	private Integer updatedBy;

    @Enumerated(EnumType.STRING)
	@Column(name = "sync_status")
	private GMBLocationJobStatus	locationSyncStatus;
	
	@Column(name = "next_sync_date")
	private Date	nextSync;

	@Column(name = "gmsg_location_status")
	private String	gMsgLocationStatus;

	@Column(name = "gmsg_location_comment")
	private String	gMsgLocationComment;

	@Column(name = "gmsg_location_name")
	private String	gMsgLocationName;

	@Column(name = "gmb_acc_id")
	private Integer gmbAccountId; // null

	@Column(name = "report_next_sync_date")
	private Date	reportNextSyncDate;

	@Column(name = "agent_id")
	private Integer	agentId;

	public Integer getCanPost() {
		return canPost;
	}

	public void setCanPost(Integer canPost) {
		this.canPost = canPost;
	}

	public String getgMsgLocationStatus() {
		return gMsgLocationStatus;
	}

	public void setgMsgLocationStatus(String gMsgLocationStatus) {
		this.gMsgLocationStatus = gMsgLocationStatus;
	}

	public String getgMsgLocationComment() {
		return gMsgLocationComment;
	}

	public void setgMsgLocationComment(String gMsgLocationComment) {
		this.gMsgLocationComment = gMsgLocationComment;
	}

	public String getgMsgLocationName() {
		return gMsgLocationName;
	}

	public void setgMsgLocationName(String gMsgLocationName) {
		this.gMsgLocationName = gMsgLocationName;
	}

	public Integer getIsMessagingEnabled() {
		return isMessagingEnabled;
	}

	public void setIsMessagingEnabled(Integer isMessagingEnabled) {
		this.isMessagingEnabled = isMessagingEnabled;
	}

	public Long getResellerId() {
		return resellerId;
	}

	public void setResellerId(Long resellerId) {
		this.resellerId = resellerId;
	}

	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}


	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	public Integer getValidType() {
		return validType;
	}

	public void setValidType(Integer validType) {
		this.validType = validType;
	}

	/**
	 * @return the singleLineAddress
	 */
	public String getSingleLineAddress() {
		return singleLineAddress;
	}

	/**
	 * @param singleLineAddress
	 *            the singleLineAddress to set
	 */
	public void setSingleLineAddress(String singleLineAddress) {
		this.singleLineAddress = singleLineAddress;
	}

	/**
	 * @return the createdAt
	 */
	public Date getCreatedAt() {
		return createdAt;
	}

	/**
	 * @param createdAt
	 *            the createdAt to set
	 */
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	/**
	 * @return the updatedAt
	 */
	public Date getUpdatedAt() {
		return updatedAt;
	}

	/**
	 * @param updatedAt
	 *            the updatedAt to set
	 */
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	/**
	 * @return the enterpriseId
	 */
	public Long getEnterpriseId() {
		return enterpriseId;
	}

	/**
	 * @param enterpriseId
	 *            the enterpriseId to set
	 */
	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	/**
	 * @return the isSelected
	 */
	public Integer getIsSelected() {
		return isSelected;
	}

	/**
	 * @param isSelected
	 *            the isSelected to set
	 */
	public void setIsSelected(Integer isSelected) {
		this.isSelected = isSelected;
	}

	/**
	 * @return the isValid
	 */
	public Integer getIsValid() {
		return isValid;
	}

	/**
	 * @param isValid
	 *            the isValid to set
	 */
	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}


	/**
	 * @return the pictureUrl
	 */
	public String getPictureUrl() {
		return pictureUrl;
	}

	/**
	 * @param pictureUrl
	 *            the pictureUrl to set
	 */
	public void setPictureUrl(String pictureUrl) {
		this.pictureUrl = pictureUrl;
	}

	/**
	 * @return the accountId
	 */
	public String getAccountId() {
		return accountId;
	}

	/**
	 * @param accountId the accountId to set
	 */
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	/**
	 * @return the accountName
	 */
	public String getAccountName() {
		return accountName;
	}

	/**
	 * @param accountName the accountName to set
	 */
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	/**
	 * @return the locationId
	 */
	public String getLocationId() {
		return locationId;
	}

	/**
	 * @param locationId the locationId to set
	 */
	public void setLocationId(String locationId) {
		this.locationId = locationId;
	}

	/**
	 * @return the locationName
	 */
	public String getLocationName() {
		return locationName;
	}

	/**
	 * @param locationName the locationName to set
	 */
	public void setLocationName(String locationName) {
		this.locationName = locationName;
	}

	/**
	 * @return the locationUrl
	 */
	public String getLocationUrl() {
		return locationUrl;
	}

	/**
	 * @param locationUrl the locationUrl to set
	 */
	public void setLocationUrl(String locationUrl) {
		this.locationUrl = locationUrl;
	}


	/**
	 * @return the googlePlusId
	 */
	public String getGooglePlusId() {
		return googlePlusId;
	}

	/**
	 * @param googlePlusId the googlePlusId to set
	 */
	public void setGooglePlusId(String googlePlusId) {
		this.googlePlusId = googlePlusId;
	}

	/**
	 * @return the placeId
	 */
	public String getPlaceId() {
		return placeId;
	}

	/**
	 * @param placeId the placeId to set
	 */
	public void setPlaceId(String placeId) {
		this.placeId = placeId;
	}

	/**
	 * @return the coverImageUrl
	 */
	public String getCoverImageUrl() {
		return coverImageUrl;
	}

	/**
	 * @param coverImageUrl the coverImageUrl to set
	 */
	public void setCoverImageUrl(String coverImageUrl) {
		this.coverImageUrl = coverImageUrl;
	}

	/**
	 * @return the userId
	 */
	public String getUserId() {
		return userId;
	}

	/**
	 * @param userId the userId to set
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}

	/**
	 * @return the websiteUrl
	 */
	public String getWebsiteUrl() {
		return websiteUrl;
	}

	/**
	 * @param websiteUrl the websiteUrl to set
	 */
	public void setWebsiteUrl(String websiteUrl) {
		this.websiteUrl = websiteUrl;
	}

	/**
	 * @return the locationState
	 */
	public String getLocationState() {
		return locationState;
	}

	/**
	 * @param locationState the locationState to set
	 */
	public void setLocationState(String locationState) {
		this.locationState = locationState;
	}

	/**
	 * @return the userName
	 */
	public String getUserName() {
		return userName;
	}

	/**
	 * @param userName the userName to set
	 */
	public void setUserName(String userName) {
		this.userName = userName;
	}

	/**
	 * @return the refreshTokenId
	 */
	public Integer getRefreshTokenId() {
		return refreshTokenId;
	}

	public String getServiceArea() {
		return serviceArea;
	}

	public void setServiceArea(String serviceArea) {
		this.serviceArea = serviceArea;
	}

	/**
	 * @param refreshTokenId the refreshTokenId to set
	 */
	public void setRefreshTokenId(Integer refreshTokenId) {
		this.refreshTokenId = refreshTokenId;
	}

	/**
	 * @return the locationMapUrl
	 */
	public String getLocationMapUrl() {
		return locationMapUrl;
	}

	/**
	 * @param locationMapUrl the locationMapUrl to set
	 */
	public void setLocationMapUrl(String locationMapUrl) {
		this.locationMapUrl = locationMapUrl;
	}

	/**
	 * @return the primaryPhone
	 */
	public String getPrimaryPhone() {
		return primaryPhone;
	}

	/**
	 * @param primaryPhone the primaryPhone to set
	 */
	public void setPrimaryPhone(String primaryPhone) {
		this.primaryPhone = primaryPhone;
	}

	/**
	 * @return the isVerified
	 */
	public Integer getIsVerified() {
		return isVerified;
	}

	/**
	 * @param isVerified the isVerified to set
	 */
	public void setIsVerified(Integer isVerified) {
		this.isVerified = isVerified;
	}

	/**
	 * @return the accountType
	 */
	public String getAccountType() {
		return accountType;
	}

	/**
	 * @param accountType the accountType to set
	 */
	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}

	/**
	 * @return the accountStatus
	 */
	public String getAccountStatus() {
		return accountStatus;
	}

	/**
	 * @param accountStatus the accountStatus to set
	 */
	public void setAccountStatus(String accountStatus) {
		this.accountStatus = accountStatus;
	}

	/**
	 * @return the requestId
	 */
	public String getRequestId() {
		return requestId;
	}

	/**
	 * @param requestId the requestId to set
	 */
	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}

	public String getPermissions() {
		return permissions;
	}

	public void setPermissions(String permissions) {
		this.permissions = permissions;
	}

	/**
	 * @return the createdBy
	 */
	public Integer getCreatedBy() {
		return createdBy;
	}

	/**
	 * @param createdBy the createdBy to set
	 */
	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	/**
	 * @return the updatedBy
	 */
	public Integer getUpdatedBy() {
		return updatedBy;
	}

	/**
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}


	public GMBLocationJobStatus getLocationSyncStatus() {
		return locationSyncStatus;
	}

	public void setLocationSyncStatus(GMBLocationJobStatus locationCheckStatus) {
		this.locationSyncStatus = locationCheckStatus;
	}

	public Date getNextSync() {
		return nextSync;
	}

	public void setNextSync(Date date) {
		this.nextSync = date;
	}

	public Integer getBusinessId() {
		return businessId;
	}

	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}

	public String getErrorLog() {
		return errorLog;
	}

	public void setErrorLog(String errorLog) {
		this.errorLog = errorLog;
	}

	public Integer getGmbAccountId() {
		return gmbAccountId;
	}

	public void setGmbAccountId(Integer gmbAccountId) {
		this.gmbAccountId = gmbAccountId;
	}

	public Date getReportNextSyncDate() {
		return reportNextSyncDate;
	}

	public void setReportNextSyncDate(Date reportNextSyncDate) {
		this.reportNextSyncDate = reportNextSyncDate;
	}

	public Integer getAgentId() {
		return agentId;
	}

	public void setAgentId(Integer agentId) {
		this.agentId = agentId;
	}

	public Integer getShortAccountId() {
		return shortAccountId;
	}

	public void setShortAccountId(Integer shortAccountId) {
		this.shortAccountId = shortAccountId;
	}

	@Override
	public String toString() {
		return "BusinessGoogleMyBusinessLocation{" +
				"id=" + id +
				", accountId='" + accountId + '\'' +
				", resellerId=" + resellerId +
				", emailId='" + emailId + '\'' +
				", accountName='" + accountName + '\'' +
				", accountType='" + accountType + '\'' +
				", accountStatus='" + accountStatus + '\'' +
				", locationId='" + locationId + '\'' +
				", locationName='" + locationName + '\'' +
				", locationUrl='" + locationUrl + '\'' +
				", locationMapUrl='" + locationMapUrl + '\'' +
				", websiteUrl='" + websiteUrl + '\'' +
				", primaryPhone='" + primaryPhone + '\'' +
				", singleLineAddress='" + singleLineAddress + '\'' +
				", locationState='" + locationState + '\'' +
				", serviceArea='" + serviceArea + '\'' +
				", googlePlusId='" + googlePlusId + '\'' +
				", placeId='" + placeId + '\'' +
				", isVerified=" + isVerified +
				", isMessagingEnabled=" + isMessagingEnabled +
				", permissions='" + permissions + '\'' +
				", pictureUrl='" + pictureUrl + '\'' +
				", coverImageUrl='" + coverImageUrl + '\'' +
				", enterpriseId=" + enterpriseId +
				", shortAccountId=" + shortAccountId +
				", businessId=" + businessId +
				", errorLog='" + errorLog + '\'' +
				", userId='" + userId + '\'' +
				", userName='" + userName + '\'' +
				", validType=" + validType +
				", isSelected=" + isSelected +
				", isValid=" + isValid +
				", refreshTokenId=" + refreshTokenId +
				", createdAt=" + createdAt +
				", updatedAt=" + updatedAt +
				", requestId='" + requestId + '\'' +
				", createdBy=" + createdBy +
				", updatedBy=" + updatedBy +
				", locationSyncStatus=" + locationSyncStatus +
				", nextSync=" + nextSync +
				", gMsgLocationStatus='" + gMsgLocationStatus + '\'' +
				", gMsgLocationComment='" + gMsgLocationComment + '\'' +
				", gMsgLocationName='" + gMsgLocationName + '\'' +
				", gmbAccountId=" + gmbAccountId +
				", reportNextSyncDate=" + reportNextSyncDate +
				", agentId=" + agentId +
				'}';
	}
}
