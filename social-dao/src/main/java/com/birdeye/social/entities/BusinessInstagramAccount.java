package com.birdeye.social.entities;

import com.birdeye.social.aspect.PageRegionSync;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.*;

@Entity
@Table(name = "business_instagram_account")
@PageRegionSync
public class BusinessInstagramAccount implements Serializable {

	private static final long serialVersionUID = 6540390695719817417L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(name = "enterprise_id")
	private Long enterpriseId;

	@Column(name = "accountId")
	private Integer	 accountId;

	@Column(name = "business_id")
	private Integer businessId;

	@Column(name = "facebook_page_id")
	private String facebookPageId;

	@Column(name = "instagram_link")
	private String instagramLink;


	@Column(name = "instagram_account_id")
	private String instagramAccountId;

	@Column(name = "page_access_token")
	private String pageAccessToken;

	@Column(name = "instagram_account_name")
	private String instagramAccountName;

	@Column(name = "instagram_picture_url")
	private String instagramAccountPictureUrl;

	@Column(name = "is_managed")
	private Integer isManaged;

	@Column(name = "followers_count")
	private Integer followersCount;

	@Column(name = "instagram_handle")
	private String instagramHandle;

	@Column(name = "user_id")
	private String userId;

	@Column(name = "first_name")
	private String firstName;

	@Column(name = "last_name")
	private String lastName;

	@Column(name = "is_valid")
	private Integer isValid;

	@Column(name = "can_post")
	private Integer canPost = 1;
	
	@Column(name = "validity_type")
	private Integer validType;

	@Column(name = "is_selected")
	private Integer isSelected;

	@Column(name = "business_get_page_id")
	private String businessGetPageId;

	@Column(name = "task")
	private String task;

	@Column(name="page_permissions")
	private String pagePermissions;

	@Column(name="granular_page_permissions")
	private String granularPagePermissions;

	@Column(name = "created_by")
	private Integer createdBy;

	@Column(name = "updated_by")
	private Integer updatedBy;


	@Column(name = "error_code")
	private String errorCode;

	@Column(name = "error_message")
	private String errorMessage;

	@Column(name = "next_sync_date")
	private Date nextSyncDate;

	@Column(name = "last_scanned_on")
	private Date lastScannedOn;

	@Column(name = "manual_scanned_on")
	private Date manualScannedOn;

	@Column(name = "engage_sync_date")
	private Date engageSyncDate;

	@Column(name = "reseller_id")
	private Long resellerId;
	
	@Column(name = "user_email_id")
	private String userEmailId;

	public Integer getCanPost() {
		return canPost;
	}

	public void setCanPost(Integer canPost) {
		this.canPost = canPost;
	}

	public Date getLastScannedOn() {
		return lastScannedOn;
	}

	public void setLastScannedOn(Date lastScannedOn) {
		this.lastScannedOn = lastScannedOn;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Long getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public Integer getBusinessId() {
		return businessId;
	}

	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}

	public String getFacebookPageId() {
		return facebookPageId;
	}

	public void setFacebookPageId(String facebookPageId) {
		this.facebookPageId = facebookPageId;
	}

	public String getInstagramLink() {
		return instagramLink;
	}

	public void setInstagramLink(String instagramLink) {
		this.instagramLink = instagramLink;
	}

	public String getInstagramAccountId() {
		return instagramAccountId;
	}

	public void setInstagramAccountId(String instagramAccountId) {
		this.instagramAccountId = instagramAccountId;
	}

	public String getPageAccessToken() {
		return pageAccessToken;
	}

	public void setPageAccessToken(String pageAccessToken) {
		this.pageAccessToken = pageAccessToken;
	}

	public String getInstagramAccountName() {
		return instagramAccountName;
	}

	public void setInstagramAccountName(String instagramAccountName) {
		this.instagramAccountName = instagramAccountName;
	}

	public String getInstagramAccountPictureUrl() {
		return instagramAccountPictureUrl;
	}

	public void setInstagramAccountPictureUrl(String instagramAccountPictureUrl) {
		this.instagramAccountPictureUrl = instagramAccountPictureUrl;
	}

	public Integer getIsManaged() {
		return isManaged;
	}

	public void setIsManaged(Integer isManaged) {
		this.isManaged = isManaged;
	}

	public Integer getFollowersCount() {
		return followersCount;
	}

	public void setFollowersCount(Integer followersCount) {
		this.followersCount = followersCount;
	}

	public String getInstagramHandle() {
		return instagramHandle;
	}

	public void setInstagramHandle(String instagramHandle) {
		this.instagramHandle = instagramHandle;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Integer getIsSelected() {
		return isSelected;
	}

	public void setIsSelected(Integer isSelected) {
		this.isSelected = isSelected;
	}

	public String getBusinessGetPageId() {
		return businessGetPageId;
	}

	public void setBusinessGetPageId(String businessGetPageId) {
		this.businessGetPageId = businessGetPageId;
	}

	public String getTask() {
		return task;
	}

	public void setTask(String task) {
		this.task = task;
	}

	public String getPagePermissions() {
		return pagePermissions;
	}

	public void setPagePermissions(String pagePermissions) {
		this.pagePermissions = pagePermissions;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Integer getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public Date getNextSyncDate() {
		return nextSyncDate;
	}

	public void setNextSyncDate(Date nextSyncDate) {
		this.nextSyncDate = nextSyncDate;
	}

	public Date getManualScannedOn() {
		return manualScannedOn;
	}

	public void setManualScannedOn(Date manualScannedOn) {
		this.manualScannedOn = manualScannedOn;
	}

	public Integer getAccountId() {
		return accountId;
	}

	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}
	
	public Long getResellerId() {
		return resellerId;
	}

	public void setResellerId(Long resellerId) {
		this.resellerId = resellerId;
	}

	public String getUserEmailId() {
		return userEmailId;
	}

	public void setUserEmailId(String userEmailId) {
		this.userEmailId = userEmailId;
	}

	public String getGranularPagePermissions() {
		return granularPagePermissions;
	}

	public void setGranularPagePermissions(String granularPagePermissions) {
		this.granularPagePermissions = granularPagePermissions;
	}

	public Integer getValidType() {
		return validType;
	}

	public void setValidType(Integer validType) {
		this.validType = validType;
	}

	@Override
	public String toString() {
		return "BusinessInstagramAccount [id=" + id + ", enterpriseId=" + enterpriseId + ", accountId=" + accountId
				+ ", businessId=" + businessId + ", facebookPageId=" + facebookPageId + ", instagramLink="
				+ instagramLink + ", instagramAccountId=" + instagramAccountId + ", pageAccessToken=" + pageAccessToken
				+ ", instagramAccountName=" + instagramAccountName + ", instagramAccountPictureUrl="
				+ instagramAccountPictureUrl + ", isManaged=" + isManaged + ", followersCount=" + followersCount
				+ ", instagramHandle=" + instagramHandle + ", userId=" + userId + ", firstName=" + firstName
				+ ", lastName=" + lastName + ", isValid=" + isValid + ", validType=" + validType + ", isSelected="
				+ isSelected + ", businessGetPageId=" + businessGetPageId + ", task=" + task + ", pagePermissions="
				+ pagePermissions + ", granularPagePermissions=" + granularPagePermissions + ", createdBy=" + createdBy
				+ ", updatedBy=" + updatedBy + ", errorCode=" + errorCode + ", errorMessage=" + errorMessage
				+ ", nextSyncDate=" + nextSyncDate + ", lastScannedOn=" + lastScannedOn + ", manualScannedOn="
				+ manualScannedOn + ", engageSyncDate=" + engageSyncDate + ", resellerId=" + resellerId
				+ ", userEmailId=" + userEmailId + "]";
	}
	
}
