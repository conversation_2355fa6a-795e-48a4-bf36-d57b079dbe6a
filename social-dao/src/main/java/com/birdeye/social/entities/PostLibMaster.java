package com.birdeye.social.entities;
/**
 *
 * <AUTHOR>
 *
 */
import com.birdeye.social.sro.postlibrary.PostLibInsightsMetaData;
import com.birdeye.social.utils.PostLibraryInsightsDBConverter;
import lombok.*;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "post_lib_master")
public class PostLibMaster {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "post_text")
    private String	postText;

    @Column(name = "created_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdDate;

    @Column(name = "enterprise_id")
    private Integer enterpriseId;

    @Column(name = "social_master_post_id")
    private Integer socialMasterPostId;

    @Column(name = "video_ids")
    private String videoIds;

    @Column(name = "image_ids")
    private String	imageIds;

    @Column(name = "public_image_ids")
    private String	publicImageIds;

    @Column(name = "compressed_image_ids")
    private String	compressedImageIds;

    @Column(name = "public_compressed_image_ids")
    private String	publicCompressedImageIds;

    @Column(name = "pexels_image_ids")
    private String	pexelsImageIds;

    @Column(name = "created_by")
    private Integer	createdBy;

    @Column(name = "edited_by")
    private Integer	editedBy;

    @Column(name = "post_metadata")
    private String	postMetadata;

    @Column(name = "link_preview_url")
    private String linkPreviewUrl;

    @Column(name = "last_edited_at")
    private Date lastEditedAt;

    @Column(name = "usage_count")
    private Integer usageCount = 0; // This AI post schedule/publish usage counter

    @Column(name = "total_engagements")
    private Integer totalEngagements = 0;

    @Column(name = "total_impressions")
    private Integer totalImpressions = 0;

    @Column(name = "insight_metadata",columnDefinition = "json")
    @Convert(converter = PostLibraryInsightsDBConverter.class)
    private PostLibInsightsMetaData insightMetadata;

    @Column(name = "last_used_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastUsedDate;

    @OneToMany(mappedBy = "postLibMaster", orphanRemoval = true,fetch = FetchType.EAGER)
    private List<PostLib> postLib;

    @Column(name = "approval_workflow_id")
    private Integer approvalWorkflowId;

    @Column(name = "is_tagged")
    private Integer isTagged=0;

    @Column(name = "ai_post")
    private Integer aiPost;
    // AI Suggested columns
    @Column(name = "ai_suggested")
    private Integer aiSuggested = 0;
    @Column(name = "post_header")
    private String	postHeader;
    @Column(name = "calendar_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date calendarTime;
    @Column(name = "not_recommended")
    private Boolean notRecommended;
    @Column(name = "is_content_published")
    private Boolean isContentPublished;
    @Column(name = "post_id")
    private String postId;
    @Column(name = "ai_reason")
    private String aiReason;
    @Transient
    private String tag;
    @Column(name = "image_source")
    private String imageSource;
}
