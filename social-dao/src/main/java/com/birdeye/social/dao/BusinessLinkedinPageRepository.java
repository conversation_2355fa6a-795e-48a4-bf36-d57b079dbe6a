package com.birdeye.social.dao;

import com.birdeye.social.dto.ApprovalPageInfo;
import com.birdeye.social.dto.SocialBusinessPageInfo;
import com.birdeye.social.entities.BusinessLinkedinPage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

public interface BusinessLinkedinPageRepository extends JpaRepository<BusinessLinkedinPage, Integer>, JpaSpecificationExecutor<BusinessLinkedinPage> {

	public List<BusinessLinkedinPage> findByBusinessId(Integer businessId);

	public BusinessLinkedinPage findById(Integer id);

	List<BusinessLinkedinPage> findByProfileIdIn(List<String> profileIds);

	BusinessLinkedinPage findByProfileId(String profileId);

	BusinessLinkedinPage findByProfileIdAndEnterpriseIdAndIsValid(String profileId, Long enterpriseId, Integer isValid);

	List<BusinessLinkedinPage> findByResellerIdAndProfileIdInAndIsValid(Long businessId, List<String> profileIds, Integer isValid);

	BusinessLinkedinPage findByProfileIdAndIsValid(String profileId, Integer isValid);

	List<BusinessLinkedinPage> findByProfileIdInAndPageType(List<String> profileIds, String pageType);

	BusinessLinkedinPage findByProfileIdAndPageType(String profileId, String pageType);

	List<BusinessLinkedinPage> findByBusinessGetPageId(String businessGetPageReqId);

	BusinessLinkedinPage findFirstByBusinessGetPageId(String businessGetPageReqId);

	public BusinessLinkedinPage findFirstByProfileId(String profileId);

	public List<BusinessLinkedinPage> findByBusinessIdAndCompanyId(Integer businessId, Integer companyId);

	public List<BusinessLinkedinPage> findByEnterpriseIdAndProfileIdIn(Long enterpriseId, List<String> profileIds);

	public List<BusinessLinkedinPage> findByResellerIdAndProfileIdIn(Long enterpriseId, List<String> profileIds);

	List<BusinessLinkedinPage> findByIsValidAndPageTypeAndEnterpriseIdNotNull(Integer isValid, String pageType);

	public BusinessLinkedinPage findByIdAndBusinessId(Integer id, Integer businessId);

	@Query("Select b from BusinessLinkedinPage b where b.businessId = :businessId and b.profileId is not null")
	public List<BusinessLinkedinPage> findByBusinessIdAndProfileIdIsNotNull(@Param("businessId") Integer businessId);

	@Query("Select b from BusinessLinkedinPage b where b.isValid = 1 and b.companyId is not null")
	public List<BusinessLinkedinPage> findAllActivePages();

	@Query("Select b from BusinessLinkedinPage b where b.isValid = 1 and b.profileId is not null and date(b.expiresOn) = :expireDate and b.refreshToken is not null group by b.refreshToken")
	public List<BusinessLinkedinPage> findActivePagesBatchForReconnectBatch(@Param("expireDate") Date expireDate, Pageable pageable);

	@Query("Select b from BusinessLinkedinPage b where b.businessId in :businessIds and b.isValid = 1")
	public List<BusinessLinkedinPage> findByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	@Query("select count(b) from BusinessLinkedinPage b where b.resellerId = :resellerId and b.validType in (:validity)")
	Long findCountByResellerIdAndValidityType(@Param("resellerId") Long resellerId, @Param("validity") List<Integer> asList);

	@Query("Select b from BusinessLinkedinPage b where b.businessId in :businessIds")
	List<BusinessLinkedinPage> findByBusinessIds(@Param("businessIds") Set<Integer> businessIds);

	@Query("Select b from BusinessLinkedinPage b where b.businessId in :businessIds")
	public List<BusinessLinkedinPage> findAllByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	@Query(value = "Select distinct(b.profileId) from BusinessLinkedinPage b where b.businessId in :businessId")
	public List<String> findDistinctProfileIdByBusinessIdIn(@Param("businessId") List<Integer> businessIds);

	@Query("Select count(b.id) from BusinessLinkedinPage b where b.businessId in :businessIds and b.isValid = 0 and b.companyId is not null")
	public int getNumberOfInvalidPages(@Param("businessIds") List<Integer> businessIds);

	@Query("Select b from BusinessLinkedinPage b where b.businessId in :businessIds")
	public List<BusinessLinkedinPage> findAllPagesByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	@Query("Select b from BusinessLinkedinPage b where b.businessId in :businessIds and b.isValid = 0 and b.companyId is not null")
	public List<BusinessLinkedinPage> findInvalidPagesByBusinessIdInWhereCompanyIdIsNonNull(@Param("businessIds") List<Integer> businessIds);

	@Query("Select b from BusinessLinkedinPage b where b.businessId in :businessIds and b.profileId = :profileId and b.companyId is not null")
	public List<BusinessLinkedinPage> findAllPagesByBusinessIdAndProfileIdInWhereCompanyIdIsNonNull(@Param("businessIds") List<Integer> businessIds, @Param("profileId") String profileId);

	@Query("Select b from BusinessLinkedinPage b where b.businessId in :businessIds and b.profileId = :profileId and b.isValid = 0 and b.companyId is not null")
	public List<BusinessLinkedinPage> findInvalidPagesByBusinessIdAndProfileIdInWhereCompanyIdIsNonNull(@Param("businessIds") List<Integer> businessIds, @Param("profileId") String profileId);

	@Query("Select b from BusinessLinkedinPage b where b.isValid = 1 and b.companyId is not null and b.profileId is null")
	public List<BusinessLinkedinPage> findAllValidPagesWhereProfileIdIsNull();

	boolean existsByEnterpriseIdAndIsSelected(Long enterpriseId, Integer isSelected);

	boolean existsByResellerIdAndIsSelected(Long resellerId, Integer selected);

	@Transactional
	Long deleteByPageTypeAndProfileIdIn(String pageType, List<String> profileIds);

	@Transactional
	Long deleteByPageTypeAndProfileIdNotIn(String pageType, List<String> profileIds);

	@Transactional
	void deleteByAccountId(Integer accountId);


	List<BusinessLinkedinPage> findByEnterpriseIdAndIsSelected(Long enterpriseId, int isSelected);

	List<BusinessLinkedinPage> findByEnterpriseIdAndIsSelectedAndIsValid(Long enterpriseId, int isSelected, int isValid);

	@Query("Select b from BusinessLinkedinPage b where (b.enterpriseId = :enterpriseId or b.resellerId = :enterpriseId) and b.isSelected = :isSelected and b.isValid = :isValid")
	List<BusinessLinkedinPage> findFirstByEnterpriseIdOrResellerIdAndIsSelectedAndIsValid(@Param("enterpriseId") Long enterpriseId,
																						  @Param("isSelected") Integer isSelected,
																						  @Param("isValid") Integer isValid,
																						  Pageable pageable);

	List<BusinessLinkedinPage> findByIsSelected(Integer isSelected);

	BusinessLinkedinPage findByCompanyId(Integer companyId);

	List<BusinessLinkedinPage> findByEnterpriseId(Long enterpriseId);

	List<BusinessLinkedinPage> findByAccountId(Integer accountId);

	@Query(value = "Select DISTINCT b.enterpriseId  from BusinessLinkedinPage b where b.enterpriseId is not null and b.isValid = :isValid")
	List<Long> findDistinctEnterpriseIdByIsValid(@Param("isValid") int isValid);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessLinkedinPage b SET b.accessToken = :accessToken , b.expiresOn = :expireTime , b.lastScannedOn = :date where b.refreshToken = :refreshToken")
	int updateAccessTokenByRefreshTokenId(@Param("refreshToken") Integer refreshToken, @Param("accessToken") String accessToken, @Param("expireTime") Date expireTime, @Param("date") Date date);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessLinkedinPage b SET b.accessToken = :accessToken , b.expiresOn = :expireTime , b.refreshToken = :refreshToken where b.profileId = :profileId")
	int updateAccessTokenByProfileId(@Param("profileId") String profileId, @Param("accessToken") String accessToken, @Param("expireTime") Date expireTime, @Param("refreshToken") Integer refreshToken);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessLinkedinPage b SET b.isValid = 0 , b.linkedinErrorCode = 1 , b.lastScannedOn = :date where b.refreshToken = :refreshToken")
	int updatePageDetailByRefreshTokenId(@Param("refreshToken") Integer refreshToken, @Param("date") Date date);

	@Query(value = "Select b.id from BusinessLinkedinPage b where b.profileId in :pages And b.pageType = :type")
	List<Number> findIdByProfileIdInAndPageType(@Param("pages") List<String> pages, @Param("type") String type);

	List<BusinessLinkedinPage> findByProfileIdInAndBusinessIdIsNotNull(List<String> linkedinProfileIds);

	// todo - add check for organization only - like "%organization%" - verify if needed?
	@Query("Select b.businessId from BusinessLinkedinPage b where b.enterpriseId = :enterpriseId and b.isValid = :isValid")
	List<Integer> findBusinessIdsByEnterpriseId(@Param("enterpriseId") Long enterpriseId, @Param("isValid") int i);

	@Query(value = "Select distinct(b.profileId) from BusinessLinkedinPage b where b.businessId in :businessIds")
	List<String> findDistinctPageIdByBusinessIdInAndIsValid(@Param("businessIds") List<Integer> businessIds);

	Page<BusinessLinkedinPage> findByIsValidAndBusinessIdNotNullAndNextSyncDateIsLessThanEqualOrderByNextSyncDateAsc(Integer isValid, Date nextSyncDate, Pageable pageable);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessLinkedinPage b SET b.nextSyncDate = :nextSyncDate where b.id in :ids")
	int updateNextSyncDate(@Param("nextSyncDate") Date nextSyncDate, @Param("ids") List<Integer> id);

	List<BusinessLinkedinPage> findByAccountIdAndBusinessIdNotNull(Integer accountId);


	@Modifying
	@Transactional
	@Query("UPDATE BusinessLinkedinPage b SET b.eventExpiryDate = :eventExpiryDate where b.id = :id")
	int updateEventRecord(@Param("eventExpiryDate") Date eventExpiryDate, @Param("id") Integer id);

	@Query("Select b from BusinessLinkedinPage b where b.enterpriseId in :enterpriseId and b.businessId is not null and b.isValid =1  And b.pageType = :type")
	List<BusinessLinkedinPage> findAllValidAccounts(@Param("enterpriseId") List<Long> enterpriseId, @Param("type") String type);


	@Query("Select b from BusinessLinkedinPage b where b.isValid = 1 and b.pageType ='company' and b.profileId is not null and b.id in :ids")
	public List<BusinessLinkedinPage> findAllValidPagesWhereProfileIdIsNotNull(@Param("ids") List<Integer> id);


	@Modifying
	@Transactional
	@Query("UPDATE BusinessLinkedinPage b SET b.logoUrl =:logoUrl , b.vanityName =:vanityName  where b.id = :id")
	int updatePageDetailById(@Param("logoUrl") String logoUrl, @Param("vanityName") String vanityName, @Param("id") Integer id);

	@Query(value = "Select distinct(b.profileId) from BusinessLinkedinPage b where b.businessId in :businessIds and b.isValid = 1")
	List<String> findDistinctPageIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	@Query(value = "Select distinct(b.profileId) from BusinessLinkedinPage b where b.businessId in :businessIds")
	List<String> findDistinctPageIdByBusinessIdsIn(@Param("businessIds") List<Integer> businessIds);

	BusinessLinkedinPage findFirstByProfileIdAndIsValid(String externalPageId, Integer isValid);

	Page<BusinessLinkedinPage> findByLastScannedOnLessThanAndIsValid(Date addDays, Integer isValid, Pageable pageable);

	@Modifying
	@Transactional
	@Query("update BusinessLinkedinPage b set b.lastScannedOn =:date where b.id in (:ids)")
	void updateLastScannedOnByIdIn(@Param("date") Date date, @Param("ids") List<Integer> ids);

	@Query(value = "Select distinct(b.profileId) from BusinessLinkedinPage b where b.enterpriseId in :enterpriseId and b.profileId is not null ")
	Collection<String> findDistinctPageIdByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

	@Query("select distinct b.businessId from BusinessLinkedinPage b where b.businessId is not NULL")
	public Page<Integer> findBusinessIdsNotNull(Pageable page);

	@Transactional
	@Modifying
	@Query("update BusinessLinkedinPage b set b.businessId = null, b.enterpriseId=null, b.isSelected=0 where b.businessId in :invalidBusinessIds")
	void unmapInvalidPage(@Param("invalidBusinessIds") List<Integer> invalidBusinessIds);

	@Query("select distinct b.enterpriseId from BusinessLinkedinPage b where b.enterpriseId is NOT NULL")
	List<Long> findEnterpriseIds(Pageable page);

	@javax.transaction.Transactional
	@Modifying
	@Query("update BusinessLinkedinPage b set b.businessId = null, b.enterpriseId=null, b.isSelected=0 where b.enterpriseId in :invalidBusinessIds")
	void unmapSMBInvalidPage(@Param("invalidBusinessIds") Set<Long> invalidBusinessIds);

	@Query(value = "Select b from BusinessLinkedinPage b where b.enterpriseId in :enterpriseId and b.businessId is not null  and b.isValid=1")
	List<BusinessLinkedinPage> findValidPages(@Param("enterpriseId") Long enterpriseId);

	BusinessLinkedinPage findFirstByRefreshToken(Integer refreshTokenId);

	@Query("Select b.profileId, b.enterpriseId from BusinessLinkedinPage b where b.profileId in :profileIds")
	List<Object[]> findPageIdAndEnterpriseIdbyProfileIds(@Param("profileIds") List<String> profileIds);

	@Query("select a.profileId as pageId , a.companyName as pageName , a.logoUrl as profileImage from BusinessLinkedinPage a where a.profileId = :profileId")
	List<ApprovalPageInfo> findByProfileInLite(@Param("profileId") String pageId);

	@Query("Select b.profileId from BusinessLinkedinPage b where b.enterpriseId is not null and b.businessId is not null and b.isValid =1")
	List<String> findValidBusinessPageIds(Pageable pageRequest);

	@Query("Select b.profileId from BusinessLinkedinPage b where b.enterpriseId = :enterpriseId and b.isValid = 1 and b.businessId is NOT NULL")
	List<String> findByEnterpriseIdAndIsValidAndMapped(@Param("enterpriseId") Long enterpriseId);

	@Query("Select b from BusinessLinkedinPage b where b.enterpriseId IN (:enterpriseId) and b.isValid = 1 and b.businessId is NOT NULL")
	List<BusinessLinkedinPage> findDataByEnterpriseIdAndIsValidAndMapped(@Param("enterpriseId") List<Long> enterpriseId);
	@Query("Select b.profileId from BusinessLinkedinPage b where b.id in :ids")
	public Set<String> findProfileIdById(@Param("ids") List<Integer> ids);

	@Query("Select b.profileId from BusinessLinkedinPage b where b.businessId in :businessIds")
	public Set<String> findProfileIdByBusinessId(@Param("businessIds") List<Integer> businessIds);

	@Query("SELECT B.isValid as isValid, B.businessId as businessId from BusinessLinkedinPage B where B.businessId in (:businessIds)")
	List<SocialBusinessPageInfo> findSocialBusinessPageInfoByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	@Query(value = "Select b from BusinessLinkedinPage b where b.businessId is not null  and b.isValid=1 and b.profileId in :profileIds and b.urn like '%organization%'")
	List<BusinessLinkedinPage> findByProfileIdInAndBusinessIdIsNotNullAndEligible(@Param("profileIds") List<String> profileIds);

	List<BusinessLinkedinPage> findByProfileIdInAndIsSelected(List<String> profileIds, Integer isSelected);

	Page<BusinessLinkedinPage> findByBusinessGetPageId(String requestId, Pageable pageable);

	public Page<BusinessLinkedinPage> findByResellerIdAndBusinessIdIsNull(@Param("resellerId") Long resellerId, Pageable pageable);

	@Query(value = "SELECT b.businessId FROM BusinessLinkedinPage b WHERE b.businessId IN :businessIds")
	public List<Integer> findAllIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	@Query("Select b.businessId from BusinessLinkedinPage b where b.resellerId = :resellerId and b.businessId is not null ")
	List<Integer> findByResellerIdAndBusinessIdIsNotNull(@Param("resellerId") Long resellerId);

	boolean existsByBusinessId(@Param("businessId") Integer businessId);

	@Query("Select b.profileId from BusinessLinkedinPage b where b.businessGetPageId = :requestId and b.enterpriseId is null and b.resellerId is null")
	List<String> findAllByRequestId(@Param("requestId") String requestId);


	@Query("Select b from BusinessLinkedinPage b where b.profileId in :profileIds")
	public List<BusinessLinkedinPage> findByProfileIdInWithLimit(@Param("profileIds") Collection<String> profileIds, Pageable page);

	@Query("select count(b) from BusinessLinkedinPage b where b.resellerId = :resellerId and b.isValid = :isValid")
	Long findCountByResellerIdAndValidity(@Param("resellerId") Long resellerId, @Param("isValid") Integer isValid);

	@Query("select b from BusinessLinkedinPage b where b.resellerId = :resellerId and b.isValid = :isValid and b.isSelected = :isSelected")
	Page<BusinessLinkedinPage> findByResellerIdAndValidity(@Param("resellerId") Long resellerId, @Param("isValid") Integer isValid, Pageable pageable, @Param("isSelected") Integer isSelected);

	public Page<BusinessLinkedinPage> findByResellerIdAndIsSelected(Long enterpriseId, Integer isSelected, Pageable pageable);

	@Query(value = "Select b from BusinessLinkedinPage b where b.enterpriseId in :enterpriseId and b.isValid = 1")
	List<BusinessLinkedinPage> findDistinctPageIdByEnterpriseIdIn(@Param("enterpriseId") List<Long> enterpriseId);

	List<BusinessLinkedinPage> findByEnterpriseIdIn(Set<Long> enterpriseId);

	@Query("Select b.businessId from BusinessLinkedinPage b where  b.enterpriseId in :bizHierarchyList and b.businessId is not null")
	public List<Integer> findByEnterpriseIdInAndBusinessIdIsNotNull(@Param("bizHierarchyList") List<Long> bizHierarchyList);

	@Query("SELECT b FROM BusinessLinkedinPage b WHERE b.isValid = 1 AND (b.moduleImpacted NOT LIKE %:INSIGHTS% OR b.moduleImpacted IS NULL) AND b.businessId IS NOT NULL")
	List<BusinessLinkedinPage> findAllPagesByIsValidAndModuleImpactedNotIn(Pageable pageRequest);

	@Query(value = "SELECT bfp.business_id AS businessId, bfp.profile_id AS profileId "
			+ "FROM business_linkedin_page bfp where bfp.business_id in (:businessIds)", nativeQuery = true)
	List<BusinessLinkedinPageRepository.BLP> findByPageBusinessIds(@Param("businessIds") List<Integer> businessIds);

	Page<BusinessLinkedinPage> findByEnterpriseIdAndBusinessIdIsNull(@Param("enterpriseId") Long enterpriseId, Pageable pageable);

	interface BLP {
		Integer getBusinessId();

		String getProfileId();
	}

	@Query(value = "Select distinct(linkedin.businessGetPageId) from BusinessLinkedinPage linkedin where linkedin.businessGetPageId in :businessGetPageIds")
	List<String> findDistinctBusinessGetPageIdByBusinessGetPageIdIn(@Param("businessGetPageIds") Set<String> businessGetPageIds);

	@Query(value = "SELECT linkedin.businessId FROM BusinessLinkedinPage linkedin where linkedin.profileId IN :profileIds")
	Set<Integer> getBusinessIdByProfileIdsIn(@Param("profileIds") List<String> profileIds);

	@Query("select count(b) from BusinessLinkedinPage b where b.enterpriseId = :enterpriseId and (b.validType in (:validity) or b.isValid = :isValid))")
	Long findCountByEnterpriseIdAndValidityTypeOrIsValid(@Param("enterpriseId") Long enterpriseId, @Param("validity") List<Integer> asList, @Param("isValid") Integer isValid);

	@Query("SELECT COUNT(b) > 0 FROM BusinessLinkedinPage b WHERE b.enterpriseId = :enterpriseId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
	boolean existsMappedPageByEnterpriseId(@Param("enterpriseId") Long enterpriseId, @Param("businessIds")List<Integer> businessIds);

	@Query("SELECT COUNT(b) > 0 FROM BusinessLinkedinPage b WHERE b.resellerId = :resellerId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
	boolean existsMappedPageByResellerId(@Param("resellerId") Long resellerId, @Param("businessIds")List<Integer> businessIds);

	@Query("Select b.profileId from BusinessLinkedinPage b where b.enterpriseId = :enterpriseId")
	List<String> findProfileIdByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

	@Query("SELECT b.profileId FROM BusinessLinkedinPage b")
	List<String> findProfileIdsWithPagination(Pageable pageRequest);
}

