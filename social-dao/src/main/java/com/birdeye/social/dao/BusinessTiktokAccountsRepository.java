package com.birdeye.social.dao;

import com.birdeye.social.entities.BusinessTiktokAccounts;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import org.springframework.data.jpa.repository.Modifying;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Set;

public interface BusinessTiktokAccountsRepository extends JpaRepository<BusinessTiktokAccounts, Integer> {


    BusinessTiktokAccounts findByBusinessId(Integer id);

    BusinessTiktokAccounts findByIdAndBusinessId(Integer id, Integer businessId);

    List<BusinessTiktokAccounts> findByProfileIdInAndBusinessIdIsNotNull(List<String> pageIds);

    @Query(value = "Select distinct(b.profileId) from BusinessTiktokAccounts b where b.businessId in :businessIds")
    List<String> findProfileIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

    @Query(value = "Select distinct(b.profileId) from BusinessTiktokAccounts b where b.businessId in :businessIds  and b.isValid = 1")
    Set<String> findDistinctProfileIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

    @Query("Select distinct(a.profileId) from BusinessTiktokAccounts a where a.enterpriseId = :enterpriseId and a.profileId is not null")
    Collection<String> findDistinctProfileIdByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

    @Query("Select distinct(a.profileId) from BusinessTiktokAccounts a where a.enterpriseId = :enterpriseId and a.profileId is not null")
    List<String> findDistinctProfileIdsByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

    @Query("SELECT b.profileId FROM BusinessTiktokAccounts b where b.profileId is not null")
    List<String> findProfileIdsWithPagination(Pageable pageRequest);

    BusinessTiktokAccounts findById(Integer id);

    BusinessTiktokAccounts findByProfileId(String pageId);

    List<BusinessTiktokAccounts> findByRequestId(String id);

    Page<BusinessTiktokAccounts> findByRequestId(String id, Pageable pageable);

    List<BusinessTiktokAccounts> findByAccountId(Integer accountId);

    List<BusinessTiktokAccounts> findByEnterpriseId(Long enterpriseId);

    List<BusinessTiktokAccounts> findByEnterpriseIdAndBusinessIdNotNull(Long enterpriseId);

    @Query("Select b.businessId from BusinessTiktokAccounts b where b.resellerId = :resellerId and b.businessId is not null")
    List<Integer> findByResellerIdAndBusinessIdNotNull(Long resellerId);

    @Query("Select b.businessId from BusinessTiktokAccounts b where  b.enterpriseId in :bizHierarchyList and b.businessId is not null")
    List<Integer> findByEnterpriseIdInAndBusinessIdNotNull(@Param("bizHierarchyList") List<Long> bizHierarchyList);

    Page<BusinessTiktokAccounts> findByResellerIdAndBusinessIdNull(Long resellerId, Pageable pageable);

    @Modifying
    @Transactional
    @Query(nativeQuery =true,value="DELETE from business_tiktok_accounts  WHERE reseller_id = :reseller_id AND profile_id NOT IN (:pageIds)")
    void deleteByResellerIdAndProfileIdNotIn(@Param("reseller_id") Long parentId, @Param("pageIds") List<String> pageIds);

    @Query("Select b from BusinessTiktokAccounts b where b.businessId in :businessIds")
    List<BusinessTiktokAccounts> findAllByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

    List<BusinessTiktokAccounts> findByProfileIdIn(@Param("profileIds") Collection<String> profileIds, Pageable page);

    @Modifying
    @Transactional
    @Query(nativeQuery =true,value="DELETE from business_tiktok_accounts  WHERE enterprise_id = :enterpriseId AND profile_id NOT IN (:pageIds)")
    void deleteByEnterpriseIdAndProfileIdNotIn(@Param("enterpriseId") Long parentId, @Param("pageIds") List<String> pageIds);

    @Transactional
    void deleteByAccountId(Integer accountId);

    @Query("Select b.profileId from BusinessTiktokAccounts b where b.profileName like %:pageName% and b.requestId = :requestId and b.enterpriseId is null and b.resellerId is null")
    List<String> findAllByTiktokPageName(@Param("pageName") String pageName, @Param("requestId") String requestId);

    @Query("Select b.profileId from BusinessTiktokAccounts b where b.requestId = :requestId and b.enterpriseId is null and b.resellerId is null")
    List<String> findAllByRequestId(@Param("requestId") String requestId);

    List<BusinessTiktokAccounts> findByProfileIdIn(List<String> pageIds);

    boolean existsByBusinessId(@Param("businessId") Integer businessId);

    List<BusinessTiktokAccounts> findByResellerIdAndProfileIdIn(long resellerId, List<String> pageIds);

    List<BusinessTiktokAccounts> findByEnterpriseIdAndProfileIdIn(long enterpriseId, List<String> pageIds);

    @Query(value = "SELECT tk.businessId FROM BusinessTiktokAccounts tk WHERE tk.businessId IN :businessIds")
    List<Integer> findAllIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

    @Query("select count(b) from BusinessTiktokAccounts b where b.enterpriseId = :enterpriseId and b.validType in (:validity)")
    Long findCountByEnterpriseIdAndValidityType(@Param("enterpriseId") Long enterpriseId, @Param("validity") List<Integer> asList);
    @Query("select count(b) from BusinessTiktokAccounts b where b.resellerId = :resellerId and b.validType in (:validity)")
    Long findCountByResellerIdAndValidityType(@Param("resellerId") Long resellerId, @Param("validity") List<Integer> asList);

    Page<BusinessTiktokAccounts> findAll(Specification<BusinessTiktokAccounts> specification, Pageable pageable);

    Page<BusinessTiktokAccounts> findByIsValidAndBusinessIdNotNullAndLastScannedOnLessThan(Integer isValid, Date nDaysBeforeTodayDate,
                                                                                           Pageable pageable);

    List<BusinessTiktokAccounts> findByEnterpriseIdAndIsSelected(Long enterpriseId, int isSelected);

    List<BusinessTiktokAccounts> findByBusinessIdIn(List<Integer> businessIds);

    boolean existsByResellerIdAndIsSelected(Long resellerId, Integer isSelected);

    @Query("Select b.profileId, b.enterpriseId from BusinessTiktokAccounts b where b.profileId in :profileIds")
    List<Object[]> findPageIdAndEnterpriseIdByProfileIds(@Param("profileIds") List<String> profileIds);

    @Query(value = "Select distinct(tiktok.requestId) from BusinessTiktokAccounts tiktok where tiktok.requestId in :requestIds")
    List<String> findDistinctRequestIdByRequestIdIn(@Param("requestIds") Set<String> requestIds);

    BusinessTiktokAccounts findFirstByProfileIdAndIsValid(String profileId, int isValid);

    BusinessTiktokAccounts findFirstByProfileId(String profileId);

    Page<BusinessTiktokAccounts> findByIsValidAndBusinessIdNotNullAndNextSyncDateIsLessThanEqualOrderByNextSyncDateAsc(Integer i, Date date, Pageable pageable);

    @Modifying
    @Transactional
    @Query("UPDATE BusinessTiktokAccounts b SET b.nextSyncDate = :nextSyncDate where b.id in :ids")
    int updateNextSyncDate(@Param("nextSyncDate") Date nextSyncDate, @Param("ids") List<Integer> id);

    BusinessTiktokAccounts findByProfileIdAndBusinessIdIsNotNull(String profileId);

    @Query(value = "Select distinct(b.profileId) from BusinessTiktokAccounts b where b.businessId in :businessIds AND isValid = 1")
    List<String> findDistinctProfileIdByBusinessIdInAndIsValid(@Param("businessIds")List<Integer> businessIds);

    List<BusinessTiktokAccounts> findByAccountIdAndBusinessIdNotNull(Integer accountId);

    @Query("SELECT COUNT(b) > 0 FROM BusinessTiktokAccounts b WHERE b.enterpriseId = :enterpriseId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
    boolean existsMappedPageByEnterpriseId(@Param("enterpriseId") Long enterpriseId, @Param("businessIds")List<Integer> businessIds);

    @Query("SELECT COUNT(b) > 0 FROM BusinessTiktokAccounts b WHERE b.resellerId = :resellerId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
    boolean existsMappedPageByResellerId(@Param("resellerId") Long resellerId, @Param("businessIds")List<Integer> businessIds);

    @Query(value = "SELECT tiktok.businessId FROM BusinessTiktokAccounts tiktok where tiktok.profileId IN :profileIds")
    Set<Integer> getBusinessIdByProfileIdsIn(@Param("profileIds") List<String> profileIds);

    @Query("Select b.businessId from BusinessTiktokAccounts b where  b.businessId in :businessIds")
    List<Integer> findByBusinessIds(@Param("businessIds") List<Integer> businessIds);

    Page<BusinessTiktokAccounts> findByEnterpriseIdAndBusinessIdIsNull(@Param("enterpriseId") Long enterpriseId, Pageable pageable);
}
