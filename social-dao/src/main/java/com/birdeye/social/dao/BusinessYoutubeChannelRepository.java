package com.birdeye.social.dao;

import com.birdeye.social.dto.ApprovalPageInfo;
import com.birdeye.social.dto.SocialBusinessPageInfo;
import com.birdeye.social.entities.BusinessYoutubeChannel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Repository
public interface BusinessYoutubeChannelRepository extends JpaRepository<BusinessYoutubeChannel, Integer>, JpaSpecificationExecutor<BusinessYoutubeChannel> {

    BusinessYoutubeChannel findById(Integer id);
    BusinessYoutubeChannel findByIdAndBusinessId(Integer id, Integer businessId);
    List<BusinessYoutubeChannel> findByRequestId(String requestId);

    List<BusinessYoutubeChannel> findByEnterpriseId(Long enterpriseId);

    List<BusinessYoutubeChannel> findByAccountId(Integer accountId);

    List<BusinessYoutubeChannel> findByChannelIdIn(List<String> channelId);
    List<BusinessYoutubeChannel> findByIsValidAndEnterpriseIdNotNull(Integer isValid);

    List<BusinessYoutubeChannel> findByBusinessId(Integer enterpriseId);

    List<BusinessYoutubeChannel> findByEnterpriseIdAndChannelIdIn(Long enterpriseId, List<String> channelIds);

    List<BusinessYoutubeChannel> findByResellerIdAndChannelIdIn(Long resellerId, List<String> channelIds);

    BusinessYoutubeChannel findByChannelId(String channelId);

    BusinessYoutubeChannel findByChannelIdAndEnterpriseIdAndIsValid(String channelId, Long enterpriseId, Integer isValid);

    List<BusinessYoutubeChannel> findByBusinessIdIn(List<Integer> enterpriseId);


//    @Modifying
//    @Transactional
//    void deleteByEnterpriseIdAndChannelIdNotIn(Long enterpriseId, List<String> channelId);

//    @Modifying
//    @Transactional
//    void deleteByEnterpriseId(Long enterpriseId);

    @Modifying
    @Transactional
    void deleteByAccountIdAndChannelIdNotIn(Integer accountId, List<String> channelId);

    @Modifying
    @Transactional
    void deleteByAccountId(Integer accountId);

    List<BusinessYoutubeChannel> findByChannelIdAndBusinessIdIsNotNull(List<String> pageIds);

    boolean existsByEnterpriseIdAndIsSelected(Long enterpriseId, Integer isSelected);

    List<BusinessYoutubeChannel> findByEnterpriseIdAndIsSelected(Long enterpriseId, Integer isSelected);

    @Modifying
    @Transactional
    void deleteByChannelIdIn(List<String> channelIds);

    List<BusinessYoutubeChannel> findAllByBusinessIdIn(List<Integer> locations);

    @Query(value = "Select distinct(b.channelId) from BusinessYoutubeChannel b where b.businessId in :businessId and b.isValid = 1")
    List<String> findDistinctChannelIdByBusinessIdIn(@Param("businessId") List<Integer> businessIds);

    @Query("select count(b) from BusinessYoutubeChannel b where b.resellerId = :resellerId and b.validType in (:validity)")
    Long findCountByResellerIdAndValidityType(@Param("resellerId") Long resellerId, @Param("validity") List<Integer> asList);

    List<BusinessYoutubeChannel> findByEnterpriseIdAndBusinessIdNotNull(Long enterpriseId);
//    List<BusinessYoutubeChannel> findByEnterpriseIdAndBusinessIdNotNull(Long enterpriseId);
    List<BusinessYoutubeChannel> findByAccountIdAndBusinessIdNotNull(Integer accountId);

    Page<BusinessYoutubeChannel> findByIsValidAndRefreshDataSyncDateIsLessThanEqualOrderByIdDesc(Integer isValid, Date reportNextSyncDate, Pageable pageable);

    List<BusinessYoutubeChannel> findByRefreshTokenId(Integer refreshTokenId);

    @Query("select distinct b.businessId from BusinessYoutubeChannel b where b.businessId is not NULL")
    public Page<Integer> findBusinessIdsNotNull(Pageable page);

    @Transactional
    @Modifying
    @Query("update BusinessYoutubeChannel b set b.businessId = null, b.enterpriseId=null, b.isSelected=0 where b.businessId in :invalidBusinessIds")
    void unmapInvalidPage(@Param("invalidBusinessIds") List<Integer> invalidBusinessIds);

    @Query("Select distinct(b.enterpriseId) from BusinessYoutubeChannel b where b.enterpriseId is NOT NULL")
    List<Long> findEnterpriseIds(Pageable page);

    @javax.transaction.Transactional
    @Modifying
    @Query("update BusinessYoutubeChannel b set b.businessId = null, b.enterpriseId=null, b.isSelected=0 where b.enterpriseId in :invalidBusinessIds")
    void unmapSMBInvalidPage(@Param("invalidBusinessIds") Set<Long> invalidBusinessIds);

    @Query("select a.channelId as pageId , a.channelName as pageName , a.pictureUrl as profileImage from BusinessYoutubeChannel a where a.channelId = :channelId")
    List<ApprovalPageInfo> findByChannelIdLite(@Param("channelId") String pageId);

    @Query("Select distinct(b.channelId) from BusinessYoutubeChannel b where b.businessId in :businessIds and b.isValid = 1")
    List<String> findDistinctYoutubeChannelsIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);



    @Query("Select b.channelId, b.enterpriseId from BusinessYoutubeChannel b where b.channelId in :channelIds")
    List<Object[]> findChannelIdAndEnterpriseIdByChannelIds(@Param("channelIds") List<String> channelIds);

    List<BusinessYoutubeChannel> findByChannelIdInAndIsValidAndBusinessIdNotNull(List<String> pageIds,Integer isValid);

    @Query(value = "Select b.enterpriseId from BusinessYoutubeChannel b where b.channelId = :channelId")
    Long findEnterpriseIdByChannelId(@Param("channelId") String channelId);

    Page<BusinessYoutubeChannel> findByIsValidAndBusinessIdNotNullAndEngageSyncDateIsLessThanEqualOrderByEngageSyncDateAsc(Integer isValid, Date engageSyncDate, Pageable pageable);

    @Transactional
    @Modifying
    @Query("update BusinessYoutubeChannel b set b.engageSyncDate = :syncDate where b.id in :ids")
    void updateEngageSyncDate(@Param("ids") List<Integer> ids, @Param("syncDate") Date syncDate);

    @Query(value = "Select distinct(b.channelId) from BusinessYoutubeChannel b where b.businessId in :businessIds")
    List<String> findDistinctPageIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

    Page<BusinessYoutubeChannel> findByIsValidAndBusinessIdNotNullAndNextSyncDateIsLessThanEqualOrderByNextSyncDateAsc(Integer isValid, Date nextSyncDate, Pageable pageable);

    @Modifying
    @Transactional
    @Query("UPDATE BusinessYoutubeChannel b SET b.nextSyncDate = :nextSyncDate where b.id in :ids")
    int updateNextSyncDate(@Param("nextSyncDate") Date nextSyncDate, @Param("ids") List<Integer> id);

    @Query(value = "Select distinct(b.channelId) from BusinessYoutubeChannel b where b.businessId in :businessId")
    List<String> findDistinctChannelIdByBusinessIdInAndIsValid(@Param("businessId")List<Integer> businessIds);


    @Query("select b.channelId from BusinessYoutubeChannel b where b.businessId in :businessIds")
    public Set<String> findPageIdByBusinessId(@Param("businessIds") List<Integer> businessIds);

    @Query("SELECT B.isValid as isValid, B.businessId as businessId from BusinessYoutubeChannel B where B.businessId in (:businessIds)")
    List<SocialBusinessPageInfo> findSocialBusinessPageInfoByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

    List<BusinessYoutubeChannel> findByChannelIdInAndIsSelected(List<String> channelId, Integer isSelected);
    @Query(value = "Select b from BusinessYoutubeChannel b where b.enterpriseId in :enterpriseId and b.isValid = 1")
    List<BusinessYoutubeChannel> findDistinctChannelIdByEnterpriseIdIn(@Param("enterpriseId") List<Long> enterpriseId);

    boolean existsByResellerIdAndIsSelected(Long resellerId, Integer selected);

    @Query("select count(b) from BusinessYoutubeChannel b where b.resellerId = :resellerId and b.isValid = :isValid")
   	Long findCountByResellerIdAndValidity(@Param("resellerId") Long resellerId, @Param("isValid") Integer isValid);

    @Query("select b from BusinessYoutubeChannel b where b.resellerId = :resellerId and b.isValid = :isValid and b.isSelected = :isSelected")
	Page<BusinessYoutubeChannel> findByResellerIdAndValidity(@Param("resellerId") Long resellerId, @Param("isValid") Integer isValid,Pageable pageable,@Param("isSelected") Integer isSelected);

	Page<BusinessYoutubeChannel> findByResellerIdAndIsSelected(Long resellerId, Integer isSelected, Pageable pageRequest);
	Page<BusinessYoutubeChannel> findAll(Specification<BusinessYoutubeChannel> ytChannel, Pageable pageRequest);
	Page<BusinessYoutubeChannel> findByRequestId(String requestId, Pageable pageable);

    @Query(value = "SELECT yt.businessId FROM BusinessYoutubeChannel yt WHERE yt.businessId IN :businessIds")
    public List<Integer> findAllIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	@Query("Select b from BusinessYoutubeChannel b where b.channelId in :channelIds")
	List<BusinessYoutubeChannel> findByChannelIdInWithLimit(@Param("channelIds") List<String> channelIds, Pageable pageRequest);

	@Query("Select b.channelId from BusinessYoutubeChannel b where b.channelName like %:channelName% and b.requestId = :requestId and b.enterpriseId is null and b.resellerId is null")
	List<String> findAllByYoutubeChannelName(@Param("channelName") String channelName,@Param("requestId") String requestId);

	@Query("Select b.channelId from BusinessYoutubeChannel b where b.requestId = :requestId and b.enterpriseId is null and b.resellerId is null")
	List<String> findChannelIdsByRequestId(@Param("requestId") String requestId);

	Page<BusinessYoutubeChannel> findByResellerIdAndBusinessIdIsNull(@Param("resellerId") Long resellerId, Pageable pageable);

	@Query("Select b.businessId from BusinessYoutubeChannel b where b.resellerId = :resellerId and b.businessId is NOT NULL")
    List<Integer> findByResellerIdAndBusinessIdIsNotNull(@Param("resellerId") Long resellerId);

	List<BusinessYoutubeChannel> findByEnterpriseIdIn(Set<Long> invalidBusinessIds);

	@Query("Select b.businessId from BusinessYoutubeChannel b where  b.enterpriseId in :bizHierarchyList and b.businessId is not null")
	public List<Integer> findByEnterpriseIdInAndBusinessIdIsNotNull(@Param("bizHierarchyList") List<Long> bizHierarchyList);

    boolean existsByBusinessId(Integer businessId);
    List<BusinessYoutubeChannel> findByChannelIdAndIsValid(String channelId, Integer isValid);
    @Query("Select b from BusinessYoutubeChannel b where b.enterpriseId IN (:enterpriseId) and b.isValid = 1 and b.businessId is NOT NULL")
    List<BusinessYoutubeChannel>  findByEnterpriseIdAndIsValid(@Param("enterpriseId") List<Long> enterpriseId);



    @Query(value = "Select distinct(yt.requestId) from BusinessYoutubeChannel yt where yt.requestId in :requestIds")
    List<String> findDistinctRequestIdByRequestIdIn(@Param("requestIds") Set<String> requestIds);

    @Query(value = "SELECT bfp.business_id AS businessId, bfp.channel_id AS channelId "
            + "FROM business_youtube_channel bfp where bfp.business_id in (:businessIds)", nativeQuery = true)
    List<BusinessYoutubeChannelRepository.BYP> findByBusinessIds(@Param("businessIds") List<Integer> businessIds);

    Page<BusinessYoutubeChannel> findByEnterpriseIdAndBusinessIdIsNull(@Param("enterpriseId") Long enterpriseId, Pageable pageable);

    public static interface BYP {
        public Integer getBusinessId();

        public String getChannelId();
    }
    @Query(value = "SELECT yt.businessId FROM BusinessYoutubeChannel yt where yt.channelId IN :channelIds")
    Set<Integer> getBusinessIdByChannelIdsIn(@Param("channelIds") List<String> channelIds);

    @Query("select count(b) from BusinessYoutubeChannel b where b.enterpriseId = :enterpriseId and (b.validType in (:validity) or b.isValid = :isValid))")
    Long findCountByEnterpriseIdAndValidityTypeOrIsValid(@Param("enterpriseId") Long enterpriseId, @Param("validity") List<Integer> asList, @Param("isValid") Integer isValid);


    @Query("SELECT COUNT(b) > 0 FROM BusinessYoutubeChannel b WHERE b.enterpriseId = :enterpriseId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
    boolean existsMappedPageByEnterpriseId(@Param("enterpriseId") Long enterpriseId, @Param("businessIds")List<Integer> businessIds);

    @Query("SELECT COUNT(b) > 0 FROM BusinessYoutubeChannel b WHERE b.resellerId = :resellerId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
    boolean existsMappedPageByResellerId(@Param("resellerId") Long resellerId, @Param("businessIds")List<Integer> businessIds);

    @Query("Select distinct(b.channelId) from BusinessYoutubeChannel b where b.enterpriseId = :enterpriseId")
    List<String> findDistinctYoutubeChannelsIdByBusinessIdIn(@Param("enterpriseId") Long enterpriseId);

    @Query("SELECT b.channelId FROM BusinessYoutubeChannel b")
    List<String> findChannelIdsWithPagination(Pageable pageRequest);

}
