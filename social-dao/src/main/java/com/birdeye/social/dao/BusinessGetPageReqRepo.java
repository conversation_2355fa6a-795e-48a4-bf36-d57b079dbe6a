/**
 * 
 */
package com.birdeye.social.dao;

import com.birdeye.social.entities.BusinessGetPageRequest;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;

import javax.persistence.LockModeType;
import javax.persistence.QueryHint;
import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public interface BusinessGetPageReqRepo extends JpaRepository<BusinessGetPageRequest, Integer> {
	
	public List<BusinessGetPageRequest> findByEnterpriseIdAndStatusAndChannel(Long enterpriseId, String status, String channel);

	public List<BusinessGetPageRequest> findByResellerIdAndStatusAndChannel(Long resellerId, String status, String channel);

	public List<BusinessGetPageRequest> findByEnterpriseIdAndStatusInAndChannel(long enterpriseId, List<String> status, String channel);

	public List<BusinessGetPageRequest> findByResellerIdAndStatusInAndChannel(Long businessId, List<String> statusList,
			String channel);
	
	@Query("select distinct b.birdeyeUserId from BusinessGetPageRequest b where b.enterpriseId=:enterpriseId and b.channel=:channel and b.status=:status")
	public List<Integer> findIdsByEnterpriseIdAndStatusInAndChannel(@Param("enterpriseId") Long enterpriseId,@Param("channel")  String channel,  @Param("status") List<String> status );

	public List<BusinessGetPageRequest> findByEnterpriseIdAndStatusInAndChannelAndRequestType(long enterpriseId, List<String> status, String channel, String requestType);

	public List<BusinessGetPageRequest> findByEnterpriseIdAndStatusInAndChannelIn(long enterpriseId, List<String> status, List<String> channel);
	
	public List<BusinessGetPageRequest> findByEnterpriseIdAndStatusAndChannelIn(Long enterpriseId, String status, List<String> channel);

	public List<BusinessGetPageRequest> findByEnterpriseIdAndStatusAndChannelAndRequestType(Long businessId, String status, String name, String requestType);

	public List<BusinessGetPageRequest> findByResellerIdAndStatusAndChannelAndRequestType(Long resellerId, String status, String name, String requestType);

	@Query("select b from BusinessGetPageRequest b where b.id = (select MAX(id) from BusinessGetPageRequest where enterpriseId=:enterpriseId and channel=:channel and requestType=:requestType)")
	public List<BusinessGetPageRequest> findLastRequestByEnterpriseIdAndChannelAndRequestType(@Param("enterpriseId") Long businessId, @Param("channel") String channel, @Param("requestType") String requestType);

	@Query("select b from BusinessGetPageRequest b where b.id = (select MAX(id) from BusinessGetPageRequest where resellerId=:resellerId and channel=:channel and requestType=:requestType)")
	public List<BusinessGetPageRequest> findLastRequestByResellerIdIdAndChannelAndRequestType(@Param("resellerId") Long businessId, @Param("channel") String channel, @Param("requestType") String requestType);


	@Query("select b from BusinessGetPageRequest b where b.id = (select MAX(id) from BusinessGetPageRequest where enterpriseId=:enterpriseId and channel=:channel)")
	public List<BusinessGetPageRequest> findLastRequestByEnterpriseIdAndChannel(@Param("enterpriseId") Long businessId, @Param("channel") String channel);

	@Query("select b from BusinessGetPageRequest b where b.id = (select MAX(id) from BusinessGetPageRequest where resellerId=:resellerId and channel=:channel)")
	public List<BusinessGetPageRequest> findLastRequestByResellerIdIdAndChannel(@Param("resellerId") Long resellerId, @Param("channel") String channel);

	@Query("select b from BusinessGetPageRequest b where b.id = (select MAX(id) from BusinessGetPageRequest where enterpriseId=:enterpriseId and status=:status and channel=:channel)")
	public List<BusinessGetPageRequest> findLastRequestByEnterpriseIdAndStatusAndChannel(@Param("enterpriseId") Long businessId, @Param("status") String status, @Param("channel") String channel);

	@Query("select b from BusinessGetPageRequest b where b.id = (select MAX(id) from BusinessGetPageRequest where enterpriseId=:enterpriseId and channel=:channel and status=:status and requestType=:requestType)")
	public List<BusinessGetPageRequest> findLastRequestByEnterpriseIdAndChannelAndUserAndRequestType(@Param("enterpriseId") Long businessId, @Param("channel") String channel, @Param("requestType") String requestType, @Param("status") String status);

	@Query("Select b from BusinessGetPageRequest b where b.freemiumSessionId=:freemiumSessionId")
	BusinessGetPageRequest findByFreemiumSessionId(@Param("freemiumSessionId") Integer freemiumSessionId);

	@Lock(LockModeType.PESSIMISTIC_WRITE)
	@QueryHints({@QueryHint(name = "javax.persistence.lock.timeout", value = "2000")})
	@Transactional
	@Query(value = "SELECT b from BusinessGetPageRequest b where b.id =:id")
    BusinessGetPageRequest fetchById(@Param("id") Integer id);

	@Query(value = "SELECT b.birdeyeUserId from BusinessGetPageRequest b where b.id =:id")
	Integer findBirdeyeUserIdById(@Param("id") Integer id);

	BusinessGetPageRequest findFirstByEnterpriseIdAndChannelAndStatusIn(Long enterpriseId, String channel, List<String> status);


	@Transactional
	@Modifying
	@Query("update BusinessGetPageRequest b set b.status = :status,b.errorLog = :errorLog where b.id in (:requestIds) ")
    void updateByRequestIds(@Param("requestIds") List<Integer> requests,@Param("status") String status,@Param("errorLog") String errorLog);
	@Query("select b from BusinessGetPageRequest b where b.id = (select MAX(id) from BusinessGetPageRequest where resellerId=:resellerId and channel=:channel and requestType=:requestType)")
	BusinessGetPageRequest findLastRequestByResellerIdAndChannelAndRequestType(@Param("resellerId") Long resellerId, @Param("channel") String channel, @Param("requestType") String requestType);


	@Query(value = "SELECT bgp.id AS id ,bgp.resellerId AS resellerId, bgp.requestType AS requestType, bgp.status AS status ,bgp.email AS userEmail"
			+ " FROM BusinessGetPageRequest bgp where bgp.id in (select MAX(b.id) from BusinessGetPageRequest b where b.resellerId=:reseller_id and b.channel=:channel and b.requestType=:request_type)")
	public List<BusinessGetPageReqRepo.BGP> findLatestRequest(@Param("reseller_id") Long resellerId, @Param("channel") String channel, @Param("request_type") String requestType);

	BusinessGetPageRequest findFirstByResellerIdAndChannelAndStatusIn(Long parentId, String name, List<String> asList);
	
	BusinessGetPageRequest findByIdAndStatusAndRequestType(@Param("id") Integer id, @Param("status") String status, @Param("requestType") String requestType);

	// GMB getPages Lite projection
	public static interface BGP {

		public Integer getId();

		public Long getResellerId();

		public String getRequestType();

		public String getStatus();

		public String getUserEmail();
	}

	@Query(value = "select b.email from BusinessGetPageRequest b where b.id =:id")
	String findUserEmailById(@Param("id") Integer id);

	/**
	 * Bulk query to fetch user IDs for multiple enterprises and channels
	 * Returns results with enterpriseId, channel, and userId for bulk processing
	 */
	@Query("SELECT b.enterpriseId, b.channel, b.birdeyeUserId " +
		   "FROM BusinessGetPageRequest b " +
		   "WHERE b.enterpriseId IN :enterpriseIds " +
		   "AND b.channel IN :channels " +
		   "AND b.status IN :statusList")
	List<Object[]> findIdsByEnterpriseIdsAndChannelsAndStatusBulk(
		@Param("enterpriseIds") List<Long> enterpriseIds,
		@Param("channels") List<String> channels,
		@Param("statusList") List<String> statusList);

}


