package com.birdeye.social.dao;


import com.birdeye.social.dto.*;
import com.birdeye.social.entities.BusinessFBPage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Repository
public interface SocialFBPageRepository extends JpaRepository<BusinessFBPage, Integer>, JpaSpecificationExecutor<BusinessFBPage> {
	
	public BusinessFBPage findById(Integer id);

	List<BusinessFBPage> findByIdIn(List<Integer> id);
	
	public List<BusinessFBPage> findByFacebookPageId(String facebookPageId);

	public BusinessFBPage findByFacebookPageIdAndEnterpriseIdAndIsValid(String facebookPageId, Long enterpriseId, Integer isValid);

	public BusinessFBPage findByFacebookPageIdAndResellerIdAndIsValid(String facebookPageId, Long enterpriseId, Integer isValid);

	public List<BusinessFBPage> findByFacebookPageIdAndBusinessIdIsNotNull(String facebookPageId);

	public List<BusinessFBPage> findAllByBusinessIdIn(List<Integer> businessIds);

	@Query(value = "SELECT fb.businessId FROM BusinessFBPage fb WHERE fb.businessId IN :businessIds")
	public List<Integer> findAllIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	public BusinessFBPage findFirstByFacebookPageId(String facebookPageId);
	
	public List<BusinessFBPage> findByEnterpriseIdAndIsSelected(Long enterpriseId, Integer isSelected);

	public Page<BusinessFBPage> findByResellerIdAndIsSelected(Long enterpriseId, Integer isSelected, Pageable pageable);
	public List<BusinessFBPage> findByResellerIdAndIsSelected(Long enterpriseId, Integer isSelected);

	public List<BusinessFBPage> findByResellerIdAndIsSelectedAndBusinessIdIn(Long enterpriseId, Integer isSelected, List<Integer> businessIds);

	public List<BusinessFBPage> findByIsSelected(Integer isSelected);

	public List<BusinessFBPage> findByIsValid(Integer valid);

	public List<BusinessFBPage> findByFacebookPageIdIn(Collection<String> facebookPageIds);

	@Query("Select b from BusinessFBPage b where b.facebookPageId in :facebookPageIds")
	public List<BusinessFBPage> findByFacebookPageIdInWithLimit(@Param("facebookPageIds") Collection<String> facebookPageIds,Pageable page);

	public List<BusinessFBPage> findByEnterpriseIdIn(List<Long> enterpriseIds);

	@Query(value = "Select new com.birdeye.social.dto.FbPagePartialDTO (b.id, b.enterpriseId , b.facebookPageId, b.businessId) " +
			"from BusinessFBPage b where b.enterpriseId is not null and b.isSelected= :isSelected")
	public List<FbPagePartialDTO> findPartialSelectedId(@Param("isSelected") Integer isSelected);


	@Query(value="SELECT page FROM BusinessFBPage page WHERE page.facebookPageId IN :facebookPageIds AND (page.enterpriseId IS NULL OR page.enterpriseId = :enterpriseId)")
	public List<BusinessFBPage> findByFacebookPageIdInAndEnterpriseIdIsNullOrEnterpriseIdIn(List<String> facebookPageIds, Long enterpriseId);

	public List<BusinessFBPage> findByEnterpriseId(Long enterpriseId);

	public List<BusinessFBPage> findByAccountId(Integer accountId);

	public List<BusinessFBPage> findByEnterpriseIdAndFacebookPageIdIn(Long enterpriseId,List<String> facebookPageIds);

	public List<BusinessFBPage> findByResellerIdAndFacebookPageIdIn(Long resellerId,List<String> facebookPageIds);

	public Integer countByEnterpriseIdAndIsSelected(Long enterpriseId, Integer isSelected);

	@Query("Select distinct(fb.facebookPageId) from BusinessFBPage fb where fb.isSelected = 1")
	public List<String> findAllSelectedPageIds();

	@Query("Select distinct(fb.facebookPageId) from BusinessFBPage fb where fb.isSelected = 0")
	public List<String> findAllUnmappedPageIds();

	@Query("Select fb.facebookPageId from BusinessFBPage fb where fb.isValid = 1")
	public List<String> findValidBusinessPageIds();

	@Query("Select fb.facebookPageId from BusinessFBPage fb where fb.isValid = 1")
	public List<String> findValidBusinessPageIds(Pageable pageRequest);

	@Query(value = "Select new com.birdeye.social.dto.DuplicatePageDTO(b.enterpriseId, b.facebookPageId , count(*) as count) from BusinessFBPage b group by b.enterpriseId, b.facebookPageId having count(*) >1")
	public List<DuplicatePageDTO> findDuplicateEntries();

	public List<BusinessFBPage> findByEnterpriseIdAndFacebookPageIdOrderByUpdatedAtDesc(Long enterpriseId, String fbPageId);

	@Query("Select distinct(fb.facebookPageId) from BusinessFBPage fb where fb.isSelected = 1 and fb.enterpriseId = :enterpriseId")
	public List<String> findAllByEnterpriseIdAndSelectedPageIds(@Param("enterpriseId") Long enterpriseId);

	@Query("Select distinct(fb.facebookPageId) from BusinessFBPage fb where fb.enterpriseId = :enterpriseId")
	public List<String> findAllByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

	@Query("Select fb from BusinessFBPage fb where fb.isSelected = 1 and fb.enterpriseId = :enterpriseId and fb.pagePermissions like '%read_insights%'")
	public List<BusinessFBPage> findAllByEnterpriseIdAndInsightsPermission(@Param("enterpriseId") Long enterpriseId);

	@Query("Select fb from BusinessFBPage fb where fb.isSelected = 1 and fb.enterpriseId = :enterpriseId and fb.pagePermissions not like '%read_insights%'")
	public List<BusinessFBPage> findAllByEnterpriseIdAndNoInsightsPermission(@Param("enterpriseId") Long enterpriseId);

	@Query("Select distinct(fb.facebookPageId) from BusinessFBPage fb where fb.isSelected = 1 and fb.enterpriseId = :enterpriseId")
	public Set<String> findAllByEnterpriseIdAndSelectedPageIdsInSet(@Param("enterpriseId") Long enterpriseId);

	@Query("Select distinct(fb.facebookPageId) from BusinessFBPage fb where fb.userId is null")
	public List<String> findAllByUserIdNull();
	
	public List<BusinessFBPage> findByRequestId(String requestId);

	@Query(value = "Select distinct(fb.requestId) from BusinessFBPage fb where fb.requestId in :requestIds")
	List<String> findDistinctRequestIdByRequestIdIn(@Param("requestIds") Set<String> requestIds);

//	@Query("Select fb.facebookPageId, fb.facebookPageName, fb.link, fb.enabled, fb.singleLineAddress, fb.resellerId from BusinessFBPage fb where fb.requestId= :requestId order by fb.createdAt desc")
	public Page<BusinessFBPageLite> findByRequestId(String requestId, Pageable pageable);

	@Query("select a.facebookPageId as pageId , a.facebookPageName as pageName , a.facebookPagePictureUrl as profileImage from BusinessFBPage a where a.facebookPageId = :pageId")
    List<ApprovalPageInfo> findFirstByFacebookPageIdLite(@Param("pageId") String pageId);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessFBPage b SET b.requestId = :requestId where b.facebookPageId in :facebookPageId")
	public int updateRequestId(@Param("requestId") String requestId,@Param("facebookPageId") List<String> facebookPageId);

	public List<BusinessFBPage> findByFacebookPageIdAndIsValid(String pageId, int isValid);
	
	public List<BusinessFBPage> findByPagePermissionsIsNull();
	
	public List<BusinessFBPage> findByEnterpriseIdInAndIsValid(Set<Long> enterpriseId,int isValid);
	
	public boolean existsByEnterpriseIdAndIsSelected(Long enterpriseId, Integer isSelected);

	@Query(value="Select DISTINCT b.enterpriseId  from BusinessFBPage b where  b.enterpriseId is not null and b.isValid = :isValid")
    List<Long> findDistinctEnterpriseIdByIsValid( @Param("isValid" )int isValid);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessFBPage b SET b.enterpriseId = :enterpriseId where b.enterpriseId = :oldEnterpriseId")
	public void updateEnterpriseId(@Param("oldEnterpriseId") Long oldEnterpriseId,@Param("enterpriseId") Long enterpriseId);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessFBPage b SET b.enterpriseId = :enterpriseId, b.accountId = :accountId where b.enterpriseId = :oldEnterpriseId")
	public void updateEnterpriseIdAndAccountId(@Param("oldEnterpriseId") Long oldEnterpriseId,@Param("enterpriseId") Long enterpriseId,@Param("accountId") Integer accountId);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessFBPage b SET b.enterpriseId = :enterpriseId where b.enterpriseId = :oldEnterpriseId AND b.facebookPageId in :pageIds")
	public void updateEnterpriseIdByPageIdIn(@Param("oldEnterpriseId") Long oldEnterpriseId,@Param("enterpriseId") Long enterpriseId,@Param("pageIds") List<String> pageIds);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessFBPage b SET b.enterpriseId = :enterpriseId, b.accountId = :accountId where b.enterpriseId = :oldEnterpriseId AND b.facebookPageId in :pageIds")
	public void updateEnterpriseIdAndAccountIdByPageIdIn(@Param("oldEnterpriseId") Long oldEnterpriseId,@Param("enterpriseId") Long enterpriseId,@Param("pageIds") List<String> pageIds,@Param("accountId") Integer accountId);

	@Query("Select b from BusinessFBPage b where b.businessId in :businessIds order by businessId ASC")
	List<BusinessFBPage> findAllByBusinessIdInWithLimit(@Param("businessIds") List<Integer> businessIds, Pageable pageable);

	@Query("Select distinct(b.businessId) from BusinessFBPage b where b.businessId in :businessIds")
	public List<Integer> countByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	@Transactional
	public Long deleteByEnterpriseId(Long enterpriseId);

	@Transactional
	public void deleteByAccountId(Integer accountId);

	@Query(value="Select b.id  from BusinessFBPage b where  b.facebookPageId in :integrationIds")
    List<Number> findIdByFacebookPageIdIn(@Param("integrationIds") List<String> integrationIds);

    List<BusinessFBPage> findByBusinessId(Integer businessId);

	List<BusinessFBPage> findByBusinessIdIn(Collection<Integer> businessId);

	@Query(value = "Select b.businessId from BusinessFBPage b where b.facebookPageId = :fbPageId AND b.isValid = :isValid")
	List<Integer> findBusinessIdByFacebookPageIdAndIsValid(@Param("fbPageId")String fbPageId, @Param("isValid")Integer isValid);

	@Query(value = "Select distinct(b.facebookPageId) from BusinessFBPage b where b.businessId in :businessId")
	List<String> findDistinctFacebookPageIdByBusinessIdInAndIsValid(@Param("businessId")List<Integer> businessIds);

	@Query("Select distinct(b.userId) from BusinessFBPage b where b.businessId in :businessIds and b.isValid = 1")
	List<String> findDistinctUserIds(@Param("businessIds") List<Integer> businessIds);

	@Query("Select b from BusinessFBPage b where b.userId = :userId and b.facebookPageName is null and b.isValid = 1")
	List<BusinessFBPage> findByFacebookUserIdAndFacebookPageNameIsNull(@Param("userId") String userId);

	List<BusinessFBPage> findByAccountIdAndBusinessIdNotNull(Integer accountId);

	@Query("Select b from BusinessFBPage b where b.enterpriseId = :enterpriseId and b.isValid = :isValid")
	List<BusinessFBPage> findByEnterpriseIdAndIsValid(@Param("enterpriseId") Long enterpriseId,@Param("isValid") int isValid);

	@Query("Select distinct(b.facebookPageId) from BusinessFBPage b where b.enterpriseId = :enterpriseId and b.facebookPageId is not null")
	List<String> findFacebookPageIdByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

	@Transactional
	@Modifying
	@Query("update BusinessFBPage b set b.isValid = 1 , b.validType = 1 , b.pageAccessToken = :accessToken where b.facebookPageId = :pageId")
	void updateAccessToken(@Param("accessToken") String accessToken, @Param("pageId") String pageId);

	@Query("Select b.pageAccessToken from BusinessFBPage b where b.pageAccessToken is not null")
	List<String> getAccessTokens(Pageable pageable);

	List<BusinessFBPage> findFirstByEnterpriseIdAndIsSelectedAndIsValid(Long enterpriseId, Integer isSelected, Integer isValid);

	@Query("Select b from BusinessFBPage b where (b.enterpriseId = :enterpriseId or b.resellerId = :enterpriseId) and b.isSelected = :isSelected and b.isValid = :isValid")
	List<BusinessFBPage> findFirstByEnterpriseIdOrResellerIdAndIsSelectedAndIsValid(@Param("enterpriseId") Long enterpriseId,
																					@Param("isSelected") Integer isSelected,
																					@Param("isValid") Integer isValid,
																					Pageable pageable);

	@Query("Select b from BusinessFBPage b where (b.enterpriseId = :enterpriseId or b.resellerId = :enterpriseId) and b.isSelected = :isSelected and b.isValid = :isValid and b.businessId is not null")
	List<BusinessFBPage> findByEnterpriseIdOrResellerIdAndIsSelectedAndIsValidAndBusinessIdIsNotNull(@Param("enterpriseId") Long enterpriseId,
																					@Param("isSelected") Integer isSelected,
																					@Param("isValid") Integer isValid,
																					Pageable pageable);

	@Query("Select b from BusinessFBPage b where (b.enterpriseId = :enterpriseId or b.resellerId = :enterpriseId) and b.isSelected = :isSelected and b.isValid = :isValid and b.businessId is null")
	List<BusinessFBPage> findByEnterpriseIdOrResellerIdAndIsSelectedAndIsValidAndBusinessIdIsNull(@Param("enterpriseId") Long enterpriseId,
																										@Param("isSelected") Integer isSelected,
																										@Param("isValid") Integer isValid,
																										Pageable pageable);

	@Query("Select b.facebookPageId, b.enterpriseId from BusinessFBPage b where b.facebookPageId in :pageIds")
	List<Object[]> findPageIdAndEnterpriseIdbyPageIds(@Param("pageIds") List<String> pageIds);

	@Query("SELECT B.isValid as isValid, B.businessId as businessId from BusinessFBPage B where B.businessId in (:businessIds)")
	List<SocialBusinessPageInfo> findSocialBusinessPageInfoByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);
	@Query("Select b.businessId from BusinessFBPage b where  b.enterpriseId in :bizHierarchyList and b.businessId is not null")
    List<Integer> findByEnterpriseIdInAndBusinessIdIsNotNull(@Param("bizHierarchyList") List<Long> bizHierarchyList);

	@Query("Select b.businessId from BusinessFBPage b where  b.businessId in :businessIds")
	List<Integer> findByBusinessIds(@Param("businessIds") List<Integer> businessIds);

	@Query("Select ig from BusinessFBPage ig where ig.isValid = 1 and ig.businessId is not null and ig.enterpriseId is not null")
    List<BusinessFBPage> findAllPagesByIsValid(Pageable pageable);

    public static interface BusinessInsightsAndValidity {

		public Integer getIsValid();

		public Integer getBusinessId();

		public String getFacebookPageId();

		public String getPagePermissions();
	}

	@Query("SELECT B.isValid as isValid, B.businessId as businessId, B.facebookPageId as facebookPageId, B.pagePermissions as pagePermissions from BusinessFBPage B where B.isValid = :isValid")
	List<SocialFBPageRepository.BusinessInsightsAndValidity> findBusinessIdsByIsValid(@Param("isValid") Integer isValid);

	List<BusinessFBPage> findByBusinessIdAndFacebookPageId(Integer businessId, String pageId);

	BusinessFBPage findByIdAndBusinessId(Integer id, Integer businessId);

	@Query("Select b from BusinessFBPage b where b.businessId in :businessIds and b.isValid = 0")
	List<BusinessFBPage> findInvalidPagesByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	List<BusinessFBPage> findByUserId(String userId);

	public List<BusinessFBPage> findByUserIdIsNull();

	List<BusinessFBPage> findFirstByIsValid(Integer isValid);

	@Query("Select count(b.id) from BusinessFBPage b where b.businessId in :businessIds and b.isValid = 0")
	int getNumberOfInvalidPages(@Param("businessIds") List<Integer> businessIds);

	@Query("Select b from BusinessFBPage b where b.isValid = 1 and (b.lastScannedOn is null or b.lastScannedOn < :nDaysBeforeTodayDate) and b.businessId is not null ORDER BY b.businessId ASC")
	List<BusinessFBPage> findEarliestExecutedPages(@Param("nDaysBeforeTodayDate") Date nDaysBeforeTodayDate,Pageable pageable);

	@Query("Select b from BusinessFBPage b where b.businessId in :businessIds and b.enabled = 1")
	List<BusinessFBPage> findEnabledPagesByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	List<BusinessFBPage> findByFacebookPageIdAndBusinessIdAndIsValid(String fbPageId, Integer businessId, Integer isValid);

	@Query(value = "SELECT bfp.page_access_token AS accessToken, bfp.business_id AS businessId, bfp.facebook_page_id AS facebookPageId "
			+ "FROM business_fb_page bfp where bfp.business_id in (:businessIds)", nativeQuery = true)
	public List<SocialFBPageRepository.BFP> findByBusinessIdsIn(@Param("businessIds") List<Integer> businessIds);

	@Query(value = "Select COUNT(IF(b.is_valid = 0, 1, NULL)) invalid, COUNT(*) total from business_fb_page b where b.business_id in :businessIds", nativeQuery = true)
	public List<SocialFBPageRepository.IntegrationSummary> getInvalidAndTotalCount(@Param("businessIds") List<Integer> businessIds);

	public List<BusinessFBPage> findByBusinessIdAndIsValid(Integer businessId, Integer isValid);

    boolean existsByResellerIdAndIsSelected(Long resellerId, Integer selected);

	@Query("Select b.facebookPageId from BusinessFBPage b where b.resellerId = :resellerId and b.isValid = 0")
   	List<String> findByResellerIdAndIsInValid(@Param("resellerId") Long resellerId);

	@Query("Select b.facebookPageId from BusinessFBPage b where b.enterpriseId = :enterpriseId and b.isValid = 0")
	List<String> findByEnterpriseIdAndIsInValid(@Param("enterpriseId") Long enterpriseId);

	List<BusinessFBPage> findByResellerId(Long resellerId);

	@Query("Select b.facebookPageId from BusinessFBPage b where b.requestId = :requestId and b.enterpriseId is null and b.resellerId is null")
    List<String> findAllByRequestId(@Param("requestId") String requestId);

    // https://docs.spring.io/spring-data/jpa/docs/current/reference/html/#projections
	public static interface BFP {

		public String getAccessToken();

		public Integer getBusinessId();

		public String getFacebookPageId();
	}

	public static interface IntegrationSummary {

		public Integer getInvalid();

		public Integer getTotal();

	}

	@Query(value = "SELECT bfl.business_id AS businessId, bfl.facebook_page_id AS facebookPageId,bfl.facebook_page_name AS name,"
			+" bfl.page_permissions AS pagePermissions ,bfl.scope AS scope"
			+ " FROM business_fb_page bfl where bfl.business_id in (:businessIds) AND bfl.enterprise_id = (:accountId) AND bfl.is_valid=1 ", nativeQuery = true)
	public List<SocialFBPageRepository.BFLMappingStatus> getValidIntegrations(@Param("accountId") Long accountId, @Param("businessIds") List<Integer> businessIds);

	public static interface BFLMappingStatus{

		public Integer getBusinessId();

		public Integer getFacebookPageId();

		public String getName();

		public String getPagePermissions();

		public String getScope();
	
	}
	public static interface BFL {

		public Integer getBusinessId();

		public Integer getFacebookPageId();

		public String getName();

		public String getPagePermissions();

		public String getScope();

		public Integer getIsManged();

		public Integer getIsValid();

		public Integer getFbErrorSubCode();

	}

	@Query("Select b.pagePermissions AS pagePermissions , b.scope AS scope , b.isValid AS isValid, b.isManaged AS isManaged , b.fbErrorSubcode AS fbErrorSubCode" +
			" from BusinessFBPage b where b.resellerId = :resellerId")
	public List<SocialFBPageRepository.BFL> getFBLiteInfo(@Param("resellerId") Long resellerId);

	public Page<BusinessFBPageLite> findByResellerIdAndBusinessIdIsNull(@Param("resellerId") Long resellerId, Pageable pageable);

	Page<BusinessFBPageLite> findByEnterpriseIdAndBusinessIdIsNull(@Param("enterpriseId") Long enterpriseId, Pageable pageable);

	@Query("select b from BusinessFBPage b where b.resellerId = :resellerId and b.validType in (:validity) and b.isSelected = :isSelected")
	Page<BusinessFBPage> findByResellerIdAndValidityType(@Param("resellerId") Long resellerId, @Param("validity") List<Integer> asList,Pageable pageable,@Param("isSelected") Integer isSelected);

	@Query("select b from BusinessFBPage b where b.resellerId = :resellerId and b.validType in (:validity) and b.isSelected = :isSelected")
	List<BusinessFBPage> findByResellerIdAndValidityType(@Param("resellerId") Long resellerId, @Param("validity") List<Integer> asList,@Param("isSelected") Integer isSelected);

	@Query("select b from BusinessFBPage b where b.resellerId = :resellerId and b.validType in (:validity) and b.isSelected = :isSelected and b.businessId in (:businessIds)")
	List<BusinessFBPage> findByResellerIdAndValidityTypeAndBusinessIdIn(@Param("resellerId") Long resellerId, @Param("validity") List<Integer> asList,@Param("isSelected") Integer isSelected, @Param("businessIds") List<Integer> businessIds);

	@Query("select b.facebookPageId from BusinessFBPage b where b.resellerId = :resellerId and b.validType in (:validity)")
	List<String> findByResellerIdAndValidityType(@Param("resellerId") Long resellerId, @Param("validity") List<Integer> asList);

	boolean existsByBusinessId(@Param("businessId") Integer businessId);

	public static interface MigrationBFL {
		public Integer getId();
		public Long getEnterpriseId();
		public String getRequestId();
	}

	@Query("select b.id as id , b.enterpriseId as enterpriseId, b.requestId as requestId from BusinessFBPage b where b.enterpriseId is not null and b.resellerId is null")
	List<SocialFBPageRepository.MigrationBFL> findByEnterpriseIdIsNotNullAndResellerIdIsNull();

	@Modifying
	@Transactional
	@Query("UPDATE BusinessFBPage b SET b.resellerId = :resellerId where b.enterpriseId in :enterpriseId")
	void updateResellerId(@Param("resellerId") Long resellerId, @Param("enterpriseId") Long enterpriseId);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessFBPage b SET b.userEmailId = :userEmailId where b.id= :id")
	void updateUserEmailId(@Param("userEmailId") String userEmailId, @Param("id") Integer id);

	@Query("select count(b) from BusinessFBPage b where b.resellerId = :resellerId and b.validType in (:validity)")
	Long findCountByResellerIdAndValidityType(@Param("resellerId") Long resellerId, @Param("validity") List<Integer> asList);

	@Query("Select b.facebookPageId from BusinessFBPage b where (b.facebookPageName like %:pageName% or b.singleLineAddress like %:addName%) and b.requestId = :requestId and b.enterpriseId is null and b.resellerId is null")
	List<String> findAllByFBPageName(@Param("pageName") String pageName, @Param("addName") String addName, @Param("requestId") String requestId);

	public static interface BFCreds {

		public Integer getId();

		public String getPageAccessToken();

		public String getFacebookPageId();
	}

	@Query("Select b.id as id, b.pageAccessToken as pageAccessToken, b.facebookPageId as pageId from BusinessFBPage b where b.singleLineAddress is null and b.primaryPhone is null")
	List<SocialFBPageRepository.BFCreds> getNumberOfPagesWithNoPhone();

	@Modifying
	@Transactional
	@Query("update BusinessFBPage b set b.primaryPhone = :phone where b.id = :id")
	void updatePagePhoneNumber(@Param("phone") String phone, @Param("id") Integer id);

	List<BusinessFBPage> findByFacebookPageIdInAndBusinessIdIsNotNull(Collection<String> facebookPageIds);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessFBPage b SET b.requestId = :requestId where b.facebookPageId = :facebookPageId")
	public int updateRequestIdByFacebookPageId(@Param("requestId") String requestId,@Param("facebookPageId") String facebookPageId);

	Page<BusinessFBPage> findByIsValidAndBusinessIdNotNullAndNextSyncDateIsLessThanEqualOrderByNextSyncDateAsc(Integer isValid, Date nextSyncDate, Pageable pageable);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessFBPage b SET b.nextSyncDate = :nextSyncDate where b.id in :ids")
	int updateNextSyncDate(@Param("nextSyncDate") Date nextSyncDate, @Param("ids") List<Integer> id);

	@Query("Select b.businessId from BusinessFBPage b where b.enterpriseId = :enterpriseId and b.isValid = :isValid")
	List<Integer> findBusinessIdsByEnterpriseIdAndIsValid(@Param("enterpriseId") Long enterpriseId, @Param("isValid") Integer isValid);

	@Query(value = "Select distinct(b.facebookPageId) from BusinessFBPage b where b.businessId in :businessId and b.isValid = 1")
	List<String> findDistinctFacebookPageIdByBusinessIdIn(@Param("businessId")List<Integer> businessIds);

	@Query(value = "Select distinct(b.facebookPageId) from BusinessFBPage b where b.businessId in :businessId")
	List<String> findDistinctAllFacebookPageIdByBusinessIdIn(@Param("businessId")List<Integer> businessIds);

	@Query(value = "Select distinct(b.facebookPageId) from BusinessFBPage b where b.businessId in :businessId")
	List<String> findDistinctFacebookPageIdByBusinessIdsIn(@Param("businessId")List<Integer> businessIds);

	@Transactional
	@Modifying
	@Query("update BusinessFBPage b set b.isValid = 1 where b.facebookPageId = :pageId")
	void updateAccessTokenByPageId(@Param("pageId") String pageId);

//	@Query("Select b from BusinessFBPage b where b.isValid = :isValid")
	Page<BusinessFBPage> findByIsValidOrderByIdAsc(@Param("isValid") Integer isValid, Pageable pageable);

	@Query("select distinct b.businessId from BusinessFBPage b where b.businessId is not NULL")
	public Page<Integer> findBusinessIdsNotNull(Pageable page);

	@Transactional
	@Modifying
	@Query("update BusinessFBPage b set b.businessId = null, b.enterpriseId=null, b.isSelected=0 where b.businessId in :invalidBusinessIds")
	void unmapInvalidPage(@Param("invalidBusinessIds") List<Integer> invalidBusinessIds);

	public List<BusinessFBPage> findByFacebookPageIdInAndIsValid(Collection<String> facebookPageIds,Integer isValid);




	@Query("select distinct b.enterpriseId from BusinessFBPage b where b.enterpriseId is NOT NULL")
	public List<Long> findEnterpriseIds(Pageable page);
	@Transactional
	@Modifying
	@Query("update BusinessFBPage b set b.businessId = null, b.enterpriseId=null, b.isSelected=0 where b.enterpriseId in :invalidBusinessIds")
	void unmapSMBInvalidPage(@Param("invalidBusinessIds") Set<Long> invalidBusinessIds);

	@Query(value = "Select b.enterpriseId from BusinessFBPage b where b.facebookPageId = :facebookPageId")
	Long findEnterpriseIdByFacebookPageId(@Param("facebookPageId") String facebookPageId);

	@Query("Select b.facebookPageId from BusinessFBPage b where b.enterpriseId = :enterpriseId and b.isValid = 1 and b.businessId is NOT NULL")
	List<String> findByEnterpriseIdAndIsValidAndMapped(@Param("enterpriseId") Long enterpriseId);

	@Query("Select b.facebookPageId from BusinessFBPage b where b.enterpriseId in :enterpriseIds and b.isValid = 1 and b.businessId is NOT NULL")
	List<String> findPageIdByEnterpriseIdInAndIsValidAndBusinessIdNotNull(@Param("enterpriseIds") List<Long> enterpriseId);

	@Query("Select b from BusinessFBPage b where b.enterpriseId in :enterpriseIds and b.isValid = 1 and b.businessId is NOT NULL")
	List<BusinessFBPage> findByEnterpriseIdInAndIsValidAndBusinessIdNotNull(@Param("enterpriseIds") List<Long> enterpriseId);

	@Query("Select b.facebookPageId from BusinessFBPage b where b.id in :ids")
	public Set<String> findPageIdById(@Param("ids") List<Integer> ids);


	@Query("Select b.facebookPageId from BusinessFBPage b where b.businessId in :businessIds")
	public Set<String> findPageIdByBusinessId(@Param("businessIds") List<Integer> businessIds);
	@Query("select b from BusinessFBPage b where b.facebookPageId in :facebookPageIds and b.isValid = 1 and b.pagePermissions like '%read_insights%'")
	List<BusinessFBPage> findByFacebookPageIdInAndEligible(@Param("facebookPageIds") Collection<String> facebookPageIds);

	@Query("Select b.facebookPageId from BusinessFBPage b where b.businessId in :businessIds")
	public List<BusinessFBPage> findPagesByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	List<BusinessFBPage> findByFacebookPageIdInAndIsSelected(Collection<String> facebookPageId, Integer isSelected);
	@Query("Select b.businessId from BusinessFBPage b where b.resellerId = :resellerId and b.businessId is not null")
	public List<Integer> findByResellerIdAndBusinessIdIsNotNull(@Param("resellerId") Long resellerId);
	@Query(value = "Select b from BusinessFBPage b where b.enterpriseId in :enterpriseId and b.isValid = 1")
	public List<BusinessFBPage> findFacebookPageIdByEnterpriseIdIn(@Param("enterpriseId") List<Long> enterpriseId);


	public static interface PageIdToLocation {

		public Integer getBusinessId();

		public String getFacebookPageId();

		String getFacebookPagePictureUrl();

		String getFacebookPageName();
	}

	@Query(value = "SELECT bfp.business_id AS businessId, bfp.facebook_page_id AS facebookPageId, bfp.facebook_picture_url AS facebookPagePictureUrl, bfp.facebook_page_name AS facebookPageName "
			+ "FROM business_fb_page bfp WHERE bfp.facebook_page_id IN (:pageIds)", nativeQuery = true)
	List<PageIdToLocation> findBusinessIdByPageIdsIn(@Param("pageIds") List<String> pageIds);

	@Query(value = "SELECT fbp.businessId FROM BusinessFBPage fbp where fbp.facebookPageId IN :pageIds")
	Set<Integer> getBusinessIdByPageIdsIn(@Param("pageIds") List<String> pageIds);

	@Query("select count(b) from BusinessFBPage b where b.enterpriseId = :enterpriseId and (b.validType in (:validity) or b.isValid = :isValid))")
	Long findCountByEnterpriseIdAndValidityTypeOrIsValid(@Param("enterpriseId") Long enterpriseId, @Param("validity") List<Integer> asList, @Param("isValid") Integer isValid);

	@Query("SELECT COUNT(b) > 0 FROM BusinessFBPage b WHERE b.enterpriseId = :enterpriseId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
	boolean existsMappedPageByEnterpriseId(@Param("enterpriseId") Long enterpriseId, @Param("businessIds")List<Integer> businessIds);

	@Query("SELECT COUNT(b) > 0 FROM BusinessFBPage b WHERE b.resellerId = :resellerId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
	boolean existsMappedPageByResellerId(@Param("resellerId") Long resellerId, @Param("businessIds")List<Integer> businessIds);



	@Query("SELECT b.facebookPageId FROM BusinessFBPage b")
	List<String> findPageIdsWithPagination(Pageable pageRequest);

}

