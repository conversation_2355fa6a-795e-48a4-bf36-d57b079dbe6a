/**
 * 
 */
package com.birdeye.social.dao;

import com.birdeye.social.constant.GMBLocationJobStatus;
import com.birdeye.social.dto.*;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.sql.Timestamp;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
public interface BusinessGMBLocationRawRepository extends JpaRepository<BusinessGoogleMyBusinessLocation, Integer>, JpaSpecificationExecutor<BusinessGoogleMyBusinessLocation> {

	List<BusinessGoogleMyBusinessLocation> findByEnterpriseIdAndIsSelected(Long enterpriseId, Integer isSelected);
	
	List<BusinessGoogleMyBusinessLocation> findByLocationId(String locationId);

	BusinessGoogleMyBusinessLocation findByBusinessId(Integer businessId);

	BusinessGoogleMyBusinessLocation findByIdAndBusinessId(Integer id, Integer businessId);

	@Transactional
	@Modifying
	@Query("update BusinessGoogleMyBusinessLocation gmb set gmb.agentId = :agentId where gmb.enterpriseId =:enterpriseId")
    void updateAgentIdByEnterpriseId(@Param("agentId") Integer agentId,@Param("enterpriseId") Long enterpriseId);

	@Query("select count(b) from BusinessGoogleMyBusinessLocation b where b.resellerId = :resellerId and b.validType in (:validity)")
	Long findCountByResellerIdAndValidityType(@Param("resellerId") Long resellerId, @Param("validity") List<Integer> asList);

    List<BusinessGoogleMyBusinessLocation> findByAgentIdAndIsValidAndIsSelected(Integer agentId, int isValid, int isSelected);

	List<BusinessGoogleMyBusinessLocation> findByIsValidAndBusinessIdNotNull(Integer isValid);

	@Query("select a.businessId from BusinessGoogleMyBusinessLocation a where a.businessId is not null and a.isValid = 1")
	List<Integer> findBusinessIdsByIsValidAndBusinessIdNotNull();

	@Query(value = "SELECT gmb.businessId FROM BusinessGoogleMyBusinessLocation gmb WHERE gmb.businessId IN :businessIds")
	List<Integer> findAllIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	@Transactional
	@Modifying
	@Query("update BusinessGoogleMyBusinessLocation b set b.agentId = null where b.placeId = :pageId")
	void updateAgentIdToNull(@Param("pageId") String placeId);

	@Query("select a.locationId from BusinessGoogleMyBusinessLocation a where a.enterpriseId in (:enterpriseIds)")
	Page<String> findByEnterpriseIdAndPagable(@Param("enterpriseIds") List<Long> enterpriseIds, Pageable pageRequest);

	@Query("select a.locationId from BusinessGoogleMyBusinessLocation a where a.enterpriseId is not null and a.locationId is not null and a.isValid = 1")
	List<String> findAllLocationIds(Pageable pageRequest);

	@Transactional
	@Modifying
	@Query("update BusinessGoogleMyBusinessLocation b set b.isValid = 0 where b.locationId =:locationId ")
	void updateByLocationId(@Param("locationId") String locationId);

    BusinessGoogleMyBusinessLocation findFirstByAgentIdAndGMsgLocationStatusAndBusinessIdNotNullOrderByBusinessIdAsc(Integer agentId,String status);

	@Query("select distinct(a.locationId) from BusinessGoogleMyBusinessLocation a where a.enterpriseId = :enterpriseId and a.locationId is not null")
	Collection<String> findLocationIdByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

    List<BusinessGoogleMyBusinessLocation> findByLocationIdAndAgentId(String pageId, Integer agentId);

	@Query("Select b.locationId, b.enterpriseId from BusinessGoogleMyBusinessLocation b where b.locationId in :locationIds")
	List<Object[]> findLocationIdAndEnterpriseIdByLocationsIds(@Param("locationIds") List<String> locationIds);

	@Query("select a.locationId as pageId , a.locationName as pageName , a.pictureUrl as profileImage from BusinessGoogleMyBusinessLocation a where a.locationId = :locationId")
    List<ApprovalPageInfo> findByLocationIdLite(@Param("locationId") String pageId);

	@Query("SELECT B.isValid as isValid, B.businessId as businessId from BusinessGoogleMyBusinessLocation B where B.businessId in (:businessIds)")
	List<SocialBusinessPageInfo> findSocialBusinessPageInfoByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	@Query("Select b.businessId from BusinessGoogleMyBusinessLocation b where b.resellerId = :resellerId and b.businessId is not null")
	List<Integer> findByResellerIdAndBusinessIdIsNotNull(@Param("resellerId") Long resellerId);

	List<BusinessGoogleMyBusinessLocation> findByEnterpriseIdIn(Set<Long> invalidBusinessIds);

	@Query("select count(b) from BusinessGoogleMyBusinessLocation b where b.resellerId = :resellerId and b.isValid = :isValid")
	Long findCountByResellerIdAndValidity(@Param("resellerId") Long resellerId, @Param("isValid") Integer isValid);

	@Query("Select b.businessId from BusinessGoogleMyBusinessLocation b where  b.enterpriseId in :bizHierarchyList and b.businessId is not null")
	List<Integer> findByEnterpriseIdInAndBusinessIdIsNotNull(@Param("bizHierarchyList") List<Long> bizHierarchyList);

	Page<BusinessGoogleMyBusinessLocation> findByEnterpriseIdAndBusinessIdIsNull(@Param("enterpriseId") Long enterpriseId, Pageable pageable);

	public static interface AgentDetailsBFL {
		public Integer getAgentId();
		public Integer getBusinessId();

	}
	@Query("select gmb from BusinessGoogleMyBusinessLocation gmb where gmb.agentId in (:agentIds)")
	List<BusinessGoogleMyBusinessLocation> findByAgentIds(@Param("agentIds") Collection agentIds);

	@Query(value="select gmb from BusinessGoogleMyBusinessLocation gmb where gmb.agentId=:agentId")
	List<BusinessGoogleMyBusinessLocation> findByAgentId(@Param("agentId") Integer agentId);

	List<BusinessGoogleMyBusinessLocation> findAllByBusinessId(Integer businessId);

	List<BusinessGoogleMyBusinessLocation> findByBusinessIdIn(Collection<Integer> businessId);
	List<BusinessGoogleMyBusinessLocation> findByBusinessIdInAndIsValidAndIsSelected(Collection<Integer> businessIds, Integer isValid, Integer isSelected);
	List<BusinessGoogleMyBusinessLocation> findByShortAccountIdAndBusinessIdInAndGMsgLocationStatus(Integer accountId,Collection<Integer> businessIds , String gmsgLocationStatus);
	List<BusinessGoogleMyBusinessLocation> findByIsSelected(Integer isSelected);

	@Query(value = "Select new com.birdeye.social.dto.GMBPartialDTO (b.id, b.enterpriseId , b.locationId, b.businessId) " +
			"from BusinessGoogleMyBusinessLocation b where b.enterpriseId is not null and b.isSelected= :isSelected")
	public List<GMBPartialDTO> findPartialSelectedId(@Param("isSelected") Integer isSelected);
	
	List<BusinessGoogleMyBusinessLocation> findByLocationIdIn(Collection<String> locationIds);
	
	@Query("select gmb from BusinessGoogleMyBusinessLocation gmb where gmb.locationId = :locationId")
	BusinessGoogleMyBusinessLocation findGMBByLocationId(@Param("locationId") String locationId);
	
	List<BusinessGoogleMyBusinessLocation> findByEnterpriseIdAndLocationIdIn(Long enterpriseId, List<String> locationIds);
	
	@Transactional
	public Long deleteByLocationId(String locationId);

//	@Transactional
//	public Long deleteByEnterpriseId(Long enterpriseId);

	@Transactional
	public void deleteByShortAccountId(Integer accountId);
	
	@Modifying
	@Transactional
	@Query("UPDATE BusinessGoogleMyBusinessLocation b SET b.isValid = 1 where b.refreshTokenId in :refreshTokenIds")
	public int updateGMBPagesForRefreshToken(@Param("refreshTokenIds") List<Integer> refreshTokenIds);

	@Transactional
	@Modifying
	@Query("Delete from BusinessGoogleMyBusinessLocation b where b.locationId in (:locationIds)")
	int deleteByLocationIdIn(@Param("locationIds") List<String> locationIds);
	
	List<BusinessGoogleMyBusinessLocation> findByRequestId(String requestId);

	List<BusinessGoogleMyBusinessLocation> getGMBPagesByRequestIdAndGmbAccountId(String requestId,Integer accountId);

	List<BusinessGoogleMyBusinessLocation> getGMBPagesByRequestIdAndAccountId(String requestId, String accountId);


	@Query(value = "SELECT bl.locationId AS locationId ,bl.permissions AS permissions,bl.gMsgLocationStatus AS gmsgStatus,bl.gMsgLocationComment AS gmsgComment,bl.accountType AS accountType," +
			"bl.isValid AS isValid ,bl.locationState AS locationState , bl.locationMapUrl AS mapUrl , bl.accountName AS accountName, bl.isSelected as isSelected , bl.locationName AS locationName"
			+ " FROM BusinessGoogleMyBusinessLocation bl where bl.requestId = :requestId and bl.gmbAccountId = :accountId ")
	Page<BusinessGMBLocationRawRepository.RL> getGMBPagesByRequestIdAndGmbAccountIdLite(@Param("requestId") String requestId, @Param("accountId") Integer accountId, Pageable pageable);


	@Query(value="Select b from BusinessGoogleMyBusinessLocation b where b.locationId = :locationId and b.enterpriseId IS NOT NULL and b.isSelected = 1")
	List<BusinessGoogleMyBusinessLocation> findByLocationIdAndIsSelected(@Param("locationId" ) String locationId);
	
	BusinessGoogleMyBusinessLocation findFirstByPlaceIdAndIsValid(String placeId, Integer isValid);

	List<BusinessGoogleMyBusinessLocation> findByEnterpriseId(Long enterpriseId);

	List<BusinessGoogleMyBusinessLocation> findByShortAccountId(Integer accountId);

	List<BusinessGoogleMyBusinessLocation> findByLocationIdAndIsValid(String locationId, Integer isValid);

	List<BusinessGoogleMyBusinessLocation> findByEnterpriseIdAndIsValid(Long enterpriseId, Integer isValid);

	Boolean existsByEnterpriseIdAndIsValidAndGMsgLocationStatus(Long enterpriseId, Integer isValid, String gmsgLocationStatus);

	Boolean existsByAgentIdAndIsValidAndGMsgLocationStatus(Integer agentId, Integer isValid, String gmsgLocationStatus);
	
	@Query(value="Select DISTINCT b.enterpriseId  from BusinessGoogleMyBusinessLocation b where  b.enterpriseId is not null and b.isValid = :isValid")
    List<Long> findDistinctEnterpriseIdByIsValid(@Param("isValid" )int isValid);

    /*
	*Multiple entries exist with same placeid as GMB seems to generate different location ids across user accounts
	*/
	List<BusinessGoogleMyBusinessLocation> findByPlaceIdAndIsValid(String placeId, Integer isValid);

	List<BusinessGoogleMyBusinessLocation> findByRefreshTokenId(Integer integer);
	
	@Query(value="Select new com.birdeye.social.dto.BusinessGMBLocationWithPageId (b.locationId, b.refreshTokenId,b.placeId) from BusinessGoogleMyBusinessLocation b where b.placeId IN :placeIds and b.enterpriseId is not null")
	List<BusinessGMBLocationWithPageId> findAllByPlaceIds(@Param("placeIds" ) List<String> placeIds);


	@Query(value="Select b.placeId from BusinessGoogleMyBusinessLocation b where b.locationId = :locationId and b.isValid = :isValid")
	String findPlaceIdByLocationIdAndIsValid(@Param("locationId") String locationId, @Param("isValid") int isValid);

	@Query(value="Select new com.birdeye.social.dto.BusinessGMBNotificationDTO (b.enterpriseId,b.accountId,b.locationId,b.refreshTokenId) from BusinessGoogleMyBusinessLocation b where b.enterpriseId = :enterpriseId and b.isValid = :isValid group by accountId")
	List<BusinessGMBNotificationDTO> findDistinctAccountIdsByEnterpriseId(@Param("enterpriseId")Long enterpriseId,@Param("isValid") Integer isValid);

	@Query(value="Select new com.birdeye.social.dto.BusinessGMBNotificationDTO (b.enterpriseId,b.accountId,b.locationId,b.refreshTokenId) from BusinessGoogleMyBusinessLocation b where b.locationId = :locationId")
	BusinessGMBNotificationDTO getBusinessGMBNotificationByLocationId(@Param("locationId")String locationId);

	@Query(value="Select new com.birdeye.social.dto.BusinessGMBNotificationDTO (b.enterpriseId,b.accountId,b.locationId,b.refreshTokenId) from BusinessGoogleMyBusinessLocation b where b.enterpriseId is not null  and b.isValid = 1 group by accountId")
	List<BusinessGMBNotificationDTO> getDistinctAccountId();

	@Query(value="Select b from BusinessGoogleMyBusinessLocation b where b.enterpriseId is not null and b.enterpriseId != :businessId and b.accountId = :accountId and b.isValid = 1")
	List<BusinessGoogleMyBusinessLocation> findByAccountIdExcludingEnterprise(@Param("accountId") String accountId,@Param("businessId") Long businessId);
	
	List<BusinessGoogleMyBusinessLocation> findByAccountIdAndLocationIdNotAndIsValid( String accountId, String locationId, int isValid);

	@Query(value="Select DISTINCT b.refreshTokenId from BusinessGoogleMyBusinessLocation b where    (b.locationSyncStatus !='IN_PROGRESS' OR b.locationSyncStatus IS NULL) AND b.isValid = 1 and b.refreshTokenId IS NOT NULL ORDER BY b.nextSync")
	List<Integer> findExecutableRefreshTokenIds(Pageable p);
	
	@Query(value="Select b.locationId from BusinessGoogleMyBusinessLocation b where  b.isValid = 1 AND  b.refreshTokenId = :refreshTokenId	 AND b.accountId = :accountId")
	List<String> findLocationIdByRefreshTokenIdAndAccountId(@Param("refreshTokenId")Integer refreshTokenId,@Param("accountId")String accountId);
	
	@Query(value="Select b from BusinessGoogleMyBusinessLocation b where  b.isValid = 1 AND  b.refreshTokenId = :refreshTokenId	 AND b.accountId = :accountId")
	List<BusinessGoogleMyBusinessLocation> findByRefreshTokenIdAndAccountId(@Param("refreshTokenId")Integer refreshTokenId,@Param("accountId")String accountId);
	 
	@Transactional
	@Query("UPDATE BusinessGoogleMyBusinessLocation b SET b.locationSyncStatus = :locationSyncStatus , b.nextSync = :nextSync  where b.refreshTokenId = :refreshTokenId")
	public void updateSyncDataForLocation(@Param("refreshTokenId") Integer refreshTokenId,@Param("nextSync") Date nextSync,@Param("locationSyncStatus") GMBLocationJobStatus locationSyncStatus);

	
	@Query(value="Select b from BusinessGoogleMyBusinessLocation b where  b.isValid = 1 AND b.refreshTokenId =:refreshTokenId")
	List<BusinessGoogleMyBusinessLocation> findLocationsByRefreshTokenId(@Param("refreshTokenId")Integer refreshTokenId);

	@Query(value = "Select distinct(b.locationId) from BusinessGoogleMyBusinessLocation b where b.businessId in :businessId")
	List<String> findDistinctGmbLocationIdByBusinessIdInAndIsValid(@Param("businessId")List<Integer> businessIds);


	@Modifying
	@Transactional
	@Query("UPDATE BusinessGoogleMyBusinessLocation b SET b.locationSyncStatus = :locationSyncStatus where b.refreshTokenId = :refreshTokenId AND b.locationId=:locationId")
	public void updateGMBPageForLocation(@Param("refreshTokenId") Integer refreshTokenId,@Param("locationId") String locationId,@Param("locationSyncStatus") GMBLocationJobStatus locationSyncStatus);

	@Query(value="Select COUNT(DISTINCT b.accountId) from BusinessGoogleMyBusinessLocation b where  b.locationSyncStatus = :locationSyncStatus ")
	Integer countDistinctAccountIdByLocationSyncStatus( @Param("locationSyncStatus")GMBLocationJobStatus locationSyncStatus);


	@Modifying
	@Transactional
	@Query("UPDATE BusinessGoogleMyBusinessLocation b SET b.locationSyncStatus = :locationSyncStatus, nextSync = :nextSync where b.refreshTokenId = :refreshTokenId AND b.locationId in :locationIds")
	public void updateStatusNextSyncDateByLocationId(@Param("refreshTokenId") Integer refreshTokenId,@Param("locationIds") List<String> locationIds,@Param("locationSyncStatus") GMBLocationJobStatus locationSyncStatus, @Param("nextSync") Timestamp nextSync);

	public boolean existsByEnterpriseIdAndIsSelected(Long enterpriseId, Integer isSelected);

	public boolean existsByEnterpriseId(Long enterpriseId);

	@Query("Select distinct(b.placeId) from BusinessGoogleMyBusinessLocation b where b.isSelected = 1 and b.enterpriseId = :enterpriseId")
	public List<String> findAllByEnterpriseIdAndSelectedPageIds(@Param("enterpriseId") Long enterpriseId);

	@Query("Select distinct(b.placeId) from BusinessGoogleMyBusinessLocation b where b.isSelected = 1 and b.enterpriseId = :enterpriseId")
	public Set<String> findAllByEnterpriseIdAndSelectedPageIdsInSet(@Param("enterpriseId") Long enterpriseId);

	@Query("Select b from BusinessGoogleMyBusinessLocation b where b.enterpriseId = :enterpriseId " +
			"AND (b.gMsgLocationStatus like '%ERROR' OR b.isValid = 0 OR b.isVerified = 0)")
	Set<BusinessGoogleMyBusinessLocation> findAllErroneousPageIdsByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

	@Query("Select b from BusinessGoogleMyBusinessLocation b where b.agentId = :agentId " +
			"AND (b.gMsgLocationStatus like '%ERROR' OR b.isValid = 0 OR b.isVerified = 0) AND b.businessId is not null ")
	Set<BusinessGoogleMyBusinessLocation> findAllErroneousPageIdsByAgentId(@Param("agentId") Integer agentId);

	@Query("Select b from BusinessGoogleMyBusinessLocation b where b.agentId = :agentId " +
			"AND (b.gMsgLocationStatus != 'LAUNCHED' OR b.isValid = 0 OR b.isVerified = 0) AND b.businessId is not null ")
	Set<BusinessGoogleMyBusinessLocation> findAllNotLaunchedPageIdsByAgentId(@Param("agentId") Integer agentId);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessGoogleMyBusinessLocation B " +
			"SET B.isMessagingEnabled = :isMessagingEnabled " +
			"WHERE B.enterpriseId = :enterpriseId")
	void updateMessengerEnabledForEnterpriseId(@Param("enterpriseId") Long enterpriseId,
											   @Param("isMessagingEnabled") Integer isMessagingEnabled);


	@Query(value = "SELECT a.* " +
			"FROM business_google_mybusiness_location a " +
			"JOIN (SELECT c.refresh_token_id, max(c.updated_at) as last_updated " +
				"from business_google_mybusiness_location c " +
				"where c.enterprise_id = :enterpriseId " +
				"and c.is_valid = :isValid and c.is_selected = :isSelected " +
				"group by c.refresh_token_id) b " +
			"ON a.refresh_token_id = b.refresh_token_id " +
			"where a.updated_at = b.last_updated " +
			"and a.enterprise_id = :enterpriseId " +
			"and a.is_valid = :isValid " +
			"and a.is_selected = :isSelected " +
			"order by a.updated_at desc limit :limit", nativeQuery = true)
	List<BusinessGoogleMyBusinessLocation> findLastUpdatedWithDistinctRefreshTokenId(@Param("enterpriseId") Long enterpriseId,
																					 @Param("isValid") Integer isValid,
																					 @Param("isSelected") Integer isSelected,
																					 @Param("limit") Integer limit);

	@Query(value = "SELECT a.* " +
			"FROM business_google_mybusiness_location a " +
			"JOIN (SELECT c.refresh_token_id, max(c.updated_at) as last_updated " +
			"from business_google_mybusiness_location c " +
			"where c.agent_id = :agentId " +
			"and c.is_valid = :isValid and c.is_selected = :isSelected " +
			"group by c.refresh_token_id) b " +
			"ON a.refresh_token_id = b.refresh_token_id " +
			"where a.updated_at = b.last_updated " +
			"and a.agent_id = :agentId " +
			"and a.is_valid = :isValid " +
			"and a.is_selected = :isSelected " +
			"order by a.updated_at desc limit :limit", nativeQuery = true)
	List<BusinessGoogleMyBusinessLocation> findLastUpdatedWithDistinctRefreshTokenIdWithAgentId(@Param("agentId") Integer agentId,
																					 @Param("isValid") Integer isValid,
																					 @Param("isSelected") Integer isSelected,
																					 @Param("limit") Integer limit);

	List<BusinessGoogleMyBusinessLocation> findByEnterpriseIdAndIsValidAndIsSelected(@Param("enterpriseId") Long enterpriseId,
																				   @Param("isValid") Integer isValid,
																				   @Param("isSelected")Integer isSelected);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessGoogleMyBusinessLocation b SET b.enterpriseId = :enterpriseId where b.enterpriseId = :oldEnterpriseId")
	public void updateEnterpriseId(@Param("oldEnterpriseId") Long oldEnterpriseId,@Param("enterpriseId") Long enterpriseId);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessGoogleMyBusinessLocation b SET b.enterpriseId = :enterpriseId, b.shortAccountId= :accountId where b.enterpriseId = :oldEnterpriseId")
	public void updateEnterpriseIdAndAccountId(@Param("oldEnterpriseId") Long oldEnterpriseId,@Param("enterpriseId") Long enterpriseId,@Param("accountId") Integer accountId);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessGoogleMyBusinessLocation b SET b.enterpriseId = :enterpriseId, b.shortAccountId= :accountId where b.enterpriseId = :oldEnterpriseId AND b.locationId in :locationIds")
	public void updateEnterpriseIdAndAccountIdByLocationIdIn(@Param("oldEnterpriseId") Long oldEnterpriseId,@Param("enterpriseId") Long enterpriseId,@Param("locationIds") String locationIds,@Param("accountId") Integer accountId);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessGoogleMyBusinessLocation b SET b.enterpriseId = :enterpriseId where b.enterpriseId = :oldEnterpriseId AND b.locationId in :locationIds")
	public void updateEnterpriseIdByLocationIdIn(@Param("oldEnterpriseId") Long oldEnterpriseId,@Param("enterpriseId") Long enterpriseId,@Param("locationIds") String locationIds);

//	@Modifying
//	@Transactional
//	public Long deleteByEnterpriseIdAndLocationIdNotIn(Long enterpriseId,List<String> locationIds);

	@Modifying
	@Transactional
	public void deleteByShortAccountIdAndLocationIdNotIn(Integer accountId,List<String> locationIds);

	List<BusinessGoogleMyBusinessLocation> findByPlaceIdIn(List<String> placeIds);

	@Query("Select b.id from BusinessGoogleMyBusinessLocation b where b.locationId in :locationIds")
    List<Number> findIdByLocationIn(@Param("locationIds") List<String> locationIds);

    List<BusinessGoogleMyBusinessLocation> findByIdIn(List<Integer> rawIds);


		BusinessGoogleMyBusinessLocation findFirstByLocationIdAndIsValid(String locationId, int isValid);

    BusinessGoogleMyBusinessLocation findFirstByLocationId(String locationId);

	@Query(value = "Select COUNT(IF(b.is_valid = 0, 1, NULL)) invalid, COUNT(*) total from business_google_mybusiness_location b where b.business_id in :businessIds", nativeQuery = true)
	public List<BusinessGMBLocationRawRepository.IntegrationSummary> getInvalidAndTotalCount(@Param("businessIds") List<Integer> businessIds);

	@Query(value="Select new com.birdeye.social.dto.BusinessGMBLocationAggregation (b.locationId, b.refreshTokenId, b.businessId) from BusinessGoogleMyBusinessLocation b where b.placeId = :placeId and b.enterpriseId is not null")
	List<BusinessGMBLocationAggregation> findByPlaceId(@Param("placeId" ) String placeId);

	@Query(value="select b from BusinessGoogleMyBusinessLocation b where b.businessId = :businessId and b.locationId= :locationId and b.isValid = 1")
	List<BusinessGoogleMyBusinessLocation> findByLocationIdAndBusinessIdAndIsValid(@Param("businessId") Integer businessId, @Param("locationId") String locationId);

	@Query(value="select b.businessId from BusinessGoogleMyBusinessLocation b where b.locationId= :locationId")
	List<Integer> findBusinessByLocationId(@Param("locationId") String locationId);

	@Query("Select count(b.id) from BusinessGoogleMyBusinessLocation b where b.businessId in :businessIds and b.isValid = 0")
	public int getNumberOfInvalidPages(@Param("businessIds") List<Integer> businessIds);

	@Query("Select b from BusinessGoogleMyBusinessLocation b where b.businessId in :businessIds and b.isValid = 0")
	public List<BusinessGoogleMyBusinessLocation> findInvalidPagesByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	@Query("select distinct b.businessId from BusinessGoogleMyBusinessLocation b where b.isValid = :isValid")
	public List<Integer> findBusinessIdsByIsValid(@Param("isValid")Integer isValid);

	BusinessGoogleMyBusinessLocation findByBusinessIdAndIsValid(Integer businessId,Integer isValid);

	@Query("Select b from BusinessGoogleMyBusinessLocation b where b.businessId in :businessIds")
	public List<BusinessGoogleMyBusinessLocation> findAllByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	BusinessGoogleMyBusinessLocation findGMBByPlaceId(String placeId);

	Page<BusinessGoogleMyBusinessLocation> findByRequestId(String requestId,Pageable page);

	List<BusinessGoogleMyBusinessLocation> findByResellerIdAndLocationIdIn(Long resellerId, List<String> locationIds);

	Page<BusinessGoogleMyBusinessLocation> findByResellerIdAndIsSelected(Long enterpriseId, Integer i,Pageable page);

	List<BusinessGoogleMyBusinessLocation> findByResellerIdAndIsSelected(Long resellerId, int i);

	public boolean existsByResellerIdAndIsSelected(Long resellerId, Integer isSelected);

	@Query("select distinct bl.accountId as accountId,bl.refreshTokenId as refreshTokenId from BusinessGoogleMyBusinessLocation bl where bl.enterpriseId is not null and bl.isSelected = 1")
	List<BusinessGMBLocationRawRepository.BL> findAllByAccountId();

	@Query("select distinct bl.accountId as accountId,bl.refreshTokenId as refreshTokenId from BusinessGoogleMyBusinessLocation bl where bl.accountId in :accountId")
	List<BL> findByAccountIds(@Param("accountId") List<String> accountIds);


	@Transactional
	@Modifying
	@Query("update BusinessGoogleMyBusinessLocation bl set bl.isValid = :isValid , bl.isVerified = :isVerified, " +
			"bl.locationState = :locationState, bl.validType = :validType,bl.canPost = :canPost where bl.locationId = :locationId")
	void updateGMBPage(@Param("isValid") Integer isValid,@Param("isVerified")  Integer isVerified,
					   @Param("locationState")  String locationState,@Param("validType")  Integer validType,
					   @Param("locationId")  String locationId,@Param("canPost") Integer canPost);

	List<BusinessGoogleMyBusinessLocation> findByEnterpriseIdAndBusinessIdNotNull(Long enterpriseId);

	List<BusinessGoogleMyBusinessLocation> findByShortAccountIdAndBusinessIdNotNull(Integer accountId);

	@Query("select b.placeId from BusinessGoogleMyBusinessLocation b where b.enterpriseId = :enterpriseId and b.gMsgLocationName is not null")
	List<String> findPlaceIdsByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

	@Query("select b.placeId from BusinessGoogleMyBusinessLocation b where b.enterpriseId = :enterpriseId and b.gMsgLocationName is not null and b.businessId is not null")
	List<String> findPlaceIdsForEnterpriseId(@Param("enterpriseId") Long enterpriseId);

    List<BusinessGoogleMyBusinessLocation> findFirstByPlaceIdInAndIsValid(List<String> pageIds, Integer isValid);

	@Transactional
	@Modifying
	@Query("update BusinessGoogleMyBusinessLocation b set b.gMsgLocationStatus = :name where b.placeId in :pageIds ")
	void updateLocationStatus(@Param("pageIds") List<String> pageIds,@Param("name") String name);

	@Transactional
	@Modifying
	@Query("update BusinessGoogleMyBusinessLocation b set b.gMsgLocationStatus = :name , b.agentId = null where b.placeId = :pageIds ")
	void updateLocationStatusAndAgentId(@Param("pageIds") String pageIds,@Param("name") String name);

	BusinessGoogleMyBusinessLocation findFirstByEnterpriseIdAndPlaceIdAndIsValidAndBusinessIdIsNull(Long enterpriseId,String profileId, int isValid);

	List<BusinessGoogleMyBusinessLocation> findByEnterpriseIdAndIsValidAndIsSelectedAndAgentIdAndBusinessIdIsNotNull(Long enterpriseId, Integer isValid, Integer isSelected,Integer agentId);

	List<BusinessGoogleMyBusinessLocation> findByEnterpriseIdAndIsValidAndIsSelectedAndBusinessIdIsNotNull(Long enterpriseId, Integer isValid, Integer isSelected);

	@Query("select distinct(a.agentId) from BusinessGoogleMyBusinessLocation a where a.resellerId = :parentId")
	Set<Integer> findDistinctAgentIdByResellerId(@Param("parentId") Long parentId);


	@Query("select distinct(a.agentId) from BusinessGoogleMyBusinessLocation a where a.enterpriseId = :parentId")
	Set<Integer> findDistinctAgentIdByEnterpriseId(@Param("parentId") Long parentId);

	@Transactional
	@Modifying
	@Query("update BusinessGoogleMyBusinessLocation a set a.gMsgLocationStatus = :locationStatus,a.agentId = null where a.agentId = :agentId")
    void updateLocationStateForPages(@Param("agentId") Integer agentId,@Param("locationStatus") String locationStatus);

	@Query("select a.placeId from BusinessGoogleMyBusinessLocation a where a.agentId = :agentId and a.isValid = 1")
	List<String> findPlaceIdByAgentIdAndIsSelected(@Param("agentId") Integer agentId);

	public static interface BL{
		public String getAccountId();
		public Integer getRefreshTokenId();
	}


	@Query("Select distinct(b.enterpriseId) from BusinessGoogleMyBusinessLocation b where b.resellerId = :parentId")
    List<Long> findEnterpriseIds(@Param("parentId") Long parentId);

	@Query("select b.locationId from BusinessGoogleMyBusinessLocation b where b.requestId = :requestId and b.enterpriseId is null and b.resellerId is null")
    List<String> findAllLocationIdsByRequestId(@Param("requestId") String requestId);

	List<BusinessGoogleMyBusinessLocation> findByResellerId(Long parentId);

	@Query("Select b.locationId from BusinessGoogleMyBusinessLocation b where b.resellerId = :parentId and b.isValid = 0")
	List<String> findByResellerIdAndIsValid(@Param("parentId") Long parentId,Pageable page);

	List<BusinessGoogleMyBusinessLocation> findAllByLocationIdIn(List<String> locationIds, Pageable page);

	List<BusinessGoogleMyBusinessLocation> findAllByLocationIdIn(Collection<String> locationIds);

	@Query(value = "SELECT bl.locationId AS locationId ,bl.permissions AS permissions,bl.gMsgLocationStatus AS gmsgStatus,bl.gMsgLocationComment AS gmsgComment,bl.accountType AS accountType," +
			"bl.isValid AS isValid ,bl.locationState AS locationState , bl.locationMapUrl AS mapUrl , bl.accountName AS accountName, bl.isSelected as isSelected , bl.locationName AS locationName"
			+ " FROM BusinessGoogleMyBusinessLocation bl where bl.resellerId = :resellerId and bl.requestId = :requestId")
	Page<BusinessGMBLocationRawRepository.RL> findByResellerIdAndIsSelectedAndRequestIdAndIsAdded(@Param("resellerId") Long resellerId,@Param("requestId") String requestId,Pageable pageable);

	@Query("select b.locationId from BusinessGoogleMyBusinessLocation b where b.resellerId = :resellerId and b.validType in (:validTypes)")
	List<String> findByResellerIdAndValidType(@Param("resellerId") Long resellerId,@Param("validTypes") Collection<Integer> validityType );

	@Query("select b from BusinessGoogleMyBusinessLocation b where b.resellerId = :resellerId and b.validType in (:validity) and b.isSelected = :isSelected")
	Page<BusinessGoogleMyBusinessLocation> findByResellerIdAndValidityType(@Param("resellerId") Long resellerId, @Param("validity") List<Integer> asList,Pageable pageable,@Param("isSelected") Integer isSelected);


	// https://docs.spring.io/spring-data/jpa/docs/current/reference/html/#projections
	public static interface IntegrationSummary {

		public Integer getInvalid();

		public Integer getTotal();

	}

	@Query(value = "SELECT bgl.business_id AS businessId, bgl.is_valid AS valid, bgl.location_id AS locationId ,"
			+ "bgl.place_id AS placeId,bgl.permissions AS permissions,bgl.gmsg_location_status AS gmsgStatus"
			+ " FROM business_google_mybusiness_location bgl where bgl.business_id in (:businessIds) AND bgl.enterprise_id = :accountId AND bgl.is_valid = 1 ", nativeQuery = true)
	public List<BusinessGMBLocationRawRepository.BGL> getValidIntegrations(@Param("accountId") Long accountId,
			@Param("businessIds") List<Integer> businessIds);

	// GMB Lite projection
	public static interface BGL {

		public String getValid();

		public Integer getBusinessId();

		public Integer getLocationId();

		public Integer getPlaceId();

		public String getPermissions();

		public String getGmsgStatus();
	}

	@Query(value =  "SELECT bgl.placeId FROM BusinessGoogleMyBusinessLocation bgl where bgl.businessId in (:business_ids)")
	public List<String> getPlaceIdsForBusinesses(@Param("business_ids") List<Integer> business_ids);

	@Query(value = "SELECT bl.locationId AS locationId ,bl.permissions AS permissions,bl.gMsgLocationStatus AS gmsgStatus,bl.gMsgLocationComment AS gmsgComment," +
			"bl.isValid AS isValid ,bl.locationState AS locationState"
			+ " FROM BusinessGoogleMyBusinessLocation bl where bl.resellerId = :parentId")
	public List<BusinessGMBLocationRawRepository.RL> getBusinessGoogleMyBusinessLocation(@Param("parentId") Long parentId);

	@Query(value = "SELECT bl.locationId AS locationId ,bl.permissions AS permissions,bl.gMsgLocationStatus AS gmsgStatus,bl.gMsgLocationComment AS gmsgComment,bl.accountType AS accountType," +
			"bl.isValid AS isValid ,bl.locationState AS locationState , bl.locationMapUrl AS mapUrl , bl.accountName AS accountName, bl.isSelected as isSelected , bl.locationName AS locationName"
			+ " FROM BusinessGoogleMyBusinessLocation bl where bl.requestId = :requestId")
	public Page<BusinessGMBLocationRawRepository.RL> getPagesByRequestId(@Param("requestId") String requestId,Pageable pageable);
	//Reseller Lite
	public static interface RL {

		public String getLocationId();

		public String getLocationName();

		public String getAccountName();

		public String getMapUrl();

		public String getSingleLineAddress();

		public String getPermissions();

		public String getGmsgStatus();

		public String getGmsgComment();

		public Integer getIsValid();

		public String getLocationState();

		public Integer getIsSelected();

		public String getAccountType();
	}

	Page<BusinessGoogleMyBusinessLocation> findByResellerIdAndBusinessIdIsNull(@Param("resellerId") Long resellerId, Pageable pageable);

	boolean existsByBusinessId(@Param("businessId") Integer businessId);

	public static interface MigrationBFL {
		public Integer getId();
		public Long getEnterpriseId();
		public String getRequestId();
	}

	@Query("select  b.id as id , b.enterpriseId as enterpriseId, b.requestId as requestId from BusinessGoogleMyBusinessLocation b where b.enterpriseId is not null and b.resellerId is null")
	List<BusinessGMBLocationRawRepository.MigrationBFL> findByEnterpriseIdIsNotNullAndResellerIdIsNull();


	@Modifying
	@Transactional
	@Query("UPDATE BusinessGoogleMyBusinessLocation b SET b.resellerId = :resellerId where b.enterpriseId in :enterpriseId")
	void updateResellerId(@Param("resellerId") Long resellerId, @Param("enterpriseId") Long enterpriseId);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessGoogleMyBusinessLocation b SET b.emailId = :userEmailId where b.id= :id")
	void updateUserEmailId(@Param("userEmailId") String userEmailId, @Param("id") Integer id);

	@Query("select b.locationId from BusinessGoogleMyBusinessLocation b where (b.locationName like %:pageName% or b.singleLineAddress like %:addName%) and b.requestId = :requestId and b.enterpriseId is null and b.resellerId is null")
	List<String> findAllByGMBPageName(@Param("pageName") String pageName, @Param("addName") String addName, @Param("requestId") String requestId);

	List<BusinessGoogleMyBusinessLocation> findByLocationIdInAndBusinessIdIsNotNull(Collection<String> locationIds);

	Page<BusinessGoogleMyBusinessLocation> findByIsValidAndBusinessIdNotNullAndReportNextSyncDateIsLessThanEqualOrderByReportNextSyncDateAsc(Integer isValid, Date reportNextSyncDate, Pageable pageable);
   @Modifying
	@Transactional
	@Query("UPDATE BusinessGoogleMyBusinessLocation b SET b.reportNextSyncDate = :nextSyncDate where b.id in :ids")
	int updateReportNextSyncDate(@Param("nextSyncDate") Date nextSyncDate, @Param("ids") List<Integer> id);


	BusinessGoogleMyBusinessLocation findById(Integer id);

	BusinessGoogleMyBusinessLocation findByLocationIdAndEnterpriseIdAndIsValid(String locationId, Long enterpriseId, Integer isValid);

	@Query(value =  "SELECT bgl.businessId FROM BusinessGoogleMyBusinessLocation bgl where bgl.enterpriseId= :enterpriseId")
	List<Integer> findBusinessIdsByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

	@Query(value = "Select b from BusinessGoogleMyBusinessLocation b where b.agentId = :agentId and b.businessId is not null and b.isSelected = :isSelected ")
	List<BusinessGoogleMyBusinessLocation> findByAgentIdAndIsSelected(@Param("agentId") Integer agentId,@Param("isSelected") Integer isSelected);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessGoogleMyBusinessLocation b SET b.agentId = :agentId where b.businessId in :ids")
	int updateAgentIdByBusinessIds(@Param("agentId") Integer agentId, @Param("ids") List<Integer> ids);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessGoogleMyBusinessLocation b SET b.agentId = null where b.agentId=:agentId")
	void updateAgentIdByBusinessIdsNull(@Param("agentId") Integer agentId);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessGoogleMyBusinessLocation b SET b.agentId = :agentId where b.businessId in :ids")
	void updateAgentIdByBusinessIdsIn(@Param("agentId") Integer agentId, @Param("ids") List<Integer> ids);


	@Query(value = "Select distinct(b.locationId) from BusinessGoogleMyBusinessLocation b where b.businessId in :businessId and b.isValid = 1")
	List<String> findDistinctGmbLocationIdByBusinessIdIn(@Param("businessId")List<Integer> businessIds);

	@Query("select distinct b.businessId from BusinessGoogleMyBusinessLocation b where b.businessId is not NULL")
	public Page<Integer> findBusinessIdsNotNull(Pageable page);

	@Transactional
	@Modifying
	@Query("update BusinessGoogleMyBusinessLocation b set b.businessId = null, b.enterpriseId=null, b.isSelected=0 where b.businessId in :invalidBusinessIds")
	void unmapInvalidPage(@Param("invalidBusinessIds") List<Integer> invalidBusinessIds);

	Page<BusinessGoogleMyBusinessLocation> findByBusinessIdNotNullAndIsValidOrderByIdDesc(Integer isValid, Pageable page);


	Page<BusinessGoogleMyBusinessLocation> findByIsValidAndNextSyncIsLessThanEqualAndBusinessIdIsNotNullOrderByNextSyncAsc(Integer isValid, Date nextSyncDate, Pageable pageable);


	@Query("Select distinct(b.enterpriseId) from BusinessGoogleMyBusinessLocation b where b.enterpriseId is NOT NULL")
	List<Long> findEnterpriseId(Pageable page);

	@javax.transaction.Transactional
	@Modifying
	@Query("update BusinessGoogleMyBusinessLocation b set b.businessId = null, b.enterpriseId=null, b.isSelected=0 where b.enterpriseId in :invalidBusinessIds")
	void unmapSMBInvalidPage(@Param("invalidBusinessIds") Set<Long> invalidBusinessIds);

	@Query(value = "Select distinct(b.locationId) from BusinessGoogleMyBusinessLocation b where b.businessId in :businessId")
	List<String> findDistinctLocationIdByBusinessIdIn(@Param("businessId") List<Integer> businessIds);

	@Query(value = "Select distinct(b.locationId) from BusinessGoogleMyBusinessLocation b where b.businessId in :businessIds")
	List<String> findDistinctPageIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);


	@Query(value = "Select b.locationId from BusinessGoogleMyBusinessLocation b where b.businessId in :businessIds")
	Set<String> findPageIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	List<BusinessGoogleMyBusinessLocation> findByPlaceIdInAndIsValid(Collection<String> placeIds, Integer isValid);


	List<BusinessGoogleMyBusinessLocation> findByLocationIdInAndIsSelected(Collection<String> locationIds, Integer isSelected);

	@Query(value = "Select distinct(gmb.requestId) from BusinessGoogleMyBusinessLocation gmb where gmb.requestId in :requestIds")
	List<String> findDistinctRequestIdByRequestIdIn(@Param("requestIds") Set<String> requestId);

	@Query(value = "SELECT gmb.businessId FROM BusinessGoogleMyBusinessLocation gmb where gmb.locationId IN :locationIds")
	Set<Integer> getBusinessIdByLocationIdsIn(@Param("locationIds") List<String> locationIds);

	@Query("Select b.businessId from BusinessGoogleMyBusinessLocation b where  b.businessId in :businessIds")
	List<Integer> findByBusinessIds(@Param("businessIds") List<Integer> businessIds);

	@Query("select count(b) from BusinessGoogleMyBusinessLocation b where b.enterpriseId = :enterpriseId and  b.isValid = :isValid)")
	Long findCountByEnterpriseIdAndIsValid(@Param("enterpriseId") Long enterpriseId, @Param("isValid") Integer isValid);

	@Query("SELECT COUNT(b) > 0 FROM BusinessGoogleMyBusinessLocation b WHERE b.enterpriseId = :enterpriseId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
	boolean existsMappedPageByEnterpriseId(@Param("enterpriseId") Long enterpriseId, @Param("businessIds")List<Integer> businessIds);

	@Query("SELECT COUNT(b) > 0 FROM BusinessGoogleMyBusinessLocation b WHERE b.resellerId = :resellerId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
	boolean existsMappedPageByResellerId(@Param("resellerId") Long resellerId, @Param("businessIds")List<Integer> businessIds);

	@Query("Select distinct(b.locationId) from BusinessGoogleMyBusinessLocation b where b.enterpriseId = :enterpriseId")
	public List<String> findAllLocationIdsByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

	@Query("SELECT gmb.locationId FROM BusinessGoogleMyBusinessLocation gmb")
	List<String> findPageIdsWithPagination(Pageable pageRequest);
}
