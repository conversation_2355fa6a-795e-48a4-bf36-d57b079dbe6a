package com.birdeye.social.dao;

import com.birdeye.social.dto.ApprovalPageInfo;
import com.birdeye.social.dto.SocialBusinessPageInfo;
import com.birdeye.social.entities.BusinessTwitterAccounts;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface SocialTwitterAccountRepository extends JpaRepository<BusinessTwitterAccounts, Integer> {

	BusinessTwitterAccounts findById(Integer id);

	List<BusinessTwitterAccounts> findByIdIn(Collection<Integer> ids);
	
	@Query("Select b from BusinessTwitterAccounts b where b.businessId in :businessIds")
	public List<BusinessTwitterAccounts> findAllByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	@Query("Select distinct(b.profileId) from BusinessTwitterAccounts b where b.businessId in :businessIds")
	public List<Long> findProfileIdsByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	BusinessTwitterAccounts findByProfileIdAndEnterpriseIdAndIsValid(Long profileId, Long enterpriseId, Integer isValid);

	public List<BusinessTwitterAccounts> findByBusinessIdAndProfileId(Integer businessId, Long profileId);

	@Query("Select distinct(b.profileId) from BusinessTwitterAccounts b where b.businessId is not null and b.isValid = 1")
	public List<Long> findValidProfileIds(Pageable pageable);

	List<BusinessTwitterAccounts> findByRequestId(String id);
	
	List<BusinessTwitterAccounts> findByBusinessId(Integer businessId);
					//SELECT fb.businessId FROM BusinessFBPage fb WHERE fb.businessId IN :businessIds
	@Query(value = "SELECT b.businessId FROM BusinessTwitterAccounts b WHERE b.businessId IN :businessIds")
	List<Integer> findAllIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	@Query("select count(b) from BusinessTwitterAccounts b where b.resellerId = :resellerId and b.validType in (:validity)")
	Long findCountByResellerIdAndValidityType(@Param("resellerId") Long resellerId, @Param("validity") List<Integer> asList);

	List<BusinessTwitterAccounts> findByBusinessIdIn(List<Integer> businessIds);

	BusinessTwitterAccounts findByIdAndBusinessId(Integer id, Integer businessId);
	public List<BusinessTwitterAccounts> findByProfileIdIsNull();
	
	
	List<BusinessTwitterAccounts> findByProfileIdIn(List<Long> accIds);
	@Query("Select b from BusinessTwitterAccounts b where b.profileId in :accIds")
    public List<BusinessTwitterAccounts> findByProfileIdInWithLimit(@Param("accIds") Collection<Long> accIds, Pageable page);

	
	List<BusinessTwitterAccounts> findByEnterpriseIdAndProfileIdIn(long enterpriseId, List<Long> twitterAccountIds);

	List<BusinessTwitterAccounts> findByIsValidAndEnterpriseIdNotNull(Integer isValid);
	
	List<BusinessTwitterAccounts> findByProfileId(long id);

	BusinessTwitterAccounts findFirstByProfileId(long id);
	
	List<BusinessTwitterAccounts> findByEnterpriseId(Long businessId);

	List<BusinessTwitterAccounts> findByAccountId(Integer accountId);
	
	List<BusinessTwitterAccounts> findByEnterpriseIdAndIsSelected(Long enterpriseId, int i);

	List<BusinessTwitterAccounts> findByIsSelected(Integer isSelected);

	/**
	 * @param profileIds
	 * @return
	 */
	@Transactional
	Long deleteByProfileIdIn(List<Long> profileIds);
	
	Integer countByEnterpriseIdAndIsSelected(Long enterpriseId, Integer isSelected);
	
	@Modifying
	@Transactional
	@Query("UPDATE BusinessTwitterAccounts b SET b.enterpriseId = :enterpriseId where b.enterpriseId = :oldEnterpriseId")
	public void updateEnterpriseId(@Param("oldEnterpriseId") Long oldEnterpriseId,@Param("enterpriseId") Long enterpriseId);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessTwitterAccounts b SET b.enterpriseId = :enterpriseId, b.accountId = :accountId where b.enterpriseId = :oldEnterpriseId")
	public void updateEnterpriseIdAndAccountId(@Param("oldEnterpriseId") Long oldEnterpriseId,@Param("enterpriseId") Long enterpriseId, @Param("accountId") Integer accountId);
	
	@Modifying
	@Transactional
	@Query(nativeQuery =true,value="UPDATE business_twitter_accounts  SET enterprise_id = :enterpriseId where enterprise_id = :oldEnterpriseId AND profile_id in (:profileIdList)")
	public void updateEnterpriseIdByProfileIdIn(@Param("oldEnterpriseId") Long oldEnterpriseId,@Param("enterpriseId") Long enterpriseId,@Param("profileIdList") List<String> profileIdList);

	@Modifying
	@Transactional
	@Query(nativeQuery =true,value="UPDATE business_twitter_accounts  SET enterprise_id = :enterpriseId, accountId = :accountId where enterprise_id = :oldEnterpriseId AND profile_id in (:profileIdList)")
	public void updateEnterpriseIdAndAccountIdByProfileIdIn(@Param("oldEnterpriseId") Long oldEnterpriseId,@Param("enterpriseId") Long enterpriseId,@Param("profileIdList") List<String> profileIdList, @Param("accountId") Integer accountId);

	@Modifying
	@Transactional
	@Query(nativeQuery =true,value="DELETE from business_twitter_accounts  WHERE enterprise_id = :enterpriseId AND profile_id NOT IN (:profileIds)")
	public void deleteByEnterpriseIdAndProfileIdNotIn(@Param("enterpriseId")Long enterpriseId, @Param("profileIds") List<String> profileIds);

	@Modifying
	@Transactional
	@Query(nativeQuery =true,value="DELETE from business_twitter_accounts  WHERE reseller_id = :reseller_id AND profile_id NOT IN (:profileIds)")
	public void deleteByResellerIdAndProfileIdNotIn(@Param("reseller_id")Long enterpriseId, @Param("profileIds") List<String> profileIds);

//	@Transactional
//	Long deleteByEnterpriseId(Long enterpriseId);

	@Transactional
	void deleteByAccountId(Integer accountId);
	
	public boolean existsByEnterpriseIdAndIsSelected(Long enterpriseId, Integer isSelected);

	@Query(value="Select DISTINCT b.enterpriseId  from BusinessTwitterAccounts b where b.enterpriseId is not null and b.isValid = :isValid")
    List<Long> findDistinctEnterpriseIdByIsValid( @Param("isValid" )int isValid);

	@Query(value="Select b.id  from BusinessTwitterAccounts b where profile_id in (:profileIdList)")
    List<Number> findIdByProfileIdIn(@Param("profileIdList") List<Long> profileIdList);

	/**
	 *
	 * @param businessIds
	 * @param pageable
	 * @return
	 */
	@Query("Select b from BusinessTwitterAccounts b where b.businessId in :businessIds order by businessId ASC")
	List<BusinessTwitterAccounts> findAllByBusinessIdInWithLimit(@Param("businessIds") List<Integer> businessIds, Pageable pageable);

	/**
	 *
	 * @param businessIds
	 * @return
	 */
	@Query("Select distinct(b.businessId) from BusinessTwitterAccounts b where b.businessId in :businessIds")
	public List<Integer> countByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	
	@Query("Select b from BusinessTwitterAccounts b where b.businessId in :businessIds and b.isValid = 0")
	public List<BusinessTwitterAccounts> findInvalidPagesByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);
	
	@Query("Select count(b.id) from BusinessTwitterAccounts b where b.businessId in :businessIds and b.isValid = 0")
	public int getNumberOfInvalidPages(@Param("businessIds") List<Integer> businessIds);
	
	@Query("Select b from BusinessTwitterAccounts b where (b.lastScannedOn is null or b.lastScannedOn < :nDaysBeforeTodayDate)")
	public List<BusinessTwitterAccounts> *************************(@Param("nDaysBeforeTodayDate") Date nDaysBeforeTodayDate, Pageable pageable);


	List<BusinessTwitterAccounts> findByProfileIdInAndBusinessIdIsNotNull(List<Long> accIds);

	@Query("Select b.businessId from BusinessTwitterAccounts b where b.enterpriseId = :enterpriseId and b.isValid = :isValid")
	List<Integer> findByEnterpriseIdAndIsValid(@Param("enterpriseId") Long enterpriseId, @Param("isValid") Integer isValid);

	Page<BusinessTwitterAccounts> findByIsValidAndBusinessIdNotNullAndNextSyncDateIsLessThanEqualOrderByNextSyncDateAsc(Integer isValid, Date nextSyncDate, Pageable pageable);

	@Modifying
	@Transactional
	@Query("UPDATE BusinessTwitterAccounts b SET b.nextSyncDate = :nextSyncDate where b.id in :ids")
	 int updateNextSyncDate(@Param("nextSyncDate") Date nextSyncDate, @Param("ids") List<Integer> id);

//	List<BusinessTwitterAccounts> findByEnterpriseIdAndBusinessIdNotNull(Long businessId);

	List<BusinessTwitterAccounts> findByAccountIdAndBusinessIdNotNull(Integer accountId);

	@Query("Select distinct(b.profileId) from BusinessTwitterAccounts b where b.businessId in :businessIds and b.isValid = 1")
	public List<Long> findProfileIdsByBusinessIdInAndIsValid(@Param("businessIds") List<Integer> businessIds);

	@Query("select distinct b.businessId from BusinessTwitterAccounts b where b.businessId is not NULL")
	public Page<Integer> findBusinessIdsNotNull(Pageable page);

	@javax.transaction.Transactional
	@Modifying
	@Query("update BusinessTwitterAccounts b set b.businessId = null, b.enterpriseId=null, b.isSelected=0 where b.businessId in :invalidBusinessIds")
	void unmapInvalidPage(@Param("invalidBusinessIds") List<Integer> invalidBusinessIds);

	@Query("Select distinct(b.profileId) from BusinessTwitterAccounts b where b.enterpriseId in :enterpriseId and b.profileId is not null ")
	List<Long> findProfileIdByEnterpriseId(@Param("enterpriseId") Long enterpriseId);
	@Query("select distinct b.enterpriseId from BusinessTwitterAccounts b where b.enterpriseId is NOT NULL")
    List<Long> findEnterpriseIds(Pageable page);

	@javax.transaction.Transactional
	@Modifying
	@Query("update BusinessTwitterAccounts b set b.businessId = null, b.enterpriseId=null, b.isSelected=0 where b.enterpriseId in :invalidBusinessIds")
	void unmapSMBInvalidPage(@Param("invalidBusinessIds") Set<Long> invalidBusinessIds);

	@Query("select a.profileId as pageId ,a.name as pageName, a.profilePicUrl as profileImage from BusinessTwitterAccounts a where a.profileId = :profileId")
    List<ApprovalPageInfo> findByProfileIdLite(@Param("profileId") Long profileId);

	Long findEnterpriseIdByProfileId(Long profileId);

	List<BusinessTwitterAccounts> findByProfileIdAndIsValid(Long profileId, int isValid);

	@Query("Select t.profileId from BusinessTwitterAccounts t where t.isValid = 1")
    List<Long> findValidBusinessPageIds(Pageable pageRequest);

	@Query("Select b.profileId from BusinessTwitterAccounts b where b.enterpriseId = :enterpriseId and b.isValid = 1 and b.businessId is NOT NULL")
	List<Long> findByEnterpriseIdAndIsValidAndMapped(@Param("enterpriseId") Long enterpriseId);

	@Query("Select b from BusinessTwitterAccounts b where b.enterpriseId IN (:enterpriseId) and b.isValid = 1 and b.businessId is NOT NULL")
	List<BusinessTwitterAccounts> findDataByEnterpriseIdAndIsValidAndMapped(@Param("enterpriseId") List<Long> enterpriseId);

	@Query(value = "Select distinct(b.profileId) from BusinessTwitterAccounts b where b.businessId in :businessIds")
	List<Long> findDistinctPageIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	@Query("Select b.profileId from BusinessTwitterAccounts b where b.id in :ids")
	public Set<Long> findProfileIdById(@Param("ids") List<Integer> ids);

	List<BusinessTwitterAccounts> findByEnterpriseIdAndIsValidAndBusinessIdIsNotNull(Long enterpriseId, Integer isValid);

	@Query("Select b from BusinessTwitterAccounts b where (b.enterpriseId = :enterpriseId or b.resellerId = :enterpriseId) and b.isSelected = :isSelected and b.isValid = :isValid")
	List<BusinessTwitterAccounts> findFirstByEnterpriseIdOrResellerIdAndIsSelectedAndIsValid(@Param("enterpriseId") Long enterpriseId,
																					@Param("isSelected") Integer isSelected,
																					@Param("isValid") Integer isValid,
																					Pageable pageable);
	@Query(value = "Select distinct(b.profileId) from BusinessTwitterAccounts b where b.businessId in :businessId")
	public List<Long> findDistinctProfileIdByBusinessIdIn(@Param("businessId")List<Integer> businessIds);
	@Query("Select b.profileId, b.enterpriseId from BusinessTwitterAccounts b where b.profileId in :profileIds")
    List<Object[]> findProfileIdAndEnterpriseIdByProfileIds(@Param("profileIds") List<Long> profileIds);

	@Query("Select b.profileId from BusinessTwitterAccounts b where b.businessId in :businessIds")
	public Set<Long> findProfileIdByBusinessId(@Param("businessIds") List<Integer> businessIds);

	@Query("SELECT B.isValid as isValid, B.businessId as businessId from BusinessTwitterAccounts B where B.businessId in (:businessIds)")
	List<SocialBusinessPageInfo> findSocialBusinessPageInfoByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	List<BusinessTwitterAccounts> findByProfileIdInAndIsSelected(List<Long> accIds, Integer isSelected);

	@Query("Select b.profileId from BusinessTwitterAccounts b where b.name like %:pageName% and b.requestId = :requestId and b.enterpriseId is null and b.resellerId is null")
	List<Long> findAllByTwitterPageName(@Param("pageName") String pageName,@Param("requestId") String requestId);

	@Query("Select b.profileId from BusinessTwitterAccounts b where b.requestId = :requestId and b.enterpriseId is null and b.resellerId is null")
	List<Long> findAllByRequestId(@Param("requestId") String requestId);

	List<BusinessTwitterAccounts> findByResellerIdAndProfileIdIn(long resellerId, List<Long> twitterAccountIds);

	boolean existsByResellerIdAndIsSelected(Long resellerId, Integer selected);

	boolean existsByBusinessId(@Param("businessId") Integer businessId);

	 @Query("select count(b) from BusinessTwitterAccounts b where b.resellerId = :resellerId and b.isValid = :isValid")
	Long findCountByResellerIdAndValidity(@Param("resellerId") Long resellerId, @Param("isValid") Integer isValid);

	@Query("select b from BusinessTwitterAccounts b where b.resellerId = :resellerId and b.isValid = :isValid and b.isSelected = :isSelected")
	Page<BusinessTwitterAccounts> findByResellerIdAndValidity(@Param("resellerId") Long resellerId, @Param("isValid") Integer isValid,Pageable pageable,@Param("isSelected") Integer isSelected);

	Page<BusinessTwitterAccounts> findAll(Specification<BusinessTwitterAccounts> igPage, Pageable pageable);

	Page<BusinessTwitterAccounts> findByResellerIdAndIsSelected(Long resellerId, Integer isSelected, Pageable pageRequest);

	Page<BusinessTwitterAccounts> findByRequestId(String requestId, Pageable pageable);

	Page<BusinessTwitterAccounts> findByResellerIdAndBusinessIdIsNull(@Param("resellerId") Long resellerId, Pageable pageable);

	@Query("Select b.businessId from BusinessTwitterAccounts b where b.resellerId = :resellerId and b.businessId is NOT NULL")
    List<Integer> findByResellerIdAndBusinessIdIsNotNull(@Param("resellerId") Long resellerId);
	List<BusinessTwitterAccounts> findByEnterpriseIdIn(Set<Long> enterpriseIds);

	List<BusinessTwitterAccounts> findByEnterpriseIdInAndIsValid(List<Long> enterpriseIds, Integer isValid);

	@Query("Select b.businessId from BusinessTwitterAccounts b where b.enterpriseId in :bizHierarchyList and b.businessId is NOT NULL")
    List<Integer> findByEnterpriseIdInAndBusinessIdIsNotNull(@Param("bizHierarchyList") List<Long> bizHierarchyList);


	@Query(value = "Select distinct(twitter.requestId) from BusinessTwitterAccounts twitter where twitter.requestId in :requestIds")
	List<String> findDistinctRequestIdByRequestIdIn(@Param("requestIds") Set<String> requestIds);

	@Query(value = "SELECT bfp.business_id AS businessId, bfp.profile_id AS profileId "
			+ "FROM business_twitter_accounts bfp where bfp.business_id in (:businessIds)", nativeQuery = true)
    List<SocialTwitterAccountRepository.BTP> findByBusinessIds(@Param("businessIds") List<Integer> businessIds);

	Page<BusinessTwitterAccounts> findByEnterpriseIdAndBusinessIdIsNull(@Param("enterpriseId") Long enterpriseId, Pageable pageable);

	interface BTP {
		Integer getBusinessId();
		String getProfileId();
	}

	@Query(value = "SELECT twitter.businessId FROM BusinessTwitterAccounts twitter where twitter.profileId IN :profileIds")
	Set<Integer> getBusinessIdByProfileIdsIn(@Param("profileIds") List<Long> profileIds);

	@Query("select count(b) from BusinessTwitterAccounts b where b.enterpriseId = :enterpriseId and (b.validType in (:validity) or b.isValid = :isValid))")
	Long findCountByEnterpriseIdAndValidityTypeOrIsValid(@Param("enterpriseId") Long enterpriseId, @Param("validity") List<Integer> asList, @Param("isValid") Integer isValid);

	@Query("SELECT COUNT(b) > 0 FROM BusinessTwitterAccounts b WHERE b.enterpriseId = :enterpriseId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
	boolean existsMappedPageByEnterpriseId(@Param("enterpriseId") Long enterpriseId, @Param("businessIds")List<Integer> businessIds);

	@Query("SELECT COUNT(b) > 0 FROM BusinessTwitterAccounts b WHERE b.resellerId = :resellerId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
	boolean existsMappedPageByResellerId(@Param("resellerId") Long resellerId, @Param("businessIds")List<Integer> businessIds);

	@Query("Select t.profileId from BusinessTwitterAccounts t where t.enterpriseId = :enterpriseId")
	List<Long> findProfileIdsByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

	@Query("SELECT b.profileId FROM BusinessTwitterAccounts b")
	List<Long> findProfileIdsWithPagination(Pageable pageRequest);
}
