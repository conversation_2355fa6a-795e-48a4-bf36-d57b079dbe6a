package com.birdeye.social.dao;

import com.birdeye.social.entities.EngageFeedDetails;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface EngageFeedDetailsRepo extends JpaRepository<EngageFeedDetails, Integer> {

    EngageFeedDetails findById(Integer id);

    @Query("SELECT f.feedId from EngageFeedDetails f where f.id = :id")
    String findFeedIdById(@Param("id") Integer id);

    EngageFeedDetails findByEngageId(String engageId);

    EngageFeedDetails findTopByPageIdOrderByIdDesc(String pageId);

    EngageFeedDetails findTopByPageIdAndTypeOrderByFeedDateDesc(String pageId, String type);

    EngageFeedDetails findFirstByEngageIdAndTypeAndPageId(String engageId, String type, String pageId);

    EngageFeedDetails findByEngageIdAndType(String engageId, String type);

    EngageFeedDetails findByFeedId(String feedId);
    EngageFeedDetails findFirstByFeedIdAndPageId(String feedId,String pageId);

    EngageFeedDetails findByFeedIdAndPageId(String feedId,String pageId);

    List<EngageFeedDetails> findAllByFeedIdIn(List<String> ids);
    EngageFeedDetails findTopByPageIdAndTypeAndSubTypeOrderByFeedIdDesc(String profileId, String type , String subType);

    EngageFeedDetails findTopByPageIdAndSubTypeOrderByFeedId(Long profileId, String name);

   @Query(nativeQuery = true , value="SELECT f.feed_id from engage_feed_details f where f.page_id= :pageId and f.type= :type order by id desc limit :lim")
    List<String> findFeedIdByPageIdAndType(@Param("pageId") String pageId, @Param("type") String type, @Param("lim") Integer lim);

    @Query("SELECT f.metaData from EngageFeedDetails f where f.metaData is not null")
    List<String> getMetaDataForAll(Pageable pageRequest);

    EngageFeedDetails findByEngageIdAndPageId(String engageId, String pageId);

}
