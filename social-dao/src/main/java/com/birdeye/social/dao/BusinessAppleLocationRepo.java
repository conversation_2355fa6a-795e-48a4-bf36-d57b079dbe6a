package com.birdeye.social.dao;

import com.birdeye.social.dto.SocialBusinessPageInfo;
import com.birdeye.social.entities.BusinessAppleLocation;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.Collection;
import java.util.List;
import java.util.Set;

@Repository
public interface BusinessAppleLocationRepo extends JpaRepository<BusinessAppleLocation, Integer>, JpaSpecificationExecutor<BusinessAppleLocation> {

    List<BusinessAppleLocation> findByEnterpriseId(Long enterpriseId);

    List<BusinessAppleLocation> findByAccountId(Integer accountId);

    List<BusinessAppleLocation> findByBusinessIdIn(List<Integer> businessIds);

    List<Long> findDistinctEnterpriseIdBy();

    BusinessAppleLocation findByAppleLocationId(String appleLocationId);

    @Query(value="select bal from BusinessAppleLocation bal where bal.businessId is not null")
    Page<BusinessAppleLocation> findAll(Pageable pageRequest);

    Page<BusinessAppleLocation> findByIsValidAndBusinessIdNotNull(Integer isValid, Pageable pageRequest);

    List<BusinessAppleLocation> findByEnterpriseIdAndAppleCompanyIdAndAppleBusinessId(Long enterpriseId,
            String appleCompanyId, String appleBusinessId);

    List<BusinessAppleLocation> findByAppleLocationIdIn(List<String> appleLocationId);

    Page<BusinessAppleLocation> findByBusinessIdNotNullAndNextSyncDateIsLessThanEqualOrderByNextSyncDateAsc(Date nextSyncDate, Pageable pageable);

    @Modifying
    @Transactional
    @Query("UPDATE BusinessAppleLocation b SET b.nextSyncDate = :nextSyncDate where b.id in :ids")
    int updateNextSyncDate(@Param("nextSyncDate") Date nextSyncDate, @Param("ids") List<Integer> id);

    BusinessAppleLocation findById(Integer id);

    BusinessAppleLocation findFirstByAppleLocationId(String appleLocationId);

    @Query(value = "Select distinct(b.appleLocationId) from BusinessAppleLocation b where b.businessId in :businessId")
    List<String> findDistinctAppleLocationIdByBusinessIdIn(@Param("businessId")List<Integer> businessIds);
    @Query(value = "Select b from BusinessAppleLocation b where b.appleLocationId in :locationIds and b.businessId is not null")
    List<BusinessAppleLocation> findByAppleLocationIdAndBusinessIdIsNotNull(@Param("locationIds")List<String> locationIds);

    BusinessAppleLocation findByIdAndBusinessId(Integer id, Integer businessId);

    List<BusinessAppleLocation> findAllByBusinessIdIsNotNull();

    @Query("Select b from BusinessAppleLocation b where b.businessId in :businessIds")
    public List<BusinessAppleLocation> findAllByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

    BusinessAppleLocation findByBusinessId(Integer businessId);
    @Query("Select b.appleLocationId, b.enterpriseId from BusinessAppleLocation b where b.appleLocationId in :pageIds")
    List<Object[]> findPageIdAndEnterpriseIdbyPageIds(@Param("pageIds") List<String> pageIds);

	@Query(value = "select business_id from business_apple_location where apple_location_id=:locationId limit 1",nativeQuery = true)
	Integer getBusinessIdByAppleLocationId(@Param("locationId") String locationId);

    @Query("SELECT B.businessId as businessId from BusinessAppleLocation B where B.businessId in (:businessIds)")
    List<SocialBusinessPageInfo> findSocialBusinessPageInfoByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

    boolean existsByBusinessId(Integer locationId);

    @Query(value = "SELECT apple.businessId FROM BusinessAppleLocation apple where apple.appleLocationId IN :locationIds")
    Set<Integer> getBusinessIdByLocationIdsIn(@Param("locationIds") List<String> locationIds);

    @Query("select count(b) from BusinessAppleLocation b where b.enterpriseId = :enterpriseId and b.isValid = :isValid")
    Long findCountByEnterpriseIdAndValidityType(@Param("enterpriseId") Long enterpriseId, @Param("isValid") Integer isValid);

    @Query("SELECT COUNT(b) > 0 FROM BusinessAppleLocation b WHERE b.enterpriseId = :enterpriseId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
    boolean existsMappedPageByEnterpriseId(@Param("enterpriseId") Long enterpriseId, @Param("businessIds")List<Integer> businessIds);

    @Query(value = "SELECT b.businessId FROM BusinessAppleLocation b WHERE b.businessId IN :businessIds")
    public List<Integer> findAllIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

    Page<BusinessAppleLocation> findByEnterpriseIdAndBusinessIdIsNull(@Param("enterpriseId") Long enterpriseId, Pageable pageable);

    List<BusinessAppleLocation> findByRequestId(String requestId);

    @Query("Select distinct(ap.appleLocationId) from BusinessAppleLocation ap where ap.enterpriseId = :enterpriseId")
    public List<String> findAllByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

    @Query("SELECT ap.appleLocationId FROM BusinessAppleLocation ap")
    List<String> findPageIdsWithPagination(Pageable pageRequest);
}
