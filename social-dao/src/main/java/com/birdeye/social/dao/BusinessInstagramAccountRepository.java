/**
 * 
 */
package com.birdeye.social.dao;

import com.birdeye.social.dto.ApprovalPageInfo;
import com.birdeye.social.dto.SocialBusinessPageInfo;
import com.birdeye.social.entities.BusinessInstagramAccount;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.*;

public interface BusinessInstagramAccountRepository extends JpaRepository<BusinessInstagramAccount, Integer>,JpaSpecificationExecutor<BusinessInstagramAccount> {

    BusinessInstagramAccount findById(Integer id);

    BusinessInstagramAccount findByIdAndBusinessId(Integer id, Integer BusinessId);
    List<BusinessInstagramAccount> findByBusinessId(Integer businessId);

    BusinessInstagramAccount findByPageAccessToken(String pageAccessToken);

    @Query("Select instagramAccountId from BusinessInstagramAccount ig where ig.businessId =:businessId and ig.isValid = 1")
    List<String> findInstagramIdByBusinessId(@Param("businessId") Integer businessId);

    @Query("Select distinct(ig.instagramAccountId) from BusinessInstagramAccount ig where ig.businessId in :businessIds and ig.isValid = 1")
    List<String> findDistinctInstagramIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

    @Query("Select distinct(ig.instagramAccountId) from BusinessInstagramAccount ig where ig.businessId in :businessIds")
    List<String> findDistinctInstagramIdByBusinessIdsIn(@Param("businessIds") List<Integer> businessIds);

    @Query(value = "SELECT ig.businessId FROM BusinessInstagramAccount ig WHERE ig.businessId IN :businessIds")
    public List<Integer> findAllIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

    List<BusinessInstagramAccount> findByEnterpriseId(Long enterpriseId);

    List<BusinessInstagramAccount> findByAccountId(Integer accountId);

    List<BusinessInstagramAccount> findByEnterpriseIdAndIsSelected(Long enterpriseId, Integer isSelected);

    List<BusinessInstagramAccount> findByBusinessGetPageId(String businessGetPageId);

    @Query("Select b from BusinessInstagramAccount b where b.businessId in :businessIds")
    List<BusinessInstagramAccount> findAllPageByBusinessIdIn(@Param("businessIds") Set<Integer> businessIds);

    List<BusinessInstagramAccount> findAllByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);
    @Query(value = "Select distinct(b.instagramAccountId) from BusinessInstagramAccount b where b.businessId in :businessId")
    List<String> findDistinctInstagramAccountIdByBusinessIdIn(@Param("businessId") List<Integer> businessIds);

    List<BusinessInstagramAccount> findByBusinessIdAndInstagramHandle(Integer businessId, String instagramHandle);

    List<BusinessInstagramAccount> findByFacebookPageIdIn(List<String> facebookPageIds);

    List<BusinessInstagramAccount> findByInstagramAccountIdIn(List<String> instagramAccountIds);

    List<BusinessInstagramAccount> findByEnterpriseIdAndInstagramAccountIdIn(Long enterpriseId, List<String> instagramAccountIds);

    BusinessInstagramAccount findByEnterpriseIdAndInstagramAccountId(Long enterpriseId, String instagramAccountId);

    BusinessInstagramAccount findByFacebookPageId(String facebookPageId);

    List<BusinessInstagramAccount> findByFacebookPageIdAndBusinessIdIsNotNull(String facebookPageId);

    List<BusinessInstagramAccount> findByInstagramAccountIdAndIsValid(String instagramAccountId, Integer isValid);

    BusinessInstagramAccount findByInstagramAccountId(String instagramAccountId);

    @Modifying
    @Transactional
    Long deleteByInstagramAccountIdIn(List<String> instagramAccountIds);

    @Query(value = "Select DISTINCT b.enterpriseId  from BusinessInstagramAccount b where b.enterpriseId is not null and b.isValid = :isValid")
    List<Long> findDistinctEnterpriseIdByIsValid(@Param("isValid") int isValid);

    boolean existsByEnterpriseIdAndIsSelected(Long enterpriseId, Integer isSelected);

    List<BusinessInstagramAccount> findByInstagramAccountIdInAndBusinessIdIsNotNull(List<String> instagramAccountIds);

    List<BusinessInstagramAccount> findByAccountIdAndBusinessIdIsNotNull(Integer accountId);

    Page<BusinessInstagramAccount> findByIsValidAndBusinessIdNotNullAndNextSyncDateIsLessThanEqualOrderByNextSyncDateAsc(Integer isValid, Date nextSyncDate, Pageable pageable);

    Page<BusinessInstagramAccount> findByIsValidAndBusinessIdNotNullAndLastScannedOnLessThan(Integer isValid,Date nDaysBeforeTodayDate,
                                                                                          Pageable pageable);


    @Modifying
    @Transactional
    @Query("UPDATE BusinessInstagramAccount b SET b.nextSyncDate = :nextSyncDate where b.id in :ids")
    int updateNextSyncDate(@Param("nextSyncDate") Date nextSyncDate, @Param("ids") List<Integer> id);

    @Query("Select b.businessId from BusinessInstagramAccount b where b.enterpriseId = :enterpriseId and b.isValid = :isValid")
    List<Integer> findBusinessIdsByEnterpriseId(@Param("enterpriseId") Long enterpriseId, @Param("isValid") Integer isValid);

    List<BusinessInstagramAccount> findByIsValidAndEnterpriseIdNotNull(Integer isValid);

    List<BusinessInstagramAccount> findByEnterpriseIdInAndIsValidAndBusinessIdIsNotNull(List<Long> enterpriseId,int isValid);

    List<BusinessInstagramAccount> findByAccountIdInAndIsValidAndBusinessIdIsNotNull(List<Integer> accountId,int isValid);

    @Query("Select b from BusinessInstagramAccount b where b.enterpriseId is not null and b.businessId is not null and b.isValid =1")
    List<BusinessInstagramAccount>findAllValidAccounts();

    @Query("Select b.instagramAccountId from BusinessInstagramAccount b where b.enterpriseId is not null and b.businessId is not null and b.isValid =1")
    List<String>findAllValidAccountIds(Pageable pageRequest);

    @Query("Select distinct(a.instagramAccountId) from BusinessInstagramAccount a where a.enterpriseId = :enterpriseId and a.instagramAccountId is not null")
    Collection<String> findDistinctInstagramIdByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

    @Query("Select b from BusinessInstagramAccount b where b.enterpriseId IN (:enterpriseId) and b.isValid = 1 and b.businessId is NOT NULL")
    List<BusinessInstagramAccount>  findByEnterpriseIdAndIsValid(@Param("enterpriseId") List<Long> enterpriseId);

    @Query("select distinct b.businessId from BusinessInstagramAccount b where b.businessId is not NULL")
    public Page<Integer> findBusinessIdsNotNull(Pageable page);

    @Transactional
    @Modifying
    @Query("update BusinessInstagramAccount b set b.businessId = null, b.enterpriseId=null, b.isSelected=0 where b.businessId in :invalidBusinessIds")
    void unmapInvalidPage(@Param("invalidBusinessIds") List<Integer> invalidBusinessIds);
   @Query("select distinct b.enterpriseId from BusinessInstagramAccount b where b.enterpriseId is NOT NULL")
   List<Long> findEnterpriseIds(Pageable page);

    @javax.transaction.Transactional
    @Modifying
    @Query("update BusinessInstagramAccount b set b.businessId = null, b.enterpriseId=null, b.isSelected=0 where b.enterpriseId in :invalidBusinessIds")
    void unmapSMBInvalidPage(@Param("invalidBusinessIds") Set<Long> invalidBusinessIds);
    List<BusinessInstagramAccount> findByInstagramAccountIdInAndIsValid(List<String> instagramAccountId, Integer isValid);

    @Query("select a.instagramAccountId as pageId , a.instagramAccountName as pageName , a.instagramAccountPictureUrl as profileImage from BusinessInstagramAccount a where a.instagramAccountId = :instagramAccountId")
    List<ApprovalPageInfo> findByInstagramAccountIdLite(@Param("instagramAccountId") String pageId);

    @Query("Select b.instagramAccountId, b.enterpriseId from BusinessInstagramAccount b where b.instagramAccountId in :instagramAccountIds")
    List<Object[]> findInstagramAccountIdAndEnterpriseIdbyInstagramAccountIds(@Param("instagramAccountIds")  List<String> instagramAccountIds);

    @Transactional
    @Modifying
    @Query("update BusinessInstagramAccount b set b.engageSyncDate = :syncDate where b.id in :ids")
    void updateEngageSyncDate(@Param("ids") List<Integer> ids, @Param("syncDate") Date syncDate);
    Page<BusinessInstagramAccount> findByIsValidAndBusinessIdNotNullAndEngageSyncDateIsLessThanEqualOrderByEngageSyncDateAsc(Integer isValid, Date engageSyncDate, Pageable pageable);

    @Query("Select b.instagramAccountId from BusinessInstagramAccount b where b.enterpriseId = :enterpriseId and b.isValid = 1 and b.businessId is NOT NULL")
    List<String> findByEnterpriseIdAndIsValidAndMapped(@Param("enterpriseId") Long enterpriseId);

    @Query("Select b.instagramAccountId from BusinessInstagramAccount b where b.id in :ids")
    public Set<String> findInstagramAccountIdById(@Param("ids") List<Integer> ids);

    @Query("Select b.instagramAccountId from BusinessInstagramAccount b where b.businessId in :businessIds")
    public Set<String> findInstagramAccountIdByBusinessId(@Param("businessIds") List<Integer> businessIds);
    @Query("Select b.id from BusinessInstagramAccount b where b.businessId is not null and b.isValid = 1 order by b.id desc")
    List<Integer> findIdsByMappedAndValid();

    @Query("SELECT B.isValid as isValid, B.businessId as businessId from BusinessInstagramAccount B where B.businessId in (:businessIds)")
    List<SocialBusinessPageInfo> findSocialBusinessPageInfoByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

    @Query("Select b from BusinessInstagramAccount b where b.instagramAccountId in :instagramAccountIds " +
            "and b.pagePermissions like '%instagram_manage_insights%' and b.pagePermissions like '%instagram_basic%' " +
            "and b.pagePermissions like '%pages_read_engagement%' and b.pagePermissions like '%pages_show_list%'")
    List<BusinessInstagramAccount> findByInstagramAccountIdInAndEligible(@Param("instagramAccountIds") List<String> instagramAccountIds);
    List<BusinessInstagramAccount> findByInstagramAccountIdInAndIsSelected(List<String> instagramAccountIds, Integer isSelected);


	@Query("Select b.instagramAccountId from BusinessInstagramAccount b where b.instagramAccountName like %:pageName% and b.businessGetPageId = :requestId and b.enterpriseId is null and b.resellerId is null")
	List<String> findAllByIGPageName(@Param("pageName") String pageName,@Param("requestId") String requestId);

	@Query("Select b.instagramAccountId from BusinessInstagramAccount b where b.businessGetPageId = :requestId and b.enterpriseId is null and b.resellerId is null")
    List<String> findAllByRequestId(@Param("requestId") String requestId);

	List<BusinessInstagramAccount> findByResellerIdAndInstagramAccountIdIn(long resellerId,
			List<String> instagramAccountIds);
    boolean existsByBusinessId(@Param("businessId") Integer businessId);
    public Page<BusinessInstagramAccount> findByResellerIdAndBusinessIdIsNull(@Param("resellerId") Long resellerId, Pageable pageable);


    @Query("select count(b) from BusinessInstagramAccount b where b.resellerId = :resellerId and b.isValid = :isValid")
    Long findCountByResellerIdAndValidity(@Param("resellerId") Long resellerId, @Param("isValid") Integer isValid);

    @Query("select count(b) from BusinessInstagramAccount b where b.resellerId = :resellerId and b.validType in (:validity)")
    Long findCountByResellerIdAndValidityType(@Param("resellerId") Long resellerId, @Param("validity") List<Integer> asList);

    @Query("select b from BusinessInstagramAccount b where b.resellerId = :resellerId and b.isValid = :isValid and b.isSelected = :isSelected")
    Page<BusinessInstagramAccount> findByResellerIdAndValidity(@Param("resellerId") Long resellerId, @Param("isValid") Integer isValid,Pageable pageable,@Param("isSelected") Integer isSelected);

    public Page<BusinessInstagramAccount> findByResellerIdAndIsSelected(Long enterpriseId, Integer isSelected, Pageable pageable);

    boolean existsByResellerIdAndIsSelected(Long resellerId, Integer selected);

	Page<BusinessInstagramAccount> findByBusinessGetPageId(String requestId, Pageable pageable);

    @Query("Select b from BusinessInstagramAccount b where b.instagramAccountId in :instagramAccountIds")
    public List<BusinessInstagramAccount> findByInstagramPageIdInWithLimit(@Param("instagramAccountIds") Collection<String> instagramAccountIds, Pageable page);

    List<BusinessInstagramAccount> findByResellerId(Long resellerId);

    @Query("Select b.businessId from BusinessInstagramAccount b where b.resellerId = :resellerId and b.businessId is not null ")
    List<Integer> findByResellerIdAndBusinessIdIsNotNull(@Param("resellerId") Long resellerId);

    @Query("Select ig from BusinessInstagramAccount ig where ig.enterpriseId in :enterpriseId and ig.isValid = 1")
    List<BusinessInstagramAccount> findDistinctInstagramIdByEnterpriseIn(@Param("enterpriseId") List<Long> enterpriseId);
    List<BusinessInstagramAccount> findByEnterpriseIdIn(Set<Long> enterpriseId);

    @Query("Select b.businessId from BusinessInstagramAccount b where b.enterpriseId in :bizHierarchyList and b.businessId is not null ")
    List<Integer> findByEnterpriseIdInAndBusinessIdIsNotNull(@Param("bizHierarchyList") List<Long> bizHierarchyList);

    @Query("Select ig from BusinessInstagramAccount ig where ig.isValid = 1 and ig.businessId is not null and ig.enterpriseId is not null")
    List<BusinessInstagramAccount> findAllPagesByIsValid(Pageable pageRequest);

    @Query(value = "SELECT bfp.business_id AS businessId, bfp.instagram_account_id AS instagramAccountId " +
            "FROM business_instagram_account bfp where bfp.business_id in (:businessIds)", nativeQuery = true)
    List<BusinessInstagramAccountRepository.BIA> findByBusinessIds(@Param("businessIds") List<Integer> businessIds);

    Page<BusinessInstagramAccount> findByEnterpriseIdAndBusinessIdIsNull(@Param("enterpriseId") Long enterpriseId, Pageable pageable);

    interface BIA {
        Integer getBusinessId();
        String getInstagramAccountId();
    }

    @Query(value = "Select distinct(ig.businessGetPageId) from BusinessInstagramAccount ig where ig.businessGetPageId in :businessGetPageIds")
    List<String> findDistinctBusinessGetPageIdByBusinessGetPageIdIn(@Param("businessGetPageIds") Set<String> businessGetPageId);

    @Query(value = "SELECT ig.businessId FROM BusinessInstagramAccount ig where ig.instagramAccountId IN :accountIds")
    Set<Integer> getBusinessIdByAccountIdsIn(@Param("accountIds") List<String> accountIds);

    List<BusinessInstagramAccount> findByUserId(String userId);

    @Query("select count(b) from BusinessInstagramAccount b where b.enterpriseId = :enterpriseId and (b.validType in (:validity) or b.isValid = :isValid))")
    Long findCountByEnterpriseIdAndValidityTypeOrIsValid(@Param("enterpriseId") Long enterpriseId, @Param("validity") List<Integer> asList, @Param("isValid") Integer isValid);

    @Query("SELECT COUNT(b) > 0 FROM BusinessInstagramAccount b WHERE b.enterpriseId = :enterpriseId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
    boolean existsMappedPageByEnterpriseId(@Param("enterpriseId") Long enterpriseId, @Param("businessIds")List<Integer> businessIds);

    @Query("SELECT COUNT(b) > 0 FROM BusinessInstagramAccount b WHERE b.resellerId = :resellerId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
    boolean existsMappedPageByResellerId(@Param("resellerId") Long resellerId, @Param("businessIds")List<Integer> businessIds);

    @Query(value = "Select distinct(b.instagramAccountId) from BusinessInstagramAccount b where b.enterpriseId = :enterpriseId")
    List<String> findDistinctInstagramAccountIdByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

    @Query("SELECT b.instagramAccountId FROM BusinessInstagramAccount b")
    List<String> findPageIdsWithPagination(Pageable pageRequest);
}