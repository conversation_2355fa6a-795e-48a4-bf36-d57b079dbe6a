package com.birdeye.social.insights.constants;

import java.util.ArrayList;
import java.util.List;

public enum InstagramPageEngagementMetric {
    likes,
    saves,
    comments,
    shares,
    views;

    public static String getListOfMetricAsString(){
        StringBuilder list = new StringBuilder();
        try {
            for(InstagramPageEngagementMetric metric : InstagramPageEngagementMetric.values()){
                list.append(metric.name()).append(",");
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return list.toString();
    }
    public static List<String> getListOfMetric(){
        List<String> list = new ArrayList<>();
        try {
            for(InstagramPageEngagementMetric metric : InstagramPageEngagementMetric.values()){
                list.add(metric.name());
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return list;
    }
}
