/**
 *
 */
package com.birdeye.social.businessCore;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.dto.*;
import com.birdeye.social.dto.businessProfile.AccountListFilter;
import com.birdeye.social.dto.businessProfile.BusinessProfileDTO;
import com.birdeye.social.dto.businessProfile.BusinessUserLocationResponse;
import com.birdeye.social.external.exception.ExternalAPIErrorCode;
import com.birdeye.social.external.exception.ExternalAPIException;
import com.birdeye.social.external.request.business.BusinessAccountLocationRequest;
import com.birdeye.social.external.request.business.BusinessBulkRequest;
import com.birdeye.social.external.request.business.BusinessLiteRequest;
import com.birdeye.social.external.request.business.BusinessLocationDetailRequest;
import com.birdeye.social.utils.JSONUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Gupta
 *
 */
@Service
public class BusinessCoreServiceImpl implements IBusinessCoreService {

	private static final String CORE_SERVICE_BASE_URL = "social.business.core.services.url";
	public static final String SOCIAL = "social";
	public static final String GET_BUSINESS_LITE = "v1/business/getBusinessLite";
	public static final String GET_BUSINESS_PROFILE_INFO = "/v1/business/profile/basic-info";
	public static final String GET_BUSINESS_INDUSTRY_INFO = "/v1/business/getIndustry/";
	public static final String GET_BUSINESS_HIERARCHY_LIST = "/v1/business/businessHierarchyList";
	public static final String GET_BUSINESS_BULK_BY_BUSINESS_IDS = "v1/business/data";

	public static final String GET_BUSINESS_BULK_BY_BUSINESS_IDS_AND_SEARCH = "v1/reseller/locations/filter-search";
	public static final String GET_BUSINESS_LOCATIONS = "v1/business/enterprises/locations/";
	public static final String GET_BUSINESS_LOCATION = "/v1/business/locations/";
	public static final String GET_BUSINESS_DOMAIN = "v1/domain/";
	public static final String GET_BUSINESS_TIMEZONE = "v1/business/timezone/";
	public static final String GET_BUSINESS_HOURS = "v1/business/timings/";
	public static final String GET_USER = "v1/user/";

	public static final String GET_USERS_IN_BULK = "v1/user/fetchUsers";
	public static final String GET_BUSINESS_USERS_LITE = "v1/user/get/{enterpriseId}";
	public static final String GET_BUSINESS_USERS_LITE_WITH_LOCATION = "v1/user/all";
	public static final String GET_BUSINESS_OPTIONS = "v1/business/businessoptions?businessId=";
	public static final String ACCEPT = "Accept";
	public static final String CONTENT_TYPE = "Content-Type";
	public static final String SERVICE_NAME = "SERVICE-NAME";
	public static final String KEY = "key";
	public static final String LOCATION = "location";
	public static final String VALUE = "value";
	public static final String PAGESIZE = "pageSize";
	public static final String SORTBY = "sortBy";
	public static final String SORTORDER = "sortOrder";
	public static final String STARTINDEX = "startIndex";
	public static final String ENTERPRISEUSER = "enterpriseUser";
	private static final String CACHE_VALUE="business.core";
	private static final String USER_FULL_NAME_VALUE = "user.full.name";
	private static final String USER_INFO_CACHE = "user.info.cache";
	public static final String CUSTOM_HIERARCHY_TO_LOC_DETAILS ="/v1/business-hierarchy/custom-hierarchy-loc-mapping/{accountId}";
	public static final String GET_BUSINESS_PROFILE = "v1/business/profile?businessId=";
	public static final String GET_BIZ_LITE = "v1/business/biz-lite";
	public static final String GET_BUSINESS_LINKS = "v1/business/links/";
	public static final String GET_BUSINESS_SUBHIERARCHY_LIST = "v1/business/subHierarchyList/";
	public static final String GET_BUSINESS_PROPERTY = "v1/activity/productFeatures/";
	public static final String GET_BUSINESS_CATEGORIES = "v1/category/getBusinessCategory";
	private static final String CDN_IMAGE_UPLOAD = "v1/image/upload?source=";
	private static final String DOMAIN_ROUTE = "/v1/domain/";
	private static final String USER_NOTIFICATION_DETAILS = "v1/user/notification/social/details";
	public static final String BUSINESS_ID = "businessId";
	public static final String FETCH_USER_DETAILS = "/v1/user/fetchUsers";
	public static final String FETCH_USER_BUSINESS_IDS = "v1/business/getbusinessids/%s";
	public static final String USER_ID = "userId";
	public static final String BUSINESS_LITE_INFO = "businessLiteInfo";
	public static final String USER_BUSINESS_ACCESS = "userBusinessAccess";
	public static final String GET_RESELLER_LOCATIONS_DETAILS = "v1/reseller/locations/filter/list";

	public static final String GET_BUSINESS_CUSTOM_FIELD = "api/v1/custom-fields/business-tokens/%s";
	public static final String BUSINESS_SETUP_ACCOUNT_LIST_URI = "/v1/business/setup/enterprise-locations/%s";




	@Autowired
	RestTemplate businessCoreRestClient;

	@Autowired
	private RedisTemplate<String, String> redisTemplate;

	@Value("${redis.key.prefix.name}")
	private String keyPrefix;

	private static Logger logger = LoggerFactory.getLogger(BusinessCoreServiceImpl.class);



	@Override
	public Boolean isWebChatEnabled(Integer businessId) {
		String businessCoreServiceUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = businessCoreServiceUrl.concat("v1/business/businessoptions?businessId=").concat(businessId.toString()).concat("&traversehierarchy=true");
		HttpHeaders headers = new HttpHeaders();
		headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		ResponseEntity<BusinessOptionResponse> businessOptionResponse =  businessCoreRestClient.exchange(url,HttpMethod.GET,entity,BusinessOptionResponse.class);
		if (businessOptionResponse.getStatusCode().equals(HttpStatus.OK)){
			return businessOptionResponse.getBody().getEnableChatWidget() != null && businessOptionResponse.getBody().getEnableChatWidget() == 1;
		} else{
			logger.error("Error while calling business core service for business options : response code:{}",businessOptionResponse.getStatusCode());
			return false;
		}
	}

	@Override
	@Cacheable(value = "enterpriseId", key = "#enterpriseId.toString()", unless = "#result == null")
	public Boolean isWebChatEnabledByNumber(Long enterpriseId) {
		String businessCoreServiceUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = businessCoreServiceUrl.concat("v1/business/businessoptionsByNumber?businessNumber=").concat(enterpriseId.toString()).concat("&traversehierarchy=true");
		logger.info("URL to fetch business option for enterprise:{} : {}",enterpriseId,url);
		HttpHeaders headers = new HttpHeaders();
		headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		ResponseEntity<BusinessOptionResponse> businessOptionResponse =  businessCoreRestClient.exchange(url,HttpMethod.GET,entity,BusinessOptionResponse.class);
		logger.info("response: {}",businessOptionResponse.getBody());
		if (businessOptionResponse.getStatusCode().equals(HttpStatus.OK)){
			if(businessOptionResponse.getBody().getEnableMessenger()!= null && businessOptionResponse.getBody().getEnableMessenger() == 1) {
				return true;
			} else {
				return false;
			}
		} else{
			logger.error("Error while calling business core service for business options : response code:{}",businessOptionResponse.getStatusCode());
			return false;
		}
	}
	@Override
	public BusinessProfileResponse getBusinessProfile(Integer accountId) {
		String businessCoreServiceUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_PROFILE_INFO);
		HttpHeaders headers = new HttpHeaders();
		headers.add(ACCEPT,MediaType.APPLICATION_JSON_VALUE);
		headers.add(SERVICE_NAME, SOCIAL);
		headers.add("account-id", String.valueOf(accountId));
		HttpEntity<?> entity = new HttpEntity<>(headers);
		logger.info("url for getProfileData: {}", url);
		ResponseEntity<BusinessProfileResponse> businessProfileResponse = businessCoreRestClient.exchange(url, HttpMethod.GET, entity, BusinessProfileResponse.class);
		if (businessProfileResponse.getStatusCode().equals(HttpStatus.OK)) {
			logger.info("BusinessProfileResponse response: {}", businessProfileResponse.getBody());
			return businessProfileResponse.getBody();
		} else {
			logger.error("Error while calling business core service for business profile : response code:{}", businessProfileResponse.getStatusCode());
			return null;
		}
	}

	@Override
	public BusinessIndustryResponse getBusinessIndustry(Integer accountId) {
		String businessCoreServiceUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_INDUSTRY_INFO).concat(accountId.toString());
		HttpHeaders headers = new HttpHeaders();
		headers.add(ACCEPT,MediaType.APPLICATION_JSON_VALUE);
		headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		logger.info("url for getIndustryData: {}", url);
		ResponseEntity<BusinessIndustryResponse> businessIndustryResponse = businessCoreRestClient.exchange(url, HttpMethod.GET, entity, BusinessIndustryResponse.class);
		if (businessIndustryResponse.getStatusCode().equals(HttpStatus.OK)) {
			logger.info("BusinessIndustryResponse response: {}", businessIndustryResponse.getBody());
			return businessIndustryResponse.getBody();
		} else {
			logger.error("Error while calling business core service for business industry : response code:{}", businessIndustryResponse.getStatusCode());
			return null;
		}
	}

	@Override
	public BusinessLiteDTO getBusinessLite(BusinessLiteRequest businessLiteRequest) {
		logger.info("Get businessLite info from core for businessId: {}",businessLiteRequest.getValue());
		BusinessLiteDTO businessLiteDTO = new BusinessLiteDTO();
		String businessCoreServiceUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_LITE);
		HttpHeaders headers = new HttpHeaders();
		headers.add(ACCEPT,MediaType.APPLICATION_JSON_VALUE);
		headers.add(SERVICE_NAME, SOCIAL);

		UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url)
				.queryParam(KEY,businessLiteRequest.getKey())
				.queryParam(LOCATION,businessLiteRequest.isLocationRequired())
				.queryParam(VALUE,businessLiteRequest.getValue());
		HttpEntity<?> entity = new HttpEntity<>(headers);
		HttpEntity<BusinessLiteDTO> response;
		try{
			response = businessCoreRestClient.exchange(builder.build().toString(),HttpMethod.GET,entity,BusinessLiteDTO.class);
			businessLiteDTO = response.getBody();
		}catch (HttpStatusCodeException ex){
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return businessLiteDTO;
	}

	public List<Integer> getBusinessHierarchyList(Integer businessId) {
		List<Integer> hierarchyList = new ArrayList<>();
		String businessCoreServiceUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_HIERARCHY_LIST);
		StringBuilder builder = new StringBuilder(url);
		builder.append("/").append(businessId.toString());
		HttpHeaders headers = new HttpHeaders();
		headers.add(ACCEPT, MediaType.APPLICATION_JSON_VALUE);
		headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		HttpEntity<List> response;
		try{
			response = businessCoreRestClient.exchange(builder.toString(), HttpMethod.GET,entity,List.class);
			hierarchyList = response.getBody();
		}catch (HttpStatusCodeException ex){
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return hierarchyList;
	}

	@Override
	public Map<String,BusinessLocationLiteDTOForGMB> getBusinessesInBulkByBusinessNumber(List<Long> businessNumbers, boolean publicUrl) {
		BusinessBulkRequest request = new BusinessBulkRequest();
		request.setBusinessNumbers(businessNumbers);
		return getBusinessInBulkWithLocation(request,false, publicUrl);
	}

	private Map<String,BusinessLocationLiteDTOForGMB> getBusinessInBulkWithLocation(BusinessBulkRequest request ,boolean locationRequired, boolean publicUrl){
		String coreUrl = publicUrl ?
				CacheManager.getInstance().getCache(SystemPropertiesCache.class).getCorePublicUrl() :
				CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = coreUrl.concat(GET_BUSINESS_BULK_BY_BUSINESS_IDS);
		HttpHeaders headers = new HttpHeaders();
		headers.add(ACCEPT,MediaType.APPLICATION_JSON_VALUE);
		headers.add(SERVICE_NAME, SOCIAL);

		List<BusinessBulkRequest> businessBulkRequests = getBusinessBulkRequestsInBatches(request,
				CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBusinessDetailsBatchSize());
		Map<String, BusinessLocationLiteDTOForGMB> finalResponse = new HashMap<>();

		businessBulkRequests.parallelStream().forEach(businessBulkRequest -> {
			HttpEntity<BusinessBulkRequest> requestEntity = new HttpEntity<>(businessBulkRequest, headers);

			UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url).queryParam(LOCATION,locationRequired);
			ResponseEntity<Map<String,BusinessLocationLiteDTOForGMB>> response;
			try{
				response = businessCoreRestClient.exchange(builder.build().toString(), HttpMethod.POST, requestEntity,
						new ParameterizedTypeReference<Map<String, BusinessLocationLiteDTOForGMB>>() {});
				finalResponse.putAll(response.getBody());
			}catch (HttpStatusCodeException ex){
				if (ex.getStatusCode().is5xxServerError()) {
					logger.warn("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
					throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
				}
				if (ex.getStatusCode().is4xxClientError()) {
					logger.warn("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
					throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
				}
			}
		});

		return finalResponse;
	}

	private List<BusinessBulkRequest> getBusinessBulkRequestsInBatches(BusinessBulkRequest request, int batchSize) {
		List<List<Long>> businessNumbers = Objects.nonNull(request.getBusinessNumbers()) ? Lists.partition(request.getBusinessNumbers(), batchSize) : null;
		List<List<Integer>> businessIds = Objects.nonNull(request.getBusinessIds()) ? Lists.partition(request.getBusinessIds(), batchSize) : null;

		if (CollectionUtils.isNotEmpty(businessNumbers)) {
			return getBusinessBulkRequestsForBusinessNumbers(businessNumbers);
		} else if (CollectionUtils.isNotEmpty(businessIds)) {
			return getBusinessBulkRequestsForBusinessIds(businessIds);
		}

		return Collections.emptyList();
	}

	private List<BusinessBulkRequest> getBusinessBulkRequestsForBusinessNumbers(List<List<Long>> businessNumbers) {
		List<BusinessBulkRequest> businessBulkRequests = new ArrayList<>();
		businessNumbers.forEach(businessNumber -> businessBulkRequests.add(new BusinessBulkRequest(businessNumber, null)));

		return businessBulkRequests;
	}

	private List<BusinessBulkRequest> getBusinessBulkRequestsForBusinessIds(List<List<Integer>> businessIds) {
		List<BusinessBulkRequest> businessBulkRequests = new ArrayList<>();
		businessIds.forEach(businessId -> businessBulkRequests.add(new BusinessBulkRequest(null, businessId)));

		return businessBulkRequests;
	}

	@Override
	public Map<String,Object> getBusinessesInBulkByBusinessIds(List<Integer> businessIds,boolean locationRequired) {
		BusinessBulkRequest request = new BusinessBulkRequest();
		request.setBusinessIds(businessIds);
		return getBusinessInBulk(request,true);
	}

	private Map<String,Object> getBusinessInBulk(BusinessBulkRequest request ,boolean locationRequired){
		String coreUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = coreUrl.concat(GET_BUSINESS_BULK_BY_BUSINESS_IDS);
		HttpHeaders headers = new HttpHeaders();
		headers.add(ACCEPT,MediaType.APPLICATION_JSON_VALUE);
		headers.add(SERVICE_NAME, SOCIAL);

		List<BusinessBulkRequest> businessBulkRequests = getBusinessBulkRequestsInBatches(request,
				CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBusinessDetailsBatchSize());
		Map finalResponse = new HashMap<>();
		businessBulkRequests.parallelStream().forEach(businessBulkRequest -> {
			HttpEntity<BusinessBulkRequest> requestEntity = new HttpEntity<>(businessBulkRequest, headers);

			UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url).queryParam(LOCATION, locationRequired);
			ResponseEntity<Map> response;
			try{
				response = businessCoreRestClient.exchange(builder.build().toString(), HttpMethod.POST, requestEntity, Map.class);
				finalResponse.putAll(response.getBody());
			}catch (HttpStatusCodeException ex){
				if (ex.getStatusCode().is5xxServerError()) {
					logger.warn("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
					throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
				}
				if (ex.getStatusCode().is4xxClientError()) {
					logger.warn("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
					throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
				}
			}
		});

		return finalResponse;
		/*String businessCoreServiceUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_BULK_BY_BUSINESS_IDS);
		HttpHeaders headers = new HttpHeaders();
		headers.add(ACCEPT,MediaType.APPLICATION_JSON_VALUE);
		headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<BusinessBulkRequest> requestEntity = new HttpEntity<>(request, headers);

		UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url).queryParam(LOCATION,locationRequired);
		ResponseEntity<Map> response;
		try{
			response = businessCoreRestClient.exchange(builder.build().toString(), HttpMethod.POST, requestEntity, Map.class);
			return response.getBody();
		}catch (HttpStatusCodeException ex){
			if (ex.getStatusCode().is5xxServerError()) {
				logger.warn("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.warn("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return new HashMap<>();*/
	}

	@Override
	public List<LocationDetailsDTO> getBusinessLocations(Integer businessId) {
		String businessCoreServiceUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_LOCATIONS).concat(businessId.toString());
		List<LocationDetailsDTO> locationList = new ArrayList<>();
		HttpHeaders headers = new HttpHeaders();
		headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		try{
			ResponseEntity<LocationDetailsDTO[]> locationDetailsDTOResponseEntity =  businessCoreRestClient.exchange(url,HttpMethod.GET,entity,LocationDetailsDTO[].class);
			if (locationDetailsDTOResponseEntity.getStatusCode().equals(HttpStatus.OK)){
				locationList = Arrays.asList(locationDetailsDTOResponseEntity.getBody());
			} else{
				logger.error("Error while calling business core service for business options : response code:{}",locationDetailsDTOResponseEntity.getStatusCode());
			}
		}catch (HttpStatusCodeException ex){
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return  locationList;
	}

	@Override
	public List<BusinessLiteDTO> getBusinessLiteListByEnterpriseId(Integer businessId) {
		String businessCoreServiceUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_LOCATION).concat(businessId.toString());
		List<BusinessLiteDTO> locationList = new ArrayList<>();
		HttpHeaders headers = new HttpHeaders();
		headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		try{
			ResponseEntity<BusinessLiteDTO[]> locationDetailsDTOResponseEntity =  businessCoreRestClient.exchange(url,HttpMethod.GET,entity,BusinessLiteDTO[].class);
			if (locationDetailsDTOResponseEntity.getStatusCode().equals(HttpStatus.OK)){
				locationList = Arrays.asList(locationDetailsDTOResponseEntity.getBody());
			} else{
				logger.error("Error while calling business core service for business options : response code:{}",locationDetailsDTOResponseEntity.getStatusCode());
			}
		}catch (HttpStatusCodeException ex){
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return  locationList;
	}

	@Override
	public BusinessLiteUserDTO getEnterpriseUsers(Integer enterpriseId) {
		BusinessLiteUserDTO businessUserLiteDTO = new BusinessLiteUserDTO();
		String businessCoreServiceUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_USERS_LITE);
		HttpHeaders headers = new HttpHeaders();
		headers.add(ACCEPT,MediaType.APPLICATION_JSON_VALUE);
		headers.add(SERVICE_NAME, SOCIAL);

		UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(url)
				.queryParam(PAGESIZE,"10000")
				.queryParam(SORTBY,"name")
				.queryParam(SORTORDER,"1")
				.queryParam(STARTINDEX,"0")
				.queryParam(ENTERPRISEUSER,"true")
				.buildAndExpand(enterpriseId);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		HttpEntity<BusinessLiteUserDTO> response;

		try{
			response = businessCoreRestClient.exchange(uriComponents.toUriString(),HttpMethod.GET,entity,BusinessLiteUserDTO.class);
			businessUserLiteDTO = response.getBody();
		}catch (HttpStatusCodeException ex){
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return businessUserLiteDTO;
	}

	@Override
	public BusinessLiteUserDTO getEnterpriseUsersWithLocation(Integer enterpriseId,List<Integer> businessIds) {
		String businessCoreServiceUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_USERS_LITE_WITH_LOCATION).concat("?accountId=").concat(enterpriseId.toString())
				.concat("&all=true&sortBy=name");
		BusinessLiteUserDTO businessUserLiteDTO =  new BusinessLiteUserDTO();
		BusinessBulkRequest businessBulkRequest= new BusinessBulkRequest();
		businessBulkRequest.setBusinessIds(businessIds);
		HttpHeaders headers = new HttpHeaders();
		//headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<BusinessBulkRequest> requestEntity = new HttpEntity<>(businessBulkRequest, headers);
//		headers.add(SERVICE_NAME, SOCIAL);
		//HttpEntity<BusinessLiteUserDTO> requestEntity = new HttpEntity<>(new BusinessLiteUserDTO(), headers);
		try {
			ResponseEntity<BusinessLiteUserDTO> businessLiteUserDTOResponseEntity = businessCoreRestClient.exchange(url, HttpMethod.POST, requestEntity, BusinessLiteUserDTO.class);
			if (businessLiteUserDTOResponseEntity.getStatusCode().equals(HttpStatus.OK)) {
				businessUserLiteDTO = businessLiteUserDTOResponseEntity.getBody();
			} else {
				logger.error("Error while calling business core service for business options : response code:{}", businessLiteUserDTOResponseEntity.getStatusCode());
			}
		} catch (HttpStatusCodeException ex) {
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return businessUserLiteDTO;
	}

	@Override
	public BusinessDomainDTO getBusinessDomain(Integer businessId) {

		String businessCoreServiceUrl = getCoreServiceUrl();
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_DOMAIN).concat(businessId.toString());
		BusinessDomainDTO businessDomainDTO = new BusinessDomainDTO();
		HttpHeaders headers = new HttpHeaders();
//		headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		try {
			logger.info("URL for business domain: {}", url);
			ResponseEntity<BusinessDomainDTO> businessDomainDTOResponseEntity = businessCoreRestClient.exchange(url, HttpMethod.GET, entity, BusinessDomainDTO.class);
			if (businessDomainDTOResponseEntity.getStatusCode().equals(HttpStatus.OK)) {
				businessDomainDTO = businessDomainDTOResponseEntity.getBody();
			} else {
				logger.error("Error while calling business core service for business options : response code:{}", businessDomainDTOResponseEntity.getStatusCode());
			}
		} catch (HttpStatusCodeException ex) {
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return businessDomainDTO;
	}

	@Override
	public BusinessOptionsDTO getBusinessOptions(Integer businessId) {
		String businessCoreServiceUrl = getCoreServiceUrl();
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_OPTIONS).concat(businessId.toString().concat("&traversehierarchy=true"));
		BusinessOptionsDTO businessOptionsDTO = new BusinessOptionsDTO();
		HttpHeaders headers = new HttpHeaders();
//		headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		try {
			ResponseEntity<BusinessOptionsDTO> businessOptionsDTOResponseEntity = businessCoreRestClient.exchange(url, HttpMethod.GET, entity, BusinessOptionsDTO.class);
			if (businessOptionsDTOResponseEntity.getStatusCode().equals(HttpStatus.OK)) {
				businessOptionsDTO = businessOptionsDTOResponseEntity.getBody();
			} else {
				logger.error("Error while calling business core service for business options : response code:{}", businessOptionsDTOResponseEntity.getStatusCode());
			}
		} catch (HttpStatusCodeException ex) {
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return businessOptionsDTO;
	}

	@Override
	public Integer getBusinessId(Long businessNumber) {
		BusinessLiteDTO businessLiteDTO = getBusinessLiteByNumber(businessNumber);
		return businessLiteDTO != null ? businessLiteDTO.getBusinessId() : null;
	}

	@Override
	//@Cacheable(value = BUSINESS_LITE_INFO, key = "#businessNumber.toString()", unless = "#result == null")
	public BusinessLiteDTO getBusinessLiteByNumber(Long businessNumber) {
		return getBusinessLiteByNumber(businessNumber,false);
	}
	@Override
	public BusinessLiteDTO getBusinessLiteByNumber(Long businessNumber,boolean locationRequired) {
		String key = keyPrefix + BUSINESS_LITE_INFO + "_" + businessNumber;
		Object businessValueFromCache = redisTemplate.opsForValue().get(key);
		if(Objects.isNull(businessValueFromCache)) {
			BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest();
			businessLiteRequest.setKey("businessNumber");
			businessLiteRequest.setValue(businessNumber);
			businessLiteRequest.setLocationRequired(locationRequired);
			BusinessLiteDTO businessLiteDTO  = getBusinessLite(businessLiteRequest);
			redisTemplate.opsForValue().set(key, JSONUtils.toJSON(businessLiteDTO), 43200l, TimeUnit.SECONDS);
			return businessLiteDTO;
		} else {
			return JSONUtils.fromJSON((String) businessValueFromCache, BusinessLiteDTO.class);
		}
	}

	@Override
	public String getBusinessTimezone(Integer businessId) {
		final String businessCoreServiceUrl = getCoreServiceUrl();
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_TIMEZONE).concat(businessId.toString());
		HttpHeaders headers = new HttpHeaders();
//		headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		try {
			ResponseEntity<String> timezoneStr = businessCoreRestClient.exchange(url, HttpMethod.GET, entity, String.class);
			if (timezoneStr.getStatusCode().equals(HttpStatus.OK)) {
				return timezoneStr.getBody();
			} else {
				logger.error("Error while calling business core service for timezone : response code:{}", timezoneStr.getStatusCode());
			}
		} catch (HttpStatusCodeException ex) {
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return null;
	}

	@Override
	public BusinessHours getBusinessHours(Integer businessId) {
		final String businessCoreServiceUrl = getCoreServiceUrl();
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_HOURS).concat(businessId.toString());
		HttpHeaders headers = new HttpHeaders();
//		headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		try {
			ResponseEntity<BusinessHours> timezoneStr = businessCoreRestClient.exchange(url, HttpMethod.GET, entity, BusinessHours.class);
			if (timezoneStr.getStatusCode().equals(HttpStatus.OK)) {
				return timezoneStr.getBody();
			} else {
				logger.error("Error while calling business core service for hours : response code:{}", timezoneStr.getStatusCode());
			}
		} catch (HttpStatusCodeException ex) {
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return null;
	}

	@Override
	@Cacheable(value = USER_INFO_CACHE, key = "#userId.toString()", unless = "#result == null")
	public BusinessCoreUser getUserInfo(Integer userId) {
		final String businessCoreServiceUrl = getCoreServiceUrl();
		String url = businessCoreServiceUrl.concat(GET_USER).concat(userId.toString());
		HttpHeaders headers = new HttpHeaders();
//		headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		try {
			ResponseEntity<BusinessCoreUser> bizCoreUser = businessCoreRestClient.exchange(url, HttpMethod.GET, entity, BusinessCoreUser.class);
			if (bizCoreUser.getStatusCode().equals(HttpStatus.OK)) {
				return bizCoreUser.getBody();
			} else {
				logger.error("Error while calling business core service for user : response code:{}", bizCoreUser.getStatusCode());
			}
		} catch (HttpStatusCodeException ex) {
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return null;
	}

	@Override
//	@Cacheable(value = USER_FULL_NAME_VALUE, key = "#userId.toString()", unless = "#result == null")
	public String getFullUsername(Integer userId) {
		String name = null;

		try {
			BusinessCoreUser user = getUserInfo(userId);
			if (Objects.isNull(user)) {
				logger.info("No User found for userId: {}", userId);
				return name;
			}

			String firstName = user.getFirstName();
			String lastName = user.getLastName();
			String emailId = user.getEmailId();

			if (name == null) {
				if (StringUtils.isBlank(firstName) &&
						StringUtils.isBlank(lastName) &&
						StringUtils.isNotBlank(emailId)) {
					name = emailId.substring(0, emailId.indexOf("@"));
				} else if (StringUtils.isNotBlank(firstName) ||
						StringUtils.isNotBlank(lastName)) {
					name = firstName;
					if (StringUtils.isNotBlank(lastName)) {
						if (StringUtils.isBlank(name)) {
							name = lastName;
						} else {
							name = name + " " + lastName;
						}
					}
				}
			}
		} catch (Exception ex) {
			logger.info("error while fetching userDetails for userId: {}", userId);
			name = "";
		}
		return name;
	}


	//todo: listen to business update data event to update cache
	@Override
	@Cacheable(value = CACHE_VALUE, key = "#businessId.toString().concat(#locationRequired.toString())", unless = "#result == null")
	public BusinessLiteDTO getBusinessLite(Integer businessId, Boolean locationRequired) {
		logger.info("BusinessLite call for businessId: {}", businessId);
		BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest();
		businessLiteRequest.setKey("businessId");
		businessLiteRequest.setValue(businessId);
		businessLiteRequest.setLocationRequired(locationRequired);
		return this.getBusinessLite(businessLiteRequest);

	}

	@Override
	@CacheEvict(value = CACHE_VALUE,key = "#businessId.toString().concat(#locationRequired.toString())")
	public void clearBusinessCache(Integer businessId, Boolean locationRequired) {
		logger.info("Business cache evicted for businessId {}", businessId);
	}

	@Override
	//@CacheEvict(value = BUSINESS_LITE_INFO,key = "#businessNumber.toString()")
	public void clearBusinessCache(Long businessNumber) {
		logger.info("Business cache evicted for businessNumber {}", businessNumber);
		redisTemplate.delete(keyPrefix+BUSINESS_LITE_INFO+"_"+Long.toString(businessNumber));
	}

	@Override
	@CachePut(value = CACHE_VALUE, key = "#businessId.toString()", unless = "#result == null")
	public BusinessLiteDTO getBusinessLiteWithUpdated(Integer businessId) {
		BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest();
		businessLiteRequest.setKey("businessId");
		businessLiteRequest.setValue(businessId);
		businessLiteRequest.setLocationRequired(true);
		return this.getBusinessLite(businessLiteRequest);
	}

	@Override
	public CustomHierarchyToLocMapping getCustomHierarchyToLocationMapping(Integer accountId, String levelAlias) {
		final StringBuilder businessCoreServiceUrl = new StringBuilder(getCoreServiceUrl());

		businessCoreServiceUrl.append(CUSTOM_HIERARCHY_TO_LOC_DETAILS);

		HttpHeaders headers = new HttpHeaders();
		headers.add(ACCEPT, MediaType.APPLICATION_JSON_VALUE);
//			headers.add(SERVICE_NAME, SOCIAL);
		UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(businessCoreServiceUrl.toString())
				.queryParam("levelAlias", levelAlias)
				.buildAndExpand(accountId);
		String url = uriComponents.toUriString();

		HttpEntity<?> entity = new HttpEntity<>(headers);
		ResponseEntity<CustomHierarchyToLocMapping> response;
		CustomHierarchyToLocMapping customHierarchyToLocMapping = null;
		try {
			response = businessCoreRestClient.exchange(url, HttpMethod.GET, entity, CustomHierarchyToLocMapping.class);
			if (response.getStatusCode().is2xxSuccessful()) {
				customHierarchyToLocMapping = response.getBody();
			}

		} catch (HttpStatusCodeException ex) {
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				// logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				//throw new ExternalAPIException(ExternAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return customHierarchyToLocMapping;

	}

	@Override
	public BusinessProfileDTO getProfileData(Long businessId) {
		String businessCoreServiceUrl = getCoreServiceUrl();
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_PROFILE).concat(businessId.toString());
		HttpHeaders headers = new HttpHeaders();
//		headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		logger.info("url for getProfileData: {}", url);
		ResponseEntity<BusinessProfileDTO> businessProfileResponse = businessCoreRestClient.exchange(url, HttpMethod.GET, entity, BusinessProfileDTO.class);
		if (businessProfileResponse.getStatusCode().equals(HttpStatus.OK)) {
			logger.info("BusinessProfileDTO response: {}", businessProfileResponse.getBody());
			return businessProfileResponse.getBody();
		} else {
			logger.error("Error while calling business core service for business profile : response code:{}", businessProfileResponse.getStatusCode());
			return null;
		}
	}

	@Override
	public List<BusinessBizLiteDto> getBusinessLiteDtoByBusinessIds(List<Integer> businessIds) {
		String businessCoreServiceUrl = getCoreServiceUrl();
		String url = businessCoreServiceUrl.concat(GET_BIZ_LITE);

		List<BusinessBizLiteDto> businessLiteDTO = new ArrayList<>();
		try {
			ResponseEntity<BusinessBizLiteDto[]> response = businessCoreRestClient.postForEntity(url, businessIds,
					BusinessBizLiteDto[].class);
			if (response.getStatusCode().equals(HttpStatus.OK)) {
				businessLiteDTO = Arrays.asList(response.getBody());
			} else {
				logger.error("Error while calling business core service for business get biz : response code:{}", response.getStatusCode());
			}
		} catch (HttpStatusCodeException ex) {
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return businessLiteDTO;
	}

	@Override
	public BusinessCategoriesDTO getBusinessCategories(Integer businessId) {
		String businessCoreServiceUrl = getCoreServiceUrl();
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_CATEGORIES).concat("?businessId=").concat(businessId.toString());
		HttpHeaders headers = new HttpHeaders();
//		headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		logger.info("Getting list of categories for businessId: {}, url: {}", businessId, url);
		ResponseEntity<BusinessCategoriesDTO> businessCategoriesResponse = businessCoreRestClient.exchange(url, HttpMethod.GET, entity, BusinessCategoriesDTO.class);
		if (businessCategoriesResponse.getStatusCode().equals(HttpStatus.OK)) {
			return businessCategoriesResponse.getBody();
		} else {
			logger.error("Error while calling business core service for business category : response code:{}", businessCategoriesResponse.getStatusCode());
			return null;
		}
	}

	@Override
	public void uploadMediaToCDN(CDNUploadDTO uploadDTO) {
		String businessCoreServiceUrl = getCoreServiceUrl();
		String url = businessCoreServiceUrl.concat(CDN_IMAGE_UPLOAD).concat(SOCIAL);
		HttpHeaders headers = new HttpHeaders();
		HttpEntity<?> entity = new HttpEntity<>(uploadDTO, headers);
		logger.info("uploading media to cdn, url: {} , payload: {}", url, uploadDTO);
		ResponseEntity<CDNResponseDTO> cdnUploadResponse = businessCoreRestClient.exchange(url, HttpMethod.POST, entity, CDNResponseDTO.class);
		if (cdnUploadResponse.getStatusCode().equals(HttpStatus.OK) || cdnUploadResponse.getStatusCode().equals(HttpStatus.ACCEPTED)) {
			logger.info("uploading media to cdn, response: {}", cdnUploadResponse.getBody());
		} else {
			logger.error("Error while calling uploadMediaToCDN, response code:{}", cdnUploadResponse.getStatusCode());
		}
	}

	@Override
	public BusinessLinksInfo getBusinessLinksById(Integer businessId) {
		String businessCoreServiceUrl = getCoreServiceUrl();
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_LINKS) + businessId;
		HttpHeaders headers = new HttpHeaders();
		headers.add(ACCEPT, MediaType.APPLICATION_JSON_VALUE);
		headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<HttpHeaders> httpEntity = new HttpEntity<>(headers);
		BusinessLinksInfo businessLinksInfo = new BusinessLinksInfo();
		try {
			ResponseEntity<BusinessLinksInfo> response = businessCoreRestClient.exchange(url, HttpMethod.GET, httpEntity, BusinessLinksInfo.class);
			if (response.getStatusCode().equals(HttpStatus.OK)) {
				businessLinksInfo = response.getBody();
			} else {
				logger.error("Error while calling business core service for business get links : response code:{}", response.getStatusCode());
			}
		} catch (HttpStatusCodeException ex) {
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return businessLinksInfo;
	}

	@Override
	public BusinessSocialEnabled getEnabledProperty(Long businessNumber) {
		String businessCoreServiceUrl = getCoreServiceUrl();
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_PROPERTY) + businessNumber;
		HttpHeaders headers = new HttpHeaders();
		headers.add(ACCEPT, MediaType.APPLICATION_JSON_VALUE);
		headers.add(SERVICE_NAME, SOCIAL);
		HttpEntity<HttpHeaders> httpEntity = new HttpEntity<>(headers);
		BusinessSocialEnabled businessSocialEnabled = new BusinessSocialEnabled();
		try {
			ResponseEntity<BusinessSocialEnabled> response = businessCoreRestClient.exchange(url, HttpMethod.GET, httpEntity, BusinessSocialEnabled.class);
			if (response.getStatusCode().equals(HttpStatus.OK)) {
				businessSocialEnabled = response.getBody();
			} else {
				logger.error("Error while calling business core service for business get links : response code:{}", response.getStatusCode());
			}
		} catch (HttpStatusCodeException ex) {
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				if(ex.getResponseBodyAsString().contains("Business id is invalid")){
					logger.info("Business id is invalid for businessNumber: {}", businessNumber);
				}else{
					logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				}
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return businessSocialEnabled;
	}

	@Override
	public String getWebsiteDomain(Integer businessId) {
		String businessCoreServiceUrl = getCoreServiceUrl();
		String url = businessCoreServiceUrl.concat(DOMAIN_ROUTE).concat(String.valueOf(businessId));
		String domain = null;
		ResponseEntity<DomainMessage> responseEntity = null;
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.set("SERVICE-NAME", SERVICE_NAME);
			HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
			logger.info("Request to get website domain with url: {}", url);
			responseEntity = businessCoreRestClient.exchange(url, HttpMethod.GET, httpEntity, DomainMessage.class);
		} catch (Exception e) {
			logger.info("Website Domain Fetch call failed: {}", e.getMessage());
		}
		if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
			logger.info("Response of website domain {}", responseEntity.getBody());
			domain = responseEntity.getBody().getDomain();
			if (responseEntity.getBody().getSecureEnabled() == 1) {
				domain = Constants.SECURE_PROTOCOL + domain;
			} else {
				domain = Constants.NON_SECURE_PROTOCOL + domain;
			}
		} else {
			logger.info("Website Domain Fetch call failed, returning default domain {}", Constants.DEFAULT_URL);
			domain = Constants.DEFAULT_URL;
		}
		return domain;
	}

	@Override
	public BusinessSocialNotificationDTO getSocialNotificationUsersList(Integer businessId) {
		try {
			String businessCoreServiceUrl = getCoreServiceUrl();
			String url = businessCoreServiceUrl.concat(USER_NOTIFICATION_DETAILS);

			List<String> requestBody = new ArrayList<>();

			HttpHeaders headers = new HttpHeaders();
			headers.add(SERVICE_NAME, SOCIAL);
			headers.add(BUSINESS_ID, businessId.toString());
			HttpEntity<?> entity = new HttpEntity<>(requestBody, headers);

			ResponseEntity<BusinessSocialNotificationDTO> response = businessCoreRestClient.exchange(url, HttpMethod.POST, entity, BusinessSocialNotificationDTO.class);
			if (response.getStatusCode().equals(HttpStatus.OK)) {
				return response.getBody();
			} else {
				logger.error("Error while calling business core service for social notification users : response code:{} for businessId {}", response.getStatusCode(), businessId);
				return null;
			}
		} catch (Exception ex) {
			logger.error("Error while fetching email IDs of users for businessId {} with error {}", businessId, ex);
			return null;
		}
	}

	private String getCoreServiceUrl() {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
	}

	@Override
	public List<BusinessCoreUser> getUserDetails(Collection<Long> userIds) {
		if (CollectionUtils.isEmpty(userIds)) {
			return Collections.emptyList();
		}

		StringBuilder url = new StringBuilder(getCoreServiceUrl()).append(FETCH_USER_DETAILS);
		logger.info("[BusinessCoreServiceImpl] getUserDetails URI :{} - Calling for userIds :{}", FETCH_USER_DETAILS, userIds);
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
			headers.add(SERVICE_NAME, SOCIAL);

			Map<String, Object> inputRequest = new HashMap<>();
			inputRequest.put("userIds", userIds);

			HttpEntity<Map<String, Object>> entity = new HttpEntity<>(inputRequest, headers);
			ResponseEntity<BusinessCoreUserDetailsResponse> responseEntity = businessCoreRestClient.exchange(url.toString(), HttpMethod.POST, entity, BusinessCoreUserDetailsResponse.class);
			logger.info("[BusinessCoreServiceImpl] getUserDetails URI :{} - Successful for userIds :{}", FETCH_USER_DETAILS, userIds);
			if (Objects.nonNull(responseEntity.getBody())) {
				return responseEntity.getBody().getUsers();
			}
		} catch (RestClientException e) {
			logger.info("[BusinessCoreServiceImpl] getUserDetails RestClientException occurred while getting user details", e);
		} catch (Exception e) {
			logger.info("[BusinessCoreServiceImpl] getUserDetails Exception occurred while getting user details", e);
		}
		return Collections.emptyList();
	}

	@Override
	public BusinessLiteUserDTO getUserDetailsWithRole(Integer enterpriseId) {
		BusinessLiteUserDTO businessUserLiteDTO = new BusinessLiteUserDTO();
		String businessCoreServiceUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_USERS_LITE);
		HttpHeaders headers = new HttpHeaders();
		headers.add(ACCEPT, MediaType.APPLICATION_JSON_VALUE);
		headers.add(SERVICE_NAME, SOCIAL);

		UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(url)
				.queryParam(PAGESIZE, "10000")
				.queryParam(SORTBY, "name")
				.queryParam(SORTORDER, "0")
				.queryParam(STARTINDEX, "0")
				.buildAndExpand(enterpriseId);
		HttpEntity<?> entity = new HttpEntity<>(headers);
		HttpEntity<BusinessLiteUserDTO> response;

		try {
			response = businessCoreRestClient.exchange(uriComponents.toUriString(), HttpMethod.GET, entity, BusinessLiteUserDTO.class);
			businessUserLiteDTO = response.getBody();
		} catch (HttpStatusCodeException ex) {
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return businessUserLiteDTO;

	}

	@Override
	public BusinessCoreUsersBulk getUserInfoInBulk(BusinessCoreUserBulkRequest request) {
		final String businessCoreServiceUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = businessCoreServiceUrl.concat(GET_USERS_IN_BULK);
		try {
			ResponseEntity<BusinessCoreUsersBulk> response = businessCoreRestClient.postForEntity(url, request, BusinessCoreUsersBulk.class);
			if (response.getStatusCode().equals(HttpStatus.OK)) {
				return response.getBody();
			} else {
				logger.error("Error while calling business core service for user : response code:{}", response.getStatusCode());
			}
		} catch (HttpStatusCodeException ex) {
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return null;
	}

	@Override
	public Map<Integer, BusinessCoreUser> getBusinessUserForUserId(List<Integer> userIds) {
		Map<Integer, BusinessCoreUser> responseMap = new HashMap<>();
		if(CollectionUtils.isEmpty(userIds)) return responseMap;

		userIds = userIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
		List<List<Integer>> userIdsList = Lists.partition(userIds, 50);

		List<BusinessCoreUser> businessCoreUsers = new ArrayList<>();
		for(List<Integer> userIdsSubList: userIdsList) {
			BusinessCoreUserBulkRequest request = BusinessCoreUserBulkRequest.builder()
					.userIds(userIdsSubList)
					.build();
			BusinessCoreUsersBulk businessCoreUsersBulk = getUserInfoInBulk(request);
			if(Objects.nonNull(businessCoreUsersBulk) && CollectionUtils.isNotEmpty(businessCoreUsersBulk.getUsers())) {
				businessCoreUsers.addAll(businessCoreUsersBulk.getUsers());
			}
		}

		responseMap = businessCoreUsers.stream().collect(Collectors.toMap(s->s.getId(), s->s));
		return responseMap;
	}


	@Override
	public String getFullUsername(BusinessCoreUser user) {

		String name = null;
		if(Objects.isNull(user)) {
			logger.info("No User found");
			return name;
		}

		String firstName = user.getFirstName();
		String lastName = user.getLastName();
		String emailId = user.getEmailId();

		if(name == null){
			if(StringUtils.isBlank(firstName) &&
					StringUtils.isBlank(lastName) &&
					StringUtils.isNotBlank(emailId)){
				name = emailId.substring(0, emailId.indexOf("@"));
			}else if(StringUtils.isNotBlank(firstName) ||
					StringUtils.isNotBlank(lastName)){
				name = firstName;
				if(StringUtils.isNotBlank(lastName)){
					if(StringUtils.isBlank(name)){
						name = lastName;
					}else{
						name = name + " " + lastName;
					}
				}
			}
		}
		return name;
	}

	@Override
	//@Cacheable(value=USER_BUSINESS_ACCESS,unless = "#result == null",key="#accountId+'-'+#userId")
	public Collection<Integer> getAllowedBusinessIdsForUser(Integer accountId,Integer userId) {
		logger.info("[BusinessCoreServiceImpl][isLocationAccessibleToUser] Getting user access for location for user id : {} ", userId);
		if (Objects.isNull(accountId) || Objects.isNull(userId)) {
			return Collections.emptyList();
		}

		BusinessLocationDetailRequest businessLocationDetailRequest = new BusinessLocationDetailRequest();

		StringBuilder url = new StringBuilder(getCoreServiceUrl()).append(String.format(FETCH_USER_BUSINESS_IDS,accountId));

		UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url.toString())
				.queryParam(USER_ID, userId);

		HttpHeaders headers = new HttpHeaders();
		headers.add(ACCEPT, MediaType.APPLICATION_JSON_VALUE);
		headers.add(SERVICE_NAME, SOCIAL);

		HttpEntity<BusinessLocationDetailRequest> requestEntity = new HttpEntity<>(businessLocationDetailRequest, headers);

		List<Integer> userAllowedLocations = null;
		try {
			userAllowedLocations = businessCoreRestClient.exchange(builder.build().toString(), HttpMethod.POST, requestEntity, List.class).getBody();
		} catch (HttpStatusCodeException ex) {
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("[BusinessCoreServiceImpl][isLocationAccessibleToUser] InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("[BusinessCoreServiceImpl][isLocationAccessibleToUser] ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return userAllowedLocations;
	}

	@Override
	public BusinessAccountLocationResponse getAllLocationsDetailsUnderAccount(Integer accountId, Integer userId, BusinessAccountLocationRequest request) {
		String coreUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = coreUrl.concat(GET_RESELLER_LOCATIONS_DETAILS);
		HttpHeaders headers = new HttpHeaders();
		headers.add(ACCEPT, MediaType.APPLICATION_JSON_VALUE);
		headers.add(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
		headers.add("account-id", String.valueOf(accountId));
		headers.add("user-id", String.valueOf(Optional.ofNullable(userId).orElse(0)));
		HttpEntity<BusinessAccountLocationRequest> requestEntity = new HttpEntity<>(request, headers);
		logger.info("[BusinessCoreService] Request for location details {} with data: {}", url, requestEntity);
		try {
			ResponseEntity<BusinessAccountLocationResponse> responseEntity = businessCoreRestClient.exchange(url, HttpMethod.POST, requestEntity, BusinessAccountLocationResponse.class);
			if(responseEntity.getStatusCode().equals(HttpStatus.OK)) {
				logger.info("[BusinessCoreService] Received location details with response: {}", responseEntity.getBody());
				return responseEntity.getBody();
			} else {
				logger.error("Error while calling business core service: response code:{}, and error: {}", responseEntity.getStatusCode(), responseEntity.getBody());
			}
		} catch (HttpStatusCodeException ex) {
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return null;
	}

	@Override
	public BusinessCustomFieldDto getCustomFieldsTokenData(Integer id) {
		StringBuilder url = new StringBuilder(getCoreServiceUrl()).append(String.format(GET_BUSINESS_CUSTOM_FIELD,id));
		HttpHeaders headers = new HttpHeaders();
		HttpEntity<?> entity = new HttpEntity<>(headers);
		logger.info("url for get custom field data: {}", url);
		ResponseEntity<BusinessCustomFieldDto> businessCustomFieldResponse = businessCoreRestClient.exchange(url.toString(), HttpMethod.GET, entity, BusinessCustomFieldDto.class);
		if (businessCustomFieldResponse.getStatusCode().equals(HttpStatus.OK)) {
			logger.info("BusinessCustomFieldDto response: {}", businessCustomFieldResponse.getBody());
			return businessCustomFieldResponse.getBody();
		} else {
			logger.error("Error while calling business core service for business custom field : response code:{}", businessCustomFieldResponse.getStatusCode());
			return null;
		}
	}
	@Override
	public List<Long> getBusinessSubHierarchyList(Long businessNumber, GetBusinessHierarchyList businessHierarchyList) {
		String businessCoreServiceUrl = getCoreServiceUrl();
		String url = businessCoreServiceUrl.concat(GET_BUSINESS_SUBHIERARCHY_LIST).concat(businessNumber.toString());

		HttpHeaders headers = new HttpHeaders();
		HttpEntity<GetBusinessHierarchyList> entity = new HttpEntity<>(businessHierarchyList, headers);

		logger.info("Get business sub hierarchy list, url: {} , payload: {}", url, businessHierarchyList);

		ResponseEntity<List<Long>> subHierarchyListResponse = businessCoreRestClient.exchange(
				url, HttpMethod.POST, entity, new ParameterizedTypeReference<List<Long>>() {});

		if (subHierarchyListResponse.getStatusCode().equals(HttpStatus.OK) ||
				subHierarchyListResponse.getStatusCode().equals(HttpStatus.ACCEPTED)) {

			List<Long> subHierarchyList = subHierarchyListResponse.getBody();
			logger.info("Business sub hierarchy list retrieved successfully, response: {}", subHierarchyList);
			return subHierarchyList;

		} else {
			logger.error("Error while calling getBusinessSubHierarchyList, response code: {}",
					subHierarchyListResponse.getStatusCode());
			return Collections.emptyList();  // Return an empty list in case of error
		}
	}

	@Override
	public BusinessUserLocationResponse getUserLocationsDetailsAccounts(Integer accountId, AccountListFilter request) {
		String coreUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(CORE_SERVICE_BASE_URL);
		String url = String.format(BUSINESS_SETUP_ACCOUNT_LIST_URI, accountId);
		HttpHeaders headers = new HttpHeaders();
		headers.add(ACCEPT, MediaType.APPLICATION_JSON_VALUE);
		headers.add(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

		HttpEntity<AccountListFilter> requestEntity = new HttpEntity<>(request, headers);
		logger.info("[BusinessCoreService] Request for User location details {} with data: {}", url, requestEntity);
		try {
			ResponseEntity<BusinessUserLocationResponse> responseEntity = businessCoreRestClient.exchange(coreUrl.concat(url), HttpMethod.POST, requestEntity, BusinessUserLocationResponse.class);
			if(responseEntity.getStatusCode().equals(HttpStatus.OK)) {
				logger.info("[BusinessCoreService] Received User  location details with response: {}", responseEntity.getBody());
				return responseEntity.getBody();
			} else {
				logger.error("Error while calling business core service: response code:{}, and error: {}", responseEntity.getStatusCode(), responseEntity.getBody());
			}
		} catch (HttpStatusCodeException ex) {
			if (ex.getStatusCode().is5xxServerError()) {
				logger.error("InternalServerException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
			}
			if (ex.getStatusCode().is4xxClientError()) {
				logger.error("ClientException while calling core service for URL {} and exception {}", url, ex.getResponseBodyAsString());
				throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
			}
		}
		return null;
	}

}
