/**
 * 
 */
package com.birdeye.social.external.service;

import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

import com.birdeye.social.platform.PlatformApi;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 *
 */
//TODO: Fix class name.
@Service
public class KafkaProducerServiceImpl implements KafkaProducerService {
	
	@Autowired
	private KafkaTemplate<String, Object> kafkaTemplate;
	
	@Autowired
	private KafkaTemplate<String, Object> newKafkaTemplate;
	@Autowired
	private KafkaTemplate<String, Object> newKafkaTemplateUS;
	
	@Autowired
	private ObjectMapper objectMapper;
	
	@Autowired
	private PlatformApi platformApi;
	
	private static final Logger		LOGGER	= LoggerFactory.getLogger(KafkaProducerServiceImpl.class);
	
	@Override
	public void send(String topic,Integer activityId, Object payload) {
		LOGGER.info("Pushing mention to kafka. Topic : {} , Activity Id : {}", topic, activityId);
		try {
			final String data=objectMapper.writeValueAsString(payload);
			ListenableFuture<SendResult<String, Object>> future =kafkaTemplate.send(topic, data);
			future.addCallback(new ListenableFutureCallback<SendResult<String, Object>>() {
				@Override
				public void onSuccess(SendResult<String, Object> result) {
					LOGGER.info("[Kafka Producer] Message for activity id {} has been push to kafka",activityId);
				}
				@Override
				public void onFailure(Throwable ex) {
					LOGGER.error("[Kafka Producer] Error occurs while pushing the message for activity id {} to kafka and error {}",activityId,ex.getMessage());
					try {
						Boolean processed=platformApi.processSocialMentions(data);
						LOGGER.info("[Kafka Producer] Message for activity id {} has been processed with API with status {}",activityId,processed);
					} catch (IOException exe) 
					{
						LOGGER.error("[Kafka Producer] Error occurs while processing the activity through API for activity id {} and error {}",activityId,exe.getMessage());
					}
				}
			});
		} catch (Exception e) {
			LOGGER.error("Exception while pushing activity Id :: {} to kafka topic :: {} is {}", activityId, topic, e);
		}
	}
	
	@Override
	public boolean sendWithKey(String kafkaTopic, String key, Object payload) {
		LOGGER.info("Pushing message to kafka. Topic : {} , key : {}", kafkaTopic, key);
		try {
			final String data = objectMapper.writeValueAsString(payload);
			ListenableFuture<SendResult<String, Object>> future = kafkaTemplate.send(kafkaTopic, key, data);
			future.addCallback(new ListenableFutureCallback<SendResult<String, Object>>() {
				@Override
				public void onSuccess(SendResult<String, Object> result) {
					LOGGER.info("Message successfully pushed to kafka topic {} :: {}", kafkaTopic, payload);
				}
				
				@Override
				public void onFailure(Throwable ex) {
					LOGGER.error("Message failed to push to kafka topic {} :: {}", kafkaTopic, payload);
				}
				
			});
		} catch (Exception e) {
			LOGGER.error("Exception while pushing key :: {} to kafka topic :: {} is {}", key, kafkaTopic, e);
			return false;
		}
		return true;
	}
	
	@Override
	public boolean sendObject(String kafkaTopic, Object payload) {
		LOGGER.info("Pushing message to kafka. Topic : {}", kafkaTopic);
		try {
			final String data = objectMapper.writeValueAsString(payload);
			ListenableFuture<SendResult<String, Object>> future = kafkaTemplate.send(kafkaTopic, data);
			future.addCallback(new ListenableFutureCallback<SendResult<String, Object>>() {
				@Override
				public void onSuccess(SendResult<String, Object> result) {
					LOGGER.info("Message successfully pushed to kafka topic {} :: {}", kafkaTopic, payload);
				}
				
				@Override
				public void onFailure(Throwable ex) {
					LOGGER.error("Message failed to push to kafka topic {} :: {}", kafkaTopic, payload);
				}
				
			});
		} catch (Exception e) {
			LOGGER.error("Exception while pushing to kafka topic :: {} is {}", kafkaTopic, e);
			return false;
		}
		return true;
	}
	
	// Producer service to use new kafka broker.
	@Override
	public boolean sendObjectV1(String kafkaTopic, Object payload) {
		LOGGER.info("Pushing message to kafka. Topic : {}", kafkaTopic);
		try {
			final String data = objectMapper.writeValueAsString(payload);
			ListenableFuture<SendResult<String, Object>> future = newKafkaTemplate.send(kafkaTopic, data);
			future.addCallback(new ListenableFutureCallback<SendResult<String, Object>>() {
				@Override
				public void onSuccess(SendResult<String, Object> result) {
					LOGGER.info("Message successfully pushed to kafka topic {} :: {}", kafkaTopic, payload);
				}
				
				@Override
				public void onFailure(Throwable ex) {
					LOGGER.error("Message failed to push to kafka topic {} :: {}", kafkaTopic, payload);
				}
				
			});
		} catch (Exception e) {
			LOGGER.error("Exception while pushing to kafka topic :: {} is {}", kafkaTopic, e);
			return false;
		}
		return true;
	}

	@Override
	public boolean sendObjectV1US(String kafkaTopic, Object payload) {
		LOGGER.info("Pushing message to US kafka. Topic : {}", kafkaTopic);
		try {
			final String data = objectMapper.writeValueAsString(payload);
			ListenableFuture<SendResult<String, Object>> future = newKafkaTemplateUS.send(kafkaTopic, data);
			future.addCallback(new ListenableFutureCallback<SendResult<String, Object>>() {
				@Override
				public void onSuccess(SendResult<String, Object> result) {
					LOGGER.info("Message successfully pushed to US kafka topic {} :: {}", kafkaTopic, payload);
				}

				@Override
				public void onFailure(Throwable ex) {
					LOGGER.error("Message failed to push to US kafka topic {} :: {}", kafkaTopic, payload);
				}

			});
		} catch (Exception e) {
			LOGGER.error("Exception while pushing to US kafka topic :: {} is {}", kafkaTopic, e);
			return false;
		}
		return true;
	}

	@Override
	public boolean sendObjectWithKeyV1(String key, String kafkaTopic, Object payload) {
		LOGGER.info("Pushing message to kafka Topic : {} and key : {}", kafkaTopic, key);
		try {
			final String data = objectMapper.writeValueAsString(payload);
			ListenableFuture<SendResult<String, Object>> future = newKafkaTemplate.send(kafkaTopic, key, data);
			future.addCallback(new ListenableFutureCallback<SendResult<String, Object>>() {
				@Override
				public void onSuccess(SendResult<String, Object> result) {
					LOGGER.info("Message successfully pushed to kafka topic {} :: {}", kafkaTopic, payload);
				}

				@Override
				public void onFailure(Throwable ex) {
					LOGGER.error("Message failed to push to kafka topic {} :: {}", kafkaTopic, payload);
				}

			});
		} catch (Exception e) {
			LOGGER.error("Exception while pushing to kafka topic :: {} is {}", kafkaTopic, e);
			return false;
		}
		return true;
	}
	
}
