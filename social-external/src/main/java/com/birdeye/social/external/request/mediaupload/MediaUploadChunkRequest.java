package com.birdeye.social.external.request.mediaupload;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import javassist.bytecode.ByteArray;
import lombok.Data;
import org.apache.http.util.ByteArrayBuffer;

import java.io.Serializable;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class MediaUploadChunkRequest implements Serializable {
//    {
//        "id": 123 // unique identifier per video url (As received by social)
//        "chunk": <Array Buffer>, // max buffer size - 5 MB
//        "partNumber": 1, // current chunk sequence
//            "finalChunk": false // true, if this is the last response
//    }
    private String id;
    private BufferArray chunk;
    private String chunkUrl;
    private Integer partNumber;
    private boolean finalChunk;
    private String message;
    private boolean error;

    @Override
    public String toString() {
        return "MediaUploadChunkRequest{" +
                "id='" + id + '\'' +
                ", chunkUrl='" + chunkUrl + '\'' +
                ", partNumber=" + partNumber +
                ", finalChunk=" + finalChunk +
                '}';
    }
}
