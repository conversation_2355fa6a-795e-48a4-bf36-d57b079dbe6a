package com.birdeye.social.external.service;

import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.TooManyRequestException;
import com.birdeye.social.external.exception.ExternalAPIErrorCode;
import com.birdeye.social.external.exception.ExternalAPIException;
import com.birdeye.social.facebook.FacebookApis;
import com.birdeye.social.instagram.exception.InstagramContainerException;
import com.birdeye.social.instagram.response.InstagramBaseResponse;
import com.birdeye.social.instagram.response.InstagramErrorResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;
import java.util.Objects;

@Service
public class InstagramPostServiceImpl implements InstagramPostService{

    private final Logger LOGGER = LoggerFactory.getLogger(InstagramPostServiceImpl.class);
    @Autowired
    @Qualifier("socialRestTemplate")
    private RestTemplate socialRestTemplate;

    private static final String INSTAGRAM_GRAPH_API_URL = "https://graph.facebook.com/";

    private static final String INSTAGRAM_GRAPH_API_VER = FacebookApis.VERSION.equals("") ? "v21.0/": FacebookApis.VERSION;

    @Override
    public String getMediaPostURL(String mediaId, String accessToken) {
        String url = INSTAGRAM_GRAPH_API_URL + INSTAGRAM_GRAPH_API_VER + mediaId+"?fields=permalink&access_token=" + accessToken;
        LOGGER.info("Getting url of posted IG media: {}",url);
        Map<String, ?> response;
        try {
            response = socialRestTemplate.getForObject(url, Map.class);
            return response.get("permalink").toString();
        }
        catch (TooManyRequestException ex) {
            LOGGER.info("Failed to get post url for post id: {} due to too many requests",mediaId);
            throw ex;
        }
        catch (Exception e) {
            LOGGER.info("Failed to get post url for post id: {}",mediaId);
            return null;
        }
    }

    private InstagramErrorResponse getIGErrorResponse(HttpStatusCodeException ex) {
        ObjectMapper mapper = new ObjectMapper();
        InstagramErrorResponse errorResponse = null;
        try {
            InstagramBaseResponse instagramBaseResponse = mapper.readValue(ex.getResponseBodyAsString(), InstagramBaseResponse.class);
            if(Objects.nonNull(instagramBaseResponse)) {
                errorResponse = instagramBaseResponse.getError();
            }
        } catch (Exception e) {
            LOGGER.info("Errror while getting IG error!!",e);
        }
        return errorResponse;
    }


    @Override
    @Retryable(value = InstagramContainerException.class, maxAttempts = 2, backoff = @Backoff(delay = 3000))
    public String uploadContent(String contentUrl,MultiValueMap<String, String> parametersMap) {
        String url = INSTAGRAM_GRAPH_API_URL + INSTAGRAM_GRAPH_API_VER + contentUrl;
        url = UriComponentsBuilder.fromHttpUrl(url).queryParams(parametersMap).build().encode().toUriString();
        LOGGER.info("uploading IG content, URL: {}",url);
        Map<String, ?> response = null;
        try {
            response = socialRestTemplate.postForObject(url, parametersMap, Map.class);
            return response.get("id").toString();
        } catch (TooManyRequestException e) {
            LOGGER.info("Too many request exception {}", e.getMessage());
            throw e;
        }
        catch (HttpStatusCodeException e) {
            LOGGER.info("[IG post] HTTP Exception during container creation: {} with error body: {}",e.getMessage(), e.getResponseBodyAsString());
            InstagramErrorResponse errorResponse = getIGErrorResponse(e);
            if(Objects.nonNull(errorResponse) && Objects.nonNull(errorResponse.getError_subcode()) &&
                    errorResponse.getError_subcode().equals(2207003)) {
                throw new InstagramContainerException("Container creation issue", ErrorCodes.INSTAGRAM_CONTAINER_CREATION_ERROR.value());
            }
            throw e;
        }
        catch (RestClientException e) {
            LOGGER.error("[IG post] Rest Exception during container creation: {}: ", e.getMessage());
            throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR,e.getMessage());
        }
        catch (Exception e) {
            LOGGER.info("[IG post] Exception during container creation: {}",e.getMessage());
            throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN,e.getMessage());
        }
    }

    @Override
    public String postContent(String igAccountId, String accessToken, String creationId) {
        MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
        String mediaPublishUrl = INSTAGRAM_GRAPH_API_URL + INSTAGRAM_GRAPH_API_VER + igAccountId+"/media_publish" +
                "?creation_id="+creationId+"&access_token="+accessToken;
        LOGGER.info("Posting IG content URL: {}",mediaPublishUrl);
        Map<String, ?> isPosted = null;
        try {
            isPosted = socialRestTemplate.postForObject(mediaPublishUrl, parametersMap, Map.class);
            return isPosted.get("id").toString();
        }
        catch (HttpStatusCodeException | TooManyRequestException e) {
            LOGGER.info("[IG post] HTTP Exception during posting on instagram: {}",e.getMessage());
            throw e;
        }
        catch (RestClientException e) {
            LOGGER.error("[IG post] Rest Exception during posting on instagram: {}: ", e.getMessage());
            throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR,e.getMessage());
        }
        catch (Exception e) {
            LOGGER.info("[IG post] Exception during posting on instagram: {}",e.getMessage());
            throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN,e.getMessage());
        }
    }

    @Override
    @Retryable( value = InstagramContainerException.class, maxAttempts = 15, backoff = @Backoff(delay = 10000))
    public String checkContainerStatus(String containerId, String accessToken) {
        String status;
        String statusCode;
        try {
            String url = INSTAGRAM_GRAPH_API_URL + containerId + "?fields=status_code,status&access_token=" + accessToken;
            LOGGER.info("checking IG container status url: {}",url);
            Map<String, ?> response = socialRestTemplate.getForObject(url, Map.class);
            status = response.get("status_code").toString();
            statusCode = response.get("status").toString();
            LOGGER.info("STATUS: {}, code: {}", status, statusCode);
            if (status.equalsIgnoreCase("ERROR")) {
                return statusCode;
            }
        }
        catch (HttpStatusCodeException | TooManyRequestException e) {
            LOGGER.info("[IG post] HTTP Exception during container status check: {}",e.getMessage());
            throw e;
        }
        catch (RestClientException e) {
            LOGGER.error("[IG post] Rest Exception during container status check: {}: ", e.getMessage());
            throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR,e.getMessage());
        }
        catch (Exception e) {
            LOGGER.info("[IG post] Exception during container status check: {}",e.getMessage());
            throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN,e.getMessage());
        }

        if(!status.equalsIgnoreCase("FINISHED")) {
            throw new InstagramContainerException("Container Status: "+status, ErrorCodes.INSTAGRAM_CONTAINER_CREATION_ERROR.value());
        }
        return status;
    }

    @Override
    public Map<String, Object> checkContainerStatusWithoutRetry(String containerId, String accessToken) {
        String status;
        String statusCode;
        Map<String, Object> response;
        try {
            String url = INSTAGRAM_GRAPH_API_URL + containerId + "?fields=status_code,status&access_token=" + accessToken;
            LOGGER.info("checking IG container status without retry url: {}", url);
            response = socialRestTemplate.getForObject(url, Map.class);
            status = response.get("status_code").toString();
            statusCode = response.get("status").toString();
            LOGGER.info("STATUS: {}, code: {}", status, statusCode);
            if (status.equalsIgnoreCase("ERROR")) {
                return response;
            }
        }
        catch (HttpStatusCodeException | TooManyRequestException e) {
            LOGGER.info("[IG post] HTTP Exception during container status check: {}", e.getMessage());
            throw e;
        }
        catch (RestClientException e) {
            LOGGER.error("[IG post] Rest Exception during container status check: {}: ", e.getMessage());
            throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, e.getMessage());
        }
        catch (Exception e) {
            LOGGER.info("[IG post] Exception during container status check: {}", e.getMessage());
            throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN, e.getMessage());
        }

        if(!(status.equalsIgnoreCase("FINISHED") || status.equalsIgnoreCase("PUBLISHED"))) {
            throw new InstagramContainerException("Container Status: " + status, ErrorCodes.INSTAGRAM_CONTAINER_CREATION_ERROR.value());
        }
        return response;
    }
}
