package com.birdeye.social.external.mediaupload;

import com.birdeye.social.dao.mediaupload.SocialAssetChunkInfoRepo;
import com.birdeye.social.entities.mediaupload.SocialAssetChunkInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SocialAssetChunkInfoServiceImpl implements SocialAssetChunkInfoService{

    @Autowired
    SocialAssetChunkInfoRepo socialAssetChunkInfoRepo;

    @Override
    public boolean existsByAssetId(Integer assetId) {
        return socialAssetChunkInfoRepo.existsByAssetId(assetId);
    }

    @Override
    public void save(SocialAssetChunkInfo socialAssetChunkInfo) {
        socialAssetChunkInfoRepo.save(socialAssetChunkInfo);
    }

    @Override
    public Integer getCountOfAssetChunks(Integer assetId) {
        return socialAssetChunkInfoRepo.findCountByAssetId(assetId);
    }

    @Override
    public SocialAssetChunkInfo findByAssetIdAndSequenceIdAndSourceId(Integer assetId, Integer sequenceId, Integer sourceId) {
        return socialAssetChunkInfoRepo.findByAssetIdAndSequenceIdAndSourceId(assetId,sequenceId, sourceId);
    }

    @Override
    public List<SocialAssetChunkInfo> findByAssetId(Integer assetId) {
        return socialAssetChunkInfoRepo.findByAssetId(assetId);
    }

    @Override
    public List<SocialAssetChunkInfo> findByAssetIdAndSourceId(Integer assetId, Integer sourceId) {
        return socialAssetChunkInfoRepo.findByAssetIdAndSourceId(assetId, sourceId);
    }

    @Override
    public List<Integer> findSequenceIdsByAssetIdAndSourceId(Integer assetId, Integer sourceId) {
        return socialAssetChunkInfoRepo.findSequenceIdsByAssetIdAndSourceId(assetId, sourceId);
    }
}
