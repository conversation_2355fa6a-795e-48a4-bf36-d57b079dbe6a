package com.birdeye.social.external.service;


import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.facebook.FacebookApis;
import com.birdeye.social.instagram.response.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.stream.Collectors;

@Service
public class InstagramMentionExternalServiceImpl implements InstagramMentionExternalService {

    private final Logger LOGGER = LoggerFactory.getLogger(InstagramMentionExternalServiceImpl.class);
    @Autowired
    @Qualifier("socialRestTemplate")
    private RestTemplate socialRestTemplate;
    private static final String INSTAGRAM_BASE_URL = FacebookApis.GRAPH_API_BASE_VERSION_DEFAULT_V21;


    private UriComponentsBuilder getInstagramMentionPostUrl(String accessToken, String accountId,String mediaId) {
        //https://graph.facebook.com/v15.0/*****************?fields=mentioned_media.media_id(*****************){id,caption,media_url,media_type,comments_count,like_count,timestamp,permalink,children{media_url,media_type}}&access_token={token}
        String url = StringUtils.join(INSTAGRAM_BASE_URL, accountId);
        String fields = "mentioned_media.media_id("+ mediaId +"){id,caption,media_url,media_type,comments_count,like_count,timestamp,permalink,username,children{media_url,media_type}}";
        return UriComponentsBuilder.fromUriString(url)
                .queryParam("fields",fields)
                .queryParam("access_token",accessToken);
    }

    private UriComponentsBuilder getInstagramMentionCommentUrl(String accessToken, String accountId,String commentId) {
        //https://graph.facebook.com/v15.0/*****************?fields=mentioned_comment.comment_id(*****************){timestamp,like_count,text,media{id,caption,media_url,media_type,comments_count,like_count,timestamp,permalink,children{media_url,media_type}}}&access_token={token}
        String url = StringUtils.join(INSTAGRAM_BASE_URL, accountId);
        String fields = "mentioned_comment.comment_id("+commentId+"){timestamp,like_count,text,username,media{id,caption,username,media_url,media_type,comments_count,like_count,timestamp,permalink,children{media_url,media_type}}}";
        return UriComponentsBuilder.fromUriString(url)
                .queryParam("fields",fields)
                .queryParam("access_token",accessToken);
    }

    private UriComponentsBuilder getInstagramTaggedPostsUrl(String accessToken, String accountId) {
        //https://graph.facebook.com/v15.0/*****************/tags?fields=id,caption,media_url,media_type,comments_count,like_count,timestamp,permalink,children{media_url,media_type}&access_token={token}
        String url = StringUtils.join(INSTAGRAM_BASE_URL, accountId,"/tags");
        String fields = "id,caption,username,media_url,media_type,comments_count,like_count,timestamp,permalink,children{media_url,media_type}";
        return UriComponentsBuilder.fromUriString(url)
                .queryParam("fields",fields)
                .queryParam("access_token",accessToken);
    }

    private UriComponentsBuilder getInstagramHashTaggedPostsUrl(String accessToken, String accountId, String hashTagId) {
        //https://graph.facebook.com/v15.0/*****************/recent_media?user_id=*****************&fields=id,caption,media_url,media_type,comments_count,like_count,timestamp,permalink,children{media_url,media_type}&access_token={token}
        String url = StringUtils.join(INSTAGRAM_BASE_URL, hashTagId,"/recent_media");
        String fields = "id,caption,media_url,media_type,comments_count,like_count,timestamp,permalink,children{media_url,media_type}";
        return UriComponentsBuilder.fromUriString(url)
                .queryParam("user_id",accountId)
                .queryParam("fields",fields)
                .queryParam("access_token",accessToken);
    }


    @Override
    public InstagramMentionedMediaResponse getMentionedMediaInfo(BusinessInstagramAccount instagramAccount,String mediaId) {
        InstagramMentionedMediaResponse mentionedMediaResponse;
        UriComponentsBuilder url = getInstagramMentionPostUrl(instagramAccount.getPageAccessToken(),instagramAccount.getInstagramAccountId(),mediaId);
        LOGGER.info("Request url for getMentionedMediaInfo: {}",url.build());
        try {
            mentionedMediaResponse = socialRestTemplate.getForObject(url.build().toUri(), InstagramMentionedMediaResponse.class);
        } catch (RestClientResponseException e) {
            LOGGER.error("RestClientResponseException while getting mentioned post info for url: {}, error: {}",
                    url, e.getResponseBodyAsString());
            throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_MENTION_ERROR, e.getResponseBodyAsString());
        }catch (Exception e) {
            LOGGER.error("Exception while getting mentioned post info for url: {}, error: {} ",
                    url, e.getStackTrace());
            throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_MENTION_ERROR, e.getMessage());
        }
        LOGGER.info("Instagram response for getMentionedMediaInfo is {}", mentionedMediaResponse);
        return mentionedMediaResponse;
    }


    @Override
    public InstagramMentionedCommentResponse getMentionedCommentInfo(BusinessInstagramAccount instagramAccount, String commentId) {
        InstagramMentionedCommentResponse mentionedCommentResponse = null;
        UriComponentsBuilder url = getInstagramMentionCommentUrl(instagramAccount.getPageAccessToken(),instagramAccount.getInstagramAccountId(),commentId);
        LOGGER.info("Request url for getMentionedCommentInfo: {}",url.build());
        try {
            mentionedCommentResponse = socialRestTemplate.getForObject(url.build().toUri(), InstagramMentionedCommentResponse.class);
        } catch (RestClientResponseException e) {
            LOGGER.error("RestClientResponseException while getting mentioned comment info for url: {}, error: {}",
                    url, e.getResponseBodyAsString());
            throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_MENTION_ERROR, e.getResponseBodyAsString());
        }catch (Exception e) {
            LOGGER.error("Exception while getting mentioned comment info for url: {}, error: {} ",
                    url, e.getStackTrace());
            throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_MENTION_ERROR, e.getMessage());
        }
        LOGGER.info("Instagram response for getMentionedCommentInfo is {}", mentionedCommentResponse);
        return mentionedCommentResponse;
    }

    @Override
    public InstagramTaggedPosts getTaggedPosts(BusinessInstagramAccount instagramAccount) {
        InstagramTaggedPosts taggedPosts = null;
        UriComponentsBuilder url = getInstagramTaggedPostsUrl(instagramAccount.getPageAccessToken(),instagramAccount.getInstagramAccountId());
        LOGGER.info("Request url for getTaggedPosts: {}",url.build());
        try {
            taggedPosts = socialRestTemplate.getForObject(url.build().toUri(), InstagramTaggedPosts.class);
        } catch (RestClientResponseException e) {
            LOGGER.error("RestClientResponseException while getting tagged posts for url: {}, error: {}",
                    url, e.getResponseBodyAsString());
            throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_MENTION_ERROR, e.getMessage());
        }catch (Exception e) {
            LOGGER.error("Exception while getting tagged posts info for url: {}, error: {} ",
                    url, e.getStackTrace());
            throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_MENTION_ERROR, e.getMessage());
        }
        LOGGER.info("Instagram response for getTaggedPosts is {}", taggedPosts.getData().stream().map(InstagramTimelineResponse::getId).collect(Collectors.toList()));
        return taggedPosts;
    }

    @Override
    public HashtagIdResponse getInstagramHashTagId(BusinessInstagramAccount instagramAccount, String keyword) {
        HashtagIdResponse hashtagIdResponse = null;
        //https://graph.facebook.com/v15.0/ig_hashtag_search?user_id=*****************&q=anime&access_token={token}
        HttpHeaders headers = new HttpHeaders();
        headers.add("Accept", MediaType.APPLICATION_JSON_VALUE);
        String url = INSTAGRAM_BASE_URL.concat("ig_hashtag_search");
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam("user_id",instagramAccount.getInstagramAccountId())
                .queryParam("q",keyword)
                .queryParam("access_token",instagramAccount.getPageAccessToken());
        HttpEntity<?> entity = new HttpEntity<>(headers);
        HttpEntity<HashtagIdResponse> response;
        LOGGER.info("Request url for getInstagramHashTagId: {}",builder.build());
        try {
            response = socialRestTemplate.exchange(builder.build().toString(), HttpMethod.GET,entity,HashtagIdResponse.class);
            hashtagIdResponse = response.getBody();
        } catch (RestClientResponseException e) {
            LOGGER.error("RestClientResponseException while getting hashtag id for url: {}, error: {}",
                    url, e.getResponseBodyAsString());
            throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_MENTION_ERROR, e.getMessage());
        }catch (Exception e) {
            LOGGER.error("Exception while getting hashtag id for url: {}, error: {} ",
                    url, e.getStackTrace());
            throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_MENTION_ERROR, e.getMessage());
        }
        LOGGER.info("Instagram response for getInstagramHashTagId is {}", hashtagIdResponse);
        return hashtagIdResponse;
    }

    @Override
    public InstagramHashtagPosts getHashtagPosts(BusinessInstagramAccount instagramAccount, String hashtagId) {
        InstagramHashtagPosts hashtagPosts = null;
        UriComponentsBuilder url = getInstagramHashTaggedPostsUrl(instagramAccount.getPageAccessToken(),instagramAccount.getInstagramAccountId(),hashtagId);
        LOGGER.info("Request url for getHashtagPosts: {}",url.build());
        try {
            hashtagPosts = socialRestTemplate.getForObject(url.build().toUri(), InstagramHashtagPosts.class);
        } catch (RestClientResponseException e) {
            LOGGER.error("RestClientResponseException while getting hashTagged posts for url: {}, error: {}",
                    url, e.getResponseBodyAsString());
            throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_MENTION_ERROR, e.getMessage());
        }catch (Exception e) {
            LOGGER.error("Exception while getting hashTagged posts info for url: {}, error: {} ",
                    url, e.getStackTrace());
            throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_MENTION_ERROR, e.getMessage());
        }
        LOGGER.info("Instagram response for getHashtagPosts is {}", hashtagPosts);
        return hashtagPosts;
    }

}
