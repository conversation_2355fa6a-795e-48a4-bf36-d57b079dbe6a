/**
 * 
 */
package com.birdeye.social.external.service;

import com.fasterxml.jackson.core.JsonProcessingException;

/**
 * <AUTHOR>
 *
 */
public interface KafkaProducerService {

	public void send(String topic,Integer activityId, Object payload);

	/**
	 * @param topic
	 * @param activityId
	 * @param payload
	 * @return 
	 * @throws JsonProcessingException
	 */
	public boolean sendWithKey(String topic, String key, Object payload);
	
	public boolean sendObject(String topic, Object payload);
	
	public boolean sendObjectV1(String topic, Object payload);
	boolean sendObjectV1US(String kafkaTopic, Object payload);

	boolean sendObjectWithKeyV1(String key, String kafkaTopic, Object payload);
}
