package com.birdeye.social.facebook;

import java.io.Serializable;

import com.birdeye.social.facebook.response.FbUploadStatusResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FacebookFeed implements Serializable {

	/**
	 *
	 */
	private static final long	serialVersionUID	= 9001263676301196658L;
	private String id;
	private String post_id; // for fb reels
	private String message;
	private FbUser from;
	private FbAttachement attachments;
	private FbComments comments;
	private FbLikes likes;
	private String created_time;
	private String creation_time;  // used for fb story
	//Deprecated in graph api v3.1
	private String type;
	private FbShare shares;
	//Deprecated in graph api v3.1
	private String source;
	private String story;
	//Deprecated in graph api v3.1
	private String description;
	private String parent_id;
	private String postType;  // POST, REEL, STORY
	private FbUploadStatusResponse.Status status;
	private String media_id; // used for fb story
	private String media_type;		// used for fb story
	public String getMedia_id() {return media_id;}
	public void setMedia_id(String media_id) {this.media_id = media_id;}
	public String getMedia_type() {return media_type;}
	public void setMedia_type(String media_type) {this.media_type = media_type;}
	public String getCreation_time() {return creation_time;}
	public void setCreation_time(String creation_time) {this.creation_time = creation_time;}
	public FbUploadStatusResponse.Status getStatus() {return status;}
	public void setStatus(FbUploadStatusResponse.Status status) {this.status = status;}
	public String getPost_id() {return post_id;}
	public void setPost_id(String post_id) {this.post_id = post_id;}
	public String getPostType() {return postType;}
	public void setPostType(String postType) {this.postType = postType;}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public FbUser getFrom() {
		return from;
	}
	public void setFrom(FbUser from) {
		this.from = from;
	}
	public FbAttachement getAttachments() {
		return attachments;
	}
	public void setAttachments(FbAttachement attachments) {
		this.attachments = attachments;
	}
	public FbComments getComments() {
		return comments;
	}
	public void setComments(FbComments comments) {
		this.comments = comments;
	}
	public FbLikes getLikes() {
		return likes;
	}
	public void setLikes(FbLikes likes) {
		this.likes = likes;
	}
	public String getCreated_time() {
		return created_time;
	}
	public void setCreated_time(String created_time) {
		this.created_time = created_time;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
 
	public FbShare getShares() {
		return shares;
	}
	public void setShares(FbShare shares) {
		this.shares = shares;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}
	 
	public String getStory() {
		return story;
	}
	public void setStory(String story) {
		this.story = story;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getParent_id() {
		return parent_id;
	}
	public void setParent_id(String parent_id) {
		this.parent_id = parent_id;
	}
	
	@Override
	public String toString() {
		return "FacebookFeed [id=" + id + ", message=" + message + ", from=" + from + ", attachments=" + attachments + ", comments=" + comments + ", likes=" + likes + ", created_time=" + created_time
				+ ", type=" + type + ", shares=" + shares + ", source=" + source + ", story=" + story + ", description=" + description + ", parent_id=" + parent_id + "]";
	}
	
}
