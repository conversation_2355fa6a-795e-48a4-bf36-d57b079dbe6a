package com.birdeye.social.facebook;

import com.birdeye.social.aspect.Profiled;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.exception.*;
import com.birdeye.social.external.exception.ExternalAPIErrorCode;
import com.birdeye.social.external.exception.ExternalAPIException;
import com.birdeye.social.external.request.facebook.FbMetricName;
import com.birdeye.social.external.request.facebook.FbMetricPeriod;
import com.birdeye.social.facebook.FacebookPost.FacebookPageSearch;
import com.birdeye.social.facebook.FacebookPost.FacebookPostDetails;
import com.birdeye.social.facebook.response.*;
import com.birdeye.social.model.*;
import com.birdeye.social.utils.FacebookUtils;
import com.birdeye.social.utils.StringUtils;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.birdeye.social.utils.JSONUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.*;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Profiled
public class FacebookServiceImpl implements FacebookService  {

	private static final String UPDATE_ON_FACEBOOK_RESPONSE = "[Facebook Social Integration] update on facebook response: {}";

	private static final String UPDATE_COVER_IMAGE_OF_FB_RESPONSE = "[Facebook Social Integration] update cover image of fb response: {}";

	private static final String UPDATE_LOGO_IMAGE_OF_FB_RESPONSE = "[Facebook Social Integration] update logo image of fb response: {}";

	private static final String EMPTY_RESPONSE_BODY = "empty response body";

	private static final String COMMENT_FIELDS = "comment_count,can_reply_privately,attachment,message,created_time,from,parent,message_tags";

	private static final Logger	LOGGER		= LoggerFactory.getLogger(FacebookServiceImpl.class);


	@Autowired
	@Qualifier("socialRestTemplate")
	private RestTemplate		socialRestTemplate;

	/**
	 * Posting a new activity to Facebook feed
	 */
	@Override
	public String postTextV2(FacebookPageAccessInfo creds, FacebookData fbData) throws IOException {
		//String url = StringUtils.join(creds.getBaseUrl(), creds.getProfileId(), "/feed?access_token=", creds.getAccessToken());
		String url = StringUtils.format(FacebookApis.FEED,creds.getPageId());
		MultiValueMap<String, String> parametersMap = getPostTextParameters(fbData);
		parametersMap.add(ACCESS_TOKEN, creds.getAccessToken());
		FacebookPostResponse response = null;
		LOGGER.info("Received request for API postTextV2 URL {} and parameters {}", url, parametersMap);
		try {
			response = socialRestTemplate.postForObject(url, parametersMap, FacebookPostResponse.class);
		}
		catch (HttpStatusCodeException e) {
			handleClientError(e);
		} catch (ResourceAccessException e) {
			handleError(url, e, ErrorCodes.FB_SOCKET_TIME_OUT, true);
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_POST_ACTIVITY_ON_FACEBOOK, true);
		}
		return response.getId();
	}


	@Override
	public Boolean editPost(FacebookPageAccessInfo creds, FacebookData fbData) throws IOException {
		//String url = StringUtils.join(creds.getBaseUrl(), creds.getProfileId(), "/feed?access_token=", creds.getAccessToken());
		String url = StringUtils.format(FacebookApis.POST,creds.getPageId());
		MultiValueMap<String, String> parametersMap = getPostTextParameters(fbData);
		parametersMap.add(ACCESS_TOKEN, creds.getAccessToken());
		FacebookPostResponse response = null;
		LOGGER.info("Received request for API postTextV2  URL {} and parameters {}", url, parametersMap);
		try {
			response = socialRestTemplate.postForObject(url, parametersMap, FacebookPostResponse.class);
		}
		catch (HttpStatusCodeException e) {
			handleClientError(e);
		} catch (ResourceAccessException e) {
			handleError(url, e, ErrorCodes.FB_SOCKET_TIME_OUT, true);
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_POST_ACTIVITY_ON_FACEBOOK, true);
		}
		return response.getSuccess();
	}
	@Override
	public FacebookPostResponse editPostText(FacebookPageAccessInfo creds, String message) throws IOException {
		String url = FacebookApis.GRAPH_API_BASE_VERSION_DEFAULT_V21 + creds.getObjectId();
		MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
		parametersMap.add(MESSAGE, message);
		parametersMap.add("name", message);
		parametersMap.add("description", message);
		parametersMap.add(ACCESS_TOKEN, creds.getAccessToken());
		FacebookPostResponse response = null;
		LOGGER.info("Received request for API editTextV2  URL {} and parameters {}", url, parametersMap);
		try {
			response = socialRestTemplate.postForObject(url, parametersMap, FacebookPostResponse.class);
		}
		catch (HttpStatusCodeException e) {
			handleClientError(e);
		} catch (ResourceAccessException e) {
			handleError(url, e, ErrorCodes.FB_SOCKET_TIME_OUT, true);
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_EDIT_POST_TEXT_ON_FACEBOOK, true);
		}
		return response;
	}

	private MultiValueMap<String, String> getPostTextParameters(FacebookData fbData) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		if (fbData == null) {
			return map;
		}
		String message = StringUtils.isNotEmpty(fbData.getText()) ? fbData.getText() : " ";
		map.add(MESSAGE, message);
		if (CollectionUtils.isNotEmpty(fbData.getMediaId())) {
			for (int i = 0; i < fbData.getMediaId().size(); i++) {
				map.add("attached_media[" + i + "]", "{\"media_fbid\":\"" + fbData.getMediaId().get(i) + "\"}");
			}
		}
		if (CollectionUtils.isEmpty(fbData.getMediaId()) && StringUtils.isNotEmpty(fbData.getLink())) {
			map.add("link", fbData.getLink());
		}
		return map;
	}

	private MultiValueMap<String, String> getPostImageParameters(FacebookData fbData) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		if (fbData == null) {
			return map;
		}
		if (fbData.getIsMultiMedia()) {
			map.add("published", "false");
		} else {
			String message = StringUtils.isNotEmpty(fbData.getText()) ? fbData.getText() : " ";
			map.add(CAPTION, message);
		}
		map.add("url", fbData.getMediaUrl());
		return map;
	}

//	@Override
//	public String postText2(FacebookPageAccessInfo creds, FacebookData fbData) {
//		FacebookTemplate facebook = getFbTemplate(creds);
//		return facebook.feedOperations().post(creds.getProfileId(), fbData.getText());
//	}
//
//	private FacebookTemplate getFbTemplate(FacebookPageAccessInfo creds) {
//		FacebookTemplate fbTemplate = new FacebookTemplate(creds.getAccessToken());
//		fbTemplate.setApiVersion(API_VERSION);
//		return fbTemplate;
//	}

	@Override
	public FacebookPostInfo postTextRevamped(FacebookPageAccessInfo creds, FacebookData fbData) throws IOException {
		String postId = postTextV2(creds, fbData);
		return new FacebookPostInfo(postId);
	}

	@Override
	public String postImageV2(FacebookPageAccessInfo creds, FacebookData fbData) throws IOException {
		//String url = StringUtils.join(creds.getBaseUrl(), creds.getProfileId(), "/photos?access_token=", creds.getAccessToken());
		String url = StringUtils.format(FacebookApis.PAGE_PHOTOS, creds.getPageId());
		MultiValueMap<String, String> parametersMap = getPostImageParameters(fbData);
		parametersMap.add(ACCESS_TOKEN, creds.getAccessToken());
		FacebookPostResponse response = null;
		LOGGER.info("Received request for API postImageV2  URL {} and parameters {}", url, parametersMap);
		try {
			response = socialRestTemplate.postForObject(url, parametersMap, FacebookPostResponse.class);
		} catch (HttpStatusCodeException e) {
			handleClientError(e);
		} catch (ResourceAccessException e) {
			handleError(url, e, ErrorCodes.FB_SOCKET_TIME_OUT, true);
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_POST_ACTIVITY_ON_FACEBOOK, true);
		}
		return response.getId();
	}

	@Override
	public String postVideoV2(FacebookPageAccessInfo creds, FacebookData fbData) throws Exception {
		//String postVideoUrl = StringUtils.join(creds.getBaseUrl(), creds.getProfileId(), "/videos?access_token=", creds.getAccessToken());
		String postVideoUrl = StringUtils.format(FacebookApis.PAGE_VIDEOS, creds.getPageId());
//		String fileName = fbData.getMediaUrl();
//		fileName = fileName.lastIndexOf('/') == -1 ? fileName : fileName.substring(fileName.indexOf('/') + 1);
		LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
		map.add(ACCESS_TOKEN, creds.getAccessToken());
		//map.add("file", new FileSystemResource(fbData.getMedia()));
		// new FileSystemResource(fbData.getMedia())
		if (StringUtils.isNotEmpty(fbData.getText())) {
			map.add("description", fbData.getText());
		}
		if(StringUtils.isNotEmpty(fbData.getLink())) {
			map.add("link", fbData.getLink());
		}
		String videoUrl = fbData.getCompleteCdnMediaUrl();
		map.add("file_url", videoUrl);
		if(Objects.nonNull(fbData.getVideoThumbnail())) {
			map.add("thumb", new FileSystemResource(fbData.getVideoThumbnail()));
		}
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.MULTIPART_FORM_DATA);

		org.springframework.http.HttpEntity<LinkedMultiValueMap<String, Object>> requestEntity = new org.springframework.http.HttpEntity<>(map, headers);

		FacebookPostResponse response = null;
		LOGGER.info("Received request for API postVideoV2  URL {} and parameters {}", postVideoUrl, map);
		try {
			response = socialRestTemplate.exchange(postVideoUrl, HttpMethod.POST, requestEntity, FacebookPostResponse.class).getBody();
		} catch (HttpStatusCodeException e) {
			handleClientError(e);
		} catch (ResourceAccessException e) {
			handleError(postVideoUrl, e, ErrorCodes.FB_SOCKET_TIME_OUT, true);
		} catch (Exception e) {
			handleError(postVideoUrl, e, ErrorCodes.UNABLE_TO_POST_ACTIVITY_ON_FACEBOOK, true);
		}
		return response.getId();
	}

	@Override
	public String postVideoInChunksV2(FacebookPageAccessInfo creds, FacebookData fbData) throws Exception {
		//String postVideoUrl = StringUtils.join(creds.getBaseUrl(), creds.getProfileId(), "/videos?access_token=", creds.getAccessToken());
		String postVideoUrl = StringUtils.format(FacebookApis.PAGE_VIDEOS, creds.getPageId());
		String fileName = fbData.getMediaUrl();
		fileName = fileName.lastIndexOf('/') == -1 ? fileName : fileName.substring(fileName.indexOf('/') + 1);
		LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();

		map.add(ACCESS_TOKEN, creds.getAccessToken());
		FileSystemResource fileSystemResource = new FileSystemResource(fbData.getMedia());
		byte[] video = null;
		try {
			// TODO: Avoid loading all data in memory at once.
			video = IOUtils.toByteArray(fileSystemResource.getInputStream());
		} catch (IOException e) {
			throw new BirdeyeSocialException("Failed to upload video", e);
		}
		map.add("file_size", String.valueOf(video.length));
		map.add("upload_phase", "start");
		// new FileSystemResource(fbData.getMedia())
		if(StringUtils.isNotEmpty(fbData.getLink())) {
			map.add("link", fbData.getLink());
		}

		map.add("url", fbData.getMediaUrl());
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.MULTIPART_FORM_DATA);

		org.springframework.http.HttpEntity<LinkedMultiValueMap<String, Object>> requestEntity = new org.springframework.http.HttpEntity<>(map, headers);

		FacebookPostChunkStartResponse response = null;
		LOGGER.info("Received request for API postVideoV2  URL {} and parameters {}", postVideoUrl, map);
		try {
			response = socialRestTemplate.exchange(postVideoUrl, HttpMethod.POST, requestEntity, FacebookPostChunkStartResponse.class).getBody();
			uploadVideosInChunk(fbData, video, response.getUploadSessionId(), response.getStartOffset(), response.getEndOffset(), postVideoUrl, creds.getAccessToken());
		} catch (HttpStatusCodeException e) {
			handleClientError(e);
		}catch (Exception e) {
			handleError(postVideoUrl, e, ErrorCodes.UNABLE_TO_POST_ACTIVITY_ON_FACEBOOK, true);
		}
		return response.getVideoId();
	}

	private void uploadVideosInChunk(FacebookData fbData, byte[] video,String uploadSessionId, String startOffset, String endOffset, String postVideoUrl, String accessToken) throws Exception {
		FacebookPostChunkTransferResponse response = new FacebookPostChunkTransferResponse();
		Long sessionId = Long.parseLong(uploadSessionId);

		int totalBytes = video.length;
		int chunkCount = 0;

		while(!startOffset.equals(endOffset)) {
			Long startOffsetNumber = Long.parseLong(startOffset);
			Long endOffsetNumber = Long.parseLong(endOffset);
			int newLength = endOffsetNumber.intValue()-startOffsetNumber.intValue();
			byte[] chunkVideo = new byte[newLength];
			System.arraycopy(video, startOffsetNumber.intValue(), chunkVideo, 0, newLength);
			File file = new File("video_chunk"+chunkCount+".tmp");
			FileOutputStream fileOutputStream = new FileOutputStream(file);
			fileOutputStream.write(chunkVideo);
			LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
			map.add("video_file_chunk", new FileSystemResource(file));
			map.add("upload_phase", "transfer");
			map.add("start_offset", startOffset);
			map.add("upload_session_id", uploadSessionId);
			map.add("access_token", accessToken);
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.MULTIPART_FORM_DATA);

			org.springframework.http.HttpEntity<LinkedMultiValueMap<String, Object>> requestEntity = new org.springframework.http.HttpEntity<>(map, headers);
			try {
				response = socialRestTemplate.exchange(postVideoUrl, HttpMethod.POST, requestEntity, FacebookPostChunkTransferResponse.class).getBody();
			} catch (HttpStatusCodeException e) {
				handleClientError(e);
			}catch (Exception e) {
				handleError(postVideoUrl, e, ErrorCodes.UNABLE_TO_POST_ACTIVITY_ON_FACEBOOK, true);
			}
			startOffset = response.getStartOffset();
			endOffset = response.getEndOffset();
		}

		FacebookPostChunkFinishResponse finsihResponse = new FacebookPostChunkFinishResponse();
		LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
		map.add("upload_phase", "finish");
		map.add("upload_session_id", uploadSessionId);
		if(StringUtils.isNotEmpty(fbData.getText())) {
			map.add("description", fbData.getText());
		}
		map.add("access_token", accessToken);
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.MULTIPART_FORM_DATA);

		org.springframework.http.HttpEntity<LinkedMultiValueMap<String, Object>> requestEntity = new org.springframework.http.HttpEntity<>(map, headers);
		try {
			finsihResponse = socialRestTemplate.exchange(postVideoUrl, HttpMethod.POST, requestEntity, FacebookPostChunkFinishResponse.class).getBody();
		}catch (HttpStatusCodeException e) {
			handleClientError(e);
		}catch (Exception e) {
			handleError(postVideoUrl, e, ErrorCodes.UNABLE_TO_POST_ACTIVITY_ON_FACEBOOK, true);
		}

		if(!finsihResponse.getSuccess()) {
			throw new BirdeyeSocialException("error uploading video on facebook");
		}
	}

	private void handleClientError(HttpStatusCodeException e) throws IOException {
		LOGGER.info("[social post] error occured while posting for fb {}", e.getResponseBodyAsString());
		Map<String, Object> errorMap = new HashMap<String, Object>();
		try {
			if(Objects.isNull(e) || StringUtils.isEmpty(e.getResponseBodyAsString())) {
				errorMap.put("http_response", 400);
				errorMap.put("error_code", ErrorCodes.EMPTY_RESPONSE_BODY.value());
				errorMap.put("error_message",  EMPTY_RESPONSE_BODY);
				throw new BirdeyeSocialException(400, EMPTY_RESPONSE_BODY, errorMap);
			}
			errorMap.put("http_response", e.getRawStatusCode());
			if(Constants.FACEBOOK_ERROR_WITHOUT_BODY.contains(e.getResponseBodyAsString())) {
				errorMap.put("error_message", e.getResponseBodyAsString());
				throw new BirdeyeSocialException(e.getRawStatusCode(), e.getResponseBodyAsString(), errorMap);
			}

			FacebookBaseResponse ex = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), FacebookBaseResponse.class);
			if (Objects.nonNull(ex.getError().getCode())) {
				errorMap.put("error_code", ex.getError().getCode());
			}
			if (Objects.nonNull(ex.getError().getError_subcode())) {
				errorMap.put("error_sub_code", ex.getError().getError_subcode());
			}
			if (Objects.nonNull(ex.getError().getMessage())) {
				errorMap.put("error_message", ex.getError().getMessage());
			}
			throw new BirdeyeSocialException(e.getRawStatusCode(), ex.getError().getMessage(), errorMap);
		}
		catch(JsonParseException ex){
			throw new BirdeyeSocialException(e.getRawStatusCode(),e.getMessage(),errorMap);
		}

		/*if(e.getStatusCode().is4xxClientError()) {
			FacebookBaseResponse ex =  new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), FacebookBaseResponse.class);
			if(Objects.isNull(ex.getError().getError_subcode())) {
				// Error code and message is constant for now, this need to configurable
				throw new BirdeyeSocialException(Constants.ERROR_CONSTANT_FOR_REST_CLIENT_SERVICE, ex.getError().getMessage());
//				throw new BirdeyeSocialException(ex.getError().getCode(), ex.getError().getMessage());
			}  else {
				throw new BirdeyeSocialException(Constants.ERROR_CONSTANT_FOR_REST_CLIENT_SERVICE, ex.getError().getMessage());
			}
		}
		if(e.getStatusCode().is5xxServerError()) {
			throw new BirdeyeSocialException(Constants.ERROR_CONSTANT_FOR_REST_CLIENT_SERVICE, "Something went wrong");
		}*/
 	}


	/**
	 * Long lived access token for a page.
	 */
	@Override
	public String getExtendedFacebookToken(FacebookCreds creds) throws Exception {
		FacebookExtendedTokenResponse response = null;
		MultiValueMap<String, String> params = getExtendedFacebookTokenParameters(creds);
		String  url = UriComponentsBuilder.fromHttpUrl(FacebookApis.OAUTH_API).queryParams(params).build().encode().toUriString();

		LOGGER.info("Received request for API getExtendedFacebookToken  URL {} and parameters {}", url, params);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FacebookExtendedTokenResponse.class);
			if (response != null && StringUtils.isNotBlank(response.getAccess_token())) {
				return response.getAccess_token();
			}
		} catch (Exception e) {
			if(e instanceof HttpStatusCodeException) {
				LOGGER.info("Something went wrong while getting the URL {} with error {}", url, ((HttpStatusCodeException) e).getResponseBodyAsString());
			}
			handleError(url, e,ErrorCodes.UNABLE_TO_GENERATE_ACCESS_TOKEN_ON_FACEBOOK, true);
		}
		return null;
	}

	private MultiValueMap<String, String> getExtendedFacebookTokenParameters(FacebookCreds creds) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		if (creds == null) {
			return map;
		}
		map.add(PARAM_CI, creds.getClientId());
		map.add(PARAM_CS, creds.getClientSecret());

		if(StringUtils.isNotEmpty(creds.getAuthCode())) {
			if(creds.getRedirectUri()!=null){
				map.add(PARAM_REDIRECT_URI, creds.getRedirectUri());
			}
			map.add(PARAM_CODE, creds.getAuthCode());
		} else if(StringUtils.isNotEmpty(creds.getTempAccessToken())) {
			map.add(PARAM_GT, "fb_exchange_token");
			map.add(PARAM_ET, creds.getTempAccessToken());
		} else {
			LOGGER.error("AuthCode and TempAccessToken both cannot be null");
			throw new BirdeyeSocialException("AuthCode and TempAccessToken both cannot be null");
		}
		return map;
	}

	/**
	 * User details with supplied access token context
	 */
	@Override
	public FbUserProfileInfo getUserDetails(String accessToken) throws Exception {
		//String url = StringUtils.join(baseUrl, "me");

		FbUserProfileInfo response = null;
		MultiValueMap<String, String> params = getUserDetailsParameters(accessToken);
		String url  = UriComponentsBuilder.fromHttpUrl(FacebookApis.ME).
				queryParams(params).build().encode().toUriString();

		LOGGER.info("Received request for API getUserDetails  URL {} and parameters {}", url, params);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FbUserProfileInfo.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_GET_USER_DETAILS, true);
		}
		return null;
	}

	@Override
	public FbUserProfileInfo getUserDetailsDebug(String pageId, String accessToken) throws Exception {

		FbUserProfileInfo responseEntity = null;
		MultiValueMap<String, String> params =  new LinkedMultiValueMap<>();

		params.add(ACCESS_TOKEN, accessToken);
		params.add(FIELDS, "name,username,access_token");
		String url = StringUtils.format(FacebookApis.USER_DETAIL, pageId);

		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();



		LOGGER.info("Received request for API getUserDetails  URL {} and parameters {}", url, params);
		try {
			responseEntity = socialRestTemplate.getForObject(new URI(url), FbUserProfileInfo.class);
			if (responseEntity != null && responseEntity.getError() == null) {
				return responseEntity;
			}
		} catch (HttpStatusCodeException e) {
			LOGGER.error("HttpStatusCodeException while updating facebook business API for URL {} :: {}", url, e.getResponseBodyAsString());
			Map<String, Object> exceptionDataMap = new HashMap<>();
			try {
				FacebookPostResponse response = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), FacebookPostResponse.class);
				FacebookErrorResponse errorResponse = response != null ? response.getError() : new FacebookErrorResponse();
				exceptionDataMap = getExceptionDataMap(errorResponse);
				throw new SocialPageUpdateException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN,e.getMessage() ,exceptionDataMap);

			} catch (Exception ex) {
				if(ex instanceof JsonParseException) {
					exceptionDataMap = getExceptionDataMapFromString(e.getResponseBodyAsString());
					throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN, e.getResponseBodyAsString(), exceptionDataMap);
				}
				throw new SocialPageUpdateException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN,e.getMessage() ,exceptionDataMap);
			}
		} catch (RestClientException e) {
			LOGGER.error("[Facebook Social Integration] Exception in while updating facebook business API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR,e.getMessage(), e);
		} catch (Exception e) {
			LOGGER.error("[Facebook Social Integration] Exception in while updating facebook business API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN, e.getMessage(), e);
		}
		return responseEntity != null ? responseEntity : null;
	}

	private MultiValueMap<String, String> getUserDetailsParameters(String accessToken) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		if (StringUtils.isEmpty(accessToken)) {
			return map;
		}
		map.add(ACCESS_TOKEN, accessToken);
		map.add(FIELDS, "id,first_name,last_name,link,picture{url},email,permissions,name");
		return map;
	}


	@Override
	public String getUserProfilePicture(String baseUrl, String accessToken, String userId) throws IOException {
		FacebookGetProfilePictureResponse response = null;
		//String url = StringUtils.join(baseUrl, userId, "/picture?access_token=", accessToken, "&redirect=false&suppress_http_code=1");
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		map.add(ACCESS_TOKEN, accessToken);
		map.add("redirect", "false");
		map.add("suppress_http_code", "1");
		String url  = UriComponentsBuilder.fromHttpUrl(StringUtils.format(FacebookApis.USER_PICTURE, userId)).queryParams(map).build().encode().toUriString();
		LOGGER.info("Received request for API getUserProfilePicture URL {} and parameters {}", url, null);
		try {
			response = socialRestTemplate.getForObject(url, FacebookGetProfilePictureResponse.class);
			if (response != null && response.getError() == null && StringUtils.isNotBlank((String) response.getData().get("url"))) {
				return (String) response.getData().get("url");
			}
		}  catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_GET_USER_DETAILS, true);
		}
		return null;
	}

	@Override
	public String getUserProfilePicture(String accessToken, String userId) throws IOException {
		FacebookProfilePictureResponse response = null;
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		map.add(ACCESS_TOKEN, accessToken);
		map.add("redirect", "false");
		map.add("suppress_http_code", "1");
		String url  = UriComponentsBuilder.fromHttpUrl(StringUtils.format(FacebookApis.USER_PICTURE, userId)).queryParams(map).build().encode().toUriString();
		LOGGER.info("Received request for API getUserProfilePicture URL {} and parameters {}", url, null);
		try {
			response = socialRestTemplate.getForObject(url, FacebookProfilePictureResponse.class);
			if (response != null && response.getError() == null && response.getData().get("url")!=null) {
				return (String)response.getData().get("url");
			}
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_GET_USER_DETAILS, true);
		}
		return null;
	}

	@Override
	public FbPage getPageDetailsOfMyUser(String baseUrl, String accessToken) throws Exception {
		//String url = StringUtils.join(baseUrl, "me/accounts");
		FbPage response = null;
		MultiValueMap<String, String> params = getPageDetailsParameters(accessToken);
		String url = UriComponentsBuilder.fromHttpUrl(ACCOUNTS).queryParams(params).build().encode().toUriString();
		LOGGER.info("Received request for API getPageDetailsOfMyUser URL {} and parameters {}", url, params);

		try {
			response = socialRestTemplate.getForObject(new URI(url), FbPage.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_PAGE_DETAILS, true);
		}
		return null;
	}

	// "locations{id,name,single_line_address,link,picture{url},access_token}",

	private MultiValueMap<String, String> getPageDetailsParameters(String accessToken) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		if (StringUtils.isEmpty(accessToken)) {
			return map;
		}
		map.add(ACCESS_TOKEN, accessToken);
		map.add(FIELDS, StringUtils.commaSeparatedString("id",
				"name",
				"username",
				"link",
				"picture{url}",
				"single_line_address",
				"access_token",
				//"temporary_status",
				//"location{latitude,longitude}",
				"emails",
				//"emails",
				//"start_info",
				//"products",
				//"payment_options",
				//"impressum",
				"phone",
				//"description",
				//"category",
				//"category_list",
				//"is_permanently_closed",
				"tasks",
				"location"
				));

		String pageLimit = CacheManager.getInstance()
				.getCache(SystemPropertiesCache.class)
				.getSocialFbPagesLimit();

		LOGGER.info("Fb pages limit : {}", pageLimit);
		map.add(LIMIT, pageLimit);
		return map;
	}

	private MultiValueMap<String, String> getPageDetailsParametersWithoutTasks(String accessToken) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		if (StringUtils.isEmpty(accessToken)) {
			return map;
		}
		map.add(ACCESS_TOKEN, accessToken);
		map.add(FIELDS, StringUtils.commaSeparatedString("id",
				"name",
				"username",
				"link",
				"picture{url}",
				"single_line_address",
				"access_token",
				"temporary_status",
				"location{latitude,longitude}",
				"emails",
				"start_info",
				"products",
				"payment_options",
				"impressum",
				"phone",
				"description",
				"category",
				"category_list",
				"is_permanently_closed",
				"instagram_business_account"
		));

		String pageLimit = CacheManager.getInstance()
				.getCache(SystemPropertiesCache.class)
				.getSocialFbPagesLimit();

		LOGGER.info("Fb pages limit : {}", pageLimit);
		map.add(LIMIT, pageLimit);
		return map;
	}

	private MultiValueMap<String, String> getPageDetailsParametersInstagram(String accessToken) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		if (StringUtils.isEmpty(accessToken)) {
			return map;
		}
		map.add(ACCESS_TOKEN, accessToken);
		map.add(FIELDS, StringUtils.commaSeparatedString("id",
				"name",
				"access_token",
				"instagram_business_account"));

		String pageLimit = CacheManager.getInstance()
				.getCache(SystemPropertiesCache.class)
				.getSocialFbPagesLimit();

		LOGGER.info("Fb pages limit : {}", pageLimit);
		map.add(LIMIT, pageLimit);
		return map;
	}

	// @mahak : comment
//	@Override
//	public List<FbPage> getPageDetailsList(String accessToken, String userId,String channel)  {
//		FbPage page = getPageDetails(accessToken, userId,channel);
//		List<FbPage> list = new ArrayList<>();
//		if (page != null) {
//			list.add(page);
//		}
//		while (page != null && page.getPaging() != null && page.getPaging().getNext() != null) {
//			page = getPageDetails(page.getPaging().getNext());
//			if (page != null) {
//				list.add(page);
//			}
//		}
//		return list;
//	}

// @mahak : comment
//	private List<FbPage> getChildPageDetailsList(String fbPageId, String pageAccessToken,String channel) {
//		FbPage page = getChildLoactionsFromFB(fbPageId, pageAccessToken,channel);
//		int total = 0;
//		List<FbPage> list = new ArrayList<>();
//		if (page != null) {
//			if (page.getData() != null && !page.getData().isEmpty()) {
//				total = page.getData().size();
//				list.add(page);
//			}
//
//		}
//		while (page != null && page.getPaging() != null && page.getPaging().getNext() != null) {
//			String nextPageUrl = page.getPaging().getNext();
//			LOGGER.info("Parent page Id {} and next Page Url {}  ", fbPageId, nextPageUrl);
//			page = getPageDetails(nextPageUrl);
//			if (page != null) {
//				if (page.getData() != null && !page.getData().isEmpty()) {
//					total += page.getData().size();
//					list.add(page);
//				}
//
//			}
//		}
//		if (total > 0) {
//			LOGGER.info("Child Page size {} for parent page {}  ", total, fbPageId);
//
//		} else {
//			LOGGER.info("No Child Page found for parent page {} with token {} ", fbPageId, pageAccessToken);
//		}
//		return list;
//	}
//
//	// @mahak : comment
//	public List<FbPage> getPageDetailsListV1(String accessToken, String userId,String channel) throws Exception {
//		// Parent pages
//		List<FbPage> parentPages = getPageDetailsList(accessToken, userId, channel);
//		List<FbPage> allPages = new ArrayList<FbPage>();
//		allPages.addAll(parentPages);
//		//TODO: We need to filter pages where child pages are not present.
//		// Child Pages.
//		parentPages.stream().flatMap(fbp -> fbp.getData().stream()).forEach(p -> {
//			List<FbPage> childPage;
//			try {
//				childPage = getChildPageDetailsList(p.getId(), p.getAccess_token(),channel);
//				allPages.addAll(childPage);
//			} catch (Exception e) {
//				LOGGER.error("Exception while fetching  child pages for page {}", p.getId(), e);
//			}
//		});
//		return allPages;
//	}

	@Retryable(value = BirdeyeSocialException.class,maxAttempts = 2,backoff = @Backoff(delay = 1000))
	public List<FbPage> getPageDetailsList(String accessToken, String userId,String channel) throws Exception {
		FbPage page = getPageDetails(accessToken, userId,channel);
		List<FbPage> list = new ArrayList<>();
		if (page != null) {
			list.add(page);
		}
		while (page != null && page.getPaging() != null && page.getPaging().getNext() != null) {
			page = getPageDetails(page.getPaging().getNext());
			if (page != null) {
				list.add(page);
			}
		}
		return list;
	}


	private List<FbPage> getChildPageDetailsList(String fbPageId, String pageAccessToken,String channel) throws Exception {
		FbPage page = getChildLoactionsFromFB(fbPageId, pageAccessToken,channel);
		int total = 0;
		List<FbPage> list = new ArrayList<>();
		if (page != null) {
			if (page.getData() != null && !page.getData().isEmpty()) {
				total = page.getData().size();
				list.add(page);
			}

		}
		while (page != null && page.getPaging() != null && page.getPaging().getNext() != null) {
			String nextPageUrl = page.getPaging().getNext();
			LOGGER.info("Parent page Id {} and next Page Url {}  ", fbPageId, nextPageUrl);
			page = getPageDetails(nextPageUrl);
			if (page != null) {
				if (page.getData() != null && !page.getData().isEmpty()) {
					total += page.getData().size();
					list.add(page);
				}

			}
		}
		if (total > 0) {
			LOGGER.info("Child Page size {} for parent page {}  ", total, fbPageId);

		} else {
			LOGGER.info("No Child Page found for parent page {} with token {} ", fbPageId, pageAccessToken);
		}
		return list;
	}

	public List<FbPage> getPageDetailsListV1(String accessToken, String userId,String channel) throws Exception {
		// Parent pages
		List<FbPage> parentPages = getPageDetailsList(accessToken, userId, channel);
		List<FbPage> allPages = new ArrayList<FbPage>();
		allPages.addAll(parentPages);
		//TODO: We need to filter pages where child pages are not present.
		// Child Pages.
		parentPages.stream().flatMap(fbp -> fbp.getData().stream()).forEach(p -> {
			List<FbPage> childPage;
			try {
				childPage = getChildPageDetailsList(p.getId(), p.getAccess_token(),channel);
				allPages.addAll(childPage);
			} catch (Exception e) {
				LOGGER.error("Exception while fetching  child pages for page {}", p.getId(), e);
			}
		});
		return allPages;
	}

	@Override
	public FbPage getChildLoactionsFromFB(String pageId, String pageAccessToken,String channel){
		String url = StringUtils.format(FacebookApis.PAGE_LOCATIONS,pageId);
		FbPage response = null;
		 MultiValueMap<String, String> params = SocialChannel.INSTAGRAM.getName().equals(channel) ? getPageDetailsParametersInstagram(pageAccessToken): getPageDetailsParameters(pageAccessToken);

		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();

		LOGGER.info("Received request for getChildLoactions API with URL {} and parameters {}", url, params);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FbPage.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_PAGE_DETAILS, true);
		}
		return response;
	}
	

	@Override
	public FbPage getPageDetails(String accessToken, String userId,String channel)  {
		//String url = StringUtils.join(baseUrl, userId, "/accounts");
		String url = StringUtils.format(FacebookApis.USER_ACCOUNTS,userId);
		FbPage response = null;
		
		MultiValueMap<String, String> params = SocialChannel.INSTAGRAM.getName().equals(channel) ? getPageDetailsParametersInstagram(accessToken): getPageDetailsParameters(accessToken);
		
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();

		LOGGER.info("Received request for API getPageDetails URL {} and parameters {}", url, params);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FbPage.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_PAGE_DETAILS, true);
		}
		return response;
	}
	private String prepareRoleURL(String fbPageId, String accessToken) {
		String url = StringUtils.format(FacebookApis.FETCH_INSTAGRAM_INFO, fbPageId);
		MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
		params.add(ACCESS_TOKEN, accessToken);
		params.add(FIELDS, "roles,name");
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();
		return url;
	}

	@Override
	public Permission fetchRole(String pageId, String accessToken) {
		String url = prepareRoleURL(pageId,accessToken);
		Permission responseR  =  new Permission();
		try {
			LOGGER.info("[Instagram]: Received Request for adding role through updateRole URL : {}, access token : {}", url, accessToken);
			responseR = socialRestTemplate.getForObject(new URI(url), Permission.class);
		} catch (Exception e) {
			LOGGER.error("Exception while adding role through updateRole  {} ", e.getMessage());
		}
		return responseR;
	}

	@Override
	public FacebookPageInfo getPageDetailsByPageId(String baseUrl, String accessToken, String pageId) throws Exception {
		//String url = StringUtils.join(baseUrl, pageId);
		String url = StringUtils.format(FacebookApis.PAGE,pageId);
		FacebookPageInfo response = null;
		MultiValueMap<String, String> params = getPageDetailsParametersWithoutTasks(accessToken);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();

		LOGGER.info("Received request for API getPageDetailsByPageId URL {} and parameters {}", url, params);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FacebookPageInfo.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_PAGE_DETAILS, true);
		}
		return null;
	}

	@Override
	public FacebookPageCategories getAllCategories(String accessToken) throws Exception {
		FacebookPageCategories response = null;
		MultiValueMap<String, String> params = getPageDetailsParameters(accessToken);
		final String url = UriComponentsBuilder.fromHttpUrl(FacebookApis.FB_PAGE_CATEGORIES)
				.queryParam("access_token", accessToken)
				.build()
				.encode()
				.toUriString();

		LOGGER.info("Received request for API getAllCategories URL {} accessToken {}", url, accessToken);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FacebookPageCategories.class);
			if (response != null) {
				return response;
			}
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FB_CATEGORIES, true);
		}
		return null;
	}

	@Override
	public FbMetricResponse getFbInsights(List<FbMetricName> metricNames, FbMetricPeriod period, String since,
										  String until, String pageId, String accessToken) throws Exception {
		FbMetricResponse response = null;
		final String url = UriComponentsBuilder.fromHttpUrl(StringUtils.format(FacebookApis.FB_PAGE_INSIGHTS, pageId))
				.queryParams(new LinkedMultiValueMap<String, String>(){{
					add(ACCESS_TOKEN, accessToken);
					add(METRIC, new HashSet<>(metricNames)
							.stream()
							.map(FbMetricName::name)
							.collect(Collectors.joining(",")));
					add(SINCE, since);
					add(UNTIL, until);
					if (period != null) add(PERIOD, period.name());
				}}).build().encode().toUriString();

		LOGGER.info("Received request for API getFbInsights URL {}", url);
		try {
			response = socialRestTemplate.getForObject(url, FbMetricResponse.class);
			LOGGER.info("Received response with data length {} for API getFbInsights", CollectionUtils.isNotEmpty(response.getData()) ? response.getData().size() : 0);
			response.getData().stream().forEach(res->{
				res.setFacebookPageId(pageId);
			});
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("getFbInsights HttpStatusCodeException occurred {}", clientExp.getResponseBodyAsString());
			FacebookBaseResponse facebookBaseResponse = JSONUtils.fromJSON(clientExp.getResponseBodyAsString(), FacebookBaseResponse.class);
			boolean ackError = checkIfAcknowledgeException(facebookBaseResponse);
			if(ackError) {
				throw new BirdeyeSocialException(ErrorCodes.FB_ACKNOWLEDGE_ERROR, clientExp.getMessage());
			}
			handleError(url, clientExp, ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS, true);
		} catch (Exception e) {
			LOGGER.error("getFbInsights exception occurred", e);
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS, true);
		}
		return response;
	}

	@Override
	public String getPermToken(String pageId, String accessToken, String baseUrl) throws Exception {
		FacebookPageData facebookPage = getFacebookPage(pageId, accessToken, baseUrl);
		return facebookPage == null ? null : facebookPage.getAccess_token();
	}

	@Override
	public FacebookPageData getFacebookPage(String pageId, String accessToken, String baseUrl) throws Exception {
		//String url = StringUtils.join(baseUrl, pageId);
		String url = StringUtils.format(FacebookApis.PAGE,pageId);
		FacebookPageData response = null;
		MultiValueMap<String, String> params = getFacebookPageParameters(accessToken);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();

		LOGGER.info("Received request for API getFacebookPage URL {} and parameters {}", url, params);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FacebookPageData.class);
			LOGGER.info("Response for getFacebookPage for url {} is :: {}", url, response);
			if (response != null && response.getError() == null) {
				return response;
			}
		}  catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_PAGE_DETAILS, true);
		}
		return null;
	}

	private MultiValueMap<String, String> getFacebookPageParameters(String accessToken) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		if (StringUtils.isEmpty(accessToken)) {
			return map;
		}
		//
		map.add(ACCESS_TOKEN, accessToken);
		map.add(FIELDS, "access_token,username,name,picture,single_line_address");
		return map;
	}

	private void handleError(String url, Exception e, ErrorCodes errorCodes, boolean rethrow){
		//TODO: This may no longer required and can be moved to SocialRestTemplate
		// Spring REST exceptions.
		LOGGER.info("[fb] Error while calling url: {} with error {} ", url, e.getMessage());
		try {
			if(e instanceof ResourceAccessException) {
				LOGGER.info("[fb] Handling socket time out");
				Map<String, Object> errorMap = new HashMap<String, Object>();
				errorMap.put("http_response", 502);
				errorMap.put("error_code", 100);
				throw new BirdeyeSocialException(errorCodes, errorCodes.name(), errorMap);
			}
			if (e instanceof HttpStatusCodeException || e instanceof RestClientException) {
				if (rethrow) {
					throw new BirdeyeSocialException(errorCodes, errorCodes.name(), e);
				}else{
					//Logging the exception and not throwing.
					LOGGER.error("Error calling facebook API {} with error {} ", url, errorCodes , e);
				}
			} else if (e instanceof TooManyRequestException) {
				throw e;
			} else {
				LOGGER.error("Exception while calling facebook API {} ", url);
				throw new BirdeyeSocialException(errorCodes, e.getMessage());
			}
		} catch (BirdeyeSocialException ex) {
			throw ex;
		}
		catch (Exception ex) {
			if(ex instanceof JsonParseException) {
				LOGGER.error("Exception while calling facebook API {} ", url);
				throw new BirdeyeSocialException(errorCodes, e.getMessage());
			} else if (ex instanceof TooManyRequestException) {
				throw new BirdeyeSocialException(errorCodes, e.getMessage(), new HashMap<>());
			}
		}
	}


	@Override
	public void installTabOnPage(FacebookCreds creds) {
		//String url = StringUtils.join(creds.getBaseUrl(), "/tabs?access_token=", creds.getTempAccessToken());

		String url = StringUtils.format(FacebookApis.INSTALL_TAB,creds.getPageId());
		MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
		parametersMap.add(ACCESS_TOKEN, creds.getTempAccessToken());
		parametersMap.add("app_id", creds.getClientId());

		LOGGER.info("Received request for API installTabOnPage URL {} and parameters {}", url, parametersMap);
		try {
			socialRestTemplate.postForObject(url, parametersMap, FacebookGenericResponse.class);
		} catch(Exception e){
			handleError(url, e,ErrorCodes.UNABLE_TO_INSTALL_BIRDEYE_APP_TAB_ON_FACEBOOK_PAGE, false);
		}
	}


	@Override
	public void uninstallTabOnPage(FacebookCreds creds) {
		String url = StringUtils.format(FacebookApis.INSTALL_TAB,creds.getPageId());

		MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
		parametersMap.add(ACCESS_TOKEN, creds.getTempAccessToken());
		parametersMap.add("tab", StringUtils.join("app_", creds.getClientId()));

		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(parametersMap).build().encode().toUriString();

		LOGGER.info("Received request for API uninstallTabOnPage URL {} and parameters {}", url, parametersMap);
		try {
			socialRestTemplate.delete(url);
		}  catch (Exception e) {
			handleError(url, e,ErrorCodes.UNSTALL_BIRDEYE_APP_TAB_ON_FACEBOOK_PAGE, false);
		}
	}


	@Override
	public FacebookFeedData getFacebookHomeFeed(FacebookPageAccessInfo creds, String nextToken) throws Exception {
		FacebookFeedData response = null;
		//String url = StringUtils.join(creds.getBaseUrl(), creds.getProfileId(), "/feed");
		String url = StringUtils.format(FacebookApis.FEED,creds.getPageId());

		MultiValueMap<String, String> parametersMap = getFacebookMyPostParameters(creds, nextToken);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(parametersMap).build().encode().toUriString();

		LOGGER.info("Received request for API getFacebookHomeFeed URL {} and parameters {}", url, parametersMap);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FacebookFeedData.class);
			if (response != null && response.getError() == null) {
				return response;
			}

		}
		catch(HttpStatusCodeException e){
			LOGGER.error("HttpStatusCodeException while calling facebook feed API for URL {} :: {}", url, e.getResponseBodyAsString());
			FacebookPostResponse facebookPostResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
			FacebookErrorResponse errorResponse = facebookPostResponse != null ? facebookPostResponse.getError() : new FacebookErrorResponse();
			Map<String, Object> exceptionDataMap = getExceptionDataMap(errorResponse);
			LOGGER.info("error map:{}",exceptionDataMap);
			throw new BirdeyeSocialException(e.getRawStatusCode(),e.getMessage(),exceptionDataMap);
		}
		catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_FEED, true);
		}
		return response;
	}

	//TODO: What is the use of FacebookPageAccessInfo
	@Override
	public FacebookFeedData getFacebookHomeFeedPaginatedByUrl(FacebookPageAccessInfo creds, String nextUrl) throws Exception {
		FacebookFeedData response = null;

		LOGGER.info("Received request for API getFacebookHomeFeedPaginatedByUrl URL {} and parameters {}", nextUrl, null);
		try {
			response = socialRestTemplate.getForObject(nextUrl, FacebookFeedData.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		}catch (Exception e) {
			handleError(nextUrl, e, ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_FEED, true);
		}
		return response;
	}

	@Override
	public FacebookFeedData getFacebookMyPost(FacebookPageAccessInfo creds, String nextToken) throws Exception {
		FacebookFeedData response = null;
		//String url = StringUtils.join(creds.getBaseUrl(), creds.getProfileId(), "/posts");
		String url = StringUtils.format(FacebookApis.POSTS,creds.getPageId());

		MultiValueMap<String, String> parametersMap = getFacebookMyPostParameters(creds, nextToken);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(parametersMap).build().encode().toUriString();

		LOGGER.info("Received request for API getFacebookMyPost URL {} and parameters {}", url, parametersMap);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FacebookFeedData.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		}catch(HttpStatusCodeException e){
			LOGGER.error("HttpStatusCodeException while calling facebook feed API for URL {} :: {}", url, e.getResponseBodyAsString());
			FacebookPostResponse facebookPostResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
			FacebookErrorResponse errorResponse = facebookPostResponse != null ? facebookPostResponse.getError() : new FacebookErrorResponse();
			Map<String, Object> exceptionDataMap = getExceptionDataMap(errorResponse);
			LOGGER.info("error map:{}",exceptionDataMap);
			throw new BirdeyeSocialException(e.getRawStatusCode(),e.getMessage(),exceptionDataMap);
		}
		catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_POSTS, true);
		}
		return response;
	}

	@Override
	public FacebookFeedData getFacebookMyPostMetadata(FacebookPageAccessInfo creds, Integer postCount) {
		FacebookFeedData response = null;
		String url = StringUtils.format(FacebookApis.POSTS,creds.getPageId());

		MultiValueMap<String, String> parametersMap = getFacebookMyPostMetaDataParameters(creds, postCount);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(parametersMap).build().encode().toUriString();

		LOGGER.info("Received request for API getFacebookMyPost URL {} and parameters {}", url, parametersMap);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FacebookFeedData.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_POSTS, true);
		}
		return response;
	}

	private MultiValueMap<String, String> getFacebookMyPostCommentsParameters(String accessToken, String nextPageToken, Boolean getAll) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		map.add(ACCESS_TOKEN, accessToken);
		map.add(FIELDS,
				FB_POST_COMMENT_FIELDS);
		map.add(FILTER, FB_POST_COMMENT_FILTER);
		map.add(LIMIT, "50");
		if (!getAll) {
			map.add(ORDER, REVERSE);
			map.add(LIMIT, "20");
		}
		if(Objects.nonNull(nextPageToken)) {
			map.add(AFTER, nextPageToken);
		}

		return map;
	}

	private MultiValueMap<String, String> getFacebookMyPostParameters(FacebookPageAccessInfo creds, String nextToken) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		String pageLimit = CacheManager.getInstance()
				.getCache(SystemPropertiesCache.class)
				.getSocialFbWallPostCountLimit();
		map.add(ACCESS_TOKEN, creds.getAccessToken());

		map.add(FIELDS,
				FB_QUERY_FIELDS);
		map.add(LIMIT, pageLimit);
		map.add(AFTER, nextToken);

		return map;
	}

	private MultiValueMap<String, String> getFacebookMyPostMetaDataParameters(FacebookPageAccessInfo creds, Integer count) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		map.add(ACCESS_TOKEN, creds.getAccessToken());
		map.add(FIELDS,
				FB_POST_METDATA_QUERY_FIELDS);
		map.add(LIMIT, count.toString());
		return map;
	}

	@Override
	public String postComment(FacebookPageAccessInfo creds, FacebookData commentData, FbUser publisher) throws Exception {
		//String url = StringUtils.join(creds.getBaseUrl(), "comments?access_token=", creds.getAccessToken());
		String url = StringUtils.format(FacebookApis.COMMENTS,creds.getObjectId());

		FacebookPostResponse response = null;
		MultiValueMap<String, String> parameters = getFacebookPostCommentsParameters(commentData, publisher);
		parameters.add(ACCESS_TOKEN, creds.getAccessToken());

		LOGGER.info("Received request for API postComment URL {} and parameters {}", url, parameters);
		try {
			response = socialRestTemplate.postForObject(url, parameters, FacebookPostResponse.class);
			if (response != null && response.getError() == null) {
				return response.getId();
			}
		}  catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_COMMENT_FACEBOOK_OBJECT, true);
		}
		return null;
	}

	private MultiValueMap<String, String> getFacebookPostCommentsParameters(FacebookData commentData, FbUser publisher) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		if (StringUtils.isNotBlank(commentData.getText())) {
			if (publisher != null) {
				String comment = StringUtils.join("@[", publisher.getId(), ":", publisher.getName(), "]", commentData.getText());
				map.add(MESSAGE, comment);
			} else {
				map.add(MESSAGE, commentData.getText());
			}
		}
		if (StringUtils.isNotBlank(commentData.getMediaUrl())) {
			map.add("attachment_url", commentData.getMediaUrl());
		}
		return map;
	}

	@Override
	public boolean likeObject(FacebookPageAccessInfo creds) throws Exception {
		//String url = StringUtils.join(creds.getBaseUrl(), "likes?access_token=", creds.getAccessToken());
		String url = StringUtils.format(FacebookApis.LIKES,creds.getObjectId());
		MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
		parametersMap.add(ACCESS_TOKEN, creds.getAccessToken());

		FacebookAcknowledgementResponse response = null;

		LOGGER.info("Received request for API likeObject URL {} and parameters {}", url, parametersMap);
		try {
			response = socialRestTemplate.postForObject(url, parametersMap, FacebookAcknowledgementResponse.class);
			if (response != null && response.getError() == null) {
				return BooleanUtils.isTrue(response.getSuccess());
			}
		}  catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_LIKE_FACEBOOK_OBJECT, true);
		}
		return false;
	}

	@Override
	public boolean unlikeObject(FacebookPageAccessInfo creds) throws Exception {
		String url = StringUtils.join(creds.getBaseUrl(), "likes?access_token=", creds.getAccessToken());
		
		FacebookAcknowledgementResponse response = null;
		
		LOGGER.info("Received request for API unlikeObject URL {} and parameters {}", url, null);
		try {
			ResponseEntity<FacebookAcknowledgementResponse> responseEntity = socialRestTemplate.exchange(url, HttpMethod.DELETE, null, FacebookAcknowledgementResponse.class);
			response = responseEntity.getBody();
			if (response != null && response.getError() == null) {
				return BooleanUtils.isTrue(response.getSuccess());
			}
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_UNLIKE_FACEBOOK_OBJECT, true);
		}
		return false;
	}

	@Override
	public boolean deleteObject(FacebookPageAccessInfo creds) throws Exception {
		String url = StringUtils.join(creds.getBaseUrl(), "?",ACCESS_TOKEN,"=", creds.getAccessToken());
		FacebookAcknowledgementResponse response = null;

		LOGGER.info("Received request for API deleteObject URL {} and parameters {}", url, null);
		try {
			ResponseEntity<FacebookAcknowledgementResponse> responseEntity = socialRestTemplate.exchange(url, HttpMethod.DELETE, null, FacebookAcknowledgementResponse.class);
			if (responseEntity != null && responseEntity.getBody() != null) {
				response = responseEntity.getBody();
				return BooleanUtils.isTrue(response.getSuccess());
			}
		}  catch (Exception e) {
			LOGGER.error("Error calling delete facebook API {} with error ", url , e);
			if(e instanceof HttpStatusCodeException) {
				HttpStatusCodeException ex = (HttpStatusCodeException) e;
				LOGGER.info("HTTP exception occured: {}",ex.getResponseBodyAsString());
				FacebookBaseResponse facebookBaseResponse = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(ex.getResponseBodyAsString(), FacebookPostResponse.class);
				FacebookErrorResponse errorResponse = facebookBaseResponse != null ? facebookBaseResponse.getError() : new FacebookErrorResponse();
				if(errorResponse.getCode()==100 && errorResponse.getError_subcode()==33) {
					throw new BirdeyeSocialException(ErrorCodes.CLIENT_ERROR_404, e.getMessage());
				}
			}
			handleError(url, e, ErrorCodes.UNABLE_TO_DELETE_FACEBOOK_OBJECT, true);
		}
		return false;
	}

	@Override
	public DebugTokenResponse getTokenDetails(String pageAccessToken, String appAccessToken) {
		if (StringUtils.isEmpty(pageAccessToken) || StringUtils.isEmpty(appAccessToken)) {
			return null;
		}
		DebugTokenResponse response = null;
		MultiValueMap<String, String> params = getTokenDetailsParameters(pageAccessToken, appAccessToken);
		String url = UriComponentsBuilder.fromHttpUrl(DEBUG_TOKEN).queryParams(params).build().encode().toUriString();

		LOGGER.info("Received request for API getTokenDetails URL {} and parameters {}", url, params);

		try {
				response = socialRestTemplate.getForObject(new URI(url), DebugTokenResponse.class);
				if (response != null && response.getError() == null) {
					return response;
				}
			}
		catch (HttpStatusCodeException e) {
			LOGGER.error("HttpStatusCodeException while calling facebook debug token API for URL {} :: {}", url, e.getResponseBodyAsString());
			FacebookPostResponse facebookPostResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
			FacebookErrorResponse errorResponse = facebookPostResponse != null ? facebookPostResponse.getError() : new FacebookErrorResponse();
			Map<String, Object> exceptionDataMap = getExceptionDataMap(errorResponse);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GENERATE_TOKEN_ON_FACEBOOK,e.getMessage() ,exceptionDataMap);
		}
		catch (Exception e) {
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GENERATE_TOKEN_ON_FACEBOOK, ErrorCodes.UNABLE_TO_GENERATE_TOKEN_ON_FACEBOOK.name(), e);
		}
		return response;
	}

	private MultiValueMap<String, String> getTokenDetailsParameters(String pageAccessToken, String appAccessToken) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		map.add("input_token", pageAccessToken);
		map.add(ACCESS_TOKEN, appAccessToken);
		return map;
	}

	@Override
	public MediaTarget getVideoSource(FacebookPageAccessInfo creds) throws Exception {
		String url = creds.getBaseUrl();
		MediaTarget response = null;
		MultiValueMap<String, String> params = getVideoSourceParameters(creds);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();

		LOGGER.info("Received request for API getVideoSource URL {} and parameters {}", url, params);
		try {
			response = socialRestTemplate.getForObject(new URI(url), MediaTarget.class);

			if (response != null && response.getError() == null) {
				return response;
			}
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_GET_FACEBOOK_VIDEO_SOURCE, true);
		}
		return response;
	}

	private MultiValueMap<String, String> getVideoSourceParameters(FacebookPageAccessInfo creds) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		map.add(ACCESS_TOKEN, creds.getAccessToken());
		map.add(FIELDS, "id,source");
		return map;
	}

	@Override
	public FbUser getUserForPost(FacebookPageAccessInfo creds) throws  Exception {
		//String url = creds.getBaseUrl();
		String url = StringUtils.format(FacebookApis.POST,creds.getObjectId());
		FbUser response = null;
		MultiValueMap<String, String> params = getUserForPostParameters(creds);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();

		LOGGER.info("Received request for API getUserForPost URL {} and parameters {}", url, params);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FbUser.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_GET_USER_DETAILS, true);
		}
		return null;
	}

	private MultiValueMap<String, String> getUserForPostParameters(FacebookPageAccessInfo creds) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		map.add(ACCESS_TOKEN, creds.getAccessToken());
		map.add(FIELDS, "from");
		return map;
	}

	@Override
	public FacebookPlacesResponse getFacebookPlaces(FacebookPageAccessInfo creds, String queryParam) {
		String url = FacebookUtils.getFacebookPlacesUrl(creds, queryParam);
		FacebookPlacesResponseExternal externalResponse = null;

		LOGGER.info("Received request for API getFacebookPlaces URL {} and parameters {}", url, null);
		try {
			externalResponse = socialRestTemplate.getForObject(new URI(url), FacebookPlacesResponseExternal.class);
		} catch (RestClientResponseException e) {
			LOGGER.error("RestClientResponseException while getting places for request {} is {}. Detailed Stacktrace is {} ", url, e.getResponseBodyAsString(), e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.FACEBOOK_INVALID_PLACES_DATA, e.getMessage());
		} catch (Exception e) {
			LOGGER.error("RestClientResponseException while getting places for request {} Detailed Stacktrace is {} ", url, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.FACEBOOK_INVALID_PLACES_DATA, e.getMessage());
		}
		// Find something else rather than this approach. Problem is deserializing from instagram and redis for a camel case object results in null
		// value from redis
		return FacebookUtils.getFacebookPlacesResponseFromExternal(externalResponse);
	}

	@Override
	public FbPage getPageDetails(String nextUrl) {
		FbPage page = null;
		try {
			GetMethod getMethod = new GetMethod(nextUrl);
			HttpClient httpClient = new HttpClient();

			httpClient.executeMethod(getMethod);

			if (getMethod.getStatusCode() == 200) {
				String response = getMethod.getResponseBodyAsString();
				page = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).readValue(response, FbPage.class);
			} else {
				JsonObject json = new JsonParser().parse(getMethod.getResponseBodyAsString().trim()).getAsJsonObject();
				if (json.has(ERROR)) {
					LOGGER.info("Error occurred while retrieving facebook pages : {} and url : {}", json.getAsJsonObject(ERROR).get(MESSAGE).getAsString(), nextUrl);
					JsonObject errorObj = json.getAsJsonObject(ERROR);
					if (errorObj.has("code")) {
						int code = errorObj.get("code").getAsInt();
						if (code == 100 || code == 190) {
							throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN, json.getAsJsonObject(ERROR).get(MESSAGE).getAsString());
						} else {
							throw new BirdeyeSocialException(json.getAsJsonObject(ERROR).get(MESSAGE).getAsString());
						}
					}
				}
			}
		} catch (IOException e) {
			handleError(nextUrl, e, ErrorCodes.UNABLE_TO_FETCH_PAGE_DETAILS, true);
		}
		return page;
	}

//	@Cacheable(value = "facebookPlaces", key = "#queryParam", unless = "#result == null")
//	@Override
//	public FacebookPlacesResponse getFacebookPlacesCached(String queryParam) {
//		return null;
//	}


	/*
	 * (non-Javadoc)
	 *
	 * @see com.birdeye.social.facebook.FacebookService#postText1(com.birdeye.social.facebook.FacebookPageAccessInfo,
	 * com.birdeye.social.facebook.FacebookData)
	 */


	@Override
	public String postVideoHandlingExceptions(FacebookPageAccessInfo creds, FacebookData fbData, Long entId)
			throws Exception {
		return postVideoV2(creds, fbData);
	}

	@Override
	public FbUserProfileInfo getUserDetails(String baseUrl, String userId, String accessToken) throws Exception {
		//String url = StringUtils.join(baseUrl, userId);
		String url = StringUtils.format(FacebookApis.USER,userId);
		FbUserProfileInfo response = null;
		MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
		if (StringUtils.isNotEmpty(accessToken)) {
			params.add(ACCESS_TOKEN, accessToken);
			params.add(FIELDS, "id,name,picture");
		}
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();

		LOGGER.info("Received request for API getUserDetails  URL {} and parameters {}", url, params);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FbUserProfileInfo.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		} catch (HttpStatusCodeException e) {
			throw new SocialBirdeyeException(ErrorCodes.UNABLE_TO_GET_USER_DETAILS, e.getMessage());
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_GET_USER_DETAILS, true);
		}
		return null;
	}


//	@Override
//	public List<BusinessFBPage> updateRole(List<BusinessFBPage> pages) {
//		List<BusinessFBPage> updatedList = new ArrayList<>();
//		pages.parallelStream().forEach(page -> {
//			updatedList.add(updateRole(page));
//		});
//		return updatedList;
//	}

	private String prepareRoleURL(BusinessFBPage page) {
		String url = StringUtils.join(GRAPH_API_BASE, page.getFacebookPageId());
		MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
		params.add(ACCESS_TOKEN, page.getPageAccessToken());
		params.add(FIELDS, "roles,parent_page");
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();
		return url;
	}


	/**
	 * API to fetch Posts from Facebook using page alias
	 * @throws IOException
	 */
	@Override
	@Retryable(value = BirdeyeSocialException.class, maxAttempts = 3, backoff = @Backoff(delay = 15000))
	public List<PostInsightItem> getPost(FacebookPostInput input) throws IOException {
		LOGGER.info("Inside getPost for facebook for pageId :{}", input.getPageId());
		List<PostInsightItem> posts = new ArrayList<>();
		//String url = URL.replace("[Handler]", input.getHandler());
		String url = StringUtils.format(FacebookApis.FEED,input.getPageId());

		MultiValueMap<String, String> queryParam = new LinkedMultiValueMap<>();
		queryParam.add(FIELDS, "attachments{title},created_time,message,story,id,from");
		queryParam.add(LIMIT, "10");
		queryParam.add(ACCESS_TOKEN, input.getAccessToken());
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(queryParam).build().encode().toUriString();
		LOGGER.info("Getting Facebook Mentions data for url {} ", url);

		ResponseEntity<PostInsightData> responseEntity = null;
		int count=10;
		Integer facebookGetPostPageRetry = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getFacebookPostPageRetry();
		Integer singlePageRetry = facebookGetPostPageRetry;
		try {
			do {
				try {
					responseEntity = socialRestTemplate.getForEntity(new URI(url), PostInsightData.class);
					LOGGER.info("Received Response from Facebook api with status :{}",responseEntity.getStatusCode());
					url = null;
					if (responseEntity != null && responseEntity.getBody() != null && CollectionUtils.isNotEmpty(responseEntity.getBody().getData())) {
						posts.addAll(responseEntity.getBody().getData());
						if(Objects.nonNull(responseEntity.getBody().getPaging()) && Objects.nonNull(responseEntity.getBody().getPaging().getNext())) {
							url = responseEntity.getBody().getPaging().getNext();
						}
					}
					count--;
					singlePageRetry = facebookGetPostPageRetry;
				} catch (HttpStatusCodeException e) {
					if(singlePageRetry <= 0) throw e;
					singlePageRetry--;
				} catch (Exception e) {
					throw e;
				}
			} while(count>0 && StringUtils.isNotEmpty(url));
		} catch (HttpStatusCodeException e) {
			FacebookBaseResponse res= null;
			try {
				res = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), FacebookBaseResponse.class);
			}
			catch(JsonParseException ex){
				LOGGER.error("InternalServerException while calling getPost for Facebook for URL {} and exception {}", url, e.getResponseBodyAsString());
				return posts;
			}
			if (e.getStatusCode().is5xxServerError()) {
				if(Objects.nonNull(res.getError()) && res.getError().getCode() == 1 &&
						Objects.nonNull(res.getError().getMessage()) && res.getError().getMessage().contains("unknown error")) {
					LOGGER.warn("BadRequest while calling getPost for Facebook for URL {} and error message {}", url,res.getError().getMessage());
					throw new BirdeyeSocialException(res.getError().getMessage());
				}
				LOGGER.error("InternalServerException while calling getPost for Facebook for URL {} and exception {}", url, e.getResponseBodyAsString());
//				throw new ExternalAPIException(ExternAPIErrorCode.INTERNAL_SERVER_ERROR, e.getStatusCode().getReasonPhrase());
			}
			if (e.getStatusCode().is4xxClientError()) {
				if(res.getError().getCode() == 100 && res.getError().getError_subcode() == 33) {
					LOGGER.warn("BadRequest while calling getPost for Facebook for URL {} and error message {}", url,res.getError().getMessage());
				} else {
					LOGGER.error("RestClientException while calling getPost for Facebook for URL {} and exception {}", url, e.getResponseBodyAsString());
//					throw new ExternalAPIException(ExternAPIErrorCode.CLIENT_ERROR, e.getStatusCode().getReasonPhrase());
				}
			}
		} catch (Exception e) {
			LOGGER.error("Error occurred while calling Facebook api for url {}, exception : {}", url, e);
//			throw new ExternalAPIException(ExternAPIErrorCode.UNKNOWN, e.getMessage(), e);
		}
		return posts;
	}

	// https://developers.facebook.com/docs/graph-api/reference/v3.2/page
	@SuppressWarnings({"unchecked","rawtypes"})
	public Map<String,String> getFacebookPageId(String accessToken, String pageUrl) {
		MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
		queryParams.add(ACCESS_TOKEN, accessToken);
		queryParams.add("id", pageUrl);
		String url = UriComponentsBuilder.fromHttpUrl(GRAPH_API_BASE_VERSION_DEFAULT_V21)
				.queryParams(queryParams).build().encode().toUriString();
		LOGGER.info("url for getFacebookPageId {} ", url);
		ResponseEntity<Map> response = socialRestTemplate.getForEntity(url,
				Map.class);
		return response.getBody();
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public Map<String, Object> updateFacebookPageInfo(FacebookPageAccessInfo creds, FBPageInput fbPageInput) throws IOException {
		String url = StringUtils.join(creds.getBaseUrl(),creds.getPageId(), "?access_token=", creds.getAccessToken());
		ResponseEntity<Map> responseEntity = null;
		try{
			LOGGER.info("[Facebook Social Integration] update on facebook page for location URI: {} with request: {}", url,fbPageInput.toJson());
			responseEntity = socialRestTemplate.postForEntity(url, fbPageInput, Map.class);
			LOGGER.info(UPDATE_ON_FACEBOOK_RESPONSE, responseEntity.getStatusCode());
			if (responseEntity.getStatusCode().is2xxSuccessful()) {
				LOGGER.info(UPDATE_ON_FACEBOOK_RESPONSE, responseEntity.getBody());
			} else {
				LOGGER.error(UPDATE_ON_FACEBOOK_RESPONSE, responseEntity.getStatusCode().getReasonPhrase());
			}
		} catch (HttpStatusCodeException e) {
			LOGGER.error("HttpStatusCodeException while updating facebook business API for URL {} :: {}", url, e.getResponseBodyAsString());
			Map<String, Object> exceptionDataMap = new HashMap<>();
			try {
				FacebookPostResponse response = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), FacebookPostResponse.class);
				FacebookErrorResponse errorResponse = response != null ? response.getError() : new FacebookErrorResponse();
				exceptionDataMap = getExceptionDataMap(errorResponse);
				throw new SocialPageUpdateException(ErrorCodes.UNABLE_TO_UPDATE_FACEBOOK_PAGE_INFO,e.getMessage() ,exceptionDataMap);

			} catch (Exception ex) {
				if(ex instanceof JsonParseException) {
					exceptionDataMap = getExceptionDataMapFromString(e.getResponseBodyAsString());
					throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_UPDATE_FACEBOOK_PAGE_INFO, e.getMessage(), exceptionDataMap);
				}
				throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_UPDATE_FACEBOOK_PAGE_INFO, e.getMessage());
			}
		} catch (RestClientException e) {
			LOGGER.error("[Facebook Social Integration] Exception in while updating facebook business API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR,e.getMessage(), e);
		} catch (Exception e) {
			LOGGER.error("[Facebook Social Integration] Exception in while updating facebook business API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN, e.getMessage(), e);
		}
		return responseEntity != null ? responseEntity.getBody() : null;
	}

	private Map<String, Object> getExceptionDataMapFromString(String errorString) {
		Map<String, Object> map = new HashMap<>();
		Boolean markPageInactive = errorString.contains("this content isn't available right now");
		map.put("mark_page_inactive", markPageInactive);
		return map;
	}

	private FBPhoto uploadPhoto(FacebookPageAccessInfo creds, String imageUrl) throws IOException{
		String url = StringUtils.join(creds.getBaseUrl(),creds.getPageId(), "/photos?access_token=", creds.getAccessToken())+"&url="+imageUrl+"&published=false";
		ResponseEntity<FBPhoto> responseEntity = null;
		try{
			LOGGER.info("[Facebook Social Integration] update cover image of fb for url: {}", url);
			responseEntity = socialRestTemplate.postForEntity(url, null, FBPhoto.class);
			LOGGER.info(UPDATE_COVER_IMAGE_OF_FB_RESPONSE, responseEntity.getStatusCode());
		} catch (HttpStatusCodeException e) {
			LOGGER.error("HttpStatusCodeException while update cover image of fb API for URL {} :: {}", url, e.getResponseBodyAsString());
			Map<String, Object> exceptionDataMap = new HashMap<>();
			try {
				FacebookPostResponse response = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), FacebookPostResponse.class);
				FacebookErrorResponse errorResponse = response != null ? response.getError() : new FacebookErrorResponse();
				exceptionDataMap = getExceptionDataMap(errorResponse);
				throw new SocialPageUpdateException(ErrorCodes.UNABLE_TO_UPDATE_FACEBOOK_PAGE_INFO,e.getMessage() ,exceptionDataMap);
			} catch (Exception ex) {
				if(ex instanceof JsonParseException) {
					exceptionDataMap = getExceptionDataMapFromString(e.getResponseBodyAsString());
					throw new SocialPageUpdateException(ErrorCodes.UNABLE_TO_UPDATE_FACEBOOK_PAGE_INFO,e.getMessage() ,exceptionDataMap);
				}
			}
		} catch (RestClientException e) {
			LOGGER.error("[Facebook Social Integration] Exception in while update cover image of fb API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR,e.getMessage(), e);
		} catch (Exception e) {
			LOGGER.error("[Facebook Social Integration] Exception in while update cover image of fb API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN, e.getMessage(), e);
		}
		return responseEntity != null ? responseEntity.getBody() : null;
	}

	/**
	 * Uploading cover photo is a 2-step process
	 * Step 1: Upload image to FB to get Photo ID
	 * Step 2: Set photo ID to page's "cover" property
	 * @param creds
	 * @param imageUrl
	 * @return
	 * @throws IOException
	 */
	@Override
	public void updateCoverImage(FacebookPageAccessInfo creds, String imageUrl) throws IOException {
		try{
			LOGGER.info("updateCoverImage: Trying to upload image with URL {}", imageUrl);
			final FBPhoto fbPhoto = uploadPhoto(creds, imageUrl);
			if (fbPhoto == null || fbPhoto.getId() == null) {
				throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_UPDATE_FACEBOOK_PAGE_INFO, "Cannot upload FB Photo");
			}
			LOGGER.info("updateCoverImage: Trying to set cover image with photo ID {}", fbPhoto.getId());
			final String url = StringUtils.join(creds.getBaseUrl(),
					creds.getPageId(),
					"?access_token=" + creds.getAccessToken());
			final Map<String, String> postBody = new HashMap<>();
			postBody.put("cover", fbPhoto.getId());
			LOGGER.info("updateCoverImage: Updating cover image of fb with URL: {}", url);
			final ResponseEntity<Map> resEntity = socialRestTemplate.postForEntity(url, postBody, Map.class);
			LOGGER.info("updateCoverImage: Upload successful {}", resEntity.getBody());
		} catch (HttpStatusCodeException e) {
			throw new SocialPageUpdateException(ErrorCodes.UNABLE_TO_UPDATE_FACEBOOK_PAGE_INFO,e.getMessage());
		} catch (RestClientException e) {
			LOGGER.error("updateCoverImage: Exception while updating cover image of FB Page: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR,e.getMessage(), e);
		} catch (Exception e) {
			if(e instanceof SocialPageUpdateException) {
				LOGGER.error("updateCoverImage: SocialPageUpdateException while updating cover image of FB Page: ", e);
				Map<String, Object> exceptionDataMap = ((SocialPageUpdateException) e).getData();
				throw new SocialPageUpdateException(ErrorCodes.UNABLE_TO_UPDATE_FACEBOOK_PAGE_INFO,e.getMessage() ,exceptionDataMap);
			}
			LOGGER.error("updateCoverImage: Exception while updating cover image of FB Page: " ,e);
			throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN, e.getMessage(), e);
		}
	}

	@Override
	public void updateLogoImage(FacebookPageAccessInfo creds, String imageUrl) throws IOException {
		String url = StringUtils.join(creds.getBaseUrl(),creds.getPageId(), "/picture?access_token=", creds.getAccessToken())+"&picture="+imageUrl;
		ResponseEntity<Void> responseEntity = null;
		try{
			LOGGER.info("[Facebook Social Integration] update logo image of fb for url: {}", url);
			responseEntity = socialRestTemplate.postForEntity(url, null, Void.class);
			LOGGER.info(UPDATE_LOGO_IMAGE_OF_FB_RESPONSE, responseEntity.getStatusCode());
			if (responseEntity.getStatusCode().is2xxSuccessful()) {
				LOGGER.error(UPDATE_LOGO_IMAGE_OF_FB_RESPONSE, responseEntity.getBody());
			} else {
				LOGGER.error(UPDATE_LOGO_IMAGE_OF_FB_RESPONSE, responseEntity.getStatusCode().getReasonPhrase());
			}
		} catch (HttpStatusCodeException e) {
			LOGGER.error("HttpStatusCodeException while update logo image of fb API for URL {} :: {}", url, e.getResponseBodyAsString());
			Map<String, Object> exceptionDataMap = new HashMap<>();
			try {
				FacebookPostResponse response = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), FacebookPostResponse.class);
				FacebookErrorResponse errorResponse = response != null ? response.getError() : new FacebookErrorResponse();
				exceptionDataMap = getExceptionDataMap(errorResponse);
				if (errorResponse.getCode() == 100) {
					LOGGER.warn("updateLogoImage: Received 400 error on Facebook for picture update");
				} else {
					exceptionDataMap = getExceptionDataMapFromString(e.getResponseBodyAsString());
					throw new SocialPageUpdateException(ErrorCodes.UNABLE_TO_UPDATE_FACEBOOK_PAGE_INFO,e.getMessage() ,exceptionDataMap);
				}
			} catch (Exception ex) {
				if(ex instanceof JsonParseException) {

					throw new SocialPageUpdateException(ErrorCodes.UNABLE_TO_UPDATE_FACEBOOK_PAGE_INFO,e.getMessage() ,exceptionDataMap);

				}
			}
		} catch (RestClientException e) {
			LOGGER.error("[Facebook Social Integration] Exception in while update logo image of fb API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR,e.getMessage(), e);
		} catch (Exception e) {
			LOGGER.error("[Facebook Social Integration] Exception in while update logo image of fb API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN, e.getMessage(), e);
		}
	}

	@Override
	public FbUserProfileInfo getPagePhoneNumber(String accessToken) throws Exception {

			FbUserProfileInfo response = null;
			MultiValueMap<String, String> params = getPagePhoneDetails(accessToken);
			String url  = UriComponentsBuilder.fromHttpUrl(FacebookApis.ME).
					queryParams(params).build().encode().toUriString();

			LOGGER.info("Received request for API getUserDetails  URL {} and parameters {}", url, params);
			try {
				response = socialRestTemplate.getForObject(new URI(url), FbUserProfileInfo.class);
				if (response != null && response.getError() == null) {
					return response;
				}
			} catch (Exception e) {
				handleError(url, e, ErrorCodes.UNABLE_TO_GET_USER_DETAILS, true);
			}
			return null;
		}
	@Override
	public FbPostInsightResponse getInsightResponseForPostAndStory(String accessToken, String postId, List<String>metric) {
		FbPostInsightResponse response = null;
		final String url = UriComponentsBuilder.fromHttpUrl(StringUtils.format(FacebookApis.FB_PAGE_INSIGHTS, postId))
				.queryParams(new LinkedMultiValueMap<String, String>(){{
					add(ACCESS_TOKEN, accessToken);
					add(METRIC, String.join(",", new HashSet<>(metric)));
				}}).build().encode().toUriString();

		LOGGER.info("Received request for API getPostInsight URL: {}", url);
		try {
			response = socialRestTemplate.getForObject(url, FbPostInsightResponse.class);
			LOGGER.info("Received response with data length {} for API getPostInsight", CollectionUtils.isNotEmpty(response.getData()) ? response.getData().size() : 0);
		} catch (HttpStatusCodeException clientExp) {
			FacebookBaseResponse facebookBaseResponse = JSONUtils.fromJSON(clientExp.getResponseBodyAsString(), FacebookBaseResponse.class);
			boolean ackError = checkIfAcknowledgeException(facebookBaseResponse);
			if(ackError) {
				LOGGER.warn("getFbInsights acknowledged HttpStatusCodeException occurred: {}", clientExp.getResponseBodyAsString());
				throw new BirdeyeSocialException(ErrorCodes.FB_ACKNOWLEDGE_ERROR, clientExp.getMessage());
			}
			LOGGER.error("getFbInsights HttpStatusCodeException occurred: {}", clientExp.getResponseBodyAsString());
			if(Objects.nonNull(facebookBaseResponse) && facebookBaseResponse.getError().getCode() == 100
					&& facebookBaseResponse.getError().getError_subcode() == 33 ){
				throw new HttpStatusCodeException(clientExp.getStatusCode(), ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS.name()) {
				};
			}
			if(Objects.nonNull(facebookBaseResponse) && Objects.nonNull(facebookBaseResponse.getError())
					&& facebookBaseResponse.getError().getCode() == 1
					&& facebookBaseResponse.getError().getError_subcode() == 99 ){
				throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS, clientExp.getResponseBodyAsString());
			}
			handleError(url, clientExp, ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS, true);
		} catch (Exception e) {
			LOGGER.error("getFbInsights exception occurred", e);
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS, true);
		}
		return response;
	}

	private boolean checkIfAcknowledgeException(FacebookBaseResponse errorResponse) {
		if(Objects.isNull(errorResponse) || Objects.isNull(errorResponse.getError()))
			return false;

		FacebookErrorResponse error = errorResponse.getError();
		List<Integer> ackErrorSubCodes = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getFbInSightsAcknowledgedErrorSubCodes();
		if(Objects.nonNull(error.getCode()) && error.getCode().equals(190)
				&& Objects.nonNull(error.getError_subcode()) && ackErrorSubCodes.contains(error.getError_subcode())) {
			return true;
		}

		return false;
	}

	private boolean checkIfDeletedCommentException(FacebookBaseResponse errorResponse, String commentId) {
		if(Objects.isNull(errorResponse) || Objects.isNull(errorResponse.getError()))
			return false;

		FacebookErrorResponse error = errorResponse.getError();
		if(Objects.nonNull(error.getCode()) && error.getCode().equals(100)
				&& Objects.nonNull(error.getError_subcode()) && error.getError_subcode().equals(33) &&
				Objects.nonNull(error.getMessage()) &&
				error.getMessage().contains("Unsupported get request. Object with ID '"+commentId+"' does not exist")) {
			return true;
		}

		return false;
	}

	@Override
	public FacebookPostDetails getPostData(String accessToken, String postId) {
		FacebookPostDetails facebookFeed = null;
		final String url = UriComponentsBuilder.fromHttpUrl(StringUtils.format(FacebookApis.POST_INFO, postId))
				.queryParams(new LinkedMultiValueMap<String, String>(){{
					add(ACCESS_TOKEN, accessToken);
					add(FIELDS, GET_PAGE_FEED_DETAILS);
				}}).build().encode().toUriString();
		LOGGER.info("Received request for API getPostData URL: {}", url);
		try {
			facebookFeed = socialRestTemplate.getForObject(url, FacebookPostDetails.class);
			LOGGER.info("Received response  for API getPostData: {}",facebookFeed);
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("getPostData HttpStatusCodeException occurred: {}", clientExp.getResponseBodyAsString());
			handleError(url, clientExp, ErrorCodes.UNABLE_TO_FETCH_FB_POST_DATA, true);
		} catch (Exception e) {
			LOGGER.error("getPostData exception occurred", e);
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FB_POST_DATA, true);
		}
		return facebookFeed;
	}

	@Override
	public FacebookPostDetails getStoryData(String accessToken, String postId) {
		FacebookPostDetails facebookFeed = null;
		final String url = UriComponentsBuilder.fromHttpUrl(StringUtils.format(FacebookApis.POST_INFO, postId))
				.queryParams(new LinkedMultiValueMap<String, String>(){{
					add(ACCESS_TOKEN, accessToken);
					add(FIELDS, GET_STORY_DETAILS);
				}}).build().encode().toUriString();
		LOGGER.info("Received request for API getStoryData URL: {}", url);
		try {
			facebookFeed = socialRestTemplate.getForObject(url, FacebookPostDetails.class);
			LOGGER.info("Received response  for API getStoryData: {}",facebookFeed);
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("getStoryData HttpStatusCodeException occurred: {}", clientExp.getResponseBodyAsString());
			handleError(url, clientExp, ErrorCodes.UNABLE_TO_FETCH_FB_POST_DATA, true);
		} catch (Exception e) {
			LOGGER.error("getStoryData exception occurred", e);
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FB_POST_DATA, true);
		}
		return facebookFeed;
	}
	@Override
	public FacebookPostDetails getPostDataForEngage(String accessToken, String postId) {
		FacebookPostDetails facebookFeed = null;
		final String url = UriComponentsBuilder.fromHttpUrl(StringUtils.format(FacebookApis.POST_INFO, postId))
				.queryParams(new LinkedMultiValueMap<String, String>(){{
					add(ACCESS_TOKEN, accessToken);
					add(FIELDS, GET_PAGE_FEED_DETAILS_ENGAGE);
				}}).build().encode().toUriString();
		LOGGER.info("Received request for API getPostData URL: {}", url);
		try {
			facebookFeed = socialRestTemplate.getForObject(url, FacebookPostDetails.class);
			LOGGER.info("Received response  for API getPostData: {}",facebookFeed);
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.info("getPostData HttpStatusCodeException occurred: {}", clientExp.getResponseBodyAsString());
			handleError(url, clientExp, ErrorCodes.UNABLE_TO_FETCH_FB_POST_DATA, true);
		} catch (Exception e) {
			LOGGER.error("getPostData exception occurred", e);
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FB_POST_DATA, true);
		}
		return facebookFeed;
	}
	@Override
	public FacebookPostDetails getPostDataForEngageMention(String accessToken, String postId) {
		FacebookPostDetails facebookFeed = null;
		final String url = UriComponentsBuilder.fromHttpUrl(StringUtils.format(FacebookApis.POST_INFO, postId))
				.queryParams(new LinkedMultiValueMap<String, String>(){{
					add(ACCESS_TOKEN, accessToken);
					add(FIELDS, GET_PAGE_FEED_DETAILS_ENGAGE_MENTION);
				}}).build().encode().toUriString();
		LOGGER.info("Received request for API getPostData URL: {}", url);
		try {
			facebookFeed = socialRestTemplate.getForObject(url, FacebookPostDetails.class);
			LOGGER.info("Received response  for API getPostData: {}",facebookFeed);
		} catch (HttpStatusCodeException clientExp) {
			LOGGER.error("getPostData HttpStatusCodeException occurred: {}", clientExp.getResponseBodyAsString());
			handleError(url, clientExp, ErrorCodes.UNABLE_TO_FETCH_FB_POST_DATA, true);
		} catch (Exception e) {
			LOGGER.error("getPostData exception occurred", e);
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FB_POST_DATA, true);
		}
		return facebookFeed;
	}

	@Override
	public FacebookFeedData getFacebookPostFeedPaginatedByUrl(String nextUrl){
		FacebookFeedData response = null;

		LOGGER.info("Received request for API getFacebookPostFeedPaginatedByUrlome URL {} and parameters {}", nextUrl, null);
		try {
			response = socialRestTemplate.getForObject(new URI(nextUrl), FacebookFeedData.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		}catch (Exception e) {
			handleError(nextUrl, e, ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_POSTS, true);
		}
		return response;
	}

	@Override
	public FacebookPageSearch getPageMentions(String search, String pageAccessToken, String fields, String nextToken) {
		MultiValueMap<String,String> queryParams = new LinkedMultiValueMap<>();
		queryParams.add("fields",fields);
		queryParams.add("access_token",pageAccessToken);
		queryParams.add("limit","5");
		queryParams.add("q",search);
		if(StringUtils.isNotEmpty(nextToken)) {
			queryParams.add("after", nextToken);
		}
		String url = UriComponentsBuilder.fromHttpUrl(SEARCH_PAGES).queryParams(queryParams).build().toUriString();
		try {
			LOGGER.info("url to getPageMentions {} ", url);
			ResponseEntity<FacebookPageSearch> response = socialRestTemplate.exchange(url,HttpMethod.GET,null,FacebookPageSearch.class);
			return response.getBody();
		}catch (HttpStatusCodeException e){
			LOGGER.info("[Facebook Page Search] HttpStatusCodeException part where http status is = {}",e.getStatusCode().value());
			LOGGER.error("HttpStatusCodeException while fetching social mentions of fb API with PPCA token for URL {} :: {}", url, e.getResponseBodyAsString());
			FacebookPostResponse response = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
			FacebookErrorResponse errorResponse = response != null ? response.getError() : new FacebookErrorResponse();
			Map<String, Object> exceptionDataMap = getExceptionDataMap(errorResponse);
			throw new SocialPageUpdateException(ErrorCodes.UNABLE_TO_FETCH_MENTIONS,e.getMessage() ,exceptionDataMap);
		}catch (RestClientException e) {
			LOGGER.error("[Facebook Social Search] Exception in while searching pages of fb API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR,e.getMessage(), e);
		} catch (Exception e) {
			LOGGER.error("[Facebook Social Search] Exception in while searching pages of fb API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN, e.getMessage(), e);
		}
	}

	private MultiValueMap<String, String> getPagePhoneDetails(String accessToken) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		if (StringUtils.isEmpty(accessToken)) {
			return map;
		}
		map.add(ACCESS_TOKEN, accessToken);
		map.add(FIELDS, StringUtils.commaSeparatedString("id",
				"phone"));
		return map;
	}

	private Map<String, Object> getExceptionDataMap(FacebookErrorResponse errorResponse) {
		Map<String, Object> map = new HashMap<>();
		if(errorResponse != null){
			Boolean markPageInactive = FacebookUtils.markPageInactive(errorResponse);
			map.put("mark_page_inactive", markPageInactive);
			map.put("errorCode", errorResponse.getCode());
			map.put("errorSubCode", errorResponse.getError_subcode());
			map.put("errorMessage", errorResponse.getMessage());
			map.put("type", errorResponse.getType());
			map.put("error_user_title", errorResponse.getError_user_title());
			map.put("error_user_msg", errorResponse.getError_user_msg());
			map.put("fbtrace_id", errorResponse.getFbtrace_id());
			map.put("message", errorResponse.getMessage());
		}
		return map;
	}

	@Override
	public List<Map<String, Object>> getBatchImageData(List<String> pageId, String accessToken) {

		MultiValueMap<String,String> queryParams = new LinkedMultiValueMap<>();
		queryParams=getBatchJSONObject(pageId,accessToken,HttpMethod.GET.name(),"picture?redirect=0");

		String url = UriComponentsBuilder.fromHttpUrl(GET_BATCH_IMAGES).queryParams(queryParams).build().toUriString();

		try {
			LOGGER.info("url for getBatchImageData {} ", url);
			ResponseEntity<List<Map<String, Object>>> responseEntity = socialRestTemplate.exchange(url,
					HttpMethod.POST,null,new ParameterizedTypeReference<List<Map<String, Object>>>(){});
			return responseEntity.getBody();
		}catch (HttpStatusCodeException e){
			LOGGER.info("[Facebook Page Batch Images] HttpStatusCodeException part where http status is = {}",e.getStatusCode().value());
			LOGGER.error("HttpStatusCodeException while fetching batch images of fb page id API for URL {} :: {}", url, e.getResponseBodyAsString());
			FacebookPostResponse response = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
			FacebookErrorResponse errorResponse = response != null ? response.getError() : new FacebookErrorResponse();
			throw new SocialPageUpdateException(ErrorCodes.UNABLE_TO_FETCH_IMAGE,e.getMessage() ,null);
		}catch (RestClientException e) {
			LOGGER.error("[Facebook Social Integration] Exception in while get batch image of fb API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR,e.getMessage(), e);
		} catch (Exception e) {
			LOGGER.error("[Facebook Social Integration] Exception in while get batch image of fb API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN, e.getMessage(), e);
		}
	}

	@Override
	public Map<String, String> getFacebookAccessTokenByPageId(String pageId, String userAccessToken) {
		MultiValueMap<String,String> queryParams = new LinkedMultiValueMap<>();
		queryParams.set("fields","id,access_token");
		queryParams.set("access_token",userAccessToken);
		String url = UriComponentsBuilder.fromHttpUrl(GRAPH_API_BASE_VERSION_DEFAULT_V21+pageId).queryParams(queryParams).build().toUriString();
		try {
			LOGGER.info("url for getFacebookAccessTokenByPageId {} ", url);
			ResponseEntity<Map<String,String>> responseEntity =
					socialRestTemplate.exchange(url, HttpMethod.GET,null,new ParameterizedTypeReference<Map<String,String>>(){});
			return responseEntity.getBody();
		}catch (HttpStatusCodeException e){
			LOGGER.info("[Facebook Page Batch Images] HttpStatusCodeException part where http status is = {}",e.getStatusCode().value());
			LOGGER.error("HttpStatusCodeException while fetching access token of fb page id API for URL {} :: {}", url, e.getResponseBodyAsString());
			FacebookPostResponse response = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
			FacebookErrorResponse errorResponse = response != null ? response.getError() : new FacebookErrorResponse();
			throw new SocialPageUpdateException(ErrorCodes.UNABLE_TO_GENERATE_TOKEN_ON_FACEBOOK,errorResponse.getMessage() ,null);
		}catch (RestClientException e) {
			LOGGER.error("[Facebook Social Integration] Exception in while get batch image of fb API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR,e.getMessage(), e);
		} catch (Exception e) {
			LOGGER.error("[Facebook Social Integration] Exception in while get batch image of fb API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN, e.getMessage(), e);
		}
	}

	@Override
	public List<FbRoleInfo> getTasksForPage(String accessToken, String facebookPageId) {
		MultiValueMap<String,String> queryParams = new LinkedMultiValueMap<>();
		queryParams.set("fields","id,tasks,is_active");
		queryParams.set("access_token",accessToken);
		String url = StringUtils.format(GET_USER_PAGE_TASKS,facebookPageId);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(queryParams).build().toUriString();
		LOGGER.info("url for getTasksForPage {} ", url);
		try {
			ResponseEntity<FbTasksInfo> responseEntity = socialRestTemplate.exchange(url,
					HttpMethod.GET,null,FbTasksInfo.class);
			if(Objects.nonNull(responseEntity.getBody()) && CollectionUtils.isNotEmpty(responseEntity.getBody().getData())) {
				return responseEntity.getBody().getData();
			}
		}catch (HttpStatusCodeException e){
			LOGGER.info("[Facebook Page Batch Images] HttpStatusCodeException part where http status is = {}",e.getStatusCode().value());
			LOGGER.error("HttpStatusCodeException while fetching access token of fb page id API for URL {} :: {}", url, e.getResponseBodyAsString());
			FacebookPostResponse response = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
			FacebookErrorResponse errorResponse = response != null ? response.getError() : new FacebookErrorResponse();
			throw new SocialPageUpdateException(ErrorCodes.UNABLE_TO_GENERATE_TOKEN_ON_FACEBOOK,errorResponse.getMessage() ,null);
		}catch (RestClientException e) {
			LOGGER.error("[Facebook Social Integration] Exception in while get batch image of fb API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR,e.getMessage(), e);
		} catch (Exception e) {
			LOGGER.error("[Facebook Social Integration] Exception in while get batch image of fb API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN, e.getMessage(), e);
		}
		return null;
	}


	@Override
	public FacebookPageInfo getPageDetailsByPageId(String pageId, String userAccessToken) {
		MultiValueMap<String,String> queryParams = new LinkedMultiValueMap<>();
		String urlParams = "{url}";
		queryParams.add("fields","id,name,username,link,picture{urlParams},single_line_address,access_token,emails,phone,location" );
		queryParams.add("access_token", userAccessToken);
		String url = UriComponentsBuilder.fromHttpUrl(GRAPH_API_BASE_VERSION_DEFAULT_V21+pageId).queryParams(queryParams).build().toUriString();
		LOGGER.info("url for getPageDetailsByPageId {} ", url);
		try {
			ResponseEntity<FacebookPageInfo> responseEntity = socialRestTemplate.exchange(url,
					HttpMethod.GET,null, FacebookPageInfo.class, urlParams);
			return responseEntity.getBody();
		}catch (HttpStatusCodeException e){
			LOGGER.info("[Facebook Page Batch Images] HttpStatusCodeException part where http status is = {}",e.getStatusCode().value());
			LOGGER.error("HttpStatusCodeException while fetching page details of fb page id API for URL {} :: {}", url, e.getResponseBodyAsString());
			FacebookPostResponse response = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
			FacebookErrorResponse errorResponse = response != null ? response.getError() : new FacebookErrorResponse();
			throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN,errorResponse.getMessage() ,e);
		}catch (RestClientException e) {
			LOGGER.error("[Facebook Social Integration] Exception in while get batch image of fb API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR,e.getMessage(), e);
		} catch (Exception e) {
			LOGGER.error("[Facebook Social Integration] Exception in while get batch image of fb API: ", e);
			throw new ExternalAPIException(ExternalAPIErrorCode.UNKNOWN, e.getMessage(), e);
		}
	}
	private MultiValueMap<String,String> getBatchJSONObject(List<String> pageId, String accessToken,String methodType,String relativeUrl) {

		MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
		JsonArray batch = new JsonArray();
		BatchRequestData requests= new BatchRequestData();
		List<BatchModeRequestInfo> batchModeRequestInfoList = new ArrayList<>();

		pageId.forEach(list-> {
					BatchModeRequestInfo info = new BatchModeRequestInfo();
					info.setMethod(methodType);
					StringBuilder url= new StringBuilder();
					url.append(list).append("/").append(relativeUrl);
					info.setRelativeUrl(url.toString());
					batchModeRequestInfoList.add(info);
				}
			);
		requests.setRequests(batchModeRequestInfoList);

		for (BatchModeRequestInfo requestEntry : requests.getRequests()) {
			JsonObject batchElement = new JsonObject();
			batchElement.addProperty("method", requestEntry.method);
			batchElement.addProperty("relative_url", requestEntry.relativeUrl);

			batch.add(batchElement);
			}

		params.add("batch", batch.toString());
		params.add("access_token",accessToken);
		params.add("include_headers", "false");
		return params;
	}


	@Override
	public FacebookFeedData getFacebookPostFeedDateWise(String accessToken,String since, String until, String pageId) {
		FacebookFeedData response = null;
		String url = StringUtils.format(FacebookApis.POSTS,pageId);

		MultiValueMap<String, String> parametersMap = getFacebookMyPostDateParameters(accessToken, since,until);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(parametersMap).build().encode().toUriString();

		LOGGER.info("Received request for API getFacebookPostFeedDateWise URL {} and parameters {}", url, parametersMap);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FacebookFeedData.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		}catch(HttpStatusCodeException e){
			LOGGER.error("HttpStatusCodeException while calling getFacebookPostFeedDateWise API for URL {} :: {}", url, e.getResponseBodyAsString());
			Map<String, Object> exceptionDataMap = new HashMap<String, Object>();
			if(Constants.FACEBOOK_ERROR_WITHOUT_BODY.contains(e.getResponseBodyAsString())) {
				exceptionDataMap.put("http_response", e.getRawStatusCode());
				exceptionDataMap.put("error_message", e.getResponseBodyAsString());
				throw new BirdeyeSocialException(e.getRawStatusCode(), e.getResponseBodyAsString(), exceptionDataMap);
			}
			FacebookPostResponse facebookPostResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
			FacebookErrorResponse errorResponse = facebookPostResponse != null ? facebookPostResponse.getError() : new FacebookErrorResponse();
			exceptionDataMap = getExceptionDataMap(errorResponse);
			LOGGER.info("error map:{}",exceptionDataMap);
			throw new BirdeyeSocialException(e.getRawStatusCode(),e.getMessage(),exceptionDataMap);
		}
		catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_POSTS, true);
		}
		return response;
	}

	@Override
	public FacebookBaseResponse hideContent(String objectId, String accessToken, boolean isHidden, String... parity) {
		FacebookBaseResponse response = null;
		String url = StringUtils.format(FacebookApis.PAGE, objectId);

		MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();

		parametersMap.add(IS_HIDDEN, Boolean.toString(isHidden));
		parametersMap.add(ACCESS_TOKEN, accessToken);
//		if(Objects.nonNull(parity)) {
//			parametersMap.add(PARITY, parity);
//		}

		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(parametersMap).build().toUriString();

		LOGGER.info("Received request for API hide/unhide object URL {} and parameters {}", url, parametersMap);
		try {
			response = socialRestTemplate.postForObject(new URI(url), null, FacebookBaseResponse.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		}catch(HttpStatusCodeException e){
			LOGGER.error("HttpStatusCodeException while calling hide/unhide object API for URL {} :: {}", url, e.getResponseBodyAsString());
			Map<String, Object> exceptionDataMap = new HashMap<String, Object>();
			if(Constants.FACEBOOK_ERROR_WITHOUT_BODY.contains(e.getResponseBodyAsString())) {
				exceptionDataMap.put("http_response", e.getRawStatusCode());
				exceptionDataMap.put("error_message", e.getResponseBodyAsString());
				throw new BirdeyeSocialException(e.getRawStatusCode(), e.getResponseBodyAsString(), exceptionDataMap);
			}
			FacebookPostResponse facebookPostResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
			FacebookErrorResponse errorResponse = facebookPostResponse != null ? facebookPostResponse.getError() : new FacebookErrorResponse();
			exceptionDataMap = getExceptionDataMap(errorResponse);
			LOGGER.info("error map:{}",exceptionDataMap);
			throw new BirdeyeSocialException(e.getRawStatusCode(),e.getMessage(),exceptionDataMap);
		}
		catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_POSTS, true);
		}
		return response;
	}

	@Override
	public FacebookBaseResponse blockUser(String userId, String pageId, String accessToken) {
		FacebookBaseResponse baseResponse = new FacebookBaseResponse();
		ResponseEntity<Map> response = null;
		String url = StringUtils.format(FacebookApis.FB_PAGE_USER_BLOCKED, pageId);

		MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
		if(StringUtils.isNotEmpty(userId)) {
			parametersMap.add(ASID, Collections.singletonList(userId).toString());
		}
		parametersMap.add(ACCESS_TOKEN, accessToken);



		LOGGER.info("Received request for API block user API URL {} and parameters {}", url, parametersMap);
		try {
			response = socialRestTemplate.postForEntity(new URI(url), parametersMap, Map.class);
			if (response != null && response.getStatusCode().is2xxSuccessful()) {
				baseResponse.setSuccess(true);
				return baseResponse;
			}
		}catch(HttpStatusCodeException e){
			LOGGER.error("HttpStatusCodeException while calling block user API for URL {} :: {}", url, e.getResponseBodyAsString());
			Map<String, Object> exceptionDataMap = new HashMap<String, Object>();
			if(Constants.FACEBOOK_ERROR_WITHOUT_BODY.contains(e.getResponseBodyAsString())) {
				exceptionDataMap.put("http_response", e.getRawStatusCode());
				exceptionDataMap.put("error_message", e.getResponseBodyAsString());
				throw new BirdeyeSocialException(e.getRawStatusCode(), e.getResponseBodyAsString(), exceptionDataMap);
			}
			FacebookPostResponse facebookPostResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
			FacebookErrorResponse errorResponse = facebookPostResponse != null ? facebookPostResponse.getError() : new FacebookErrorResponse();
			exceptionDataMap = getExceptionDataMap(errorResponse);
			LOGGER.info("error map:{}",exceptionDataMap);
			throw new BirdeyeSocialException(errorResponse.getCode(),errorResponse.getMessage(),exceptionDataMap);
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_POSTS, true);
			baseResponse.setSuccess(false);
		}
		return baseResponse;
	}

	@Override
	public void unblockUser(String userId,String pageId, String accessToken) {
		String url = StringUtils.format(FacebookApis.FB_PAGE_USER_BLOCKED, pageId);

		LinkedMultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
		parametersMap.add(ACCESS_TOKEN, accessToken);
		parametersMap.add(PSID, userId);

//		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(parametersMap).build().encode().toUriString();

		org.springframework.http.HttpEntity<LinkedMultiValueMap<String, String>> requestEntity
				= new org.springframework.http.HttpEntity<>(parametersMap, null);


		LOGGER.info("Received request for API unblock User URL {} and parameters {}", url, parametersMap);
		try {
			socialRestTemplate.exchange(url, HttpMethod.DELETE, requestEntity, Map.class);
		}  catch (Exception e) {
			handleError(url, e,ErrorCodes.UNSTALL_BIRDEYE_APP_TAB_ON_FACEBOOK_PAGE, false);
		}
	}

	@Override
	public FbComments getPostComments(String objectId, String accessToken, String nextPageToken, Boolean getAll){
		FbComments response = null;
		List<Comment> comments = new ArrayList<>();
		String url = StringUtils.format(FacebookApis.COMMENTS,objectId);

		MultiValueMap<String, String> parametersMap = getFacebookMyPostCommentsParameters(accessToken, nextPageToken, getAll);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(parametersMap).build().encode().toUriString();
		try {
			LOGGER.info("Starting to fetch comments for API getFacebookHomeFeed URL {} with parameters {}", url, parametersMap);

			FbComments fbResponse;
			do {
				LOGGER.debug("Fetching comments from URL: {}", url);
				// Fetch the comments for the current page
				fbResponse = socialRestTemplate.getForObject(new URI(url), FbComments.class);
				if (fbResponse != null && fbResponse.getData() != null) {
					comments.addAll(fbResponse.getData());
				}
				// Check for the next page
				url = (fbResponse != null && fbResponse.getPaging() != null) ? fbResponse.getPaging().getNext() : null;
			} while (url != null && getAll);

			if (!getAll && comments.isEmpty()) {
				LOGGER.warn("No non-reply comments found in the first 20 comments.");
			}
			else if (!getAll) {
				// Filter non-reply comments (those without a parent field)
				List<Comment> nonReplyComments = comments.stream()
						.filter(comment -> comment.getParent() == null)
						.limit(5) // Limit to 5 non-reply comments
						.collect(Collectors.toList());
				comments = nonReplyComments;
			}

			response = new FbComments();
			response.setData(comments);
			LOGGER.info("Comments successfully fetched for the post {}. Total count: {}", objectId, comments.size());
		}
		catch(HttpStatusCodeException e){
			LOGGER.error("HttpStatusCodeException while calling facebook feed API for URL {} :: {}", url, e.getResponseBodyAsString());
			FacebookPostResponse facebookPostResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
			FacebookErrorResponse errorResponse = facebookPostResponse != null ? facebookPostResponse.getError() : new FacebookErrorResponse();
			Map<String, Object> exceptionDataMap = getExceptionDataMap(errorResponse);
			LOGGER.info("error map:{}",exceptionDataMap);
			throw new BirdeyeSocialException(e.getRawStatusCode(),e.getMessage(),exceptionDataMap);
		}
		catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_POSTS, true);
		}
		return response;
	}

	@Override
	public Comment getCommentById(String commentId, String accessToken) {
		Comment response = null;
		String url = StringUtils.format(FacebookApis.POST,commentId);

		MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
		parametersMap.set(FIELDS,"can_reply_privately,attachment,message_tags");
		parametersMap.set(ACCESS_TOKEN,accessToken);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(parametersMap).build().encode().toUriString();

		LOGGER.info("Received request for API getFacebookHomeFeed URL {} and parameters {}", url, parametersMap);
		try {
			response = socialRestTemplate.getForObject(new URI(url), Comment.class);
			if (response != null) {
				return response;
			}
		}
		catch(HttpStatusCodeException e){
			LOGGER.error("HttpStatusCodeException while calling facebook comment API for URL {} :: {}", url, e.getResponseBodyAsString());
			FacebookPostResponse facebookPostResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
			FacebookErrorResponse errorResponse = facebookPostResponse != null ? facebookPostResponse.getError() : new FacebookErrorResponse();
			Map<String, Object> exceptionDataMap = getExceptionDataMap(errorResponse);
			if(checkIfAcknowledgeException(facebookPostResponse)) {
				throw new BirdeyeSocialException(ErrorCodes.FB_ACKNOWLEDGE_ERROR, e.getMessage(), exceptionDataMap);
			}
			LOGGER.info("error map:{}",exceptionDataMap);
			throw new BirdeyeSocialException(e.getRawStatusCode(),e.getMessage(),exceptionDataMap);
		}
		catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_POSTS, true);
		}
		return null;
	}

	private MultiValueMap<String, String> getFacebookMyPostDateParameters(String accessToken,String since,
																		  String until) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		String pageLimit = CacheManager.getInstance()
				.getCache(SystemPropertiesCache.class)
				.getSocialFbPagesRetryLimit();
		map.add(ACCESS_TOKEN, accessToken);
		map.add(FIELDS,
				FB_QUERY_FIELDS);
		map.add(LIMIT, pageLimit);
		map.add(SINCE, since);
		map.add(UNTIL, until);

		return map;
	}

	@Override
	public FbPublicProfileInfo getPublicProfileInfo(String accessToken, String pageId) {
		FbPublicProfileInfo response = null;

		MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
		queryParams.add(ACCESS_TOKEN, accessToken);
		queryParams.add(FIELDS, "id,name,link,about,bio,category,cover,description,emails,followers_count,location,single_line_address,hours,username,website,picture,verification_status,is_always_open");
		String url = UriComponentsBuilder.fromHttpUrl(StringUtils.format(FacebookApis.PAGE, pageId))
				.queryParams(queryParams)
				.build()
				.encode()
				.toUriString();

		LOGGER.info("Received request for API getPublicProfileInfo URL: {}", url);
		try {
			response = socialRestTemplate.getForObject(url, FbPublicProfileInfo.class);
			LOGGER.info("Received response with data for API getPublicProfileInfo: {}", response);
			return response;
		} catch (HttpStatusCodeException e) {
			LOGGER.info("HTTP exception with PPCA token: {}", e.getResponseBodyAsString());
			throw new BirdeyeSocialException(ErrorCodes.FB_PUBLIC_PROFILE_ERROR, e.getResponseBodyAsString());
		} catch (Exception e) {
			LOGGER.info("Exception: {}", e.getMessage());
			throw new BirdeyeSocialException(ErrorCodes.FB_PUBLIC_PROFILE_ERROR, e.getMessage());
		}
	}

	@Override
	public FbProfileInfo getProfileInfoListing(String accessToken, String pageId) {
		FbProfileInfo response = null;

		MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
		queryParams.add(ACCESS_TOKEN, accessToken);
		queryParams.add(FIELDS, "id,name,link,about,bio,category,cover,description,emails,followers_count,location,single_line_address,hours,username,website,picture,verification_status,phone,is_always_open");
		String url = UriComponentsBuilder.fromHttpUrl(StringUtils.format(FacebookApis.PAGE, pageId))
				.queryParams(queryParams)
				.build()
				.encode()
				.toUriString();

		LOGGER.info("Received request for API getProfileInfoListing URL: {}", url);
		try {
			response = socialRestTemplate.getForObject(url, FbProfileInfo.class);
			LOGGER.info("Received response with data for API getProfileInfoListing: {}", response);
			return response;
		} catch (HttpStatusCodeException e) {
			LOGGER.info("HTTP exception with user token: {}", e.getResponseBodyAsString());
			throw new BirdeyeSocialException(ErrorCodes.FB_PUBLIC_PROFILE_ERROR, e.getResponseBodyAsString());
		} catch (Exception e) {
			LOGGER.info("Exception: {}", e.getMessage());
			throw new BirdeyeSocialException(ErrorCodes.FB_PUBLIC_PROFILE_ERROR, e.getMessage());
		}
	}

	@Override
	public Boolean getCommentExistById(String commentId, String accessToken) {
		Comment response = null;
		String url = StringUtils.format(FacebookApis.POST,commentId);

		MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
		parametersMap.set(ACCESS_TOKEN,accessToken);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(parametersMap).build().encode().toUriString();

		LOGGER.info("Received request for API getCommentExistById URL {} and parameters {}", url, parametersMap);
		try {
			response = socialRestTemplate.getForObject(new URI(url), Comment.class);
			if (response != null) {
				return false;
			}
		}
		catch(HttpStatusCodeException e){
			LOGGER.error("HttpStatusCodeException while calling facebook comment API for URL {} :: {}", url, e.getResponseBodyAsString());
			FacebookPostResponse facebookPostResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
			FacebookErrorResponse errorResponse = facebookPostResponse != null ? facebookPostResponse.getError() : new FacebookErrorResponse();
			Map<String, Object> exceptionDataMap = getExceptionDataMap(errorResponse);
			if(checkIfDeletedCommentException(facebookPostResponse,commentId)) {
				return true;
			}
			LOGGER.info("error map:{}",exceptionDataMap);
			throw new BirdeyeSocialException(e.getRawStatusCode(),e.getMessage(),exceptionDataMap);
		}
		catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_POSTS, true);
		}
		return null;
	}

	private MultiValueMap<String, String> getFacebookCompetitorPostParameters(String accessToken, String nextToken) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		map.add(ACCESS_TOKEN, accessToken);

		map.add(FIELDS, FB_COMPETITOR_QUERY_FIELDS);
		map.add(LIMIT, "100");
		map.add(AFTER, nextToken);

		return map;
	}

	@Override
	public FacebookFeedDataV2 getFacebookCompetitorFeed(String pageId, String accessToken, String nextToken) throws Exception {
		FacebookFeedDataV2 response = null;
		String url = StringUtils.format(FacebookApis.FEED,pageId);

		MultiValueMap<String, String> parametersMap = getFacebookCompetitorPostParameters(accessToken, nextToken);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(parametersMap).build().encode().toUriString();

		LOGGER.info("Received request for API getFacebookCompetitorFeed URL {} and parameters {}", url, parametersMap);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FacebookFeedDataV2.class);
		}
		catch(HttpStatusCodeException e){
			LOGGER.info("HttpStatusCodeException while calling facebook feed API with PPCA token for URL {} :: {}", url, e.getResponseBodyAsString());
			throw new BirdeyeSocialException(e.getRawStatusCode(),e.getResponseBodyAsString());
		}
		catch (Exception e) {
			LOGGER.info("Exception while calling facebook feed API for URL {}", url, e);
			throw new BirdeyeSocialException(e.getMessage());
		}
		return response;
	}

	@Override
	public FacebookFeedV2 getFacebookFeedEngagement(String postId, String accessToken) throws Exception {
		FacebookFeedV2 response = null;
		String url = StringUtils.format(FacebookApis.POST, postId);

		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		map.add(ACCESS_TOKEN, accessToken);
		map.add(FIELDS, POST_ENGAGEMENT_METRIC_COUNT);

		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(map).build().encode().toUriString();

		LOGGER.info("Received request for API getFacebookCompetitorFeed URL {} and parameters {}", url, map);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FacebookFeedV2.class);
		}
		catch(HttpStatusCodeException e){
			LOGGER.info("HttpStatusCodeException while calling facebook feed API for URL {} :: {}", url, e.getResponseBodyAsString());
			throw new BirdeyeSocialException(e.getRawStatusCode(),e.getResponseBodyAsString());
		}
		catch (Exception e) {
			LOGGER.info("Exception while calling facebook feed API for URL {}", url, e);
			throw new BirdeyeSocialException(e.getMessage());
		}
		return response;
	}


	@Override
	public List<Map<String, Object>> getBatchFbProfileInfoImageData(List<String> pageId, String accessToken) {

		MultiValueMap<String,String> queryParams = new LinkedMultiValueMap<>();
		queryParams=getBatchJSONObject(pageId,accessToken,HttpMethod.GET.name(),"?fields=followers_count,single_line_address,username,location");

		String url = UriComponentsBuilder.fromHttpUrl(GRAPH_API_BASE_VERSION_DEFAULT_V21).queryParams(queryParams).build().toUriString();
		LOGGER.info("url for getBatchFbProfileInfoImageData {} with queryParameters {} ", url,queryParams);
		try {
			ResponseEntity<List<Map<String, Object>>> responseEntity = socialRestTemplate.exchange(url,
					HttpMethod.POST,null,new ParameterizedTypeReference<List<Map<String, Object>>>(){});
			return responseEntity.getBody();
		}catch (HttpStatusCodeException e){
			LOGGER.info("[Facebook Page Batch Images] HttpStatusCodeException part where http status is = {}",e.getStatusCode().value());
			LOGGER.error("HttpStatusCodeException while fetching batch profile detail of fb page id API for URL {} :: {}", url, e.getResponseBodyAsString());
			FacebookPostResponse response = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
			FacebookErrorResponse errorResponse = response != null ? response.getError() : new FacebookErrorResponse();
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_PAGE_DETAILS, e.getMessage());
		}catch (RestClientException e) {
			LOGGER.error("[Facebook Social Integration] Exception in while get batch profile of fb API: ", e);
			throw new BirdeyeSocialException(ErrorCodes.CLIENT_ERROR_400, e.getMessage());
		} catch (Exception e) {
			LOGGER.error("[Facebook Social Integration] Exception in while get batch profile of fb API: ", e);
			throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, e.getMessage());
		}
	}

	@Override

	public FbPostInsightResponse getPostInsightResponseForReels(String accessToken, String postId, List<String> metric) {
		FbPostInsightResponse response = null;
		final String url = UriComponentsBuilder.fromHttpUrl(StringUtils.format(FacebookApis.REELS_INSIGHT, postId))
				.queryParams(new LinkedMultiValueMap<String, String>(){{
					add(ACCESS_TOKEN, accessToken);
					add(METRIC, String.join(",", new HashSet<>(metric)));
				}}).build().encode().toUriString();

		LOGGER.info("Received request for API getPostInsightResponseForReels URL: {}", url);
		try {
			response = socialRestTemplate.getForObject(url, FbPostInsightResponse.class);
			LOGGER.info("Received response with data length {} for API getPostInsightResponseForReels", CollectionUtils.isNotEmpty(response.getData()) ? response.getData().size() : 0);
		} catch (HttpStatusCodeException clientExp) {
			FacebookBaseResponse facebookBaseResponse = JSONUtils.fromJSON(clientExp.getResponseBodyAsString(), FacebookBaseResponse.class);
			boolean ackError = checkIfAcknowledgeException(facebookBaseResponse);
			if(ackError) {
				LOGGER.warn("getPostInsightResponseForReels acknowledged HttpStatusCodeException occurred: {}", clientExp.getResponseBodyAsString());
				throw new BirdeyeSocialException(ErrorCodes.FB_ACKNOWLEDGE_ERROR, clientExp.getMessage());
			}
			LOGGER.error("getPostInsightResponseForReels HttpStatusCodeException occurred: {}", clientExp.getResponseBodyAsString());
			if(Objects.nonNull(facebookBaseResponse) && facebookBaseResponse.getError().getCode() == 100
					&& facebookBaseResponse.getError().getError_subcode() == 33 ){
				throw new HttpStatusCodeException(clientExp.getStatusCode(), ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS.name()) {
				};
			}
			if(Objects.nonNull(facebookBaseResponse) && Objects.nonNull(facebookBaseResponse.getError())
					&& facebookBaseResponse.getError().getCode() == 1
					&& facebookBaseResponse.getError().getError_subcode() == 99 ){
				throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS, clientExp.getResponseBodyAsString());
			}
			handleError(url, clientExp, ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS, true);
		} catch (Exception e) {
			LOGGER.error("getFbInsights exception occurred", e);
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS, true);
		}
		return response;
	}

	@Override
	public FacebookFeedData getFacebookReels(FacebookPageAccessInfo creds) {
		FacebookFeedData response = null;
		String url = StringUtils.format(FacebookApis.REELS,creds.getPageId());

		MultiValueMap<String, String> parametersMap = getFacebookReelsParameter(creds);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(parametersMap).build().encode().toUriString();

		LOGGER.info("Received request for API getFacebook reels, URL {} and parameters {}", url, parametersMap);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FacebookFeedData.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_POSTS, true);
		}
		return response;
	}

	public String uploadPhotoStory(FacebookPageAccessInfo creds, String mediaUrl) throws Exception {
		String url = StringUtils.format(FacebookApis.PAGE_PHOTOS, creds.getPageId());
		MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
		parametersMap.add("published", "false");
		parametersMap.add("url", mediaUrl);
		parametersMap.add(ACCESS_TOKEN, creds.getAccessToken());
		FacebookPostResponse response = null;
		LOGGER.info("Received request to upload photo story  URL {} and parameters {}", url, parametersMap);

		try {
			response = socialRestTemplate.postForObject(url, parametersMap, FacebookPostResponse.class);
			LOGGER.info("Response from fb for upload photo story req: {}", response);
			return response.getId();
		} catch (HttpStatusCodeException e) {
			handleClientError(e);
		} catch (ResourceAccessException e) {
			handleError(url, e, ErrorCodes.FB_SOCKET_TIME_OUT, true);
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_POST_ACTIVITY_ON_FACEBOOK, true);
		}
		return null;
	}


	@Override
	public FbMediaPublishResponse publishPhotoStory(FacebookPageAccessInfo creds, String mediaId) throws Exception {
		String url = StringUtils.format(FacebookApis.PHOTOS_STORY_PUBLISH, creds.getPageId());
		MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
		params.add(ACCESS_TOKEN, creds.getAccessToken());
		params.add("photo_id", mediaId);
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();
		LOGGER.info("Received request to publish photo story  URL {}", url);

		try {
			FbMediaPublishResponse mediaPublishResponse = socialRestTemplate.postForObject(url, params, FbMediaPublishResponse.class);
			LOGGER.info("Response from fb for publish photo story req: {}", mediaPublishResponse);
			return mediaPublishResponse;
		} catch (HttpStatusCodeException e) {
			handleClientError(e);
		} catch (ResourceAccessException e) {
			handleError(url, e, ErrorCodes.FB_SOCKET_TIME_OUT, true);
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_POST_ACTIVITY_ON_FACEBOOK, true);
		}
		return null;
	}

	@Override
	public String initiateSession(FacebookPageAccessInfo creds, boolean isReel) throws Exception {

		String url = new StringBuilder(FacebookApis.GRAPH_API_BASE_VERSION_DEFAULT_V21)
				.append(creds.getPageId())
				.append(isReel ? "/video_reels" : "/video_stories")
				.toString();
		MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
		parametersMap.add("upload_phase", "start");
		parametersMap.add(ACCESS_TOKEN, creds.getAccessToken());
		LOGGER.info("Received request to initiate session  URL {} and parameters {}", url, parametersMap);

		try {
			FbInitiateSessionResponse initiateSessionResponse = socialRestTemplate.postForObject(url, parametersMap, FbInitiateSessionResponse.class);
			LOGGER.info("Response from fb for initiate session req: {}", initiateSessionResponse);
			return initiateSessionResponse.getVideo_id();
		} catch (HttpStatusCodeException e) {
			handleClientError(e);
		} catch (ResourceAccessException e) {
			handleError(url, e, ErrorCodes.FB_SOCKET_TIME_OUT, true);
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_POST_ACTIVITY_ON_FACEBOOK, true);
		}
		return null;
	}

	@Override
	public FbMediaPublishResponse uploadVideo(FacebookPageAccessInfo creds, String videoId, String mediaUrl) throws Exception {
		String url = StringUtils.format(FacebookApis.FB_UPLOAD_API, videoId);
		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", "OAuth "+creds.getAccessToken());
		headers.set("file_url", mediaUrl);
		HttpEntity<String> requestEntity = new HttpEntity<>(headers);
		LOGGER.info("Received request to upload video  URL {} and headers {}", url, headers);

		try {
			ResponseEntity<FbMediaPublishResponse> response = socialRestTemplate.exchange(url, HttpMethod.POST, requestEntity, FbMediaPublishResponse.class);
			LOGGER.info("Response from fb for upload video req: {}", response.getBody());
			return response.getBody();
		} catch (HttpStatusCodeException e) {
			handleClientError(e);
		} catch (ResourceAccessException e) {
			handleError(url, e, ErrorCodes.FB_SOCKET_TIME_OUT, true);
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_POST_ACTIVITY_ON_FACEBOOK, true);
		}
		return null;
	}

	@Override
	public FbUploadStatusResponse checkMediaStatus(FacebookPageAccessInfo creds, String mediaId) throws Exception {
		String url = new StringBuilder(FacebookApis.GRAPH_API_BASE_VERSION_DEFAULT_V21)
				.append(mediaId)
				.toString();
		MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
		params.add(ACCESS_TOKEN, creds.getAccessToken());
		params.add("fields", "status");
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();
		LOGGER.info("Received request to check media status  URL {}", url);

		try {
			FbUploadStatusResponse response = socialRestTemplate.getForObject(url, FbUploadStatusResponse.class);
			LOGGER.info("Response from fb for check media status req: {}", response);
			return response;
		} catch (HttpStatusCodeException e) {
			handleClientError(e);
		} catch (ResourceAccessException e) {
			handleError(url, e, ErrorCodes.FB_SOCKET_TIME_OUT, true);
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_POST_ACTIVITY_ON_FACEBOOK, true);
		}
		return null;
	}

	@Override
	public FbMediaPublishResponse publishMedia(FacebookPageAccessInfo creds, String mediaId, String text, boolean isReel) throws Exception {
		String url = new StringBuilder(FacebookApis.GRAPH_API_BASE_VERSION_DEFAULT_V21)
				.append(creds.getPageId())
				.append(isReel ? "/video_reels" : "/video_stories")
				.toString();

		MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
		params.add(ACCESS_TOKEN, creds.getAccessToken());
		params.add("video_id", mediaId);
		params.add("upload_phase", "finish");
		if(isReel) {
			params.add("video_state", "PUBLISHED");
			params.add("description", text);
		}
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();
		LOGGER.info("Received request to publish media  URL {}", url);

		try {
			FbMediaPublishResponse mediaPublishResponse = socialRestTemplate.postForObject(url, params, FbMediaPublishResponse.class);
			LOGGER.info("Response from fb for publish media req: {}", mediaPublishResponse);
			return mediaPublishResponse;
		}catch (HttpStatusCodeException e) {
			handleClientError(e);
		} catch (ResourceAccessException e) {
			handleError(url, e, ErrorCodes.FB_SOCKET_TIME_OUT, true);
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_POST_ACTIVITY_ON_FACEBOOK, true);
		}
		return null;
	}

	@Override
	public FbMediaPublishResponse uploadThumbnail(FacebookPageAccessInfo creds, String mediaId, File thumbnailUrl) throws Exception {
		String url = StringUtils.format(FacebookApis.UPLOAD_THUMBNAIL, mediaId);
		MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
		params.add(ACCESS_TOKEN, creds.getAccessToken());
		params.add("is_preferred", "true");
		params.add("source", new FileSystemResource(thumbnailUrl));
		LOGGER.info("Received request to upload thumbnail  URL {}", url);

		try {
			FbMediaPublishResponse response = socialRestTemplate.postForObject(url, params, FbMediaPublishResponse.class);
			LOGGER.info("Response from fb for upload thumbnail req: {}", response);
			return response;
		}catch (HttpStatusCodeException e) {
			handleClientError(e);
		} catch (ResourceAccessException e) {
			handleError(url, e, ErrorCodes.FB_SOCKET_TIME_OUT, true);
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_POST_ACTIVITY_ON_FACEBOOK, true);
		}
		return null;
	}


	private MultiValueMap<String, String> getFacebookReelsParameter(FacebookPageAccessInfo creds) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		map.add(ACCESS_TOKEN, creds.getAccessToken());
		map.add(FIELDS, FB_REELS_FIELDS);
		return map;
	}

	@Override
	public FacebookFeedData getFacebookReelsByPaginatedUrl(String nextUrl) {
		FacebookFeedData response = null;

		LOGGER.info("Received request for API getFacebookPostFeedPaginatedByUrlome URL {} and parameters {}", nextUrl, null);
		try {
			response = socialRestTemplate.getForObject(new URI(nextUrl), FacebookFeedData.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		}catch (Exception e) {
			handleError(nextUrl, e, ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_POSTS, true);
		}
		return response;
	}

	@Override
	public FacebookFeedData getFacebookStories(FacebookPageAccessInfo creds) {
		FacebookFeedData response = null;
		String url = StringUtils.format(FacebookApis.STORIES,creds.getPageId());

		MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
		parametersMap.add(ACCESS_TOKEN,creds.getAccessToken());
		parametersMap.add(FIELDS, "post_id,creation_time,media_type,url,media_id");
		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(parametersMap).build().encode().toUriString();
		LOGGER.info("Received request for API getFacebook stories, URL {} and parameters {}", url, parametersMap);
		try {
			response = socialRestTemplate.getForObject(new URI(url), FacebookFeedData.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_POSTS, true);
		}
		return response;
	}

	public FbStoryDetailsResponse fetchFbStoryData(String mediaId, FacebookPageAccessInfo creds) {
		FbStoryDetailsResponse response = null;

		String url = String.format(FacebookApis.FB_REEL_AND_STORY_MEDIA_DATA, mediaId);
		MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
		params.add(ACCESS_TOKEN, creds.getAccessToken());
		params.add(FIELDS, "source,from");

		url = UriComponentsBuilder.fromHttpUrl(url).queryParams(params).build().encode().toUriString();

		try {
			response = socialRestTemplate.getForObject(new URI(url), FbStoryDetailsResponse.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		} catch (Exception e) {
			handleError(url, e, ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_POSTS, true);
		}
		return response;
	}

	@Override
	public FacebookFeedData getFacebookStoriesByPaginatedUrl(String nextUrl) {
		FacebookFeedData response = null;

		LOGGER.info("Received request for API getFacebookPostFeedPaginatedByUrlome URL {} and parameters {}", nextUrl, null);
		try {
			response = socialRestTemplate.getForObject(new URI(nextUrl), FacebookFeedData.class);
			if (response != null && response.getError() == null) {
				return response;
			}
		}catch (Exception e) {
			handleError(nextUrl, e, ErrorCodes.UNABLE_TO_FETCH_FACEBOOK_POSTS, true);
		}
		return response;
	}

}
