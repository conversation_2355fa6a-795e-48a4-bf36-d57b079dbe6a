package com.birdeye.social.controller;

import com.birdeye.social.sro.socialenterprise.SocialEnterpriseBulkImportDTO;
import com.birdeye.social.sro.socialenterprise.SocialEnterpriseDoupRequestDTO;
import com.birdeye.social.sro.socialenterprise.SocialEnterpriseReportUploadDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.birdeye.social.enterprise.setup.SocialEnterpriseSetupService;
import com.birdeye.social.model.SocialAddPagesResponse;

import javax.validation.Valid;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/social/enterprise")
public class SocialEnterpriseSetupController {

	@Autowired
	SocialEnterpriseSetupService enterpriseSetupService;

	private static final Logger logger = LoggerFactory.getLogger(SocialEnterpriseSetupController.class);

	@GetMapping(value = "/get/{channel}/pages")
	public ResponseEntity<SocialAddPagesResponse> getAllPagesForChannel(
			@RequestHeader("X-Bazaarify-Session-Token") String sessionToken, @PathVariable("channel") String channel,
			@RequestParam("tempToken") String tempToken) {
		SocialAddPagesResponse addPagesResponse = enterpriseSetupService.getChannelWisePages(sessionToken, channel,
				tempToken);
		return new ResponseEntity<SocialAddPagesResponse>(addPagesResponse, HttpStatus.OK);
	}

	/**
	 * This API is specific for Enterprise
	 * API is used to send all unmapped locations for the enterprise to DOUP service
	 * Uses: This API is getting consumed from DOUP on action of import mapping download spreadsheet and also on
	 * download action of unmapped locations on integration screen
	 *
     */
	@PostMapping(value = "/doup/location/responses")
	public @ResponseBody ResponseEntity<SocialEnterpriseReportUploadDTO> processLocationEnterprisePage(@RequestHeader(value = "user-id", required = false) Integer userId, @RequestParam("start-index") Integer page, @Valid @RequestParam("page-size") Integer size, @RequestBody SocialEnterpriseDoupRequestDTO data) {
		logger.info("Fetch Location for Enterprises with Request Data object {} with Size {} Page Index {} and user id {}", data, size, page, userId);
		return new ResponseEntity<>(enterpriseSetupService.processLocationMappingIntegrationReport(data, size, page, userId), HttpStatus.OK);
	}

	/**
	 * This API is specific for Enterprise
	 * API is used to send all unmapped pages for channels to DOUP service where businessId is not present.
	 * THIS IS USED FOR ADDING INFO IN SPREADSHEET FOR BULK MAPPING
	 * Uses: This API is getting consumed from DOUP on action of import mapping download spreadsheet
	 *
	 */
	@PostMapping(value = "/doup/responses")
	public @ResponseBody ResponseEntity<SocialEnterpriseReportUploadDTO> processEnterprisePage(@RequestParam("start-index") Integer page, @RequestParam("page-size") Integer size, @RequestBody SocialEnterpriseDoupRequestDTO data) {
		logger.info("Fetch Pages List for Enterprises with Request Data object {}", data);
		return new ResponseEntity<>(enterpriseSetupService.processMappingIntegrationReport(data, size, page), HttpStatus.OK);
	}

	/**
	 * API is used to consume nifi event from DOUP service to fetch each channel row data to map with enterprise location
	 * Uses: This API is getting consumed from DOUP on action of import mapping upload spreadsheet
	 *
	 */
	@PostMapping(value = "/doup/consume/records")
	public ResponseEntity<?> processEnterprisePage(@RequestBody SocialEnterpriseBulkImportDTO data, @RequestParam("channel") String channel) {
		logger.info("Consume Records for Enterprises with Request Data object {}", data);
		enterpriseSetupService.processEnterprisePage(data, channel);
		return new ResponseEntity<>(HttpStatus.OK);
	}
}
