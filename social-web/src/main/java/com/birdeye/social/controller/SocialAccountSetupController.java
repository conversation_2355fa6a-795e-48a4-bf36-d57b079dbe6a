package com.birdeye.social.controller;


import com.birdeye.social.businessCore.BusinessStatusData;
import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.SocialSetupAuditEnum;
import com.birdeye.social.dto.*;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.InboxStatus;
import com.birdeye.social.entities.LocationMovementDto;
import com.birdeye.social.entities.SocialSetupAudit;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.linkedin.LinkedinService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.instagram.TokenUpdateRequest;
import com.birdeye.social.model.tiktok.arbor.TiktokAuthUrlResponse;
import com.birdeye.social.model.usage.InboxStatusResponseComplete;
import com.birdeye.social.platform.messages.OAuthRequestMessage;
import com.birdeye.social.service.*;
import com.birdeye.social.sro.*;
import com.birdeye.social.sro.socialReseller.SocialBusinessStatusRequest;
import com.birdeye.social.sro.socialReseller.SocialBusinessStatusResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;


@RestController
	@RequestMapping("/social/account")
public class SocialAccountSetupController {

    private static final Logger LOG = LoggerFactory.getLogger(SocialAccountSetupController.class);

    private static final String INPUT_CHANNEL_CAN_NOT_BE_NULL = "Input/Channel can not be null";

    @Autowired
    private SocialAccountService socialAccountService;

    @Autowired
    private GoogleSocialAccountService googleSocialAccountService;

    @Autowired
    private SocialPostLinkedinService socialPostLinkedInService;

    @Autowired
    private LinkedinService linkedinService;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private GMBLocationDetailService gmbLocationDetailService;

    @Autowired
    private AutoMappingService autoMappingService;

    /**
     * API to get all connected pages
     *
     * @param channel
     * @param businessId
     * @param startIndex
     * @param endIndex
     * @param userId
     * @return
     * @deprecated : new UI designs do not how location information. replaced by API GET /{channel}/pages
     */
    // Listing Reports support
    @GetMapping(value = "/{channel}/all")
    public @ResponseBody ResponseEntity<ConnectedPages> getAllPages(@PathVariable("channel") String channel,
                                                                    @RequestParam("businessId") Long businessId, @RequestParam("startIndex") Integer startIndex,
                                                                    @RequestParam("endIndex") Integer endIndex, @RequestParam("userId") Integer userId) {
        return new ResponseEntity<>(socialAccountService.getConnectedPages(channel, businessId, userId), HttpStatus.OK);

    }

    /**
     * API to get unmapped and total locations of an accountId.
     * It will be used in listing module to show banner.
     *
     * @param channel - supported values are "gmb" and  "facebook"
     * @return
     */
    @PostMapping(value = "/{channel}/status")
    public @ResponseBody ResponseEntity<ChannelStatusResponse> getChannelStatus(@PathVariable("channel") String channel,
                                                                                @RequestBody SocialFilterRequest request) {
        return new ResponseEntity<>(socialAccountService.getChannelIntegrationStatus(channel, request),
                HttpStatus.OK);
    }

    /**
     * API to check the status of the request created by a user on the business for Connect and Reconnect flow. If the request is in "init" status then user will see the message "please
     * wait, we are fetching pages". If "fetched" status for Connect Request then the list of fetched pages will be returned. In "complete" or "cancel" status then user is
     * allowed to create new request. Called from UI via platform.
     *
     * @param channel
     * @param businessId
     * @param reconnectFlag
     * @return
     * @throws Exception
     */
    @Deprecated
    @GetMapping(value = "/{channel}/integration/checkstatus")
    public @ResponseBody ResponseEntity<ChannelPageInfo> getIntegrationPageStatus(@PathVariable("channel") String channel, @RequestParam("businessId") Long businessId,
                                                                                  @RequestParam(value = "reconnect", required = false, defaultValue = "false") Boolean reconnectFlag) throws Exception {
        if (businessId != null && SocialChannel.getSocialChannelByName(channel) != null) {
            ChannelPageInfo pageInfo = socialAccountService.getIntegrationRequestInfo(channel, businessId, reconnectFlag);
            return new ResponseEntity<>(pageInfo, HttpStatus.OK);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
    }

    /**
     * API to check the status of the request created by a user on the business for Connect and Reconnect flow.
     * Called from UI via platform.
     *
     * @param channel
     * @param businessId
     * @param reconnectFlag
     * @return CheckStatusResponse
     */
    @GetMapping(value = "/{channel}/integration-status")
    public @ResponseBody ResponseEntity<CheckStatusResponse> getIntegrationStatus(@PathVariable("channel") String channel, @RequestParam("businessId") Long businessId,
                                                                                  @RequestParam(value = "reconnect", required = false, defaultValue = "false") Boolean reconnectFlag) {
        if (businessId != null && SocialChannel.getSocialChannelByName(channel) != null) {
            CheckStatusResponse pageInfo = socialAccountService.getIntegrationRequestStatus(channel, businessId, reconnectFlag);
            return new ResponseEntity<>(pageInfo, HttpStatus.OK);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
    }

    @GetMapping(value = "/{channel}/auto-mapping-status")
    public @ResponseBody ResponseEntity<AutoMappingStatusResponse> getAutoMappingStatus(@PathVariable("channel") String channel, @RequestParam("id") Long id,
                                                                                        @RequestParam(value = "type", required = false, defaultValue = "enterprise") String type) {
        if (id != null && SocialChannel.getSocialChannelByName(channel) != null) {
            AutoMappingStatusResponse response = autoMappingService.fetchAutoMappingStatus(id, channel, type);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
    }

    /**
     * API which will return fetched pages for requested channel and enterprise.
     * This is called when ui gets check status as fetched in firebase.
     *
     * @param channel
     * @param businessId
     * @return
     */

    @GetMapping(value = "/{channel}/integration-page")
    public @ResponseBody ResponseEntity<FetchPageResponse> getIntegrationPage(@PathVariable("channel") String channel, @RequestParam("businessId") Long businessId) {
        if (businessId != null && SocialChannel.getSocialChannelByName(channel) != null) {
            FetchPageResponse fetchPageResponse = socialAccountService.getIntegrationPage(channel, businessId);
            LOG.info("[getIntegrationPage] for business id {} :   {} ", businessId, fetchPageResponse);
            return new ResponseEntity<>(fetchPageResponse, HttpStatus.OK);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
    }

    /**
     * API to initiate fetch pages request. If the request for the channel on the business is already created by one user, all other users on that
     * business will see a message "please wait, we are fetching pages".
     *
     * @param channel
     * @throws Exception
     */
    @PostMapping(value = "/{channel}/pages")
    public @ResponseBody ResponseEntity<Void> initiatePageRequest(@PathVariable("channel") String channel, @RequestBody ChannelAuthRequest authRequest) throws Exception {
        if (authRequest.getBusinessId() != null && SocialChannel.getSocialChannelByName(channel) != null) {
            socialAccountService.submitGetPageRequest(channel, authRequest);
            return new ResponseEntity<>(HttpStatus.OK);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
    }

    /**
     * API to initiate fetch account for gmb request from google. If the request for the channel on the business is already created by one user, all other users on that
     * business will see a message "please wait, we are fetching accounts".
     *
     * @param
     * @throws Exception
     */
    @PostMapping(value = "/gmb-account")
    public ResponseEntity<?> gmbAccountFetch(@RequestBody ChannelAuthRequest authRequest) {
        socialAccountService.initiateGmbAccountFetch(authRequest, "Enterprise");
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/gmb/token-valid")
    public ResponseEntity<?> gmbSSOAuth(@RequestBody GoogleSSOAuthRequest ssoAuthRequest) {
        return new ResponseEntity<>(socialAccountService.googleSSOAuth(ssoAuthRequest), HttpStatus.OK);
    }

    @PostMapping(value = "/fb/token-valid")
    public ResponseEntity<?> fbSSOAuth(@RequestBody FacebookSSOAuthRequest ssoAuthRequest) {
        return new ResponseEntity<>(socialAccountService.fbSSOAuth(ssoAuthRequest), HttpStatus.OK);
    }

    @PostMapping(value = "/fb/token/user/details")
    public ResponseEntity<?> fbSSOAuthMobile(@RequestBody FacebookSSOAuthRequest ssoAuthRequest) {
        return new ResponseEntity<>(socialAccountService.fbSSOAuthUserDetails(ssoAuthRequest), HttpStatus.OK);
    }

    @PostMapping(value = "/check/whitelable")
    public ResponseEntity<?> getWhiteLabelAccounts(@RequestBody GoogleWhiteLabelAccountsRequest googleWhiteLabelAccountsRequest) {
        return new ResponseEntity<>(socialAccountService.getWhiteLabelAccounts(googleWhiteLabelAccountsRequest), HttpStatus.OK);
    }

    @PostMapping(value = "/add/whitelabel")
    public ResponseEntity<?> addWhitelabelEntries(@RequestBody List<String> urls) {
        socialAccountService.addWhiteLabelAccounts(urls);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Api to initiate page fetch by account. Account details passed from UI
     *
     * @param gmbAccountDTO
     * @param businessId
     * @return
     */

    @PostMapping(value = "/gmb-account-page")
    public ResponseEntity<?> gmbPageFetchByAccount(@RequestBody GMBAccountDTO gmbAccountDTO, @RequestParam Long businessId) {
        socialAccountService.gmbPageFetchByAccount(gmbAccountDTO, businessId, "Enterprise");
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to fetch google fetched account from database.
     *
     * @param businessId
     * @return
     */

    @GetMapping(value = "/gmb-account")
    public ResponseEntity<?> gmbAccount(@RequestParam Long businessId) {
        return new ResponseEntity<>(socialAccountService.getGmbAccount(businessId, "Enterprise"), HttpStatus.OK);
    }

    /**
     * APi to refresh user gmb accounts. If new account found then that will be added to existing account and also existing account details will get updated too.
     *
     * @param userEmail
     * @param businessId
     * @return
     */

    @PostMapping(value = "/gmb-refresh-account")
    public ResponseEntity<?> refreshGmbUserAccount(@RequestParam("user") String userEmail, @RequestParam("businessId") Long businessId) {
        socialAccountService.refreshGmbUserAccount(userEmail, businessId, Constants.ENTERPRISE);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Api to reset gmb business get page request status from fetched to account_fetched
     *
     * @param businessId
     * @return
     */
    @PostMapping(value = "/gmb-change-status")
    public ResponseEntity<?> gmbBackStatus(@RequestParam("businessId") Long businessId) {
        socialAccountService.gmbBackStatus(businessId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to cancel the fetch page request. The option to cancel the request will be available on the screen when all the pages are fetched and shown on
     * the screen.
     *
     * @param channel
     * @param businessId
     * @throws Exception
     */
    @PostMapping(value = "/{channel}/request/cancel")
    public @ResponseBody ResponseEntity<?> cancelRequest(@PathVariable("channel") String channel, @RequestParam("businessId") Long businessId,
                                                         @RequestParam(value = "forceCancel", required = false, defaultValue = "false")
                                                         Boolean forceCancel) throws Exception {
        socialAccountService.cancelRequest(channel, businessId, forceCancel);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to connect the fetched pages to the enterprise. Called from UI while saving new fetched pages.
     *
     * @param channel
     * @param enterpriseId
     * @param inputs       -> list of pages to be connected to the business
     * @return ChannelPageInfo -> list of saved pages
     * @throws Exception
     */

    @PutMapping(value = "/{channel}/connectpage")
    public @ResponseBody ResponseEntity<ChannelPageInfo> connectPagesV1(@PathVariable("channel") String channel, @RequestParam("businessId") Long enterpriseId,
                                                                        @RequestBody Map<String, List<String>> inputs, @RequestHeader("account-id") Integer accountId) throws Exception {
        if (inputs != null && !inputs.isEmpty() && enterpriseId != null && SocialChannel.getSocialChannelByName(channel) != null) {
            return new ResponseEntity<>(socialAccountService.connectPagesV1(channel, enterpriseId, inputs, accountId), HttpStatus.OK);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
    }

    /**
     * API to get twitter authenticationURL
     *
     * @param businessId
     * @return
     * @throws Exception
     */
    @GetMapping(value = "twitter/authurl")
    public @ResponseBody ResponseEntity<OAuthRequestMessage> getTwitterAuthUrl(@RequestParam("businessId") Long businessId,
                                                                               @RequestParam("origin") String origin) throws Exception {
        if (businessId != null) {
            OAuthRequestMessage authResponse = socialAccountService.generateTwitterAuthenticationURL(businessId, origin);
            return new ResponseEntity<>(authResponse, HttpStatus.OK);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Business Id cannot be null");
        }
    }

    /**
     * API to reconnect invalid pages for Social channel. Called from UI
     *
     * @param channel
     * @param businessId
     * @param input
     * @param userId
     * @return
     * @throws Exception
     */
    @PutMapping(value = "{channel}/reconnect/all")
    public @ResponseBody ResponseEntity<Void> reconnectAllPage(@PathVariable("channel") String channel, @RequestParam("businessId") Long businessId,
                                                               @RequestBody ChannelAllPageReconnectRequest input, @RequestParam("userId") Integer userId) throws Exception {
        if (input != null && SocialChannel.getSocialChannelByName(channel) != null) {
            socialAccountService.reconnectAllPage(channel, input, businessId, userId, Constants.ENTERPRISE);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to get Summary for last integration request for Social channel. Called from UI.
     *
     * @param channel
     * @param businessId
     * @param userId
     * @return
     * @throws Exception
     */
    @GetMapping(value = "/{channel}/integration/summary")
    public @ResponseBody ResponseEntity<LastReconnectDetails> getIntegrationSummary(@PathVariable("channel") String channel, @RequestParam("businessId") Long businessId,
                                                                                    @RequestParam(value = "reconnect", required = false, defaultValue = "false") Boolean reconnectFlag, @RequestParam("userId") Integer userId) throws Exception {
        if (businessId != null && SocialChannel.getSocialChannelByName(channel) != null) {
            LastReconnectDetails summary = socialAccountService.getIntegrationSummary(channel, businessId, reconnectFlag);
            return new ResponseEntity<>(summary, HttpStatus.OK);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
    }

    /**
     * API to remove social page. Called from UI.
     *
     * @param channel
     * @param input
     * @param businessId
     * @param userId
     * @return
     * @throws Exception
     */
    @PutMapping(value = "{channel}/remove/page")
    public @ResponseBody ResponseEntity<Void> removePage(@PathVariable("channel") String channel, @RequestBody List<LocationPageMappingRequest> input,
                                                         @RequestParam("businessId") Long businessId, @RequestParam(value = "userId", required = false) Integer userId,
                                                         @RequestHeader(value = Constants.BUSINESS_NUMBER, required = false) Long enterpriseId) throws Exception {
        if (input != null && SocialChannel.getSocialChannelByName(channel) != null) {
            socialAccountService.removePage(channel, input, businessId, userId, enterpriseId);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PutMapping(value = "/remove-page")
    public @ResponseBody ResponseEntity<Void> removePages(@RequestBody DeleteEventRequest deleteEventRequest) {
        LOG.info("Request received to delete pages :{}", deleteEventRequest);
        socialAccountService.removePagesByIds(deleteEventRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to get location-mapping pages. Called from UI.
     *
     * @param channel
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/{channel}/locations/mapping/get")
    public @ResponseBody ResponseEntity<LocationPageMapping> getLocationMappingPagesV2(@PathVariable("channel") String channel,
                                                                                       @RequestBody LocationMappingRequest request,
                                                                                       @RequestHeader(Constants.BUSINESS_ID) Integer businessId)
            throws Exception {
        return new ResponseEntity<>(socialAccountService.getLocationMappingPagesV2(channel, request, businessId), HttpStatus.OK);
    }

    /**
     * API to get location-mapping pages for reseller. Called from UI.
     *
     * @param channel
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/{channel}/locations/mapping/get/reseller")
    public @ResponseBody ResponseEntity<LocationPageMapping> getLocationMappingPagesForResellerV2(@PathVariable("channel") String channel,
                                                                                                  @RequestBody LocationMappingRequest request,
                                                                                                  @RequestHeader(Constants.BUSINESS_ID) Integer businessId)
            throws Exception {
        return new ResponseEntity<>(socialAccountService.getLocationMappingPagesV2(channel, request, businessId), HttpStatus.OK);
    }

    @Deprecated
    @GetMapping(value = "/{channel}/locations/mapping")
    public @ResponseBody ResponseEntity<LocationPageMapping> getLocationMappingPages(@PathVariable("channel") String channel, @RequestParam("businessId") Long businessId,
                                                                                     @RequestParam(value = "userId") Integer userId, @RequestParam(value = "startIndex", defaultValue = "0") Integer startIndex,
                                                                                     @RequestParam(value = "count", defaultValue = "25") Integer count, @RequestParam("context") String context, @RequestParam(value = "sort", defaultValue = "0") Integer sort,
                                                                                     @RequestHeader(Constants.BUSINESS_ID) Integer accountId)
            throws Exception {
        return new ResponseEntity<>(socialAccountService.getLocationMappingPages(channel, businessId, startIndex, count, context, sort, userId, accountId), HttpStatus.OK);
    }

    /**
     * API to save page-location mapping. Called from UI.
     *
     * @param channel
     * @param input
     * @param userId
     * @return
     * @throws Exception
     */

    @PutMapping(value = "/{channel}/page/mapping")
    public @ResponseBody ResponseEntity<Map<String, Object>> saveLocationPageMapping(@RequestHeader("userId") Integer userId, @PathVariable("channel") String channel,
                                                                                     @RequestBody LocationPageMappingRequest input, @RequestParam(value = "force", required = false, defaultValue = "false") Boolean force, @RequestHeader(value = Constants.BUSINESS_NUMBER, required = false) Long enterpriseId) throws Exception {
        if (input != null && SocialChannel.getSocialChannelByName(channel) != null) {
            return new ResponseEntity<>(socialAccountService.saveLocationPageMapping(channel, input.getLocationId(), input.getPageId(), userId, force, enterpriseId), HttpStatus.OK);
        }
        throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
    }

    /**
     * API to remove page-location mapping. Called from UI.
     * This API has been marked as deprecated in lieu of @PostMapping("/{channel}/mapping")
     *
     * @param channel
     * @param input
     * @return
     * @throws Exception
     */
    @Deprecated
    @PutMapping(value = "/{channel}/remove/mapping")
    public @ResponseBody ResponseEntity<Void> removePageMapping(@PathVariable("channel") String channel, @RequestParam("unlink") boolean unlink,
                                                                @RequestBody LocationPageMappingRequest input,
                                                                @RequestHeader(value = Constants.BUSINESS_NUMBER, required = false) Long enterpriseId
    ) throws Exception {
        if (input != null && SocialChannel.getSocialChannelByName(channel) != null) {
            LOG.info("DELETE mapping request received for {} with mapping data {}", channel, input);
            socialAccountService.removePageMappings(channel, Collections.singletonList(input), unlink, enterpriseId);
        } else {
            LOG.error("Invalid input/channel received.");
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to remove page-location mappings for a channel using a list. Called from UI.
     *
     * @param channel - channel name
     * @param input   - List of LocationPageMappingRequest
     * @return - HTTP Status
     * @throws Exception - If input is incorrect
     */
    @PostMapping("/{channel}/remove/mapping")
    public @ResponseBody ResponseEntity<Void> removePageMapping(@PathVariable("channel") String channel, @RequestBody List<LocationPageMappingRequest> input,
                                                                @RequestHeader(value = Constants.BUSINESS_NUMBER, required = false) Long enterpriseId) throws Exception {
        LOG.info("POST mapping request received for {} with mapping data {}", channel, input);
        if (input != null && SocialChannel.getSocialChannelByName(channel) != null) {
            LOG.info("POST mapping request received for {} with mapping data {}", channel, input);
            socialAccountService.removePageMappings(channel, input, false, enterpriseId);
        } else {
            LOG.error("Invalid input/channel received.");
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PutMapping("/remove-mapping")
    public @ResponseBody ResponseEntity<Void> removeMappingForLM(@Valid @RequestBody RemovePageMappingRequest removePageMappingRequest,
                                                                 @RequestHeader(value = Constants.BUSINESS_NUMBER, required = false) Long enterpriseId) throws Exception {
        LOG.info("Remove page mapping for request : {}", removePageMappingRequest);
        socialAccountService.removeMappingForPageIds(removePageMappingRequest, enterpriseId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Not in use
     *
     * @param enterpriseId
     * @return
     * @throws Exception
     */
    @GetMapping(value = "/pages/counts")
    public @ResponseBody ResponseEntity<Map<String, ChannelPageCount>> getPagesCount(@RequestParam("businessId") Long enterpriseId) throws Exception {
        if (enterpriseId != null) {
            Map<String, ChannelPageCount> pageCount = socialAccountService.getChannelPageCounts(enterpriseId);
            return new ResponseEntity<>(pageCount, HttpStatus.OK);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
    }

    /**
     * API to perform cleanup tasks for a page whose mapping has been removed
     * This API is called from NiFi
     *
     * @param channel - channel name
     * @param input   - List of LocationPageMappingRequest
     * @return - HTTP Status
     * @throws Exception - If input is incorrect
     */
    @PostMapping("/{channel}/cleanup/mapping")
    public @ResponseBody ResponseEntity<Void> cleanupPageMappings(@PathVariable("channel") String channel, @RequestBody List<FacebookRemovePageMappingCleanupRequest> input) throws Exception {
        LOG.info("POST cleanup mapping request received for {} with mapping data {}", channel, input);
        if (input != null && SocialChannel.getSocialChannelByName(channel) != null) {
            socialAccountService.cleanupPageMappings(channel, input);
        } else {
            LOG.error("Invalid input/channel received.");
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to get autopost options
     *
     * @param channel
     * @param businessId
     * @param startIndex
     * @param count
     * @param userId
     * @return
     * @throws Exception
     */
    @GetMapping(value = "{channel}/autopost/options")
    public @ResponseBody ResponseEntity<ReviewOptionsInfo> getReviewOptions(@PathVariable("channel") String channel, @RequestParam("businessId") Long businessId,
                                                                            @RequestParam("startIndex") Integer startIndex, @RequestParam("count") Integer count, @RequestParam("userId") Integer userId) throws Exception {
        return new ResponseEntity<>(socialAccountService.getReviewShareOptions(channel, businessId, startIndex, count, userId), HttpStatus.OK);
    }

    /**
     * Auto suggester page search API Fetch unmapped pages of an enterprise. Called from UI
     *
     * @param channel
     * @param businessId
     * @return
     * @throws Exception
     */

    @GetMapping(value = "{channel}/search/pages")
    public @ResponseBody ResponseEntity<AutoSuggesterPagesResponse> findUnmappedPages(@PathVariable("channel") String channel, @RequestParam("businessId") Long businessId) throws Exception {
        return new ResponseEntity<>(socialAccountService.findUnmappedPages(channel, businessId), HttpStatus.OK);
    }

    /**
     * API to update autopost options
     *
     * @param channel
     * @param input
     * @param userId
     * @return
     * @throws Exception
     */
    @PutMapping(value = "{channel}/autopost/update")
    public @ResponseBody ResponseEntity<ReviewOptionDto> updateReviewSharingOptions(@PathVariable("channel") String channel, @RequestBody ReviewOptionDto input,
                                                                                    @RequestParam("userId") Integer userId) throws Exception {
        return new ResponseEntity<>(
                socialAccountService.updateReviewSharingOptions(input.getLocationId(), input.getPageId(), input.getAutoPostingEnabled(), input.getStarVal(), input.getPostVal(), channel, userId),
                HttpStatus.OK);
    }

    @GetMapping(value = "/{channel}/location/page")
    public @ResponseBody ResponseEntity<ChannelLocationInfo> getSingleLocationMappingPage(@PathVariable("channel") String channel, @RequestParam("businessId") Integer businessId)
            throws Exception {
        return new ResponseEntity<>(socialAccountService.getSingleLocationMappingPages(channel, businessId), HttpStatus.OK);
    }

    /**
     * API to mark social pages as invalid
     *
     * @param channel
     * @param pageId
     * @return
     * @throws Exception
     */
    @PutMapping(value = "/{channel}/page/invalid")
    public @ResponseBody ResponseEntity<Void> updateInvalidPage(@PathVariable("channel") String channel, @RequestParam("pageId") String pageId) throws Exception {
        if (pageId != null && SocialChannel.getSocialChannelByName(channel) != null) {
            socialAccountService.updateInvalidPage(channel, pageId);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }


    /**
     * API to get google redirect_url
     *
     * @param businessId
     * @param redirectToSetup
     * @return
     * @throws Exception
     */
    @GetMapping(value = "/auth/url")
    public ResponseEntity<Map<String, String>> getGoogleAuthenticationParams(@RequestParam("businessId") Long businessId,
                                                                             @RequestParam(name = "redirectToSetup", required = false) Boolean redirectToSetup) throws Exception {
        String url = socialAccountService.getGoogleAuthUrl(businessId, redirectToSetup);
        Map<String, String> map = new HashMap<>();
        map.put("redirect_url", url);
        return new ResponseEntity<>(map, HttpStatus.OK);
    }

    @GetMapping(value = "youtube/auth/url")
    public ResponseEntity<Map<String, String>> getYoutubeAuthenticationParams(@RequestParam("businessId") Long businessId,
                                                                              @RequestParam("origin") String origin) throws Exception {
        String url = socialAccountService.getYoutubeAuthUrl(businessId, false, "", origin);
        Map<String, String> map = new HashMap<>();
        map.put("redirect_url", url);
        return new ResponseEntity<>(map, HttpStatus.OK);
    }

    /**
     * API to update counter for pages where oAuthException has occurred in case of FB.
     *
     * @param pageId
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/facebook/page/{pageId}/invalid")
    public void updateInvalidPageCounter(@PathVariable("pageId") String pageId, @RequestBody Object details) {
        socialAccountService.updateInvalidPageCounter(pageId, details);
    }

    /**
     * API to mark FB Page as invalid as per the new relaxed approach.(After 24hrs)
     */
    @PostMapping(value = "/facebook/markinvalid")
    public void markFBIntegrationInvalid() {
        socialAccountService.markFBIntegrationInvalid();
    }

    /**
     * API to remove social pages integration for suspended/inactive businesses
     *
     * @param enterpriseId
     * @return
     */
    @DeleteMapping(value = "/removeintegration")
    public ResponseEntity<Void> removeInactiveIntegration(@RequestParam("enterpriseId") Long enterpriseId) {
        if (enterpriseId != null) {
            socialAccountService.removeInactiveIntegration(enterpriseId);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Channel or enterpriseId can not be blank.");
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }


    /**
     * Api to remove integration and disable gnip rule and keywords when business is suspended, called from nifi
     *
     * @param request
     * @return
     */
    @PutMapping(value = "/integration")
    public ResponseEntity<Void> inactiveBusinessIntegrationAndRules(@RequestBody BusinessStatusData request) {
        LOG.info("request received to remove integration for business : closed : activationStatus : isEnterprise : isSmb : {} {} {} {} {}",
                request.getBusinessId(), request.getClosed(), request.getActivationStatus(), request.getIsEnterpriseLocation(), request.getIsSMB());
        socialAccountService.removeInactiveBusinessIntegration(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Api to restore gnip rule and keywords when business is re-opened, called from nifi
     *
     * @param request
     * @return
     */
    @PutMapping(value = "/restore-rules")
    public ResponseEntity<Void> reActivateBusinessRuleAndKeywords(@RequestBody BusinessStatusData request) {
        LOG.info("request received to restore rules and keywords for business : closed : fromStatus : toStatus : isEnterprise : isSmb : {} {} {} {} {} {}",
                request.getBusinessId(), request.getClosed(), request.getFromStatus(), request.getToStatus(), request.getIsEnterpriseLocation(), request.getIsSMB());
        socialAccountService.restoreRules(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/location/migration-event")
    public ResponseEntity<Void> businessLocationMovemet(@RequestBody LocationMovementDto locationMovementDto) {
        if (locationMovementDto != null) {
            socialAccountService.moveBusinessLocation(locationMovementDto);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "location movement detaisls cannot be empty");
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/messenger/status")
    public ResponseEntity<InboxStatusResponseComplete> getMessengerStatus(@RequestBody InboxStatus request) {
        InboxStatusResponseComplete response = socialAccountService.getMessengerStatus(request);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }


    @GetMapping("/facebook/updatetokenpermission")
    public ResponseEntity<Void> updateFBTokenPermissionsForAllPages() {
        socialAccountService.updateFBTokenPermissionsForAllPages();
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to restore Pages for Enterprises
     *
     * @param enterpriseIds
     * @return
     * @throws Exception
     */
    @PutMapping(value = "restore/integrations")
    public @ResponseBody ResponseEntity<Void> backupPage(@RequestBody List<Long> enterpriseIds, @RequestParam("onlyPosts") boolean onlyPosts) throws Exception {
        if (CollectionUtils.isNotEmpty(enterpriseIds)) {
            if (!onlyPosts) {
                socialAccountService.backupPages(enterpriseIds);
            }
            socialAccountService.restorePosts(enterpriseIds);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Channel or enterpriseId can not be blank.");
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to migrate existing fb pages to subscribe for rating notification from fb webhook
     *
     * @param limit
     */

    @PutMapping(value = "/migrateAllPagesToRatings")
    public ResponseEntity<List<Integer>> migrateAllPagesToRatings(@RequestParam Integer limit) {
        List<Integer> failedResponses = socialAccountService.migrate(limit);
        return new ResponseEntity<>(failedResponses, HttpStatus.OK);
    }

    /**
     * API to get all connected pages
     *
     * @param channel
     * @param enterpriseId
     * @param userId
     * @param type
     * @return
     */
    @GetMapping(value = "/{channel}/pages")
    public @ResponseBody ResponseEntity<ConnectedPages> getPages(@PathVariable("channel") String channel, @RequestHeader("userId") Integer userId,
                                                                 @RequestParam("enterpriseId") Long enterpriseId, @RequestParam(value = "type", defaultValue = "all") String type) {
        return new ResponseEntity<>(socialAccountService.getPages(channel, enterpriseId, userId, type), HttpStatus.OK);
    }

    /**
     * Api to return Smb source wise page data for Api key smb
     *
     * @param businessId
     * @return
     */

    @GetMapping(value = "/page")
    public @ResponseBody ResponseEntity<SmbChannelData> allSourcePageForSMB(@RequestParam("businessId") Long businessId) {
        return new ResponseEntity<>(socialAccountService.allSourcePageForSMB(businessId), HttpStatus.OK);
    }

    /**
     * Api to return source wise page data for smb business
     *
     * @param businessId
     * @return
     */

    @GetMapping(value = "/page/all")
    public @ResponseBody ResponseEntity<Map<String, LocationPageListInfo>> getAllSourcePageForSMB(@RequestParam("businessId") Long businessId) {
        return new ResponseEntity<>(socialAccountService.getPageAllSource(businessId), HttpStatus.OK);
    }

    /**
     * API to check if page exists for channels
     *
     * @param accountId
     * @param channel
     * @return ChannelConnectedPageInfo
     */

    @GetMapping(value = "/connect/{accountId}/status")
    public @ResponseBody ResponseEntity<ChannelConnectedPageInfo> getChannelWisePageExistsInfo(@PathVariable("accountId") Long accountId,
                                                                                               @RequestParam(value = "channel", required = false, defaultValue = "") String channel) throws Exception {
        ChannelConnectedPageInfo channelPageInfo = socialAccountService.checkForConnectedPages(accountId, channel);
        return new ResponseEntity<>(channelPageInfo, HttpStatus.OK);
    }

    /**
     * API to check integration status of all or some of the channels on BirdEye Dashboard
     * This API is used to show dashboard alerts
     *
     * @param enterpriseId : Long enterpriseId
     * @param channel      : Specific channel name or ""
     * @return ChannelSetupStatus containing Enum result with each channel
     * @throws Exception
     */
    @GetMapping(value = "/integration/status/{accountId}")
    public @ResponseBody ResponseEntity<ChannelSetupStatus> getChannelWiseSetupStatus(@PathVariable("accountId") Long enterpriseId,
                                                                                      @RequestParam(value = "channel", required = false, defaultValue = "") String channel) throws Exception {
        return new ResponseEntity<>(socialAccountService.getChannelWiseSetupStatus(enterpriseId, channel), HttpStatus.OK);
    }

    @GetMapping(value = "/integration/connection/status")
    public @ResponseBody ResponseEntity<ChannelSetupStatus> getChannelWisePageConnection(@RequestParam(value = "channel", required = false, defaultValue = "") String channel,
                                                                                         @RequestHeader(Constants.BUSINESS_NUMBER) Long enterpriseId) throws Exception {
        return new ResponseEntity<>(socialAccountService.getChannelWisePageConnectionStatus(enterpriseId, channel), HttpStatus.OK);
    }


    /**
     * API to get social integration status for given business location id
     *
     * @param businessId
     * @return
     * @throws Exception
     */
    @GetMapping(value = "/status/business/{businessId}")
    public @ResponseBody ResponseEntity<BusinessIntegrationStatus> getBusinessIntegrationStatus(@PathVariable("businessId") Integer businessId) throws Exception {
        return new ResponseEntity<>(socialAccountService.getBusinessIntegrationStatus(businessId), HttpStatus.OK);
    }

    /**
     * API to initiate auto mapping triggered from connectpages and and pushed to kafka. Initiated from Nifi
     *
     * @param request
     */
    @PostMapping(value = "/autoMapping/initRequest")
    public @ResponseBody ResponseEntity<Void> recieveAutoMappingInitRequest(@RequestBody AutoMappingRequest request) throws Exception {

        if (SocialChannel.getSocialChannelByName(request.getChannel()) != null) {
            socialAccountService.processAutoMappingInitRequest(request);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to initiate page mapping post matching from automapping and pushed to kafka. Initiated from Nifi
     *
     * @param request
     */
    @PostMapping(value = "/autoMapping/matchedRequest")
    public @ResponseBody ResponseEntity<Void> recieveAutoMappingMatchedRequest(@RequestBody AutoMappingMatchedRequest request) throws Exception {
        if (SocialChannel.getSocialChannelByName(request.getChannel()) != null) {
            socialAccountService.processAutoMappingMatchedRequest(request);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to remove all firebase entries with init status, will be called from scheduler
     */
    @PostMapping(value = "/autoMapping/Firebase")
    public @ResponseBody ResponseEntity<Void> updateFirebaseForAutoMapping() throws Exception {
        socialAccountService.updateFirebaseForAutoMapping();
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to fetch gmb pages account level for setup, called from nifi
     *
     * @param gmbAccountSyncRequest
     * @return
     */

    @PostMapping(value = "/gmb/accountFetchPage")
    public ResponseEntity<?> gmbAccountCheck(@RequestBody GMBAccountSyncRequest gmbAccountSyncRequest) {
        if (gmbAccountSyncRequest != null) {
            gmbLocationDetailService.fetchGMBLocationsForAccount(gmbAccountSyncRequest);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to fetch gmb pages account level for reconnect, called from nifi
     *
     * @param gmbAccountSyncRequest
     * @return
     */

    @PostMapping(value = "/gmb/gmbAccReconnect")
    public ResponseEntity<?> gmbAccountReconnectFetch(@RequestBody GMBAccountSyncRequest gmbAccountSyncRequest) {
        if (gmbAccountSyncRequest != null) {
            gmbLocationDetailService.fetchGMBLocationsForReconnect(gmbAccountSyncRequest);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to fill the priority queue with enterprise having disconnected location(s)
     *
     * @param channel
     */
    @PostMapping(value = "enterprise/{channel}/disconnect/job")
    public void processDisconnectedLocations(@PathVariable("channel") String channel) {
        socialAccountService.processDisconnectedLocations(channel);
    }


    /**
     * Utility API to take action on enterprise for channel disconnect
     *
     * @param channelDisconnectRequest
     */
    @PostMapping(value = "enterprise/disconnect/action")
    public void performActionOnDisconnect(@RequestBody ChannelDisconnectRequest channelDisconnectRequest) {
        socialAccountService.performActionOnDisconnected(channelDisconnectRequest);
    }

    /**
     * API to check integration status of all or some of the channels on BirdEye Dashboard
     * This API is used to show dashboard alerts
     *
     * @param channel : Specific channel name or ""
     * @throws Exception
     */
    @GetMapping(value = "/unmapped/SMB/{channel}")
    public @ResponseBody ResponseEntity<SmbUnmappedDataResponse> getUnmappedPagesForSmb(@PathVariable("channel") String channel) throws Exception {
        return new ResponseEntity<>(socialAccountService.getUnmappedPagesForSmb(channel), HttpStatus.OK);
    }

    /**
     * API to get unmapped location count. Called from other services like core.
     *
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/unmapped-location-count")
    public @ResponseBody ResponseEntity<UnmappedLocationMappingResponse> getUnmappedLocationCount(@RequestBody UnmappedLocationMappingReq request)
            throws Exception {
        return new ResponseEntity<>(socialAccountService.getUnmappedLocationCount(request), HttpStatus.OK);
    }

    @PutMapping(value = "/{channel}/audit")
    public ResponseEntity<?> auditSocialPage(@PathVariable("channel") String channel, @RequestBody SocialSetupAudit socialSetupAudit) {
        if (socialSetupAudit != null) {
            socialAccountService.auditSocialPage(socialSetupAudit, channel);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to reconnect invalid pages for Social channel. Called from UI
     *
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/permissions/")
    public @ResponseBody ResponseEntity<Void> updatePermissions(@RequestBody TokenUpdateRequest request) throws Exception {
        if (request != null) {
            socialAccountService.updateAccessToken(request);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API will be used by Inbox to get fb page id for given business id
     **/
    @GetMapping(value = "/fb/businessid/{businessId}")
    public @ResponseBody ResponseEntity<String> getFbIdByBusinessId(@PathVariable("businessId") Integer businessId) {
        String fbId = socialAccountService.getFbIdByBusinessId(businessId);
        return new ResponseEntity<>(fbId, HttpStatus.OK);
    }

    /**
     * API will be used by Inbox to get fb page id for given business id
     **/
    @GetMapping(value = "/fb/{facebookPageId}")
    public @ResponseBody ResponseEntity<Map<String, Object>> getByFbId(@PathVariable("facebookPageId") String facebookPageId,
                                                                       @RequestParam(value = "fields", required = false) List<String> fields) {
        Map<String, Object> instagramInfo = socialAccountService.getByBusinessId(facebookPageId, fields);
        return new ResponseEntity<>(instagramInfo, HttpStatus.OK);
    }

    @GetMapping(value = "/migration/phoneNumber")
    public @ResponseBody ResponseEntity<Void> updatedFbPagesWithPhone() {
        socialAccountService.updatedFbPagesWithPhone();
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/validate/token")
    public @ResponseBody ResponseEntity<Void> validateToken(@RequestBody SocialTokenValidationDTO payload) {
        socialAccountService.validateToken(payload);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/invalid/page")
    public @ResponseBody ResponseEntity<Void> markPageAsInvalid(@RequestBody SocialTokenValidationDTO payload) {
        socialAccountService.markPageAsInvalid(payload);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PutMapping(value = "update/page")
    public @ResponseBody ResponseEntity<Void> updateLinkedinPage(@RequestBody LinkedinUpdateFieldRequest updateRequest) throws Exception {
        LOG.info("Update request received for linkedin with mapping data {}", updateRequest);
        if (Objects.nonNull(updateRequest)) {
            linkedinService.updateLinkedinMapping(updateRequest);
        } else {
            LOG.error("Invalid input/channel received.");
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/reconnect/valid/pages")
    public @ResponseBody ResponseEntity<Set<String>> fetchReconnectValidPages(@RequestBody AccessTokenUpdateRequest updateRequest) {
        LOG.info(" request received for fetchReconnectValidPages {}", updateRequest);
        socialAccountService.reconnectValidPages(updateRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * The API will be called from BE dashboard for google auth url for redirection flow
     *
     * @return
     * @throws Exception
     */
    @GetMapping(value = "google/auth/login/url")
    public ResponseEntity<Map<String, String>> getGoogleV2AuthenticationParams(@RequestParam("domain") String domain) throws Exception {
        LOG.info("request received for getGoogleV2AuthenticationParams {}", domain);
        String url = socialAccountService.googleAuthLoginUrl(domain);
        Map<String, String> map = new HashMap<>();
        map.put("redirect_url", url);
        return new ResponseEntity<>(map, HttpStatus.OK);
    }

    @GetMapping(value = "google/auth/url")
    public ResponseEntity<Map<String, String>> getGoogleV2AuthenticationParamsForLogin(@RequestParam("origin") String origin) throws Exception {
        LOG.info("request received for getGoogleV2AuthenticationParams {}", origin);
        String url = socialAccountService.googleAuthSetupUrl(origin);
        Map<String, String> map = new HashMap<>();
        map.put("redirect_url", url);
        return new ResponseEntity<>(map, HttpStatus.OK);
    }

    @GetMapping(value = "/{channel}/initiate/dp-sync")
    public @ResponseBody ResponseEntity<Void> initiateDPSync(@PathVariable String channel) throws Exception {
        socialAccountService.initiateDPSync(channel);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/{channel}/sync/dp")
    public ResponseEntity<Void> syncIGDP(@PathVariable String channel, @RequestBody DpSyncRequest dpSyncRequest) {
        socialAccountService.syncDP(channel, dpSyncRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/all/status")
    public @ResponseBody ResponseEntity<AccountValidationResponse> getAllChannelWiseSetupStatus(@RequestBody AccountValidationRequest request) throws Exception {
        return new ResponseEntity<>(socialAccountService.getAllChannelWiseSetupStatus(request), HttpStatus.OK);
    }

    @PostMapping(value = "/get/business/status")
    public ResponseEntity<SocialBusinessStatusResponse> getStatusOfBusinessWithChannel(@RequestBody SocialBusinessStatusRequest request) {
        LOG.info("Request received for business ids : {}", request);
        return new ResponseEntity<>(socialAccountService.getStatusOfBusinessWithChannel(request), HttpStatus.OK);
    }

    @GetMapping(value = "/unsubscribe/{channel}/{months}")
    public @ResponseBody ResponseEntity<Void> unsubscribeAccountInit(@PathVariable String channel, @PathVariable Integer months,
                                                                     @RequestParam("action") SocialSetupAuditEnum action) {
        socialAccountService.unsubscribeAccountInit(channel, months, action);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/page/unsubscribe")
    public ResponseEntity<Void> unsubscribePage(@RequestBody SocialSetupAudit socialSetupAudit) {
        socialAccountService.unsubscribePage(socialSetupAudit);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/{channel}/post/pages")
    public @ResponseBody ResponseEntity<ConnectedPages> getPagesForPostingReconnect(@PathVariable("channel") String channel, @RequestHeader("user-id") Integer userId,
                                                                                    @RequestParam("enterpriseId") Long enterpriseId, @RequestParam(value = "type", defaultValue = "all") String type, @RequestBody SocialPostPageConnectRequest request) {
        return new ResponseEntity<>(socialAccountService.getPagesForPostReconnect(channel, enterpriseId, userId, type, request), HttpStatus.OK);
    }

    @PostMapping(value = "/channels/all")
    public @ResponseBody ResponseEntity<LocationPageMappingAllChannels> getPagesForAllChannels(@RequestBody LocationMappingRequest request,
                                                                                               @RequestHeader(Constants.BUSINESS_ID) Integer businessId,
                                                                                               @RequestHeader(Constants.USER_ID) Integer userId,
                                                                                               @RequestHeader(Constants.BUSINESS_NUMBER) Long enterpriseId) throws Exception {
        request.setUserId(userId);
        request.setBusinessId(enterpriseId);
        return new ResponseEntity<>(socialAccountService.getLocationMappingPagesAllChannels(request, businessId), HttpStatus.OK);
    }

    @PutMapping(value = "/{channel}/update/address")
    public @ResponseBody ResponseEntity<Void> updateAddress(@PathVariable("channel") String channel,
                                                            @RequestBody SocialScanEventDTO socialScanEventDTO) {
        socialAccountService.updateAddress(socialScanEventDTO, channel);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(value = "{channel}/auth/url")
    public ResponseEntity<TiktokAuthUrlResponse> getAuthenticationParamsForLogin(@PathVariable("channel") String channel, @RequestParam("origin") String origin) throws Exception {
        return new ResponseEntity<>(socialAccountService.integrationAuthUrl(channel, origin), HttpStatus.OK);
    }

    @PostMapping("/{channel}/locations")
    public @ResponseBody ResponseEntity<ResellerLeafLocationResponse> getAllLocations(@PathVariable("channel") String channel, @Valid @RequestBody ResellerLeafLocationRequest request,
                                                                                      @RequestHeader("account-id") Integer accountId,
                                                                                      @RequestHeader(value = "user-id", required = false) Integer userId,
                                                                                      @RequestParam(name = "size", defaultValue = "5") Integer size,
                                                                                      @RequestParam(name = "page", defaultValue = "0") Integer page) throws Exception {

        LOG.info("[Reseller] get all leaf locations request received for {} with data {}: {}", channel, accountId, request);
        if (request != null && SocialChannel.getSocialChannelByName(channel) != null && accountId != null) {
            return new ResponseEntity<>(socialAccountService.getAllLocations(request, channel, accountId, userId, page, size), HttpStatus.OK);
        } else {
            LOG.error("[Reseller] Invalid input/channel received.");
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
    }

    @PostMapping(value = "/{channel}/pages/all")
    public @ResponseBody ResponseEntity<ConnectedPages> getPages(@PathVariable("channel") String channel,
                                                                 @RequestBody GetPagesRequest request,
                                                                 @RequestHeader(Constants.BUSINESS_NUMBER) Long enterpriseId) {
        LOG.info("Get pages request : {} and channel :{}", request, channel);
        request.setEnterpriseId(enterpriseId);
        return new ResponseEntity<>(socialAccountService.getPaginatedPagesForEnterprise(channel, request), HttpStatus.OK);
    }

    @GetMapping("/{channel}/permission/status")
    public @ResponseBody ResponseEntity<SocialModulePermissionStatusResponse> postPermissionStatus(@RequestHeader(Constants.BUSINESS_NUMBER) Long enterpriseId,
                                                                                                   @PathVariable("channel") String channel,
                                                                                                   @RequestParam(required = false) List<String> modules) {
        LOG.info(" Received request to fetch PermissionStatus with businessId : {} for modules: {}", enterpriseId, modules);
        return new ResponseEntity<>(socialAccountService.postPermissionStatus(enterpriseId, modules, channel), HttpStatus.OK);
    }


    @PostMapping(value = "/check/state/page-request")
    public ResponseEntity<Void> checkInvalidStatePageRequest(@RequestBody CheckInvalidGetPageState checkInvalidGetPageState) {
        socialAccountService.checkInvalidStatePageRequest(checkInvalidGetPageState);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(value = "/{channel}/connections")
    public @ResponseBody ResponseEntity<ConnectedPageResponse> getConnectedPages(@PathVariable("channel") String channel, @RequestParam("enterpriseId") Long enterpriseId) {
        if (Objects.nonNull(enterpriseId) && Objects.nonNull(SocialChannel.getSocialChannelByName(channel))) {
            ConnectedPageResponse connectedPageResponse = socialAccountService.getConnectedPages(channel, enterpriseId);
            LOG.info("Response [getConnectedPages] for enterprise id {} :   {} ", enterpriseId, connectedPageResponse);
            return new ResponseEntity<>(connectedPageResponse, HttpStatus.OK);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
    }

    @GetMapping("/{channel}/region/sync")
	public @ResponseBody ResponseEntity<SocialModulePermissionStatusResponse> pageRegionSync(@PathVariable("channel") String channel,
																								   @RequestParam(required = false) Long enterpriseId) {
		LOG.info("Received request to sync region for channel: {} with businessId : {} ",channel, enterpriseId);
		socialAccountService.pageRegionSync(channel, enterpriseId);
		return new ResponseEntity<>(HttpStatus.OK);
	}

}
