package com.birdeye.social.controller;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialTagEntityType;
import com.birdeye.social.service.SocialTagService;
import com.birdeye.social.sro.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Post the Epic : https://birdeye.atlassian.net/browse/BIRD-282 all the social tags
 * are Globally maintained.
 * <p>
 * This controller is meant for all TAG(s) related functionalities across social.
 *
 * <AUTHOR> on 21/12/23
 */
@RestController
@RequestMapping("/social/tag")
public class SocialTagController {

    @Autowired
    private SocialTagService socialTagService;

    /**
     * API to fetch ALL the tag(s) associated with an account.
     * <p>
     * If #liteVersion is passed as true, then only the basic tag
     * details are populated to make the response time fast and response payload small.
     *
     * @param socialTagFetchAllRequest
     * @param startIndex
     * @param pageSize
     * @param sortBy
     * @param sortOrder
     * @param liteVersion
     * @param accountId
     * @param timezoneId
     * @return
     */
    @PostMapping(path = "/all")
    public ResponseEntity<SocialTagFetchAllResponse> getAllTags(@RequestBody SocialTagFetchAllRequest socialTagFetchAllRequest,
                                                                @RequestParam(value = "start-index", defaultValue = "0") Integer startIndex,
                                                                @RequestParam(value = "page-size", defaultValue = "100") Integer pageSize,
                                                                @RequestParam(value = "sort-by", defaultValue = "date-added") String sortBy,
                                                                @RequestParam(value = "sort-order", defaultValue = "1") Integer sortOrder,
                                                                @RequestParam(value = "lite-version", defaultValue = "false") Boolean liteVersion,
                                                                @RequestHeader(name = "account-id") Integer accountId,
                                                                @RequestHeader(name = "time-zone-id") String timezoneId) {
        return new ResponseEntity<>(socialTagService.getAllTags(socialTagFetchAllRequest, accountId, timezoneId, startIndex, pageSize,
                                                                sortBy, sortOrder, liteVersion), HttpStatus.OK);
    }

    /**
     * API to perform all TAG related operations
     *
     * @param tagOperationRequests
     * @param accountId
     * @param accountNum
     * @param userId
     * @return
     */
    @PutMapping(path = "/operation")
    public ResponseEntity<List<SocialTagOperationResponse>> performTagOperations(@RequestBody List<SocialTagOperationRequest> tagOperationRequests,
                                                                                 @RequestHeader(name = "account-id") Integer accountId,
                                                                                 @RequestHeader(name = "x-business-number") Long accountNum,
                                                                                 @RequestHeader(name = "user-id") Long userId) {
        return new ResponseEntity(socialTagService.performTagOperations(tagOperationRequests, accountId, accountNum, userId, true), HttpStatus.OK);
    }

    /**
     * This API is intended to perform a TAG and the provided ENTITY mapping
     *
     * @param socialTagEntityMappingRequest
     * @param entityType
     * @param entityId
     * @param accountId
     * @param userId
     * @return
     */
    @PutMapping(path = "/{entityType}/{entityId}/mapping")
    public ResponseEntity<Void> performTagEntityMappingOperations(@RequestBody SocialTagEntityMappingRequest socialTagEntityMappingRequest,
                                                                  @PathVariable(value = "entityType") SocialTagEntityType entityType,
                                                                  @PathVariable(value = "entityId") Long entityId,
                                                                  @RequestHeader(name = "account-id") Integer accountId,
                                                                  @RequestHeader(Constants.BUSINESS_NUMBER) Long businessNumber,
                                                                  @RequestHeader(name = "user-id") Long userId) {
        socialTagService.performTagEntityMappingOperations(socialTagEntityMappingRequest, entityType, entityId, accountId, userId, businessNumber, true);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * This API is intended tp perform a TAG and the provided ENTITY mapping in BULK
     *
     * @param socialTagEntityBulkMappingRequest
     * @param entityType
     * @param accountId
     * @param userId
     * @return
     */
    @PutMapping(path = "/{entityType}/bulk/mapping")
    public ResponseEntity<Void> performTagEntityBulkMappingOperations(@RequestBody SocialTagEntityBulkMappingRequest socialTagEntityBulkMappingRequest,
                                                                      @PathVariable(value = "entityType") SocialTagEntityType entityType,
                                                                      @RequestHeader(name = "account-id") Integer accountId,
                                                                      @RequestHeader(name = "user-id") Long userId,
                                                                      @RequestHeader(name = Constants.BUSINESS_NUMBER) Long businessNumber) {
        socialTagService.performTagEntityMappingBulkOperations(socialTagEntityBulkMappingRequest, entityType, accountId, userId, businessNumber);
        return new ResponseEntity<>(HttpStatus.ACCEPTED);
    }

    /**
     * This API is invoked via NiFi for deleting all the tag mappings for the given
     * entity and its type post the entity deletion happens
     *
     * @param entityType
     * @param entityId
     * @return
     */
    @DeleteMapping(path = "/{entityType}/{entityId}/mapping/all")
    public ResponseEntity<Void> deleteAllTagEntityMapping(@PathVariable(value = "entityType") SocialTagEntityType entityType,
                                                          @PathVariable(value = "entityId") Long entityId,
                                                          @RequestHeader(name = "account-id") Integer accountId) {
        socialTagService.deleteAllTagEntityMapping(entityId, entityType, accountId);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    /**
     * This is an internal helper API to evict the account level cache that exists for the account
     *
     * @return
     */
    @DeleteMapping(path = "/{accountId}/cache")
    public ResponseEntity<Void> evictAccountTagCache(@PathVariable(value = "accountId") Integer accountId) {
        socialTagService.evictAllTagsCacheByAccountId(accountId);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    /**
     * This is an adhoc API intended to do a one time migration for the asset library
     * tags to the social tags
     *
     * @return
     */
    @PutMapping(path = "/migrate/asset-library-tag")
    public ResponseEntity<Void> migrateAssetLibraryTagAsSocialTag() {
        socialTagService.migrateAssetLibraryTagAsSocialTag();
        return new ResponseEntity<>(HttpStatus.ACCEPTED);
    }

    /**
     * This API is invoked via NiFi post the
     * {@link #migrateAssetLibraryTagAsSocialTag()} for each individual account to
     * perform a migration
     *
     * @param accountId
     * @return
     */
    @PutMapping(path = "/migrate/{accountId}/asset-library-tag")
    public ResponseEntity<Void> migrateAssetLibraryTagAsSocialTag(@PathVariable(value = "accountId") Integer accountId) {
        socialTagService.migrateAssetLibraryTagAsSocialTag(accountId);
        return new ResponseEntity<>(HttpStatus.ACCEPTED);
    }

}
