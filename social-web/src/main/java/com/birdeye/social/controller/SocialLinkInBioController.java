package com.birdeye.social.controller;

import com.birdeye.social.model.GenericScriptRequest;
import com.birdeye.social.model.linkinbio.*;
import com.birdeye.social.service.linkinbio.LinkInBioService;
import com.birdeye.social.service.scripts.ScriptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/social/link-in-bio")
public class SocialLinkInBioController {

    @Autowired
    private LinkInBioService linkInBioService;

    @Autowired
    private ScriptService scriptService;

    /**
     * Create Link in Bio
     * @param linkInBioRequest
     * @return
     */
    @PostMapping("/create")
    public ResponseEntity<LinkInBioResponse> createLinkInBio(@RequestBody @Valid LinkInBioRequest linkInBioRequest){
        return new ResponseEntity<>(linkInBioService.createLinkInBio(linkInBioRequest),HttpStatus.OK);
    }

    /**
     * Get link in bio details
     * @param enterpriseId
     * @return
     */
    @PostMapping("/")
    public ResponseEntity<LinkInBioWrapperResponse> getLinkInBioDetails(@RequestHeader("X-BUSINESS-NUMBER") Long enterpriseId,
                                                                        @RequestBody LinkInBioGetRequest request){
        return new ResponseEntity<>(linkInBioService.getLinkInBioDetails(enterpriseId,request),HttpStatus.OK);
    }

    /**
     * Get link in bio details
     * @param enterpriseId
     * @return
     */
    @PostMapping("/reseller")
    public ResponseEntity<LinkInBioWrapperResponse> getLinkInBioDetailsForReseller(@RequestHeader("X-BUSINESS-NUMBER") Long enterpriseId,
                                                                        @RequestBody LinkInBioGetRequest request){
        return new ResponseEntity<>(linkInBioService.getLinkInBioDetails(enterpriseId,request),HttpStatus.OK);
    }

    /**
     * Link in bio url validator
     * @param url
     * @return
     */
    @GetMapping("/validate")
    public ResponseEntity<LinkValidationResponse> getUrlValidator(@RequestParam("url") String url){
        return new ResponseEntity<>(linkInBioService.checkIfLinkIsPresent(url),HttpStatus.OK);
    }

    /**
     * Link Url validator
     * @param request
     * @return
     */
    @PutMapping("/validate/link")
    public ResponseEntity<List<LinkValidationResponse>> validateCreatePostLinks(@RequestHeader("X-BUSINESS-NUMBER") Long enterpriseId,
                                                                                @RequestHeader("business-id") Integer businessId,
                                                                                @RequestBody List<LinkValidationRequest> request){
        return new ResponseEntity<>(linkInBioService.validateCreatePostLinks(enterpriseId,businessId,request),HttpStatus.OK);
    }


    /**
     * update links or add new links for link in bio
     * @param linkInBioId
     * @param linkInBioRequest
     * @return
     */
    @PutMapping("/update/{id}")
    public ResponseEntity<Void> updateLinksForLinkInBio(@PathVariable("id") Integer linkInBioId,@Valid @RequestBody LinkInBioRequest linkInBioRequest){
        linkInBioService.updateLinksForLinkInBio(linkInBioId,linkInBioRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Get links associated with link in bio id
     * @param linkInBioId
     * @return
     */
    @GetMapping("/links/{id}")
    public ResponseEntity<LinkInBioResponse> getLinksOfLinkInBio(@PathVariable("id") Integer linkInBioId){
        return new ResponseEntity<>(linkInBioService.getLinksOfLinkInBio(linkInBioId),HttpStatus.OK);
    }

    /**
     * Get links associated with link in bio id for reseller tab
     * @param linkInBioId
     * @return
     */
    @GetMapping("/links/{id}/reseller")
    public ResponseEntity<LinkInBioResponse> getLinksOfLinkInBioForReseller(@PathVariable("id") Integer linkInBioId){
        return new ResponseEntity<>(linkInBioService.getLinksOfLinkInBio(linkInBioId),HttpStatus.OK);
    }

    /**
     * Get Links for SMB
     * @param enterpriseId
     * @return
     */
    @GetMapping("/links")
    public ResponseEntity<LinkInBioResponse> getLinksForSMB(@RequestHeader("X-BUSINESS-NUMBER") Long enterpriseId , @RequestHeader("business-id") Integer businessId){
        return new ResponseEntity<>(linkInBioService.getLinksForSmb(enterpriseId,businessId),HttpStatus.OK);
    }

    /**
     * Delete link in bio and corresponding links
     * @param linkInBioId
     * @return
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteLinkInBio(@PathVariable("id") Integer linkInBioId){
        linkInBioService.deleteLinkInBio(linkInBioId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Delete link for link in bio Id
     * @param linkInBioId
     * @param linkId
     * @return
     */
    @DeleteMapping("/{id}/{linkId}")
    public ResponseEntity<Void> deleteLinkForLinkInBio(@PathVariable("id") Integer linkInBioId , @PathVariable("linkId") Integer linkId){
        linkInBioService.deleteLinkForLinkInBio(linkInBioId,linkId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Get info by url
     * @param linkInBioUrl
     * @return
     */
    @GetMapping("/get")
    public ResponseEntity<LinkInBioPublicResponse> getLinkInBioResponse(@RequestParam("url") String linkInBioUrl){
        return new ResponseEntity<>(linkInBioService.getLinkInBioResponse(linkInBioUrl),HttpStatus.OK);
    }

    /**
     * Increment click count by 1
     * @param buttonId
     * @return
     */
    @PutMapping("/{buttonId}")
    public ResponseEntity<Void> updateClickCount(@PathVariable("buttonId") Integer buttonId){
        linkInBioService.updateClickCount(buttonId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Update view count for link in bio
     * @param linkId
     * @param enterpriseId
     * @return
     */
    @PutMapping("/link/{linkId}")
    public ResponseEntity<Void> updateViewCount(@PathVariable("linkId") Integer linkId,@RequestHeader("X-BUSINESS-NUMBER") Long enterpriseId){
        linkInBioService.updateViewCount(linkId,enterpriseId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Reports data for analyse screen
     * @param enterpriseId
     * @param request
     * @return
     */
    @PostMapping("/reports")
    public ResponseEntity<LinkInBioWrapperResponse> getTotalViewsAndCount(@RequestHeader("X-BUSINESS-NUMBER") Long enterpriseId,
                                                                          @RequestBody LinkInBioGetRequest request){
        return new ResponseEntity<>(linkInBioService.getReportsData(enterpriseId,request),HttpStatus.OK);
    }

    /**
     * Create new link in Bio via core event
     * @param businessSignUpEvent
     * @return
     */
    @PostMapping("/create/link")
    public ResponseEntity<Void> createLinkInBioForCoreEvent(@Valid @RequestBody BusinessSignUpEvent businessSignUpEvent){
        linkInBioService.createLinkInBioForCoreEvent(businessSignUpEvent);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Update link via link id
     * @param linkId
     * @param linkInBioRequest
     * @return
     */
    @PutMapping("/update/link/{linkId}")
    public ResponseEntity<LinkInBioResponse> updateLinkInBioInfo(@PathVariable("linkId") Integer linkId ,
                                                                 @Valid @RequestBody LinkInBioRequest linkInBioRequest){
        return new ResponseEntity<>(linkInBioService.updateLinkInBioInfo(linkInBioRequest,linkId),HttpStatus.OK);
    }

    /**
     * Create links via post
     * @param request
     * @return
     */
    @PostMapping("/create/links")
    public ResponseEntity<Void> postLinksFromCreatePost(@RequestBody LinkInfoEventRequest request){
        linkInBioService.createLinksIfNotPresent(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Script to create link in bio
     * @param event
     * @return
     */
    @PostMapping("/create/links/script")
    public ResponseEntity<Void> postLinkInBioForBusiness(@RequestBody BusinessPropertyEventRequest event){
        linkInBioService.createLinkInBioViaSocialEnabled(event);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Get list of link in bio for create post screen
     * @param businessNumber
     * @return
     */
    @GetMapping("/get/list")
    public ResponseEntity<LinkInBioWrapperResponse> getLinkInBioForEnterprise(@RequestHeader("X-BUSINESS-NUMBER") Long businessNumber){
        return new ResponseEntity<>(linkInBioService.getLinkInBioResponseForEnterprise(businessNumber),HttpStatus.OK);
    }

    /**
     * One time script to update data in social_business_property and create link-in-bio
     * @param genericScriptRequest
     * @return
     */
    @PostMapping("/script/business")
    public ResponseEntity<Void> scriptForBusiness(@RequestBody GenericScriptRequest genericScriptRequest){
        scriptService.addLinkInBio(genericScriptRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Event from core if social is enabled or disabled
     * @param request
     * @return
     */
    @PutMapping("/social/enabled/event")
    public ResponseEntity<Void> socialEnabledEvent(@RequestBody BusinessPropertyEventRequest request){
        linkInBioService.updateSocialBusinessProperty(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Create link in bio with social enabled event
     * @param propertyEventRequest
     * @return
     */
    @PostMapping("/social-enabled")
    public ResponseEntity<Void> createLinkInBioSocialEnabled(@RequestBody BusinessPropertyEventRequest propertyEventRequest){
        linkInBioService.createLinkInBioViaSocialEnabled(propertyEventRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/remove/duplicate")
    public ResponseEntity<Void> removeDuplicate(@RequestBody List<Long> businessNumberList){
        linkInBioService.removeDuplicate(businessNumberList);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Event from /social/link-in-bio/social/enabled/event to purge scheduled posts if social is disabled.
     * @param request
     * @return
     */
    @PutMapping("/event/post/purge")
    public ResponseEntity<Void> purgePostsOnSocialDisable(@RequestBody BusinessPropertyEventRequest request){
        linkInBioService.deleteScheduledPostsOnSocialDisbale(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }

}
