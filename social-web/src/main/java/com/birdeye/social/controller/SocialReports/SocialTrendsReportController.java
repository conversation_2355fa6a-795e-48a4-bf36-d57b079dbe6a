package com.birdeye.social.controller.SocialReports;

import com.birdeye.social.trends.SocialTrendsReportResponse;
import com.birdeye.social.trends.TrendsLocationLeaderboardResponse;
import com.birdeye.social.trends.TrendsOverviewResponseDTO;
import com.birdeye.social.trends.TrendsReportSummaryResponse;
import com.birdeye.social.service.SocialTrendsReportService.SocialTrendsReportService;
import com.birdeye.social.trends.TrendsReportRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.io.IOException;

@RestController
@RequestMapping("/social/response-trends/report")
public class SocialTrendsReportController {

    @Autowired
    SocialTrendsReportService socialSlaReportService;

    @PutMapping("/summary")
    public ResponseEntity<TrendsReportSummaryResponse> slaReportSummary(@RequestBody TrendsReportRequest request,
                                                                        @RequestHeader(name = "x-business-number") Long accountNum) {
        request.setEnterpriseId(accountNum);
        return new ResponseEntity<>(socialSlaReportService.getSLAReportSummary(request), HttpStatus.OK);
    }

    @PutMapping("/response-rate-by-channel")
    public ResponseEntity<SocialTrendsReportResponse> getResponseRateByChannel(@RequestBody TrendsReportRequest request,
                                                                               @RequestHeader(name = "x-business-number") Long accountNum) {
        request.setEnterpriseId(accountNum) ;
        return new ResponseEntity<>(socialSlaReportService.getResponseRateByChannel(request), HttpStatus.OK);
    }

    @PutMapping("/response-time-by-users")
    public ResponseEntity<SocialTrendsReportResponse> getResponseTimeByUser(@RequestBody TrendsReportRequest request,
                                                                            @RequestHeader(name = "x-business-number") Long accountNum,
                                                                            @RequestHeader(name = "account-id") Integer accountId,
                                                                            @RequestParam(value = "startIndex", required = false, defaultValue = "0") Integer startIndex,
                                                                            @RequestParam(value = "pageSize", required = false, defaultValue = "100") Integer pageSize,
                                                                            @RequestParam(value = "sortParam", required = false, defaultValue = "responseTime") String sortParam,
                                                                            @RequestParam(value = "sortOrder", required = false, defaultValue = "asc") String sortOrder) throws IOException {
        updateReportFilter(request,startIndex,sortParam,sortOrder,pageSize,accountNum,accountId)      ;
        return new ResponseEntity<>(socialSlaReportService.getResponseTimeByUser(request), HttpStatus.OK);
    }

    private void updateReportFilter(TrendsReportRequest request, Integer startIndex, String sortParam, String sortOrder,Integer pageSize,Long accNum, Integer accountId) {
        request.setPageSize(pageSize);
        request.setStartIndex(startIndex);
        request.setSortParam(sortParam);
        request.setSortOrder(sortOrder);
        request.setEnterpriseId(accNum);
        request.setAccountId(accountId);
    }

    @PutMapping("/response-time-by-channel")
    public ResponseEntity<SocialTrendsReportResponse> getResponseTimeByChannel(@RequestBody TrendsReportRequest request,
                                                                               @RequestHeader(name = "x-business-number") Long accountNum) {
        request.setEnterpriseId(accountNum);
        return new ResponseEntity<>(socialSlaReportService.getResponseTimeByChannel(request), HttpStatus.OK);
    }

    @PutMapping("/response-overview")
    public ResponseEntity<TrendsOverviewResponseDTO> getResponseOverview(@RequestBody TrendsReportRequest request,
                                                                         @RequestHeader(name = "x-business-number") Long accountNum,
                                                                         @RequestHeader(name = "account-id") Integer accountId,
                                                                         @RequestParam(value = "startIndex", required = false, defaultValue = "0") Integer startIndex,
                                                                         @RequestParam(value = "pageSize", required = false, defaultValue = "100") Integer pageSize,
                                                                         @RequestParam(value = "sortParam", required = false, defaultValue = "responseTime") String sortParam,
                                                                         @RequestParam(value = "sortOrder", required = false, defaultValue = "asc") String sortOrder) throws IOException {
        updateReportFilter(request,startIndex,sortParam,sortOrder,pageSize,accountNum,accountId);
        request.setEnterpriseId(accountNum);
        return new ResponseEntity<>(socialSlaReportService.getTrendsResponseOverview(request), HttpStatus.OK);
    }

    @PutMapping("/location-leaderboard")
    public ResponseEntity<TrendsLocationLeaderboardResponse> getLocationLeaderboardData(@RequestBody TrendsReportRequest request,
                                                                                        @RequestHeader(name = "x-business-number") Long accountNum,
                                                                                        @RequestHeader(name = "account-id") Integer accountId, // AccountId or businessId
                                                                                        @RequestParam(value = "startIndex", required = false, defaultValue = "0") Integer startIndex,
                                                                                        @RequestParam(value = "pageSize", required = false, defaultValue = "100") Integer pageSize,
                                                                                        @RequestParam(value = "sortParam", required = false, defaultValue = "responseRate") String sortParam,
                                                                                        @RequestParam(value = "sortOrder", required = false, defaultValue = "desc") String sortOrder) throws IOException {
        updateReportFilter(request,startIndex,sortParam,sortOrder,pageSize,accountNum, accountId);
        return new ResponseEntity<>(socialSlaReportService.getLocationLeaderboardData(request), HttpStatus.OK);
    }
}
