package com.birdeye.social.controller.Doup;

import com.birdeye.social.constant.DoupControllerURIConstants;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.LocationReportRequest;
import com.birdeye.social.response.DoupDownloadResponse;
import com.birdeye.social.response.PdfDataDownloadResponse;
import com.birdeye.social.service.DoupDownloadService;
import com.birdeye.social.trends.TrendsReportRequest;
import com.birdeye.social.utils.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The DoupController containes wrappers which fetches data from Social in format desired by Doup
 *
 * <AUTHOR>
 */

@Slf4j
@RestController
@RequestMapping("/social/insights")
public class DoupController {

    @Autowired
    private DoupDownloadService doupDownloadService;

    @PostMapping(DoupControllerURIConstants.FOLLOWER_URI)
    public ResponseEntity<DoupDownloadResponse<? extends PageInsightDataPoint>> getFollowerData(@RequestBody InsightsRequest insightsRequest,
                                                                                                @PathVariable("channel") String channel,
                                                                                                @RequestParam(value = "sort-by", required = false) String sortBy,
                                                                                                @RequestParam(value = "sort-order", required = false) String sortOrder) throws Exception {
        log.info("[DoupDownloadController] Received Request to fetch Follower Data for channel:{}",channel);
        DoupDownloadResponse<? extends PageInsightDataPoint> response = doupDownloadService.downloadExcelReportForExcel(insightsRequest, channel, sortBy, sortOrder);
        return new ResponseEntity<>( response, HttpStatus.OK);
    }

    @PostMapping(DoupControllerURIConstants.ENGAGEMENT_RATE_URI)
    public ResponseEntity<DoupDownloadResponse<? extends PageInsightDataPoint>> getEngagementRateData(@RequestBody InsightsRequest insightsRequest,
                                                                                                      @PathVariable("channel") String channel,
                                                                                                      @RequestParam(value = "sort-by", required = false) String sortBy,
                                                                                                      @RequestParam(value = "sort-order", required = false) String sortOrder) throws Exception {
        log.info("[DoupDownloadController] Received Request to fetch Engagement Rate Data for channel:{}",channel);
        DoupDownloadResponse<? extends PageInsightDataPoint> response = doupDownloadService.downloadExcelReportForExcel(insightsRequest, channel, sortBy, sortOrder);
        return new ResponseEntity<>( response, HttpStatus.OK);
    }

    @PostMapping(DoupControllerURIConstants.POST_METRIC_URI)
    public ResponseEntity<DoupDownloadResponse<? extends PageInsightDataPoint>> getPostMetricData(@RequestBody InsightsRequest insightsRequest,
                                                                                                  @PathVariable("channel") String channel,
                                                                                                  @RequestParam(value = "sort-by", required = false) String sortBy,
                                                                                                  @RequestParam(value = "sort-order", required = false) String sortOrder) throws Exception {
        log.info("[DoupDownloadController] Received Request to fetch Post-Metric Data for channel:{}",channel);
        DoupDownloadResponse<? extends PageInsightDataPoint> response = doupDownloadService.downloadExcelReportForExcel(insightsRequest, channel, sortBy, sortOrder);
        return new ResponseEntity<>( response, HttpStatus.OK);
    }

    @PostMapping(DoupControllerURIConstants.LIKE_URI)
    public ResponseEntity<DoupDownloadResponse<? extends PageInsightDataPoint>> getLikesData(@RequestBody InsightsRequest insightsRequest,
                                                                                             @PathVariable("channel") String channel,
                                                                                             @RequestParam(value = "sort-by", required = false) String sortBy,
                                                                                             @RequestParam(value = "sort-order", required = false) String sortOrder) throws Exception {
        log.info("[DoupDownloadController] Received Request to fetch Likes Data for channel:{}",channel);
        DoupDownloadResponse<? extends PageInsightDataPoint> response = doupDownloadService.downloadExcelReportForExcel(insightsRequest, channel, sortBy, sortOrder);
        return new ResponseEntity<>( response, HttpStatus.OK);
    }

    @PostMapping(DoupControllerURIConstants.FOLLOWER_URI_PDF)
    public ResponseEntity<PdfDataDownloadResponse<? extends PageInsightsResponse>> getFollowerDataForPdf(@RequestBody InsightsRequest insightsRequest,
                                                                                                         @PathVariable("channel") String channel,
                                                                                                         @RequestParam(value = "sort-by", required = false) String sortBy,
                                                                                                         @RequestParam(value = "sort-order", required = false) String sortOrder,
                                                                                                         @RequestParam(value = "page-size", required = false) Integer pageSize,
                                                                                                         @RequestParam(value = "start-index", required = false) Integer startIndex) throws Exception {
        log.info("[DoupDownloadController] PDF Received Request to fetch Follower Data for channel:{}",channel);
        PdfDataDownloadResponse<? extends PageInsightsResponse> response = doupDownloadService.downloadExcelReportForPdf(insightsRequest, channel, sortBy, sortOrder, pageSize, startIndex);
        return new ResponseEntity<>( response, HttpStatus.OK);
    }

    @PostMapping(DoupControllerURIConstants.ENGAGEMENT_RATE_URI_PDF)
    public ResponseEntity<PdfDataDownloadResponse<? extends PageInsightsResponse>> getEngagementRateDataForPdf(@RequestBody InsightsRequest insightsRequest,
                                                                                                               @PathVariable("channel") String channel,
                                                                                                               @RequestParam(value = "sort-by", required = false) String sortBy,
                                                                                                               @RequestParam(value = "sort-order", required = false) String sortOrder,
                                                                                                               @RequestParam(value = "page-size", required = false) Integer pageSize,
                                                                                                               @RequestParam(value = "start-index", required = false) Integer startIndex) throws Exception {
        log.info("[DoupDownloadController] PDF Received Request to fetch Engagement Rate Data for channel:{}",channel);
        PdfDataDownloadResponse<? extends PageInsightsResponse> response = doupDownloadService.downloadExcelReportForPdf(insightsRequest, channel, sortBy, sortOrder, pageSize, startIndex);
        return new ResponseEntity<>( response, HttpStatus.OK);
    }

    @PostMapping(DoupControllerURIConstants.POST_METRIC_URI_PDF)
    public ResponseEntity<PdfDataDownloadResponse<? extends PageInsightsResponse>> getPostMetricDataForPdf(@RequestBody InsightsRequest insightsRequest,
                                                                                                           @PathVariable("channel") String channel,
                                                                                                           @RequestParam(value = "sort-by", required = false) String sortBy,
                                                                                                           @RequestParam(value = "sort-order", required = false) String sortOrder,
                                                                                                           @RequestParam(value = "page-size", required = false) Integer pageSize,
                                                                                                           @RequestParam(value = "start-index", required = false) Integer startIndex) throws Exception {
        log.info("[DoupDownloadController] PDF Received Request to fetch Post-Metric Data for channel:{}",channel);
        PdfDataDownloadResponse<? extends PageInsightsResponse> response = doupDownloadService.downloadExcelReportForPdf(insightsRequest, channel, sortBy, sortOrder, pageSize, startIndex);
        return new ResponseEntity<>( response, HttpStatus.OK);
    }

    @PostMapping(DoupControllerURIConstants.LIKE_URI_PDF)
    public ResponseEntity<PdfDataDownloadResponse<? extends PageInsightsResponse>> getLikesDataforPdf(@RequestBody InsightsRequest insightsRequest,
                                                                                                      @PathVariable("channel") String channel,
                                                                                                      @RequestParam(value = "sort-by", required = false) String sortBy,
                                                                                                      @RequestParam(value = "sort-order", required = false) String sortOrder,
                                                                                                      @RequestParam(value = "page-size", required = false) Integer pageSize,
                                                                                                      @RequestParam(value = "start-index", required = false) Integer startIndex) throws Exception {
        log.info("[DoupDownloadController] PDF Received Request to fetch Likes Data for channel:{}",channel);
        PdfDataDownloadResponse<? extends PageInsightsResponse> response = doupDownloadService.downloadExcelReportForPdf(insightsRequest, channel, sortBy, sortOrder, pageSize, startIndex);
        return new ResponseEntity<>( response, HttpStatus.OK);
    }

    @PostMapping(DoupControllerURIConstants.PERFORMANCE_REPORT)
    public ResponseEntity<DoupDownloadResponse<? extends ProfilePerformanceExcelResponse> > getEngagementRateData(@RequestBody InsightsRequest insightsRequest,
                                                                                                                  @RequestParam(value = "sort-by", required = false) String sortBy,
                                                                                                                  @RequestParam(value = "sort-order", required = false) String sortOrder) throws Exception {
        log.info("[DoupDownloadController] Received Request to fetch profile performance Data for request: {}", insightsRequest);
        DoupDownloadResponse<? extends ProfilePerformanceExcelResponse> response = doupDownloadService.downloadExcelReportForPerformance(insightsRequest, sortBy, sortOrder);
        return new ResponseEntity<>( response, HttpStatus.OK);
    }

    @PostMapping(DoupControllerURIConstants.ANALYZE_REPORT)
    public ResponseEntity<DoupDownloadResponse<? extends AnalyzeTabDataResponse>> getAnalayzeTabData(@RequestBody InsightsRequest insightsRequest,
                                                                                           @PathVariable("channel") String channel,
                                                                                           @RequestParam(value = "sort-by", required = false, defaultValue = "engagement") String sortBy,
                                                                                           @RequestParam(value = "sort-order", required = false, defaultValue = "desc") String sortOrder,
                                                                                           @RequestParam(value = "page-size", required = false, defaultValue = "5") Integer pageSize,
                                                                                           @RequestParam(value = "start-index", required = false, defaultValue = "0") Integer startIndex) {
        log.info("[getAnalayzeTabData] request received  to download data for analyze tab for channel:{}, insightsRequest :{},sortBy:{}, sortOrder:{}, pageSize:{},startIndex:{}"
                ,channel, JSONUtils.toJSON(insightsRequest),sortBy,sortOrder,pageSize,startIndex);
        pageSize = 500;
        DoupDownloadResponse<? extends AnalyzeTabDataResponse> response = doupDownloadService.downloadExcelAnalyzeData(insightsRequest, channel, sortBy, sortOrder, pageSize, startIndex);
        return new ResponseEntity<>( response, HttpStatus.OK);
    }

    @PostMapping(DoupControllerURIConstants.SLA_REPORT)
    public ResponseEntity<DoupDownloadResponse<? extends Object>> getSLAReportData(@RequestBody TrendsReportRequest insightsRequest,
                                                                                     @RequestHeader(name = "account-id") Integer accountId,
                                                                                     @RequestParam(value = "sort-by", required = false) String sortBy,
                                                                                     @RequestParam(value = "sort-order", required = false) String sortOrder,
                                                                                     @RequestParam(value = "page-size", required = false, defaultValue = "100") Integer pageSize,
                                                                                     @RequestParam(value = "start-index", required = false, defaultValue = "0") Integer startIndex) throws Exception {
        updateReportFilter(insightsRequest, startIndex, sortBy, sortOrder, pageSize, accountId);
        log.info("[DoupDownloadController] Received Request to fetch SLA report Data for request: {}", insightsRequest);
        DoupDownloadResponse<? extends Object> response = doupDownloadService.downloadExcelReportForSLA(insightsRequest,  pageSize, startIndex, sortBy, sortOrder);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    @PostMapping(DoupControllerURIConstants.EXECUTIVE_SUMMARY)
    public ResponseEntity<DoupDownloadResponse<? extends ExecutiveSummaryResponse>> getExecutiveSummaryData(@RequestBody ExecutiveSummaryRequest executiveSummaryRequest){
        log.info("[Executive Summary] request received to download data for request :{}",executiveSummaryRequest);
        DoupDownloadResponse<? extends ExecutiveSummaryResponse> response =
                doupDownloadService.downloadExcelReportForExecutiveSummary(executiveSummaryRequest);
        return new ResponseEntity<>( response, HttpStatus.OK);
    }

    @PostMapping(DoupControllerURIConstants.LOCATION_LEADERSHIP)
    public ResponseEntity<DoupDownloadResponse<? extends LeadershipByPostsResponseWrapper>> getLocationLeadership(@RequestBody LocationReportRequest insightsRequest,
                                                                                                             @RequestParam(value = "sort-by", required = false, defaultValue = "post_engagement") String sortBy,
                                                                                                             @RequestParam(value = "sort-order", required = false, defaultValue = "desc") String sortOrder,
                                                                                                             @RequestParam(value = "page-size", required = false, defaultValue = "5") Integer pageSize,
                                                                                                             @RequestParam(value = "start-index", required = false, defaultValue = "0") Integer startIndex) {
        log.info("[Location Leadership] request received to download data for request :{}", insightsRequest);
        DoupDownloadResponse<? extends LeadershipByPostsResponseWrapper> response =
                doupDownloadService.downloadExcelReportForLocationSummary(insightsRequest, sortBy, sortOrder, pageSize, startIndex);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    private void updateReportFilter(TrendsReportRequest request, Integer startIndex, String sortParam, String sortOrder,Integer pageSize, Integer accountId) {
        request.setStartIndex(startIndex);
        request.setSortParam(sortParam);
        request.setSortOrder(sortOrder);
        request.setPageSize(pageSize);
        request.setAccountId(accountId);
    }

    @PostMapping(DoupControllerURIConstants.CHANNEL_SPECIFIC_REPORT_URI)
    public ResponseEntity<Map<String,List<ProfilePerformanceExcelResponse>>> downloadExcelReportForSpecificTab(@RequestBody InsightsRequest insightsRequest,
                                                                                               @PathVariable("channel") String channel) throws Exception {
        log.info("[DoupDownloadController] EXCEL Received Request to fetch Video-Views Data for channel:{}",channel);
        Map<String, List<ProfilePerformanceExcelResponse>> resopnseMap = new HashMap<>();
        resopnseMap.put("data", doupDownloadService.downloadExcelReportForSpecificTab(insightsRequest, channel));
        return new ResponseEntity<>( resopnseMap, HttpStatus.OK);
    }
}