package com.birdeye.social.controller;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.entities.LocationMovementPostDTO;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.request.linkedin.TargetAudienceResponse;
import com.birdeye.social.external.request.posting.*;
import com.birdeye.social.facebook.FacebookPlacesResponse;
import com.birdeye.social.model.*;
import com.birdeye.social.model.Youtube.YoutubeCategory.YoutubeCategory;
import com.birdeye.social.model.Youtube.YoutubePlaylist.YoutubePlaylist;
import com.birdeye.social.model.apple.AppleLiveCheckRequest;
import com.birdeye.social.model.approval_workflow.*;
import com.birdeye.social.model.tiktok.TikTokHashtagResponse;
import com.birdeye.social.service.*;
import com.birdeye.social.service.applePost.SocialPostAppleService;
import com.birdeye.social.service.approvalworkflow.ApprovalWorkflowService;
import com.birdeye.social.service.drafts.SocialPostDraftService;
import com.birdeye.social.sro.LocationMappingRequest;
import com.birdeye.social.sro.SocialTagEntityMappingActionEvent;
import com.birdeye.social.sro.applePost.AppleLocationStatusResponse;
import com.birdeye.social.sro.bulkupload.BulkUploadDraftRequest;
import com.birdeye.social.sro.bulkupload.PostInfoRequest;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.validation.MasterPostInputValidator;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.Validator;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@Validated
@RequestMapping("/social/post")
public class SocialSitePostingController {

	@Autowired
	private SocialPostService			socialPostService;

	@Autowired
	private SocialPostCalendarService socialPostCalendarService;

	@Autowired
	private SocialPostLinkedinService	socialPostLinkedInService;

	@Autowired
	private SocialPostGooglePlusService	socialPostGooglePlusService;

	@Autowired
	private SocialPostFacebookService	socialPostFacebookService;

	@Autowired
	private SocialPostTwitterService	socialPostTwiterService;

	@Autowired
	private ReviewSharingService		reviewSharingService;

	@Autowired
	private SocialReconnectService		reconnectService;
	
	@Autowired
	private FacebookSocialAccountService fbSocialAccountService;

	@Autowired
	private SocialPostDraftService socialPostDraftService;

	@Qualifier("postInputValidator")
	@Autowired
	private Validator postInputValidator;

	@Autowired
	private MasterPostInputValidator masterPostInputValidator;

	@Autowired
	private SocialPostActivityService socialPostActivityService;

	@Autowired
	PostDeleteRequestService postDeleteRequestService;
	@Autowired
	private SocialBulkScheduleService socialBulkScheduleService;


	@Autowired
	private ApprovalWorkflowService approvalWorkflowService;

	@Autowired
	private SocialPostAppleService postAppleService;

	private static final Logger LOG = LoggerFactory.getLogger(SocialSitePostingController.class);


	@PostMapping(value = "/")
	public @ResponseBody ResponseEntity<Void> postOnSocialSites(@RequestParam("businessId") Integer businessId ,@RequestParam("userId") Integer userId,
																@RequestBody SocialPostInputMessage socialPost,
																@RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception {
		socialPostService.postOnSocialSites(businessId,userId, socialPost, requestSource);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping(value = "/schedule/acknowledge")
	public @ResponseBody ResponseEntity<Void> postScheduleAcknowledgment(@RequestBody SamayAcknowledgementRequest data) {
		socialPostService.postScheduleAcknowledgment(data);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@Deprecated
	@PostMapping(value = "/create/schedule")
	public @ResponseBody ResponseEntity<Void> scheduledPostOnSocialSites(@Valid @RequestBody SocialPostInputMessageRequest socialPost,
																		 @RequestHeader(Constants.BUSINESS_ID) Integer businessId,
																		 @RequestHeader(Constants.USER_ID) Integer userId,
																		 @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception {
		if(Objects.isNull(socialPost) || !Objects.equals(socialPost.getBusinessId(),businessId)){
			LOG.info("Not a valid request : {} with business id : {}",socialPost,businessId);
			return new ResponseEntity<>(HttpStatus.OK);
		}
		postInputValidator.validate(socialPost, null);
		LOG.info("Received request to post with object : {}", socialPost);
		socialPostService.scheduledPostOnSocialSites(socialPost, null, null, null,false, requestSource);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping(value = "/immediate")
	public @ResponseBody ResponseEntity<Void> postImmediately(@Valid @RequestBody SocialPostInputMessageRequest socialPost,
															  @RequestHeader(Constants.BUSINESS_ID) Integer businessId,
															  @RequestHeader(Constants.USER_ID) Integer userId,
															  @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception{
		if(Objects.isNull(socialPost) || !Objects.equals(socialPost.getBusinessId(),businessId)){
			LOG.info("Not a valid request : {} with business id : {}",socialPost,businessId);
			return new ResponseEntity<>(HttpStatus.OK);
		}
		postInputValidator.validate(socialPost, null);
		LOG.info(" Received request to post immediate flow with post object : {}", socialPost);
		socialPostService.immediatePostOnSocialSites(socialPost, null, null, null, false, requestSource);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	/**
	 * topic - social-post-schedule-topic
	 * @param externalIds
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "/get/schedule")
	public @ResponseBody ResponseEntity<Void> processDataForScheduledPosts(@RequestBody SocialPostScheduleIdsRequest externalIds) throws Exception {
		LOG.info("Received request to send post to submit with post object : {}", externalIds);
		socialPostService.processDataForScheduledPosts(externalIds, true, null);
		return new ResponseEntity<>(HttpStatus.OK);

	}

	/**
	 * topic - social-post-publish-event
	 * @param data
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "/submit")
	public @ResponseBody ResponseEntity<Void> postDataOnSocialSites(@RequestBody @Valid SocialPostPublishInfoRequest data,
																	@RequestParam(value = "channel", required = false) String channel) throws Exception {
		LOG.info(" Received request to submit with post object : {}", data);
		socialPostService.postDataOnSocialSites(data);
		return new ResponseEntity<>(HttpStatus.OK);

	}

	@PostMapping (value = "/schedule/{postId}/edit")
	public @ResponseBody ResponseEntity<SocialScheduleEditPostResponse> getSchedulerPostData(@PathVariable("postId") Integer postId, @RequestParam(value = "type",required = false) String type,
																							 @RequestParam(value = "srcId",required = false) Integer srcId,
																							 @RequestBody BusinessIdRequest businessIdRequest) throws Exception {
		LOG.info(" Received request to edit post with postId : {}, type: {} and request body: {}", postId, type, businessIdRequest);
		return new ResponseEntity<>(socialPostService.getSchedulerPostData(postId, type, srcId, businessIdRequest), HttpStatus.OK);

	}

	@PostMapping (value = "/schedule/{postId}/edit/reseller")
	public @ResponseBody ResponseEntity<SocialScheduleEditPostResponse> getSchedulerPostDataForReseller(@PathVariable("postId") Integer postId, @RequestParam(value = "type",required = false) String type,
																							 @RequestParam(value = "srcId",required = false) Integer srcId,
																							 @RequestBody BusinessIdRequest businessIdRequest) throws Exception {
		LOG.info(" Received request to edit post with postId : {}, type: {} and request body: {}", postId, type, businessIdRequest);
		return new ResponseEntity<>(socialPostService.getSchedulerPostData(postId, type, srcId, businessIdRequest), HttpStatus.OK);

	}

	@PostMapping (value = "/schedule/edit")
	public @ResponseBody ResponseEntity<EditPostResponse> editSchedulerPostData(@RequestBody SocialPostInputMessageRequest socialPost,
																				@RequestHeader(Constants.BUSINESS_ID) Integer businessId,
																				@RequestHeader(Constants.USER_ID) Integer userId,
																				@RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception {
		if(Objects.isNull(socialPost) || !Objects.equals(socialPost.getBusinessId(),businessId)){
			LOG.info("Not a valid request : {} with business id : {}",socialPost,businessId);
			return new ResponseEntity<>(HttpStatus.OK);
		}
		postInputValidator.validate(socialPost, null);
		LOG.info(" Received request to remove post with post Object : {}", socialPost);
		return new ResponseEntity<>(socialPostService.editSchedulerPostData(socialPost, null, null,false, requestSource),HttpStatus.OK);

	}

	@GetMapping (value = "/schedule/{postId}/{businessId}/now")
	public @ResponseBody ResponseEntity<Void> schedulerPostNowData(@PathVariable("postId") Integer postId, @PathVariable("businessId") Integer businessId,
																   @RequestParam("activity") String activity,
																   @RequestHeader("user-id") Integer userId) throws Exception {
		LOG.info(" Received request to post with post Object : {}", postId);
		socialPostService.schedulerPostNowData(postId, businessId, activity, userId);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping (value = "/schedule/{postId}/{businessId}/now")
	public @ResponseBody ResponseEntity<Void> schedulerPostNowDataV2(@RequestBody AccessibleLocationIdsInput accessibleLocationIdsInput,
																	 @PathVariable("postId") Integer postId, @PathVariable("businessId") Integer businessId,
																	 @RequestParam("activity") String activity,
																	 @RequestHeader("user-id") Integer userId,
																	 @RequestHeader(Constants.BUSINESS_NUMBER) Long enterpriseId,
																	 @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception {
		LOG.info(" Received request to post with post Object: {} and postId: {}",accessibleLocationIdsInput, postId);
		socialPostService.schedulerPostNowDataV2(accessibleLocationIdsInput, postId, businessId, activity, userId, enterpriseId,requestSource);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping (value = "/draft/{postId}/now")
	public @ResponseBody ResponseEntity<Void> draftPostNowData(@PathVariable("postId") Integer postId,
																   @RequestParam("activity") String activity,
																   @RequestHeader("user-id") Integer userId,
															   @RequestHeader("account-id") Integer businessId,
															   @RequestHeader(Constants.BUSINESS_NUMBER) Long businessNumber,
															   @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception {
		LOG.info(" Received request to post with post Object : {}", postId);
		socialPostService.draftPostNowData(postId, businessId, activity, userId, false, businessNumber, requestSource);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping (value = "/draft/{postId}/now/reseller")
	public @ResponseBody ResponseEntity<Void> draftPostNowDataForReseller(@PathVariable("postId") Integer postId,
															   @RequestParam("activity") String activity,
															   @RequestHeader("user-id") Integer userId,
															   @RequestHeader("account-id") Integer businessId,
																@RequestHeader(Constants.BUSINESS_NUMBER) Long businessNumber,
																@RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception {
		LOG.info(" Received request to post with post Object : {}", postId);
		socialPostService.draftPostNowData(postId, businessId, activity, userId, true, businessNumber, requestSource);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@DeleteMapping (value = "/schedule/delete/{postId}")
	public @ResponseBody ResponseEntity<Void> removeSchedulerData(@PathVariable("postId") Integer postId, @RequestParam("activity") String activity,
																  @RequestHeader("user-id") Integer userId,
																  @RequestHeader("business-id") Integer businessId) throws Exception {
		LOG.info(" Received request to remove post with postId : {}", postId);
		socialPostService.removeSchedulerData(postId, userId, activity, businessId, false);
		return new ResponseEntity<>(HttpStatus.OK);

	}

	@PostMapping (value = "/schedule/delete/{postId}")
	public @ResponseBody ResponseEntity<Void> removeSchedulerDataV2(@RequestBody AccessibleLocationIdsInput accessibleLocationIdsInput,
																	@PathVariable("postId") Integer postId, @RequestParam("activity") String activity,
																	@RequestHeader("user-id") Integer userId,
																	@RequestHeader("business-id") Integer businessId) throws Exception {
		LOG.info(" Received request to remove post with postId : {} and input: {}", postId, accessibleLocationIdsInput);
		socialPostService.removeSchedulerDataV2(accessibleLocationIdsInput, postId, userId, activity, businessId);
		return new ResponseEntity<>(HttpStatus.OK);

	}

	@PostMapping(value = "/schedule/all/reseller")
	public @ResponseBody ResponseEntity<SocialSchedulePostResponse> getAllScheduledPostsForReseller(@RequestBody GlobalFilterCriteriaSchedulePostMessage filter,
																									@RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String source) throws Exception {
		LOG.info(" Received request to get post with filters : {}", filter);
		SocialSchedulePostResponse posts;
		if(StringUtils.isBlank(source) || !StringUtils.equalsIgnoreCase(source, "dashboard")) {
			posts = socialPostService.getAllScheduledPostsForReseller(filter);
		} else {
			LOG.info("Received request to get reseller posts with source header : {}", source);
			posts = socialPostCalendarService.getAllScheduledPostsForResellerV2(filter);
		}
		//LOG.info(" Received response to send post  : {}", posts);
		return new ResponseEntity<>(posts, HttpStatus.OK);
	}

	@PutMapping(value = "/update")
	public @ResponseBody ResponseEntity<Void> updatePost(@RequestParam("businessId") Integer businessId ,@RequestParam("userId") Integer userId,
														 @RequestBody SocialPostInputMessage socialPost,
														 @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception {
		socialPostService.updatePost(businessId, userId, socialPost, requestSource);
		return new ResponseEntity<>(HttpStatus.OK);
		
	}
	
	@DeleteMapping(value = "/delete/{postId}")
	public @ResponseBody ResponseEntity<Void> deletePost(@PathVariable("postId") Integer postId) throws Exception {
		socialPostService.deletePost(postId);
		return new ResponseEntity<>(HttpStatus.OK);
		
	}
	
	@PostMapping(value = "/all")
	public @ResponseBody ResponseEntity<PostMessageOutput> getAllPosts(@RequestParam("businessId") Integer businessId ,@RequestParam("userId") Integer userId,
																	   @RequestBody GlobalFilterCriteriaMessage filter,
			@RequestParam("count") Integer count) throws Exception {
		PostMessageOutput posts = socialPostService.getAllPosts(businessId, userId, filter, count);
		return new ResponseEntity<>(posts, HttpStatus.OK);
	}
	
	@GetMapping(value = "/get/config")
	public @ResponseBody ResponseEntity<Map<String, List<SocialPageInfo>>> getConfig(@RequestParam("businessId") Integer businessId ,@RequestParam("userId") Integer userId) throws Exception {
		Map<String, List<SocialPageInfo>> appStatus = socialPostService.getConfig(businessId,userId);
		return new ResponseEntity<>(appStatus, HttpStatus.OK);
	}
	
	@GetMapping(value = "/linkedin/authurl")
	public @ResponseBody ResponseEntity<Map<String, String>> getAuthorizationUrl(@RequestParam("businessId") Integer businessId, @RequestParam("redirect_uri") String redirectUri)
			throws Exception {
		String authUrl = socialPostLinkedInService.getAuthorizationUrl(businessId, redirectUri);
		Map<String, String> authMap = new HashMap<>();
		authMap.put("authUrl", authUrl);
		return new ResponseEntity<>(authMap, HttpStatus.OK);
	}
	
	@GetMapping(value = "/linkedin/accesstoken")
	@Deprecated
	public @ResponseBody ResponseEntity<LinkedinInstallInfo> getAccessToken(@RequestHeader("X-Bazaarify-Session-Token") String sessionToken, @RequestParam("code") String code,
			@RequestParam("redirect_uri") String redirectUri) throws Exception {
		LinkedinInstallInfo linkedinInstallInfo = socialPostLinkedInService.getAccessToken(sessionToken, code, redirectUri);
		return new ResponseEntity<>(linkedinInstallInfo, HttpStatus.OK);
	}
	
	@PutMapping(value = "/linkedin/update/pageinfo")
	@Deprecated
	public @ResponseBody ResponseEntity<Void> saveProfileAndCompanyPageInfo(@RequestHeader("X-Bazaarify-Session-Token") String sessionToken, @RequestBody LinkedinInstallInfo linkedInInfo)
			throws Exception {
		socialPostLinkedInService.saveProfileAndCompanyPageInfo(sessionToken, linkedInInfo);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@GetMapping(value = "/assets")
	public @ResponseBody ResponseEntity<SocialPostAssets> getAssets(@RequestParam("businessId") Integer businessId) throws Exception {
		SocialPostAssets assets = socialPostService.getAssets(businessId);
		return new ResponseEntity<>(assets, HttpStatus.OK);
	}
	
	@GetMapping(value = "/all/accounts")
	public @ResponseBody ResponseEntity<List<InstalledSocialAccounts>> getAccounts(@RequestParam("businessId") Integer businessId ,@RequestParam("userId") Integer userId) throws Exception {
		List<InstalledSocialAccounts> accounts = socialPostService.getAllAddedAccounts(businessId,userId);
		return new ResponseEntity<>(accounts, HttpStatus.OK);
	}
	
	@PostMapping(value = "/update/account")
	public ResponseEntity<Void> setChannelSettings(@RequestParam("businessId") Integer businessId, @RequestParam(name = "channel") String channel,
			@RequestParam(name = "enabled") boolean enabled, @RequestParam(name = "minrating") Integer minRating, @RequestParam(name = "pageid") String pageId) {
		socialPostService.setAccountConfigurations(businessId, pageId, minRating, enabled, channel);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@GetMapping(value = "/facebook/pageinfo")
	public @ResponseBody ResponseEntity<FacebookInstallInfo> getFbPageInfo(@RequestParam("businessId") Integer businessId, @RequestParam("tempAccessToken") String tempAccessToken)
			throws Exception {
		FacebookInstallInfo fbInstallInfo = socialPostFacebookService.getFacebookPagesInfo(businessId, tempAccessToken);
		return new ResponseEntity<>(fbInstallInfo, HttpStatus.OK);
	}
	
	@PostMapping(value = "/facebook/pageinfo/next")
	public @ResponseBody ResponseEntity<FacebookInstallInfo> getFbPageInfo(@RequestBody FacebookPaginationInput input) throws Exception {
		FacebookInstallInfo fbInstallInfo = socialPostFacebookService.getFacebookPagesInfo(input.getNext());
		return new ResponseEntity<>(fbInstallInfo, HttpStatus.OK);
	}
	
	@PostMapping(value = "/facebook/update/pageinfo")
	public @ResponseBody ResponseEntity<Void> updateFbPageInfo(@RequestParam("businessId") Integer businessId, @RequestBody FacebookInstallInfo fbInstallInfo) throws Exception {
		socialPostFacebookService.saveAndUpdateFbPageInfo(businessId, fbInstallInfo);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@DeleteMapping(value = "/delete/account/{id}")
	public @ResponseBody ResponseEntity<Void> deleteAccount(@RequestParam("businessId") Integer businessId, @PathVariable("id") String id, @RequestParam("channel") String channel)
			throws Exception {
		socialPostService.deleteAccount(businessId, id, channel);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@PostMapping(value = "/review")
	@ResponseBody
	public ResponseEntity<ActivityResponse> reviewSharing(@RequestBody SocialPostInputMessage socialPost) {
		ActivityResponse response = socialPostService.shareReview(socialPost);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	@PostMapping(value = "/review/all")
	@ResponseBody
	public ResponseEntity<List<ActivityResponse>> reviewSharingForBusiness(@RequestBody SocialPostInputMessage socialPost,
																		   @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) {
		List<ActivityResponse> response = reviewSharingService.shareReview(socialPost, requestSource);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@Deprecated
	@PostMapping(value = "/review/template")
	@ResponseBody
	public ResponseEntity<Void> shareReviewWithTemplate(@RequestBody ReviewRequest reviewRequest) {
		reviewSharingService.shareReviewWithTemplate(reviewRequest);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping(value = "/review/media")
	@ResponseBody
	public ResponseEntity<Void> shareReviewWithMedia(@RequestBody ReviewRequestMedia reviewRequestMedia) {
		reviewSharingService.shareReviewWithMedia(reviewRequestMedia);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	// @duplicate --master post api is created for this
	@PostMapping(value = "/review/text")
	@ResponseBody
	public ResponseEntity<Void> shareReviewWithoutTemplate(@RequestBody SocialPostInputMessage socialPost,
														   @RequestHeader(value = Constants.BUSINESS_NUMBER,required = false) Long enterpriseId,
														   @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) {
		reviewSharingService.shareReviewWithoutTemplate(socialPost, enterpriseId, requestSource);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping(value = "/master-post/review/text")
	@ResponseBody
	public ResponseEntity<Void> shareMasterReviewWithoutTemplate(@RequestBody SocialPostInputMessage socialPost, @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) {
		reviewSharingService.shareMasterReviewWithoutTemplate(socialPost, requestSource);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	/*
	 * API to relink facebook page when user token expires or fb page owner change
	 */
	@SuppressWarnings("deprecation")
	@PostMapping(value = "/facebook/relink/page")
	public @ResponseBody ResponseEntity<Void> linkFacebookAccount(@RequestParam("businessId") Integer businessId, @RequestBody ReconnectAccountRequest request) throws Exception {
		socialPostFacebookService.relinkFacebookPage(businessId, request.getTempAccessToken(), request.getFbPageId());
		return new ResponseEntity<>(HttpStatus.OK);
	}

//	@Varun ask from where this api is called : no usage found
	@PutMapping(value = "/facebook/update/pagestatus/{id}")
	public @ResponseBody ResponseEntity<Boolean> updateFbPageStatus(@PathVariable("id") Integer id) throws Exception {
		Boolean status = socialPostFacebookService.updateFacebookPageStatusById(id);
		return new ResponseEntity<>(status, HttpStatus.OK);
	}

	/**
	 * Generates instagram notifications stream for mobile apps : pagination
	 *
	 *
	 * @param pageNo
	 * @param pageSize
	 * @return
	 */
	@GetMapping(value = "/instagram/notifications")
	public ResponseEntity<SocialNotificationResponse> getPushNotificationsStreamForABusiness(@RequestParam("businessId") Integer businessId,
			@RequestParam(name = "pageNo", required = false, defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", required = false, defaultValue = "4") Integer pageSize) {
		SocialNotificationResponse response = socialPostService.getNotifications(businessId, pageNo, pageSize);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	/**
	 *
	 * MARK INSTAGRAM POST AS PUBLISHED
	 *
	 *
	 * @param postid
	 * @return
	 */
	@PutMapping(value = "/instagram/acknowledge/{postid}/{status}")
	public ResponseEntity<Map<String, Boolean>> toggleInstagramStatus(@PathVariable("postid") Integer postid,
			@PathVariable("status") Integer status) {
		boolean success = socialPostService.toggleInstagramPublishingStatus(postid, status);
		Map<String, Boolean> statusMap = new HashMap<>();
		statusMap.put("success", success);
		return new ResponseEntity<>(statusMap, HttpStatus.OK);
	}
	
	/**
	 *
	 * MARK INSTAGRAM POST AS PUBLISHED
	 *
	 *
	 * @return
	 * @throws Exception
	 */
	@GetMapping(value = "/search/places")
	public ResponseEntity<FacebookPlacesResponse> getFacebookPlaces(@RequestParam("businessId") Integer businessId, @RequestParam("q") String queryParam) throws Exception {
		FacebookPlacesResponse response = socialPostFacebookService.getFacebookPlacesResponse(businessId, queryParam);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	/**
	 * Show reconnect logo on review tab
	 *
	 * @param sessionToken
	 * @return true if business has disconnected social pages
	 */
	@GetMapping(value = "/reconnect/required")
	public @ResponseBody ResponseEntity<Map<String, Object>> getReconnectConfig(@RequestHeader("X-Bazaarify-Session-Token") String sessionToken) {
		boolean showReconnect = reconnectService.showSocialDisconnected(sessionToken);
		Map<String, Object> map = new HashMap<>();
		map.put("disconnected", showReconnect);
		return new ResponseEntity<>(map, HttpStatus.OK);
	}
	
	/**
	 * Get all disconnected pages info
	 *
	 * @param sessionToken
	 * @return
	 */
	@GetMapping(value = "/reconnect/info")
	public @ResponseBody ResponseEntity<List<SocialPageInfo>> getDisconnectedPages(@RequestHeader("X-Bazaarify-Session-Token") String sessionToken) {
		List<SocialPageInfo> response = reconnectService.getInvalidPages(sessionToken);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	/**
	 * Get all disconnected pages info v1
	 *
	 * @param sessionToken
	 * @return
	 */
	@GetMapping(value = "/reconnect/info/v1")
	public @ResponseBody ResponseEntity<Map<String, SocialInvalidPageInfo>> getDisconnectedPagesAlert(@RequestHeader("X-Bazaarify-Session-Token") String sessionToken) {
		Map<String, SocialInvalidPageInfo> response = reconnectService.getInvalidPagesAlert(sessionToken);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	/**
	 * Reconnect flow for social channels. Access token generated after user consents are entered is passed in request.
	 *
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "/{channel}/reconnect/page")
	public @ResponseBody ResponseEntity<Void> linkSocialAccount(@RequestHeader("X-Bazaarify-Session-Token") String sessionToken, @RequestBody ReconnectAccountRequest request, @PathVariable("channel") String channel) throws Exception {
		reconnectService.reconnectSocialChannel(request, channel, sessionToken);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@GetMapping(value = "/twitter/url")
	public @ResponseBody ResponseEntity<String> getTwitterAuthUrl(@RequestHeader("X-Bazaarify-Session-Token") String sessionToken) throws Exception {
		String redirectUrl = socialPostTwiterService.getRedirectUrl(sessionToken);
		return new ResponseEntity<>(redirectUrl, HttpStatus.OK);
	}
	
	@PostMapping(value = "/twitter/auth")
	public @ResponseBody ResponseEntity<Void> generateTwitterAuthCode(@RequestHeader("X-Bazaarify-Session-Token") String sessionToken, @RequestParam(name = "verifierToken") String verifierToken,
			@RequestParam(name = "authToken") String authToken, @RequestParam(name = "authSecret") String authSecret) throws Exception {
		socialPostTwiterService.generatedPermanentToken(sessionToken, verifierToken, authToken, authSecret);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@GetMapping(value = "/linkedin/update/profile/info")
	public @ResponseBody ResponseEntity<Void> updateLinkedinPagesWithProfileInformation() {
		socialPostLinkedInService.updateAllLinkedinPagesWithProfileInformation();
		return new ResponseEntity<>(HttpStatus.OK);
	}
	@PostMapping(value = "/extract/metatags")
	public @ResponseBody ResponseEntity<MetatagsResponseDTO> fetchMetatagsFromAUrl(@RequestParam(name = "url") String url) {
		MetatagsResponseDTO response = socialPostService.getMetatagsOfAUrl(url);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
/**
	 * API to update the information present on social pages of GMB & FB
	 * Called by BE Dashboard Profile page to sync BE & Social Pages
	 * @param businessId
	 * @param channel
	 * @param locationInfoMessage
	 * @param update
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "/{channel}/update/page")
	public @ResponseBody ResponseEntity<?> updateSocialPageInfo(@RequestParam("businessId") Integer businessId,
			@PathVariable("channel") String channel,@RequestBody LocationInfoMessage locationInfoMessage,@RequestParam("update") boolean update) throws Exception {
		return new ResponseEntity<>(socialPostService.updateSocialPageInfo(channel,businessId,locationInfoMessage,update), HttpStatus.OK);
	}

	@PostMapping(value = "/{channel}/create/media")
	public @ResponseBody ResponseEntity<?> createSocialPageMedia(@RequestParam("businessId") Integer businessId,
			@PathVariable("channel") String channel,@RequestBody LocationInfoMessage locationInfoMessage) throws Exception {
		return new ResponseEntity<>(socialPostService.createSocialPageMedia(channel,businessId,locationInfoMessage), HttpStatus.OK);
	}

	@PostMapping(value = "/{channel}/delete/media")
	public @ResponseBody ResponseEntity<?> deleteSocialPageMedia(@RequestParam("businessId") Integer businessId,
			@PathVariable("channel") String channel,@RequestParam("gmbImageId") String gmbImageId) throws Exception {
		return new ResponseEntity<>(socialPostService.deleteSocialPageMedia(channel,businessId,gmbImageId), HttpStatus.OK);
	}

	@PutMapping(value = "/{channel}/update/media/category")
	public @ResponseBody ResponseEntity<?> updateSocialMediaCategory(@RequestParam("businessId") Integer businessId,
			@PathVariable("channel") String channel,@RequestBody LocationInfoMessage locationInfoMessage) throws Exception {
		return new ResponseEntity<>(socialPostService.updateSocialMediaCategory(channel,businessId,locationInfoMessage), HttpStatus.OK);
	}

	@GetMapping(value = "/facebook/{pageId}")
	public @ResponseBody ResponseEntity<Map<String, String>> fetchPagePermissionSet(@PathVariable("pageId") String pageId) {
		if (pageId != null) {
			String permissionSet = fbSocialAccountService.fetchPagePermissionSet(pageId);
			Map<String, String> map = new HashMap<>();
			map.put("permissions", permissionSet);
			return new ResponseEntity<>(map, HttpStatus.OK);
		} else {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "pageId can not be null.");
		}
	}
	
	@GetMapping(value = "/facebook/status/{businessId}")
	public @ResponseBody ResponseEntity<Map<String, String>> getFacebookIntegrationStatus(@PathVariable("businessId") Integer businessId) {
		if (businessId != null) {
			String status = fbSocialAccountService.getFacebookIntegrationStatus(businessId);
			Map<String, String> map = new HashMap<>();
			map.put("status", status);
			return new ResponseEntity<>(map, HttpStatus.OK);
		} else {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "businessId can not be null.");
		}
	}

	@PostMapping(value = "/pages")
	public @ResponseBody ResponseEntity<SocialPostPageStatusData> fetchPagesForPost(@RequestBody SocialPostStatusRequest socialPostStatusRequest,
																					@RequestHeader("account-id") @NotNull Integer accountId) {
		LOG.info("Social Post: request received for view details page {}",socialPostStatusRequest );
		return new ResponseEntity<>( socialPostService.fetchPagesForPost(socialPostStatusRequest, accountId, false),HttpStatus.OK);
	}

	@PostMapping(value = "/pages/reseller")
	public @ResponseBody ResponseEntity<SocialPostPageStatusData> fetchPagesForPostForReseller(@RequestBody SocialPostStatusRequest socialPostStatusRequest,
																							   @RequestHeader("account-id") @NotNull Integer accountId) {
		LOG.info("Social Post: request received for view details page {}",socialPostStatusRequest );
		return new ResponseEntity<>( socialPostService.fetchPagesForPost(socialPostStatusRequest, accountId, true),HttpStatus.OK);
	}

	//used it for only for post status update, when isPublished is 1.
	@GetMapping("/google/status")
	public @ResponseBody ResponseEntity<Void> checkForGMBPostStatus() {

		socialPostService.checkForGMBPostStatus();
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping("/google/status/failed")
	public @ResponseBody ResponseEntity<Void> checkForGMBPostStatusForFailedPost() {

		socialPostService.checkForGMBPostStatusForFailedPosts();
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PutMapping("/google/status")
	public @ResponseBody ResponseEntity<Void> checkForGMBPostStatusForPostsInList(@RequestBody List<Integer> publishInfoIds) {

		socialPostService.checkForGMBPostStatusForPostsInList(publishInfoIds);
		return new ResponseEntity<>(HttpStatus.OK);
	}


	// add projection based on input
	// 2. sourceId, businessId, enterpriseId, failureId.
	// add failure reason as well.
	@PostMapping("/retry")
	public @ResponseBody ResponseEntity<Void> retryPosts(@RequestBody SocialPostRetryRequest data,
														 @RequestParam(value = "channel", required = false) String channel) {
		socialPostService.retryPosts(data);
		return new ResponseEntity<>(HttpStatus.OK);
	}

/*	@PostMapping("/retry-loop")
	public @ResponseBody ResponseEntity<Void> retryPosts(@RequestBody List<SocialPostRetryRequest> data) {
		socialPostService.retryPostsLoop(data);
		return new ResponseEntity<>(HttpStatus.OK);
	}*/


	// @duplicate --master post api is created for this
	@PostMapping("/draft")
	public @ResponseBody ResponseEntity<SocialGenericResponse> saveDraftPost(@Valid @RequestBody SocialPostInputMessageRequest postDraftInputMessageRequest) {
		LOG.info("Social Post Draft: request received to save draft post {}", postDraftInputMessageRequest);
		return new ResponseEntity<>(socialPostService.saveDraft(postDraftInputMessageRequest, false), HttpStatus.OK);
	}

	// @duplicate --master post api is created for this
	@PutMapping("/draft")
	public @ResponseBody ResponseEntity<SocialGenericResponse> editDraftPost(@Valid @RequestBody SocialPostInputMessageRequest postDraftInputMessageRequest) {
		LOG.info("Social Post Draft: request received to save draft post {}", postDraftInputMessageRequest);
		return new ResponseEntity<>(socialPostService.editDraft(postDraftInputMessageRequest), HttpStatus.OK);
	}

	// @duplicate --master post api is created for this
	@GetMapping(value = "/draft/{postId}/edit")
	public @ResponseBody ResponseEntity<SocialDraftEditPostResponse> getDraftData(@PathVariable("postId") Integer postId,
																				  @RequestParam("account-id") Integer accountId) throws Exception {
		LOG.info(" Received request to edit draft with draftId : {}", postId);
		return new ResponseEntity<>(socialPostService.getDraftData(postId, accountId, false), HttpStatus.OK);
	}

	// @duplicate --master post api is created for this
	@DeleteMapping(value = "/draft/{postId}")
	public @ResponseBody ResponseEntity<SocialGenericResponse> deleteDraftData(@PathVariable("postId") Integer postId) throws Exception {
		LOG.info(" Received request to delete draft with postId : {}", postId);
		return new ResponseEntity<>(socialPostService.deleteDraft(postId), HttpStatus.OK);
	}

	// @duplicate --master post api is created for this
	@PostMapping("/{businessId}/draft/all")
	public @ResponseBody ResponseEntity<SocialDraftPostListResponse> draftListData(@PathVariable("businessId") Integer businessId,
																				   @RequestBody BusinessIdRequest businessIdRequest) {
		LOG.info(" Received request to fetch draft list with businessId {} and request body: {}", businessId, businessIdRequest);
		return new ResponseEntity<>(socialPostService.draftListData(businessId,businessIdRequest), HttpStatus.OK);
	}

	@PostMapping("/{businessId}/draft/count")
	public @ResponseBody ResponseEntity<SocialDraftPostCountResponse> draftListCount(@PathVariable("businessId") Integer businessId,
																					 @RequestBody BusinessIdRequest businessIdRequest) {
		LOG.info(" Received request to fetch draft list with businessId {} and request body: {}", businessId,businessIdRequest);
		return new ResponseEntity<>(socialPostService.draftListCount(businessId, businessIdRequest), HttpStatus.OK);
	}

	@PostMapping("retry/processing/posts/{channel}")
	public @ResponseBody ResponseEntity<Void> retryProcessingPosts(@PathVariable("channel") String channel) {
		socialPostService.retryProcessingPosts(channel);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping("/linkedin/{pageId}/target")
	public @ResponseBody ResponseEntity<List<TargetAudienceResponse>> getTargetAudienceList(@PathVariable("pageId") String pageId, @RequestParam("category") String category) {
		LOG.info("Received request to fetch list for category: {}",category);
		return new ResponseEntity<>(socialPostService.getTargetAudienceList(pageId,category), HttpStatus.OK);
	}

	@GetMapping("/{businessId}/permission/status")
	public @ResponseBody ResponseEntity<SocialPostPermissionStatusResponse> postPermissionStatus(@PathVariable("businessId") Integer businessId,
																								 @RequestParam(required = false) List<String> modules) {
		LOG.info(" Received request to fetch PermissionStatus with businessId : {} for modules: {}", businessId,modules);
		return new ResponseEntity<>(socialPostService.postPermissionStatus(businessId,modules), HttpStatus.OK);
	}

	@GetMapping("/{channel}/search/mentions")
	public @ResponseBody ResponseEntity<SocialMentionsData> socialMentionSearch(@PathVariable("channel") String channel,
																					 @RequestParam("search") String search,
																					 @RequestParam(value = "businessId") Integer businessId,
																				@RequestParam(value = "enterpriseId") Long enterpriseId,
                                                                                @RequestParam(value = "nextToken", required = false) String nextToken) {
		LOG.info("Request Received to search for channel {} and string {} and businessId {} and enterpriseId {}",channel,search,businessId,enterpriseId);
		return new ResponseEntity<>(socialPostService.searchMentionData(channel, search, businessId,enterpriseId, nextToken), HttpStatus.OK);
	}

	@GetMapping("/{channel}/search/locations")
	public @ResponseBody ResponseEntity<SocialLocationsTagData> socialLocationSearch(@PathVariable("channel") String channel,
																							  @RequestParam("search") String search,
																							  @RequestParam(value = "businessId") Integer businessId){
		LOG.info("Request Received to search for channel {} and string {}",channel,search);
		return new ResponseEntity<>(socialPostService.searchLocationData(channel, search, businessId), HttpStatus.OK);
  }

	@PutMapping(value = "/failure/reason/update/{errorCode}")
	public @ResponseBody ResponseEntity<Integer> updateFailureReason(@PathVariable("errorCode") String errorCode,  @RequestParam("description") String description) {
		Integer updateCount = socialPostService.updateFailureReason(errorCode,description);
		return new ResponseEntity<>(updateCount, HttpStatus.OK);
	}

	@GetMapping(value = "/youtube/{pageId}/category")
	public @ResponseBody ResponseEntity<List<YoutubeCategory>> getYoutubeCategories(@PathVariable("pageId") String pageId) throws Exception{
		LOG.info("Get category for page id: {}",pageId);
		return new ResponseEntity<>(socialPostService.getYoutubeCategories(pageId),HttpStatus.OK);
	}

	@GetMapping(value = "/youtube/{pageId}/playlist")
	public @ResponseBody ResponseEntity<List<YoutubePlaylist>> getYoutubePlaylistForChannel(@PathVariable("pageId") String pageId) throws Exception {
		LOG.info("Get playlist for page id: {}",pageId);
		return new ResponseEntity<>(socialPostService.getPlaylistForChannel(pageId),HttpStatus.OK);
	}
	@PostMapping(value = "/public/schedule")
	public @ResponseBody ResponseEntity<SocialPublicResponsePayload> createPublicSchedule(@RequestHeader("account-id") @NotNull Integer businessId, @Valid @RequestBody SocialPostInputMessagePublicRequest socialPostInputMessagePublicRequest,
																						  @RequestHeader("X-BUSINESS-NUMBER") Long businessNumber,@RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) {
		socialPostInputMessagePublicRequest.setAccountId(businessId);
		LOG.info("createPublicSchedule with payload: {}",socialPostInputMessagePublicRequest);
		SocialPublicResponsePayload responsePayload=socialPostService.createPublicSchedule(socialPostInputMessagePublicRequest, businessNumber,requestSource);
		return new ResponseEntity<>(responsePayload,HttpStatus.OK);
	}

	@GetMapping(value = "/track/{trackingId}")
	public @ResponseBody ResponseEntity<SocialPublicTrackingPayload> createPublicSchedule(@RequestHeader("account-id") @NotNull Integer businessId,@PathVariable("trackingId") @NotNull String trackingId)   {
		LOG.info("trackingId: {}",trackingId);
		SocialPublicTrackingPayload responsePayload=socialPostService.trackPostInfo(trackingId,businessId);
		return new ResponseEntity<>(responsePayload,HttpStatus.OK);

	}

	@DeleteMapping(value = "/failed/{postId}")
	public @ResponseBody ResponseEntity<SocialGenericResponse> disableFailedPost(@PathVariable("postId") Integer postId, @RequestParam("userId") Integer userId,
																				 	@RequestParam("isDisabled") Integer isDisabled,
																					@RequestParam(value = "srcId",required = false) Integer srcId,
																				 @RequestParam(value = "activity") String activity,
																				 @RequestHeader(value = "business-id") Integer businessId) {
		return new ResponseEntity<>(socialPostService.disableFailedPost(postId,userId,isDisabled,srcId, activity, businessId, false), HttpStatus.OK);
	}

	@DeleteMapping(value = "/failed/{postId}/reseller")
	public @ResponseBody ResponseEntity<SocialGenericResponse> disableFailedPostForReseller(@PathVariable("postId") Integer postId, @RequestParam("userId") Integer userId,
																				 @RequestParam("isDisabled") Integer isDisabled,
																				 @RequestParam(value = "srcId",required = false) Integer srcId,
																				 @RequestParam(value = "activity") String activity,
																							@RequestHeader(value = "business-id") Integer businessId) {
		return new ResponseEntity<>(socialPostService.disableFailedPost(postId,userId,isDisabled,srcId, activity, businessId, true), HttpStatus.OK);
	}

	@PostMapping(value = "/failed/all")
	public @ResponseBody ResponseEntity<FailedPostListResponse> getAllFailedPosts(@RequestHeader("business-id") Integer businessId,
																				  @RequestBody FailedPostFilter filter,
																				  @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception {
		LOG.info(" Received request to get failed post for businessId: {} with filters : {}",businessId, filter);
		FailedPostListResponse posts = socialPostService.getAllFailedPosts(businessId, filter, false, requestSource);
		LOG.info(" Received response to send failed post  : {}", posts);
		return new ResponseEntity<>(posts, HttpStatus.OK);
	}

	@PostMapping(value = "/failed/all/reseller")
	public @ResponseBody ResponseEntity<FailedPostListResponse> getAllFailedPostsForReseller(@RequestHeader("business-id") Integer businessId,
																				  @RequestBody FailedPostFilter filter,
																							 @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception {
		LOG.info("Received request to get failed post for reseller for businessId: {} with filters : {}",businessId, filter);
		FailedPostListResponse posts = socialPostService.getAllFailedPosts(businessId, filter, true, requestSource);
		LOG.info(" Received response to send failed post  : {}", posts);
		return new ResponseEntity<>(posts, HttpStatus.OK);
	}

	@PostMapping(value = "/pages/smb")
	public @ResponseBody ResponseEntity<SocialPostPageStatusDataSMB> fetchPagesForPost(@RequestBody SocialPostStatusRequestSMB socialPostStatusRequest) {
		LOG.info("Social Post: request received for view details page for SMB {}",socialPostStatusRequest );
		return new ResponseEntity<>( socialPostService.fetchPagesForPostSMB(socialPostStatusRequest),HttpStatus.OK);
	}

	@PostMapping(value = "/pageDetail/{type}")
	public @ResponseBody ResponseEntity<GetPageDetailForPostResponse> getPageDetailForPost(@PathVariable("type") @NotNull String type,
																						   @RequestBody GetPageDetailForPostRequest getPageDetailForPostRequest,
																						   @RequestHeader("account-id") Integer accountId) throws Exception {

		GetPageDetailForPostResponse responsePayload=socialPostService.getPageDetailsForPosts(type, getPageDetailForPostRequest, accountId, false);
		return new ResponseEntity<>(responsePayload,HttpStatus.OK);
	}

	@PostMapping(value = "/page-count")
	public @ResponseBody ResponseEntity<GetPageCountForPostResponse> getPageCountForPost(@RequestBody GetPageCountForPostRequest getPageDetailForPostRequest) throws Exception {
		LOG.info("request received to get page count: {}", getPageDetailForPostRequest);
		GetPageCountForPostResponse responsePayload=socialPostService.getPageCountForPosts(getPageDetailForPostRequest);
		return new ResponseEntity<>(responsePayload,HttpStatus.OK);

	}
	@PostMapping(value = "/pageDetail/{type}/reseller")
	public @ResponseBody ResponseEntity<GetPageDetailForPostResponse> getPageDetailForPostForReseller(@PathVariable("type") @NotNull String type,
																									  @RequestBody GetPageDetailForPostRequest getPageDetailForPostRequest,
																									  @RequestHeader("account-id") Integer accountId) throws Exception {

		GetPageDetailForPostResponse responsePayload=socialPostService.getPageDetailsForPosts(type, getPageDetailForPostRequest, accountId, true);
		return new ResponseEntity<>(responsePayload,HttpStatus.OK);
	}

	@GetMapping(value = "/postActivity/{postId}")
	public @ResponseBody ResponseEntity<PostActivityResponse> getPostActivityData(@PathVariable("postId") @NotNull Integer postId,
																				  @RequestParam(value = "timezone", required = false) String timezone) throws Exception {
		PostActivityResponse responsePayload = socialPostActivityService.getPostActivityData(postId, timezone, null, null, false);
		return new ResponseEntity<>(responsePayload, HttpStatus.OK);
	}

	@GetMapping(value = "/postActivity/mobile/{postId}")
	public @ResponseBody ResponseEntity<PostActivityResponse> getPostActivityDataForMobile(@PathVariable("postId") @NotNull Integer postId,
																				  @RequestParam(value="pageNo", required = true) Integer pageNo,
																				  @RequestParam(value="size", required = true) Integer size,
																				  @RequestParam(value = "timezone", required = false) String timezone) throws Exception {
		PostActivityResponse responsePayload = socialPostActivityService.getPostActivityData(postId, timezone, pageNo, size, true);
		return new ResponseEntity<>(responsePayload, HttpStatus.OK);
	}

	@GetMapping(value = "/postActivity/{postId}/reseller")
	public @ResponseBody ResponseEntity<PostActivityResponse> getPostActivityDataForReseller(@PathVariable("postId") @NotNull Integer postId, @RequestParam(value = "timezone", required = false) String timezone) throws Exception {
		PostActivityResponse responsePayload = socialPostActivityService.getPostActivityData(postId, timezone, null, null, false);
		return new ResponseEntity<>(responsePayload, HttpStatus.OK);
	}

	@PostMapping(value = "notification/failed/post")
	public @ResponseBody ResponseEntity<Void> failedPostNotification(@RequestBody SocialFailedPostIdsRequest socialFailedPostIdsRequest) {
		LOG.info("Social Post: request received for failed post notification for socialFailedPostIdsRequest {}", JSONUtils.toJSON(socialFailedPostIdsRequest ));
		socialPostService.processingFailedPostNotification(socialFailedPostIdsRequest);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping(value = "/failed/{postId}")
	public @ResponseBody ResponseEntity<FailedPostResponse> getFailedPosts(@RequestBody FailedPostIdFilter filter)  {
		LOG.info(" Received request to get failed post  with filters : {}", filter);
		FailedPostResponse posts = socialPostService.getFailedPosts( filter);
		LOG.info(" Received response to send failed post  : {}", posts);
		return new ResponseEntity<>(posts, HttpStatus.OK);
	}

	@PostMapping(value = "/postActivity")
	public @ResponseBody ResponseEntity<Void> SavePostActivityData(@RequestBody SavePostActivityRequest request) throws Exception {
		socialPostActivityService.saveActivity(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	@PostMapping(value = "/published/delete/{postId}")
	public @ResponseBody ResponseEntity<SocialGenericResponse> deletePublishedPosts(@RequestBody DeletePublishedRequest deletePublishedRequest,
																					@PathVariable("postId") @NotNull Integer postId,
																					@RequestHeader("account-id") @NotNull Integer businessId,
																					@RequestHeader("user-id") Integer userId,
																					@RequestParam("activity") String activity) {
		return new ResponseEntity<>(socialPostService.deletePublishedPosts(deletePublishedRequest, businessId, postId, userId, false), HttpStatus.OK);
	}

	@DeleteMapping(value = "/published/delete/{postId}/reseller")
	public @ResponseBody ResponseEntity<SocialGenericResponse> deletePublishedPostsForReseller(@PathVariable("postId") @NotNull Integer postId,
																					@RequestHeader("account-id") @NotNull Integer businessId,
																					@RequestHeader("user-id") Integer userId,
																					@RequestParam("activity") String activity) {
		return new ResponseEntity<>(socialPostService.deletePublishedPosts(null,businessId, postId, userId, true), HttpStatus.OK);
	}

	@PostMapping (value = "/master/published/edit")
	public @ResponseBody ResponseEntity<EditMasterPostResponse> editPublishMaterPostData(@RequestBody SocialMasterPostInputMessages socialPost,
																						   @RequestHeader(Constants.BUSINESS_ID) Integer businessId,
																						   @RequestHeader(Constants.BUSINESS_NUMBER) Long enterpriseId,
																						   @RequestHeader(Constants.USER_ID) Integer userId,
																						   @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception {
		if(Objects.isNull(socialPost) || !Objects.equals(socialPost.getBusinessId(),businessId) || CollectionUtils.isEmpty(socialPost.getSocialPostList()) ||
			Objects.isNull(socialPost.getSocialPostList().get(0).getId()) || MapUtils.isEmpty(socialPost.getSocialPostList().get(0).getPostingSites())){
			LOG.info("Not a valid request : {} with business id : {}",socialPost,businessId);
			return new ResponseEntity<>(HttpStatus.OK);
		}
		LOG.info(" Received request to edit published post with post Object : {}", socialPost);
		masterPostInputValidator.validate(socialPost, null);
		return new ResponseEntity<>(socialPostService.editPublishedMasterPostData(socialPost, enterpriseId, requestSource, businessId),HttpStatus.OK);
	}
	@PostMapping(value = "published/pages/{postId}")
	public @ResponseBody ResponseEntity<List<ChannelLocationInfo>> getPublishedPagesList(@PathVariable("postId") @NotNull Integer postId,
																						 @RequestBody LocationMappingRequest request,
																						 @RequestHeader(Constants.BUSINESS_ID) Integer businessId)throws Exception{
		LOG.info("Received request to delete published post with post Object : {}", request);
		return new ResponseEntity<>(socialPostService.getPublishedPagesList(postId,request,businessId),HttpStatus.OK);
	}
	@PostMapping(value = "update/submit")
	public @ResponseBody ResponseEntity<Void> updateDataOnSocialSites(@RequestBody @Valid SocialPostPublishInfoRequest data) throws Exception {
		LOG.info(" Received request to update published with post object : {}", data);
		socialPostService.updateDataOnSocialSites(data);
		return new ResponseEntity<>(HttpStatus.OK);

	}

	@PostMapping(value = "update/post/approval/expiry")
	public @ResponseBody ResponseEntity<Void> updatePostApprovalExpiryStatus(@RequestBody SocialPostDetailsRequest data) throws Exception {
		LOG.info(" Received request to mark post approval status as expired for post : {}", data);
		socialPostService.updatePostApprovalExpiryStatus(data);
		return new ResponseEntity<>(HttpStatus.OK);

	}

	@GetMapping(value = "/delete-request-status/{postId}")
	public @ResponseBody ResponseEntity<DeletePostRequestStatusData> getDeletePostRequestStatus(@PathVariable("postId") @NotNull Integer postId) throws Exception {

		DeletePostRequestStatusData responsePayload = postDeleteRequestService.fetchLatestDeletePostStatus(postId);
		return new ResponseEntity<>(responsePayload, HttpStatus.OK);
	}

	@GetMapping(value = "/tweet-details/{postId}")
		public @ResponseBody ResponseEntity<TweetDetailsResponse> getPostTweetDetails(@PathVariable("postId") @NotNull String postId, @RequestParam("isParent") Boolean isParent,@RequestParam(value = "source",defaultValue = "BE") String source) throws Exception {

		TweetDetailsResponse responsePayload = socialPostService.getTwitterDetails(postId, isParent,source);
		return new ResponseEntity<>(responsePayload, HttpStatus.OK);
	}
	@PostMapping(value = "/direct")
	public @ResponseBody ResponseEntity<Void> postOnSocialSites(@RequestBody SocialPostInputMessage socialPost) throws Exception {
		LOG.info("Request received for post direct with payload {}", socialPost);
		socialPostService.postOnSocialSitesDirect(socialPost);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping(value = "/bulk/draft")
	public @ResponseBody ResponseEntity<?> postBulkDraftApi(@RequestBody BulkUploadDraftRequest request){
		socialPostService.postBulkDraft(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping(value = "/all/sources")
	public @ResponseBody ResponseEntity<List<AggregationSourceMessage>> getAllSocialSources(@RequestParam("businessId") Integer businessId) throws Exception {
		List<AggregationSourceMessage> messages = socialPostService.getAllSocialSources(businessId);
		return new ResponseEntity<>(messages, HttpStatus.OK);
	}
	@PostMapping(value = "/bulk/draft/consume")
	public @ResponseBody ResponseEntity<?> postBulkDraftConsumeApi(@RequestBody PostInfoRequest request, @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource){
		socialPostService.prepareMasterPostRequest(request, requestSource);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping(value = "/calendar/events")
	public @ResponseBody ResponseEntity<CalendarEventResponse> getCalendarEvents(@RequestBody GetCalendarEventRequest request){
		return new ResponseEntity<>(socialPostService.getCalendarEvents(request), HttpStatus.OK);
	}

	@PostMapping(value = "/{businessId}/calendar/events")
	public @ResponseBody ResponseEntity<?> getCalendarEvents(@PathVariable("businessId") String businessIdStr,
															 @RequestBody GetCalendarEventRequest request) {
		try {
			Integer businessId = Integer.parseInt(businessIdStr);
			return ResponseEntity.ok(socialPostService.getCalendarEventsWithBusinessId(request, businessId));
		} catch (NumberFormatException e) {
			return ResponseEntity.badRequest().body("Invalid businessId: Must be a numeric value");
		}
	}

	@PutMapping(value = "/calendar/events")
	public @ResponseBody ResponseEntity<CalendarEventResponse> saveCalendarEvents(@RequestBody SaveCalendarEventRequest request){
		socialPostService.saveCalendarEvents(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping(value = "{enterpriseId}/locations")
	private @ResponseBody ResponseEntity<CalenderEventCountyListResponse> fetchEnabledLocations(@PathVariable Long enterpriseId) {
		return new ResponseEntity<>(socialPostService.getEventCountryList(enterpriseId), HttpStatus.OK);
	}

	@PostMapping(value = "{enterpriseId}/locations")
	private @ResponseBody ResponseEntity<SocialGenericResponse> updateEnabledLocations(@PathVariable Long enterpriseId, @RequestBody UpdateLocationEventRequest request) {
		return new ResponseEntity<>(socialPostService.updateEventLocations(enterpriseId, request), HttpStatus.OK);
	}


	@PutMapping(value = "notification/failed/post/update")
	public @ResponseBody ResponseEntity<Void> failedPostNotification(@RequestBody List<Integer> publishInfoIds) {
		LOG.info("Social Post: request received for failed post notification update for socialFailedPostIdsRequest {}",publishInfoIds );
		socialPostService.updateFailedPostNotification(publishInfoIds);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	@PostMapping(value = "notification/scheduling")
	public @ResponseBody ResponseEntity<Void> failedPostSamayScheduling(@RequestBody SocialPostPublishInfo socialPostPublishInfo) {
		LOG.info("Social Post: request received for failedPostSamayScheduling {}",socialPostPublishInfo );
		socialPostService.samayNotificationSchedule(socialPostPublishInfo);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping(value = "notification/pick/post")
	public @ResponseBody ResponseEntity<Void> pickFailedPost() {
		LOG.info("Social Post: request received for pickFailedPost" );
		socialPostService.pickFailedPost();
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping(value = "/instagram/posting/limit")
	public @ResponseBody ResponseEntity<?> getIgPostLimit(@RequestBody IgPostLimitRequest request){
		return new ResponseEntity<>(socialPostService.getIgPostLimit(request),HttpStatus.OK);
	}

	@PostMapping("master-post/draft")
	public @ResponseBody ResponseEntity<SocialGenericResponse> saveDraftMasterPost(@Valid @RequestBody SocialMasterPostInputMessages socialMasterPostInputMessages,
																				   @RequestHeader(Constants.BUSINESS_ID) Integer businessId,
																				   @RequestHeader(Constants.BUSINESS_NUMBER) Long enterpriseId,
																				   @RequestHeader(Constants.USER_ID) Integer userId,
																				   @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) {
		LOG.info("Social Post Draft: request received to save draft master post {} and business id :{}", socialMasterPostInputMessages,businessId);
		if(Objects.isNull(socialMasterPostInputMessages)
				|| !Objects.equals(socialMasterPostInputMessages.getBusinessId(),businessId)){
			LOG.info("Not a valid request : {} with business id : {}",socialMasterPostInputMessages,businessId);
			return new ResponseEntity<>(HttpStatus.OK);
		}
		return new ResponseEntity<>(socialPostService.saveMasterDraft(socialMasterPostInputMessages, enterpriseId, false, requestSource), HttpStatus.OK);
	}

	@PostMapping("master-post/draft/reseller")
	public @ResponseBody ResponseEntity<SocialGenericResponse> saveDraftMasterPostForReseller(@Valid @RequestBody SocialMasterPostInputMessages socialMasterPostInputMessages,
																							  @RequestHeader(Constants.BUSINESS_NUMBER) Long enterpriseId,
																							  @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) {
		LOG.info("Social Post Draft: request received to save draft master post {}", socialMasterPostInputMessages);
		return new ResponseEntity<>(socialPostService.saveMasterDraft(socialMasterPostInputMessages,enterpriseId, true, requestSource), HttpStatus.OK);
	}

	@PutMapping("master-post/draft")
	public @ResponseBody ResponseEntity<SocialGenericResponse> editDraftMasterPost(@Valid @RequestBody SocialMasterPostInputMessages socialMasterPostInputMessages,
																				   @RequestHeader(Constants.BUSINESS_ID) Integer businessId,
																				   @RequestHeader(Constants.USER_ID) Integer userId,
																				   @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) {
		LOG.info("Social edit Draft: request received to edit draft master post {} and business id : {}", socialMasterPostInputMessages,businessId);
		if(Objects.isNull(socialMasterPostInputMessages)
				|| !Objects.equals(socialMasterPostInputMessages.getBusinessId(),businessId)){
			LOG.info("Not a valid request : {} with business id : {}",socialMasterPostInputMessages,businessId, requestSource);
			return new ResponseEntity<>(HttpStatus.OK);
		}
		return new ResponseEntity<>(socialPostService.editMasterDraft(socialMasterPostInputMessages, false, requestSource), HttpStatus.OK);
	}

	@PutMapping("master-post/draft/reseller")
	public @ResponseBody ResponseEntity<SocialGenericResponse> editDraftMasterPostReseller(@Valid @RequestBody SocialMasterPostInputMessages socialMasterPostInputMessages,
																						   @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) {
		LOG.info("Social edit Draft: request received to edit draft master post {}", socialMasterPostInputMessages);
		return new ResponseEntity<>(socialPostService.editMasterDraft(socialMasterPostInputMessages, true, requestSource), HttpStatus.OK);
	}

	@GetMapping(value = "master-post/draft/{postId}/edit")
	public @ResponseBody ResponseEntity<SocialMasterDraftEditPostResponse> getMasterDraftData(@PathVariable("postId") Integer postId,
																							  @RequestHeader("account-id") Integer accountId) throws Exception {
		LOG.info(" Received request to get master draft data with draftId : {}", postId);
		return new ResponseEntity<>(socialPostService.getMasterDraftData(postId, accountId, false), HttpStatus.OK);
	}

	@GetMapping(value = "master-post/draft/{postId}/edit/reseller")
	public @ResponseBody ResponseEntity<SocialMasterDraftEditPostResponse> getMasterDraftDataForReseller(@PathVariable("postId") Integer postId,
																										 @RequestHeader("account-id") Integer accountId) throws Exception {
		LOG.info(" Received request to get master draft data with draftId : {}", postId);
		return new ResponseEntity<>(socialPostService.getMasterDraftData(postId, accountId, true), HttpStatus.OK);
	}

	@DeleteMapping(value = "master-post/draft/{postId}")
	public @ResponseBody ResponseEntity<SocialGenericResponse> deleteMasterDraftData(@RequestHeader(value = Constants.BUSINESS_ID, required = true) Integer businessId,
																					 @PathVariable("postId") Integer postId) throws Exception {
		LOG.info(" Received request to delete master draft with postId : {} and business :{}", postId,businessId);
		return new ResponseEntity<>(socialPostService.deleteMasterDraft(postId, businessId, false), HttpStatus.OK);
	}

	@DeleteMapping(value = "master-post/draft/{postId}/reseller")
	public @ResponseBody ResponseEntity<SocialGenericResponse> deleteMasterDraftDataForReseller(@RequestHeader(Constants.BUSINESS_ID) Integer businessId,
																								@PathVariable("postId") Integer postId) throws Exception {
		LOG.info(" Received request to delete master draft with postId : {}", postId);
		return new ResponseEntity<>(socialPostService.deleteMasterDraft(postId, businessId, true), HttpStatus.OK);
	}

	@PostMapping("master-post/draft/all")
	public @ResponseBody ResponseEntity<SocialDraftPostListResponse> masterDraftListData(@RequestHeader("business-id") Integer businessId,
																						 @RequestBody BusinessIdRequest businessIdRequest) {
		LOG.info(" Received request to fetch master draft list with businessId {} and request body: {}", businessId, businessIdRequest);
		return new ResponseEntity<>(socialPostService.masterDraftListData(businessId,businessIdRequest, false), HttpStatus.OK);
	}

	@PostMapping("master-post/draft/all/reseller")
	public @ResponseBody ResponseEntity<SocialDraftPostListResponse> masterDraftListDataForReseller(@RequestHeader("business-id") Integer businessId,
																						 @RequestBody BusinessIdRequest businessIdRequest) {
		LOG.info(" Received request to fetch master draft list with businessId {} and request body: {}", businessId, businessIdRequest);
		return new ResponseEntity<>(socialPostService.masterDraftListData(businessId,businessIdRequest, true), HttpStatus.OK);
	}

	@PostMapping(value = "/master/create/schedule")
	public @ResponseBody ResponseEntity<CreatePostResponse> scheduledMaterPostOnSocialSites(@Valid @RequestBody SocialMasterPostInputMessages socialPost,
																			  @RequestHeader(Constants.BUSINESS_ID) Integer businessId,
																			  @RequestHeader(Constants.BUSINESS_NUMBER) Long enterpriseId,
																			  @RequestHeader(Constants.USER_ID) Integer userId,
																			  @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception {
		if(Objects.isNull(socialPost) || !Objects.equals(socialPost.getBusinessId(),businessId)){
			LOG.info("Not a valid request : {} with business id : {}",socialPost,businessId);
			return new ResponseEntity<>(HttpStatus.OK);
		}
		masterPostInputValidator.validate(socialPost, null);
		LOG.info("Received request to post with object : {} and business id :{}", socialPost,businessId);
		CreatePostResponse response = socialPostService.scheduledMasterPostOnSocialSites(socialPost, businessId, enterpriseId, false, requestSource);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@PostMapping(value = "/master/create/schedule/reseller")
	public @ResponseBody ResponseEntity<CreatePostResponse> scheduledMaterPostOnSocialSitesForReseller(@Valid @RequestBody SocialMasterPostInputMessages socialPost,
																						 @RequestHeader(Constants.BUSINESS_NUMBER) Long enterpriseId,
																									   @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception {
		LOG.info("Received request to post with object : {}", socialPost);
		masterPostInputValidator.validate(socialPost, null);
		CreatePostResponse response = socialPostService.scheduledMasterPostOnSocialSites(socialPost, null, enterpriseId, true, requestSource);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@PostMapping(value = "/master/immediate")
	public @ResponseBody ResponseEntity<CreatePostResponse> postImmediately(@Valid @RequestBody SocialMasterPostInputMessages socialPost,
															  @RequestHeader(Constants.BUSINESS_ID) Integer businessId,
															  @RequestHeader(Constants.BUSINESS_NUMBER) Long enterpriseId,
															  @RequestHeader(Constants.USER_ID) Integer userId,
															  @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception{
		if(Objects.isNull(socialPost) || !Objects.equals(socialPost.getBusinessId(),businessId)){
			LOG.info("Not a valid request : {} with business id : {}",socialPost,businessId);
			return new ResponseEntity<>(HttpStatus.OK);
		}
		LOG.info(" Received request to post immediate flow with post object : {} and business id : {}", socialPost ,businessId);
		masterPostInputValidator.validate(socialPost, null);
		CreatePostResponse response = socialPostService.immediateMasterPostOnSocialSites(socialPost, enterpriseId, false, requestSource);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@PostMapping(value = "/master/immediate/reseller")
	public @ResponseBody ResponseEntity<CreatePostResponse> postImmediatelyForReseller(@Valid @RequestBody SocialMasterPostInputMessages socialPost,
																		 @RequestHeader(Constants.BUSINESS_NUMBER) Long enterpriseId,
																					   @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception{
		LOG.info(" Received request to post immediate flow with post object : {}", socialPost);
		masterPostInputValidator.validate(socialPost, null);
		CreatePostResponse response = socialPostService.immediateMasterPostOnSocialSites(socialPost, enterpriseId, true, requestSource);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@PostMapping (value = "/master/schedule/edit")
	public @ResponseBody ResponseEntity<EditMasterPostResponse> editSchedulerMaterPostData(@RequestBody SocialMasterPostInputMessages socialPost,
																						   @RequestHeader(Constants.BUSINESS_ID) Integer businessId,
																						   @RequestHeader(Constants.BUSINESS_NUMBER) Long enterpriseId,
																						   @RequestHeader(Constants.USER_ID) Integer userId,
																						   @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception {
		if(Objects.isNull(socialPost) || !Objects.equals(socialPost.getBusinessId(),businessId)){
			LOG.info("Not a valid request : {} with business id : {}",socialPost,businessId);
			return new ResponseEntity<>(HttpStatus.OK);
		}
		LOG.info(" Received request to remove post with post Object : {}", socialPost);
		masterPostInputValidator.validate(socialPost, null);
		return new ResponseEntity<>(socialPostService.editSchedulerMasterPostData(socialPost, enterpriseId, false, requestSource, businessId),HttpStatus.OK);
	}

	@PostMapping (value = "/master/schedule/edit/reseller")
	public @ResponseBody ResponseEntity<EditMasterPostResponse> editSchedulerMaterPostDataForReseller(@RequestBody SocialMasterPostInputMessages socialPost,
																									  @RequestHeader(value = Constants.BUSINESS_ID, required = false) Integer businessId,
																									  @RequestHeader(Constants.BUSINESS_NUMBER) Long enterpriseId,
																									  @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) throws Exception {
		LOG.info(" Received request to remove post with post Object : {}", socialPost);
		masterPostInputValidator.validate(socialPost, null);
		return new ResponseEntity<>(socialPostService.editSchedulerMasterPostData(socialPost,enterpriseId, true, requestSource, businessId),HttpStatus.OK);
	}

	// Below APIs were one time migration API, not to be used anymore
	/*@PostMapping (value = "/master/scheduled/migrate")
	public @ResponseBody ResponseEntity<Void> migrateSchedulePostData(@RequestBody PostMigrationRequest postMigrationRequest) throws Exception {
		LOG.info(" Received request to migrate data for post : {}", postMigrationRequest.getPostId());
		socialPostService.migratePostData(postMigrationRequest,"scheduled");
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping (value = "/master/draft/migrate")
	public @ResponseBody ResponseEntity<Void> migrateDraftPostData(@RequestBody PostMigrationRequest postMigrationRequest) throws Exception {
		LOG.info(" Received request to migrate data for post : {}", postMigrationRequest.getPostId());
		socialPostService.migratePostData(postMigrationRequest,"draft");
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping (value = "/master/published/migrate")
	public @ResponseBody ResponseEntity<Void> migratePublishedPostData(@RequestBody PostMigrationRequest postMigrationRequest) throws Exception {
		LOG.info(" Received request to migrate data for post : {}", postMigrationRequest.getPostId());
		socialPostService.migratePostData(postMigrationRequest,"published");
		return new ResponseEntity<>(HttpStatus.OK);
	}*/
	@PostMapping(value = "/license/upload")
	public @ResponseBody ResponseEntity<Void> uploadDocumentForSocialPost(@RequestBody SocialDocumentRequest request){
		socialPostService.uploadSocialPostDocument(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping(value = "/license/get")
	public @ResponseBody ResponseEntity<Object> getDocumentForSocialPost(@RequestParam("documentId") String documentId){
		return new ResponseEntity<>(socialPostService.getSocialPostDocument(documentId),HttpStatus.OK);
	}


	@PostMapping(value = "/approval/all")
	public @ResponseBody ResponseEntity<?> getAllApprovals (@RequestBody @Valid ApprovalWorkFlowRequest approvalRequest,
															@RequestHeader("account-id") Integer businessId,
															@RequestHeader("X-BUSINESS-NUMBER") Long enterpriseId,
															@RequestParam(value = "startIndex", required = false, defaultValue = "0") Integer startIndex,
															@RequestParam(value = "pageSize", required = false, defaultValue = "5") Integer pageSize,
															@RequestParam(value = "sortBy", required = false, defaultValue = "created_by") String sortParam,
															@RequestParam(value = "sortOrder", required = false, defaultValue = "desc") String sortOrder,
															@RequestHeader("user-id") Integer userId,
															@RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) {
		LOG.info(" Received request to get approval for request : {} for businessId and userId: {}", approvalRequest, businessId, userId);
		return new ResponseEntity<>(approvalWorkflowService.getAllApprovals(approvalRequest, businessId,enterpriseId,
				userId, startIndex, pageSize, sortParam, sortOrder, requestSource), HttpStatus.OK);
	}


	@GetMapping(value = "/approval/post")
	public @ResponseBody ResponseEntity<List<ApprovalWorkFlowData>> getApprovalDataFromPostId(@RequestHeader(value = "user-id",required = false) Integer userId ,
																						@RequestHeader("account-id") Integer businessId,
																						@RequestParam("postId") String postId,
																						@RequestParam(value = "timeZone", required = false) String timeZone,
																						@RequestHeader(value = "X-BUSINESS-NUMBER",required = false) Long enterpriseId,
																						@RequestParam(value = "isPublicPost",required = false
																								,defaultValue = "false") boolean isPublicPost){
		LOG.info("Received request to get approval for postId : {} and businessId: {}", postId, businessId);
		return new ResponseEntity<>(socialPostService.getPostDetailsFromPostId(userId,businessId,enterpriseId,postId,isPublicPost, timeZone),HttpStatus.OK);
	}

	@GetMapping(value = "/approval/{postId}")
	public @ResponseBody ResponseEntity<ApprovalWorkFlowData> getApproverDataFromPostId(@RequestHeader(value = "user-id",required = false) Integer userId ,
																						@RequestHeader("account-id") Integer businessId,
																						@PathVariable("postId") Integer postId,
																						@RequestHeader(value = "X-BUSINESS-NUMBER",required = false) Long enterpriseId,
																						@RequestParam(value = "isPublicPost",required = false
																								,defaultValue = "false") boolean isPublicPost){
		LOG.info(" Received request to get approval for postId : {}", postId);
		return new ResponseEntity<>(approvalWorkflowService.getApprovalDataFromPostId(userId,businessId,enterpriseId,postId,isPublicPost, null),HttpStatus.OK);
	}

	@PostMapping (value = "/social-post/es")
	public @ResponseBody ResponseEntity<Void> saveOrUpdateSocialPostToEs(@RequestBody SocialPostEsSyncRequest socialPostEsSyncRequest) throws Exception {
		LOG.info(" Received request to post social post on es : {}", socialPostEsSyncRequest.getPostId());
		socialPostService.saveOrUpdateSocialPostOnEs(socialPostEsSyncRequest);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping("/approval/update-tag")
	public ResponseEntity<?> updateTags(@RequestBody SocialTagEntityMappingActionEvent tagUpdateEvent){
		LOG.info(" Received request to update tags in post approval es : {}", tagUpdateEvent);
		socialPostService.updateTagInApprovalPostEs(tagUpdateEvent);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping (value = "/approval/event")
	public @ResponseBody ResponseEntity<Void> migratePublishedPostData(@RequestBody ApprovalWorkflowEvent approvalWorkflowEvent) throws Exception {
		LOG.info("Approval Workflow Event request :{}",approvalWorkflowEvent);
		socialPostService.saveApprovalUpdate(approvalWorkflowEvent);
    	return new ResponseEntity<>(HttpStatus.OK);
  	}

	@PostMapping (value = "/approval/activity")
	public @ResponseBody ResponseEntity<Void> processApprovalActivity(@RequestBody ApprovalActivity approvalActivity) throws Exception {
		LOG.info("Approval Workflow Activity request :{}",approvalActivity);
		socialPostService.saveApprovalActivity(approvalActivity);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping(value = "/approval/count")
	public @ResponseBody ResponseEntity<Void> updateCountForApproval(@RequestBody SocialPostEsRequest socialPostEsRequest){
		approvalWorkflowService.updateCountForApproval(socialPostEsRequest);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping(value = "/approval/count")
	public @ResponseBody ResponseEntity<ApprovalCountResponse> getApprovalCount(@RequestHeader(value = "user-id") Integer userId ,
																				@RequestHeader("account-id") Integer businessId,
																				@RequestHeader(value = "X-BUSINESS-NUMBER",required = false) Long enterpriseId){
		return new ResponseEntity<>(approvalWorkflowService.getApprovalCount(userId,businessId),HttpStatus.OK);
	}

	@PostMapping(value = "/reset/count")
	public @ResponseBody ResponseEntity<Void> resetCountForRequest(@RequestBody ResetCountRequest resetCountRequest) {
		approvalWorkflowService.resetApprovalCount(resetCountRequest);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping (value = "/approval/email")
	public @ResponseBody ResponseEntity<ApprovalEmailResponse> sendEmailNotification(@RequestHeader("user-id") Integer userId,
																	@RequestHeader("account-id") Integer businessId,
																	@RequestHeader("X-BUSINESS-NUMBER") Long enterpriseId,
																	@RequestBody ApprovalEmailReminder approvalEmailReminder) throws Exception {
		LOG.info(" Received reminder request to get approval for : {}", approvalEmailReminder);
		return new ResponseEntity<>(approvalWorkflowService.sendApprovalEmail(enterpriseId,approvalEmailReminder,businessId,userId),HttpStatus.OK);
	}

	@PostMapping(value = "/get/reminder")
	public @ResponseBody ResponseEntity<Void> processReminderEvent(@RequestBody SocialPostPostIdRequest postIdRequest) throws Exception {
		LOG.info("Received request to send reminder for object : {}", postIdRequest);
		approvalWorkflowService.processReminderEvent(postIdRequest);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PutMapping(value = "/update/conversation")
	public @ResponseBody ResponseEntity<Void> updateConversation(@Valid @RequestBody InboxRequestEvent requestEvent){
		LOG.info("Received request to update conversation for object : {}", requestEvent);
		socialPostService.updateConversation(requestEvent);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping(value = "/get/post/{postId}")
	public @ResponseBody ResponseEntity<ApprovalWorkFlowData> getApprovalPostFromPostId(@PathVariable("postId") Integer postId,
																						@RequestParam("businessNumber") Long enterpriseId) throws Exception {
		LOG.info("Get request for post Id :{}", postId);
		return new ResponseEntity<>(approvalWorkflowService.getApprovalDataFromPostId(postId, enterpriseId), HttpStatus.OK);
	}
	@PostMapping (value = "/approval/apple/email")
	public @ResponseBody ResponseEntity<Void> sendAppleApprovalEmail(@RequestBody AppleShowcaseEmail emailRequest) throws Exception {
		LOG.info(" Received send email request for apple status: {}", emailRequest);
		postAppleService.sendAppleShowcaseStatusEmail(emailRequest);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping(value = "/apple/locations")
	public @ResponseBody ResponseEntity<AppleShowcaseLocationResponse> getApprovalCount(@RequestBody AppleLocationStatusResponse request){
		return new ResponseEntity<>(socialPostService.getAppleShowcase(request),HttpStatus.OK);
	}

	@PostMapping(value = "/pagePostActivity/{postId}")
	public @ResponseBody ResponseEntity<SocialPostPageAndActivityData> getPagesAndPostActivityCombine(@PathVariable("postId") @NotNull Integer postId,
																									  @RequestBody SocialPostStatusRequest socialPostStatusRequest,
																									  @RequestHeader("account-id") Integer enterpriseId) {
		LOG.info("Social Post: request received for view details page {}",socialPostStatusRequest );
		return new ResponseEntity<>( socialPostService.getPagesAndPostActivityCombine(postId, socialPostStatusRequest, enterpriseId),HttpStatus.OK);
	}
	@PostMapping(value = "/schedule/mobile/all")
	public @ResponseBody ResponseEntity<SocialSchedulePostMobileResponse> getAllScheduledPostsForMobile(@RequestBody GlobalFilterCriteriaSchedulePostMessage filter) throws Exception {
		SocialSchedulePostMobileResponse posts = socialPostService.getAllScheduledPostsForMobile(filter);
		LOG.info(" Received response to send post for mobile  : {}", posts);
		return new ResponseEntity<>(posts, HttpStatus.OK);
	}
	@PostMapping(value = "/apple/live")
	public @ResponseBody ResponseEntity<Void> checkIfAppleShowcaseIsLive(@RequestBody AppleLiveCheckRequest request){
		postAppleService.checkIfAppleShowcaseIsLive(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PatchMapping(value = "/migration")
	public @ResponseBody ResponseEntity<Void> updateScheduledPosts(@RequestBody LocationMovementPostDTO request){
		socialPostService.updateSocialPostScheduleInfo(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PatchMapping(value = "/{channel}/remove")
	public @ResponseBody ResponseEntity<Void> removePageFormScheduleInfo(@PathVariable("channel") String channel ,
																	   @RequestParam("pageId") String pageId){
		LOG.info("Request received from channel : {} to remove page : {} for schedule info",channel,pageId);
		socialPostService.removePageFormScheduleInfo(channel,pageId);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping("ig/container/check")
	public @ResponseBody ResponseEntity<Void> checkIgContainerStatus(@RequestBody IgContainerCheckDTO igContainerCheckDTO) throws Exception {
		LOG.info("Request received to check container status for DTO: {}",igContainerCheckDTO);
		socialPostService.checkIgContainerStatus(igContainerCheckDTO,false);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping("ig/container/mark-failed")
	public @ResponseBody ResponseEntity<Void> markIgContainerStatus(@RequestBody IgContainerCheckDTO igContainerCheckDTO) throws Exception {
		LOG.info("Request received to mark container status as failed for DTO: {}",igContainerCheckDTO);
		socialPostService.checkIgContainerStatus(igContainerCheckDTO,true);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping("/facebook/metadata")
	public @ResponseBody ResponseEntity<FacebookMetadataResponse> socialMentionSearch(@RequestBody FacebookMetadataRequest request) {
		LOG.info("Request Received to fetch metadata for request {}", request);
		return new ResponseEntity<>(socialPostService.fetchFacebookMetadata(request), HttpStatus.OK);
	}

	/**
	 * Method called from Kafka topic: SOCIAL_SYNC_BUSINESS_POSTS
	 * @param request
	 * @return
	 */
	@PutMapping("/sync/business-posts")
	public @ResponseBody ResponseEntity<Void> syncBusinessPostsAndSaveInES(@RequestBody SocialPostPublishInfoRequest request) {
		LOG.info("Request Received to sync business posts for publish info obj {}", request.toString());
		socialPostService.syncBusinessPostsAndSaveInES(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping (value = "/report/summary")
	public @ResponseBody ResponseEntity<SocialReportSummaryResponse> getPostReportSummary(@Valid @RequestBody SocialReportSummaryRequest request) throws Exception {
		LOG.info("Request received to fetch posts report summary ", request);
		return new ResponseEntity<>(socialPostService.getPostReportSummary(request), HttpStatus.OK);
	}

	@PostMapping("/{channel}/process/pending/posts")
	public ResponseEntity<Void> processPendingPost(@PathVariable("channel") String channel,
												   @RequestBody Map<String, Object> request) {
		Integer processingPostId = (Integer) request.get("processingPostId");
		LOG.info("Request Received from Samay to process pending post with ID: {} and channel: {}", processingPostId, channel);

		if (processingPostId == null) {
			LOG.error("Invalid request: processingPostId is null");
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}

		try {
			socialPostService.processPendingPosts(processingPostId, channel);
			return new ResponseEntity<>(HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("Error processing pending post with ID: {}", processingPostId, e);
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}


	@GetMapping("/tiktok/status")
	public @ResponseBody ResponseEntity<Void> checkForTiktokPostStatus() {
		socialPostService.checkForTiktokPostStatus();
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping("{channel}/hashtag/recommendations")
	public ResponseEntity<List<TikTokHashtagResponse.HashtagResponse>> getRecommendedHashtags(@PathVariable String channel,
			@RequestParam @Size(min = 3, message = "Keyword must have at least 3 characters") String keyword,
			@RequestHeader(Constants.BUSINESS_ID) Integer businessId) throws Exception {

		List<TikTokHashtagResponse.HashtagResponse> hashtags = socialPostService.fetchRecommendedHashtags(channel, keyword, businessId);
		return ResponseEntity.ok(hashtags);
	}
}
