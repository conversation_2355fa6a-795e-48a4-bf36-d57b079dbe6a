package com.birdeye.social.controller;

import com.birdeye.social.constant.SocialAlertTypeEnum;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.model.EmailIntergationInput;
import com.birdeye.social.model.MultiEnterpriseRequest;
import com.birdeye.social.service.SocialAlertService;
import com.birdeye.social.sro.BrokenEnterprisesList;
import com.birdeye.social.sro.EmailAlertDetailResponse;
import com.birdeye.social.sro.EmailAlertResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/social/alert")
public class SocialAlertController {
    private static final Logger LOG = LoggerFactory.getLogger(SocialAlertController.class);

    @Autowired
    private SocialAlertService socialAlertService;

    @GetMapping(value = "/email/{enterpriseId}")
    public @ResponseBody
    ResponseEntity<EmailAlertResponse> getAllUserDataForEmail(@PathVariable("enterpriseId") Long enterpriseId) {
        return new ResponseEntity<>(socialAlertService.getAllUserDataForEmail(enterpriseId, SocialAlertTypeEnum.WEEKLY.name()), HttpStatus.OK);
    }

    @GetMapping(value = "/email/disconnected/{enterpriseId}")
    public @ResponseBody
    ResponseEntity<EmailAlertResponse> getAllUserDataForDisconnectedEnterpriseForEmail(@PathVariable("enterpriseId") Long enterpriseId) {
        return new ResponseEntity<>(socialAlertService.getAllUserDataForEmail(enterpriseId,SocialAlertTypeEnum.DISCONNECTED.name()), HttpStatus.OK);
    }

    @PostMapping(value = "/email/multi")
    public @ResponseBody
    ResponseEntity<List<EmailAlertResponse>> getAllUserDataForEmailForMultipleEnterprises(@RequestBody MultiEnterpriseRequest input) {
        return new ResponseEntity<>(socialAlertService.getAllUserDataForEmailMultipleOptimized(input.getEnterpriseIds()), HttpStatus.OK);
    }

    @PostMapping(value = "/init/email")
    public ResponseEntity<?> submitInitEmailForBrokenIntegration() throws IOException {
        socialAlertService.submitInitEmailForBrokenIntegration();
        return new ResponseEntity<>( HttpStatus.OK);
    }
    
    @PostMapping(value = "/init/email/event")
    public ResponseEntity<?> initEmailForBrokenIntegration() throws IOException {
        socialAlertService.initEmailForBrokenIntegration();
        return new ResponseEntity<>( HttpStatus.OK);
    }

    @GetMapping(value = "/broken/enterprises")
    public @ResponseBody
    ResponseEntity<BrokenEnterprisesList> submitEventForAllAllBrokenIntegrationEnt() throws IOException {
    	socialAlertService.submitEventForAllAllBrokenIntegrationEnt();
        return new ResponseEntity<>( HttpStatus.OK);
    }
    
    @GetMapping(value = "/broken/enterprises/event")
    public @ResponseBody
    ResponseEntity<BrokenEnterprisesList> getAllBrokenIntegrationEnterprises() throws IOException {
        return new ResponseEntity<>(socialAlertService.getAllBrokenIntegrationEnterprises(), HttpStatus.OK);
    }
    
    /**
     * API to check for broken Integration count from a list of business Ids 
     * @param integrationInput
     * @param error
     * @return
     */
    @PostMapping(value = "brokenIntegrations/email")
	public List<EmailAlertDetailResponse> getIntergationSummary(@Valid @RequestBody EmailIntergationInput integrationInput,BindingResult error) {
		if (error.hasErrors()) {
			LOG.error("Some issue in the input parameters..Validation failed");
            throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid parameter  businessAccountNumber/ businessId in request");
		}
    	List<EmailAlertDetailResponse> integrationDetailMap = socialAlertService.getIntegrationData(integrationInput.getBaseBusinessNumber(), integrationInput.getBusinessIds());
		
		return integrationDetailMap;
	}


}
