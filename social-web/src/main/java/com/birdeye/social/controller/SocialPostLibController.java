package com.birdeye.social.controller;
/**
 * <AUTHOR>
 *
 */
import com.birdeye.social.constant.Constants;
import com.birdeye.social.model.*;
import com.birdeye.social.model.post_lib.PostInsightsData;
import com.birdeye.social.service.SocialPostLibService;
import com.birdeye.social.sro.SocialTagEntityMappingActionEvent;
import com.birdeye.social.sro.bulkupload.BulkUploadPostLibRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Objects;

@RestController
@RequestMapping("/social/post/lib")
public class SocialPostLibController {

    private static final Logger LOG = LoggerFactory.getLogger(SocialPostLibController.class);

    @Autowired
    private SocialPostLibService postLibService;


    @PostMapping
    public @ResponseBody ResponseEntity<Void> createPostLibrary(@Valid @RequestBody SocialMasterPostInputMessages socialPost,
                                                                @RequestHeader(Constants.BUSINESS_ID) Integer businessId,
                                                                @RequestHeader(Constants.USER_ID) Integer userId) {
        LOG.info("Received request to create post lib with object : {}", socialPost);
        if(Objects.isNull(socialPost) || !Objects.equals(socialPost.getBusinessId(),businessId) || !Objects.equals(socialPost.getCreatedBy(),userId)){
            LOG.info("Not a valid request : {} with business id : {} and user id: {}",socialPost,businessId,userId);
            return new ResponseEntity<>(HttpStatus.OK);
        }
        postLibService.createPostLibrary(socialPost);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PatchMapping
    public @ResponseBody ResponseEntity<Void> editPostLibrary(@Valid @RequestBody SocialMasterPostInputMessages socialPost,
                                                              @RequestHeader(Constants.BUSINESS_ID) Integer businessId,
                                                              @RequestHeader(Constants.USER_ID) Integer userId) {
        LOG.info("Received request to edit post lib with post Object : {}", socialPost);
        if(Objects.isNull(socialPost) || !Objects.equals(socialPost.getBusinessId(),businessId) || !Objects.equals(socialPost.getEditedBy(),userId)){
            LOG.info("Not a valid request : {} with business id : {} and user id: {}",socialPost,businessId,userId);
            return new ResponseEntity<>(HttpStatus.OK);
        }
        postLibService.editPostLibrary(socialPost);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping (value = "/info/{id}")
    public @ResponseBody ResponseEntity<MasterPostLibEditPostResponse> getPostEditInfo(@RequestBody BusinessIdRequest businessIdRequest,
                                                                                       @PathVariable("id") Integer id,
                                                                                       @RequestHeader("X-BUSINESS-NUMBER") Long businessNumber) {
        LOG.info(" Received request to get post lib data with post id : {}", id);
        return new ResponseEntity<>(postLibService.getPostLibData(businessIdRequest, id, businessNumber),HttpStatus.OK);
    }

    @DeleteMapping (value = "/{id}")
    public @ResponseBody ResponseEntity<Void> deletePostLib(@PathVariable("id") Integer id,
                                                            @RequestHeader(Constants.BUSINESS_ID) Integer businessId) {
        LOG.info("Received request to delete post lib data with post id : {}", id);
        postLibService.deletePostLib(id,businessId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/all")
    public @ResponseBody ResponseEntity<PostLibListResponse> getPostLibraryPosts(@RequestBody PostLibFilter filter, @RequestHeader("account-id") Integer enterpriseId,
                                                                                 @RequestHeader("time-zone-id") String timezone,
                                                                                 @RequestHeader("X-BUSINESS-NUMBER") Long businessNumber) {
        LOG.info("Received request to get post lib with filter and enterpriseId: {}", filter, businessNumber);
        return new ResponseEntity<>(postLibService.getPostLibList(filter, enterpriseId, timezone, businessNumber),HttpStatus.OK);
    }

    @PatchMapping(value = "/post/insights")
    public @ResponseBody ResponseEntity<Void> postInsights(@RequestBody PostInsightsData postInsightsData){
        LOG.info("Request received to update post insights : {}",postInsightsData);
        postLibService.updatePostInsights(postInsightsData);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PatchMapping(value = "/usage/count/{id}")
    public @ResponseBody ResponseEntity<Void> postUsageCount(@PathVariable("id") Integer postLibId){
        LOG.info("Request received to update usage count : {}",postLibId);
        postLibService.updateUsageCount(postLibId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/bulk/upload")
    public @ResponseBody ResponseEntity<Void> uploadBulkPostLib(@Valid @RequestBody BulkUploadPostLibRequest request){
        postLibService.bulkCreatePostLib(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PatchMapping("/update-tag/user-info")
    public ResponseEntity<Void> updateTags(@RequestBody SocialTagEntityMappingActionEvent tagUpdateEvent){
        LOG.info("Request received to update edit info for tag update : {}",tagUpdateEvent);
        postLibService.updateEditorInfo(tagUpdateEvent);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/settings")
    public ResponseEntity<PostLibSettingsResponse> getPostLibSettings(@RequestHeader(Constants.ACCOUNT_ID) Integer enterpriseId,
                                                @RequestHeader(Constants.BUSINESS_NUMBER) Long businessNumber) {
        return new ResponseEntity<>(postLibService.getPostLibSettings(enterpriseId, businessNumber), HttpStatus.OK);
    }
}
