package com.birdeye.social.controller;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.MappingOperationType;
import com.birdeye.social.model.AutoSuggesterPagesResponse;
import com.birdeye.social.model.GetPagesRequest;
import com.birdeye.social.model.PageConnectionStatus;
import com.birdeye.social.service.AppleSetupService;
import com.birdeye.social.sro.*;
import com.birdeye.social.sro.applePost.AppleCTAJobRequest;
import com.birdeye.social.sro.applePost.AppleLogoURLRequest;
import com.birdeye.social.sro.applePost.AppleUpdateCTARequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/social/apple/setup")
public class AppleSetupController {

    @Autowired
    AppleSetupService appleSetupService;
    private static final Logger logger = LoggerFactory.getLogger(AppleSetupController.class);

    @PostMapping("/existing/accounts")
    public @ResponseBody ResponseEntity<Void> fetchExistingAccounts(@RequestBody AppleExistingLocationRequest input) {
        logger.info("[Apple setup] Request received to get existing accounts for payload: {}", input);
        appleSetupService.fetchExistingAccounts(input);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @PostMapping("/sync/locations")
    public @ResponseBody ResponseEntity<Void> syncExistingLocations(@RequestBody AppleExistingLocationRequest input) {
        logger.info("[Apple setup] Request received to sync existing accounts for payload: {}", input);
        appleSetupService.syncExistingLocations(input);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @PostMapping("/new/location")
    public @ResponseBody ResponseEntity<Void> saveNewLocation(@RequestBody AppleLocationRequest input) {
        logger.info("[Apple setup] Request received to save new account for payload: {}", input);
        appleSetupService.saveNewLocation(input);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PutMapping("/update/location")
    public @ResponseBody ResponseEntity<Void> updateLocation(@RequestBody AppleLocationRequest input) {
        logger.info("[Apple setup] Request received to update account info for payload: {}", input);
        appleSetupService.updateLocation(input);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/business/list/{enterpriseId}")
    public @ResponseBody ResponseEntity<Map<String,String>> getBusinessNameList(@PathVariable Long enterpriseId) {
        logger.info("[Apple] Request received to get Business name list for enterpriseId: {}", enterpriseId);
        return new ResponseEntity<>(appleSetupService.getBusinessNameList(enterpriseId),HttpStatus.OK);
    }

    @GetMapping("/delegated/business/list/{pageNumber}")
    public @ResponseBody ResponseEntity<DelegatedAppleAccount> getDelegatedBusinessList(@PathVariable Integer pageNumber) {
        logger.info("[Apple] Request received to get delegated business list for pageNumber: {}",pageNumber);
        return new ResponseEntity<>(appleSetupService.getDelegatedBusinessList(pageNumber),HttpStatus.OK);
    }

    @GetMapping("/delegated/business/{enterpriseId}")
    public @ResponseBody ResponseEntity<DelegatedAppleAccount> getDelegatedBusiness(@PathVariable Long enterpriseId,
                                                                                        @RequestParam(value = "appleCompanyId", required = false) String appleCompanyId,
                                                                                        @RequestParam(value = "appleBusinessId", required = false) String appleBusinessId) {
        logger.info("[Apple] Request received to get delegated business for enterpriseId: {} and appleData: {}, {}",enterpriseId,appleCompanyId,appleBusinessId);
        return new ResponseEntity<>(appleSetupService.getDelegatedBusiness(enterpriseId,appleCompanyId,appleBusinessId),HttpStatus.OK);
    }

    @PostMapping(value = "/brands/mapping/get/{brandName}")
    public @ResponseBody ResponseEntity<AppleBrandMappingList> getBrandMappingPages(@PathVariable("brandName") String brandName,
                                                                                       @RequestBody LocationMappingRequest request) {
        logger.info("[Apple] Request received to get brand mapping page for brand: {} and enterpriseId: {} ",brandName,request.getBusinessId());
        return new ResponseEntity<>(appleSetupService.getBrandMappingPages(brandName,request), HttpStatus.OK);
    }

    @GetMapping("/enabled/{enterpriseId}")
    public @ResponseBody ResponseEntity<Boolean> getAppleEnabledStatus(@PathVariable Long enterpriseId) {
        logger.info("[Apple] Request received to check apple enabled status for enterpriseId: {} ",enterpriseId);
        return new ResponseEntity<>(appleSetupService.getAppleEnabledStatus(enterpriseId),HttpStatus.OK);
    }

    @PostMapping(value = "get/locations")
    public @ResponseBody ResponseEntity<List<String>> getAppleLocationsFromBusiness(@RequestBody List<Integer> businessIds) {
        logger.info("[Apple] Request received to get apple locationIds for businessIds: {} ",businessIds);
        return new ResponseEntity<>(appleSetupService.getAppleLocationsFromBusiness(businessIds), HttpStatus.OK);
    }

    @GetMapping("/cta/{locationId}")
    public @ResponseBody ResponseEntity<List<String>> getAppleLocationCTA(@PathVariable String locationId) {
        logger.info("[Apple] Request received to get CTA for locationId: {} ",locationId);
        return new ResponseEntity<>(appleSetupService.getAppleLocationCTA(locationId),HttpStatus.OK);
    }

    @GetMapping("/fetch/cta/init")
    public @ResponseBody ResponseEntity<Void> fetchAppleLocationCTAInit() {
        logger.info("[Apple] Request received to init fetch CTA");
        appleSetupService.fetchAppleLocationCTAInit();
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/fetch/cta")
    public @ResponseBody ResponseEntity<Void> fetchAppleLocationCTA(@RequestBody AppleCTAJobRequest appleCTAJobRequest) {
        logger.info("[Apple] Request received to fetch CTA for payload: {} ",appleCTAJobRequest);
        appleSetupService.fetchAppleLocationCTA(appleCTAJobRequest.getId());
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/update/cta")
    public @ResponseBody ResponseEntity<Void> updateAppleLocationCTA(@RequestBody AppleUpdateCTARequest appleUpdateCTARequest) {
        logger.info("[Apple] Request received to update CTA for payload: {} ",appleUpdateCTARequest);
        appleSetupService.updateAppleLocationCTA(appleUpdateCTARequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @PostMapping("/update/logo_url")
    public @ResponseBody ResponseEntity<Void> updateAppleLocationLogoURL(@RequestBody AppleLogoURLRequest appleLogoURLRequest) {
        logger.info("[Apple] Request received to update logo url for payload: {} ",appleLogoURLRequest);
        appleSetupService.updateAppleLocationLogoURL(appleLogoURLRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PutMapping("/page/map")
    public @ResponseBody ResponseEntity<AppleMapUnmapResponse> mapUnmapSmbPage(@RequestParam("operationType")MappingOperationType operationType,
                                                              @RequestParam(value = "pageId", required = false) String pageId,
                                                              @RequestHeader(Constants.BUSINESS_NUMBER) Long enterpriseId,
                                                              @RequestHeader(Constants.BUSINESS_ID) Integer businessId,
                                                              @RequestHeader(Constants.USER_ID) String userId) {
        logger.info("[Apple] Request received to :{} page for enterpriseId: {} user id: {}", operationType, enterpriseId, userId);
        AppleMapUnmapResponse response = appleSetupService.mapUnmapPageForSmb(operationType, pageId, enterpriseId, businessId);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @GetMapping("/pages/all")
    public @ResponseBody ResponseEntity<AutoSuggesterPagesResponse> getAllApplePagesForEnterprise(@RequestHeader(name = "x-business-number") Long enterpriseId) {
        logger.info("[Apple] Request received fetch paged for businessId: {} ", enterpriseId);
        return new ResponseEntity<>(appleSetupService.fetchApplePages(enterpriseId),HttpStatus.OK);
    }

    @PostMapping(value = "/mark/location/invalid")
    public @ResponseBody ResponseEntity<Void> markLocationInvalid(@RequestBody AppleDeleteLocationRequest appleDeleteLocationRequest) throws IOException {
        appleSetupService.markAppleLocationInvalid(appleDeleteLocationRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/update/location/state")
    public @ResponseBody ResponseEntity<Void> updateLocationState(@RequestBody AppleUpdateLocationStateRequest appleUpdateLocationStateRequest){
        logger.info("[Apple setup] Request received to update location state for payload: {} ",appleUpdateLocationStateRequest);
        appleSetupService.updateAppleLocationState(appleUpdateLocationStateRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
