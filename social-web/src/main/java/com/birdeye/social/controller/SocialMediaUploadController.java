package com.birdeye.social.controller;

import com.birdeye.social.external.request.mediaupload.MediaUploadRequest;
import com.birdeye.social.external.request.mediaupload.MediaUploadChunkRequest;
import com.birdeye.social.external.request.mediaupload.MediaUploadRequestForAsset;
import com.birdeye.social.service.media.MediaUploadService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/social/post/media/upload")
public class SocialMediaUploadController {

    @Autowired
    private MediaUploadService mediaUploadService;
    private static final Logger logger = LoggerFactory.getLogger(SocialMediaUploadController.class);

    @PostMapping(value = "/init")
    public @ResponseBody ResponseEntity<Void> initiateMediaUpload(@RequestBody MediaUploadRequest mediaInitiateRequest){
        logger.info("Request received to upload media : {}",mediaInitiateRequest);
        mediaUploadService.initiateMediaUpload(mediaInitiateRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/chunk/save")
    public @ResponseBody ResponseEntity<Void> saveMediaChunkFromPictures(@RequestBody MediaUploadChunkRequest mediaUploadChunkRequest){
        logger.info("Request received to upload media chunk : {}",mediaUploadChunkRequest);
        mediaUploadService.saveMediaChunkFromPictures(mediaUploadChunkRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PutMapping(value = "/push/chunk")
    public @ResponseBody ResponseEntity<Void> pushChunkToUpload(@RequestBody MediaUploadRequestForAsset request){
        logger.info("Request received to push chunk for request :{}",request);
        mediaUploadService.processVideoChunkUpload(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PatchMapping(value = "/chunk")
    public @ResponseBody ResponseEntity<Void> mediaChunkUpload(@RequestBody MediaUploadRequestForAsset request){
        logger.info("Request received to save media chunk : {}",request);
        mediaUploadService.uploadChunkForMediaRequest(request, false);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PatchMapping(value = "/chunk/v2")
    public @ResponseBody ResponseEntity<Void> mediaChunkUploadV2(@RequestBody MediaUploadRequestForAsset request){
        logger.info("Request received to save media chunk v2 : {}",request);
        mediaUploadService.uploadChunkForMediaRequest(request, true);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/caption-thumbnail")
    public @ResponseBody ResponseEntity<?> uploadCaptionAndThumbnail(@RequestBody MediaUploadRequest mediaUploadRequest){
        logger.info("Request to upload thumbnail and caption :{}", mediaUploadRequest);
        return new ResponseEntity<>(mediaUploadService.uploadCaptionAndThumbnail(mediaUploadRequest),HttpStatus.OK);
    }

    @PostMapping(value = "/finalize")
    public @ResponseBody ResponseEntity<?> finalizeUpload(@RequestBody MediaUploadRequest mediaUploadRequest){
        logger.info("Request to finalize video : {}", mediaUploadRequest);
        return new ResponseEntity<>(mediaUploadService.finalizeUpload(mediaUploadRequest),HttpStatus.OK);
    }

    @PostMapping(value = "/status")
    public @ResponseBody ResponseEntity<?> checkUploadStatus(@RequestBody MediaUploadRequest mediaUploadRequest) throws Exception {
        logger.info("Check Upload Status for request: {}",mediaUploadRequest);
        return new ResponseEntity<>(mediaUploadService.checkUploadStatus(mediaUploadRequest),HttpStatus.OK);
    }

    // nifi topic social-media-upload-content
    @PostMapping(value = "/content")
    public @ResponseBody ResponseEntity<?> postContentWithMedia(@RequestBody MediaUploadRequest request){
        logger.info("Upload content for request: {}",request);
        mediaUploadService.postContentWithMedia(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PutMapping(value = "/exception/handler")
    public @ResponseBody ResponseEntity<Void> exceptionHandler(@RequestBody MediaUploadRequest request){
        logger.info("Exception handle call with request : {}",request);
        mediaUploadService.exceptionHandler(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/google-cred-info")
    public ResponseEntity<?> getGoogleCredentials(){
        mediaUploadService.getGoogleCredentials();
        return new ResponseEntity<>(mediaUploadService.getGoogleCredentials(),HttpStatus.OK);
    }
}
