# AWS-SM
aws.secret.enabled = true
aws.secret.region=us-west-1
aws.sm.bazaarify.secret.id=qa/mysql/bazaarify/master
aws.sm.social.secret.id=qa/mysql/app_social/master
aws.sm.gnip.secret.id=qa/social/secrets


platform.datasource.jdbcUrl=***********************************************************************************************************
platform.datasource.driver-class-name=com.mysql.jdbc.Driver
platform.datasource.username=app_social
platform.datasource.password=Admin##764211342111
platform.datasource.poolName=platform-Hikari-Pool
platform.datasource.maximumPoolSize=200
platform.datasource.minimumIdle=5
platform.datasource.maxLifetime=2000000
platform.datasource.connectionTimeout=60000
platform.datasource.idleTimeout=30000
platform.datasource.test-while-idle = true
platform.datasource.test-on-borrow = true
platform.datasource.time-between-eviction-runs-millis = 60000
platform.datasource.validation-query = SELECT 1
platform.datasource.validation-query-timeout = 3
platform.datasource.pool-prepared-statements=true
platform.datasource.max-open-prepared-statements=250
platform.datasource.max-sql-prepared-statements=2048
platform.datasource.use-server-prepared-statements=true
platform.datasource.remove-abandoned = true
platform.datasource.remove-abandoned-timeout = 120
redis.key.prefix.name = social::


## social socialapp/@admin99008
social.datasource.jdbcUrl=**********************************************************************************************
social.datasource.driver-class-name=com.mysql.jdbc.Driver
social.datasource.username=app_social
social.datasource.password=Admin##764211342111
social.datasource.poolName=social-Hikari-Pool
social.datasource.maximumPoolSize=50
social.datasource.minimumIdle=5
social.datasource.maxLifetime=2000000
social.datasource.connectionTimeout=60000
social.datasource.idleTimeout=30000
social.datasource.test-while-idle = true
social.datasource.test-on-borrow = true
social.datasource.time-between-eviction-runs-millis = 60000
social.datasource.validation-query = SELECT 1
social.datasource.validation-query-timeout = 3
social.datasource.pool-prepared-statements=true
social.datasource.max-open-prepared-statements=250
social.datasource.max-sql-prepared-statements=2048
social.datasource.use-server-prepared-statements=true
social.datasource.remove-abandoned = true
social.datasource.remove-abandoned-timeout = 120

# Spring JPA
#turn off hibernate validation
spring.jpa.properties.javax.persistence.validation.mode=none
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
#spring.jpa.properties.hibernate.ejb.interceptor=com.birdeye.social.interceptors.CacheEvictInterceptor
# for sql print 
#debug=true
# Server property
server.port=8099
security.basic.enabled=false

spring.jmx.enabled=true
endpoints.jmx.enabled=true

## for pretty printing of json when endpoints accessed over HTTP
http.mappers.jsonPrettyPrint=true

####### actuator

#management.security.enabled=false
# basic auth username for actuator endpoints
security.user.name=social
# basic auth password for actuator endpoints
security.user.password=social
#Spring Boot security list of authorized Url and context of endPoints that we want to safeguard
security.authorized.url=/swagger-ui.html,/auditevents,/beans,/configprops,/heapdump,/mappings,/loggers,/info,/env,/autoconfig,/trace,/heapdump,/dump,/metrics,/metrics/**,/env/**
# sets health endpoint as non sensitive - can be accesed without auth
endpoints.health.sensitive=false
management.endpoint.health.show-details=always

#spring jest
#spring.elasticsearch.jest.uris=https://qa-es.birdeye.com
#spring.elasticsearch.jest.connection-timeout=5000
#spring.elasticsearch.jest.read-timeout=5000
#spring.elasticsearch.jest.multi-threaded=true


# Swagger Property
enable.social.swagger=true

#Javamelody configuration
enable.social.javamelody=true

# Threadpool config
async.core.pool.size=10
async.max.pool.size=40
async.pool.queue.size=1000

#Multipart Handling
spring.http.multipart.max-file-size=50MB
spring.http.multipart.max-request-size=55MB

#turn off hibernate validation

#Log config
#logging.file=${catalina.base}/logs/social.log
#Logging pattern for file
#logging.pattern.file= %d{yyyy-MM-dd HH:mm:ss.SSS} reqid:%X{requestId} %5p [%thread] %logger{36} - %msg%n

#custom properties
#hourly pattern : 	0 0 0/1 1/1 * ? *
#half hourly pattern : 0 0,30 * * * *

# Swagger Property
#enable.social.swagger=true

fb.reconnect.job.cron.pattern=0 0 0/1 1/1 * ? *

#Kafka
kafka.server=qa-kafka.birdeye.internal:9092

#New Kafka cluster
kafka.server.new=qa-kafka.birdeye.internal:9092
kafka.server.new.us=qa-kafka.birdeye.internal:9092

#Redis Configs
redis.hostname=***********
redis.port=6379
redis.password=foobird
redis.database=10
redis.timeout=3000
redis.ssl.enabled=false

birdeye.scheduler.endpoint=http://api.qa1.birdeye.com:8080
birdeye.social.email.endpoint=http://api.qa1.birdeye.com:8080
platform.base.url=http://api.qa8.birdeye.com:8080/resources/v1/

webhose.api.key=ca78587a-23b9-4e53-9f1a-059513c17181
webhose.kafka.request.topic=webhose-activities

aws.es.host=https://qa-es.birdeye.com
aws.es.service.name=es
aws.region=us-west-1
elasticsearch.http.maxConnection=50
elasticsearch.default.connect.timeout.millis=1000
elasticsearch.default.socket.timeout.millis=12000


#Gnip
gnip.username=<EMAIL>
#gnip.password=P@55word
gnip.password=PuffPuffPass
gnip.account=BirdEye
gnip.type=dev

#Encryption Method name
aes.encryption=fz&764ft#@kjgbhd


#BulkSchedule
social.schdl.post.doup.callback.topic=social-schd-post-doup-callback-event

#Asset Library Topics
asset.lib.action.event.topic=asset-lib-action-event
asset.lib.es.upsert.event.topic=asset-lib-es-upsert-event
asset.lib.post.asset.child.action.event.topic=asset-lib-post-asset-action-child-event
asset.lib.bulk.action.atomic.event.topic=asset-lib-bulk-action-atomic-event
asset.lib.account.resource.cleanup.event.topic=asset-lib-account-resource-cleanup-event
asset.lib.asset.cleanup.event.topic=asset-lib-asset-cleanup-event
asset.lib.db.es.sync.event.topic=asset-lib-db-es-sync-event
asset.lib.asset.action.audit.event.topic=asset-lib-asset-action-audit-event

#Apple business connect configuration
apple.client.id=d42a825b-958d-40c4-b456-dee16dcd8644
apple.client.secret=1esPYqKrhXnPzuIKKt5Esc3srZKqnvbIo2YQmFi7JXjGJfBZO6VnlrBxbX9iTSre

#Social Tagging Topics
social.post.tag.mapping.event.topic=social-post-tag-map-event
social.engage.tag.mapping.event.topic=social-engage-tag-map-event
social.asset.tag.mapping.event.topic=social-asset-tag-map-event
social.draft.tag.mapping.event.topic=social-draft-tag-map-event
social.post.lib.tag.mapping.event.topic=social-post-lib-tag-map-event
social.entity.bulk.tag.mapping.event.topic=social-entity-bulk-tag-map-event
asset.lib.social.tag.migration.event.topic=asset-lib-to-social-tag-mig-event

pexels.api.key=GhgoHBJT4ZYEAoBqwBTY50XLm1TT9Y6VaCjnVrp2mrVJgC0KNkr3OGX1
social.server.region=US