# AWS-SM
aws.secret.enabled = true
aws.secret.region=eu-central-1
aws.sm.bazaarify.secret.id=prod/mysql/app_social/master
aws.sm.social.secret.id=prod/mysql/appsocial/master
aws.sm.gnip.secret.id=prod/social/secrets

platform.datasource.jdbcUrl=*******************************************************************************************************************************************
platform.datasource.driver-class-name=com.mysql.jdbc.Driver
platform.datasource.username=app_social
platform.datasource.password=Admin##764211342111
platform.datasource.poolName=platform-Hikari-Pool
platform.datasource.maximumPoolSize=200
platform.datasource.minimumIdle=5
platform.datasource.maxLifetime=2000000
platform.datasource.connectionTimeout=60000
platform.datasource.idleTimeout=30000
platform.datasource.test-while-idle = true
platform.datasource.test-on-borrow = true
platform.datasource.time-between-eviction-runs-millis = 60000
platform.datasource.validation-query = SELECT 1
platform.datasource.validation-query-timeout = 3
platform.datasource.pool-prepared-statements=true
platform.datasource.max-open-prepared-statements=250
platform.datasource.max-sql-prepared-statements=2048
platform.datasource.use-server-prepared-statements=true
platform.datasource.remove-abandoned = true
platform.datasource.remove-abandoned-timeout = 120


## social
social.datasource.jdbcUrl=***************************************************************************************************************************
social.datasource.driver-class-name=com.mysql.jdbc.Driver
social.datasource.username=appsocial
social.datasource.password=Social$#975695
social.datasource.poolName=social-Hikari-Pool
social.datasource.maximumPoolSize=70
social.datasource.minimumIdle=5
social.datasource.maxLifetime=2000000
social.datasource.connectionTimeout=60000
social.datasource.idleTimeout=30000
social.datasource.test-while-idle = true
social.datasource.test-on-borrow = true
social.datasource.time-between-eviction-runs-millis = 60000
social.datasource.validation-query = SELECT 1
social.datasource.validation-query-timeout = 3
social.datasource.pool-prepared-statements=true
social.datasource.max-open-prepared-statements=250
social.datasource.max-sql-prepared-statements=2048
social.datasource.use-server-prepared-statements=true
social.datasource.remove-abandoned = true
social.datasource.remove-abandoned-timeout = 120

##
social.archival.datasource.jdbcUrl=**************************************************************************************************************************
social.archival.datasource.driver-class-name=com.mysql.jdbc.Driver
social.archival.datasource.username=archive_usr
social.archival.datasource.password=yDJV82p2s895qL
social.archival.datasource.poolName=social-archive-Hikari-Pool
social.archival.datasource.maximumPoolSize=10
social.archival.datasource.minimumIdle=5
social.archival.datasource.maxLifetime=2000000
social.archival.datasource.connectionTimeout=60000
social.archival.datasource.idleTimeout=30000
social.archival.datasource.test-while-idle = true
social.archival.datasource.test-on-borrow = true
social.archival.datasource.time-between-eviction-runs-millis = 60000
social.archival.datasource.validation-query = SELECT 1
social.archival.datasource.validation-query-timeout = 3
social.archival.datasource.pool-prepared-statements=true
social.archival.datasource.max-open-prepared-statements=250
social.archival.datasource.max-sql-prepared-statements=2048
social.archival.datasource.use-server-prepared-statements=true
social.archival.datasource.remove-abandoned = true
social.archival.datasource.remove-abandoned-timeout = 120
redis.key.prefix.name = social::

# Spring JPA
#turn off hibernate validation
spring.jpa.properties.javax.persistence.validation.mode=none
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
#spring.jpa.properties.hibernate.ejb.interceptor=com.birdeye.social.interceptors.CacheEvictInterceptor
# for sql print
#debug=true
# Server property
server.port=8080
security.basic.enabled=false

spring.jmx.enabled=true
endpoints.jmx.enabled=true

## for pretty printing of json when endpoints accessed over HTTP
http.mappers.jsonPrettyPrint=true

####### actuator

#management.security.enabled=true
# basic auth username for actuator endpoints
security.user.name=social
# basic auth password for actuator endpoints
security.user.password=social
#Spring Boot security list of authorized Url and context of endPoints that we want to safeguard
security.authorized.url=/swagger-ui.html,/auditevents,/beans,/configprops,/heapdump,/mappings,/loggers,/info,/env,/autoconfig,/trace,/heapdump,/dump,/metrics,/metrics/**,/env/**
# sets health endpoint as non sensitive - can be accesed without auth
endpoints.health.sensitive=false

# Swagger Property
enable.social.swagger=true

#Javamelody configuration
enable.social.javamelody=true

# Threadpool config
async.core.pool.size=10
async.max.pool.size=40
async.pool.queue.size=1000

#Multipart Handling
spring.http.multipart.max-file-size=50MB
spring.http.multipart.max-request-size=55MB

#Redis Configs
redis.hostname=master.eucent1-prod-common-elasticache-redis.eifjpx.euc1.cache.amazonaws.com
redis.port=6379
redis.password=jf!20093wDKm3210
redis.database=10
redis.timeout=3000
redis.max.pool.size=50
redis.min.idle.size=10
redis.max.idle.size=30
redis.ssl.enabled=true

birdeye.scheduler.endpoint=http://quartzelb.birdeye.com:8080
birdeye.social.email.endpoint=http://api.birdeye.internal/
platform.base.url=http://api.birdeye.internal/resources/v1/

#Gnip
gnip.username=<EMAIL>
gnip.password=P@55word
gnip.account=BirdEye
gnip.type=prod

#Kafka
kafka.server=eucent1-common-kafka1.birdeye.internal:9092,eucent1-common-kafka2.birdeye.internal:9092,eucent1-common-kafka3.birdeye.internal:9092
#New Kafka cluster
kafka.server.new=eucent1-common-kafka1.birdeye.internal:9092,eucent1-common-kafka2.birdeye.internal:9092,eucent1-common-kafka3.birdeye.internal:9092
kafka.server.new.us=common-kafka1.birdeye.internal:9092,common-kafka2.birdeye.internal:9092,common-kafka3.birdeye.internal:9092,common-kafka4.birdeye.internal:9092,common-kafka5.birdeye.internal:9092



#Elastic search jest client configurations
#spring.elasticsearch.jest.uris=https://vpc-prod-insight-review-avdq7v4pvq22qg75gfqjeh4qwy.us-west-1.es.amazonaws.com
#spring.elasticsearch.jest.connection-timeout=5000
#spring.elasticsearch.jest.read-timeout=5000
#spring.elasticsearch.jest.multi-threaded=true

webhose.api.key=ca78587a-23b9-4e53-9f1a-059513c17181
webhose.kafka.request.topic=webhose-activities

aws.es.host=https://eucent1-prod-es.birdeye.com
aws.es.service.name=es
aws.region=eu-central-1
elasticsearch.http.maxConnection=50
elasticsearch.default.connect.timeout.millis=1000
elasticsearch.default.socket.timeout.millis=12000

#Encryption Method name
aes.encryption=fz&764ft#@kjgbhd

#BulkSchedule
social.schdl.post.doup.callback.topic=social-schd-post-doup-callback-event

#Asset Library Topics
asset.lib.action.event.topic=asset-lib-action-event
asset.lib.es.upsert.event.topic=asset-lib-es-upsert-event
asset.lib.post.asset.child.action.event.topic=asset-lib-post-asset-action-child-event
asset.lib.bulk.action.atomic.event.topic=asset-lib-bulk-action-atomic-event
asset.lib.account.resource.cleanup.event.topic=asset-lib-account-resource-cleanup-event
asset.lib.asset.cleanup.event.topic=asset-lib-asset-cleanup-event
asset.lib.db.es.sync.event.topic=asset-lib-db-es-sync-event
asset.lib.asset.action.audit.event.topic=asset-lib-asset-action-audit-event

#Apple business connect configuration

#Social Tagging Topics
social.post.tag.mapping.event.topic=social-post-tag-map-event
social.engage.tag.mapping.event.topic=social-engage-tag-map-event
social.asset.tag.mapping.event.topic=social-asset-tag-map-event
social.draft.tag.mapping.event.topic=social-draft-tag-map-event
social.post.lib.tag.mapping.event.topic=social-post-lib-tag-map-event
social.entity.bulk.tag.mapping.event.topic=social-entity-bulk-tag-map-event
asset.lib.social.tag.migration.event.topic=asset-lib-to-social-tag-mig-event
apple.client.id=14045cfa-91f7-2000-15b6-11d22fa9ee00
apple.client.secret=8bIxAM3uRR8tuSGJM59AHt1svdvfOwSUuUl7ySB08QyZnkFTVn5Wg5aD0baZOYXQ


social.server.region=EU
debug=true