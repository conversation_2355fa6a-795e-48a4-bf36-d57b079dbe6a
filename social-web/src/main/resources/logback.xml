<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<property name="log.path" value="${catalina.base}/logs" />
	<appender name="FILE"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${log.path}/social.log</file>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<Pattern>
				%d{yyyy-MM-dd HH:mm:ss.SSS} reqid:%X{requestId} armorId:%X{armor-request-id} %5p [%thread] %logger{36} - %msg%n
			</Pattern>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<!-- rollover daily -->
			<fileNamePattern>${log.path}/social.%d{yyyy-MM-dd}.log</fileNamePattern>
			<!-- keep 30 days' worth of history -->
			<maxHistory>30</maxHistory>
		</rollingPolicy>
	</appender>
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<layout class="ch.qos.logback.classic.PatternLayout">
			<Pattern>
				%d{yyyy-MM-dd HH:mm:ss.SSS} reqid:%X{requestId} armorId:%X{armor-request-id} %5p [%thread] %logger{36} - %msg%n
			</Pattern>
		</layout>
		<encoder class="net.logstash.logback.encoder.LogstashEncoder">
			<fieldNames>
				<version>[ignore]</version>
				<levelValue>[ignore]</levelValue>
			</fieldNames>
			<timestampPattern>yyyy-MM-dd HH:mm:ss.SSS</timestampPattern>
		</encoder>
	</appender>
	<appender name="FILE_JSON"
			  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${log.path}/social-json.log</file>
		<encoder class="net.logstash.logback.encoder.LogstashEncoder">
			<fieldNames>
				<version>[ignore]</version>
				<levelValue>[ignore]</levelValue>
			</fieldNames>

			<timestampPattern>yyyy-MM-dd HH:mm:ss.SSS</timestampPattern>
		</encoder>
		<rollingPolicy
				class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${log.path}/social-json.%d{yyyy-MM-dd}.log
			</fileNamePattern>
			<maxHistory>30</maxHistory>
		</rollingPolicy>
	</appender>
	<appender name="CONSOLE_JSON"
			  class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="net.logstash.logback.encoder.LogstashEncoder">
			<fieldNames>
				<version>[ignore]</version>
				<levelValue>[ignore]</levelValue>
			</fieldNames>

			<timestampPattern>yyyy-MM-dd HH:mm:ss.SSS</timestampPattern>
		</encoder>
	</appender>

	<springProfile name="demo">
		<root level="info">
			<appender-ref ref="FILE" />
			<appender-ref ref="STDOUT" />
		</root>
		<logger name="jsonLogger" level="info">
			<appender-ref ref="FILE_JSON" />
		</logger>
	</springProfile>
	<!--  for prod we don't need log in catalina  -->
	<springProfile name="production">
		<root level="info">
			 <appender-ref ref="STDOUT" />
		</root>
		<logger name="jsonLogger" level="info">
			<appender-ref ref="CONSOLE_JSON" />
		</logger>
	</springProfile>

	<springProfile name="production-eu">
		<root level="info">
			<appender-ref ref="STDOUT" />
		</root>
		<logger name="jsonLogger" level="info">
			<appender-ref ref="CONSOLE_JSON" />
		</logger>
	</springProfile>

	<springProfile name="preprod">
		<root level="info">
			<appender-ref ref="STDOUT" />
		</root>
		<logger name="jsonLogger" level="info">
			<appender-ref ref="CONSOLE_JSON" />
		</logger>
	</springProfile>

	<springProfile name="local">
		<root level="info">
			<appender-ref ref="STDOUT" />
		</root>
		<logger name="jsonLogger" level="info">
			<appender-ref ref="CONSOLE_JSON" />
		</logger>
	</springProfile>

</configuration>
