package com.birdeye.social.constant;

import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

public class Constants {
	public static final Double LAT_LNG_MULTIPLIER = *********.0;
	public static final int MAX_RESELLER_HIERARCHY_DEPTH = 10;

	public static final String LINKEDIN_ORG_PREFIX = "urn:li:organization:";

	public static final String IMAGE_LINKEDIN = "image";

	public static final String UTC = "UTC";

	public static final String DISPLAY_MEDIA_ASSET = "digitalmediaAsset";

	public static final String SOCIAL_TASK_EXECUTOR = "SocialTaskExecutor";

	public static final String GNIP_USAGE_URL = "https://gnip-api.twitter.com/metrics/usage/accounts/BirdEye.json";

	public static final String GNIP_DAILY_USAGE_URL = "https://gnip-api.twitter.com/metrics/usage/accounts/BirdEye.json?bucket=day";

	public static final String GNIP_USAGE_TEMPLATE = "business_gnip_usage_template.ftl";

	public static final String GNIP_INVALID_RULES_TEMPLATE = "business_gnip_invalid_rules_template.ftl";

	public static final String JOB_GROUP = "JOB_GROUP";
	public static final String JOB_KEY = "JOB_KEY";
	public static final String BIRDEYE_SOCIAL_EMAIL_ENDPOINT = "birdeye.social.email.endpoint";

	public static final String SUCCESS = "success";

	public static final String FAILURE = "failure";

	public static final String FB_GP_YTUBE_MENTION_TOPIC = "fb-gp-mention";

	public static final Integer GNIP_DAILY_QUOTA = 500;

	public static final Integer GNIP_MONTHLY_QUOTA = 200000;

	public static final String INTEGRATION_TOPIC = "fb-gmb-reconnect";

	public static final String ENABLE_NOTIFICATION_TOPIC = "enable-gmb-notification";

	public static final String FIREBASE_DB_UPDATE = "nexus-firebase-db-insert-update";

	public static final String FB_MENTION_TOPIC = "fb-mention";

	public static final String GMB_REVIEW_NOTIFICATION_TOPIC = "GMB-REVIEW-NOTIFICATION-TOPIC";

	public static final String GMB_QNA_NOTIFICATION_TOPIC = "GMB-QNA-NOTIFICATION-TOPIC";
	public static final String GMB_UPDATE_NOTIFICATION_TOPIC = "social-gmb-update-notification-topic";

	public static final String GMB_MEDIA_NOTIFICATION_TOPIC = "GMB-MEDIA-NOTIFICATION-TOPIC";

	public static final String FB_REVIEW_NOTIFICATION_TOPIC = "FB-REVIEW-NOTIFICATION-TOPIC";

	public static final String FB_RATINGS_SUBSCRIPTION_FIELDS = "ratings";

	public static final String SEND_EMAIL_NEXUS_TOPIC = "nexus-email";

	public static final String FB_PAGE_MAPPING_REMOVED = "facebook-page-mapping-removed";

	public static final String FB_PAGE_MAPPING_ADDED = "facebook-page-mapping-added";

	public static final String FB_MESSENGER_RATINGS_SUBSCRIPTION_FIELDS = "messages,message_deliveries,message_echoes,message_reads,ratings";

	public static final String PAGES_MESSAGING = "pages_messaging";
	public static final String FB_FEED_SUBSCRIPTION_FIELD = "feed";
	public static final String FB_MENTION_SUBSCRIPTION_FIELD = "mention";

	public static final String GMB_LOCATION_SERVICE_AREA_UPDATE = "GMB_LOCATION_SERVICE_AREA_UPDATE";
	public static final String GMB_GOOGLE_LOCATION_NOT_PRESENT = "GOOGLE_LOCATION_NOT_PRESENT";

	public static final String GMB_LOCATION_SUSPENDED = "GMB_LOCATION_SUSPENDED";
	public static final String GMB_LOCATION_CHECK_JOB_PREFIX = "GMB Location Check Job  : {}";

	public static final String GMB_LOCATION_SYNC_JOB_REFRESH_TOKEN_PREFIX = "GMB Location Check Job: refreshTokenId  : {}, {}";
	public static final String GMB_LOCATION_SYNC_JOB_REFRESH_TOKEN_PREFIX_VALUES = "GMB Location Check Job: refreshTokenId  : {}, {} %s";

	public static final String GMB_LOCATION_STATUS_JOB_ENTERPRISE_ID_PREFIX_VALUES = "GMB Location Status Job: enterpriseId  : {}, {} %s";

	public static final String GMB_LOCATION_CHECK_JOB_LOCATION_PREFIX = "GMB Location Check Job: Location Id: {}, {}";

	public static final String SOCIAL_PAGE_REMOVED = "social-page-removed";

	public static final String GMB_PAGE_MAPPING_REMOVED = "gmb-page-mapping-removed";
	public static final String PRIORITY_QUEUE_FOR_REFRESH_TOKENS = "refreshTokenPriorityQueue";

	public static final String PRIORITY_QUEUE_FOR_GMB_ENTERPRISE_IDS = "gmb-location-state-enterprise";

	public static final String GMB_ACCOUNT_SYNC_TOPIC = "gmb-account-periodic-sync";

	public static final String CUSTOMER_MEDIA_FROM_GMB = "get-customer-media-from-gmb";

	public static final String CUSTOMER_MEDIA_FETCHED = "customer-media-fetched";

	public static final String GMB_LOCATION_STATE_SYNC = "gmb-location-state-sync-up";

	public static final String GMB_PAGE_MAPPING_ADDED = "gmb-page-mapping-added";

	public static final String TWITTER_PAGE_MAPPING_REMOVED = "twitter-page-mapping-removed";

	public static final String TWITTER_PAGE_MAPPING_ADDED = "twitter-page-mapping-added";

	public static final String LINKEDIN_PAGE_MAPPING_REMOVED = "linkedin-page-mapping-removed";

	public static final String INSTAGRAM_PAGE_MAPPING_REMOVED = "instagram-page-mapping-removed";

	public static final String LINKEDIN_PAGE_MAPPING_ADDED = "linkedin-page-mapping-added";

	public static final String INSTAGRAM_PAGE_MAPPING_ADDED = "instagram-page-mapping-added";

	public static final String TIKTOK_PAGE_MAPPING_ADDED = "tiktok-page-mapping-added";

	public static final String INSTAGRAM_URL = "https://www.instagram.com/";

	public static final String FORMAT_MM_DD_YYYY = "MM/dd/yyyy";

	public static final String CHANNEL_DISCONNECTED_EMAIL_TYPE = "channel-disconnect-alert";

	public static final String CHANNEL_DISCONNECTED_UUID_STRING = "channel_disconnect";

	public static final String INSTAGRAM_PAGE_UPDATED = "instagram-page-updated";
	public static final String TWITTER_PAGE_UPDATE = "social-twitter-update";
	public static final String LINKEDIN_PAGE_UPDATE = "social-linkedin-update";
	public static final String GMB_ACCOUNT_FETCH_PAGE = "gmb-account-fetch-page";
	public static final String INT_EMAIL_BROKEN_INTEGRATIONS = "init-email-broken-integrations";
	public static final String FETCH_ENT_BROKEN_INTEGRATIONS_TOPIC = "fetch-ent-broken-integrations";
	public static final String BROKEN_INTEGRATION_EVENT_OVERALL_STATUS_TOPIC_NAME = "broken_integration_overall_status_topic";
	public static final String BROKEN_INTEGRATION_EVENT_BUSINESS_STATUS_TOPIC_NAME = "broken_integration_business_status_topic";

	public static final String FIRST_BROKEN_INTEGRATION_TOPIC = "first_broken_integration_topic";
	public static final String INIT_DISCONNECTED_PAGE = "init_disconnected_page";
	public static final String OPEN_URL_GMB_ACCOUNT_FETCH_PAGE = "gmb-account-fetch-page-openurl";

	public static final String UPDATE_GMSG_LOCATION_STATE = "update-gMsg-location-state";

	public static final String OPEN_URL_REQUEST_PREFIX = "openurl-";
	public static final String PRIORITY_QUEUE_FOR_GMB_DISCONNECTED_ENTERPRISE = "gmbDisconnectedPriorityQueue";
	public static final String PRIORITY_QUEUE_FOR_FB_DISCONNECTED_ENTERPRISE = "fbDisconnectedPriorityQueue";

	public static final String PRIORITY_QUEUE_FOR_TWITTER_DISCONNECTED_ENTERPRISE = "twitterDisconnectedPriorityQueue";

	public static final String PRIORITY_QUEUE_FOR_LINKEDIN_DISCONNECTED_ENTERPRISE = "linkedinDisconnectedPriorityQueue";
	public static final String PRIORITY_QUEUE_FOR_INSTAGRAM_DISCONNECTED_ENTERPRISE = "instagramDisconnectedPriorityQueue";

	public static final String CHANNEL_DISCONNECT_PREFIX = "[Channel Disconnect Action] for channel";

	public static final String GMB_ACCOUNT_RECONNECT_PAGE = "gmb-account-reconnect-page";

	public static final String GMB_ACCOUNT_RECONNECT_PAGE_RESELLER = "gmb-account-reconnect-page-reseller";

	public static final String FB_PAGE_DATA_RESPONSE = "fb-page-data-response";
	public static final String FREE_FB_PAGE_DATA_RESPONSE = "free-fb-page-data-response";

	public static final String FB_LISTING_VALIDATOR_PAGE_DATA_RESPONSE = "fb-listing-validator-page-data";

	public static final String FETCH_LISTING_FB_PAGE_DATA = "fetch-listing-fb-page-data";

	public static final String GENERATE_EMAIL = "generate_email";

	public static final String GENERATE_EMAIL_TYPE = "generate_email-alert";

	public static final String DASHBOARD_ENTERPRISE_SETUP_SOCIAL_CONNECT_URL = "/dashboard/enterprise/setup/social/connect/";

	public static final String FIREBASE_UPDATE = "update";

	public static final String WEBHOSE_MENTION_SAVE_TOPIC = "webhose-mention-save";

	public static final String GNIP_RULE_DISABLE_TOPIC = "gnip-rule-disable";

	public static final String MENTION_INSERT_ES_TOPIC = "mention-insert";

	public static final String LOCATION_MOVEMENT_RESPONSE_AUDIT = "business-conversion-audit";

	public static final String LOCATION_MOVEMENT_GBM = "location-movement-gbm";

	public static final String SOCIAL_MODULE = "social-integration";

	public static final String LOCATION_MOVEMENT_LOG_PREFIX = "{LOCATION MOVEMENT] ";

	public static final String MESSENGER_NOT_ENABLED = "messengerNotEnabled";

	public static final String LOCATION_ALREADY_LAUNCHED = "locationAlreadyLaunched";

	public static final String INSUFFICIENT_SCOPE = "insufficientScope";

	public static final String DEFAULT_MESSAGING = "defaultMessaging";

	public static final String FOUND_ENABLED_LOCATION = "Found enabled locations with same place-ids";

	public static final String INSUFFICIENT_AUTHENTICATION_SCOPE = "Request had insufficient authentication scopes";

	public static final String ACCOUNT_UNMAPPED = "accountUnmapped";

	public static final String PERMISSION_MISSING = "permissionMissing";

	public static final String DEFAULT_ERROR_MESSAGE_START = "Change permission setting and reconnect this account to ";
	public static final String DEFAULT_ERROR_MESSAGE_YOUTUBE_START = "Change permission settings in your YouTube account to ";
	public static final String DEFAULT_ERROR_MESSAGE_YOUTUBE_END = "Reconnect this account after making the changes";

	public static final String DEFAULT_ERROR_INSTAGRAM_MESSAGE_START = "Additional permissions are required for ";

	public static final String DEFAULT_ERROR_TWITTER_MESSAGE_START = "Additional permissions are required for ";

	public static final String DEFAULT_ERROR_TIKTOK_MESSAGE_START = "Additional permissions are required for ";
	public static final String DEFAULT_ERROR_WHATSAPP_MESSAGE_START = "Additional permissions are required for ";


	public static final List<String> FACEBOOK_ERROR_WITHOUT_BODY = Arrays
			.asList("Sorry, this content isn't available right now");

	public static final String MESSENGER_NAME_VERIFICATION_ERROR = "messengerNameVerificationError";

	public static final String MESSENGER_VERIFICATION_ERROR = "messengerVerificationError";

	public static final List<String> TWITTER_PAGE_INVALID_ERROR = Arrays.asList(
			"Your Twitter account may have been suspended. Please visit Twitter Help Center to unsuspend your account.");

	public static final String ERROR = "ERROR";

	public static final String SETUP_MESSAGING = "setupMessaging";

	public static final String INSUFFICIENT_PERMISSION = "insufficientPermission";

	public static final String NO_PAGES_FOUND = "noPagesFound";

	public static final String DEFAULT = "default";

	public static final String DEFAULT_MESSAGE = "Something went wrong.";

	public static final String BUSINESS_MESSAGING = "business.manage";

	public static final String UPDATE_ACCESS_TOKEN = "update-access-token";

	public static final String RECONNECT_ACCESS_TOKEN = "reconnect-access-token";

	public static final String BUSINESS_COMMUNICATION = "businesscommunications";

	public static final String INTEGRATION = "integration";

	public static final String REPORT = "reportingPermissionMissing";

	public static final String SUSPENDED = "suspended";

	public static final String MEMBER_RESTRICTED = "memberRestricted";

	public static final String MESSAGING_NOT_ALLOWED = "messsagingNotAllowed";

	public static final String SPLIT_STRING_FOR_GMB = "https://www.googleapis.com/auth/";
	public static final String SOCIAL_PAGE_CONNECT = "social-page-connect";
	public static final String SOCIAL_SETUP_AUDIT = "social-setup-audit";
	public static final String FB_DEBUG_TOKEN = "fb-page-debug-token";
	public static final String SOCIAL_PAGE_SYNC = "social-page-sync";
	public static final String SOCIAL_APPLICATION = "Social";
	public static final String SOCIAL_STREAM_CONNECT = "social-stream-connect";

	public static final String ACCOUNTS = "accounts/";

	public static final String RESELLER = "reseller";
	public static final String CONNECT = "connect";

	public static final String ENTERPRISE = "enterprise";
	public static final String CHECK_VALIDITY = "check_validity";
	public static final String GMB = "gmb";
	public static final String FACEBOOK = "facebook";
	public static final String WHATSAPP = "whatsapp";
	public static final List<Integer> RESELLER_MIGRATION_AVOID_LIST = Arrays.asList(1, 2, 3, 4);
	// isSelected is set to 1 when page is already added to reseller or enterprise
	public static final String IS_SELECTED = "isSelected";
	public static final String ENABLE = "enable";
	public static final String VERIFY = "verify";
	public static final String RESOLVE_OWNERSHIP_CONFLICT = "resolveOwnershipConflict";
	public static final String COMPLY_WITH_GUIDELINES = "complyWithGuidelines";
	public static final String WAIT_FOR_VOICE_OF_MERCHANT = "waitForVoiceOfMerchant";
	public static final String HAS_VOICE_OF_MERCHANT = "hasVoiceOfMerchant";
	public static final String RECOMMENDED_REASON = "recommendationReason";
	public static final String RECOMMENDATION_REASON_UNSPECIFIED = "RECOMMENDATION_REASON_UNSPECIFIED";
	public static final String BUSINESS_LOCATION_DISABLED = "BUSINESS_LOCATION_DISABLED";
	public static final String BUSINESS_LOCATION_SUSPENDED = "BUSINESS_LOCATION_SUSPENDED";
	public static final String MESSAGING_AUDIT = "gmsg_audit";

	public static final String APPLE_CHAT_CALL_BACK_EVENT = "apple-chat-callback-event";

	public static final String TWITTER_TOKEN_CACHE = "twitter_token_cache";
	public static final String BTP_PRIORITY_CACHE = "btp_priority_cache";

	public static final String SOCIAL_BUSINESS_PROPERTY = "socialBusinessProperty";

	public static final String IMAGE = "IMAGE";
	public static final String REEL = "REEL";
	public static final String VIDEO = "VIDEO";
	public static final String USER = "user";
	public static final String COMMENT = "comment";
	public static final String POST = "post";
	public static final String ALL = "*";

	public static final String MESSENGER_TAG_HUMAN_AGENT = "HUMAN_AGENT";

	public static final String HOST_NAME = "host";

	public static final String DB_PORT = "port";
	public static final String DB_USERNAME = "username";
	public static final String DB_PASSWORD = "password";

	public static final String BAZAARIFY_MASTER_HOST_NAME = "bazaarify_master_host_name";
	public static final String BAZAARIFY_MASTER_DB = "bazaarify"; // bazaarify db name
	public static final String BAZAARIFY_MASTER_DB_PORT = "bazaarify_port";
	public static final String BAZAARIFY_MASTER_DB_USERNAME = "bazaarify_db_username";
	public static final String BAZAARIFY_MASTER_DB_PASSWORD = "bazaarify_db_password";
	public static final String BAZAARIFY_MASTER_DB_NAME = "bazaarify_db_name";

	public static final String SOCIAL_MASTER_DB = "social_master_db";

	public static final String GNIP_SECRET = "gnip_secret";
	public static final String GNIP_USERNAME = "gnip_username";
	public static final String GNIP_PASSWORD = "gnip_password";
	public static final String SOCIAL_HOST_NAME = "social_host_name";
	public static final String SOCIAL_DB_PORT = "social_db_port";
	public static final String SOCIAL_USERNAME = "social_username";
	public static final String SOCIAL_DB_NAME = "social_db_name";
	public static final String SOCIAL_DB_NAME_VALUE = "social";

	public static final String SOCIAL_PASSWORD = "social_password";

	public static final String SOCIAL_JDBC_URL = "********************************************************";
	public static final String BAZAARIFY_JDBC_URL = "********************************************************";
	public static final String AUDIT_EVENT = "social-audit-event";
	public static final String YOUTUBE_CHANNEL_URL = "https://www.youtube.com/channel/";
	public static final String YOUTUBE_VIDEO_URL = "https://www.youtube.com/watch?v=";

	public static final String FACEBOOK_PAGE_INSIGHTS = "facebook-page-insights";
	public static final String TIKTOK_PAGE_INSIGHTS = "tiktok-page-insights";
	public static final String TWITTER_PAGE_INSIGHTS = "twitter-page-insights";
	public static final String LINKEDIN_PAGE_INSIGHTS = "linkedin-page-insights";
	public static final String INSTAGRAM_PAGE_INSIGHTS = "instagram-page-insights";
	public static final String INSTAGRAM_PAGE_INSIGHTS_CORRECTIONS = "instagram-page-insights_correction";
	public static final String YOUTUBE_PAGE_INSIGHTS = "youtube-page-insights";

	public static final Integer ERROR_CONSTANT_FOR_UNKNOWN_ERROR = 34;
	public static final Integer ERROR_CONSTANT_FOR_PENDING_STATE_FB = 1366059;

	public static final String SOCIAL_RESELLER__DASHBOARD_REPORTS = "social-reseller-dashboard-reports";

	public static final String INSTAGRAM = "instagram";
	public static final String LINKEDIN = "linkedin";
	public static final String TWITTER = "twitter";
	public static final String YOUTUBE = "youtube";
	public static final int TWITTER_MAX_POSTS_INSIGHT = 250;
	public static final int TWITTER_MAX_POST_VIDEO_INSIGHTS = 100;
	public static final int TWITTER_MAX_POSTS_HISTORICAL = 25;
	public static final int SOCIAL_PROPERTY_NEXT_SYNC_DAY = 7;

	public static final String desk_map_vu_cnt = "desk_map_vu_cnt";
	public static final String mob_map_vu_cnt = "mob_map_vu_cnt";
	public static final String desk_srch_vu_cnt = "desk_srch_vu_cnt";
	public static final String mob_srch_vu_cnt = "mob_srch_vu_cnt";
	public static final String wsite_vst_cnt = "wsite_vst_cnt";
	public static final String cal_cnt = "cal_cnt";
	public static final String dir_vst_cnt = "dir_vst_cnt";

	public static final String businessImpDeskMaps = "businessImpDeskMaps";
	public static final String businessImpMobMaps = "businessImpMobMaps";
	public static final String businessImpDeskSearch = "businessImpDeskSearch";
	public static final String businessImpMobSearch = "businessImpMobSearch";
	public static final String webClicks = "webClicks";
	public static final String callClicks = "callClicks";
	public static final String businessDirReq = "businessDirReq";

	public static final String CDN_UPLOAD_TOPIC = "social-cdn-upload";

	public static final String CDN_UPLOAD_PAGE_TOPIC = "social-cdn-upload-page";

	public static final String CDN_MIGRATE_TOPIC = "social-cdn-migrate";

	public static final String ALL_CHANNEL = "ALL";
	public static final String retryConstants = "SOCIAL-POST-RETRY";
	public static final String reminderConstant = "SOCIAL-APPROVAL-REMIND";
	public static final String postingPublic = "PUBLIC-POSTING";
	public static final String bulkAccountPosting = "BULK-ACCOUNT-POSTING";
	public static final String bulkResellerPosting = "BULK-RESELLER-POSTING";
	public static final String socialPublish = "socialpublish";
	public static final String PUBLISH_MODULE = "PUBLISH";
	public static final String INTEGRATION_MODULE = "INTEGRATION";
	public static final String BRIGHT_DATA = "bright-data";
	public static final String GENERATE_FAILED_POST_EMAIL = "generate_failed_post_email";
	public static final String GENERATE_FAILED_POST_EMAIL_TYPE = "generate_failed_post_email-alert";
	public static final String AI_POST_EMAIL_ALERTS = "ai-post-email-alerts";
	public static final String FAILED_POST_NOTIFICATION = "failed-post-notification";
	public static final String FAILED_POST_UPDATE = "failed-post-notification-update";
	public static final String RATE_LIMIT_EXCEEDED_AUDIT_TOPIC = "rate-limit-exceed-audit";
	public static final String RATE_LIMIT_SHARED_AUDIT_TOPIC = "rate-limit-alert-audit";
	public static final String SOCIAL_FACEBOOK_SUBSCRIBE = "social-engage-facebook-subscribe";
	public static final String SOCIAL_INSTAGRAM_SUBSCRIBE = "social-engage-instagram-subscribe";
	public static final String SOCIAL_LINKEDIN_SUBSCRIBE = "social-engage-linkedin-subscribe";
	public static final String SOCIAL_TWITTER_SUBSCRIBE = "social-engage-twitter-subscribe";
	public static final String SOCIAL_ENGAGE_ACTIONS_AUDIT_TOPIC = "social-engage-actions-audit";
	public static final int FAILED_POST_TEXT_LIMIT = 150;
	public static final String PUSH_FAILED_POST_EMAIL = "push_failed_post_notification";
	public static final String DEFAULT_URL = "https://birdeye.com";
	public static final String SECURE_PROTOCOL = "https://";
	public static final String NON_SECURE_PROTOCOL = "http://";
	public static final String CUSTOM_RECIPIENT_TYPE = "custom";

	public static final String YT_COMMENT_TOPIC = "social-youtube-engage-comment";
	public static final String YT_SAVE_VIDEO_INFO_TOPIC = "social-youtube-engage-save-info";
	public static final String PARENT_COMMENT_NOT_FOUND = "parentCommentNotFound";
	public static final String CHECK_GMB_VALID = "check-gmb-valid";
	public static final String RESOURCE_EXHAUSTED = "RESOURCE_EXHAUSTED";
	public static final String PERMISSION_DENIED = "PERMISSION_DENIED";
	public static final String NOT_FOUND = "NOT_FOUND";
	public static final String POST_DELETE_KEY = "POST_DELETE";
	public static final String BULK_UPLOAD = "BULK_UPLOAD";

	public static final String AES_ENCRYPT_DECRYPT = "AES";
	public static final String ACTIVE = "ACTIVE";
	public static final String buttonColor = "#fff";
	public static final String buttonTextColor = "#1976d2";
	public static final String logoUrl = "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/default-business-250x250.png";

	public static final String INVALID_CHANNEL = "invalid channel";

	public static final String FAILED_POST_SUBJECT = "Post failed %s %d %s %s.";

	public static final String FAILED_POST_SUBJECT_SMB = "Post failed %s %s.";

	public static final String FAILED_POST_COMMON_SUFFIX = " Let's fix it now.";
	public static final String FAILED_POST_PUSH_SUBJECT = "Post failure!";
	public static final String FAILED_TEXT_POST_NOTIF_SUBJECT = "%s...";
	public static final String EMAIl = "EMAIL";
	public static final String PUSH_NOTIFICATION = "PUSH";

	public static final String FAILED = "FAILED";
	public static final String EXCEPTION = "Exception ";

	public static final String FAILED_POST_TOKEN_SUBJECT = "Your recent post failure";
	public static final String MASTER_POST = "masterPost";
	public static final String GOOGLE_PLUS = "googleplus";
	public static final String AI_POSTS_WEEKLY_EMAIL_SUBJECT = "Personalized AI post suggestions for your business";
	public static final String AI_POSTS_WEEKLY_EMAIL_POST_HEADER = "Here are your AI-suggested posts to supercharge your upcoming week";
	public static final String AI_POSTS_MONTHLY_EMAIL_SUBJECT =  "We've planned 4 weeks of social content for you";
	public static final String AI_POSTS_MONTHLY_POST_HEADER = "Your AI suggested posts for the next 4 weeks are ready!";
	public static final String APPROVAL_EMAIL_SUBJECT = "Approval request on %s post scheduled for %s";
	public static final String APPROVAL_EMAIL_SUBJECT_EDIT = "Approval request to update %s %s post published on %s";
	public static final String APPROVAL_EMAIL_SUB_SUBJECT_EDIT = "Approval request from %s to update %s %s post published on %s";
	public static final String APPROVAL_REJECTED_EMAIL_SUBJECT = "%s rejected your %s post scheduled for %s";
	public static final String APPROVAL_REJECTED_EMAIL_SUBJECT_EDIT = "%s rejected the update request for %s %s post published on %s";
	public static final String APPROVAL_EXPIRED_EMAIL_SUBJECT = "Your %s post has now expired";
	public static final String APPROVAL_EXPIRED_EMAIL_SUB_SUBJECT = "Reschedule your expired post and submit for approval again";
	public static final String APPROVAL_EXPIRED_EMAIL_SUBJECT_EDIT = "Approval request expired to update %s %s post published on %s";
	public static final String APPROVAL_EXPIRED_EMAIL_SUB_SUBJECT_EDIT = "Approval request from %s to update %s %s post published on %s has expired";
	public static final String APPROVAL_SCHEDULE_EMAIL_SUBJECT = "%s requires your approval on %s %s post scheduled for %s";
	public static final String APPROVAL_APPROVED_EMAIL_SUBJECT = "%s has approved %s %s post scheduled for %s";
	public static final String APPROVAL_APPROVED_EMAIL_SUBJECT_EDIT= "Approval confirmation for update request on %s %s post published on %s";
	public static final String APPROVAL_APPROVED_EMAIL_SUB_SUBJECT_EDIT= "%s approved the update request for %s %s post published on %s";
	public static final String APPROVAL_REMINDER_EMAIL_SUBJECT = "Only 1 day left to approve or reject a scheduled %s post";
	public static final String APPROVAL_REMINDER_EMAIL_SUB_SUBJECT = "%s has sent you a reminder to review a post scheduled for %s";
	public static final String APPROVAL_BULK_SCHEDULE_EMAIL_SUBJECT = "Approval request for social posts pending your review";
	public static final String APPROVAL_BULK_SCHEDULE_MULTI_CARD_EMAIL_SUB_SUBJECT = "%s needs your approval for these scheduled posts";
	public static final String APPROVAL_BULK_SCHEDULE_SINGLE_CARD_EMAIL_SUB_SUBJECT = "%s needs your approval for these posts scheduled for %s";
	public static final String APPLE_GRANT_TYPE = "client_credentials";
	public static final String APPLE_SCOPE = "business_connect";
	public static final String APPLE_CONNECT_TOKEN = "APPLE_CONNECT_TOKEN:";
	public static final String APPLE_ENGAGEMENT_METRIC = "PLACECARD_TAP_SHOWCASE";
	public static final String APPLE_IMPRESSION_METRIC = "PLACECARD_VIEW";
	public static final String APPLE_MAPS = "Apple Maps";
	public static final String APPLE_CONNECT = "apple_connect";
	public static final String TIKTOK = "tiktok";

	public static final String APPROVAL_MANUAL_REMINDER_EMAIL_SUB_SUBJECT = "Your approval required for %s %s post";
	public static final String APPLE_REPORT_RESOURCE_TYPE = "LOCATION";
	public static final String APPLE_REPORT_TIME_DAY_GRANULARITY = "DAY";
	public static final String APPLE_REPORT_TIME_MONTH_GRANULARITY = "MONTH";
	public static final DateTimeFormatter APPLE_REPORT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
	public static final String PLACECARD = "PLACECARD";
	public static final String SEARCH = "SEARCH";
	public static final String SPATIAL = "SPATIAL";
	public static final String MARK_PAGE_INVALID = "social-mark-page-invalid";

	public static final String USER_ID = "user-id";
	public static final String BUSINESS_ID = "business-id";
	public static final String ACCOUNT_ID = "account-id";
	public static final String BUSINESS_NUMBER = "X-BUSINESS-NUMBER";
	public static final String ENGAGE_FEED_TYPE_MENTION = "mention";

	public static final String ENGAGE_DISCONNECT = "DISCONNECT";
	public static final String ENGAGE_EMAIL_ALERT = "engage_email_alert";

	public static final String INVALID_PAGE = "Invalid page";
	public static final String TWITTER_CREATE_EVENT = "social-twitter-create-events";
	public static final String APPLE_EMAIL_SUBJECT_SENT = "Approval request sent for your Apple post scheduled for %s";
	public static final String APPLE_EMAIL_SUBJECT_REJECTED = "Apple rejected your post scheduled for %s";
	public static final String APPLE_EMAIL_SUBJECT_APPROVED = "Apple approved your post scheduled for %s";
	public static final String APPLE_EMAIL_SUBJECT_LIVE = "Your Apple post is live today";
	public static final String APPLE_EMAIL_SUBJECT_EXTENSION_SENT = "Approval request sent to extend your Apple post until %s ";
	public static final String APPLE_EMAIL_SUBJECT_EXTENSION_APPROVED = "Apple approved your post extension request until %s";
	public static final String APPLE_EMAIL_SUB_SUBJECT_SENT = "Your Apple post scheduled for %s is sent for approval";
	public static final String APPLE_EMAIL_SUB_SUBJECT_REJECTED = "Your Apple post scheduled for %s didn’t make it this time";
	public static final String APPLE_EMAIL_SUB_SUBJECT_APPROVED = "Good news, your Apple post is all set to go live on %s";
	public static final String APPLE_EMAIL_SUB_SUBJECT_LIVE = "Great, your Apple post is live today, expires %s";
	public static final String APPLE_EMAIL_SUB_SUBJECT_EXTENSION_SENT = "Your request to extend your Apple post until %s sent for approval";
	public static final String APPLE_EMAIL_SUB_SUBJECT_EXTENSION_APPROVED = "Good news, your Apple post is all set to stay live until %s";
	public static final String APPLE_SHOWCASE_LIVE_CHECK = "APPLE_SHOWCASE_LIVE_CHECK";
	public static final String TWITTER_SOURCE_BE = "BE";
	public static final String TWITTER_SOURCE_TWITTER = "TWITTER";

	public static final String FB_FAILURE_REDIS_KEY = "fb-failed-webhook";
	public static final String BACKFILL_REDIS_KEY = "backfill-redis-key";

	public static final String IG_FAILURE_REDIS_KEY = "ig-failed-webhook";

	public static final String LN_FAILURE_REDIS_KEY = "ln-failed-webhook";

	public static final String TWITTER_FAILURE_REDIS_KEY = "twitter-failed-webhook";

	public static final String SET_FAILURE_REDIS = "set-failure-to-redis";

	public static final String TWITTER_DM_YOU_ARE_BLOCKING_USER = "You are blocking one or more participants.";

	public static final String TWITTER_DM_OPERATION_IS_NOT_PERMITTED = "This operation is not permitted.";

	public static final String TWITTER_DM_PERMISSION = "You do not have permission to DM one or more participants.";
	public static final String TWITTER_LIKE_OPERATION_IS_NOT_PERMITTED = "You can't engage with a post from a social handle that has blocked you";
	public static final String DELETED_WORKFLOW_MESSAGE = "The selected approval is currently disabled. Please select another approval and reschedule.";
	public static final Integer UNDEFINED = -1;

	// This cache is used to store all the tags for the account
	public static final String ACCOUNT_ALL_SOCIAL_TAGS_CACHE = "accountAllSocialTagCache";
	public static final int SOCIAL_TAG_NAME_MAX_LENGTH = 100;
	public static final String SOCIAL_DRAFT_DELETE_TOPIC = "social-draft-deleted";
	public static final String GENERIC_EXCEPTION_MESSAGE = "Unknown Error Occurred";

	public static final String GOOGLE_OFFER = "GOOGLE_OFFER";
	public static final String PHOTO = "photo";
	public static final String VIDEO_POST = "video";
	public static final String ALBUM = "album";

	public static final String FB_SHARE = "share";

	public static final String CLIENT_REQUEST_SOURCE = "request-source";

	public static final String SOCIAL_TEMPLATE = "socialtemplatesfeb14";
	public static final String GOOGLE_OFFERS = "Google offers";
	public static final String REJECTED = "REJECTED";
	public static final String OTHER = "Other";

	public static final String SOCIAL_FETCH_COMPETITOR_POSTS = "social-fetch-competitor-posts";

	public static final String FB_SYSTEM_USER_PPCA_TOKEN = "fb-system-user-ppca-token";
	public static final String TOTAL_PART_CHUNKS = "total_part_chunk_";
	public static final String LIST_OF_REQUEST_IDS = "list_of_request_ids_";
    public static final String AVAILABLE = "AVAILABLE";
	public static final String REQUEST_TOTAL_PART_CHUNKS = "request_total_part_chunk";
	public static final String RESELLER_BULK_MAPPING_FOLDER = "reseller_bulk_mapping_folder";
	public static final String RESELLER_BULK_MAPPING_CONSTANT = "reseller_bulk_mapping_";
	public static final String APPLE_LOCATION_DELETED="LOCATION__MARKED_DELETED";

	public static final String APPLE_LOCATION_REJECTED="REJECTED";
	public static final String INSIGHTS_INVALID="INSIGHTS__INVALID_RESOURCE_STATUS";

	public static final int INSTAGRAM_PER_POST_COMMENT_LIMIT = 50;

	public static final String SOCIAL_MODULE_ENGAGE = "ENGAGE";
	public static final String COMPETITOR_TASK_EXECUTOR = "competitor-task-executor";
	public static final String MOBILE = "mobile";

	public static final String INIT_TO_CANCEL_ERROR_REQUEST_MESSAGE= "Request is in init state cannot cancel it";
	public static final String EXTERNAL_USER = "External user";
	public static final String SYS_ADMIN_EMAIL = "<EMAIL>";
	public static final String MAPPING_ALREADY_EXIST_ERROR = "Mapping already exists by business id";

	public static final String FB_PROFILE_RESPONSE = "social-fb-profile-response";

	public static final String GENERIC_PERMISSION_MISSING_MESSAGE = "Some permissions are missing";

	public static final String FB_BASE_URL = "http://www.facebook.com/";
	public static final String POST_ENGAGEMENT = "post_engagement";
	public static final String POST_IMPRESSIONS = "post_impressions";
	public static final String FOLLOWER_GAIN = "follower_gain";
	public static final String POST_ENG_RATE = "post_eng_rate";
	public static final String TOTAL_FOLLOWERS = "total_followers";
	public static final String TOTAL_FOLLOWERS_COUNT = "total_follower_count";
	public static final String DAY = "day";

	public static final String DEFAULT_VIDEO_THUMBNAIL_URL = "https://ddjkm7nmu27lx.cloudfront.net/153027269604508/socialpublish/original/ag0kqz7uo3/1728643388599.png";

	public static final String POSTING_SITE_NO_PAGE_MESSAGE = "This page is no longer available or its mapping has changed. Please verify the page details before proceeding";
	public static final String TIKTOK_ACCESS_TOKEN_INVALID_CODE = "40105";

	public static final String VALIDATE_PAGE_REQUEST = "social-validate-page-request";
	public static final String TIKTOK_POST_URL = "https://www.tiktok.com/@%s/video/%s";

	public static final String APPROVAL_BULK_REQUEST_CORE = "api/v1/approvals/requests/bulk";

	public static final String APPROVAL_STATUS_UPDATE = "approvalStatusUpdate";

	public static final String INSTAGRAM_USER = "Instagram User";

	public static final int BATCH_SIZE_FOR_TOP_POSTS = 1024;

	public static final int THREADPOOL_SIZE_FOR_TOP_POSTS = 3;

	private Constants() {}
}